const fs = require('fs');
const shell = require('shelljs');
const path = require('path');
const projectList = ['admin', 'home', 'site', 'common'];

// 复制各个项目tsconfig到config目录下
function copyConfig() {
  const configPath = path.resolve(__dirname, 'config');
  if (!fs.existsSync(configPath)) fs.mkdirSync(configPath);  // config文件夹不存在则创建
  projectList.forEach((name) => {
    const configPath = path.resolve(__dirname, '../packages/', name, 'tsconfig.json');
    const destPath = path.resolve(__dirname, `./config/tsconfig.${name}.lint.json`);
    fs.copyFileSync(configPath, destPath);
  });
}

// 根据传入待提交文件，修改tsconfig，并返回tsc脚本
function changeConfigAndGetCommand() {
  const filesMap = {
    admin: [],
    home: [],
    site: [],
    common: []
  };
  const files = process.argv.slice(2); // 待提交的文件路径
  files.forEach((name) => {
    if (name.indexOf('packages/admin') > -1) filesMap.admin.push(name);
    if (name.indexOf('packages/home') > -1) filesMap.home.push(name);
    if (name.indexOf('packages/site') > -1) filesMap.site.push(name);
    if (name.indexOf('packages/common') > -1) filesMap.common.push(name);
  });
  let commands = [];
  for (const name in filesMap) {
    if (filesMap[name].length > 0) {
      const configPath = path.resolve(__dirname, `./config/tsconfig.${name}.lint.json`);
      const configData = require(configPath);
      // 修改@相对路径
      if (configData.compilerOptions.paths['@/*']) {
        let pathName = name;
        if (pathName === 'common' || pathName === 'site') pathName = 'admin';
        configData.compilerOptions.paths['@/*'] = [`../../packages/${pathName}/src/*`];
      }
      configData.include = ['../../types/*.ts'].concat(filesMap[name]);
      fs.writeFileSync(configPath, JSON.stringify(configData, '', '\t'));
      commands.push(`pnpm exec vue-tsc -p .husky/config/tsconfig.${name}.lint.json`);
    }
  }
  fs.writeFileSync('../data.txt', commands.join(' & ')); // 调试使用
  return commands.join(' & ');
}

// 执行检查脚本
function exec() {
  copyConfig();
  const command = changeConfigAndGetCommand();
  shell.exec(command, (code, stdout, stderr) => {
    process.exit(code);
  });
}
exec();
