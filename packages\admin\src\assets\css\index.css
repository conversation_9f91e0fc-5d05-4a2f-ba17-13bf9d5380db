@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    font-family: var(--td-font-family);
  }

  body {
    @apply text-sm;
  }

  ::-webkit-scrollbar {
    width: 9px;
    height: 9px;
  }

  ::-webkit-scrollbar-corner {
    background-color: transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply rounded-default bg-black-disabled;
    border: 1px solid transparent;
    transition: all .3s;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-black-placeholder;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent;
  }

  input:-webkit-autofill,
  textarea:-webkit-autofill,
  select:-webkit-autofill {
    box-shadow: 0 0 0px 1000px transparent inset;
    background-color: transparent;
    background-image: none;
    transition: background-color 50000s ease-in-out 0s;
  }
}

.normal-hover {
  opacity: 1;
  cursor: pointer;
  transition: opacity .3s ease-in;
}

.normal-hover:hover {
  opacity: .6;
}

.normal-hover:active {
  opacity: .4;
}