<template>
  <BaseDialog
    ref="asyncTaskRef"
    title="Async Download Record"
    :is-show-footer="false"
  >
    <data-container
      :total="total"
      :page-size="pageSize"
      :default-page="pageNum"
      :page-size-options="[10, 20, 30, 50, 100, 200]"
      :hide-header="true"
      @on-page-change="onPageChange"
    >
      <Table
        v-if="!loading"
        class="w-[900px]"
        :display-columns="displayCols"
        :loading="loading"
        resizable
        row-key="id"
        :data="data?.data"
        :horizontal-scroll-affixed-bottom="true"
        :columns="columns"
      />
    </data-container>
  </BaseDialog>
</template>
<script setup lang="ts">
import { useAuthStageStore } from '@/store/global/auth.store';
import BaseDialog from 'common/components/Dialog/Base';
import DataContainer from 'common/components/Layout/DataContainer.vue';
import Table from 'common/components/table';
import { useFetchWrapper } from 'common/compose/request/request';
import { asyncDownloadTaskList, TGetTaskListParam, TTaskListReturn } from 'common/service/creative/dashboard/async-download-task-list';
import { resetAsyncDownloadTask } from 'common/service/creative/dashboard/reset-async-download-task';
import dayjs from 'dayjs';
import { ref, watch } from 'vue';

const props = defineProps({
  module: {
    type: String,
    default: '',
  },
});

const useAuthStage = useAuthStageStore();

const pageSize = ref(10);
const pageNum = ref(1);
const total = ref(0);
const format = 'YYYY-MM-DD HH:ss:mm';
const columns = [
  {
    colKey: 'file_name',
    title: 'File Name',
    ellipsis: true,
  },
  {
    colKey: 'task_name',
    title: 'Task Name',
    ellipsis: true,
  },
  {
    colKey: 'status',
    title: 'Status',
    cell: (_h: any, { row }: any) => (row.status === 'ing' && row.progress === 1 ? 'success' : row.status),
  },
  {
    colKey: 'progress',
    title: 'progress',
    cell: (_h: any, { row }: any) => (`${Number((row.progress * 100).toFixed(2))}%`),
  },
  {
    colKey: 'download_url',
    title: 'Download Url',
    ellipsis: true,
    cell: (_h: any, { row }: any) => (row.download_url ? _h(
      'a',
      {
        href: row.download_url,
        class: 'text-brand cursor-pointer',
      },
      row.download_url,
    ) : ''),
  },
  {
    colKey: 'create_time',
    title: 'Create Time',
    ellipsis: true,
    cell: (_h: any, { row }: any) => dayjs(new Date(row.create_time)).format(format),
  },
  {
    colKey: 'update_time',
    title: 'Update Time',
    ellipsis: true,
    cell: (_h: any, { row }: any) => dayjs(new Date(row.update_time)).format(format),
  },
  {
    colKey: 'other',
    title: 'Operate',
    fixed: 'right',
    cell: (_h: any, { row }: any) => _h(
      'p',
      {
        onClick: async () => {
          // 请求接口
          await resetAsyncDownloadTask(Number(row.id));
          emit();
        },
        class: 'text-brand cursor-pointer',
      },
      'reset',
    ),
  },
];
const displayCols = ref(columns.map(item => item.colKey));
const asyncTaskRef = ref<InstanceType<typeof BaseDialog> | null>();
const {
  loading,
  emit,
  data,
} = useFetchWrapper<TGetTaskListParam, TTaskListReturn>(
  asyncDownloadTaskList,
  {
    module: props.module,
    creator: useAuthStage.currentUser,
  },
  {
    storage: false,
    throttle: 500,
    immediately: false,
    reactive: true,
  },
);

watch(
  () => data?.value?.total,
  (value) => {
    if (value) {
      total.value = value;
    }
  },
);
const onPageChange = (current: number, info: any) => {
  pageNum.value = current;
  pageSize.value = info.pageSize;
};

defineExpose({
  show: () => {
    emit();
    asyncTaskRef.value?.show();
  },
  hide: asyncTaskRef.value?.hide,
});
</script>
