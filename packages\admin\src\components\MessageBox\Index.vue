<template>
  <div v-if="isShowTotalMessageBox" class="block">
    <div
      class="z-[999]"
      :class="{
        'message-box-block': isShowMessageBox
      }"
      @click="closeMessageBox"
    >
      <teleport to="body">
        <!-- 遮罩层，防止拖拽过快时，会触碰到下层元素 -->
        <div
          v-show="isShowMask"
          class="w-full h-full fixed bg-transparent z-[9999] left-0  top-0 "
        />
        <div
          v-if="!isShowMessageBox"
          class="fixed z-[999]"
          :style="{left: `${messageBoxStyle.left}px`, top: `${messageBoxStyle.top}px`}"
        >
          <div
            ref="triggerRef"
            class="max-w-[66px] min-w-[66px] max-h-[66px] min-h-[66px] bg-brand select-none
              inline-flex justify-center items-center box-border rounded-circle cursor-pointer overflow-hidden
            "
            @mousedown="onMouseDown"
            @mouseup="onMouseUp"
            @mouseleave="onMouseUp"
          >
            <SvgIcon v-if="hasRed" name="msg-not-active" size="32px" />
            <SvgIcon
              v-else
              name="msg-not-inactive3" size="32px" color="#fff"
            />
          </div>
        </div>
      </teleport>
    </div>
    <t-dialog
      header="Messages"
      :visible="isShowMessageBox"
      :close-on-overlay-click="true"
      :prevent-scroll-through="false"
      width="1080px"
      :footer="false"
      :on-close-btn-click="() => { setMessageBoxVisible(false) }"
      @overlay-click="() => { setMessageBoxVisible(false) }"
    >
      <div class="h-full flex flex-col overflow-hidden">
        <Messages
          ref="messageBoxRef"
          class="cursor-default flex-1"
        />
      </div>
    </t-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, watch, computed, nextTick, onMounted } from 'vue';
import { useWindowSize, useDraggable, useElementSize } from '@vueuse/core';
import { useNotificationStore } from '@/store/monitor/notification.store';

import { storeToRefs } from 'pinia';
import Messages from './Mesages.vue';
import SvgIcon from 'common/components/SvgIcon';


const { setShowMask, setMessageBoxVisible } = useNotificationStore();
const { isShowMask, isShowMessageBox, unreadData, isShowTotalMessageBox } = storeToRefs(useNotificationStore());

const triggerRef = ref<HTMLDivElement>();
const messageBoxRef = ref<HTMLDivElement>();

const hasRed = computed(() => {
  const data = unreadData.value;
  return data.account + data.ad_issues + data.rules + data.ai_optimization > 0;
});

const { width: windowWidth, height: windowHeight } = useWindowSize();
const { width: triggerWidth, height: triggerHeight  } = useElementSize(triggerRef);
const { height: messageBoxHeight, width: messageWidth } = useElementSize(messageBoxRef);

// 用于防止拖住时脱发点击事件
const isDrag = ref(false);

const messageBoxStyle = reactive({
  top: windowHeight.value - 100,
  left: windowWidth.value - 100,
});

const messageContentStyle = reactive({
  right: 0,
  bottom: 80,
});

const { x, y, isDragging } = useDraggable(triggerRef);
watch(() => [x.value, y.value], ([newX, newY]) => {
  isDrag.value = true;
  if (newX >= 0 && newX <= (windowWidth.value - triggerWidth.value)) {
    messageBoxStyle.left = newX;
  }
  if (newY >= 0 && newY <= (windowHeight.value - triggerHeight.value)) {
    messageBoxStyle.top = newY;
  }
  nextTick(() => {
    if (newY >= 0 && newY - messageBoxHeight.value > 0) messageContentStyle.bottom = 80;
    else messageContentStyle.bottom = -(messageBoxHeight.value + 20);

    if (x.value + 66 > messageWidth.value) messageContentStyle.right = 0;
    else messageContentStyle.right = 0 - (messageWidth.value - 66);
  });
}, { deep: true });

/*
 * 处理长按、点击事件的冲突
 * 鼠标按下超过300毫秒，认为是长按，忽略点击事件
 */
const longPressTriggered = ref(false);
const clickTriggered = ref(false);
const timer = ref();
const onMouseDown = () => {
  longPressTriggered.value = false;
  clickTriggered.value = true;
  timer.value = setTimeout(() => {
    longPressTriggered.value = true;
    clickTriggered.value = false;
  }, 300);
};
const onMouseUp = () => {
  if (!clickTriggered.value) return;
  clearTimeout(timer.value);
  if (longPressTriggered.value) {
  } else {
    clickTriggered.value = false;
    const tempShowValue = isShowMessageBox.value;
    setMessageBoxVisible(!isShowMessageBox.value);
    setTimeout(() => {
      if (!tempShowValue) {
        if (y.value === 0 || (y.value > 0 && y.value - messageBoxHeight.value > 0)) messageContentStyle.bottom = 80;
        else messageContentStyle.bottom = -(messageBoxHeight.value + 20);

        if (x.value === 0 || (x.value + 66 > messageWidth.value)) messageContentStyle.right = 0;
        else messageContentStyle.right = 0 - (messageWidth.value - 66);
      }
    }, 100);
  }
};

const closeMessageBox = () => {
  isShowMessageBox.value = false;
  messageBoxStyle.top = windowHeight.value - 100;
  messageBoxStyle.left = windowWidth.value - 100;
};

watch(() => [windowWidth.value, windowHeight.value], ([newWidth, newHeight]) => {
  messageBoxStyle.top = newHeight - 100;
  messageBoxStyle.left = newWidth - 100;

  if (newWidth < 1080) {
    isShowMessageBox.value = false;
  }
}, { deep: true });

watch(() => isDragging.value, (val) => {
  setShowMask(val);
}, { deep: true });

onMounted(() => {

});
</script>
<style lang="scss" scoped>
  .message-box-block {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
  }
</style>
<style lang="scss">
.aix-messagebox-enter-active,
.aix-messagebox-leave-active {
  transition: opacity 0.5s ease;
}

.aix-messagebox-enter-from,
.aix-messagebox-leave-to {
  opacity: 0;
}
</style>
<style lang="scss" scoped>
/* :deep(.t-drawer__body) {
  padding: 0;
} */

:deep(.t-dialog) {
  position: absolute;
  border: none;
  box-shadow: var(--td-shadow-2),
    var(--td-shadow-inset-top),
    var(--td-shadow-inset-right),
    var(--td-shadow-inset-bottom),
    var(--td-shadow-inset-left);
  height: 70%;
  right: 5%;
  bottom: 10%;
}
:deep(.t-dialog__ctx .t-dialog__mask) {
  background: rgba($color: #000000, $alpha: 0);
}

:deep(.t-dialog__header) {
  font-size: 18px;
  font-weight: 700;
}

:deep(.t-dialog__body) {
  height: 100%;
}

:deep(.t-dialog__modal-default) {
  padding: 20px;
}
</style>
