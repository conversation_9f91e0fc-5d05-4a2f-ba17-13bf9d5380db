<template>
  <div
    class="aix-monitor-message_box h-full"
  >
    <div
      class="w-full h-full relative border-gray-placeholder border-solid border-[0px] border-t-0"
    >
      <t-tabs
        :value="notParams.type"
        class="h-full"
        placement="left"
        :destroy-on-hide="false"
        :disabled="historyNotLoading || notLoading"
        @change="changeType"
      >
        <t-tab-panel
          v-for="(item, index) in typeList"
          :key="item.value"
          :value="item.value"
          class="flex"
        >
          <template #label>
            <div
              class="relative pb-[14px] pr-[16px] pt-[16px]"
              :class="{'message-type__active': item.value === notParams.type}"
              :style="item.num > 99 ? 'padding-right: 20px' : ''"
              @mouseover="() => { menuHover(index)}"
              @mouseleave="() => menuLeave(index)"
            >
              <t-badge :count="item.num">
                <div
                  class="rounded-default"
                >
                  <t-tooltip :content="item.label" placement="right" overlay-class-name="msg-type-overlay">
                    <img :src="getIconName(index, item.value === notParams.type)" class="w-[36px] h-[36px]">
                  </t-tooltip>
                </div>
              </t-badge>
            </div>
          </template>
          <div class="flex-[2] max-w-[40%] h-full mx-[12px]">
            <div class="py-[8px] bg-white-primary sticky left-0 top-0 z-[1]">
              <Text
                v-if="notParams.type === 'rules'" :content="item.label" weight="700"
                size="title"
              />
              <Text
                v-else :content="item.label" weight="700"
                size="title"
              />
              <Text
                content="Read all" class="text-xs cursor-pointer" size="title"
                style="position: absolute; right: 0px; color:#4981f2"
                @click="() => {setAllReadMsg(notParams.type)}"
              />
            </div>
            <t-collapse
              borderless
              expand-icon
              expand-on-row-click
              default-expand-all
              class="aix_message_box_date_type overflow-y-auto h-full"
              :disabled="historyNotLoading || notLoading"
              @change="messageDateTypeChange"
              @scroll.stop="notificationListScroll"
            >
              <t-collapse-panel value="lastDate">
                <template #header>
                  <t-tooltip
                    content="Campaigns are ranked by their Spend over the last day."
                    placement="right"
                    overlay-class-name="msg-type-overlay"
                    class="flex items-center"
                  >
                    <span>Today</span>
                    <InfoCircleIcon class="ml-[4px]" />
                  </t-tooltip>
                </template>
                <MessageItemList
                  :v-loading="notLoading"
                  :notifications="notificationList"
                  :unread-list="unreadList"
                  date-type="lastDate"
                  @read-all-msg="() => {setAllReadMsg(notParams.type)}"
                />
              </t-collapse-panel>
              <template v-if="historyNotificationList.length > 0 || countUnreadHistory > 0">
                <t-badge
                  :count="countUnreadHistory"
                  :offset="[288, 14]"
                  size="small"
                  style="display:block;"
                >
                  <t-collapse-panel value="history" header="History" class="h-[28px]">
                    <MessageItemList
                      v-loading="historyNotLoading"
                      :notifications="historyNotificationList"
                      :unread-list="unreadList"
                      date-type="history"
                    />
                  </t-collapse-panel>
                </t-badge>
              </template>
            </t-collapse>
          </div>
          <MessageDetail
            :item="detailItem"
            class="flex-[3]"
          />
        </t-tab-panel>
      </t-tabs>
      <SvgIcon
        name="setting2"
        class="absolute bottom-[24px] left-[0px] cursor-pointer"
        size="36px"
        @click="gotoMonitorSetting"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, ref } from 'vue';
import SvgIcon from 'common/components/SvgIcon';
import MessageItemList from './MessageItemList.vue';
import MessageDetail from './MessageDetail.vue';
import { useNotificationStore } from '@/store/monitor/notification.store';
import { storeToRefs } from 'pinia';
import { useGoto } from '@/router/goto';
import { TYPE_LIST } from '@/store/monitor/const';
import { Unread } from '@/store/monitor/type';
import { useRoute } from 'vue-router';
import Text from 'common/components/Text';
import { InfoCircleIcon } from 'tdesign-icons-vue-next';
import type { Notification } from '@/store/monitor/type';
import { debounce } from 'lodash-es';

const {
  getHistoryNotificationList,
  changeNotType, closeDetail, setAllReadMsg, setDetail,
} = useNotificationStore();
const {
  detailItem, notificationList, unreadData, notLoading, unreadHistoryData,
  unreadList, notParams, historyNotLoading, historyNotificationList,
} = storeToRefs(useNotificationStore());
const { gotoMonitorSetting } = useGoto();
const currentRoute = useRoute();
const media = currentRoute.meta.media as string || '';

const iconRootPath = 'https://image-1251917893.file.myqcloud.com/components-image/';

const hoverIndex = ref<boolean[]>([]);

const menuHover = (index: number) => {
  hoverIndex.value = hoverIndex.value.map(() => false);
  hoverIndex.value[index] = true;
};
const menuLeave = (index: number) => {
  hoverIndex.value[index] = false;
};

const getIconName = (index: number, isChecked: boolean) => {
  if (isChecked) {
    return `${iconRootPath}check-${index + 1}.svg`;
  }
  if (hoverIndex.value[index]) {
    return `${iconRootPath}hover-${index + 1}.svg`;
  }
  return `${iconRootPath}default-${index + 1}.svg`;
};

// const hasUnread = computed(() => unreadHistoryData.value[notParams.value.type] > 0);

const typeList = computed(() => TYPE_LIST.filter((item) => {
  if (media.toLowerCase() === 'asa') return item.value === 'rules';
  return true;
}).map((item) => {
  const data = {
    ...item,
    num: unreadData.value[item.value as keyof Unread],
  };
  return data;
}));

const messageDateTypeChange = (newValue: string) => {
  closeDetail();
  notParams.value.date_type = newValue;
  const lastDateList: Notification[] = notificationList.value;
  const historyList: Notification[] = historyNotificationList.value;

  if (newValue.includes('lastDate') && newValue.includes('history')) {
    if (newValue === 'lastDate' && lastDateList.length !== 0) {
      setDetail(lastDateList[0]);
    } else if (newValue === 'history' && lastDateList.length !== 0) {
      setDetail(historyList[0]);
    }
  } else {
    if (newValue === 'lastDate' && lastDateList.length !== 0) {
      setDetail(lastDateList[0]);
    } else if (newValue === 'history' && lastDateList.length !== 0) {
      setDetail(historyList[0]);
    }
  }
};

const changeType = (val: string) => {
  closeDetail();
  changeNotType(val);
};

const notificationListScroll = debounce((event) => {
  const { scrollTop, scrollHeight, clientHeight } = event.target;
  if (scrollTop + clientHeight + 20 >= scrollHeight && historyNotificationList.value.length) {
    getHistoryNotificationList(historyNotificationList.value[historyNotificationList.value.length - 1].notification_id);
  }
}, 300);


const countUnreadHistory = computed(() => unreadHistoryData.value[notParams.value.type]);
</script>

<script lang="ts">
export default {
  data() {
    return {
      notParams: {
        date_type: 'lastDate',
      },
    };
  },
  methods: {
    togglePanel(panel: string) {
      if (this.notParams.date_type === panel) {
        this.notParams.date_type = 'null';
      } else {
        this.notParams.date_type = panel;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
:deep {
  // .t-tabs__nav-scroll {
  //   padding: 14px 16px 0 0;
  // }
  .t-tabs__nav-container.t-is-left::after {
    background: #F0F1F6;
  }
  .t-collapse-panel__header {
    background-color: var(--td-bg-color-secondarycontainer);
    padding: 5px 15px
  }

  .t-collapse-panel__wrapper .t-collapse-panel__header {
    padding: 0 var(--td-comp-paddingLR-m);
  }

  .t-collapse-panel__wrapper .t-collapse-panel__content{
    padding: 0;
  }

  .t-tabs__nav-item-text-wrapper{
    background-color: var(--aix-bg-color-white-primary);
  }
}
:deep(.t-collapse-panel .t-collapse-panel__wrapper .t-collapse-panel__header) {
  height: 28px;
}
:global(.aix-monitor-message_box .t-tabs__bar) {
  display: none;
}

:global(.aix_message_box_date_type .t-tabs__bar) {
  display: block;
}
:global(.aix-monitor-message_box .t-tabs__header) {
  height: 100%;
}
:global(.aix-monitor-message_box .t-tabs__nav) {
  height: 100%;
}
:global(.aix-monitor-message_box .t-tabs__nav-container) {
  height: 100%;
}
:global(.aix_message_box_date_type .t-tabs__header) {
  height: initial;
}
:global(.aix_message_box_date_type .t-tabs__nav) {
  height: initial;
}
:global(.aix_message_box_date_type .t-tabs__nav-container) {
  height: initial;
}
:global(.aix-monitor-message_box .t-tabs__nav-item-wrapper) {
  padding: 0;
  height: auto;
  margin: 0;
}
:global(.aix_message_box_date_type .t-tabs__nav-item-wrapper) {
  height: var(--td-comp-size-m);
  padding: 0 var(--td-comp-paddingLR-s);
  margin-left: var(--td-comp-margin-s);
  margin-right: var(--td-comp-margin-s);
}
:global(.aix-monitor-message_box .t-tabs__nav-item.t-size-m) {
  height: auto;
  line-height: normal;
  @apply bg-white-primary;
}

:global(.aix_message_box_date_type .t-tabs__nav-item.t-size-m) {
  height: var(--td-comp-size-xxl);
  line-height: var(--td-comp-size-xxl);
  padding: 0 var(--td-comp-paddingLR-s);
}

:global(
  .aix-monitor-message_box .t-tabs__nav-item:not(.t-is-disabled):not(.t-is-active):hover .t-tabs__nav-item-wrapper) {
    @apply bg-white-primary;
}

:global(.aix-monitor-message_box  .t-tabs__content) {
  height: 100%;
}

:global(.aix-monitor-message_box .t-tab-panel) {
  height: 100%;
}
</style>
<style lang="scss">
.msg-type-overlay {
  z-index: 999999
}
</style>
