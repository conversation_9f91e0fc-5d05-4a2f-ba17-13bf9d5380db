<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="relative h-full overflow-y-auto  bg-white" style="border-left: 1px solid #F0F1F6">
    <div v-if="props.item" class="w-full pl-[16px] pr-[8px] py-[8px] rounded-[12px]	" @click="goTo">
      <div class="font-bold text-[16px]">
        {{ props.item?.title }}
      </div>
      <div v-if="props.item" class="text-[#747D98] text-[14px] mt-[8px]">
        {{ props.item.local_time }}
        <span class="ml-[13px] mr-[13px]">|</span>
        <span>{{ typeMap[props.item.ext.template_id] }}</span>
      </div>
      <div v-if="htmlContent" class="text-[14px] text-[#202A41] mt-[6px]" v-html="htmlContent" />
    </div>
    <div v-else class="flex flex-1 flex-center h-full flex-col	">
      <div class="relative" style="text-align: center;">
        <img class="w-[160px]" src="../../assets/img/empty.png" alt="">
        <div class="text-[#cecece] mt-[-32px]">There's no issue</div>
      </div>
    </div>
    <GDiagnose ref="gDiagnoseRef" />
  </div>
</template>
<script lang="ts" setup>
import { PropType, computed, defineComponent, onMounted, ref } from 'vue';
import type { Notification } from '@/store/monitor/type';
import DOMPurify from 'dompurify';
import { Icon } from 'tdesign-icons-vue-next';
import { useGoto } from '@/router/goto';
import { setPivotSearch } from './tools';
import { useNotificationStore } from '@/store/monitor/notification.store';
import GDiagnose from '@/views/trade/ads_management/components/GDiagnose.vue';
import { useGlobalGameStore } from '@/store/global/game.store';
import { storeToRefs } from 'pinia';

const { gotoAdsManage } = useGoto();
const gameStore = useGlobalGameStore();
const { gameCode } = storeToRefs(gameStore);

defineComponent({
  Icon,
});
interface Target extends EventTarget {
  id?: string,
  parentNode: Element,
}

const gDiagnoseRef = ref();

const typeMap: {[key: string]: string} = {
  1: 'Campaign',
  2: 'Adgroup',
  3: 'Ad',
  4: 'Account',
  5: 'Keyword',
  7: 'Asset',
  8: 'Metric',
};

const props = defineProps({
  item: {
    type: Object as PropType<Notification>,
    default: () => {},
  },
});
const htmlContent = computed(() => (props?.item?.content ? DOMPurify.sanitize(props.item.content) : ''));
const { report, setMessageBoxVisible } = useNotificationStore();
onMounted(() => {
});

const goTo = async (e: MouseEvent) => {
  e.preventDefault();
  e.stopPropagation();
  const id = (e?.target as Target)?.id;
  const parentId = (e?.target as Target)?.parentNode?.id;
  if (id !== 'goto_check_campaigns' && parentId !== 'goto_check_campaigns') {
    return false;
  }
  const data = props.item?.ext;
  const { media, type, notification_id: notificationId } = props.item;
  if (data) {
    if (type === 'rules') {
      gDiagnoseRef.value.open({
        ...props.item, ...props.item.ext,
      });
    } else {
      sessionStorage.pivot_from = 'messeges_box';
      await gotoAdsManage(data.media, gameCode.value);
      setPivotSearch(media, props.item.ext);
      setMessageBoxVisible(false);
    }
    report({
      optid: '0204',
      ext5: 'message_detail_views_campaign_click',
      ext7: data.campaign_id,
      ext8: props.item.local_time,
      ext6: notificationId,
      ext9: props.item.ext?.campaign_list?.map((campaignItem: {index_name: string, index_desc: string}) => `${campaignItem.index_name} ${campaignItem.index_desc}`),
      ext10: 'metric',
    });
  }
};
</script>
<style lang="scss" scoped>
</style>
