<template>
  <div class="bg-white-primary">
    <div
      class="flex flex-col h-full"
    >
      <div class="h-full">
        <div
          v-for="item in notifications"
          :key="item.notification_id"
          class="cursor-pointer px-[12px] py-[8px] text-[12px] rounded-[12px] m-[5px]"
          :class="{
            'active-item': detailItem?.notification_id === item.notification_id,
            'hover-item': hover
          }"
          @click="setDetailItem(item)"
          @mouseover="hover = true"
          @mouseleave="hover = false"
        >
          <div class="flex truncate gap-x-[4px] items-center">
            <div
              :class="{'bg-error-secondary': isUnread(item.notification_id)}"
              class="w-[10px] h-[7px] rounded-round mr-[3px]"
            />
            <!-- <t-tooltip
              v-if="item.ext" :content="notParams.type === 'rules' ?
                (item.ext.campaign_name || item.ext.campaign_list?.[0].campaign_name) : item.title"
            > -->
            <Text
              class="truncate pt-[5px] pb-[5px]"
              :title="notParams.type === 'rules' ? (item.ext.campaign_name) : item.title"
              :content="notParams.type === 'rules' ? (item.ext.campaign_name) : item.title"
            />
            <!-- </t-tooltip> -->
          </div>

          <div
            class="flex items-center justify-between gap-x-[8px] pl-[15px] pt-[5px] pb-[5px]" :style="{
              width: '100%',
            }"
          >
            <div
              v-if="notParams.type === 'rules' && item?.ext?.campaign_list"
              class="flex flex-1 gap-x-[4px] flex-wrap items-center gap-y-[4px]"
            >
              <span
                v-for="(campaignItem, campaignIndex) in getListIndexDisplay(item.ext.campaign_list).slice(0, 2)"
                :key="campaignItem.campaign_id"
                class="px-[8px] py-[3px] rounded max-w-[90px] truncate"
                :style="{
                  'background-color': campaignIndex === 0 ? '#FFF1F1' : '#FFF5DB',
                  color: campaignIndex === 0 ? '#CF2A2A' : '#D07d00'
                }"
              >
                <t-tooltip>
                  <template #content>
                    <div class="flex items-center">
                      <span v-if="campaignItem.isTargetType" class="text-[12px]">
                        {{ campaignItem.index_name }} {{ campaignItem.index_desc }}
                      </span>
                      <span v-else class="text-[12px] flex items-center">
                        <span class="mr-[4px] text-[12px]">{{ campaignItem.index_name }}</span>
                        <img
                          v-if="campaignItem?.index_desc?.toLowerCase()?.includes('decrease')"
                          class="w-[12px]"
                          src="https://image-1251917893.file.myqcloud.com/components-image/Vector%205135.png"
                        >
                        <img
                          v-else
                          class="w-[12px]"
                          src="https://image-1251917893.file.myqcloud.com/components-image/Vector%205134.png"
                        >
                      </span>
                    </div>
                  </template>
                  <span v-if="campaignItem.isTargetType" class="truncate inline max-w-[90px]">
                    {{ campaignItem.index_name }} {{ campaignItem.index_desc }}
                  </span>
                  <span v-else class="flex items-center">
                    <span class="mr-[4px] max-w-[90px] truncate">{{ campaignItem.index_name }}</span>
                    <!-- {{ campaignItem?.index_desc?.toLowerCase()?.includes('decrease') ? '↓' : '↑' }} -->
                    <img
                      v-if="campaignItem?.index_desc?.toLowerCase()?.includes('decrease')"
                      class="w-[12px]"
                      src="https://image-1251917893.file.myqcloud.com/components-image/Vector%205135.png"
                    >
                    <img
                      v-else
                      class="w-[12px]"
                      src="https://image-1251917893.file.myqcloud.com/components-image/Vector%205134.png"
                    >
                  </span>
                </t-tooltip>
              </span>
              <span
                v-if="getListIndexDisplay(item.ext.campaign_list).length > 2"
                class="bg-[#EAECF1] text-[#202A41] px-[8px] py-[3px] rounded"
              >
                +{{ getListIndexDisplay(item.ext.campaign_list).length - 2 }}
              </span>
            </div>
            <div v-if="notParams.type === 'ad_issues'">
              <span class="bg-[#FFF5DB] text-[#D07d00] h-[28px] flex items-center px-[8px] rounded">
                {{ item.ext.new_status }}
              </span>
            </div>
            <Text
              :content="dateType === 'lastDate'
                ? setDateTime(item.local_time, 'time')
                : setDateTime(item.local_time, 'date')"
              color="var(--aix-text-color-black-secondary)"
              class="block truncate"
              size="small"
            />
          </div>
        </div>
      </div>
      <div
        v-if="isNotOpenSetting && props.dateType === 'lastDate'"
        class="flex flex-col flex-1 flex-center relative"
      >
        <!-- <img class="w-[160px]" src="../../assets/img/empty.png" alt=""> -->
        <div class="p-[10px] text-[#cecece]" style="text-align: center;">
          <div>You have closed notification.</div>
          <t-link theme="primary" @click="() => gotoMonitorSetting()">go to open →</t-link>
        </div>
      </div>
      <div
        v-if="!isNotOpenSetting && notifications.length === 0 && props.dateType === 'lastDate'"
        class="flex flex-col flex-1 flex-center relative"
      >
        <span class="py-[30px] text-[#cecece]">There are no issues today.</span>
      </div>
      <!-- <div
        v-if="notifications.length === 0 && (!isNotOpenSetting || props.dateType === 'history')"
        class="flex flex-col flex-1 flex-center relative"
      >
        <img class="w-[160px]" src="../../assets/img/empty.png" alt="">
        <span class="absolute bottom-[12px] text-[#cecece]">There's no issue.</span>
      </div>
      {{ console.log(isNotOpenSetting && props.dateType === 'lastDate' && notParams.type !== 'account') }}
      <div
        v-if="isNotOpenSetting && props.dateType === 'lastDate'"
        class="flex flex-col flex-1 flex-center relative mb-[20px]"
      >
        <img class="w-[160px]" src="../../assets/img/empty.png" alt="">
        <div class="absolute bottom-[-10px] text-[#cecece]" style="text-align: center;">
          <div>You have closed notification.</div>
          <t-link theme="primary" @click="() => gotoMonitorSetting()">go to open →</t-link>
        </div>
      </div> -->
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import type { PropType } from 'vue';
import Text from 'common/components/Text';
import { useNotificationStore } from '@/store/monitor/notification.store';
import type { Notification } from '@/store/monitor/type';

import { storeToRefs } from 'pinia';
import { useGoto } from '@/router/goto';

import dayjs from 'dayjs';

type CampaignItem = {
  index_desc: string,
  index_name: string,
  campaign_id: string,
  isTargetType: boolean,
};

const { gotoMonitorSetting } = useGoto();

const props = defineProps({
  notifications: {
    type: Array as PropType<Notification[]>,
    default: () => [],
  },
  unreadList: {
    type: Array as PropType<Notification[]>,
    default: () => [],
  },
  dateType: {
    type: String,
    default: 'lastDate',
  },
});
const { notParams, detailItem, isNotOpenSetting } = storeToRefs(useNotificationStore());
const { setDetail, report } = useNotificationStore();

const getListIndexDisplay = ((campaignList: CampaignItem[]) => {
  const temp: { [key: string]: boolean} = {};
  const result: CampaignItem[] = [];
  campaignList.forEach((campaignItem) => {
    const { campaign_id: campaignId, index_desc: indexDesc, index_name: indexName } = campaignItem;
    if (!temp[indexName]) {
      temp[indexName] = true;
      result.push({
        campaign_id: campaignId,
        index_desc: indexDesc,
        index_name: indexName,
        isTargetType: campaignItem.isTargetType,
      });
    }
  });
  return result;
});

const isUnread = (notificationId: string) => props.unreadList
  .findIndex(item => item.notification_id === notificationId) !== -1;

const setDetailItem = (item: Notification) => {
  setDetail(item, props.dateType);
  report({
    optid: '0203',
    ext5: 'message_list_item_click',
    ext6: item.notification_id,
    ext7: item.ext.campaign_id,
    ext8: item.local_time,
    ext9: item.ext?.campaign_list?.map((campaignItem: {index_name: string, index_desc: string}) => `${campaignItem.index_name} ${campaignItem.index_desc}`),
    ext10: 'metric',
  });
};


const isCurrentType = computed(() => notParams.value.date_type === props.dateType);
const isShowList = ref(isCurrentType.value);


watch(() => isCurrentType.value, () => {
  isShowList.value = isCurrentType.value;
  if (isCurrentType.value && !detailItem.value && props.notifications.length) {
    setDetailItem(props.notifications[0]);
  }
});
const hover = ref(false);

const setDateTime = (datetime: string, type: string) => {
  const formatDate = 'YY-MM-DD';
  const formatTime = 'HH:mm:ss';

  if (type === 'date') {
    return dayjs(datetime).format(formatDate);
  }
  return dayjs(datetime).format(formatTime);
};
</script>

<style lang="scss" scoped>
.active-item {
  background-color: #e9f4ff;
}
.hover-item:hover {
  background-color: hsl(225, 17%, 95%);
}
.bg-brands {
  background-color: #4981f2 !important;
}
.text-black {
  color: #000 !important;
}
</style>
