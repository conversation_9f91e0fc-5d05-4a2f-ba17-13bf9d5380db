import { uniq } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { DEFAULT_PAGE_INDEX, DEFAULT_PAGE_SIZE, V2_MEDIA } from '@/store/trade/pivot/utils/const';

type DataItem = {
  campaign_id: string;
  account_id: string;
  campaign_list: {
    campaign_id: string;
    account_id: string;
  }[];
};

export const setPivotSearch = (media: string, data: DataItem) => {
  import('@/views/trade/ads_management/utils/table/data-google').then(({ getIdFrResourceName }) => {
    const isGoogle = media === 'google';
    const campaignIdList: string[] = [];
    const accountIds: string[] = [];
    const { campaign_id: campaignId, campaign_list: campaignList = [], account_id: accountId } = data;
    if (campaignId) {
      accountId && accountIds.push(accountId);
      campaignIdList.push(isGoogle ? getIdFrResourceName(campaignId) : campaignId);
    }
    if (campaignList.length) {
      const list: string[] = uniq(
        data.campaign_list.map((item: { campaign_id: string; account_id: string }) => {
          if (item.account_id) {
            accountIds.push(item.account_id);
          }
          return isGoogle ? getIdFrResourceName(item.campaign_id) : item.campaign_id;
        }),
      );
      campaignIdList.push(...list);
    }
    if (campaignIdList.length === 0) {
      return false;
    }

    // 这里改为动态引入，直接引入太早初始化报错
    if (V2_MEDIA.includes(media?.toLocaleLowerCase())) {
      import('@/store/trade/pivot/index.store').then(({ useTradePivotStore }) => {
        const store = useTradePivotStore();
        const { setPivot } = store;
        const { condition } = storeToRefs(store);
        if (isGoogle) {
          condition.value.cur.account_id = uniq(accountIds);
        }
        if (condition.value.cur.search_box) {
          condition.value.cur.search_box[3].condition = campaignIdList;
          condition.value.cur.search_box[0].condition = [];
          condition.value.cur.search_box[1].condition = [];
          condition.value.cur.search_box[2].condition = [];
          condition.value.cur.pageIndex = DEFAULT_PAGE_INDEX;
          condition.value.cur.pageSize = DEFAULT_PAGE_SIZE;
          setPivot({});
        }
      });
    }
  });
};
