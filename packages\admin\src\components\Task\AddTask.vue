<template>
  <BaseDialog
    ref="editDialogRef"
    :title="type === 'add' ? 'Add Task' : 'Edit Task'"
    :width="800"
    @close="editClose"
    @confirm="onConfirm"
  >
    <t-form
      ref="formRef"
      :data="innerForm"
      :label-width="150"
      :rules="rules"
      label-align="top"
      @submit="onSubmit"
    >
      <t-form-item label="Task Name(Email subject title)" name="taskName">
        <Input v-model="innerForm.taskName" />
      </t-form-item>
      <t-form-item label="Add Context(Email body)" name="context">
        <Input v-model="innerForm.context" />
      </t-form-item>
      <t-form-item label="Report Name(File name)" name="fileName">
        <Input v-model="innerForm.fileName" placeholder="If empty, use the task name" />
      </t-form-item>
      <t-form-item
        v-if="props.module === 'creative_top_report'"
        label="Add Report Description"
        name="fileHeader"
      >
        <!-- <Input v-model="fileMessageForm.fileHeader" placeholder="Add description" /> -->
        <Textarea
          v-model="innerForm.fileHeader"
          placeholder="Add description"
          :autosize="{ minRows: 6, maxRows: 6 }"
        />
      </t-form-item>
      <t-form-item label="Type" name="type">
        <t-radio-group
          v-model="innerForm.type"
          default-value="demand"
          variant="primary-filled"
          @change="initCron"
        >
          <t-radio-button value="demand">On Demand</t-radio-button>
          <t-radio-button value="schedule">Schedule</t-radio-button>
        </t-radio-group>
        <p v-if="innerForm.type === 'demand'" class="ml-[5px]">On Demand: The report will run once it is ready.</p>
      </t-form-item>

      <t-form-item v-if="innerForm.type === 'schedule'" label="Cron" name="cron">
        <CronLight
          v-model="innerForm.cron"
          :periods="[
            { id: 'minute', value: [] },
            { id: 'hour', value: ['minute'] },
            { id: 'day', value: ['hour', 'minute'] },
            { id: 'month', value: ['day', 'dayOfWeek', 'hour', 'minute'] },
            { id: 'year', value: ['month', 'day', 'dayOfWeek', 'hour', 'minute'] },
            { id: 'week', value: ['dayOfWeek', 'hour', 'minute'] },
          ]"
        />
      </t-form-item>
      <t-form-item v-if="innerForm.type === 'schedule'" label="Enable" name="enable">
        <t-switch v-model="innerForm.enable" size="large" />
      </t-form-item>
      <t-form-item name="recipients">
        <template #label>
          Report Recipients
          <t-icon name="help-circle" /> Please press enter after typing an e-mail address
        </template>
        <t-tag-input v-model="innerForm.recipients" clearable />
      </t-form-item>
    </t-form>
  </BaseDialog>
</template>
<script setup lang="ts">
import { useAuthStageStore } from '@/store/global/auth.store';
import BaseDialog from 'common/components/Dialog/Base';
import { cronCreate } from 'common/service/creative/dashboard/cron-task-create';
import { cronUpdate } from 'common/service/creative/dashboard/cron-task-update';
import { ref, watch } from 'vue';
import Input from 'common/components/Input';
import cron from 'cron-validate';
import { SubmitContext, MessagePlugin } from 'tdesign-vue-next';
import { isObject, snakeCase } from 'lodash-es';
import Textarea from 'common/components/Textarea';


const props = defineProps({
  module: {
    type: String,
    default: '',
  },
  message: {
    type: Function,
    default: () => ({}),
  },
  form: {
    type: Object,
    default: () => {},
  },
  type: {
    type: String,
    default: '',
  },
});
const emit = defineEmits(['close']);
const cronValidator = (val: string) => {
  const cronResult = cron(val, {
    override: {
      // useSeconds: true,
      // useYears: true,
    },
  });
  if (!cronResult.isValid()) {
    return { result: false, message: 'Cron expression error', type: 'error' };
  }
  return { result: true, message: '', type: 'success' };
};

const taskNameValidator = (val: string) => {
  const maxLength = 100;
  if ((maxLength - props.module.length - val.length) <= 0) {
    return { result: false, message: `task name max length ${maxLength - props.module.length}`, type: 'error' };
  }
  return { result: true, message: '', type: 'success' };
};

const rules = {
  taskName: [
    { required: true, message: 'task name is required', type: 'error' },
    { validator: taskNameValidator },
  ],
  cron: [
    { required: true, message: 'cron is required', type: 'error' },
    { validator: cronValidator },
  ],
  context: [{ required: true, message: 'context is required', type: 'error' }],
  recipients: [{ required: true, message: 'recipients is required', type: 'error' }],
};

const useAuthStage = useAuthStageStore();

const oldTaskName = ref('');
const formRef = ref<any>(null);
const initForm = {
  taskName: '',
  cron: '* * * * *',
  enable: true,
  fileHeader: '',
  recipients: [],
  fileName: '',
  context: '',
  type: 'demand',
};
const innerForm = ref<any>({});
watch(
  () => props.form,
  (newValue) => {
    if (props.type === 'edit') {
      oldTaskName.value = newValue.taskName;
    }
    Object.keys(newValue).forEach((key) => {
      innerForm.value[key] = newValue[key];
    });
  }, {
    deep: true,
    immediate: true,
  },
);
const editDialogRef = ref<InstanceType<typeof BaseDialog> | null>();

defineExpose({
  show: () => {
    editDialogRef.value?.show();
    Object.keys(props.form).forEach((key) => {
      innerForm.value[key] = props.form[key];
    });
  },
  hide: () => editDialogRef.value?.hide(),
});

const onConfirm = async () => {
  formRef?.value?.submit();
};

const onSubmit = async (context: SubmitContext<FormData>) => {
  if (context.validateResult === true) {
    let data: any = '';
    const snakeForm: any = {};
    Object.keys(innerForm.value).forEach((key) => {
      snakeForm[snakeCase(key)] = innerForm.value[key];
    });
    const param = {
      ...snakeForm,
      message: JSON.stringify(props.message(innerForm.value)),
      creator: useAuthStage.currentUser,
      module: props.module,
    };
    if (props.type === 'add') {
      data = await cronCreate(param);
    } else {
      data = await cronUpdate({
        ...param,
        id: innerForm.value.id,
        old_task_name: oldTaskName.value,
      });
    }

    if (!isObject(data)) {
      MessagePlugin.error(data);
    } else {
      editDialogRef.value?.hide();
      emit('close');
    }
  }
};

const editClose = () => {
  Object.keys(initForm).forEach((key: string) => {
    innerForm.value[key as keyof typeof initForm] = initForm[key as keyof typeof initForm];
  });
};

const initCron = () => {
  innerForm.value.cron = initForm.cron;
};

</script>
<style type="scss">
.vcron-select-col {
  white-space: nowrap;
}
</style>
