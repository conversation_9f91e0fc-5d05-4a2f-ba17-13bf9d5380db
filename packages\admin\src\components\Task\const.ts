import { schemaAddOptions } from '@/store/creative/dashboard/utils';
import components from '@/views/creative/common/components/components';

export const InitSelectForm: any = {
  task_name: '',
  enable: [],
  cron: '',
  file_name: '',
  creator: '',
  execute_time: [],
  string_search: [
    {
      text: 'Task Name',
      field: 'task_name',
      condition: [],
    },
    {
      text: 'File Name',
      field: 'file_name',
      condition: [],
    },
    {
      text: 'Creator',
      field: 'creator',
      condition: [],
    },
  ],
};

export const options = {
  enable: [
    {
      label: 'True',
      value: true,
    }, {
      label: 'False',
      value: false,
    },
  ],
};

export const SEARCH_SCHEMA = [
  {
    name: components.SearchBox,
    props: {
      title: 'Search',
    },
    ext: {
      key: 'string_search',
      label: 'Search',
      isAllowClose: false,
    },
  },
  {
    name: components.CommonSelect,
    props: {
      title: 'Enable',
      multiple: true,
    },
    ext: {
      key: 'enable',
      label: 'Enable',
      isAllowClose: true,
      isHide: true,
    },
  },
  {
    name: components.Cron,
    props: {
      title: '<PERSON><PERSON>',
      multiple: true,
    },
    ext: {
      key: 'cron',
      label: '<PERSON><PERSON>',
      isAllowClose: true,
      isHide: true,
    },
  },
  {
    name: components.ImpressionDate,
    props: {
      title: 'Execute Time',
    },
    ext: {
      key: 'execute_time',
      label: 'Execute Time',
      isAllowClose: true,
      isHide: true,
    },
  },
];

// export const schema
export const setSearchSchema = (searchStore: any) => {
  // 通过searchSortList来排序
  const searchSchema = SEARCH_SCHEMA.map(item => schemaAddOptions(item, options, []));

  searchStore?.updateSchema(searchSchema);
};
