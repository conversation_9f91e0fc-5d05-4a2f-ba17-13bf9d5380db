<template>
  <BaseDialog
    ref="taskRef"
    title="Task List"
    :is-show-footer="false"
  >
    <!-- 添加筛选 -->
    <div class="flex">
      <FormContainer
        :model-value="searchStore.form"
        :form-list="searchStore.schema.value"
        :is-show-reset-btn="false"
        @close="onClose"
        @update:model-value="updateModelValue"
        @submit="submit"
      />
      <t-button class="mb-[15px] ml-[10px]" @click="showEditDialog">Add Task</t-button>
    </div>
    <data-container
      :total="total"
      :page-size="pageSize"
      :default-page="pageNum"
      :page-size-options="[10, 20, 30, 50, 100, 200]"
      :hide-header="true"
      @on-page-change="onPageChange"
    >
      <Table
        ref="table"
        class="w-[1200px]"
        :display-columns="displayCols"
        :loading="loading"
        resizable
        row-key="id"
        :data="data?.data"
        :horizontal-scroll-affixed-bottom="true"
        :columns="columns"
        :tree="treeConfig"
        :tree-expand-and-fold-icon="treeExpandAndFoldIconRender"
        :rowspan-and-colspan="rowspanAndColspan"
        @tree-expand-change="onTreeExpandChange"
      />
    </data-container>
  </BaseDialog>
  <AddTask
    ref="addTask"
    :form="form"
    :type="type"
    :module="props.module"
    :message="props.message"
    @close="submit"
  />
  <BaseDialog
    ref="deleteDialog"
    title="Delete Task List"
    @confirm="deleteCron"
  >
    Confirm to delete the corresponding cron task
  </BaseDialog>
</template>
<script setup lang="ts">
// import { useAuthStageStore } from '@/store/global/auth.store';
import BaseDialog from 'common/components/Dialog/Base';
import DataContainer from 'common/components/Layout/DataContainer.vue';
import Table from 'common/components/table';
import { useFetchWrapper } from 'common/compose/request/request';
import { cronList, TCronListParam, TCronListReturn } from 'common/service/creative/dashboard/cron-task-list';
import { cronDelete } from 'common/service/creative/dashboard/cron-task-delete';
import dayjs from 'dayjs';
import { computed, reactive, ref, watch } from 'vue';
import { cloneDeep, isObject, snakeCase } from 'lodash-es';
import AddTask from './AddTask.vue';
import { asyncDownloadTaskList } from 'common/service/creative/dashboard/async-download-task-list';
import { Loading, MessagePlugin } from 'tdesign-vue-next';
import { resetAsyncDownloadTask } from 'common/service/creative/dashboard/reset-async-download-task';
import { ChevronRightIcon, ChevronDownIcon } from 'tdesign-icons-vue-next';
import FormContainer, { IFormDynamicItem, IFormItem } from 'common/components/FormContainer';
import { useCommonSearch } from '@/compose/useCommonSearch';
import { InitSelectForm, setSearchSchema } from './const';
import { ISearchValue } from 'common/components/SearchBox';
import CronIcon from './CronIcon.vue';

const props = defineProps({
  module: {
    type: String,
    default: '',
  },
  message: {
    type: Function,
    default: () => ({}),
  },
  taskName: {
    type: String,
    default: '',
  },
});

// const useAuthStage = useAuthStageStore();

const treeConfig = reactive({ childrenKey: 'list', treeNodeColumnIndex: 0, indent: 25 });
const pageSize = ref(10);
const pageNum = ref(1);
const deleteDialog = ref<any>();
const table = ref<any>(null);
const total = ref(0);
const type = ref('add');
const format = 'YYYY-MM-DD HH:ss:mm';
const initForm = computed(() => ({
  taskName: props.taskName,
  cron: '* * * * *',
  enable: true,
  fileHeader: '',
  recipients: [],
  fileName: '',
  context: '',
  type: 'demand',
}));
const deleteRow = ref<any>({});
const form = ref<any>(cloneDeep(initForm.value));
const searchStore = useCommonSearch<any, any>();
searchStore.setForm(InitSelectForm);
setSearchSchema(searchStore);

watch(
  () => props.taskName,
  (newValue, oldValue) => {
    if (form.value.taskName === oldValue) {
      form.value.taskName = newValue;
    }
  },
);
const columns = [
  {
    colKey: 'task_name',
    title: 'Task Name',
    ellipsis: true,
  },
  {
    colKey: 'enable',
    title: 'Enable',
    cell: (_h: any, { row }: any) => (row.type === 'demand' || !('type' in row) ? '' : String(row.enable)),
  },
  {
    colKey: 'cron',
    ellipsis: true,
    title: (_h: any) => _h(
      'div',
      {},
      ['Cron', _h(
        CronIcon,
        {},
        '',
      )],
    ),
    cell: (_h: any, { row }: any) => (row.type === 'demand' || !('type' in row) ? '' : String(row.cron)),
  },
  {
    colKey: 'file_name',
    title: 'File Name',
    ellipsis: true,
  },
  {
    colKey: 'context',
    title: 'context',
    ellipsis: true,
  },
  {
    colKey: 'creator',
    title: 'Creator',
    ellipsis: true,
    cell: (_h: any, { row }: any) => {
      if ('download_url' in row) {
        return _h(
          'a',
          {
            href: row.download_url,
            class: 'text-brand cursor-pointer',
          },
          row.download_url,
        );
      }
      return _h(
        'p',
        {},
        row.creator || '',
      );
    },
  },
  {
    colKey: 'execute_time',
    title: 'Execute Time',
    ellipsis: true,
    cell: (_h: any, { row }: any) => dayjs(new Date(row.execute_time)).format(format),
  },
  {
    colKey: 'other',
    title: 'Operate',
    fixed: 'right',
    cell: (_h: any, { row }: any) => {
      const noTask = 'download_url' in row;

      const children = [
        _h(
          'p',
          {
            onClick: async () => {
              if (noTask) {
                await resetAsyncDownloadTask(Number(row.id.split('-')[0]));
              } else {
                type.value = 'edit';
                form.value.id = row.id;
                Object.keys(initForm.value).forEach((key) => {
                  if (key === 'fileHeader') {
                    const { fileHeader = '' } = JSON.parse(JSON.parse(row.message).param);
                    form.value[key] = fileHeader;
                    return;
                  }
                  if (key === 'recipients') {
                    form.value[key] = row[snakeCase(key)].split(',');
                    return;
                  }
                  form.value[key] = row[snakeCase(key)];
                });
              }
              addTask.value?.show();
            },
            class: 'text-brand cursor-pointer',
          },
          noTask ? 'reset' : 'edit',
        )];
      if (!noTask) {
        children.push(_h(
          'p',
          {
            class: 'ml-[5px] text-brand cursor-pointer',
            onClick: async () => {
              deleteDialog.value.show();
              deleteRow.value = row;
            },
          },
          'delete',
        ));
      }
      return _h(
        'div',
        {
          class: 'flex',
        },
        children,
      );
    },
  },
];
const deleteCron = async () => {
  const data = await cronDelete({
    id: deleteRow.value.id,
    type: deleteRow.value.type,
    module: props.module,
    old_task_name: deleteRow.value.task_name,
  });
  if (!isObject(data)) {
    MessagePlugin.error(data);
    return;
  }
  submit();
  deleteDialog.value.hide();
};
const displayCols = ref(columns.map(item => item.colKey));
const taskRef = ref<InstanceType<typeof BaseDialog> | null>();
const addTask = ref<any>(null);
const {
  loading,
  emit,
  data,
} = useFetchWrapper<TCronListParam, TCronListReturn>(
  cronList,
  {
    module: props.module,
    // creator: useAuthStage.currentUser,
    pageSize: pageSize.value,
    pageNum: pageNum.value,
  },
  {
    storage: false,
    throttle: 500,
    immediately: false,
    reactive: true,
  },
);

watch(
  () => data?.value?.total,
  (value) => {
    if (value) {
      total.value = value;
    }
  },
);
const onPageChange = (current: number, info: any) => {
  pageNum.value = current;
  pageSize.value = info.pageSize;
  emit();
};

defineExpose({
  show: () => {
    emit();
    taskRef.value?.show();
  },
  hide: taskRef.value?.hide,
});


const showEditDialog = () => {
  type.value = 'add';
  addTask?.value?.show();
};

const lazyLoadingData = ref<any>(null);
const getExpandData = async (row: any) => {
  // 添加数据
  const expandData = await asyncDownloadTaskList({ task_id: row.id });
  if (expandData.data.length === 0) {
    // 对应list设置为false
    table.value?.appendTo(row.key, {
      id: `${row.id}-noData`,
      key: `${row.id}-noData`,
      task_name: 'no data',
    });
  } else {
    expandData.data = expandData.data.map(item => ({
      id: `${row.id}-${item.id}`,
      key: `${row.id}-${item.id}`,
      execute_time: item.create_time,
      creator: item.download_url,
      download_url: item.download_url,
    }));
    table.value?.appendTo(row.key, expandData.data);
  }
  lazyLoadingData.value = null;
};
const onTreeExpandChange = async (context: any) => {
  /**
   * 如果是懒加载，请确认自己完成了以下几个步骤
   * 1. 提前设置 children 值为 true；
   * 2. 在 onTreeExpandChange 事件中处理异步数据；
   * 3. 自定义展开图标渲染 lazyLoadingTreeIconRender
   */
  if (context.row.list === true) {
    lazyLoadingData.value = context.row;
    getExpandData(context.row);
  }
};


const treeExpandAndFoldIconRender = (h: any, { type, row }: any) => {
  if (lazyLoadingData.value && lazyLoadingData.value.id === row?.id) {
    return h(
      Loading,
      {
        size: '14px',
      },
      '',
    );
  }
  return type === 'expand' ? h(
    ChevronRightIcon,
    {},
    '',
  ) : h(
    ChevronDownIcon,
    {},
    '',
  );
};


const onClose = (item: IFormItem | IFormDynamicItem) => {
  searchStore.form.value[item.ext.key] = InitSelectForm[item.ext.key];
};

const updateModelValue = (value: any) => {
  searchStore.form.value = value;
};

const submit = () => {
  if (data?.value?.data) {
    data.value.data = [];
  }
  searchStore.form.value.string_search.forEach((item: ISearchValue) => {
    searchStore.form.value[item.field] = item.condition;
  });
  // 拼接参数查询数据
  const params = cloneDeep(searchStore.form.value);
  delete params.string_search;
  pageNum.value = 1;
  emit({
    ...params,
    pageSize: pageSize.value,
    pageNum: 1,
  });
};

const rowspanAndColspan = (param: any) => {
  const id = param?.row?.id?.toString().split('-')[1];
  if (id === 'noData') {
    return {
      colspan: 8,
      rowspan: 1,
    };
  }
  return {
    colspan: 1,
    rowspan: 1,
  };
};


</script>
<style type="scss">
.vcron-select-col {
  white-space: nowrap;
}
</style>
