<template>
  <div
    class="flex items-center cursor-pointer h-[36px] fill-white-primary hover:opacity-25 bg-[#202A41] rounded-default"
    :class="[
      short ? 'w-[36px] justify-center' : 'w-[204px]',
      select ? 'nav-select rounded-default fill-black-placeholder text-black-placeholder' : '',
    ]"
    @mouseenter="onMouseEnter"
  >
    <div class="flex flex-row h-[36px] w-full space-x-[4px]">
      <div
        class="w-[36px] h-full flex items-center justify-center"
        :class="[select ? 'rounded-default nav-select' : '']"
      >
        <SvgIcon
          :name="icon"
          size="18"
        />
      </div>
      <transition name="fade">
        <div
          v-if="!short"
          class="flex flex-col flex-1 h-full pl-[16px] justify-center"
        >
          {{ name }}
        </div>
      </transition>
    </div>
  </div>
</template>
<script setup lang="ts">
import SvgIcon from 'common/components/SvgIcon';
import { useRouterStore } from '@/store/global/router.store';
import { storeToRefs } from 'pinia';
import type { PropType } from 'vue';
import { type RouteRecordNormalized } from 'vue-router';
const routerStore = useRouterStore();
const { isShowSecondMenu } = storeToRefs(routerStore);

const props = defineProps({
  short: {
    type: Boolean,
    default: false,
  },
  icon: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    default: '',
  },
  select: {
    // 是否选中
    type: Boolean,
    default: false,
  },
  route: {
    type: Object as PropType<RouteRecordNormalized>,
    required: true,
  },
});
const emits = defineEmits(['onShow']);

const onMouseEnter = () => {
  if (props.select) {
    isShowSecondMenu.value = true;
  }

  emits('onShow', true);
  !!props.route && routerStore.setFirstRoute(props.route);
};
// const onClickNavItem = () => {
//   isShowSecondMenu.value = true;
//   !!props.route && routerStore.setFirstRoute(props.route);
// };
</script>

<style scoped>
.nav-select {
  background: linear-gradient(110.28deg, rgba(37, 44, 56, 0.6) 0.2%, rgba(80, 94, 119, 0.6) 101.11%);
  box-shadow: 22.9696px 22.9696px 33.9814px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(25.486px);
}
</style>
