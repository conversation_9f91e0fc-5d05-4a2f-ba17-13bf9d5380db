<template>
  <div
    class="mb-[32px] cursor-pointer"
    :class="[short ? 'w-[34px] overflow-hidden' : 'w-[204px]']"
    @click="gotoHome"
  >
    <img
      class="h-[34px] max-w-none"
      :src="logoPath"
      alt="logo"
      @click="gotoHome"
    >
  </div>
</template>
<script setup lang="ts">
import logo from '@/assets/logo.png';
import test from '@/assets/img/logo/logo-test.png';
import exp from '@/assets/img/logo/logo-exp.png';
import prerelease from '@/assets/img/logo/logo-pre.png';
import { computed } from 'vue';
import { useEnv } from 'common/compose/env';
import { useGoto } from '@/router/goto';

defineProps({
  short: {
    type: Boolean,
    default: false,
  },
});


type LogoEnvType = 'test' | 'exp' | 'prerelease';
const logoMap = {
  default: logo,
  test,
  exp,
  prerelease,
};

const { env } = useEnv();
const logoPath = computed(() => logoMap[env.value as LogoEnvType] || logoMap.default);

const { gotoHome } = useGoto();

</script>
