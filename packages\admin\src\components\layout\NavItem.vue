<template>
  <div
    class="nav-animate overflow-hidden items-center flex justify-center
    hover:bg-[#202A41] hover:rounded-default cursor-pointer"
    :class="[
      short ? 'w-[48px] h-[48px]' :'w-[204px] h-[48px]',
      select ? 'nav-select rounded-default' : '',
    ]"
    :title="name"
    @mouseenter="onMouseenter"
    @click="onClickNavItem"
  >
    <div
      class="relative flex flex-row w-full space-x-[4px]"
    >
      <div
        class="w-[48px] h-[48px] flex items-center justify-center"
        :class="[select ? 'rounded-default bg-[rgba(170,202,255,.22)]' : '']"
      >
        <svg-icon
          :name="icon"
          size="24"
        />
      </div>
      <transition name="fade">
        <div
          v-if="!short"
          class="flex flex-col flex-1 h-[48px] pl-[16px] justify-center"
        >
          <Text :content="name" color="var(--aix-text-color-white-primary)" size="title" />
          <Text :content="desc" color="var(--aix-text-color-white-placeholder)" size="small" />
        </div>
      </transition>
      <div
        v-if="inUnreadList" class="absolute top-1.5 w-[8px] h-[8px] rounded-round bg-error-primary"
      />
      <!-- 左上角 new tip -->
      <div
        v-if="route.meta?.tipsCfg?.isShowNewTip"
        class="`new absolute left-[-4px] top-[0px] w-[24px] h-[24px] rounded-tl-[8px]
         border-[12px] border-t-[#FF6770] border-l-[#FF6770] border-r-[transparent] border-b-[transparent] `"
      >
        <div class="text-white-primary text-xs absolute left-[-14px] top-[-14px] scale-[0.7] rotate-[-45deg]">new</div>
      </div>
      <!-- 右上角 red_point -->
      <div
        v-if="route.meta?.tipsCfg?.isShowRedPointTip"
        class="absolute top-[5px] right-[5px] w-[8px] h-[8px] bg-[#FF6770] rounded-circle"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import SvgIcon from 'common/components/SvgIcon';
import { useRouterStore } from '@/store/global/router.store';
import { storeToRefs } from 'pinia';
import Text from 'common/components/Text';
import { computed } from 'vue';
import type { PropType } from 'vue';
import { type RouteRecordNormalized } from 'vue-router';
import { useNotificationStore } from '@/store/monitor/notification.store';
import { useGlobalGameStore } from '@/store/global/game.store';

const routerStore = useRouterStore();
const { isShowSecondMenu, allowRoutes } = storeToRefs(routerStore);

const props = defineProps({
  short: {
    type: Boolean,
    default: false,
  },
  icon: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    default: '',
  },
  desc: {
    type: String,
    default: '',
  },
  select: { // 是否选中
    type: Boolean,
    default: false,
  },
  route: {
    type: Object as PropType<RouteRecordNormalized>,
    required: true,
  },
});

const { unreadList } = storeToRefs(useNotificationStore());
const gameStore = useGlobalGameStore();

const inUnreadList = computed(() => {
  const optIdList = [...new Set(unreadList.value.map(item => item.opt_id))];
  let inUnread = false;
  function find(item: RouteRecordNormalized) {
    // 为了出发computed更新
    if (gameStore.newFunctionRed
    && gameStore.showFunctionRed((item.meta?.reportId as string), item.meta?.showNewFunctionRed)) {
      inUnread = true;
      return;
    }
    if (optIdList.includes(item.meta?.reportId as string)) {
      inUnread = true;
      return;
    }
    // 过滤 children有权限才可以
    item.children?.filter(cItem => (allowRoutes.value[gameStore.gameCode] || []).includes(cItem.name as string))
      .forEach((cItem) => {
        find(cItem as any);
      });
  }
  find(props.route);
  return inUnread;
});

const emits = defineEmits(['onShow']);
const onMouseenter = () => {
  if (props.select) {
    isShowSecondMenu.value = true;
  }

  emits('onShow', true);
  routerStore.setFirstRoute(props.route);
};

const onClickNavItem = () => {
  isShowSecondMenu.value = true;
  routerStore.setFirstRoute(props.route);
};
</script>
<style scoped>
.nav-animate {
  transition: width, height;
}

.nav-select {
  background: linear-gradient(110.28deg, rgba(37, 44, 56, 0.6) 0.2%, rgba(80, 94, 119, 0.6) 101.11%);
  box-shadow: 22.9696px 22.9696px 33.9814px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(25.486px);
}

.fade-enter-active{
  transition: opacity 0.3s ease;
}

.fade-leave-from {
  opacity: 0;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
