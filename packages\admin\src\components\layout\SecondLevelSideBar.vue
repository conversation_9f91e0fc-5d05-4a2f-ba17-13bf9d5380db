<template>
  <div
    ref="secondSideBarRef"
    class="absolute h-full transition-[width] duration-300 z-10"
    :class="isCollapse || isHover ? 'w-[220px]' : 'w-0'"
    @transitionend="(ev: any) => {if (!isCollapse) ev.target.removeAttribute('style');}"
  >
    <div class="w-full h-full overflow-hidden">
      <div
        class="w-full h-full border-gray-primary border-r-[1px] border-solid
        py-[24px] px-[16px] bg-white-primary overflow-x-scroll narrow-scrollbar"
      >
        <div class="font-bold text-xl text-black-primary mb-4 px-2 box-border">{{ routeTitle }}</div>
        <ul
          v-auto-animate
          class="w-full flex space-y-[16px] flex-col"
        >
          <secondLevelSideBarItem
            v-for="(router, index) in secondRouter"
            :key="router.meta?.name"
            :index="index"
            :router="router"
            :active="activeRouter"
          />
        </ul>
      </div>
    </div>
    <div
      class="absolute top-0 bottom-0 right-[-9px] my-auto shadow-[0_3px_6px_0_rgba(0,0,0,.03)]
      flex justify-center items-center w-5 h-[60px] rounded-default bg-white-primary z-10 cursor-pointer
      border border-black-disabled border-solid box-border
      hover:shadow-[0_3px_6px_0_rgba(0,0,0,.1)] hover:border-brand"
      @click="collapseEvent()"
    >
      <svg-icon
        name="arrow"
        size="12"
        class="transition"
        :class="isCollapse || isHover ? 'rotate-[90deg]' : 'rotate-[-90deg]'"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { useRoute } from 'vue-router';
import { computed, onMounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useRouterStore } from '@/store/global/router.store';
import SecondLevelSideBarItem from '@/components/layout/SecondLevelSideBarItem.vue';
import SvgIcon from 'common/components/SvgIcon';
import { useStorage } from '@vueuse/core';
import { getSecondRouter } from 'common/utils/router';

const secondSideBarRef = ref(null);
const isCollapse = useStorage('aix-left-nav-collapse', false, localStorage);
const { nowGameAllowRoutes, firstRoute } = storeToRefs(useRouterStore());
const { setFirstRoute } = useRouterStore();
const currentRoute = useRoute();

defineProps({
  isHover: {
    type: Boolean,
    default: false,
  },
});

// const secondRouter = computed(() => {
//   if (!firstRoute?.value?.children?.length) {
//     return [];
//   }
//   return firstRoute?.value?.children
//     ?.map((route) => {
//       const isHide = !route?.meta?.hide;
//       const isAllow = isHide && route?.meta?.icon && isString(route.name)
//         && nowGameAllowRoutes.value.includes(route.name);
//       if (!isAllow) return false;

//       const allowChildren = route?.children?.filter(route => (isString(route.name)
//           && nowGameAllowRoutes.value.includes(route.name))
//         || route.name === currentRoute?.name); // 用于匹配当前命中当前路径的资源目录

//       if (route?.meta?.dir && (Array.isArray(allowChildren) ? allowChildren : []).length === 0) return false;

//       return {
//         ...route,
//         children: allowChildren,
//       };
//     })
//     ?.filter(v => v) as Array<RouteRecordRaw>;
// });

const secondRouter = computed(() => getSecondRouter(firstRoute?.value, currentRoute, nowGameAllowRoutes.value));

// 根据路径来确定默认的active router
const activeRouter = ref<string>('');

const collapseEvent = () => {
  setFirstRoute(currentRoute.matched[0]);
  isCollapse.value = !isCollapse.value;

  if (isCollapse.value) {
    (secondSideBarRef.value as unknown as HTMLDivElement).style.position = 'relative';
  }
};

onMounted(() => {
  if (isCollapse.value) {
    (secondSideBarRef.value as unknown as HTMLDivElement).style.position = 'relative';
  }
});

const routeTitle = ref<string | undefined>('');
watch(
  [() => currentRoute?.matched, () => secondRouter.value],
  ([currentRoute, secondRouter]) => {
    routeTitle.value = firstRoute?.value?.meta.title || firstRoute?.value?.meta.name;

    activeRouter.value = (secondRouter
      .find(r => r.name === currentRoute?.[1]?.name)?.name as string) || '';
  },
);

const showSecondSideBar = computed(() => true);

defineExpose({ showSecondSideBar });
</script>
<style scoped>
.absolute_ {
  position: absolute;
}

.nav-item {
  @apply w-[190px] h-[32px] flex items-center px-[8px] py-[4px] select-none rounded-default
}

.nav-item:hover {
  @apply bg-white-secondary;
}

.router-link-active {
  @apply rounded-default bg-white-primary;
}
</style>
