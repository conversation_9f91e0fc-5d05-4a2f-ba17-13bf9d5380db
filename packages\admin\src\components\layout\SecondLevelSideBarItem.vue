<template>
  <li
    ref="routerItem"
    v-auto-animate
    class="w-[188px] cursor-pointer"
  >
    <div
      v-if="showChildren"
      class="nav-item justify-between"
      @click="() => showChildren && toggleExpended()"
    >
      <div class="flex items-center">
        <SvgIcon
          v-if="router?.meta?.icon"
          :key="router?.meta?.icon"
          class="mr-[8px] inline-block"
          :name="router?.meta?.icon"
          size="16"
        />
        <Text
          overflow
          tool-tip
          type="navTitle"
          class="font-bold"
          color="var(--aix-text-color-black-primary)"
          :content="router?.meta?.title || router?.meta?.name"
        />
      </div>
      <t-icon
        v-if="showChildren"
        class="w-[14px] transition"
        :class="{'-rotate-90': !expended}"
        name="chevron-down"
      />
    </div>
    <router-link
      v-else
      class="nav-item justify-between"
      :class="{
        'router-link-active': active === router.name
      }"
      :to="router"
      @click="onUpdateReadRouteName(router)"
    >
      <div
        class="flex items-center"
        @click="routerStore.onRouterClick"
      >
        <SvgIcon
          v-if="router?.meta?.icon"
          :key="router?.meta?.icon"
          class="mr-[8px] inline-block"
          :name="router?.meta?.icon"
          size="16"
        />
        <Text
          overflow
          tool-tip
          type="navTitle"
          class="font-bold"
          color="var(--aix-text-color-black-primary)"
          :content="router?.meta?.title || router?.meta?.name"
        />
        <!-- new 提示 -->
        <img
          v-show="router?.meta?.tipsCfg?.isShowNewTip"
          class="ml-[4px] w-[24px] h-[15px]" :src="newImg" alt=""
        >
        <!-- red_point 提示 -->
        <div
          v-if="router?.meta?.tipsCfg?.isShowRedPointTip"
          class="ml-[4px] w-[8px] h-[8px] bg-[#FF6770] rounded-circle"
        />
        <div
          v-if="inUnreadList(router?.meta)"
          class="absolute top-1 left-4 w-[8px] h-[8px] rounded-round bg-error-primary"
        />
      </div>
    </router-link>
    <ul v-if="expended && showChildren" class="mt-[10px] space-y-[10px]">
      <li
        v-for="(subRouter, i) in visibleChildren"
        :key="`${router.meta?.name}-${subRouter.meta?.name || i}`"
        class="relative"
        @click.stop
      >
        <router-link
          class="nav-item pl-[32px] text-black-secondary"
          :to="subRouter"
          @click="onUpdateReadRouteName(subRouter)"
        >
          <Text
            overflow
            tool-tip
            size="normal"
            class="font-bold"
            color="var(--aix-text-color-black-primary)"
            :content="subRouter?.meta?.title || subRouter?.meta?.name"
            @click="routerStore.onRouterClick"
          />
          <!-- new 提示 -->
          <img
            v-show="subRouter?.meta?.tipsCfg?.isShowNewTip"
            class="ml-[4px] w-[24px] h-[15px]" :src="newImg" alt=""
          >
          <!-- red_point 提示 -->
          <div
            v-if="subRouter?.meta?.tipsCfg?.isShowRedPointTip"
            class="ml-[4px] w-[8px] h-[8px] bg-[#FF6770] rounded-circle"
          />
          <!-- <div
            :class="`ml-[10px] h-[22px] bg-[#FF6770] text-[10px] text-[#fff]
             rounded-[5px] rounded-bl-none pl-[4px] pr-[4px] scale-[0.7]`"
          >
            NEW
          </div> -->
        </router-link>
        <div
          v-if="inUnreadList(subRouter?.meta)"
          class="absolute top-1 left-4 w-[8px] h-[8px] rounded-round bg-error-primary"
        />
      </li>
    </ul>
  </li>
</template>
<script setup lang="ts">
import { computed, PropType } from 'vue';
import { RouteRecordRaw } from 'vue-router';
import { get, useToggle } from '@vueuse/core';
import { isEmpty } from 'lodash-es';
import SvgIcon from 'common/components/SvgIcon';
import Text from 'common/components/Text';
import { storeToRefs } from 'pinia';
import { useNotificationStore } from '@/store/monitor/notification.store';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useRoutesTipsStore } from '@/store/global/routes-tips.store';
import newImg from '@/assets/img/new.png';
import { useRouterStore } from '@/store/global/router.store';

const routerStore = useRouterStore();

const props = defineProps({
  router: {
    type: Object as PropType<RouteRecordRaw>,
    default: () => {},
  },
  active: {
    type: String,
    default: '',
  },
});

const { unreadList } = storeToRefs(useNotificationStore());
const [expended, toggleExpended] = useToggle(true);

const routesTipsStore = useRoutesTipsStore();

const visibleChildren = computed(() => (props.router.children || [])
  .filter(r => !r?.meta?.hide && (r?.meta?.title || r?.meta?.name)));

const showChildren = computed(() => !isEmpty(get(visibleChildren)));
const gameStore = useGlobalGameStore();

const inUnreadList = (meta: any) => {
  if (!meta?.reportId) return false;
  if (gameStore.showFunctionRed(meta?.reportId, meta?.showNewFunctionRed)) {
    return true;
  }
  const optIdList = [...new Set(unreadList.value.map(item => item.opt_id))];
  return optIdList.includes(meta?.reportId);
};

function onUpdateReadRouteName(routeItem: RouteRecordRaw) {
  if (routerStore.isPageLoading) return;
  routesTipsStore.updateReadRouteName(routeItem);
  // 在新的标签页中打开
  const { isOpenInNewTab, url } = routeItem?.meta || {};
  if (isOpenInNewTab === 1 && url) {
    window.open(url);
  }
}
</script>
<style scoped>
.nav-item {
  @apply w-[190px] h-[32px] flex items-center px-[8px] py-[4px] select-none rounded-default
}

.nav-item:hover {
  background: rgba(240, 241, 246, 0.6);
}

.nav-item.pl-\[32px\] {
  padding-left: 32px;
}

.router-link-active {
  /* background: #F0F1F6; */
  @apply rounded-default bg-background;
}

</style>
