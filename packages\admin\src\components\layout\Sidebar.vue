<template>
  <t-aside
    class="h-full bg-left-tree flex-none flex flex-col pt-[24px] px-[16px] will-change-auto
     justify-between items-center overflow-hidden close-animation exclude-mode z-10"
    :style="{ width: collapsed ? '70px': '236px'}"
  >
    <!-- 一级菜单 -->
    <div class="sidebar-header flex flex-col items-center">
      <logo :short="collapsed" />
      <div class="space-y-[24px]">
        <select-game-code :short="collapsed" />
        <router-link
          v-for="router in topRoute"
          :key="router.path"
          :to="router.path"
          class="block"
        >
          <NavItem
            :short="collapsed"
            :icon="router.meta.icon"
            :name="router.meta.title || router.meta.name"
            :desc="router.meta.desc"
            :route="router"
            :select="currentRoute?.matched[0]?.path === router.path"
            @on-show="showEvent"
            @click="routerStore.onRouterClick"
          />
        </router-link>
      </div>
    </div>
    <!-- 底部区域 -->
    <div
      class="sidebar-footer pb-[24px] flex flex-col"
      :class="{
        'items-center': collapsed,
        'items-end': !collapsed,
      }"
    >
      <div :style="`transform:translateY(${(collapsed?0:'52px') })`">
        <!-- 用户引导 -->
        <div
          class="mb-[27px]"
        >
          <t-tooltip
            overlay-class-name="aix-tooltip"
            content="User Guides"
            theme="light"
            placement="top-right"
            destroy-on-close
          >
            <div
              class="w-[36px] h-[36px] bg-[#202A41] rounded-default cursor-pointer
              hover:opacity-25 flex items-center justify-center"
            >
              <a href="https://aix.levelinfinite.com/docs/user_guide/desc.html" target="_blank">
                <BookOpenIcon size="18" class="text-[#fff]" />
              </a>
            </div>
          </t-tooltip>
        </div>

        <!-- 底部菜单 -->
        <div class="space-y-[33px]">
          <router-link
            v-for="router in footRoute"
            :key="router.path"
            :to="router.path"
            class="block"
          >
            <t-tooltip
              overlay-class-name="aix-tooltip"
              :content="router.meta.title || router.meta.name"
              theme="light"
              placement="top-right"
              destroy-on-close
            >
              <FootNavItem
                :short="collapsed"
                :icon="router.meta.icon"
                :route="router"
                :name="router.meta.title || router.meta.name"
                :select="currentRoute?.matched[0]?.path === router.path"
                @on-show="showEvent"
                @click="routerStore.onRouterClick"
              />
            </t-tooltip>
          </router-link>
          <div
            class="w-[36px] h-[36px] cursor-pointer flex justify-center text-white items-center
            hover:opacity-25 bg-[#202A41] rounded-default"
            @click="toggleDark"
          >
            <t-tooltip
              overlay-class-name="aix-tooltip"
              :content="isDark?'Dark':'Light'"
              theme="light"
              placement="top-right"
              destroy-on-close
            >
              <ModeDarkIcon v-if="isDark" class="w-[18px] h-[18px]" />
              <ModeLightIcon v-else class="w-[18px] h-[18px]" />
            </t-tooltip>
          </div>
        </div>
        <!-- 登录区域 -->
        <div
          class="flex items-center cursor-pointer mt-[27px] transition-[width]"
          :class="[collapsed ? 'w-[32px]' : 'w-[204px] pl-[8px]']"
        >
          <div class="w-[32px] h-[32px]">
            <t-tooltip
              overlay-class-name="aix-tooltip"
              :content="authStageStore.currentUser"
              theme="light"
              placement="top-right"
              destroy-on-close
            >
              <Avatar
                :src="getAvatarIconUrl()"
                :name="authStageStore.currentUser"
                :size="32"
                @on-logout="authStageStore.logout"
              />
            </t-tooltip>
          </div>
          <span
            v-if="!collapsed"
            class="text-white-primary text-sm ml-[16px] normal-hover"
            @click="authStageStore.logout"
          >
            {{ t(authStageStore.logined ? 'Logout' : 'Login') }}
          </span>
        </div>
      </div>
      <!-- 返回区域 -->
      <!-- <div
        class="w-[30px] h-[30px] bg-[#202A41] rounded-default mt-[23px] fill-white-primary hover:fill-brand arrow-nav
        transition cursor-pointer duration-300 flex items-center justify-center"
        :style="`transform: rotate(${collapsed ? '270' : '90'}deg)`"
      >
        <svg-icon
          name="arrow"
          size="15"
        />
      </div> -->
    </div>
  </t-aside>
</template>
<script setup lang="ts">
import { computed, onMounted, onBeforeMount } from 'vue';
import { useRoute } from 'vue-router';
import { useWindowSize, watchDebounced, useStorage, useDark } from '@vueuse/core';
import { BookOpenIcon, ModeLightIcon, ModeDarkIcon } from 'tdesign-icons-vue-next';
import SelectGameCode from '../selectGame/SelectGameCode.vue';
import Logo from '@/components/layout/Logo.vue';
import { useI18n } from 'common/compose/i18n';
import { I18N_HOME } from 'common/const/i18n';
import NavItem from '@/components/layout/NavItem.vue';
import { reduceCompare } from 'common/utils/compare';
import FootNavItem from '@/components/layout/FootNavItem.vue';
import { useAuthStageStore } from '@/store/global/auth.store';
import { useRouterStore } from '@/store/global/router.store';
import { storeToRefs } from 'pinia';
import { isString, isUndefined } from 'lodash-es';
import Avatar from 'common/components/Avatar.vue';
import { getLoginType } from 'common/utils/auth';
import { getSecondRouter } from 'common/utils/router';
import { useEnv } from 'common/compose/env';

const { t } = useI18n([I18N_HOME]);

const isDark = useDark({
  selector: 'html',
  attribute: 'class',
  valueDark: 'dark-mode',
  valueLight: 'light',
});
const toggleDark = () => {
  isDark.value = !isDark.value;
};


const { isFuncom } = useEnv();
const routerStore = useRouterStore();
const { allRoutes, nowGameAllowRoutes, collapsed, isShowSecondMenu } = storeToRefs(routerStore);
const currentRoute = useRoute();

const [TOP_LEVEL, FOOT_LEVEL] = [1, -1];
const getRoutesByLevel = (level: number) => allRoutes.value
  .filter(route => route.meta.level === level
    && isString(route.name) && !!nowGameAllowRoutes.value.includes(route.name))
  .sort((pre, nex) => reduceCompare(Number(pre.meta.index), Number(nex.meta.index)));

const topRoute = computed(() => {
  // 临时的一级路由
  const tempTopRouter = getRoutesByLevel(TOP_LEVEL);
  // 国内环境下 不过滤一级路由
  if (!isFuncom.value) return tempTopRouter;
  return tempTopRouter.filter((topRouteItem) => {
    // 是否有子路由？
    const isHasChildren = getSecondRouter(
      tempTopRouter.find(item => item?.name?.toString() === topRouteItem.name),
      currentRoute,
      nowGameAllowRoutes.value,
    ).length > 0;
    return isHasChildren;
  });
});
const footRoute = computed(() => getRoutesByLevel(FOOT_LEVEL));

const MIN_POINT = 1728 + 1; // 屏幕最小的展示断点
const { width } = useWindowSize();
const isClickCollapsed = useStorage('menu.isClickCollapsed', false, localStorage); // 用户是否点击了折叠-展开按钮

onBeforeMount(() => {
  if (!collapsed.value) {
    collapsed.value = true;
  }
});
onMounted(() => {
  if (isUndefined(collapsed.value) && isUndefined(isShowSecondMenu.value)) {
    collapsed.value = width.value <= MIN_POINT;
    isShowSecondMenu.value = width.value > MIN_POINT;
  }
});

watchDebounced(width, () => {
  if (!isClickCollapsed.value) {
    collapsed.value = width.value <= MIN_POINT;
    isShowSecondMenu.value = width.value > MIN_POINT;
  }
});

watchDebounced(() => collapsed.value, (val) => {
  if (!val) {
    collapsed.value = true;
  }
});


const authStageStore = useAuthStageStore();

const getAvatarIconUrl = () => {
  const loginType = getLoginType();
  if (loginType === '1') {
    return authStageStore.currentUser ? `https://dayu.woa.com/avatars/${authStageStore.currentUser}/avatar.jpg`
      : 'data:image/gif;base64,R0lGODlhAQABAIAAAMLCwgAAACH5BAAAAAAALAAAAAABAAEAAAICRAEAOw==';
  }
  return undefined;
};

const emits = defineEmits(['onShow']);
const showEvent = (val: boolean) => {
  emits('onShow', val);
};

defineExpose({ collapsed });

</script>
<style lang="scss" scoped>
.close-animation {
  transition-duration: .3s;
  transition-property: width;
}

.bg-left-tree {
  --tw-bg-opacity: 1;
  background-color: rgb(2 17 41 / var(--tw-bg-opacity)) !important;
}

:global(.aix-tooltip .t-popup__content--arrow .t-popup__arrow) {
  bottom: auto
}
</style>
