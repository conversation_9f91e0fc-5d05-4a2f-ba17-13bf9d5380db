<template>
  <div :class="['flex',' flex-col' , 'items-center' ,'justify-center',...classStyle ]">
    <img
      :src="imgSrc"
      class="h-[160px] w-[160px]"
    >
    <p class="text-black-primary opacity-60 text-xs mb-[20px]">
      {{ showText }}
    </p>
    <span v-if="isShowBtn">
      <t-button
        theme="primary"
      >
        <template #icon>
          <icon name="add-rectangle" />
        </template>
        {{ btnShowText }}
      </t-button>
    </span>
  </div>
</template>

<script setup lang="ts">
import {  Icon } from 'tdesign-icons-vue-next';
import configureEmptyPng from '@/assets/img/configureEmpty.png';

defineProps({
  showText: {
    type: String,
    default: '暂无发布赛事',
  },
  isShowBtn: {
    type: Boolean,
    default: false,
  },
  btnShowText: {
    type: String,
    default: '创建主赛事',
  },
  classStyle: {
    type: Array,
    default: () => [],
  },
  imgSrc: {
    type: String,
    default: configureEmptyPng,
  },
});

</script>

