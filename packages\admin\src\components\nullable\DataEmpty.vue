<template>
  <div class="flex flex-col items-center justify-center">
    <img
      :src="searchResultEmpty"
      :style="styleObject"
    >
    <slot name="content"> <div class="text-black-primary opacity-60">Data Empty</div> </slot>
    <!-- <p class="text-black-primary opacity-60">Data Empty</p> -->
  </div>
</template>

<script setup lang="ts">
import searchResultEmpty from '@/assets/img/empty.png';
import { StyleValue, computed } from 'vue';

interface IProps {
  imageSize?: {
    height?: number | string;
    width?: number | string;
  };
}
const props = withDefaults(defineProps<IProps>(), {
  imageSize: () => ({
    height: '160px',
    width: '160px',
  }),
});

const styleObject = computed<StyleValue>(() => ({
  height: serializeImageSize(props?.imageSize?.height ?? '') ?? undefined,
  width: serializeImageSize(props?.imageSize?.width ?? '') ?? undefined,
}));

const serializeImageSize = (propertyVal: number | string) => {
  if (typeof propertyVal === 'number') {
    return `${propertyVal}px`;
  }
  return propertyVal;
};
</script>
