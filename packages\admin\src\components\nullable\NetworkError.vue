<template>
  <div class="flex flex-col items-center justify-center w-full h-[100vh] bg-white-primary">
    <img
      :src="networkError"
      class="h-[160px] w-[160px]"
    >
    <t-space direction="vertical" class="flex items-center">
      <p class="text-black-primary opacity-60">
        Oops!!! Page Not Found!
      </p>
      <t-button @click="reload">
        reload
      </t-button>
    </t-space>
  </div>
</template>

<script setup lang="ts">
import networkError from '@/assets/img/networkError.png';

const reload = () => {
  window.location.reload();
};
</script>

