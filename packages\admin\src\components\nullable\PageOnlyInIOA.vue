<template>
  <div class="flex flex-col items-center justify-center w-full h-[100vh] bg-white-primary">
    <img
      :src="permissionDeny"
      class="h-[160px] w-[160px]"
    >
    <t-space direction="vertical" class="flex items-center">
      <p class="text-black-primary opacity-60">
        This page can only be accessed with IOA environment
      </p>
      <t-button @click="onReload">reload</t-button>
    </t-space>
  </div>
</template>
<script lang="ts" setup>
import permissionDeny from '@/assets/img/permissionDeny.png';

function onReload() {
  window.location.reload();
}
</script>
