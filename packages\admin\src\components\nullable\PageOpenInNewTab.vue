<template>
  <div class="flex flex-col items-center justify-center w-full h-[100vh] bg-white-primary">
    <img
      :src="networkError"
      class="h-[160px] w-[160px]"
    >
    <t-space direction="vertical" class="flex items-center">
      <p class="text-black-primary opacity-60">
        This is an external page, click the button below to open it in a new tab
      </p>
      <t-button
        :href="props.url"
        target="_blank"
      >
        Open in new tab
      </t-button>
    </t-space>
  </div>
</template>
<script lang="ts" setup>
import networkError from '@/assets/img/networkError.png';
const props = defineProps({
  url: {
    type: String,
    default: () => '',
  },
});
</script>

