<template>
  <div class="flex flex-col items-center justify-center w-full h-[100vh] bg-white-primary">
    <img
      :src="permissionDeny"
      class="h-[160px] w-[160px]"
    >
    <t-space direction="vertical" class="flex justify-center">
      <p v-if="loading">
        <loading-icon class="text-brand" />
        Checking permissions...
      </p>
      <template v-else>
        <p class="text-black-primary opacity-60">
          No access permission
        </p>
        <t-button v-if="gameStore.gameCode" @click="contactSupport">Access Request</t-button>
        <t-button v-else>Select Game</t-button>
      </template>
    </t-space>
  </div>
</template>

<script setup lang="ts">
import permissionDeny from '@/assets/img/permissionDeny.png';
import { LoadingIcon } from 'tdesign-icons-vue-next';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useConcatUs } from 'common/compose/concatus';

defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
});

const gameStore = useGlobalGameStore();

function contactSupport() {
  const emailUrl = useConcatUs().concatUsWay.value;
  const a = document.createElement('a');
  a.href = emailUrl;
  a.style.display = 'none';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

</script>

