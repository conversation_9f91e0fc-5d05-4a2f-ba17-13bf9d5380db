<template>
  <div class="w-auto max-h-[65vh] overflow-hidden">
    <div
      v-auto-animate
      class="flex flex-row m-[16px]"
    >
      <div
        class="w-[188px]"
      >
        <div class="space-y-[16px]">
          <t-input v-model="filterGame" class="bg-transparent" clearable>
            <template #suffixIcon>
              <SearchIcon />
            </template>
          </t-input>
          <div v-if="filterGame.length" class="space-y-[8px] flex flex-col">
            <Text
              v-for="i in allGameList.filter(i=> i.connect.toLowerCase().includes(filterGame.toLowerCase()))"
              :key="i.connect"
              :content="i.connect"
              color="var(--aix-text-color-white-primary)"
              class="normal-hover"
              @click="selectGame(i)"
            />
          </div>
          <div v-else class="space-y-[24px] max-h-[55vh] overflow-y-auto narrow-scrollbar">
            <div v-if="recentGameList.length" class="space-y-[16px]">
              <p
                class="text-white-primary text-sm"
              >
                Recent Game List
              </p>
              <div class="space-y-[16px]">
                <div
                  v-for="item in recentGameList.slice(-3).reverse()"
                  :key="item.connect"
                  class="rounded-default bg-[#3a435d] cursor-pointer hover:bg-[#465F8C]
          flex items-center"
                  @click="selectGame(item)"
                >
                  <div class="w-[48px] h-[48px] rounded-default bg-gradient-to-t bg-[length:48px_48px]">
                    <game-icon
                      :icon="getIconByLoginType(item)"
                    />
                  </div>
                  <div
                    class="game-title ml-[16px]"
                  >
                    <span class="text-white-primary w-[100px] block select-none">{{ item.connect }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="space-y-[16px]">
              <p
                class="text-white-primary text-sm"
              >
                {{ title }}
              </p>
              <div class="space-y-[16px]">
                <div
                  v-for="item in options"
                  :key="item.value"
                  v-element-hover="hoverGame(item.children)"
                  class="rounded-default bg-[#3a435d] cursor-pointer hover:bg-[#465F8C]
          flex items-center"
                  :class="[
                    checkSelect(item) ? 'bg-[#465F8C]' : ''
                  ]"
                  @click="selectGame(item)"
                >
                  <div
                    class="w-[48px] h-[48px] rounded-default bg-gradient-to-t bg-[length:48px_48px] flex-none"
                  >
                    <game-icon
                      :icon="getIconByLoginType(item)"
                    />
                  </div>
                  <Justify
                    class="game-title ml-[16px]"
                  >
                    <template #left>
                      <span class="text-white-primary w-[94px] block select-none">
                        {{ item.connect }}
                      </span>
                    </template>
                    <template #right>
                      <t-icon
                        v-if="item.children?.length"
                        name="chevron-down"
                        class="text-gray-secondary mr-[8px] -rotate-90"
                      />
                    </template>
                  </Justify>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="showGameDetailList" class="right-box">
        <p
          class="text-white-primary my-[4px] mx-[3px] mb-[20px] text-sm w-[136px] "
        >
          Game List
        </p>
        <div class="w-full space-y-[20px] overflow-y-auto narrow-scrollbar max-h-[55vh]">
          <p
            v-for="(child, index) in hoverGameList"
            :key="index"
            class="text-white-primary w-full truncate block leading-[16px] select-none normal-hover"
            :class="{'opacity-30': !checkSelect(child), 'active-game-item': currentGame !== child.value}"
            @click="selectGame(child)"
          >
            {{ child.connect }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import type { GameItem } from './type';
import type { PropType } from 'vue';
import { computed, onUnmounted, ref, toRaw } from 'vue';
import Justify from 'common/components/Layout/Justify.vue';
import Text from 'common/components/Text';
import { vElementHover } from '@vueuse/components';
import { get, useStorage } from '@vueuse/core';
import { useRouteQuery } from '@vueuse/router';
import { SearchIcon } from 'tdesign-icons-vue-next';
import { uniqBy, omit } from 'lodash-es';
import GameIcon from '@/views/configuration/business/components/GameIcon.vue';
import { getLoginType } from 'common/utils/auth';
import { getGameIcon } from '@/store/global/game.store';
import gameIconDefaultSVG from '@/assets/svg/game-icon_default.svg';
import { useRouterStore } from '@/store/global/router.store';

const routerStore = useRouterStore();

const props = defineProps({
  options: {
    type: Array as PropType<GameItem[]>,
    default: () => [],
  },
  title: {
    type: String,
    default: '',
  },
  defaultSelectValue: {
    type: Object as PropType<GameItem>,
    default: () => {
    },
  },
});

const getIconByLoginType = (item: GameItem) => {
  const loginType = getLoginType();
  if (loginType === '2') {
    return item?.icon ?? gameIconDefaultSVG;
  }
  return item?.icon || getGameIcon({ icon: item?.icon, code: item?.connect });
};

const checkSelect = (item: GameItem): Boolean => Boolean(props.defaultSelectValue?.value === item.value)
  || Boolean(item.children?.map(i => i.value)
    .includes(props.defaultSelectValue?.value as string));

const storageRecentGame = useStorage<GameItem[]>('game.recentGame', [], localStorage);
// 从存储列表中，过滤出，自己当前有权限的游戏
const recentGameList = computed(() => get(storageRecentGame)
  .filter(i => get(allGameListName)
    .includes(i.connect) && !checkSelect(i)));

const visible = ref(false);
const emit = defineEmits(['gameValue']);

const selectGame = (item: GameItem) => {
  // 有子节点，不让它选择
  if (item.children?.length) return;
  // 当路由正在loading时，阻止本次跳转
  if (routerStore.isPageLoading) return;
  const storageList = toRaw(get(storageRecentGame));
  storageList.push(omit(toRaw(item), 'parent'));
  storageRecentGame.value = uniqBy(storageList, 'connect')
    .slice(-4);
  emit('gameValue', item);
  filterGame.value = '';
  visible.value = false;
};


const hoverGameList = ref<GameItem[]>(props.options?.find?.(i => checkSelect(i))?.children || []);
const currentGame = useRouteQuery<string>('game');
const hoverGame = (gameList?: GameItem[]) => (state: boolean) => {
  if (state) {
    hoverGameList.value = gameList || [];
  }
};

const allGameList = computed(() => props.options
  ?.map(i => (i.children ? i.children : i))
  .flat());
const allGameListName = computed(() => get(allGameList)
  .map(i => i.connect));

const filterGame = ref('');
const showGameDetailList = computed(() => get(hoverGameList).length > 0 && get(filterGame).length === 0);

onUnmounted(() => {
  filterGame.value = '';
});

</script>
<style scoped>
.right-box {
  border-left: 1px solid rgba(112, 112, 112, 0.2);
  padding-left: 16px;
  padding-top: 8px;
  margin-left: 16px;
}

.active-game-item {
  opacity: .4;
}
</style>
