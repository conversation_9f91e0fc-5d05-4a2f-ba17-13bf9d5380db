<template>
  <t-popup
    v-model:visible="visible" placement="bottom-left"
    overlay-inner-class-name="bg-[#3a435d] rounded-default shadow-none p-0  overflow-hidden"
  >
    <div
      variant="outline" class="relative flex justify-between items-center rounded-default game-icon-item
      cursor-pointer h-[48px] border-black-secondary transition-[width] bg-white bg-opacity-10"
      :class="[short ? '' : 'w-[204px] border']"
    >
      <div
        class="w-[48px] h-[48px] p-[8px] rounded-default bg-gradient-to-t bg-no-repeat bg-cover"
      >
        <game-icon
          :icon="getIconByLoginType()"
        />
      </div>
      <div v-if="!short" class="game-title flex justify-between items-center pl-[16px] pr-[8px] flex-1">
        <Text
          class="block w-[84px]" :class="{ 'w-0': short }" color="var(--aix-text-color-white-primary)"
          size="title"
          :content="gameStore?.currentGameItem?.connect" overflow tool-tip
        />
        <t-icon name="chevron-down" class="text-gray-secondary" />
      </div>
      <div
        class="flex justify-center items-center absolute
        w-4 h-4 bg-game-icon rounded-game-icon bottom-1 right-1 p-0.5 box-border z-10"
      >
        <SvgIcon
          name="change"
          size="14"
          color="#fff"
        />
      </div>
    </div>
    <template #content>
      <Dropdown
        :options="gameStore.gameList" title="All Game List" :default-select-value="gameStore.currentGameItem"
        @game-value="updateGameValue"
      />
    </template>
  </t-popup>
</template>
<script setup lang="ts">
import Dropdown from './Dropdown.vue';
import type { GameItem } from './type';
import { useGlobalGameStore, getGameIcon } from '@/store/global/game.store';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Text from 'common/components/Text';
import { getLoginType } from 'common/utils/auth';
import GameIcon from '@/views/configuration/business/components/GameIcon.vue';
import gameIconDefaultSVG from '@/assets/svg/game-icon_default.svg';
import SvgIcon from 'common/components/SvgIcon';

defineProps({
  short: {
    type: Boolean,
    default: false,
  },
});

const gameStore = useGlobalGameStore();
const visible = ref(false);
const router = useRouter();
const route = useRoute();

const updateGameValue = (item: GameItem) => {
  // 如果没有children的话就代表有game code
  // 通过更改路由来更改gameCode
  router.replace({
    path: route.path.toString(),
    query: {
      game: item.value,
      cgiMode: route.query.cgiMode,
      dbMode: route.query.dbMode,
    },
  });
  visible.value = false;
};


const getIconByLoginType = () => {
  const loginType = getLoginType();
  if (loginType === '2') {
    return gameStore?.currentGameItem?.icon ?? gameIconDefaultSVG;
  }
  return gameStore?.currentGameItem?.icon || getGameIcon(gameStore?.currentGameItem as any);
};

</script>
<style>
.game-icon-item:hover {
  background: linear-gradient(110.28deg, rgba(37, 44, 56, 0.6) 0.2%, rgba(80, 94, 119, 0.6) 101.11%);
  box-shadow: 23px 23px 34px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(25px);
  @apply rounded-default;
  border-color: transparent;
}
</style>
