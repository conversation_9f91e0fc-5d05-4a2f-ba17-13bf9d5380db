import { reactive } from 'vue';
import { DepotToken } from 'common/service/creative/label/manage/type';
import { getArthubToken } from 'common/service/creative/common/material';
import { getPreviewUrl } from 'common/service/creative/library/get-preview-url';
import { storeToRefs } from 'pinia';
import { useGlobalGameStore } from '@/store/global/game.store';

export const useAssetPreview = ()  => {
  const { gameCode } = storeToRefs(useGlobalGameStore());
  const depotToken = reactive<DepotToken>({
    arthubCode: '', publicToken: '', type: 1,
  });

  const getDepotToken = async (game: string) => {
    const data = await getArthubToken(game, true);
    if (data) {
      depotToken.type = Number(data.type);
      depotToken.arthubCode = data.arthub_code;
      depotToken.publicToken = data.public_token;
    }
  };

  const getVideoUrl = async (asset_id: string, initToken = false) => {
    // 首次请求，获取凭证信息
    if (initToken && !depotToken.arthubCode) {
      await getDepotToken(gameCode.value);
    }

    let url = '';
    if (depotToken.type === 2) {
      url = `https://drive.google.com/file/d/${asset_id}/preview`; // google-drive
    } else {
      url = await getPreviewUrl({
        arthubCode: depotToken.arthubCode,
        publicToken: depotToken.publicToken,
        assetId: asset_id,
      });
    }
    return url;
  };

  return {
    depotToken, getDepotToken, getVideoUrl,
  };
};
