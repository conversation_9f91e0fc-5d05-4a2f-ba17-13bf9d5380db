import { ref, Ref }  from 'vue';
import type { ISelector } from 'common/components/filterContainer/type';
import { cloneDeep } from 'lodash-es';

export interface UseCommonSearchReturn<FormT, SchemaT extends ISelector[]> {
  form: Ref<FormT>,
  schema: Ref<SchemaT>,
  defaultForm: Ref<FormT>,
  defaultSchema: Ref<SchemaT>,
  setForm: (newForm: FormT) => void,
  updateForm: (key: string, value: any) => void,
  updateSchema: (newSchema: SchemaT) => void,
  updateDefaultForm: (data: any) => void,
  updateDefaultSchema: (schema: Ref<SchemaT[]>) => void,
}

export const useCommonSearch = <FormT extends {[key: string]: any}, SchemaT extends ISelector[]>
  (): UseCommonSearchReturn<FormT, SchemaT> => {
  const form = ref<any>({});
  const schema = ref<any>([]);

  const defaultForm = ref<any>({});
  const defaultSchema = ref<any>([]);

  const setForm = (newForm: FormT): void => {
    Object.keys(newForm).forEach((key) => {
      form.value[key] = newForm[key];
    });
    updateDefaultForm(form);
  };
  const updateForm = (key: string, value: any): void => {
    form.value[key] = value;
  };
  const updateSchema = (newSchema: SchemaT) => {
    schema.value = newSchema;
    updateDefaultSchema(schema);
  };
  const updateDefaultForm = (data: Ref<FormT>) => {
    Object.keys(defaultForm).forEach((key) => {
      defaultForm[key as keyof typeof defaultForm] = cloneDeep(data.value[key]);
    });
  };
  const updateDefaultSchema = (schema: Ref<SchemaT[]>) => {
    defaultSchema.value.forEach((_item: any, index: number) => {
      defaultSchema.value[index] = cloneDeep(schema.value[index]);
    });
  };
    /**
   * TODO
   * 需要把联动也加上 依赖关系
   * submit 时也要统一 需要传递一个handler处理数据就好
   */
  // const submit = () => {
  //   // TODO handler执行
  //   updateDefaultSchema(schema);
  //   updateDefaultForm(form);
  // };
  return {
    form,
    schema,
    defaultForm,
    defaultSchema,
    setForm,
    updateForm,
    updateSchema,
    updateDefaultForm,
    updateDefaultSchema,
  };
};
