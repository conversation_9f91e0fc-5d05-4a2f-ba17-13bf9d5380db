export const useCreativeStringSearch = (game: string, form: any) => {
  // 根据有序分类 pubgm gpp
  const searchMap = {
    common: [
      {
        text: 'Asset Name',
        field: 'asset_name',
        condition: form.asset_name,
      },
      {
        text: 'Serial',
        field: 'asset_serial_id',
        condition: form.asset_serial_id,
      },
      {
        text: 'YouTube Link',
        field: 'youtube_id',
        condition: form.youtube_id,
      },
      {
        text: 'Campaign Name',
        field: 'campaign_name',
        condition: form.campaign_name,
      },
      {
        text: 'Ad Group Name',
        field: 'ad_group_name',
        condition: form.ad_group_name,
      },
      {
        text: 'Ad Name',
        field: 'ad_name',
        condition: form.ad_name,
      },
    ],
    pubgm: [],
    gpp: {
      common: [
        {
          text: 'Play',
          field: 'asset_play',
          condition: form.asset_play,
        },
        {
          text: 'Custom',
          field: 'asset_custom_name',
          condition: form.asset_custom_name,
        },
      ],
    },
  };
  const getList = () => {
    const result = searchMap.common;

    if (game in searchMap) {
      return result.concat(searchMap[game as keyof typeof searchMap] as never[]);
    }
    if (game in searchMap.gpp) {
      return result.concat(searchMap.gpp[game as keyof typeof searchMap.gpp] as never[])
        .concat(searchMap.gpp.common);
    }
    return result.concat(searchMap.gpp.common);
  };
  return getList().map(item => (item.condition ? item : { ...item, condition: [] }));
};
