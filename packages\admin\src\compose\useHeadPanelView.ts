import { computed, ref, Ref, ComputedRef, watch } from 'vue';
import { ViewItem } from '@/views/creative/dashboard/components/TabView';
import { useFetchWrapper } from 'common/compose/request/request';
import { RequestFunction } from 'common/types/requestFunction';
import { useStorage, useUrlSearchParams } from '@vueuse/core';
import { useGlobalGameStore } from '@/store/global/game.store';
import { unRefObj } from 'common/utils/reactive';
import { cloneDeep, uniq } from 'lodash-es';
import { TYPE } from '@/views/creative/dashboard/components/TabView/const';
import { useTips } from 'common/compose/tips';


/**
 * 视图状态 获取视图列表 视图添加 删除 更新 分享操作
 * @param option 参数描述
 * @param option.system 视图系统模块
 * @param option.getViewRequest 获取视图列表
 * @param option.updateViewRequest 更新视图
 * @param option.deleteViewRequest 删除视图
 * @param option.addViewRequest 添加视图
 * @param option.shareViewRequest 分享视图
 * @returns result 返回值表述
 * @returns result.viewList 视图列表
 * @returns result.loading 视图加载状态
 * @returns result.currentView 当前选中视图
 * @returns result.getViewList 触发获取视图列表
 * @returns result.updateViewList 触发更新视图
 * @returns result.deleteView 触发删除视图
 * @returns result.addView 触发添加视图
 * @returns result.shareView 触发分享视图
 * @returns result.init 重新初始化视图
 */
export interface ViewOperationReturn {
  viewList: ComputedRef<ViewItem[]>,
  loading: Ref<boolean>,
  currentView: Ref<ViewItem>,
  currentViewId: Ref<string>,
  getViewList: () => void,
  updateViewList: (params: ViewItem) => Promise<void>,
  deleteView: (params: ViewItem) => Promise<void>,
  addView: (params: ViewItem) => Promise<void>,
  shareView: (params: ViewItem) => Promise<string>,
  init: () => void,
}

export const useHeadPanelView = ({
  system,
  getViewRequest,
  updateViewRequest,
  deleteViewRequest,
  addViewRequest,
  shareViewRequest,
  getShare,
  filterAllGame = false,
}: {
  system: string,
  getViewRequest: RequestFunction<{system: string}, ViewItem[]>,
  updateViewRequest:  (params: ViewItem & {system: string;}) => Promise<boolean>,
  deleteViewRequest: (params: ViewItem) => Promise<boolean>,
  addViewRequest: (params: ViewItem & {system: string;}) => Promise<string>,
  shareViewRequest: (params: ViewItem & { system: string;}) => Promise<string>,
  getShare: (_id: string) => Promise<ViewItem>,
  filterAllGame?: boolean
}): ViewOperationReturn => {
  // 通过code 判断如果是分享视图 就在default视图上展示并且替换链接中参数
  const urlParams = useUrlSearchParams<{ code: string }>('history');
  const shareViewItem = ref<any>(null);
  if (urlParams.code) {
    // 拉取数据
    getShare(urlParams.code).then((viewItem) => {
      shareViewItem.value = viewItem;
    });
    // 替换custom值
  }
  const { success, err } = useTips({
    closeBtn: true,
    offset: [0, 63],
  });
  const SHOWNUM = 3;
  const currentView = ref<any>({});
  const currentViewId = ref<string>('');
  const config = {
    storage: false,
    throttle: 500,
    immediately: false,
    reactive: false,
  };
  const param = computed(() => ({
    system,
    ...currentView.value,
  }));
  const gameStore = useGlobalGameStore();

  // ViewList
  const {
    loading: getViewLoading,
    emit: getViewListHandler, // 获取视图列表
    data: originViewList = [],
  } = useFetchWrapper(
    getViewRequest,
    { system: param.value.system },
    config,
  );
  const viewList = computed(() => {
    let list = cloneDeep((originViewList as Ref<ViewItem[]>).value);
    const item = shareViewItem.value;
    if (list && list.length > 0 && item) {
      // 需要将 default 中的第一个视图进行覆盖
      list = [cloneDeep(item)].concat(list);
      // 切换viewId到分享的视图上
      currentViewId.value = item.value;
      shareViewItem.value = null;
    }
    return (list || []).filter(item => !filterAllGame || (filterAllGame && item.game !== 'allgame'));
  });
  // updateView
  const updateViewList = async (data: ViewItem) => {
    const params = {
      ...newViewList.value.find(item => item.value === data.value),
      ...data,
      system,
    };
    let result = true;
    if (data.type === 'custom') {
      result = await updateViewRequest(params);
    }
    if (result) {
      (originViewList as Ref<ViewItem[]>).value = (originViewList as Ref<ViewItem[]>).value
        .map(item => (item.value === params.value ? ref(params).value : item));
      success('Update View Success');
    } else {
      // 更新失败
      success('Update View Error');
    }
  };
  // deleteView
  const deleteView = async (params: ViewItem) => {
    const result = await deleteViewRequest(params);
    if (result) {
      (originViewList as Ref<ViewItem[]>).value = (originViewList as Ref<ViewItem[]>).value
        .filter(item => (item.value !== params.value));
      success('Delete View Success');
    } else {
      // 删除失败
      err('Delete View Error');
    }
  };
  // addView
  const addView = async (data: ViewItem) => {
    const params = {
      ...newViewList.value.find(item => item.value === data.value),
      ...data,
      type: TYPE.CUSTOM,
      system,
    };
    const code = await addViewRequest(params);
    if (code) {
      (originViewList as Ref<ViewItem[]>).value = (originViewList as Ref<ViewItem[]>).value.concat([{
        ...params,
        value: code,
        id: code,
      }]);
      success('Add View Success');
    } else {
      err('Add View Error');
    }
  };
  // shareView
  const shareView = async () => await shareViewRequest({
    ...currentView.value,
    system,
  });
  const getViewList = () => {
    getViewListHandler({
      system,
    });
  };
  const emptyCurrent = computed(() => !unRefObj(currentView.value)
  || JSON.stringify(unRefObj(currentView.value)) === JSON.stringify({}));

  const viewStorage = useStorage<string[]>(`historyView:${system}:${gameStore.gameCode}`, []);
  if (!emptyCurrent.value && viewStorage.value.length !== 0) {
    // 从缓存中的第一个给currentView
    const [id] = viewStorage.value;
    currentView.value = (viewList as Ref<ViewItem[]>).value.find(item => item.value === id);
    currentViewId.value = id;
  }

  /**
   * 主要处理viewList
   * 将viewList中和localStorage中的做匹配并放到最前面如果没有就拿最前面三个放到localStorage
   * 放入localStorage非type 为 default 的前三个
   * 删除时删除当前视图逻辑处理 将currentViewId变成下一个并删除localStorage中数据并进行补位
   */
  watch(
    () => [(viewList as Ref<ViewItem[]>).value, currentView.value],
    ([list, current]) => {
      if (current) {
        // viewList 需要做处理把缓存中的视图拿出来放到最前面
        let filterFixDefault = [];
        if (list.length > 0) {
          // default视图的前三个
          filterFixDefault = list.filter((item: ViewItem) => item.type === 'default').slice(0, 3);
        }
        if (viewStorage.value.length === 0 && list.length > 0) {
          // 初始化时需要将viewStorage更改
          // 排除type 为 default的前三个视图
          // 3 代表最大default展示数量
          const moreList = list.filter((item: ViewItem) => item.type === 'custom')
            .concat(filterFixDefault);
          viewStorage.value = uniq(moreList.slice(0, SHOWNUM).map((item: ViewItem) => item.value));
        }
        // custom视图(不在viewStorage里的) + default视图
        const subList = list.filter((item: ViewItem) => !viewStorage.value.includes(item.value) || item.type !== 'default')
          .concat(filterFixDefault);
        // 如果viewStorage长度不为3就补充到散
        if (viewStorage.value.length < SHOWNUM && list.length >= SHOWNUM) {
          viewStorage.value = uniq(viewStorage.value
            .concat(subList.slice(0, SHOWNUM - viewStorage.value.length)));
        }
        // viewStorage中的数据不在viewList中的处理
        const storageNoInList = viewStorage.value.filter(key => !list?.find((item: ViewItem) => item.value === key));
        if (storageNoInList.length > 0 && list.length > 0) {
          // 删除viewStorage中不在viewList的数据
          viewStorage.value = uniq(viewStorage.value.filter(key => list?.find((item: ViewItem) => item.value === key)));
          // 用 custom视图(不在viewStorage里的) + default视图 补齐viewStorage
          viewStorage.value = uniq(viewStorage.value
            .concat(subList.slice(0, storageNoInList.length).map((t: ViewItem) => t.value)));
        }
        // 根据当前s视图更新localStorage 选中default视图不更新localStorage
        if (!emptyCurrent.value && viewStorage.value[0] !== current.value && current?.type !== 'default') {
          const index = viewStorage.value.findIndex(value => value === current.value);
          if (index !== -1) {
            viewStorage.value = viewStorage.value.filter(value => current.value !== value);
          } else {
            viewStorage.value.pop();
          }
          viewStorage.value.unshift(current.value);
        }
        // 删除情况
        if (!emptyCurrent.value && !list.find((item: ViewItem) => item.value === current?.value)) {
          // const index = viewStorage.value.findIndex(value => current.value === value);
          // 缓存中过滤掉已经删除的视图
          // 把上一次视图添加进来
          viewStorage.value = uniq(viewStorage.value.filter((value: string) => value !== current.value));
          viewStorage.value.push(newViewList.value.filter(item => !viewStorage.value.includes(item.value))[0].value);
        }
      }
    }, {
      deep: true,
    },
  );
  // viewStorage 为空的话就重新赋值
  const newViewList = computed(() => {
    if ((viewList as Ref<ViewItem[]>).value) {
      const hiddenViewList = (viewList as Ref<ViewItem[]>).value
        .filter(item => !viewStorage.value.includes(item.value));
      const storeList = viewStorage.value
        .map(value => (viewList as Ref<ViewItem[]>).value.find(item => item.value === value) as ViewItem)
        .filter(item => item);
      // 处理分享视图得逻辑
      return storeList
        .concat(hiddenViewList)
        .map(item => ({ ...item, game: item.game || (gameStore.gameCode as string), operate: item.type === 'custom' }));
    }
    return [];
  });


  watch(
    () => currentViewId.value,
    (value) => {
      if (value) {
        currentView.value = (viewList as Ref<ViewItem[]>).value.find(item => item.value === value);
      }
    },
  );

  const init = () => {
    currentView.value = {};
    currentViewId.value = '';
    (originViewList as Ref<ViewItem[]>).value = [];
  };

  return {
    viewList: newViewList,
    loading: (getViewLoading as Ref<boolean>),
    currentView,
    currentViewId,
    getViewList,
    updateViewList,
    deleteView,
    addView,
    shareView,
    init,
  };
};
