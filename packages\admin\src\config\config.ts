export const STORE_KEY = {
  EVENT: 'event',
  EVENT_STAGE: 'event_stage',
  COMMON_TEMPLATE: 'common_template',
  GLOBAL: {
    AUTH: 'global.auth',
    GAME: 'global.game',
    ROUTER: 'global.router',
    MEDIA_ACCOUNT: 'global.media_account',
    ROUTES_TIPS: 'global.routes_tips',
    REMOTE_CONFIG: 'global.remote_config',
    WATER_MARK_CONTENT: 'global.water_mark_content',
  },
  EVENT_SPOT: 'event_spot',
  SCHEDULE_SPOT_DETAIL: 'schedule_spot_detail',
  APPROVAL_STORE: 'approval_store',
  CREATIVE: {
    DASHBOARD_AF: 'creative_dashboard_af',
    PORTAL: 'creative_portal',
    DASHBOARD: 'creative_dashboard',
    AIX_LIBRARY: 'creative_aix_library',
    MEDIA_LIBRARY: 'creative_media_library',
    TD_MEDIA_LIBRARY: 'creative_td_media_library',
    TASK: 'creative_aix_task',
    NAME_GENERATOR: {
      INDEX: 'creative_name_generator',
      REALTIME_UPLOAD: 'creative_name_generator_realtime_upload',
      DROPBOX: 'creative_name_generator_dropbox',
      CACHE: 'creative_name_generator_cache',
    },
    NAME_GENERATOR_TASK: 'creative_name_generator_task',
    INSIGHT: {
      REPORT: 'creative_insight_report',
      TOP_REPORT: 'creative_insight_top_report',
    },
    LABELS: {
      INSIGHT: 'creative_labels_insight',
      INSIGHT_CHART: 'creative_labels_insight_chart',
      ASSETS: 'creative_labels_assets',
      DETAILS: 'creative_labels_details',
      RETENTION: 'creative_labels_retention',
      MANAGE: 'creative_labels_manage',
      SYSTEM: 'creative_labels_system',
    },
    TOOLKIT: {
      AI_CLIP: 'creative_toolkit_ai_clips',
      AI_TEMPLATE: 'creative_toolkit_ai_template',
      AI_CLIP_FAVOR: 'creative_toolkit_ai_clips_favor',
      AI_VIDEO: 'creative_toolkit_ai_video',
      AI_FACE_SWAP: 'creative_toolkit_ai_face_swap',
      AI_MOUTH_SWAP: 'creative_toolkit_ai_mouth_swap',
      AI_SMART_VIDEO_TEMPLATE: 'creative_toolkit_ai_smart_video_template',
      AI_SMART_VIDEO_LIST: 'creative_toolkit_ai_smart_video_list',
      AI_SMART_VIDEO_EDITOR: 'creative_toolkit_ai_smart_video_editor',
      AI_VIDEO_DUB_EDITOR: 'creative_toolkit_ai_video_dub',
    },
    TOP: {
      CREATIVES: 'creative_top_creatives',
      CREATIVES_LABELS: 'creative_top_creatives_labels',
      LABELS: 'creative_top_labels',
      CREATIVES_STANDARD_RULE: 'creative_top_creatives_standard_rule',
    },
  },
  TD: {
    PIVOT: 'td_pivot',
    CUSTOM_VIEW: 'td_custom_view',
    TEMPLATE: {
      NAME: 'td_template',
      GOOGLE: 'td_template_google',
      TIKTOK: 'td_template_tiktok',
      FACEBOOK: 'td_template_facebook',
      TWITTER: 'td_template_twitter',
    },
    MONITOR: {
      INDEX: 'td_monitor',
      NOTIFICATION: 'td_monitor_notification',
      TARGET: 'td_monitor_rules_target',
      RULES: 'td_monitor_rules',
      SETTING: 'td_monitor_setting',
    },
    AI_TOOLKIT: {
      SMART_COPYWRITER: {
        INDEX: 'td_aitoolkit_smart_copywriter',
        CREATE_TEXT: 'td_aitoolkit_smart_copywriter_create_text',
        SAVED_TEXT: 'td_aitoolkit_smart_copywriter_saved_text',
        EXPLORATION: 'td_aitoolkit_smart_copywriter_exploration',
      },
    },
  },
  AUDIENCE: {
    OVERVIEW: 'audience_overview',
    OVERVIEW_LOG: 'audience_overview_log',
    OVERVIEW_FORM: 'audience_overview_form',
    OVERVIEW_FORM_UPDATE: 'audience_overview_form_update',
    OVERVIEW_FORM_VISIBLE: 'audience_overview_form_visible',
    OVERVIEW_FORM_QUERY_STRING_PARAMS: 'audience_overview_form_query_string_params',
  },
  CONFIGURATION: {
    PROFILE: 'configuration_profile',
    BUSINESS: 'configuration_business',
    ADACCOUNT: 'configuration_adaccount',
    CHANNELS: 'configuration_channels',
    ROLE: 'configuration_role',
    MANAGEMENT: {
      METRIC: 'bi_management_metric',
      CAMPAIGN_NAMING: 'configuration_campaign_naming',
    },
  },
  INTELLIGENCE: {
    CREATIVE: {
      ADD_COMPETITOR: 'intelligence_creative_add_competitor',
      COMPETITOR: 'intelligence_creative_competitor',
      OVERVIEW: 'intelligence_creative_overview',
      SOCIAL_MEDIA_VIDEO: 'intelligence_creative_social_media_video',
    },
    USER_UPLOAD: 'intelligence_user_upload',
    COMMON: 'intelligence_common',
    MARKET: {
      SCALE: 'intelligence_market_scale',
      TREND: 'intelligence_market_trend',
      COMPETITIVE: 'intelligence_market_competitive',
      CHANNEL: 'intelligence_market_channel',
      OS: 'intelligence_market_os',
    },
    PREDICTION: 'intelligence_prediction',
    USER: 'intelligence_user',
  },
  INFLUENCER: {
    OVERVIEW: 'influencer_overview',
    CAMPAIGN: 'influencer_campaign',
    CAMPAIGN_SETUP: 'influencer_campaign_setup',
  },
};

export const ISDEV = import.meta.env.MODE === 'development';
export const ISPRO = import.meta.env.MODE === 'production';


export const INDEXED_DB_CONFIG = {
  AIX_CREATIVE: {
    DB_NAME: 'aix_creative',
    TABLE: {
      CREATIVE_NAME_GENERATOR: 'creative_name_generator',
    },
    VERSION: 1, // 默认为1， 后面如果要建表， 或者建索引， 记得更新版本号
  },
};
