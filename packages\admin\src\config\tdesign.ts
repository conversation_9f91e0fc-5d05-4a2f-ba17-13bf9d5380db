import { merge } from 'lodash-es';
import enConfig from 'tdesign-vue-next/es/locale/en_US';

export function useTDesignConfig() {
  // TODO 根据i18n切换来进行语言切换
  return merge(enConfig, {
    // 可以在此处定义更多自定义配置，具体可配置内容参看 API 文档
    dialog: {
      confirm: 'Confirm',
      // cancel: 'cancel',
      cancel: {
        theme: 'default',
        variant: 'outline',
        content: 'Cancel',
      },
      confirmBtnTheme: {
        default: 'primary',
        info: 'primary',
        warning: 'warning',
        danger: 'danger',
        success: 'success',
      },
    },
    input: {
      placeholder: 'Please enter',
    },
    dataPicker: {
      placeholder: {
        date: 'Select date',
        month: 'Select month',
        year: 'Select year',
      },
      weekAbbreviation: '',
      weekdays: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      rangeSeparator: ' ~ ',
      format: 'YYYYMMDD',
      yearAriaLabel: '',
      now: 'Now',
      selectTime: 'Select Time',
      selectDate: 'Select Date',
    },
    pagination: {
      itemsPerPage: '{size} / page',
      jumpTo: 'Jump to',
      page: '',
      total: 'Total {total} items',
    },
  });
}
