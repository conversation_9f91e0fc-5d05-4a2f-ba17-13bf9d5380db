{"store": {"view_key": "bi_overview_pc", "api": {"get_view": {"id": "${store.view_key} / get_view", "type": "get", "url": "/api/bi/common/get_view", "emitOptions": {"isUseStorage": true}, "payload": {"system": "${store.view_key}"}}, "get_config": {"type": "get", "url": "/api/bi/common/get_config", "payload": {"system": "${store.view_key}"}}, "get_region": {"type": "post", "url": "/api/bi/common/get_region", "payload": {}}, "get_network": {"type": "post", "url": "/api/bi/pivot_pc/get_network", "payload": {}}, "get_campaign": {"type": "post", "url": "/api/bi/pivot_pc/get_campaign", "payload": {"network_type": [], "show_network": [], "campaign_type": [], "pageIndex": 0, "pageSize": 1000, "not_in_campaign": [], "search": ""}}, "get_total_with_organic": {"type": "post", "url": "/api/bi/pivot_pc/get_total_with_organic"}, "get_total_no_organic": {"type": "post", "url": "/api/bi/pivot_pc/get_total_no_organic"}, "get_top": {"type": "post", "url": "/api/bi/pivot_pc/get_top"}, "get_top_with_date": {"type": "post", "url": "/api/bi/pivot_pc/get_top_with_date"}, "get_pivot": {"type": "post", "url": "/api/bi/pivot_pc/get_pivot"}}}, "event": {"init": {"$": "api", "value": "${store.api.get_view}"}}, "form": {"list": [{"name": "date-time-picker", "props": {"valueType": "YYYYMMDD", "presetsKey": "maxDate", "maxDate": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_view}"}, {"$": "extend", "value": {"dependId": "${store.view_key} / get_view", "pick": "0.param.form.max_date"}}, {"$": "api"}]}}, "ext": {"key": "date"}}, {"name": "new-cascader", "props": {"title": "MediaSrc", "levelList": [{"label": "NetworkType", "value": "network_type"}, {"label": "MediaSrc", "value": "network"}], "isEmptyWhenSelectAll": true, "mode": "level", "isUseDefaultButton": true, "isDirectUpdateValue": true, "isOnlyNeedSon": true, "options": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_network}"}, {"$": "extend", "value": {}}, {"$": "api"}]}}, "ext": {"isAllowClose": true, "label": "MediaSrc", "key": "network"}}, {"name": "new-cascader", "props": {"title": "Country/Market", "levelList": [{"label": "Region", "value": "region"}, {"label": "Country/Market", "value": "country"}], "isEmptyWhenSelectAll": true, "mode": "level", "isUseDefaultButton": true, "isDirectUpdateValue": true, "isOnlyNeedSon": true, "options": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_region}"}, {"$": "extend", "value": {}}, {"$": "api"}]}}, "ext": {"isAllowClose": true, "label": "Country/Market", "key": "country"}}, {"name": "a-select", "props": {"title": "Platform", "multiple": true, "isEmptyWhenSelectAll": true, "list": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "platform_list"}}, {"$": "api"}]}}, "ext": {"isAllowClose": true, "label": "Platform", "key": "platform"}}, {"name": "new-cascader", "props": {"title": "Campaign", "levelList": [{"label": "CampaignType", "value": "campaign_type"}, {"label": "Campaign", "value": "campaign"}], "isEmptyWhenSelectAll": true, "mode": "level", "isUseDefaultButton": true, "isDirectUpdateValue": true, "isOnlyNeedSon": true, "options": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_campaign}"}, {"$": "extend", "value": {}}, {"$": "api"}]}}, "ext": {"isAllowClose": true, "label": "Campaign", "key": "campaign", "dependKeyList": ["network"], "emitPropsNameList": ["options"]}}]}, "mod": {"list": [{"name": "metric-card-swiper", "props": {"allowActive": false, "metricCardStyle": {"height": "100%"}, "loadingWrapperStyle": {"height": "136px", "width": "100%"}, "cfgList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "card_list"}}, {"$": "api"}]}, "descRulesList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "desc_rules_list"}}, {"$": "api"}]}, "data": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_total_with_organic}"}, {"$": "extend", "value": {"pick": "list"}}, {"$": "api"}]}}, "ext": {"key": "card", "emitPropsNameList": ["data"]}}, {"name": "business-chart", "props": {"isAddBg": true, "detailType": "stack", "chartType": "bar", "attrType": 2, "groupbyKey": "dev_start_date", "basicChartProps": {"isShowLegend": true, "regRules": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "regRules"}}, {"$": "api"}]}}, "isTransformDataToDown": true, "attrList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "attr_list"}}, {"$": "api"}]}, "metricList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "metric_list"}}, {"$": "api"}]}, "data": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_top_with_date}"}, {"$": "extend", "value": {"pick": "list"}}, {"$": "api"}]}}, "ext": {"key": "line", "emitPropsNameList": ["data"]}}, {"name": "business-table", "props": {"tableProps": {"tableLayout": "auto", "multipleSort": false}, "isEmptyWhenOrderByAll": true, "tableColumnsRule": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "table_columns_rule"}}, {"$": "api"}]}, "attrList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "attr_list2"}}, {"$": "api"}]}, "descRulesList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "desc_rules_list"}}, {"$": "api"}]}, "metricsType": "single", "metricList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "metric_list"}}, {"$": "api"}]}, "tableData": {"$": "api", "value": "${store.api.get_pivot}"}}, "ext": {"key": "table", "emitPropsNameList": ["tableData"]}}, {"name": "div", "props": {"style": {"display": "flex", "column-gap": "16px", "background-color": "transparent"}}, "children": [{"name": "metric-card-swiper", "props": {"loadingWrapperStyle": {"width": "250px"}, "style": {"max-width": "250px"}, "wrapperStyle": {"height": "100%"}, "mode": "vertical", "metricCardHeight": "23%", "allowActive": false, "cfgList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "card_list2"}}, {"$": "api"}]}, "descRulesList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "desc_rules_list"}}, {"$": "api"}]}, "data": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_total_no_organic}"}, {"$": "extend", "value": {"pick": "list"}}, {"$": "api"}]}}, "ext": {"key": "card2", "emitPropsNameList": ["data"]}}, {"name": "business-chart", "props": {"style": {"flex": 1}, "isAddBg": true, "detailType": "stack", "chartType": "line", "attrType": 2, "groupbyKey": "dev_start_date", "basicChartProps": {"isShowLegend": true, "regRules": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "regRules"}}, {"$": "api"}]}}, "isTransformDataToDown": true, "attrList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "attr_list2"}}, {"$": "api"}]}, "metricList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "metric_list"}}, {"$": "api"}]}, "data": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_top_with_date}"}, {"$": "extend", "value": {"pick": "list"}}, {"$": "api"}]}}, "ext": {"key": "line2", "emitPropsNameList": ["data"]}}]}, {"name": "business-table", "props": {"metricsType": "single", "tableProps": {"tableLayout": "auto", "multipleSort": false}, "isEmptyWhenOrderByAll": true, "tableColumnsRule": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "table_columns_rule"}}, {"$": "api"}]}, "attrList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "attr_list"}}, {"$": "api"}]}, "descRulesList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "desc_rules_list"}}, {"$": "api"}]}, "metricList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "metric_list"}}, {"$": "api"}]}, "tableData": {"$": "api", "value": "${store.api.get_pivot}"}}, "ext": {"key": "table2", "emitPropsNameList": ["tableData"]}}]}}