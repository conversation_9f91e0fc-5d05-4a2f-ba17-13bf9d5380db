{"store": {"view_key": "bi_real_time", "api": {"get_view": {"type": "get", "url": "/api/bi/common/get_view", "emitOptions": {"isUseStorage": true}, "payload": {"system": "${store.view_key}"}}, "get_config": {"type": "get", "url": "/api/bi/common/get_config", "payload": {"system": "${store.view_key}"}}, "get_region": {"type": "post", "url": "/api/bi/common/get_region", "payload": {}}, "get_network": {"type": "post", "url": "/api/bi/common/get_network", "payload": {}}, "get_campaign": {"type": "post", "url": "/api/bi/common/get_campaign", "payload": {"network_type": [], "show_network": [], "campaign_type": [], "pageIndex": 0, "pageSize": 1000, "not_in_campaign": [], "search": ""}}, "get_latest_metrics": {"type": "post", "url": "/api/bi/hourly/get_latest_metrics", "id": "${store.view_key} / get_latest_metrics"}, "get_metrics_by_date": {"type": "post", "url": "/api/bi/hourly/get_metrics_by_date"}, "get_metrics_by_time": {"type": "post", "url": "/api/bi/hourly/get_metrics_by_time"}}}, "event": {"init": {"$": "api", "value": "${store.api.get_view}"}}, "form": {"list": [{"name": "date-time-picker", "props": {"valueType": "YYYYMMDD", "maxDate": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_view}"}, {"$": "extend", "value": {"pick": "0.param.form.max_date"}}, {"$": "api"}]}}, "ext": {"key": "date"}}, {"name": "a-select", "props": {"multiple": false, "title": "Tracking Basis", "isEmptyWhenSelectAll": false, "list": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "usertype_list"}}, {"$": "api"}]}}, "ext": {"isAllowClose": false, "label": "Interval", "key": "user_type"}}, {"name": "a-select", "props": {"multiple": false, "title": "Interval", "isEmptyWhenSelectAll": false, "list": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "interval_list"}}, {"$": "api"}]}}, "ext": {"isAllowClose": false, "label": "Interval", "key": "interval"}}, {"name": "new-cascader", "props": {"title": "MediaSrc", "levelList": [{"label": "NetworkType", "value": "network_type"}, {"label": "MediaSrc", "value": "network"}], "isEmptyWhenSelectAll": true, "mode": "level", "isUseDefaultButton": true, "isDirectUpdateValue": true, "isOnlyNeedSon": true, "options": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_network}"}, {"$": "extend", "value": {}}, {"$": "api"}]}}, "ext": {"isAllowClose": true, "label": "MediaSrc", "key": "network"}}, {"name": "new-cascader", "props": {"title": "Country/Market", "levelList": [{"label": "Region", "value": "region"}, {"label": "Country/Market", "value": "country"}], "isEmptyWhenSelectAll": true, "mode": "level", "isUseDefaultButton": true, "isDirectUpdateValue": true, "isOnlyNeedSon": true, "options": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_region}"}, {"$": "extend", "value": {}}, {"$": "api"}]}}, "ext": {"isAllowClose": true, "label": "Country/Market", "key": "country"}}, {"name": "new-cascader", "props": {"title": "Campaign", "levelList": [{"label": "CampaignType", "value": "campaign_type"}, {"label": "Campaign", "value": "campaign"}], "isEmptyWhenSelectAll": true, "mode": "level", "isUseDefaultButton": true, "isDirectUpdateValue": true, "isOnlyNeedSon": true, "options": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_campaign}"}, {"$": "extend", "value": {}}, {"$": "api"}]}}, "ext": {"isAllowClose": true, "label": "Campaign", "key": "campaign", "dependKeyList": ["network"], "emitPropsNameList": ["options"]}}, {"name": "a-select", "props": {"title": "OS", "multiple": true, "isEmptyWhenSelectAll": true, "list": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "platform_list"}}, {"$": "api"}]}}, "ext": {"isAllowClose": true, "label": "OS", "key": "platform"}}, {"name": "a-select", "props": {"title": "OpenID", "multiple": false, "isEmptyWhenSelectAll": false, "list": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "openid_list"}}, {"$": "api"}]}}, "ext": {"isAllowClose": true, "label": "OpenID", "key": "openid"}}]}, "mod": {"list": [{"name": "metric-card-swiper", "props": {"allowActive": false, "metricCardStyle": {"height": "100%"}, "loadingWrapperStyle": {"height": "136px", "width": "100%"}, "cfgList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "card_list"}}, {"$": "api"}]}, "descRulesList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "desc_rules_list"}}, {"$": "api"}]}, "data": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_latest_metrics}"}, {"$": "extend", "value": {"pick": "list"}}, {"$": "api"}]}}, "ext": {"key": "card", "emitPropsNameList": ["data"]}}, {"name": "business-chart", "props": {"isAddBg": true, "detailType": "stack", "chartType": "bar", "attrType": 2, "groupbyKey": "time", "isXAxisSort": true, "isMakeUpTime": true, "basicChartProps": {"axisFormatType": "HH:mm", "axisValueType": "YYYYMMDDHHmm", "isShowLegend": true, "regRules": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "regRules"}}, {"$": "api"}]}}, "isTransformDataToDown": true, "attrList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "attr_list"}}, {"$": "api"}]}, "metricList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "metric_list"}}, {"$": "api"}]}, "data": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_metrics_by_date}"}, {"$": "extend", "value": {"pick": "list"}}, {"$": "api"}]}}, "ext": {"key": "line", "emitPropsNameList": ["data"]}}, {"name": "business-chart", "props": {"isAddBg": true, "detailType": "stack", "chartType": "bar", "attrType": 1, "groupbyKey": "time", "isXAxisSort": true, "isMakeUpTime": true, "basicChartProps": {"axisFormatType": "HH:mm", "axisValueType": "YYYYMMDDHHmm", "isShowLegend": true, "regRules": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "regRules"}}, {"$": "api"}]}}, "isTransformDataToDown": true, "attrList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "attr_list"}}, {"$": "api"}]}, "metricList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "metric_list"}}, {"$": "api"}]}, "data": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_metrics_by_date}"}, {"$": "extend", "value": {"pick": "list"}}, {"$": "api"}]}}, "ext": {"key": "line2", "emitPropsNameList": ["data"]}}, {"name": "business-table", "props": {"metricsType": "single", "styleOverview": {"minHeight": "586px"}, "tableProps": {"tableLayout": "auto"}, "tableColumnsRule": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "table_columns_rule"}}, {"$": "api"}]}, "extFormList": [{"name": "date-time-picker", "props": {"dateType": "time", "style": {"width": "100px"}, "valueType": "YYYYMMDDHHmm", "formatType": "HH:mm"}, "ext": {"key": "time"}}], "attrList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "attr_list"}}, {"$": "api"}]}, "descRulesList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "desc_rules_list"}}, {"$": "api"}]}, "metricList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "metric_list_table"}}, {"$": "api"}]}, "isShowMetrics": false, "tableData": {"$": "api", "value": "${store.api.get_metrics_by_time}"}}, "ext": {"key": "table", "emitPropsNameList": ["tableData"]}}]}}