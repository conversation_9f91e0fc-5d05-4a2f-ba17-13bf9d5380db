{"store": {"view_key": "bi_ua", "api": {"get_view": {"id": "${store.view_key} / get_view", "type": "get", "url": "/api/bi/common/get_view", "emitOptions": {"isUseStorage": true}, "payload": {"system": "${store.view_key}"}}, "get_config": {"type": "get", "url": "/api/bi/common/get_config", "payload": {"system": "${store.view_key}"}}, "get_region": {"type": "post", "url": "/api/bi/common/get_region", "payload": {}}, "get_network": {"type": "post", "url": "/api/bi/pivot_pc/get_network", "payload": {}}, "get_campaign": {"type": "post", "url": "/api/bi/pivot_pc/get_campaign", "payload": {"network_type": [], "show_network": [], "campaign_type": [], "pageIndex": 0, "pageSize": 1000, "not_in_campaign": [], "search": ""}}, "get_total_with_organic": {"type": "post", "url": "/api/bi/pivot_pc/get_total_with_organic"}, "get_total_no_organic": {"type": "post", "url": "/api/bi/pivot_pc/get_total_no_organic"}, "get_top_with_date": {"type": "post", "url": "/api/bi/pivot_pc/get_top_with_date"}, "get_top": {"type": "post", "url": "/api/bi/pivot_pc/get_top"}, "get_pivot": {"type": "post", "url": "/api/bi/pivot_pc/get_pivot"}, "get_funnel": {"type": "post", "url": "/api/bi/pivot_pc/get_funnel"}}}, "event": {"init": {"$": "api", "value": "${store.api.get_view}"}}, "form": {"list": [{"name": "date-time-picker", "props": {"valueType": "YYYYMMDD", "presetsKey": "maxDate", "maxDate": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_view}"}, {"$": "extend", "value": {"dependId": "${store.view_key} / get_view", "pick": "0.param.form.max_date"}}, {"$": "api"}]}}, "ext": {"key": "date"}}, {"name": "new-cascader", "props": {"title": "MediaSrc", "levelList": [{"label": "NetworkType", "value": "network_type"}, {"label": "MediaSrc", "value": "network"}], "isEmptyWhenSelectAll": true, "mode": "level", "isUseDefaultButton": true, "isDirectUpdateValue": true, "isOnlyNeedSon": true, "options": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_network}"}, {"$": "extend", "value": {}}, {"$": "api"}]}}, "ext": {"isAllowClose": true, "label": "MediaSrc", "key": "network"}}, {"name": "new-cascader", "props": {"title": "Country/Market", "levelList": [{"label": "Region", "value": "region"}, {"label": "Country/Market", "value": "country"}], "isEmptyWhenSelectAll": true, "mode": "level", "isUseDefaultButton": true, "isDirectUpdateValue": true, "isOnlyNeedSon": true, "options": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_region}"}, {"$": "extend", "value": {}}, {"$": "api"}]}}, "ext": {"isAllowClose": true, "label": "Country/Market", "key": "country"}}, {"name": "a-select", "props": {"title": "Platform", "multiple": true, "isEmptyWhenSelectAll": true, "list": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "platform_list"}}, {"$": "api"}]}}, "ext": {"isAllowClose": true, "label": "Platform", "key": "platform"}}, {"name": "new-cascader", "props": {"title": "Campaign", "levelList": [{"label": "CampaignType", "value": "campaign_type"}, {"label": "Campaign", "value": "campaign"}], "isEmptyWhenSelectAll": true, "mode": "level", "isUseDefaultButton": true, "isDirectUpdateValue": true, "isOnlyNeedSon": true, "options": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_campaign}"}, {"$": "extend", "value": {}}, {"$": "api"}]}}, "ext": {"isAllowClose": true, "label": "Campaign", "key": "campaign", "dependKeyList": ["network"], "emitPropsNameList": ["options"]}}]}, "mod": {"list": [{"name": "metric-card-swiper", "props": {"wrapperStyle": {"width": "100%", "row-gap": "10px", "column-gap": "10px"}, "loadingWrapperStyle": {"height": "210px", "width": "100%"}, "mode": "horizontal", "metricCardHeight": "", "metricCardStyle": {"flex-basis": "calc(20% - 10px)"}, "allowActive": false, "cfgList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "card"}}, {"$": "api"}]}, "descRulesList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "desc_rules_list"}}, {"$": "api"}]}, "data": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_total_with_organic}"}, {"$": "extend", "value": {"pick": "list"}}, {"$": "api"}]}}, "ext": {"key": "card", "emitPropsNameList": ["data"]}}, {"name": "business-chart", "props": {"isAddBg": true, "metricMax": 9, "isEmptyWhenSelectAll": true, "detailType": "stack", "chartType": "funnel", "chartTypeList": [], "metricKey": "v", "groupbyKey": "key", "metricFilterKey": "key", "metricSort": "list", "basicChartProps": {"xAxisLabelFormatBreakByNum": 14, "seriesItemStyle": ["#5086F3", "#69D19C", "#6E7794", "#F8D74D", "#7F6FF1", "#5EC8F1", "#AD53D4", "#F19E55"], "regRules": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "regRules"}}, {"$": "api"}]}}, "isTransformDataToDown": true, "metricList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "metric_list1"}}, {"$": "api"}]}, "data": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_funnel}"}, {"$": "extend", "value": {"pick": "list"}}, {"$": "api"}]}}, "ext": {"key": "line", "emitPropsNameList": ["data"]}}, {"name": "business-chart", "props": {"isAddBg": true, "detailType": "stack", "chartType": "line", "attrType": 2, "groupbyKey": "dev_start_date", "isTransformDataToDown": true, "basicChartProps": {"isShowLegend": true, "regRules": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "regRules"}}, {"$": "api"}]}}, "attrList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "attr_list"}}, {"$": "api"}]}, "metricList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "metric_list2"}}, {"$": "api"}]}, "data": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_top_with_date}"}, {"$": "extend", "value": {"pick": "list"}}, {"$": "api"}]}}, "ext": {"key": "line2", "emitPropsNameList": ["data"]}}, {"name": "div", "props": {"style": {"display": "flex", "flex": "1", "column-gap": "16px", "background-color": "transparent"}}, "children": [{"name": "business-chart", "props": {"isAddBg": true, "detailType": "stack", "chartType": "bar", "attrType": 1, "style": {"width": "50%"}, "basicChartProps": {"dataMode": "y", "yAxisName": " ", "grid": {"right": 40, "bottom": 40}, "regRules": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "regRules"}}, {"$": "api"}]}}, "chartTypeList": [{"iconName": "statistics", "type": "bar"}], "isTransformDataToDown": true, "attrList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "attr_list2"}}, {"$": "api"}]}, "metricList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "metric_list2"}}, {"$": "api"}]}, "data": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_top}"}, {"$": "extend", "value": {"pick": "list"}}, {"$": "api"}]}}, "ext": {"key": "line3", "emitPropsNameList": ["data"]}}, {"name": "business-chart", "props": {"isAddBg": true, "detailType": "stack", "chartType": "bar", "attrType": 1, "style": {"width": "50%"}, "basicChartProps": {"dataMode": "y", "yAxisName": " ", "grid": {"right": 40, "bottom": 40}, "regRules": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "regRules"}}, {"$": "api"}]}}, "chartTypeList": [{"iconName": "statistics", "type": "bar"}], "isTransformDataToDown": true, "attrList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "attr_list2"}}, {"$": "api"}]}, "metricList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "metric_list2"}}, {"$": "api"}]}, "data": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_top}"}, {"$": "extend", "value": {"pick": "list"}}, {"$": "api"}]}}, "ext": {"key": "line4", "emitPropsNameList": ["data"]}}]}, {"name": "business-table", "props": {"styleOverview": {"minHeight": "526px"}, "metricsType": "single", "tableProps": {"tableLayout": "auto", "multipleSort": false}, "isEmptyWhenOrderByAll": true, "tableColumnsRule": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "table_columns_rule"}}, {"$": "api"}]}, "attrList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "attr_list"}}, {"$": "api"}]}, "descRulesList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "desc_rules_list"}}, {"$": "api"}]}, "metricList": {"$": "queue", "value": [{"$": "value", "value": "${store.api.get_config}"}, {"$": "extend", "value": {"pick": "metric_list2"}}, {"$": "api"}]}, "multipleSort": true, "tableData": {"$": "api", "value": "${store.api.get_pivot}"}}, "ext": {"key": "table", "emitPropsNameList": ["tableData"]}}]}}