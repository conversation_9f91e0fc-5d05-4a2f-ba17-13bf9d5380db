import { FrameWork, reportBase, ReportType } from 'common/utils/actionReport';
import { isPlainObject } from 'lodash-es';

type TExtArgs = {
  ext5?: string,
  ext6?: string,
  ext7?: string,
  ext8?: string,
  ext9?: string,
  ext10?: string,
  ext11?: string,
  ext12?: string,
  ext13?: string,
  ext14?: string,
  ext15?: string,
  ext16?: string,
};

// 获取上报时ext2- ext4
const getReportDefaultFields = (optid: string) => ({
  ext2: optid?.substring(0, 2),
  // optid的第1-2位，表示一级路由id，例如01代表bi，02代表td
  ext3: optid?.substring(2, 4),
  // optid的第3-4位，表示次级路由id，例如01代表Realtime
  ext4: optid?.substring(4, 8),
  // optid的第5-8位，表示某路由内模块位置id，例如0101表示不区分模块的路由级上报，常用语统计PV
});

// 【1】 通用的上报点击方法 （PV上报请用reportCommonExpose）
export const reportCommonClick = (optid: string, routeName?: string, extArgs?: TExtArgs) => reportBase({
  ...(isPlainObject(extArgs) ? extArgs : {}),
  ...getReportDefaultFields(optid),
  modid: optid.slice(0, 6),
  optid,
  ext1: routeName,
}, ReportType.click, optid.slice(0, 4));

// 目前没用业务会用这个方法  到先注释
// export const reportCommonPv = (optid: string, routeName?: string, extArgs?: TExtArgs) => reportBase({
//   ...(isPlainObject(extArgs) ? extArgs : {}),
//   ...getReportDefaultFields(optid),
//   modid: optid.slice(0, 6),
//   optid,
//   ext1: routeName,
// }, ReportType.pv, optid.slice(0, 4));

// 【0】通用的上报曝光方法，适合于任意曝光上报场景（如页面PV，某个页面的模块曝光）
export const reportCommonExpose = (optid: string, routeName?: string, extArgs?: TExtArgs) => reportBase({
  ...(isPlainObject(extArgs) ? extArgs : {}),
  ...getReportDefaultFields(optid),
  modid: optid.slice(0, 6),
  optid,
  ext1: routeName,
}, ReportType.expose, optid.slice(0, 4));


const Mod = {
  init: `${FrameWork.init}01`,
  router: `${FrameWork.init}02`,
  auth: `${FrameWork.init}03`,
};
export const reportFrame = (modid: string) => (
  optid: string,
  type: ReportType,
  ext?: Record<string, any>,
) => reportBase({
  modid,
  optid,
  ...ext,
}, type, FrameWork.init);

const reportFrameWorkInit = reportFrame(Mod.init);
const reportFrameWorkRouter = reportFrame(Mod.router);
const reportFrameWorkAuth = reportFrame(Mod.auth);
export const reportFrameWorkInitStart =  () => reportFrameWorkInit('00010101', ReportType.expose);

export const reportFrameWorkRouterInit = () => reportFrameWorkRouter('00010201', ReportType.other);

export const reportFrameWorkAuthInit = () => reportFrameWorkAuth('00010301', ReportType.other);

