import { useRouter } from 'vue-router';
import { setQuery } from 'common/utils/url';
import { MediaOptionEnum, UploadStatusEnum } from '@/views/creative/library/define';

export function useGoto() {
  const router = useRouter();

  // 返回上一页
  function goBack() {
    router.go(-1);
  }

  // 跳转到首页
  function gotoHome() {
    location.href = location.origin;
  }

  // 跳转到td广告创编页面
  function gotoAdsCreation({ media, param }: { media: string; param: any }) {
    router.push(`/trade/management/${media.toLocaleLowerCase()}/create?${setQuery(param)}`);
  }

  // 跳转到pivot页
  async function gotoAdsManage(media: string, game: string) {
    await router.push(`/trade/management/${media.toLocaleLowerCase()}?game=${game}`);
  }

  function gotoCreativeLibrary() {
    router.push({
      path: '/creative/management/aix',
    });
  }

  function gotoCreativeTask(
    newWin = false,
    query?: {
      channel?: MediaOptionEnum;
      status?: UploadStatusEnum;
      taskType?: string;
      ruleId?: string;
    },
  ) {
    const routerData = {
      path: '/creative/management/aix/task',
      query,
    };
    if (newWin) {
      window.open(router.options.history.base + router.resolve(routerData).fullPath);
    } else {
      router.push(routerData);
    }
  }

  function gotoAudienceLog(query: { id: string }) {
    router.push({
      path: '/audience/overview/log',
      query,
    });
  }

  function gotoAudienceForm() {
    router.push({
      path: '/audience/overview/form',
    });
  }

  function gotoAudience() {
    router.push({
      path: '/audience/overview',
    });
  }

  function gotoMonitorSetting() {
    router.push({
      path: '/trade/monitor/setting',
    });
  }

  function gotoMonitorRules() {
    router.push({
      path: '/trade/monitor/rules',
    });
  }

  function gotoMonitorAiOptimization() {
    router.push({
      path: '/trade/monitor/ai_optimization',
    });
  }

  async function gotoMonitorTargetNumberSetting() {
    await router.push({
      path: '/trade/monitor/target',
    });
  }

  function gotoPredictionOverviewAddCompetitor() {
    router.push({
      path: '/intelligence/prediction/settings',
      state: { type: 'init' },
    });
  }

  function gotoPredictionOverviewSetting() {
    router.push({
      path: '/intelligence/prediction/settings',
    });
  }

  function goToPredictionOverview() {
    router.replace({
      path: '/intelligence/prediction/overview',
    });
  }

  async function gotoLabelSystem() {
    await router.push({
      path: '/creative/management/label/system',
    });
  }

  async function gotoLabelInsightDetail(
    query: { labels?: string; firstLabel?: string; secondLabel?: string; filter: string },
    newTab = false,
  ) {
    const path = '/creative/analytics/label/detail';
    const routerData = {
      path,
      query,
    };
    newTab
      ? window.open(router.options.history.base + router.resolve(routerData).fullPath)
      : await router.push({
        path,
        query,
      });
  }

  async function gotoLabelAssetDetail(query: {
    game: string, sDate: string, eDate: string, asset_name: string, asset_type: string, url?: string,
    asset_serial_id: string, asset_id: string, youtube_id: string, index?: string, filter?: string, code?: string,
  }) {
    const routerData = {
      path: '/creative/analytics/label/asset',
      query,
    };
    window.open(router.options.history.base + router.resolve(routerData).fullPath);
  }

  async function gotoTopCreativeAssetDetail(
    query: {
      game: string, sDate: string, eDate: string, asset_name: string, asset_type: string, url?: string,
      asset_serial_id: string, asset_id: string, youtube_id: string, index?: string, filter?: string, code?: string,
    },
  ) {
    const routerData = {
      path: '/creative/analytics/top/creatives/asset',
      query,
    };
    window.open(router.options.history.base + router.resolve(routerData).fullPath);
  }

  async function gotoTopCreativesLabels(query: {
    game: string, code: string, sDate: string, eDate: string, index: string,
  }) {
    const routerData = {
      path: '/creative/analytics/top/creatives/labels',
      query,
    };
    window.open(router.options.history.base + router.resolve(routerData).fullPath);
  }

  function gotoCreativeAutoRulesList() {
    return router.push({
      path: '/creative/management/aix/auto_rules',
    });
  }

  function gotoCreativeAutoRulesDetail(query: { id: string; copy?: string }) {
    return router.push({
      path: '/creative/management/aix/auto_rules/detail',
      query,
    });
  }

  function gotoCreativeNameGenerator() {
    return router.push({
      path: '/creative/management/name_generator',
    });
  }

  function gotoCreativeNameGeneratorTaskList() {
    return router.push({
      path: '/creative/management/name_generator/task_list',
    });
  }

  return {
    goBack,
    gotoHome,
    gotoAdsCreation,
    gotoAdsManage,
    gotoCreativeTask,
    gotoAudienceLog,
    gotoAudienceForm,
    gotoAudience,
    gotoMonitorSetting,
    gotoMonitorRules,
    gotoMonitorAiOptimization,
    gotoMonitorTargetNumberSetting,
    gotoPredictionOverviewAddCompetitor,
    gotoPredictionOverviewSetting,
    goToPredictionOverview,
    gotoLabelSystem,
    gotoLabelInsightDetail,
    gotoLabelAssetDetail,
    gotoCreativeAutoRulesList,
    gotoCreativeAutoRulesDetail,
    gotoTopCreativesLabels,
    gotoTopCreativeAssetDetail,
    gotoCreativeNameGenerator,
    gotoCreativeNameGeneratorTaskList,
    gotoCreativeLibrary,
  };
}
