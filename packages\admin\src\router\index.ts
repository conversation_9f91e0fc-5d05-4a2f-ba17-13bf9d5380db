import type { RouteRecordDynamic } from '@@/router';
import type { LocationQuery, Router, RouteRecordNormalized, RouteRecordRaw } from 'vue-router';
import { createRouter, createWebHistory } from 'vue-router';
// import { useAuthStageStore } from '@/store/global/auth.store';
import adminRouter from '@/router/routes/admin.router';
import audienceRouter from '@/router/routes/audience.router';
import configurationRouter from '@/router/routes/configuration.router';
import creativeRouter from '@/router/routes/creative.router';
import intelligenceRouter from '@/router/routes/intelligence.router';
import biRouter from './routes/bi.router';
import homeRouter from './routes/home.router';
import tradeRouter from './routes/trade.router';
import influencerRouter from '@/router/routes/influencer.router';
// import heplercenterRouter from '@/router/routes/heplercenter.router';
// import notificationRouter from '@/router/routes/notification.router';
import { reportCommonExpose } from '@/report';
import { useAuthStageStore } from '@/store/global/auth.store';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useRouterStore } from '@/store/global/router.store';
// import { getLoginType } from 'common/utils/auth';
import { isEmpty, isString, pick } from 'lodash-es';
import { isIOA } from 'common/utils/common';

/**
 // 路由名 自动生成 规则举例：
 const biRouter = {
  path: '/bi',
  // name: 'BI', // 【RouteName】具体看下面解释
  meta: {
    icon: 'tv',
    name: 'BI', //【RbacName】具体看下面解释
    desc: 'Data Explorer',
    level: 1,  // 【RouteLevel】1级路由
    index: 0,
  },
  component: () => import('../../views/CommonIndex.vue'),
  children: [

    // 【默认路由】若不定义默认路由访问1级菜单，则会获取其第一个有权限的Children进行跳转
    // {
    //   path: '',
    //   name: 'BI_default',
    //   redirect: '/bi/hourly',
    // },

    // 【二级路由】
    {
          path: 'hourly',
          // name: 'BI / Real Time', // 【RouteName】一般无需定义，自动根据 每一层级的meta.name组合。若是后台返回的动态路由则需定义
          meta: {
            icon: 'time',
            name: 'Real Time', // 【RbacName】需与AiX1.0的Title配置一致，后台按Title来做RBAC权限。若无RBAC控制需求可不定义（但需要定义RouteName）
            title: 'RealTime', // 【ShowTitle】一般无需定义，默认使用meta.name。若meta.name和showTitle有冲突，可定义此项优先于meta.name展示
            url: 'https://aix.intlgame.com/bi/hourly',
          },
          component: () => import('../../views/FallbackAix.vue'),
    },

    ...
  ]
*/
export const MENU_LEVEL_SPLIT_FLAG = ' / ';
function attachRouteName(list: Array<RouteRecordRaw>, parentRbac: Array<string> = []): Array<RouteRecordRaw> {
  return list.map((routeCfg: RouteRecordRaw) => {
    const newParentRbac = parentRbac.concat(routeCfg?.meta?.name ?? []);
    const newRouteCfg: RouteRecordRaw = {
      ...(routeCfg as Pick<RouteRecordRaw, 'path'|'redirect'>),
      meta: {
        ...(routeCfg?.meta || {}),
        // 仅对静态路由的parent做生成，动态路由由后台下发时生成
        parent: routeCfg?.meta?.parent
          || parentRbac.reduce((list, _, index, parentRbac) => list
            .concat(parentRbac.slice(0, index + 1).join(MENU_LEVEL_SPLIT_FLAG)), [] as string[]),
      },
      name: routeCfg?.name ?? newParentRbac.join(MENU_LEVEL_SPLIT_FLAG),
      children: (Array.isArray(routeCfg.children)
        ? attachRouteName(routeCfg.children, newParentRbac) : undefined) as Array<RouteRecordRaw>,
    };
    return newRouteCfg;
  });
}
function getAllAllowRouter(
  allRoutes: RouteRecordRaw[] = [],
  allowRouteNameList?: string[],
) {
  return allRoutes.filter(({ name }) => name && isString(name) && allowRouteNameList?.includes(name));
}
function findRouterByName(
  targetRouteName: string | undefined,
  routes: RouteRecordRaw[] = [],
): RouteRecordRaw | undefined {
  return targetRouteName ? routes.find(item => item.name === targetRouteName) : undefined;
}
function findSiblingRouter(
  targetRoute: RouteRecordNormalized,
  allowRoutes: RouteRecordRaw[] = [],
): RouteRecordRaw | undefined {
  const parentRouter = findParentRouter(targetRoute, allowRoutes);
  if (parentRouter?.children?.length ?? 0 > 0) {
    const childRouter = getAllAllowRouter(
      parentRouter!.children,
      allowRoutes.map(item => item?.name?.toString()).filter(v => v) as string[],
    )?.find(router => router.name !== targetRoute.name);
    if (childRouter) {
      return childRouter;
    }
  }
  return parentRouter ? findParentRouter(parentRouter, allowRoutes) : undefined;
}
function findParentRouter(
  targetRoute: RouteRecordRaw,
  allowRoutes: RouteRecordRaw[] = [],
): RouteRecordRaw | undefined {
  const parentNameList = (Array.isArray(targetRoute?.meta?.parent) ? targetRoute?.meta?.parent : []) ?? [];
  const parentRouterName = parentNameList.at(-1);
  if (!parentRouterName) return undefined;

  return findRouterByName(parentRouterName, allowRoutes);
}
function isRouteIsDir(route: RouteRecordNormalized | RouteRecordRaw | undefined): boolean {
  return !!(route?.meta?.level === 1 || route?.meta?.dir);
}
// - - - - - - - - - StaticRoutes - - - - - - - - - - -
const staticRoutes = attachRouteName([
  homeRouter,
  tradeRouter,
  biRouter,
  creativeRouter,
  audienceRouter as RouteRecordRaw,
  intelligenceRouter,
  adminRouter,
  configurationRouter,
  influencerRouter,
].filter(Boolean) as RouteRecordRaw[]);
export const notFoundRoute = {
  path: '/:pathMatch(.*)*',
  name: 'NotFound',
  component: () => import('../views/404.vue'),
};

// - - - - - - - - - DynamicRoutes - - - - - - - - - - -
// const routerModules  = import.meta.glob(['../views/**/*.vue']);
const routerModules: Record<string, () => Promise<unknown>> = {
  FallbackAix: () => import('../views/FallbackAix.vue'),
  CommonFallAix: () => import('../views/CommonFallAix.vue'),
  SupersetTemplate: () => import('../views/SupersetTemplate.vue'),
};

function formatDynamicRouter(routes: RouteRecordDynamic[]): RouteRecordRaw[] {
  return routes?.map(({ component, children, ...field }: RouteRecordDynamic) => ({
    ...(field as Pick<RouteRecordRaw, 'path'|'name'|'meta'>),
    component: routerModules[component],
    children: Array.isArray(children) ? formatDynamicRouter(children) : undefined,
  }))?.filter(item => item.component) || [];
}
async function registerDynamicRouter(router: Router) {
  const routerStore = useRouterStore();

  const childrenByParentJoinName: Record<string, {
    parent: Array<string>;
    children: Array<RouteRecordRaw>
  }> = {};
  const randomJoinKey = Math.random().toString();
  formatDynamicRouter(routerStore.nowGameDynamicRoutes)?.forEach((item: RouteRecordRaw) => {
    const parent = item?.meta?.parent as Array<string>;
    if (Array.isArray(parent)) {
      // Children route
      const parentJoinName = parent.join(randomJoinKey);
      childrenByParentJoinName[parentJoinName] = childrenByParentJoinName[parentJoinName] || { parent, children: [] };
      childrenByParentJoinName[parentJoinName].children.push(item);
    } else {
      // Level-1 route
      router.addRoute(item);
    }
  });

  const allRoutesMap: Record<string, RouteRecordNormalized> = router.getRoutes()
    .reduce((prev, next) => ({ ...prev, [next.name!]: next }), {});
  Object.values(childrenByParentJoinName).forEach(({ parent, children }) => {
    const childrenList: Array<Array<RouteRecordRaw> | RouteRecordNormalized> = [children];
    parent.concat().reverse()
      .some((parentRouteName, index) => {
        const parentRoute: RouteRecordNormalized = allRoutesMap[parentRouteName];
        if (parentRoute) {
          // Collect every level's children to update its father's children
          // Coz both children ain't a same object reference
          // Need traverse every level parent to change its children and re-add route
          childrenList.push(parentRoute);
          parentRoute.children = Object.values((Array.isArray(parentRoute.children) ? parentRoute.children : [])
            .concat(childrenList[index])
            .reduce( // Children perhaps duplicate, unique by its route name
              (prev: RouteRecordRaw, next: RouteRecordRaw) => ({
                ...prev,
                ...(next?.name ? { [next.name.toString()]: next } : {}),
              })
              , {} as unknown as RouteRecordRaw,
            ));
          router.addRoute(parentRoute);
        }
        return !parentRoute;
      });
  });
}
function redirectWithQuery(routerCfg: RouteRecordNormalized | RouteRecordRaw, query: LocationQuery) {
  console.timeLog('router check');
  return {
    ...(routerCfg || {}),
    query,
  };
}
let routeCycleNum = 0;
function isEndRouteCycle() {
  routeCycleNum += 1;
  return routeCycleNum > 10;
}
function endRouteCycle(result: boolean) {
  routeCycleNum = 0;
  return result;
}
/**
 * 是否和当前目录匹配
 * @param path 路径
 * @param routes 路由
 * @param opt 配置参数
 * @returns {boolean|boolean|*}
 */
export const isBelongToRoute = (path: string, routes: RouteRecordRaw[], opt?: { like: boolean }): boolean => {
  let bool = false;
  if (!path) {
    return bool;
  }
  for (let i = 0, len = routes.length; i < len; i++) {
    const route = routes[i];
    if (route.path === path) {
      bool = true;
      break;
    }
    if (opt?.like) {
      if (route.path.includes(path)) {
        bool = true;
        break;
      }
    }
    if (route.children) {
      return isBelongToRoute(path, route.children, opt);
    }
  }
  return bool;
};

const router = createRouter({
  history: createWebHistory('/v2/'),
  routes: [...staticRoutes, notFoundRoute],
  scrollBehavior() {
    // 始终滚动到顶部
    return { top: 0 };
  },
});

export async function refreshRoute(routerGameCode: string) {
  const routerStore = useRouterStore();
  routerStore.setRouterGame(routerGameCode);
  // Register Dynamic Router
  await registerDynamicRouter(router);
  // Refresh Router
  const allRoutes = router.getRoutes();
  routerStore.setAllRoutes(allRoutes);
  routerStore.setRouterDone();
  return allRoutes;
}

router.beforeEach(async (to, from): Promise<any> => {
  console.time('router check');

  // Max Cycle Limit
  if (isEndRouteCycle()) {
    endRouteCycle(false);
    console.error('router max cycle limit', to);
    return notFoundRoute;
  }
  // Loading to Router
  const routerStore = useRouterStore();
  routerStore.setPageLoading(to, from);

  // Check Login Status
  const authStageStore = useAuthStageStore();
  await authStageStore.checkAuthState();

  const isFirstEnter = from?.path === '/' && from?.name === undefined && from?.matched?.length === 0;

  // ------------ 保留参数逻辑 start ----------------
  // url上需要保留的参数key
  const retainQuery: string[] = ['cgiMode', 'no_cache'];
  // 跳转的目标url是否带有需要保留的参数
  const toHasRetainQuery = retainQuery.some((queryKey: string) => to?.query?.[queryKey]);
  const retainQueryRecord: Record<string, unknown> = toHasRetainQuery
    ? {}
    : pick(from?.query, retainQuery);
  if (!isEmpty(retainQueryRecord)) {
    return { ...to, ...{ query: { ...to.query, ...retainQueryRecord } } };
  }
  // ------------ 保留参数逻辑 end ----------------

  // Loading to GameList
  const globalGameStore = useGlobalGameStore();
  globalGameStore.setGameCodeLoading();

  // Get Cur-Game
  const gameCodeFrQuery: string | undefined = (
    game => (Array.isArray(game) ? game.at(-1) : game)?.toString()
  )(to?.query?.game || from?.query?.game);

  // set game code by localStorage or query
  const gameCode = gameCodeFrQuery?.toString() || globalGameStore.storageGameCode || '';
  // 在 superToken的场景下，demo是一定有的（不一定）
  // if (getLoginType() === '2' && gameCode === '') {
  //   gameCode = 'demo';
  // }

  let routerGameCode: string | undefined = gameCode;
  // Init Router
  switch (true) {
    case routerStore.isRouterLoading:
      console.timeLog('router check');
      return endRouteCycle(false);
    case !routerGameCode || routerStore.checkNowGameIsNeedLoad(routerGameCode): {
      // Init Router Game
      routerStore.setRouterLoading();
      const legalGameCode = await routerStore.initGameRouter(routerGameCode);
      if (!legalGameCode) {
        routerStore.setRouterNeedSelectGame();
        console.timeLog('router check');
        return endRouteCycle(false);
      }
      routerGameCode = legalGameCode;
      // routerStore.setRouterGame(routerGameCode);
      // // Register Dynamic Router
      // await registerDynamicRouter(router);
      // // Refresh Router
      // const allRoutes = router.getRoutes();
      // routerStore.setAllRoutes(allRoutes);
      // routerStore.setRouterDone();
      const allRoutes = await refreshRoute(routerGameCode);
      // Redirect Dynamic Route ( if needed )
      const redirectTo: RouteRecordNormalized | undefined = to.name === notFoundRoute.name
        ? allRoutes.find(item => item.path === to.path) : undefined;
      if (redirectTo) {
        console.timeLog('router check');
        return redirectWithQuery(redirectTo, to?.query);
      }
    }
    default: {
      routerStore.setRouterGame(routerGameCode);
      const matchRoute: RouteRecordNormalized | undefined = to?.matched?.at(-1);
      const matchRouteName = matchRoute?.name?.toString();
      if (matchRouteName && matchRouteName !== notFoundRoute.name) {
        const allRoutes = router.getRoutes();
        const nowGameAllowRouteNameList = routerStore?.nowGameAllowRoutes?.sort()
         || []; // Need sort to avoid match last-level route

        // Dir Router
        if (isRouteIsDir(matchRoute)) {
          // Redirect to First Permission Children
          const curRouteChildsCfg = allRoutes.find(item => item.name === matchRouteName)?.children;
          const allowChild = curRouteChildsCfg?.find(item => isString(item?.name)
            && nowGameAllowRouteNameList.includes(item?.name));
          const redirectTo = allowChild ?? curRouteChildsCfg // Prior: allow route
            ?.find(item => isString(item?.name)); // Default: first route in all routes
          if (redirectTo) {
            console.timeLog('router check');
            return redirectWithQuery(redirectTo, to?.query);
          }
        }

        // Home Router
        const nowGameAllowRoutes = getAllAllowRouter(allRoutes, nowGameAllowRouteNameList);
        if (homeRouter.name === matchRouteName) {
          const fristBIRoutes = nowGameAllowRoutes.filter(item => item?.name?.toString()?.startsWith('BI') && !isRouteIsDir(item))?.sort((a, b) => (a?.meta?.index ?? 999) - (b.meta?.index ?? 999))?.[0];
          const fristTDRoutes = nowGameAllowRoutes.filter(item => item?.name?.toString()?.startsWith('Trading Desk') && !isRouteIsDir(item))?.sort((a, b) => (a?.meta?.index ?? 999) - (b.meta?.index ?? 999))?.[0];
          const redirectTo = fristBIRoutes
            || fristTDRoutes || nowGameAllowRoutes?.find(item => item.name !== matchRouteName);
          if (redirectTo) {
            return redirectWithQuery(redirectTo, {
              ...(to?.query || {}),
              ...(routerGameCode?.toString() ? {
                game: routerGameCode,
              } : {}),
            });
          }
        }

        // Non-existent Router: Switch form GameA & PathB to GameC, but GameC don't have PathB
        if (matchRouteName && !nowGameAllowRouteNameList.includes(matchRouteName!)) {
          const siblingRoute = findSiblingRouter(matchRoute!, nowGameAllowRoutes);
          if (siblingRoute) {
            console.timeLog('router check');
            return redirectWithQuery(siblingRoute, to?.query);
          }
          const routeObj = allRoutes?.reduce((routeObj: Record<string, RouteRecordNormalized>, route) => ({
            ...routeObj,
            [route?.name?.toString() || '']: route,
          }), {}) || {};

          // 当一级菜单下一直取第一个路由直到可访问的路由，且该路由没权限，且其兄弟路由也没权限，则跳其上层的最后一个兄弟路由
          const parentRouteNameList = matchRoute?.meta?.parent || [];
          if (!isFirstEnter && parentRouteNameList.length > 0) {
            const compareRouteName = parentRouteNameList.length === 1
              ? matchRouteName
              : routeObj[parentRouteNameList.at(1)!].name; // 二级路由名
            const level1Route = routeObj[parentRouteNameList.at(0)!];
            const isFristRoute = level1Route.children[0]?.name === compareRouteName;
            const isOneChildren = level1Route.children.length === 1;
            if (isOneChildren) return endRouteCycle(true);
            const level2RouteFirstChild = level1Route.children.at(-1);
            if (isFristRoute && level2RouteFirstChild) return redirectWithQuery(level2RouteFirstChild, to?.query);
          }
          const allowRouteName = nowGameAllowRouteNameList
            .filter(routeName => routeObj[routeName])?.[0]; // Keep sort order
          const firstAllowRoute = routeObj[allowRouteName];
          if (!isFirstEnter && firstAllowRoute) { // && !isRouteIsDir(firstAllowRoute)
            console.timeLog('router check');
            return redirectWithQuery(firstAllowRoute, to?.query);
          }
        }
      }
      break;
    }
  }
  // Pass Game Query
  if (!(to?.query?.game) && routerGameCode) {
    console.timeLog('router check');
    return { ...to, ...{ query: { ...to.query, game: routerGameCode } } };
  }
  console.timeLog('router check');
  return endRouteCycle(true);
});

router.beforeResolve(() => {
  console.log('before resolve');
  console.timeLog('router check');
});

router.afterEach(async (to) => {
  console.timeEnd('router check');
  const routerStore = useRouterStore();
  const isCheckInIOA = to?.meta?.isIOA === 1;
  if (isCheckInIOA) {
    routerStore.setIOAEnvironment(await isIOA());
  }
  // pv上报从commonview的tryOnMounted移动到router/index.ts中.
  const reportId = to?.meta?.reportId;
  const routeName = to.name?.toString() || to?.meta?.name;
  const globalGameStore = useGlobalGameStore();

  if (reportId) {
    // 消除新增功能红点
    // 暂时不添加pubgm
    globalGameStore.clearFunctionRed(reportId, to?.meta?.showNewFunctionRed);

    // reportCommonPv(reportId, routeName);
    reportCommonExpose(reportId, routeName);
  };

  routerStore.setPageLoadEnd();
  routerStore.setFirstRoute(to.matched[0]);
  // await registerDynamicRouter(router);

  globalGameStore.setGameCode(to?.query?.game as string);
  globalGameStore.setGameCodeDone();
  endRouteCycle(true);
});

router.onError((e) => {
  console.error('error', e);
  const routerStore = useRouterStore();
  routerStore.setPageErr();
});

export {
  router,
};

