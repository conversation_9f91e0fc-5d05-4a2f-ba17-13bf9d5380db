import CommonIndex from '@/views/CommonIndex.vue';
import { RouteComponent } from 'vue-router';

export default {
  path: '/admin',
  meta: {
    icon: 'controller',
    name: 'Admin',
    title: 'Admin',
    desc: 'Manage Panel',
    level: 1,
    index: 5,
  },
  component: CommonIndex as unknown as RouteComponent,
  // beforeEnter(to: RouteLocationNormalized) {
  //   const currentRoute = to;
  //   if (currentRoute.meta.reportId) {
  //     reportCommonExpose(currentRoute.meta.reportId, currentRoute.name?.toString() || currentRoute.meta.name);
  //   }
  //   const url = 'https://aix-monitor.intlgame.com/d/WPwj8QYVk/aix2-0?orgId=1&from=now-24h&to=now&var-gameid=All&var-router=All';
  //   window.open(url);
  //   return false;
  // },
  children: [
    // {
    //   path: 'log',
    //   meta: {
    //     icon: 'compute-monitor',
    //     name: 'Log',
    //     dir: true,
    //   },
    //   component: CommonIndex,
    //   children: [
    //     {
    //       path: 'page_log',
    //       meta: {
    //         icon: 'dot',
    //         name: 'Page Log', // AiX1.0的Title，同时也是rbac权限
    //         url: 'https://exp.aix.intlgame.com/config?__game=demo',
    //         reportId: '07010101',
    //       },
    //       component: FallbackAix,
    //       props: { type: 'iframe' },
    //     },
    //   ],
    // },
    /* {
      path: 'od',
      meta: {
        icon: 'compute-monitor',
        name: 'Operational Data',
        reportId: '07a00101',
        dir: true,
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [
        // {
        //   path: 'aix',
        //   meta: {
        //     icon: 'dot',
        //     name: 'Page Log', // AiX1.0的Title，同时也是rbac权限
        //     title: 'AiX', // AiX2.0的Title，若不写则取rbac
        //     url: 'https://aix-monitor.intlgame.com/d/WPwj8QYVk/aix?orgId=1&theme=light&from=now-2d&to=now&var-gameid=All&var-mod=All&var-router=All',
        //     reportId: '01010101',
        //   },
        //   component: Frame,
        //   // component: FallbackAix,
        // },
      ],
    }, */
    // {
    //   path: 'md',
    //   meta: {
    //     icon: 'time',
    //     name: 'Monitor Data',
    //     reportId: '07b00101',
    //     dir: true,
    //   },
    //   component: CommonIndex as unknown as RouteComponent,
    //   children: [],
    // },
    // {
    //   path: 'platform-summary',
    //   meta: {
    //     icon: 'platform-summary',
    //     name: 'Platform Summary',
    //     reportId: '07c00101',
    //     dir: true,
    //   },
    //   component: CommonIndex as unknown as RouteComponent,
    //   children: [],
    // },
    // {
    //   path: 'raw-data',
    //   meta: {
    //     icon: 'raw-data',
    //     name: 'Raw Data',
    //     reportId: '07d00101',
    //     dir: true,
    //   },
    //   component: CommonIndex as unknown as RouteComponent,
    //   children: [],
    // },
    {
      path: 'platform-summary',
      meta: {
        icon: 'platform-summary',
        name: 'Platform Summary',
        reportId: '07c00101',
        dir: true,
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [],
    },
    {
      path: 'raw-data',
      meta: {
        icon: 'raw-data',
        name: 'Raw Data',
        reportId: '07d00101',
        dir: true,
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [],
    },
  ],
};
