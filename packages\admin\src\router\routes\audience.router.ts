import { genUrlHiddenParams } from 'common/utils/url';
import CommonIndex from '@/views/CommonIndex.vue';
import FallbackAix from '@/views/CommonFallAix.vue';
import { RouteComponent } from 'vue-router';

export default {
  path: '/audience',
  meta: {
    icon: 'audience',
    name: 'Audience & Event',
    title: 'Audience',
    desc: 'Event Segmentation',
    level: 1,
    index: 2,
  },
  component: CommonIndex as unknown as RouteComponent,
  children: [
    {
      path: 'overview',
      meta: {
        icon: 'flag',
        name: 'Manage Audience',
        // url: genUrlHiddenParams('https://aix.intlgame.com/audience/overview?__game=pubgm'),
        dir: true,
      },
      component: CommonIndex as unknown as RouteComponent,
      // redirect: '/audience/overview',
      children: [
        {
          path: '',
          meta: {
            hide: true,
            name: 'Audience',
            reportId: '03010101',
            isBreadcrumb: 'false',
          },
          component: () => import('@/views/audience/overview/Index.vue'),
        },
        {
          path: 'log',
          meta: {
            name: 'Audience Log',
            hide: true,
            reportId: '03050101',
          },
          component: () => import('@/views/audience/overview/log/Index.vue'),
        },
        {
          path: 'form',
          meta: {
            name: 'Audience Form',
            hide: true,
            reportId: '03020101',
          },
          component: () => import('@/views/audience/overview/form/Index.vue'),
        },
      ],
    },
    // {
    //   path: 'overview/log',
    //   meta: {
    //     name: 'Audience Log',
    //     hide: true,
    //   },
    //   component: () => import('@/views/audience/overview/log/Index.vue'),
    // },
    {
      path: 'profile',
      meta: {
        icon: 'file-check-list',
        name: 'Audience Overview',
        reportId: '03030101',
        url: genUrlHiddenParams('https://aix.intlgame.com/audience/profile?__game=pubgm'),
      },
      component: FallbackAix as unknown as RouteComponent,
    },
    {
      path: 'launcher',
      meta: {
        icon: 'box',
        name: 'Audience Launcher',
        reportId: '03040101',
        url: genUrlHiddenParams('https://aix.intlgame.com/audience/launcher?__game=pubgm'),
      },
      component: FallbackAix as unknown as RouteComponent,
    },
  ],
};
