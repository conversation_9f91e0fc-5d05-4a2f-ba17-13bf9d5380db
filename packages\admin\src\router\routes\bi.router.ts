import { genUrlHiddenParams } from 'common/utils/url';
import CommonIndex from '@/views/CommonIndex.vue';
import FallbackAix from '@/views/FallbackAix.vue';
import CommonTemplate from '@/views/CommonTemplate.vue';
import { RouteComponent } from 'vue-router';
import { getUrlParams } from 'common/utils/baseUrl';
import { useEnv } from 'common/compose/env';

const getCommonTemplate = () => {
  const env = useEnv();
  const isUse = env.getIsFuncom(); // || env.getIsDev();
  if (!isUse) return {
    use: false,
    template: {},
  };

  // 临时先这么写判断
  const query = getUrlParams() as Record<string, string>;
  const gameTypeFrURL = query?.game_type;
  // const game = query?.game;
  // const isWhiteGame = game === 'demo';
  // if (!isWhiteGame) return {
  //   use: false,
  //   template: {},
  // };

  const isH5 = gameTypeFrURL?.toUpperCase() === 'H5';
  return {
    use: !isH5,
  };
};
const tCfg = getCommonTemplate();


// let commonTemplateInstance: any = undefined;
// const getCommonTemplateInstance = () => {
//   if (commonTemplateInstance) return commonTemplateInstance;
//   commonTemplateInstance = import('../../views/CommonTemplate.vue');
//   return commonTemplateInstance;
// };

export default {
  path: '/bi',
  meta: {
    icon: 'tv',
    name: 'BI',
    title: 'Business Insight',
    desc: 'Data Explorer',
    level: 1,
    index: 0,
  },
  component: CommonIndex as unknown as RouteComponent,
  children: [
    // 无需配置默认逻辑
    // {
    //   path: '',
    //   name: 'BI_default',
    //   redirect: '/bi/hourly',
    // },
    {
      path: 'hourly',
      meta: {
        icon: 'time-cycle',
        name: 'Real Time', // AiX1.0的Title，同时也是rbac权限
        title: 'RealTime', // AiX2.0的Title，若不写则取rbac
        url: genUrlHiddenParams('https://aix.intlgame.com/bi/hourly', {
          hideWaterMask: true,
          hideTopNav: true,
          hideLeftNav: true,
          hideInnerNav: false,
        }),
        reportId: '01010101',
        index: 0,
        templatePrefix: 'bi_realtime',
      },
      component: tCfg.use ? CommonTemplate : (FallbackAix as unknown as RouteComponent),
      // component: () => import('../../views/bi/index.vue'),
    },
    {
      path: 'realtime',
      meta: {
        icon: 'time-cycle',
        name: 'New Real Time', // AiX1.0的Title，同时也是rbac权限
        title: 'New RealTime', // AiX2.0的Title，若不写则取rbac
        reportId: '01080101',
        index: 0,
        templatePrefix: 'bi_realtime',
      },
      component: CommonTemplate,
    },
    {
      path: 'overview',
      meta: {
        icon: 'group',
        name: 'Overview',
        url: genUrlHiddenParams('https://aix.intlgame.com/bi/overview', {
          hideWaterMask: true,
          hideTopNav: true,
          hideLeftNav: true,
          hideInnerNav: false,
        }),
        reportId: '01020101',
        index: 1,
        templatePrefix: 'bi_overview',
      },
      component: tCfg.use ? CommonTemplate : (FallbackAix as unknown as RouteComponent),
    },
    {
      path: 'ua',
      meta: {
        icon: 'group',
        name: 'User Acquisition',
        url: genUrlHiddenParams('https://aix.intlgame.com/bi/overview', {
          hideWaterMask: true,
          hideTopNav: true,
          hideLeftNav: true,
          hideInnerNav: false,
        }),
        reportId: '01070101',
        index: 1,
        templatePrefix: 'bi_ua',
      },
      component: tCfg.use ? CommonTemplate : (FallbackAix as unknown as RouteComponent),
    },
    {
      path: 'pivot',
      meta: {
        icon: 'compute-monitor',
        name: 'Pivot',
        url: genUrlHiddenParams('https://aix.intlgame.com/bi/pivot/common', {
          hideWaterMask: true,
          hideTopNav: true,
          hideLeftNav: true,
          hideInnerNav: false,
        }),
        reportId: '01030101',
        index: 2,
        templatePrefix: 'bi_pivot',
      },
      component: tCfg.use ? CommonTemplate : (FallbackAix as unknown as RouteComponent),
    },
    {
      path: 'pivot_kol',
      meta: {
        icon: 'compute-monitor',
        name: 'Pivot KOL',
        reportId: '01090101',
        index: 2,
        templatePrefix: 'bi_pivot_kol',
      },
      component: CommonTemplate,
    },
    {
      path: 'pivot_reattribution',
      meta: {
        icon: 'compute-monitor',
        name: 'Pivot Reattribution',
        title: 'Reattribution',
        reportId: '01100101',
        info: 'This report aims to evaluate the effectiveness of reacquisiton campaign, and the reengagement refers to previously installed users logging back into the game after 30 days.',
        index: 10,
        templatePrefix: 'bi_pivot_reattribution',
      },
      component: CommonTemplate,
    },
    {
      path: 'pivot_reengagement',
      meta: {
        icon: 'compute-monitor',
        name: 'Pivot Reengagement',
        title: 'Reengagement',
        reportId: '01110101',
        info: '',
        index: 11,
        templatePrefix: 'bi_pivot_reengagement',
      },
      component: CommonTemplate,
    },
    {
      path: 'pivot_ualp_dashboard',
      meta: {
        icon: 'compute-monitor',
        name: 'UALP Dashboard',
        title: 'UALP Dashboard',
        reportId: '01120101',
        info: '',
        index: 12,
        templatePrefix: 'bi_pivot_ualp_dashboard',
      },
      component: CommonTemplate,
    },
    {
      path: 'pivot_sp',
      meta: {
        icon: 'compute-monitor',
        name: 'Pivot SP',
        url: genUrlHiddenParams('https://aix.intlgame.com/bi/pivot/pubgm', {
          hideWaterMask: true,
          hideTopNav: true,
          hideLeftNav: true,
          hideInnerNav: false,
        }),
        reportId: '01040101',
        index: 3,
      },
      component: FallbackAix as unknown as RouteComponent,
    },
    {
      path: 'reattribution',
      meta: {
        icon: 'change',
        name: 'Reattribution',
        url: genUrlHiddenParams('https://aix.intlgame.com/bi/reattribution', {
          hideWaterMask: true,
          hideTopNav: true,
          hideLeftNav: true,
          hideInnerNav: false,
        }),
        reportId: '01050101',
        index: 4,
      },
      component: FallbackAix as unknown as RouteComponent,
    },
    {
      path: 'ltv',
      meta: {
        icon: 'file-upload',
        name: 'LTV Prediction',
        url: genUrlHiddenParams('https://aix.intlgame.com/bi/ltv', {
          hideWaterMask: true,
          hideTopNav: true,
          hideLeftNav: true,
          hideInnerNav: false,
        }),
        reportId: '01060101',
        index: 5,
      },
      component: FallbackAix as unknown as RouteComponent,
    },
    {
      path: 'custom_report',
      meta: {
        icon: 'file-upload',
        name: 'Custom Report',
        dir: true,
        reportId: '01a00101',
        index: 6,
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [],
    },
    {
      path: 'tableau_report',
      meta: {
        icon: 'file-upload',
        name: 'Tableau Report',
        dir: true,
        reportId: '01b00101',
        index: 7,
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [],
    },
  ],
};
