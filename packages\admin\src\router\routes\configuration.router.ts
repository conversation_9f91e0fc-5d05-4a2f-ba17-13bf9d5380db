import CommonIndex from '@/views/CommonIndex.vue';
import { RouteComponent } from 'vue-router';
export default {
  path: '/config',
  name: 'Setting',
  meta: {
    icon: 'setting',
    name: 'Setting',
    title: 'Setting',
    desc: 'Setting',
    level: -1,
    index: 2,
  },
  // component: () => import('../../views/configuration/index.vue'),
  component: CommonIndex as unknown as RouteComponent,
  children: [
    {
      path: '',
      name: 'config/default',
      redirect: '/config/profile',
    },
    {
      path: 'profile',
      meta: {
        name: 'My Profile',
        icon: 'user-circle',
        reportId: '06010101',
      },
      component: () => import('../../views/configuration/profile/index.vue'),
    },
    {
      path: 'business',
      meta: {
        name: 'Business Management',
        title: 'Business Manage',
        icon: 'usergroup',
        reportId: '********',
      },
      component: () => import('../../views/configuration/business/index.vue'),
    },
    {
      path: 'role',
      meta: {
        name: 'Role Manage',
        title: 'Role Manage',
        icon: 'role-manage-icon',
        reportId: '********',
      },
      component: () => import('../../views/configuration/role/index.vue'),
    },
    {
      path: 'accounts',
      meta: {
        name: 'AD Accounts',
        title: 'AD Accounts',
        icon: 'user-circle',
        reportId: '********',
      },
      redirect: '/config/accounts',
      component: CommonIndex as unknown as RouteComponent,
      children: [
        {
          path: '',
          meta: {
            name: 'AD Accounts',
            reportId: '********',
            isBreadcrumb: 'false',
            hide: true,
          },
          component: () => import('../../views/configuration/accounts/index.vue'),
        },
        {
          path: 'add_accounts',
          meta: {
            name: 'Add Accounts',
            reportId: '********',
            hide: true,
          },
          component: () => import('../../views/configuration/accounts/components/channles/index.vue'),
        },
      ],
    },
    {
      path: 'management',
      meta: {
        icon: 'file-check-list',
        name: 'Management',
        title: 'Management',
        dir: true,
        index: 0,
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [
        {
          path: 'metric',
          meta: {
            name: 'Metric',
            title: 'Metric',
            reportId: '********',
          },
          component: () => import('@/views/configuration/management/metric/index.vue'),
        },
        {
          path: 'campaign_naming',
          meta: {
            name: 'Campaign Naming',
            title: 'Campaign Naming',
            reportId: '********',
          },
          component: () => import('../../views/configuration/management/campaign_naming/index.vue'),
        },
      ],
    },
  ],
};
