import { genUrlHiddenParams } from 'common/utils/url';
import CommonIndex from '@/views/CommonIndex.vue';
import FallbackAix from '@/views/CommonFallAix.vue';
import { RouteComponent } from 'vue-router';
import { BreadcrumbTitle } from 'common/components/Layout/BreadcrumbTitle';
export default {
  path: '/creative',
  meta: {
    icon: 'creative',
    name: 'Creative Center',
    title: 'Creative',
    desc: 'Creative Manager',
    level: 1,
    index: 3,
  },
  component: CommonIndex as unknown as RouteComponent,
  children: [
    {
      path: 'management',
      meta: {
        icon: 'file-check-list',
        name: 'Management',
        title: 'Management',
        dir: true,
        isBreadcrumb: 'false',
      },
      children: [
        {
          path: 'name_generator',
          meta: {
            // name: 'Name Generator',
            name: 'SmartNaming Hub',
            isBreadcrumb: 'false',
          },
          redirect: '/creative/management/name_generator',
          component: CommonIndex as unknown as RouteComponent,
          children: [
            {
              path: '',
              meta: {
                name: 'SmartNaming Hub',
                reportId: '04200101',
              },
              component: () => import('../../views/creative/name-generator/Index.vue'),
            },
            {
              path: 'task_list',
              meta: {
                name: 'Task list',
                title: 'Upload Cloud Drive Task List',
                reportId: '04210101',
              },
              component: () => import('../../views/creative/name-generator/Task.vue'),
            },
          ],
        },
        {
          path: 'aix',
          meta: {
            name: 'AiX Library',
            isBreadcrumb: 'false',
          },
          redirect: '/creative/management/aix', // 这个是承载容器，通过强制跳转跳转到业务组件
          component: CommonIndex as unknown as RouteComponent,
          children: [
            {
              path: '',
              meta: {
                name: 'AiX Library',
                reportId: '04010101',
                // url: genUrlHiddenParams('https://aix.intlgame.com/creative/hub'),
              },
              component: () => import('../../views/creative/library/Index.vue'),
            },
            {
              path: 'task',
              meta: {
                name: 'Synced Media Task List',
                reportId: '04060101',
              },
              component: () => import('../../views/creative/library/Task.vue'),
            },
            {
              path: 'auto_rules',
              meta: {
                name: 'Automatic Task Rules',
              },
              redirect: '/creative/management/aix/auto_rules', // 这个是承载容器，通过强制跳转跳转到业务组件
              component: CommonIndex as unknown as RouteComponent,
              children: [
                {
                  path: '',
                  meta: {
                    name: 'Automatic Task Rules',
                    reportId: '04170101',
                  },
                  component: () => import('../../views/creative/library/AutoRulesList.vue'),
                },
                {
                  path: 'detail',
                  meta: {
                    name: 'Sync Media Rule',
                    reportId: '04170101',
                  },
                  component: () => import('../../views/creative/library/AutoRulesDetail.vue'),
                },
              ],
            },
          ],
        },
        {
          path: 'media',
          meta: {
            name: 'Media Library',
            reportId: '04070101',
          },
          component: () => import('../../views/creative/library/Media.vue'),
        },
        {
          path: 'label',
          meta: {
            name: 'Label Management',
            isBreadcrumb: 'false',
          },
          redirect: '/creative/management/label', // 这个是承载容器，通过强制跳转跳转到业务组件
          component: CommonIndex as unknown as RouteComponent,
          children: [
            {
              path: '',
              meta: {
                name: 'Label Management',
                reportId: '04070201',
              },
              component: () => import('../../views/creative/label/manage/index.vue'),
            },
            {
              path: 'system',
              meta: {
                name: 'Label System',
                reportId: '04070301',
              },
              component: () => import('../../views/creative/label/manage/LabelSystem.vue'),
            },
          ],
        },
      ],
    },
    {
      path: 'analytics',
      meta: {
        icon: 'pic-search',
        name: 'Creative Analytics',
        title: 'Analytics',
        dir: true,
        isBreadcrumb: 'false',
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [
        {
          path: 'dashboard/appsflyer',
          meta: {
            name: 'Dashboard Af', // 和之前的dashboard区分
            title: 'Creative Dashboard', // 外显名称
            reportId: '04180101',
          },
          component: () => import('../../views/creative/dashboard-af/Index.vue'),
        },
        {
          path: 'daily',
          meta: {
            name: 'Dashboard', // Creative Analytics / Dashboard 对应AiX1.0的 Creative Pivot Daily 和 Creative Pivot Weekly
            reportId: '04080101',
          },
          component: () => import('../../views/creative/dashboard/index.vue'),
        },
        {
          path: 'label',
          meta: {
            name: 'Labels Insight',
            isBreadcrumb: 'false',
          },
          component: CommonIndex as unknown as RouteComponent,
          redirect: '/creative/analytics/label/insight', // 这个是承载容器，通过强制跳转跳转到业务组件
          children: [
            {
              path: 'insight',
              meta: {
                name: 'Labels Insight',
                reportId: '04170101',
              },
              component: () => import('../../views/creative/label/insight/Index.vue'),
            },
            {
              path: 'detail',
              meta: {
                name: 'Labels Detail',
                isHidePc: true, // 是否在菜单中展示
                reportId: '04170201',
              },
              component: () => import('../../views/creative/label/insight/LabelAssets.vue'),
            },
            {
              path: 'asset',
              meta: {
                name: 'Asset Detail',
                isHidePc: true, // 是否在菜单中展示
                reportId: '04170301',
              },
              component: () => import('../../views/creative/label/insight/AssetDetail.vue'),
            },
          ],
        },
        {
          path: 'top',
          meta: {
            name: 'Top Creatives',
            isBreadcrumb: 'false',
          },
          component: CommonIndex as unknown as RouteComponent,
          redirect: '/creative/analytics/top/creatives',
          children: [
            {
              path: 'creatives',
              meta: {
                name: 'Top Creatives',
                reportId: '04300101',
              },
              component: () => import('../../views/creative/top/creatives.vue'),
            },
            {
              path: 'creatives/labels',
              meta: {
                name: 'Labels',
                isHidePc: true, // 是否在菜单中展示
                reportId: '04310101',
              },
              component: () => import('../../views/creative/top/creatives-label.vue'),
            },
            {
              path: 'creatives/asset', // Top Creatives -> Asset Detail
              meta: {
                name: 'Asset Detail',
                isHidePc: true, // 是否在菜单中展示
                reportId: '04230101',
              },
              component: () => import('../../views/creative/label/insight/AssetDetail.vue'),
            },
          ],
        },
      ],
    },
    // {
    //   path: 'production',
    //   meta: {
    //     icon: 'house-export',
    //     name: 'Creative Production',
    //     title: 'Production',
    //     dir: true,
    //     isBreadcrumb: 'false',
    //   },
    //   component: CommonIndex as unknown as RouteComponent,
    //   children: [
    //     {
    //       path: 'tool',
    //       meta: {
    //         name: 'Creative Tools',
    //         title: 'Tools',
    //         url: genUrlHiddenParams('https://aix.intlgame.com/creative/tool'),
    //       },
    //       component: FallbackAix as unknown as RouteComponent,
    //     },
    //   ],
    // },
    {
      path: 'intelligence',
      meta: {
        icon: 'compute-monitor',
        name: 'Creative Intelligence',
        title: 'Intelligence',
        url: genUrlHiddenParams('https://aix.intlgame.com/creative/intelligence'),
      },
      component: FallbackAix as unknown as RouteComponent,
    },
    {
      path: 'report',
      meta: {
        icon: 'pic-search',
        name: 'Creative Report',
        title: 'Report',
        dir: true,
        isBreadcrumb: 'false',
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [
        {
          path: 'custom_report',
          meta: {
            name: 'Custom Report',
            showNewFunctionRed: true,
            reportId: '04090101',
          },
          component: () => import('../../views/creative/insight/report/index.vue'),
        },
        {
          path: 'weekly_top',
          meta: {
            name: 'Top Report',
            showNewFunctionRed: true,
            reportId: '04130101',
          },
          component: () => import('../../views/creative/dashboard/index.vue'),
        },
      ],
    },
    {
      path: 'ai_toolkit',
      meta: {
        name: 'AI Toolkit',
        icon: 'pic-search',
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [
        {
          path: 'advanced_video',
          meta: {
            name: 'Advanced Video',
            title: 'Advanced Video Search',
            breadcrumbTitle: BreadcrumbTitle({
              text: 'Advanced Video Search',
              icon: {
                name: 'error',
                size: '20',
              },
              content:
                'The Advanced Video Search is a tool to search for video clips using text queries or visual queries \
              (images or videos), it will retrieve video clips that match the best with the queries given. This tool is particularly\
               useful when producing videos as it speeds up the process of selecting raw footage to be edited later, this is \
               especially helpful when there is a large quantity of footage available. The tool uses OpenAI\'s CLIP model as \
               its backbone. CLIP compares similarity between images and text descriptions, we have extended that to videos as well.',
            }),
            isHover: true,
            reportId: '04100101',
          },
          component: () => import('../../views/creative/AIToolkit/AIClip/index.vue'),
        },
        // {
        //   path: 'text_video',
        //   meta: {
        //     name: 'Video',
        //     title: 'Text to Al Video',
        //     reportId: '04110101',
        //   },
        //   component: () => import('../../views/creative/toolkit/routes/al/detail/Video.vue'),
        // },
        {
          path: 'smart_copywriter',
          name: 'Creative Center / AI Toolkit / Smart Copywriter',
          meta: {
            name: 'Smart Copywriter',
            reportId: '02100101',
          },
          component: () => import('@/views/trade/ai_toolkit/Index.vue'),
        },
        {
          path: 'text_templates',
          meta: {
            name: 'Text Templates',
            title: 'Text to Templates',
            reportId: '04120101',
          },
          component: () => import('../../views/creative/AIToolkit/AITemplate/index.vue'),
        },
        {
          path: 'face_swap',
          meta: {
            name: 'Face Swapping',
            title: 'Face Swapping',
            reportId: '04130101',
          },
          component: () => import('../../views/creative/AIToolkit/FaceSwap/index.vue'),
        },
        {
          path: 'mouth_swap',
          meta: {
            name: 'Lip Sync',
            title: 'Lip Sync',
            reportId: '04140101',
          },
          component: () => import('../../views/creative/AIToolkit/MouthSwap/index.vue'),
        },
        {
          path: 'video_dub',
          meta: {
            name: 'Video Dubbing',
            title: 'Video Dubbing',
            reportId: '04150101',
          },
          component: () => import('../../views/creative/AIToolkit/VideoDub/index.vue'),
        },
      ],
    },
    {
      path: 'competitor',
      meta: {
        icon: 'search',
        name: 'Inspiration',
        isBreadcrumb: 'false',
        dir: true,
        // url: genUrlHiddenParams('https://aix.intlgame.com/intelligence/Creative/ChooseGames'),
        // url: genUrlHiddenParams('https://aix.intlgame.com/intelligence_project/Creative/ChooseGames'),
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [
        {
          path: 'competitor',
          meta: {
            name: 'Competitor',
            isBreadcrumb: 'false',
          },
          redirect: '/creative/competitor/competitor', // 这个是承载容器，通过强制跳转跳转到业务组件
          component: CommonIndex as unknown as RouteComponent,
          children: [
            {
              path: '',
              meta: {
                name: 'Competitor',
                reportId: '04140101',
              },
              // component: () => import('@'/views/intelligence/creative/competitor/Index.vue'),
              component: () => import('@/views/intelligence/creative/competitor/Index.vue'),
            },
            {
              path: 'addcompetitor',
              meta: {
                name: 'Add Game',
                title: 'Add Competitor Game',
                reportId: '04140201',
              },
              component: () => import('@/views/intelligence/creative/competitor/AddCompetitor.vue'),
            },
          ],
        },
        {
          path: 'overview',
          meta: {
            name: 'Creative Gallery',
            reportId: '04150101',
          },
          redirect: '/creative/competitor/overview', // 这个是承载容器，通过强制跳转跳转到业务组件
          component: CommonIndex as unknown as RouteComponent,
          children: [
            {
              path: '',
              meta: {
                name: 'Creative Gallery',
                reportId: '04150101',
              },
              component: () => import('@/views/intelligence/creative/overview/Index.vue'),
            },
            {
              path: 'analyze',
              meta: {
                name: 'Analyze',
                reportId: '04150201',
              },
              component: () => import('@/views/intelligence/creative/overview/analyze/index.vue'),
            },
          ],
        },
        {
          path: 'socialmeidavideo',
          meta: {
            name: 'Social Media Video',
            reportId: '04160101',
            isHideMobile: true,
          },
          component: () => import('@/views/intelligence/creative/social_media_video/Index.vue'),
        },
      ],
    },
  ],
};
