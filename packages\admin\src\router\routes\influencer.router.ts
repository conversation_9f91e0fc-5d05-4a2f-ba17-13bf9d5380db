import CommonIndex from '@/views/CommonIndex.vue';
import { RouteComponent } from 'vue-router';

export default {
  path: '/influencer',
  meta: {
    icon: 'crown',
    name: 'Influencer',
    title: 'Influencer',
    pageTitle: 'Influencer',
    desc: 'Influencer',
    level: 1,
    index: 8,
  },
  component: CommonIndex as unknown as RouteComponent,
  children: [
    {
      path: 'report',
      meta: {
        icon: 'dashboard',
        name: 'Report',
        dir: true,
        index: 1,
        isBreadcrumb: 'false',
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [
        {
          path: 'overview',
          meta: {
            icon: 'group',
            name: 'Overview',
            title: 'Overview',
            reportId: '08010101',
          },
          component: () => import('@/views/influencer/overview/index.vue'),
        },
        {
          path: 'campaign',
          meta: {
            icon: 'group',
            name: 'Campaign',
            title: 'Campaign',
            reportId: '08020101',
          },
          component: () => import('@/views/influencer/campaign/index.vue'),
        },
      ],
    },
    {
      path: 'campaign_setup',
      meta: {
        icon: 'setting',
        name: 'Campaign SetUp',
        title: 'Campaign Setup',
        reportId: '08030101',
        managementType: 'kol',
      },
      component: () => import('@/views/influencer/campaignSetUp/index.vue'),
    },
  ],
};
