import { genUrlHiddenParams } from 'common/utils/url';
import CommonIndex from '@/views/CommonIndex.vue';
import FallbackAix from '@/views/FallbackAix.vue';
import { RouteComponent } from 'vue-router';


export default {
  path: '/intelligence',
  meta: {
    icon: 'intelligence',
    name: 'Intelligence',
    desc: 'Market Insight',
    level: 1,
    index: 4,
  },
  component: CommonIndex as unknown as RouteComponent,
  children: [
    // {
    //   path: '',
    //   name: 'Intelligence_default',
    //   redirect: '/intelligence/report',
    // },
    {
      path: 'market',
      meta: {
        icon: 'group',
        name: 'Market',
        dir: true,
        isBreadcrumb: 'false',
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [
        {
          path: 'scale',
          meta: {
            icon: 'ads-management',
            name: 'Scale',
            reportId: '05010101',
            index: 0,
          },
          component: () => import('@/views/intelligence/market/scale/index.vue'),
        },
        {
          path: 'trend',
          meta: {
            icon: 'change',
            name: 'Trend',
            reportId: '05020101',
          },
          component: () => import('@/views/intelligence/market/trend/index.vue'),
        },
        {
          path: 'competitive',
          meta: {
            icon: 'monitor',
            name: 'Competitive',
            reportId: '05030101',
          },
          component: () => import('@/views/intelligence/market/competitive/index.vue'),
        },
        {
          path: 'channel',
          meta: {
            icon: 'tag',
            name: 'Channel',
            isBreadcrumb: 'false',
          },
          redirect: '/intelligence/market/channel', // 这个是承载容器，通过强制跳转跳转到业务组件
          component: CommonIndex as unknown as RouteComponent,
          children: [
            {
              path: '',
              meta: {
                name: 'Channel',
                reportId: '05040101',
                hide: true,
                index: 1,
              },
              component: () => import('@/views/intelligence/market/channel/index.vue'),
            },
            {
              path: 'admin',
              meta: {
                name: 'Admin',
                reportId: '05040201',
                hide: true,
              },
              component: () => import('@/views/intelligence/market/channel/admin/index.vue'),
            },
          ],
        },
        {
          path: 'os',
          meta: {
            icon: 'save-box',
            name: 'OS',
            reportId: '05050101',
          },
          component: () => import('@/views/intelligence/market/os/index.vue'),
        },
        // {
        //   path: 'cpi',
        //   meta: {
        //     icon: 'file-upload',
        //     name: 'CPI',
        //     reportId: '05060101',
        //     url: genUrlHiddenParams('https://aix.intlgame.com/intelligence_project/Market/MarketResults', {
        //       hideWaterMask: true,
        //       hideTopNav: true,
        //       hideLeftNav: true,
        //       hideInnerNav: false,
        //     }),
        //   },
        //   component: FallbackAix as unknown as RouteComponent,
        // },
      ],
    },
    {
      path: 'prediction',
      meta: {
        icon: 'question',
        name: 'Prediction',
        reportId: '05070101',
        url: genUrlHiddenParams('https://aix.intlgame.com/intelligence_project/Prediction/ChooseGames', {
          hideWaterMask: true,
          hideTopNav: true,
          hideLeftNav: true,
          hideInnerNav: false,
        }),
      },
      component: FallbackAix as unknown as RouteComponent,
    },
    {
      path: 'prediction',
      meta: {
        icon: 'box',
        title: 'Prediction',
        name: 'New Prediction',
        reportId: '05070101',
        dir: true,
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [
        {
          path: 'overview',
          meta: {
            name: 'Overview',
            reportId: '05180101',
            isBreadcrumb: 'false',
            hide: true,
          },
          component: () => import('@/views/intelligence/prediction/overview/index.vue'),
        },
        {
          path: 'analyze',
          meta: {
            name: 'Analyze',
            reportId: '05190101',
            hide: true,
          },
          component: () => import('@/views/intelligence/prediction/analyze/index.vue'),
        },
        {
          path: 'settings',
          meta: {
            name: 'Settings',
            reportId: '05200101',
            hide: true,
          },
          component: () => import('@/views/intelligence/prediction/addCompetitor/index.vue'),
        },
      ],
    },
    {
      path: 'creative',
      meta: {
        icon: 'search',
        name: 'Creative',
        isBreadcrumb: 'false',
        dir: true,
        reportId: '05080101',
        // url: genUrlHiddenParams('https://aix.intlgame.com/intelligence/Creative/ChooseGames'),
        // url: genUrlHiddenParams('https://aix.intlgame.com/intelligence_project/Creative/ChooseGames'),
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [
        {
          path: 'competitor',
          meta: {
            name: 'Competitor',
            isBreadcrumb: 'false',
          },
          redirect: '/intelligence/creative/competitor', // 这个是承载容器，通过强制跳转跳转到业务组件
          component: CommonIndex as unknown as RouteComponent,
          children: [
            {
              path: '',
              meta: {
                name: 'Competitor',
                reportId: '05110101',
              },
              component: () => import('@/views/intelligence/creative/competitor/Index.vue'),
            },
            {
              path: 'addcompetitor',
              meta: {
                name: 'Add Game',
                title: 'Add Competitor Game',
                reportId: '05110201',
              },
              component: () => import('@/views/intelligence/creative/competitor/AddCompetitor.vue'),
            },
          ],
        },
        {
          path: 'overview',
          meta: {
            name: 'Overview',
            reportId: '05120101',
          },
          redirect: '/intelligence/creative/overview', // 这个是承载容器，通过强制跳转跳转到业务组件
          component: CommonIndex as unknown as RouteComponent,
          children: [
            {
              path: '',
              meta: {
                name: 'Creative Gallery',
                reportId: '05120101',
              },
              component: () => import('@/views/intelligence/creative/overview/Index.vue'),
            },
            {
              path: 'analyze',
              meta: {
                name: 'Analyze',
                reportId: '05120201',
              },
              component: () => import('@/views/intelligence/creative/overview/analyze/index.vue'),
            },
          ],
        },
        {
          path: 'socialmeidavideo',
          meta: {
            name: 'Social Media Video',
            reportId: '05210101',
            isHideMobile: true,
          },
          component: () => import('@/views/intelligence/creative/social_media_video/Index.vue'),
        },
      ],
    },
    {
      path: 'user',
      meta: {
        icon: 'more-select',
        name: 'User',
        isBreadcrumb: 'false',
      },
      redirect: '/intelligence/user', // 这个是承载容器，通过强制跳转跳转到业务组件
      component: CommonIndex as unknown as RouteComponent,
      children: [
        {
          path: '',
          meta: {
            name: 'User',
            reportId: '05090101',
            hide: true,
            index: 6,
          },
          component: () => import('@/views/intelligence/user/index.vue'),
        },
        {
          path: 'admin',
          meta: {
            name: 'Admin',
            reportId: '05090201',
            hide: true,
          },
          component: () => import('@/views/intelligence/user/admin/index.vue'),
        },
      ],
    },
    {
      path: 'report',
      meta: {
        icon: 'more-file',
        name: 'Report',
        reportId: '05100101',
        url: genUrlHiddenParams('https://aix.intlgame.com/intelligence/report', {
          hideWaterMask: true,
          hideTopNav: true,
          hideLeftNav: true,
          hideInnerNav: false,
        }),
      },
      props: {
        type: 'iframe',
      },
      component: FallbackAix as unknown as RouteComponent,
    },
  ],
};
