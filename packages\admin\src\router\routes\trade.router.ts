import { genUrlHiddenParams } from 'common/utils/url';
import CommonIndex from '@/views/CommonIndex.vue';
import CommonFallAix from '@/views/CommonFallAix.vue';
import FallbackAix from '@/views/FallbackAix.vue';
import { RouteComponent, RouteRecordRaw } from 'vue-router';

export default {
  path: '/trade',
  meta: {
    name: 'Trading Desk',
    icon: 'td',
    desc: 'Campaign Manager',
    breadcrumbName: '',
    level: 1,
    index: 1,
  },
  component: CommonIndex as unknown as RouteComponent,
  children: [
    // {
    //   path: '',
    //   name: 'trade_default',
    //   redirect: '/trade/management/google',
    // },
    {
      path: 'management',
      meta: {
        icon: 'ads-management',
        name: 'Manage Ads',
        title: 'Ads Management',
        dir: true,
        isBreadcrumb: 'false',
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [
        {
          path: 'google',
          name: 'Trading Desk / Manage Ads / Google',
          meta: {
            name: 'Google',
            icon: 'google',
            url: genUrlHiddenParams('https://aix.intlgame.com/trade/pivot/google'),
            reportId: '02040101',
            media: 'Google',
            index: 0,
          },
          component: () => import('../../views/trade/ads_management/Index.vue'),
        },
        {
          path: 'google/create',
          name: 'Trading Desk / Manage Ads / Google / Create',
          meta: {
            name: '',
            reportId: '02040201',
            media: 'Google',
            index: 1,
          },
          component: () => import('../../views/trade/ads_creation/google/index.vue'),
        },
        {
          path: 'google_v1/create',
          name: 'Trading Desk / Manage Ads / Google / Create_v1',
          meta: {
            name: '',
            url: genUrlHiddenParams('https://aix.intlgame.com/trade/v2/google_ads'),
            reportId: '02040201',
            index: 1,
          },
          component: CommonFallAix as unknown as RouteComponent,
        },
        {
          path: 'facebook',
          name: 'Trading Desk / Manage Ads / Facebook',
          meta: {
            name: 'Facebook',
            icon: 'facebook',
            url: genUrlHiddenParams('https://aix.intlgame.com/trade/pivot/facebook'),
            reportId: '02050101',
            media: 'Facebook',
            index: 0,
          },
          component: () => import('../../views/trade/ads_management/Index.vue'),
        },
        {
          path: 'facebook/create',
          name: 'Trading Desk / Manage Ads / Facebook / Create',
          meta: {
            name: '',
            url: genUrlHiddenParams('https://test.aix.intlgame.com/trade/v2/facebook_ads'),
            reportId: '02050201',
            media: 'Facebook',
            index: 1,
          },
          component: () => import('../../views/trade/ads_creation/facebook/index.vue'),
        },
        {
          path: 'tiktok',
          name: 'Trading Desk / Manage Ads / TikTok',
          meta: {
            name: 'TikTok',
            icon: 'tiktok',
            reportId: '02060101',
            media: 'TikTok',
            index: 0,
          },
          component: () => import('../../views/trade/ads_management/Index.vue'),
        },
        {
          path: 'tiktok/create',
          name: 'Trading Desk / Manage Ads / TikTok / Create',
          meta: {
            name: '',
            reportId: '02060201',
            media: 'TikTok',
            index: 1,
          },
          component: () => import('../../views/trade/ads_creation/tiktok/index.vue'),
        },
        {
          path: 'twitter',
          name: 'Trading Desk / Manage Ads / Twitter',
          meta: {
            name: 'Twitter',
            icon: 'twitter',
            reportId: '02070101',
            media: 'Twitter',
            index: 0,
          },
          component: () => import('../../views/trade/ads_management/Index.vue'),
        },
        {
          path: 'twitter/create',
          name: 'Trading Desk / Manage Ads / Twitter / Create',
          meta: {
            name: '',
            reportId: '02070201',
            media: 'Twitter',
            index: 1,
          },
          component: () => import('../../views/trade/ads_creation/twitter/index.vue'),
        },
        {
          path: 'asa',
          name: 'Trading Desk / Manage Ads / ASA',
          meta: {
            name: 'Apple Search Ads',
            icon: 'apple',
            reportId: '02090101',
            media: 'ASA',
            index: 0,
          },
          component: () => import('../../views/trade/ads_management/Index.vue'),
        },
        {
          path: 'reddit',
          name: 'Trading Desk / Manage Ads / Reddit',
          meta: {
            name: 'Reddit',
            icon: 'apple',
            reportId: '02090101',
            media: 'Reddit',
            index: 0,
          },
          component: () => import('../../views/trade/ads_management/Index.vue'),
        },
      ],
    },
    {
      path: 'monitor',
      meta: {
        icon: 'monitor',
        name: 'Monitor',
        url: genUrlHiddenParams('https://aix.intlgame.com/trade/monitor'),
        reportId: '02020101',
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [
        {
          path: 'target',
          name: 'Trading Desk / Monitor / Target',
          meta: {
            title: 'Target',
            pageTitle: 'Target Management',
            reportId: '02020401',
          },
          component: () => import('@/views/trade/monitor/target/Index.vue'),
        },
        {
          path: 'rules',
          name: 'Trading Desk / Monitor / Rules',
          meta: {
            name: 'Rules',
            reportId: '02020301',
          },
          component: () => import('@/views/trade/monitor/rules/Index.vue'),
        },
        {
          path: 'setting',
          name: 'Trading Desk / Monitor / Setting',
          meta: {
            name: 'Setting',
            reportId: '02020201',
          },
          component: () => import('@/views/trade/monitor/setting/Index.vue'),
        },
        // {
        //   path: 'ai_optimization',
        //   name: 'Trading Desk / Monitor / AI Optimization',
        //   meta: {
        //     name: 'AI Optimization',
        //     reportId: '02020401',
        //   },
        //   component: () => import('@/views/trade/monitor/ai_optimization/Index.vue'),
        // },
      ],
    },
    {
      path: 'optimization',
      meta: {
        icon: 'recycle',
        name: 'Ads Optimization',
        title: 'Ads Optimization',
        dir: true,
        isBreadcrumb: 'false',
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [
        {
          path: 'autoplanning',
          meta: {
            icon: 'recycle',
            name: 'Autoplanning',
            url: 'https://api.aix.levelinfinite.com/api_v2/td/algo/autoplanning?game=demo',
          },
          props: {
            type: 'iframe',
          },
          component: FallbackAix as unknown as RouteComponent,
        },
        {
          path: 'automation',
          meta: {
            icon: 'recycle',
            name: 'Automation',
            url: genUrlHiddenParams('https://aix.intlgame.com/trade/v2/automation'),
          },
          component: CommonFallAix as unknown as RouteComponent,
        },
      ],
    },
    // {
    //   path: 'automation',
    //   meta: {
    //     icon: 'recycle',
    //     name: 'Automation',
    //     url: genUrlHiddenParams('https://aix.intlgame.com/trade/v2/automation'),
    //   },
    //   component: CommonFallAix as unknown as RouteComponent,
    // },
    {
      path: 'ai_toolkit',
      meta: {
        icon: 'ai-toolkit',
        name: 'AI Toolkit',
        title: 'AI Toolkit',
        dir: true,
        isBreadcrumb: 'false',
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [
        {
          path: 'smart_copywriter',
          name: 'Trading Desk / AI Toolkit / Smart Copywriter',
          meta: {
            name: 'Smart Copywriter',
            reportId: '02100101',
          },
          component: () => import('@/views/trade/ai_toolkit/Index.vue'),
        },
      ],
    },
    {
      path: 'attribution_tools',
      meta: {
        icon: 'ai-toolkit',
        name: 'Attribution Tools',
        dir: true,
        isBreadcrumb: false,
      },
      component: CommonIndex as unknown as RouteComponent,
      children: [
        {
          path: 'tracker_management',
          meta: {
            name: 'Tracker management',
            reportId: '02080101',
          },
          component: () => import('@/views/trade/attribution_tools/Index.vue'),
        },
      ],
    },
  ],
} satisfies RouteRecordRaw;
