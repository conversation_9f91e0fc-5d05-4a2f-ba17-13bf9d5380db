import CommonIndex from '@/views/CommonIndex.vue';

export default [
  {
    path: '/user',
    name: '个人信息',
    meta: {
      icon: 'user',
    },
    component: CommonIndex,
    children: [
      {
        path: '/user',
        name: 'User',
        meta: {
          sidebar: 'false',
          isBreadcrumb: 'false',
        },
        component: () => import('../../views/user/Info.vue'),
      },
      {
        path: '/user/auth',
        name: 'UserAuth',
        meta: {
          sidebar: 'false',
          isBreadcrumb: 'false',
        },
        component: () => import('../../views/user/Auth.vue'),
      },
    ],
  },
];
