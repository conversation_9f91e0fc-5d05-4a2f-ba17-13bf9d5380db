import type { IIAudienceForm } from './type';

export const INIT_FORM: IIAudienceForm = {
  createby: 'modeling', // modeling,rules,sql
  target: 'new_install',
  isCombine: 0,
  // child_target在页面是的值分别用new_target和re_target表示
  // child_target: '',
  newTarget: '',
  reTarget: '',
  media: 'Google',
  adAccountId: '',
  appToken: '',
  eventToken: '',
  audienceType: 'event', // 根据media不同，选项不同
  // 不同created_by下，update_frequency的下拉列表内容不同，所以用{created}_update_frequency表示；
  // update_frequency: '',
  modelingUpdateFrequency: '',
  rulesUpdateFrequency: '',
  sqlUpdateFrequency: '',
  name: '',
  os: 'iOS',
  modelName: '',
  percentScoreLower: 0,
  percentScore: 0,
  tableName: '',
  // rules: {}, // 存放国家、时间、语言等字段，modeling和rules用到
  country: [],
  language: [],
  installDate: [],
  registerDate: [],
  activeDate: [],
  uninstallDate: [],
  excludeActiveDate: [],
  purchaseTimes: [],
  // purchase_times_start: 0,
  // purchase_times_end: 0,
  purchaseAmount: [],
  // purchase_amount_start: 0,
  // purchase_amount_end: 0,
  level: [], // level由level_start和level_end构成
  level_start: 0,
  level_end: 0,
  profile: '',
  // 实验开关
  openTest: 0, // 默认为1;0代表关闭,1代表打开
  testParam: 10, // 默认为10,,取值为(0,50]的正整数
  // event value开关 默认打开
  openEventValue: 1,
  remark: '', // 新增audience description
  openUserTtl: 1, // 打开/关闭duration，默认打开
  userTtl: '', // 1~180正整数
  idType: '', // id type
  subIdType: '',
  blockAlarms: 0, // 是否告警
  // --- userRange 表单项验证需要
  userRangeFormItem: [],
  // Update frequency 表单项验证需要
  updateFrequencyFormItem: {
    updateFrequency: '',
    createby: '',
  },
  tiktokStdEvent: '',
};

export const SHOW_VALUE_MEDIA = ['Google', 'Facebook', 'Appsflyer', 'Adjust'];

export const AUDIENCE_OVERVIEW_TABLE_ROW_STATE = 'aix_audience_overview_table_row';

export const AUDIENCE_OVERVIEW_TABLE_OPERATION_TYPE = 'aix_audience_overview_table_operation_type';
export const AUDIENCE_OVERVIEW_LIST_DEMO = 'aix_audience_overview_list_demo';
export const AUDIENCE_OVERVIEW_BLACK_ID_LIST_DEMO = 'aix_audience_overview_black_id_list_demo';
export const AUDIENCE_OVERVIEW_REMARK_MAP_DEMO = 'aix_audience_overview_remark_map_demo';


export const ALL_ID_TYPE_MAP = {
  adsId: { label: 'Advertising ID', value: 'ads_id' },
  appInstanceId: { label: 'GA4 ID', value: 'ga4_id' },
  clickId: { label: 'Click ID', value: 'click_id' },
  clientId: { label: 'Client ID', value: 'client_id' },
  hashEmail: { label: 'Hashed Email', value: 'hashed_email' },
};

export const ID_TYPE_BY_CONDITION = {
  Google: {
    mobile: {
      event: {
        rules: [ALL_ID_TYPE_MAP.adsId],
        others: [ALL_ID_TYPE_MAP.adsId].concat([ALL_ID_TYPE_MAP.appInstanceId]),
      },
      others: {
        rules: [ALL_ID_TYPE_MAP.adsId],
        others: [ALL_ID_TYPE_MAP.adsId],
      },
    },
    pc: {
      event: {
        rules: [ALL_ID_TYPE_MAP.adsId],
        others: [ALL_ID_TYPE_MAP.appInstanceId].concat([ALL_ID_TYPE_MAP.clickId]),
      },
      others: {
        rules: [],
        others: [ALL_ID_TYPE_MAP.hashEmail],
      },
    },
  },
  Facebook: {
    mobile: {
      event: {
        rules: [ALL_ID_TYPE_MAP.adsId],
        others: [ALL_ID_TYPE_MAP.adsId],
      },
      others: {
        rules: [ALL_ID_TYPE_MAP.adsId],
        others: [ALL_ID_TYPE_MAP.adsId],
      },
    },
    pc: {
      event: {
        rules: [],
        others: [ALL_ID_TYPE_MAP.clickId],
      },
      others: {
        rules: [],
        others: [ALL_ID_TYPE_MAP.hashEmail],
      },
    },
  },
  TikTok: {
    mobile: {
      event: {
        rules: [],
        others: [],
      },
      others: {
        rules: [],
        others: [],
      },
    },
    pc: {
      event: {
        rules: [],
        others: [ALL_ID_TYPE_MAP.clickId],
      },
      others: {
        rules: [],
        others: [ALL_ID_TYPE_MAP.hashEmail],
      },
    },
  },
  Twitter: {
    mobile: {
      event: {
        rules: [],
        others: [],
      },
      others: {
        rules: [],
        others: [],
      },
    },
    pc: {
      event: {
        rules: [],
        others: [ALL_ID_TYPE_MAP.clickId],
      },
      others: {
        rules: [],
        others: [],
      },
    },
  },
};

export const ALL_ID_TYPE_LIST = [
  { label: 'Advertising ID', value: 'ads_id' },
  { label: 'Instance ID', value: 'ga4_id' },
  { label: 'Click ID', value: 'click_id' },
  { label: 'Client ID', value: 'client_id' },
  { label: 'Hashed Email', value: 'hashed_email' },
];

export const ALL_SUB_ID_TYPE_MAP = {
  appInstanceId: { label: 'Instance ID', value: 'app_instance_id' },
  wbraId: { label: 'Wbraid', value: 'wbraid' },
  gbraId: { label: 'Gbraid', value: 'gbraid' },
  gclId: { label: 'Gclid', value: 'gclid' },
  mixed: { label: 'Mixed', value: 'mixed' },
  fbc: { label: 'Fbc', value: 'fbc' },
  clientId: { label: 'Client ID', value: 'client_id' },
};


export const LOG_REQ_PARAMS = {
  audienceId: '',
  type: 'upload',
  pageIndex: undefined, // 第一次不能传页码，否则列配置拉不到
  pageSize: 20,
  pageTotal: 0,
  status: undefined,
};
