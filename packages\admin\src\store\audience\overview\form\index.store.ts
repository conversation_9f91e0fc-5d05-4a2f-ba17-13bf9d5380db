import { defineStore, storeToRefs } from 'pinia';
import { ref, reactive, computed } from 'vue';
import { STORE_KEY } from '@/config/config';
import { useAixAudienceOverviewStore } from '../index.store';
import {
  getFormConfService,
  updateTableNote as updateTableNoteService,
} from 'common/service/audience/overview/index';
import type { IOptionItem, IOptionItemArguments, IIAudienceForm, TOperationType } from '../type';
import { cloneDeep, orderBy, has, isArray } from 'lodash-es';
import { useLoading } from 'common/compose/loading';
import { transformOptions, transformOptionsArguments } from '../utils/transform';
import { useAixAudienceOverviewFormQueryStringParamsStore } from './queryStringParams.store';
import { initFormData } from '../utils/formInit';
import { getIdTypeList, getSubIdTypeList, getAdvancedList } from '../utils/get';
import { INIT_FORM } from '../const';
import { getUserName } from 'common/utils/auth';
import type {
  IFilterOptionItem,
  IAudienceFormOptionAudienceTypeList,
  IAudienceFormOptionFrequencyListObj,
  IAudienceFormOptionMediaList,
  IAudienceFormOptionRootAccountObj,
  IAudienceFormOptionAccountObj,
  IAudienceFormOptionCustomTableList,
  IIAudienceFormOptionAdvancedActionList,
  IIAudienceFormOptionAppTokenObj,
  IAudienceTable,
  IAudienceFormOptionProfileValueList,
  IAudienceFormOptionSendAudienceObj,
  IIAudienceFormOptionAudienceServerMap,
  IAudienceFormOptionPlatform,
  IStandardEvent,
} from 'common/service/audience/overview/type';
import { useGlobalGameStore } from '@/store/global/game.store';
import { optionData } from '../utils/mock/mock_option';

export const useAixAudienceOverviewFormStore = defineStore(STORE_KEY.AUDIENCE.OVERVIEW_FORM, () => {
  const { audienceTableRowState, operationTypeState } = storeToRefs(useAixAudienceOverviewStore());
  const { operationTypeUrl } = useAixAudienceOverviewFormQueryStringParamsStore();
  const { isLoading: isReqLoading, hideLoading: hideReqLoading, showLoading: showReqLoading } = useLoading();

  const { isLoading: isPageLoading, hideLoading: hidePageLoading, showLoading: showPageLoading } = useLoading();
  // 表单项 下拉框 New Install
  const newInstallList = ref<IOptionItem[]>([]);
  // 表单项 Re-attribution
  const reList = ref<IOptionItem[]>([]);
  // 表单项 单选按钮组 Created by
  const createdByList = ref<IFilterOptionItem[]>([]);
  // 表单项 下拉框  Model Name
  const modelNameList = ref<IOptionItemArguments[]>([]);
  // 表单项 下拉框 User Range
  const scoreList = ref<IOptionItem[]>([]);
  // 表单项 单选按钮组 Audience Type
  const audienceTypeList = ref<IAudienceFormOptionAudienceTypeList>({});
  // 表单项 下拉框 Update frequency 要 根据modelName作为key从frequencyListObj中取对应的数组
  const frequencyListObj = ref<IAudienceFormOptionFrequencyListObj>({});
  // 表单项 下拉框Country
  const locationList = ref<IOptionItem[]>([]);
  // 表单项 下拉框language
  const languageList = ref<IOptionItem[]>([]);
  const hasTest = ref<string[]>([]);
  // 表单项  单选按钮组 Use for which Media
  const mediaList = ref<IAudienceFormOptionMediaList[]>([]);
  const requencyList = ref<IOptionItem[]>([]);
  // --
  const rootAccountObj = ref<IAudienceFormOptionRootAccountObj>({});
  const accountObj = ref<IAudienceFormOptionAccountObj>({});
  const hasCombineList = ref<string[]>([]);
  // 表单项 下拉框 Table name
  const customTableList = ref<IAudienceFormOptionCustomTableList[]>([]);
  // 表单项 APP Advanced Action
  const advancedActionList = ref<IIAudienceFormOptionAdvancedActionList[]>([]);
  // App Token
  const appTokenObj = ref<IIAudienceFormOptionAppTokenObj>({});

  const notHasPopup = ref<string[]>([]);

  const hasProfile = ref<string[]>([]);

  const hasAdvancedAction = ref<string[]>([]);

  const profileValueList = ref<IAudienceFormOptionProfileValueList>({});

  const sendAudienceAbj = ref<IAudienceFormOptionSendAudienceObj>({});

  const audienceServerMap = ref<IIAudienceFormOptionAudienceServerMap>({});

  const osMapObj = ref<IAudienceFormOptionPlatform>({});

  // 表单的数据
  const formData = reactive<IIAudienceForm>(cloneDeep(INIT_FORM));
  // 操作类型
  const operationType = ref<TOperationType>('add');
  // // 从audience/overview 页面表格带过来的某一行数据
  // const tableDetail = ref<IAudienceTable | null>();

  const standardEvent = ref<IStandardEvent>({});

  const advancedList = computed(() => advancedActionList.value.filter(item => getAdvancedList(item.value, formData)));

  // 操作类型，是添加还是编辑
  const isAdd = computed(() => operationType.value === 'add' || operationTypeUrl === 'add');

  // 新增时的类型
  const sendType = ref<string>('ads');

  // appAdvancedActionRef表单ref
  const appAdvancedActionRef = ref();

  const idTypeList = computed(() => getIdTypeList(
    formData.os,
    formData.createby, formData.audienceType, formData.media,
  ));

  const subIdTypeList = computed(() => getSubIdTypeList(
    formData.os, formData.createby, formData.audienceType,
    formData.media, formData.idType,
  ));

  const audienceTypeItem = computed(() => (isArray(audienceTypeList.value[formData.media])
    ? audienceTypeList.value[formData.media]
    : []
  ).find(item => item.value === formData.audienceType));

  async function getConf() {
    const { isDemoGame, isMobileGame } = useGlobalGameStore();
    const { gameCode } = storeToRefs(useGlobalGameStore());
    const {
      new_install_list: resNewInstallList = [],
      re_list: resReList = [],
      created_by_list: resCreatedByList = [],
      model_name_list: resModelNameList = [],
      score_list: resScoreList = [],
      audience_type_list: resAudienceTypeList = {},
      frequency_list_obj: resFrequencyListObj = {},
      location_list: resLocationList = [],
      has_test: resHasTest = [],
      media_list: resMediaList = [],
      root_account_obj: resRootAccountObj = {},
      account_obj: resAccountObj = {},
      frequency_list: resFrequencyList = [],
      has_combine_list: resHasCombineList = [],
      custom_table_list: resCustomTableList = [],
      advanced_action_list: resAdvancedActionList = [],
      app_token_obj: resAppTokenObj = {},
      not_has_popup: resNotHasPopup = [],
      language_list: resLanguageList = [],
      has_advanced_action: resHasAdvancedAction = [],
      has_profile: resHasProfile = [],
      profile_value_list: resProfileValueList = {},
      send_audience_obj: resSendAudienceObj = {},
      audience_server_map: resAudienceServerMap = {},
      os_map_obj: resOsMapObj = {},
      standard_event: standardEventObj = {},
    } = isDemoGame() ? (optionData as any) : await getFormConfService({ game_code: gameCode.value });
    // 赋值
    newInstallList.value = transformOptions(resNewInstallList);
    reList.value = transformOptions(resReList);
    createdByList.value = resCreatedByList;
    modelNameList.value = transformOptionsArguments(orderBy(resModelNameList, ['text'], ['asc'])); // a-z排序
    scoreList.value = transformOptions(resScoreList);
    audienceTypeList.value = resAudienceTypeList;
    frequencyListObj.value = resFrequencyListObj;
    locationList.value = transformOptions(resLocationList);
    hasTest.value = resHasTest;
    if (isMobileGame()) {
      mediaList.value = (resMediaList as IAudienceFormOptionMediaList[]).filter(item => !['TikTok', 'Twitter', 'Reddit'].includes(item.value));
    } else {
      mediaList.value = (resMediaList as IAudienceFormOptionMediaList[]).filter(item => !['Appsflyer', 'Adjust'].includes(item.value));
    }
    rootAccountObj.value = resRootAccountObj;
    accountObj.value = resAccountObj;
    requencyList.value = transformOptions(resFrequencyList);
    hasCombineList.value = resHasCombineList;
    customTableList.value = resCustomTableList;
    advancedActionList.value = resAdvancedActionList;
    appTokenObj.value = resAppTokenObj;
    notHasPopup.value = resNotHasPopup;
    languageList.value = transformOptions(resLanguageList);
    hasAdvancedAction.value = resHasAdvancedAction;
    hasProfile.value = resHasProfile;
    profileValueList.value = resProfileValueList;
    sendAudienceAbj.value = resSendAudienceObj;
    audienceServerMap.value = resAudienceServerMap;
    osMapObj.value = resOsMapObj;
    standardEvent.value = standardEventObj;
  }

  async function updateTableNote(id: number, remark: string) {
    showReqLoading();
    const { gameCode } = storeToRefs(useGlobalGameStore());
    const resCustomTableList = await updateTableNoteService({
      id,
      remark,
      updater: await getUserName(),
      game_code: gameCode.value,
    });
    hideReqLoading();
    customTableList.value = resCustomTableList;
  }

  function setOperationType(val: TOperationType) {
    operationType.value = val;
  }

  function setSendType(val: string) {
    sendType.value = val;
  }

  /**
   * 页面进来之后，有两件事情
   *  1.拉接口
   *  2. 如果tableDetail中有数据的话，用它的数据初始化页面
   */
  async function init() {
    showPageLoading();
    await getConf();
    initFormData(audienceTableRowState.value as IAudienceTable);
    setOperationType(operationTypeState.value);
    setSendType('ads');
    hidePageLoading();
  }

  // 重置form数据
  function resetFormData() {
    Object.keys(cloneDeep(INIT_FORM)).forEach((key: string) => {
      if (has(formData, key)) {
        formData[key] = cloneDeep(INIT_FORM)[key];
      }
    });
  }

  return {
    init,
    newInstallList,
    createdByList,
    modelNameList,
    reList,
    scoreList,
    audienceTypeList,
    frequencyListObj,
    locationList,
    hasTest,
    mediaList,
    formData,
    isAdd,
    notHasPopup,
    rootAccountObj,
    accountObj,
    requencyList,
    hasCombineList,
    customTableList,
    advancedActionList,
    isReqLoading,
    isPageLoading,
    appTokenObj,
    operationType,
    languageList,
    hasAdvancedAction,
    hasProfile,
    profileValueList,
    advancedList,
    sendAudienceAbj,
    audienceServerMap,
    osMapObj,
    appAdvancedActionRef,
    idTypeList,
    subIdTypeList,
    sendType,
    audienceTypeItem,
    setOperationType,
    updateTableNote,
    setSendType,
    resetFormData,
    showReqLoading,
    hideReqLoading,
    standardEvent,
  };
});
