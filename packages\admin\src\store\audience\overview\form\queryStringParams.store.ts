import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { useUrlSearchParams } from '@vueuse/core';

// url中携带的参数 都在 这里
export const useAixAudienceOverviewFormQueryStringParamsStore = defineStore(
  STORE_KEY.AUDIENCE.OVERVIEW_FORM_QUERY_STRING_PARAMS,
  () => {
    // url 中的os
    // const osUrl = computed<string>(() => useUrlSearchParams<{ os: string }>('history').os);
    // // url中携带account_id，相应account_id选中且不可修改
    // const accountIdUrl = computed<string>(() => useUrlSearchParams<{ account_id: string }>('history').account_id);
    const {
      os: osUrl,
      account_id: accountIdUrl,
      audience_type: audienceTypeUrl,
      inner_campaign_id: innerCampaignIdUrl,
      media_campaign_id: mediaCampaignIdUrl,
      inner_ad_group_id: innerAdGroupIdUrl,
      media_ad_group_id: mediaAdGroupIdUrl,
      cache_status: cacheStatusUrl,
      temp_audience_id: tempAudienceIdUrl,
      operation_type: operationTypeUrl,
      from: fromUrl = 'campaign',
      media: mediaUrl,
    } = useUrlSearchParams<{
      os: string,
      account_id: string,
      audience_type: string,
      inner_campaign_id: string,
      media_campaign_id: string,
      inner_ad_group_id: string,
      media_ad_group_id: string,
      cache_status: string,
      temp_audience_id: string,
      operation_type: string,
      from: string,
      media: string,
    }>('history');

    return {
      osUrl,
      accountIdUrl,
      audienceTypeUrl,
      innerCampaignIdUrl,
      mediaCampaignIdUrl,
      innerAdGroupIdUrl,
      mediaAdGroupIdUrl,
      cacheStatusUrl,
      tempAudienceIdUrl,
      operationTypeUrl,
      fromUrl,
      mediaUrl,
    };
  },
);
