import { defineStore, storeToRefs } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { getSwitchValue } from '../utils/get';
import { useAixAudienceOverviewFormStore } from './index.store';
import type { IUpdateFrequencyFormItem } from '../type';

/**
 * 专门用于修改index.store 中formData的数据
 */
export const useAixAudienceOverviewFormUpdateStore = defineStore(STORE_KEY.AUDIENCE.OVERVIEW_FORM_UPDATE, () => {
  const { formData } = storeToRefs(useAixAudienceOverviewFormStore());

  const setMedia = (val: string) => formData.value.media = val;

  const setAppToken = (val: string) => formData.value.appToken = val;

  const setEventToken = (val: string) => formData.value.eventToken = val;

  const setCreateby  = (val: string) => formData.value.createby = val;

  const setTarget = (val: string) => formData.value.target = val;

  const setNewTarget = (val: string) => formData.value.newTarget = val;

  const setReTarget = (val: string) => formData.value.reTarget = val;

  const setModelName = (val: string) => formData.value.modelName = val;

  const setPercentScoreLower = (val: number) => formData.value.percentScoreLower = val;

  const setPercentScore = (val: number) => formData.value.percentScore = val;

  const setAudienceType = (val: string) => formData.value.audienceType = val;

  const setOpenEventValue = (val: any) => formData.value.openEventValue = getSwitchValue(val);

  const setModelingUpdateFrequency = (val: string) => formData.value.modelingUpdateFrequency = val;

  const setOpenUserTtl = (val: any) => formData.value.openUserTtl = getSwitchValue(val);

  const setUserTtl = (val: string | number) => formData.value.userTtl = val;

  const setCountry = (val: string[]) => formData.value.country = val;

  const setLanguage = (val: string[]) => formData.value.language = val;

  const setOs = (val: string) => formData.value.os = val;

  const setIdType = (val: string) => formData.value.idType = val;

  const setSubIdType = (val: string) => formData.value.subIdType = val;

  const setName = (val: string) => formData.value.name = val;

  const setRemark = (val: string) => formData.value.remark = val;

  const setOpenTest = (val: any) => formData.value.openTest = getSwitchValue(val);;

  const setTestParam = (val: number) => formData.value.testParam = val;

  const setAdAccountId = (val: string) => formData.value.adAccountId = val;

  const setBlockAlarms = (val: any) => formData.value.blockAlarms = getSwitchValue(val);

  const setRulesUpdateFrequency = (val: string) => formData.value.rulesUpdateFrequency = val;

  const setSqlUpdateFrequency = (val: string) => formData.value.sqlUpdateFrequency = val;

  const setIsCombine = (val: number) => formData.value.isCombine = val;

  const setTableName = (val: string) => formData.value.tableName = val;

  const setInstallDate = (val: string[]) => formData.value.installDate = val;

  const setRegisterDate = (val: string[]) => formData.value.registerDate = val;

  const setActiveDate = (val: string[]) => formData.value.activeDate = val;

  const setUninstallDate = (val: string[]) => formData.value.uninstallDate = val;

  const setExcludeActiveDate = (val: string[]) => formData.value.excludeActiveDate = val;

  const setProfile = (val: string) => formData.value.profile = val;

  const setPurchaseTimes = (val: number[]) => formData.value.purchaseTimes = val;

  const setPurchaseAmount = (val: number[]) => formData.value.purchaseAmount = val;

  const setTiktokStdEvent = (val: string) => formData.value.tiktokStdEvent = val;

  // ----- 表单验证需要的额外字段 -----
  const setUserRangeFormItem =  (val: number[]) => formData.value.userRangeFormItem = val;
  const setUpdateFrequencyFormItem = (val: IUpdateFrequencyFormItem) => {
    formData.value.updateFrequencyFormItem.createby = val.createby;
    formData.value.updateFrequencyFormItem.updateFrequency = val.updateFrequency;
  };

  return {
    setMedia,
    setAppToken,
    setEventToken,
    setCreateby,
    setTarget,
    setNewTarget,
    setReTarget,
    setModelName,
    setPercentScoreLower,
    setPercentScore,
    setAudienceType,
    setOpenEventValue,
    setModelingUpdateFrequency,
    setOpenUserTtl,
    setUserTtl,
    setCountry,
    setLanguage,
    setOs,
    setIdType,
    setSubIdType,
    setName,
    setRemark,
    setOpenTest,
    setTestParam,
    setAdAccountId,
    setBlockAlarms,
    setRulesUpdateFrequency,
    setSqlUpdateFrequency,
    setIsCombine,
    setTableName,
    setInstallDate,
    setRegisterDate,
    setActiveDate,
    setUninstallDate,
    setExcludeActiveDate,
    setProfile,
    setPurchaseTimes,
    setPurchaseAmount,
    setUserRangeFormItem,
    setUpdateFrequencyFormItem,
    setTiktokStdEvent,
  };
});
