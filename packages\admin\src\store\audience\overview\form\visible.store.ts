import { defineStore, storeToRefs } from 'pinia';
import { computed } from 'vue';
import { STORE_KEY } from '@/config/config';
import { useAixAudienceOverviewFormStore } from './index.store';
import { useAixAudienceOverviewStore } from '../index.store';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useAixAudienceOverviewFormQueryStringParamsStore } from './queryStringParams.store';
import { updateFrequencyModelValueMap } from '@/views/audience/overview/form/const';
import type { IAudienceTable } from 'common/service/audience/overview/type';
import { SHOW_VALUE_MEDIA } from '../const';
import { getPcStatus } from '../utils/get';

export const useAixAudienceOverviewFormVisible = defineStore(STORE_KEY.AUDIENCE.OVERVIEW_FORM_VISIBLE, () => {
  const { formData, modelNameList, audienceTypeItem, isAdd,
    hasTest, notHasPopup, hasCombineList, idTypeList, subIdTypeList,
    sendType } = storeToRefs(useAixAudienceOverviewFormStore());
  const { audienceTableRowState } = storeToRefs(useAixAudienceOverviewStore());
  const { gameCode } = storeToRefs(useGlobalGameStore());
  const { tempAudienceIdUrl } = useAixAudienceOverviewFormQueryStringParamsStore();

  // add时是否显示弹窗选择创建方式
  const isShowPopup = computed(() => (
    !notHasPopup.value.includes(gameCode.value) && isAdd.value && !tempAudienceIdUrl
  ));

  // add且有弹窗且sendType为创建campaign时，需要带上temp_audience_id
  const isToCampaign = computed(() => isShowPopup.value && sendType.value === 'campaign');

  const isShowEventValue = computed<boolean>(() => (
    SHOW_VALUE_MEDIA.includes(formData.value.media)
      && formData.value.audienceType === 'event' && formData.value.createby === 'modeling'
      && modelNameList.value.some(({ value = '', enable_value = 0 }) => value === formData.value.modelName && enable_value)
  ));

  // const audienceTypeItem = computed(() => (
  //   (isArray(audienceTypeList.value[formData.value.media])
  //     ? audienceTypeList.value[formData.value.media] : []).find(item => item.value === formData.value.audienceType)
  // ));

  const isShowDuration = computed(() => (
    formData.value.media === 'Facebook' && audienceTypeItem?.value?.text === 'Audience'
    && updateFrequencyModelValueMap()[formData.value.createby].value === 'daily'
  ));

  // 禁止关闭duration(修改中且支持duration且支持testing且同时配置了dynamic能力和实验testing的能力)
  // openTest userTtl 不能从formdata里面取得从audienceTableRowState里面取
  const isDisDuration = computed(() => (!isAdd.value && isShowDuration.value
    && (
      (audienceTableRowState.value as IAudienceTable)?.open_test
        && (audienceTableRowState.value as IAudienceTable)?.user_ttl
    )));

  // 是否显示ID Type表单项
  // const isShowIdType = computed(() => (
  //   ['Google'].includes(formData.value.media)
  //   && formData.value.audienceType === 'event' && ['iOS', 'Android'].includes(formData.value.os)
  // ));
  const isShowIdType = computed(() => idTypeList.value.length > 0);
  // 是否显示Sub ID Type表单项
  const isShowSubIdType = computed(() => subIdTypeList.value.length > 0);


  // 是否显示Test setting表单项
  const isShowTesting = computed(() => (
    hasTest.value.includes(gameCode.value) && audienceTypeItem.value?.text === 'Audience'
  ));

  // 禁止修改testing(修改中且支持testing且实验关闭状态且曾经打开过；修改中且支持testing且同时配置了dynamic能力和实验testing的能力)
  // const isDisTesting = !isAdd && isShowTesting && ((!open_test && first_open_time) || (open_test && state_user_ttl));
  const isDisTesting = computed(() => (
    !isAdd.value && isShowTesting.value
    && (
      (!(audienceTableRowState.value as IAudienceTable)?.open_test
        && (audienceTableRowState.value as IAudienceTable)?.first_open_time
      )
      || (
        (audienceTableRowState.value as IAudienceTable)?.open_test
        && (audienceTableRowState.value as IAudienceTable)?.user_ttl
      )
    )
  ));
  // media为Appsflyer/Adjust时，name做特殊处理
  const isSpecialName = computed(() => ['Appsflyer', 'Adjust'].includes(formData.value.media));

  // 记住：需要判断media和created by改变时，若选中的是pc但此时pc是禁选的，需要修改os的值
  // const isDisabledPc = computed(() => !['Google', 'Facebook'].includes(formData.value.media));
  const isDisabledPc = computed(() => getPcStatus(formData.value.media, formData.value.audienceType));

  const isShowCombineOrNot = computed(() => hasCombineList.value.includes(gameCode.value) && formData.value.media === 'Google');

  const isShowTiktokStdEvent = computed(() => formData.value.media === 'TikTok' && formData.value.audienceType === 'event' && formData.value.os === 'Web');

  return {
    isShowEventValue,
    isShowDuration,
    isDisDuration,
    isShowTesting,
    isShowIdType,
    isShowSubIdType,
    isDisabledPc,
    isDisTesting,
    isSpecialName,
    isShowPopup,
    isShowCombineOrNot,
    isToCampaign,
    isShowTiktokStdEvent,
  };
});
