import { defineStore, storeToRefs } from 'pinia';
import { computed, ref, toRaw } from 'vue';
import { cloneDeep, isString } from 'lodash-es';
import { useSessionStorage, useStorage } from '@vueuse/core';
import { STORE_KEY } from '@/config/config';
import {
  getInitData,
  getClickData,
  updateAudience as updateAudienceService,
  updateStatus as updateStatusService,
  delAudience as deleteAudienceService,
  getRestartPeriod as getRestartPeriodService,
  restartTask as restartTaskService,
} from 'common/service/audience/overview';
import type { IColumns, IAudienceFilterOption, IAudienceTable } from 'common/service/audience/overview/type';
import { ITbas, IOptionItem } from './type';
import { useLoading } from 'common/compose/loading';
import { useTips } from 'common/compose/tips';
import { useAuthStageStore } from '@/store/global/auth.store';
import {
  AUDIENCE_OVERVIEW_TABLE_ROW_STATE,
  AUDIENCE_OVERVIEW_TABLE_OPERATION_TYPE,
  AUDIENCE_OVERVIEW_LIST_DEMO,
  AUDIENCE_OVERVIEW_BLACK_ID_LIST_DEMO,
  AUDIENCE_OVERVIEW_REMARK_MAP_DEMO,
} from './const';
import { getMockOverviewData } from './utils/mock/mock_overview';
import { useGlobalGameStore } from '@/store/global/game.store';

export const useAixAudienceOverviewStore = defineStore(STORE_KEY.AUDIENCE.OVERVIEW, () => {
  const { success } = useTips();
  const { isLoading, hideLoading, showLoading } = useLoading();
  const { isLoading: isDialogLoading, hideLoading: hideDialogLoading, showLoading: showDialogLoading } = useLoading();
  const { validGameRbac } = storeToRefs(useAuthStageStore());
  // 选项卡及其表格数据
  const tabList = ref<ITbas[]>([]);
  // 列
  const columns = ref<IColumns[]>([]);
  // 列筛选项
  const filterOptions = ref<IAudienceFilterOption>();

  const periodList = ref<IOptionItem[]>([]);

  // 点击 config或者 copy时需要把那一行的数据带到编辑页面
  const audienceTableRowState = useStorage<IAudienceTable | {}>(AUDIENCE_OVERVIEW_TABLE_ROW_STATE, {});

  // 是新增还是 更新
  const operationTypeState = useStorage<'add' | 'show'>(AUDIENCE_OVERVIEW_TABLE_OPERATION_TYPE, 'add');

  // 判断有没有编辑的权限
  const isEditRbac = computed(() => validGameRbac.value.some(item => item.action === 'edit'));

  // demo游戏时用户新增的存储在sessionStorage中的audience列表
  const demoListInSeesionByAdd = useSessionStorage<IAudienceTable[]>(AUDIENCE_OVERVIEW_LIST_DEMO, []);
  // demo游戏时用户已删除的audience的id列表
  const demoGameBlackAudienceIdList = useSessionStorage<string[]>(AUDIENCE_OVERVIEW_BLACK_ID_LIST_DEMO, []);
  // 表格数据，在demo游戏下使用
  const demoGameTableData = ref<IAudienceTable[]>([]);
  // demo游戏下，audience备注信息map
  const demoGameRemarkMap = useSessionStorage<Record<string, string>>(AUDIENCE_OVERVIEW_REMARK_MAP_DEMO, {});
  // 初始化
  async function initData() {
    try {
      const { isDemoGame } = useGlobalGameStore();
      const { gameCode } = storeToRefs(useGlobalGameStore());
      if (isDemoGame()) {
        // demo游戏不显示tab选项卡，只显示一个表格就行了
        const mockOverviewData = getMockOverviewData(gameCode.value);
        demoGameTableData.value = demoListInSeesionByAdd.value.concat(mockOverviewData.table as any).map(item => ({
          ...item,
          // 设置更新后的备注
          remark: isString(demoGameRemarkMap.value[item.id]) ? demoGameRemarkMap.value[item.id] : item.remark,
        }));
        columns.value = mockOverviewData.columnsOption;
        filterOptions.value = mockOverviewData.filterOption;
        return;
      }
      showLoading();
      const {
        table: tableData,
        tableTabs = [],
        columnsOption = [],
        filterOption,
      } =  await getInitData({ game_code: gameCode.value, source_type: 'user_list' });
      hideLoading();
      // 假如是demo游戏，还需要加上storage中存储的
      // const table = isDemoGame() ? demoListInSeesionByAdd.value.concat(tableInService) : tableInService;
      columns.value = columnsOption;
      filterOptions.value = filterOption;
      tabList.value = tableTabs.map((item: any) => ({
        ...item,
        tableData,
      }));
    } catch (e: any) {
      hideLoading();
      console.log(e);
    }
  }
  // 切换选项卡时拉数据
  async function getDataByType(type: string) {
    showLoading();
    const { gameCode } = storeToRefs(useGlobalGameStore());
    const { table = [] } = await getClickData({ game_code: gameCode.value, source_type: type });
    tabList.value = tabList.value.map(item => ({
      ...item,
      tableData: type === item.type ? table : item.tableData,
    }));
    hideLoading();
  }

  // 行内编辑
  async function updateAudience(params: { audience_id: string; remark: string }) {
    const { isDemoGame } = useGlobalGameStore();
    if (isDemoGame()) {
      demoGameRemarkMap.value[params.audience_id] = params.remark;
      return updateAudienceInDemoGame(params.audience_id);
    }
    showLoading();
    await updateAudienceService(params);
    initData();
  }
  // demo游戏下行内编辑
  function updateAudienceInDemoGame(id: string) {
    demoGameTableData.value = demoGameTableData.value.map((item) => {
      const newRemark = (
        (item.id === id && isString(demoGameRemarkMap.value[id])) ? demoGameRemarkMap.value[id] : item.remark
      );
      return {
        ...item,
        remark: newRemark,
      };
    });
  }

  // 更新状态
  async function updateStatus(row: IAudienceTable) {
    try {
      showLoading();
      await updateStatusService({
        audience_id: row.id,
        status: row.status === 'Paused' ? 'Pending' : 'Paused',
      });
      success(`${row.name} has ${row.status === 'Paused' ? 'resumed' : 'stopped'} data updates and synchronization with the ad channels.`);
      initData();
    } catch (e: any) {
      hideLoading();
      console.log(e);
    }
  }

  // 删除
  async function deleteAudience(row: IAudienceTable) {
    try {
      showLoading();
      const { gameCode } = storeToRefs(useGlobalGameStore());
      await deleteAudienceService({
        audience_ids: [row.id],
        game_code: gameCode.value,
      });
      // await deleteAudienceService(row.id);
      initData();
    } catch (e: any) {
      hideLoading();
      console.log(e);
    }
  }
  // 删除，在demo游戏时
  function deleteAudienceByDemoGame(id: string) {
    demoGameBlackAudienceIdList.value.push(id);
  }

  async function getRestartPeriod(row: IAudienceTable) {
    showDialogLoading();
    periodList.value = await getRestartPeriodService(row.id);
    hideDialogLoading();
  }

  async function restartTask(id: string, name: string, periodList: string[]) {
    await restartTaskService({
      id,
      period: periodList.join(','),
    });
    success(`[${name}]  [${periodList.slice(0, 3).join(';')}${
      periodList.length > 3 ? '...' : ''
    }]数据正在上传中，可通过operation-log查看上传情况。`);
  }

  // 切换tab时
  async function init() {
    await initData();
    setAudienceTableRowState({});
    setOperationTypeState('add');
  }

  function setAudienceTableRowState(val: IAudienceTable | {}) {
    // audienceTableRowState.value = val === null ? val : { ...val };
    audienceTableRowState.value = cloneDeep(toRaw(val));
  }

  function setOperationTypeState(val: 'add' | 'show') {
    operationTypeState.value = val;
  }

  return {
    init,
    getDataByType,
    updateAudience,
    updateStatus,
    deleteAudience,
    getRestartPeriod,
    restartTask,
    setAudienceTableRowState,
    setOperationTypeState,
    isDialogLoading,
    isLoading,
    tabList,
    columns,
    filterOptions,
    periodList,
    audienceTableRowState,
    operationTypeState,
    isEditRbac,
    demoListInSeesionByAdd, // demo游戏时新增的audience列表
    demoGameTableData, // demo游戏时的audience列表
    demoGameBlackAudienceIdList, // demo游戏已经删除的audience的id
    deleteAudienceByDemoGame, // 删除，在demo游戏时
    demoGameRemarkMap, // 在demo游戏下， audience的备注信息
  };
});
