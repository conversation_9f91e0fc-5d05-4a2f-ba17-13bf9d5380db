import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { ref, reactive } from 'vue';
import {
  getLog as getLogService,
  restartTask as restartTaskService,
  setAsfailed as setAsfailedService,
} from 'common/service/audience/overview/index';
import type { ILogTable, IColumns, IFilterOptionItem } from 'common/service/audience/overview/type';
import { useRouter } from 'vue-router';
import { isString, isArray, cloneDeep } from 'lodash-es';
import { useLoading } from 'common/compose/loading';
import { useTips } from 'common/compose/tips';
import type { ILogReqParams } from './type';
import { useGlobalGameStore } from '@/store/global/game.store';
import { logData } from './utils/mock/mock_log';
import { LOG_REQ_PARAMS } from './const';

export const useAixAudienceOverviewLogStore = defineStore(STORE_KEY.AUDIENCE.OVERVIEW_LOG, () => {
  const { success } = useTips();
  const { isLoading, hideLoading, showLoading } = useLoading();
  const logList = ref<ILogTable[]>([]);
  const logColumns = ref<IColumns[]>([]);

  const logOptions = reactive<{ statusList: IFilterOptionItem[]; typeList: IFilterOptionItem[] }>({
    statusList: [],
    typeList: [],
  });

  const params = reactive<ILogReqParams>(cloneDeep(LOG_REQ_PARAMS));

  // 拉接口
  async function getLogList() {
    showLoading();
    const { isDemoGame } = useGlobalGameStore();
    const res = isDemoGame()
      ? logData
      : await getLogService({
        audience_id: params.audienceId,
        type: params.type,
        page: params.pageIndex,
        page_size: params.pageSize,
        status: params.status,
      });
    hideLoading();
    params.pageTotal = res.total;
    logList.value = res.table;
    if (isArray(res.log_columns)) {
      logColumns.value = res.log_columns;
    }
    if (res.log_options) {
      logOptions.statusList = res.log_options.status_list;
      logOptions.typeList = res.log_options.type_list;
    }
  }

  // 分页
  function setPage(pageIndex: number, pageSzie: number) {
    params.pageIndex = pageSzie === params.pageSize ? pageIndex : 1;
    params.pageSize = pageSzie;
    getLogList();
  }

  // 筛选
  function setFilter(type: string, status: string) {
    params.status = status;
    params.type = type;
    getLogList();
  }

  async function init() {
    // 从 地址栏拿id 然后通过id调接口
    const router = useRouter();
    const queryParams = router.currentRoute.value.query;
    if (isString(queryParams.id)) {
      params.audienceId = queryParams.id;
      getLogList();
    }
    console.log('router', queryParams);
    // 先初始化参数
    console.log('init');
  }

  async function restartTask(row: ILogTable) {
    try {
      await restartTaskService({
        instance_id: row.task_id,
        restart_type: 'log',
      });
      success(`[${row.audience_name}]  [${row.period}]数据正在上传中。`);
      getLogList();
    } catch (e: any) {
      console.log(e);
    }
  }

  async function setAsfailed(taskId: string) {
    try {
      await setAsfailedService(taskId);
      getLogList();
    } catch (e: any) {
      console.log(e);
    }
  }

  async function resetReqParams() {
    Object.keys(LOG_REQ_PARAMS).forEach((key) => {
      (params as any)[key] = LOG_REQ_PARAMS[key as keyof typeof LOG_REQ_PARAMS];
    });
  }

  return {
    init,
    setPage,
    setFilter,
    restartTask,
    isLoading,
    params,
    logList,
    logColumns,
    logOptions,
    setAsfailed,
    resetReqParams,
  };
});
