
import type { IAudienceTab, IAudienceTable } from 'common/service/audience/overview/type';
import type { StringKeyAnyValueObject } from 'common/types/report';
export interface ITbas extends IAudienceTab {
  tableData: IAudienceTable[],
}

export interface IOptionItem {
  label: string,
  value: string,
}

export interface IOptionItemArguments extends IOptionItem, StringKeyAnyValueObject {
}

export interface ILogReqParams {
  audienceId: string,
  type: string,
  pageIndex?: number,
  pageSize: number,
  pageTotal: number,
  status?: string,
}

export type TOperationType = 'add' | 'show';

export interface IUpdateFrequencyFormItem {
  updateFrequency: string,
  createby: string,
}

export interface IIAudienceForm extends StringKeyAnyValueObject {
  media: string,
  appToken: string,
  eventToken: string,
  createby:  string,
  target: string,
  newTarget: string,
  reTarget: string,
  modelName: string,
  percentScoreLower: number,
  percentScore: number,
  audienceType: string,
  openEventValue: number,
  modelingUpdateFrequency: string,
  openUserTtl: number,
  userTtl: string | number,
  openTest: number,
  country: string[],
  language: string[],
  os: string,
  idType: string,
  subIdType: string,
  name: string,
  remark: string,
  testParam: number,
  adAccountId: string,
  blockAlarms: number,
  rulesUpdateFrequency: string,
  sqlUpdateFrequency: string,
  isCombine: number,
  tableName: string,
  installDate: string[],
  registerDate: string[],
  activeDate: string[],
  uninstallDate: string[],
  excludeActiveDate: string[],
  profile: string,
  purchaseTimes: number[],
  purchaseAmount: number[],
  // 一下字段，仅为表单验证需要
  userRangeFormItem: number[],
  updateFrequencyFormItem: IUpdateFrequencyFormItem,
  tiktokStdEvent: string,
}

