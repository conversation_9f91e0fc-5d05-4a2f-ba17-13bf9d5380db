import { useAixAudienceOverviewFormUpdateStore } from '../form/update.store';

export function getUpdateFormFnMap() {
  const {
    setMedia,
    setAppToken,
    setEventToken,
    setCreateby,
    setTarget,
    setNewTarget,
    setReTarget,
    setModelName,
    setPercentScoreLower,
    setPercentScore,
    setAudienceType,
    setOpenEventValue,
    setModelingUpdateFrequency,
    setOpenUserTtl,
    setUserTtl,
    setCountry,
    setLanguage,
    setOs,
    setIdType,
    setSubIdType,
    setName,
    setRemark,
    setOpenTest,
    setTestParam,
    setAdAccountId,
    setBlockAlarms,
    setRulesUpdateFrequency,
    setSqlUpdateFrequency,
    setIsCombine,
    setTableName,
    setInstallDate,
    setRegisterDate,
    setActiveDate,
    setUninstallDate,
    setExcludeActiveDate,
    setProfile,
    setPurchaseTimes,
    setPurchaseAmount,
    setTiktokStdEvent,
  } = useAixAudienceOverviewFormUpdateStore();
  const camel: Record<string, Function> = {
    media: setMedia,
    appToken: setAppToken,
    eventToken: setEventToken,
    createby: setCreateby,
    target: setTarget,
    newTarget: setNewTarget,
    reTarget: setReTarget,
    modelName: setModelName,
    percentScoreLower: setPercentScoreLower,
    percentScore: setPercentScore,
    audienceType: setAudienceType,
    openEventValue: setOpenEventValue,
    modelingUpdateFrequency: setModelingUpdateFrequency,
    openUserTtl: setOpenUserTtl,
    userTtl: setUserTtl,
    country: setCountry,
    language: setLanguage,
    os: setOs,
    idType: setIdType,
    subIdType: setSubIdType,
    name: setName,
    remark: setRemark,
    openTest: setOpenTest,
    testParam: setTestParam,
    adAccountId: setAdAccountId,
    blockAlarms: setBlockAlarms,
    rulesUpdateFrequency: setRulesUpdateFrequency,
    sqlUpdateFrequency: setSqlUpdateFrequency,
    isCombine: setIsCombine,
    tableName: setTableName,
    installDate: setInstallDate,
    registerDate: setRegisterDate,
    activeDate: setActiveDate,
    uninstallDate: setUninstallDate,
    excludeActiveDate: setExcludeActiveDate,
    profile: setProfile,
    purchaseTimes: setPurchaseTimes,
    purchaseAmount: setPurchaseAmount,
    tiktokStdEvent: setTiktokStdEvent,
  };
  const snake: Record<string, Function> = {
    media: setMedia,
    app_token: setAppToken,
    event_token: setEventToken,
    createby: setCreateby,
    target: setTarget,
    new_target: setNewTarget,
    re_target: setReTarget,
    model_name: setModelName,
    percent_score_lower: setPercentScoreLower,
    percent_score: setPercentScore,
    audience_type: setAudienceType,
    open_event_value: setOpenEventValue,
    modeling_update_frequency: setModelingUpdateFrequency,
    open_user_ttl: setOpenUserTtl,
    user_ttl: setUserTtl,
    country: setCountry,
    language: setLanguage,
    os: setOs,
    id_type: setIdType,
    sub_id_type: setSubIdType,
    name: setName,
    remark: setRemark,
    open_test: setOpenTest,
    test_param: setTestParam,
    ad_account_id: setAdAccountId,
    block_alarms: setBlockAlarms,
    rules_update_frequency: setRulesUpdateFrequency,
    sql_update_frequency: setSqlUpdateFrequency,
    is_combine: setIsCombine,
    table_name: setTableName,
    install_date: setInstallDate,
    register_date: setRegisterDate,
    active_date: setActiveDate,
    uninstall_date: setUninstallDate,
    exclude_active_date: setExcludeActiveDate,
    profile: setProfile,
    purchase_times: setPurchaseTimes,
    purchase_amount: setPurchaseAmount,
    tiktok_std_event: setTiktokStdEvent,
  };
  return {
    camel,
    snake,
  };
}

