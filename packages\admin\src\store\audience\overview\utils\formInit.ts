import { has, isArray, isEmpty, cloneDeep } from 'lodash-es';
import { useAixAudienceOverviewFormUpdateStore } from '../form/update.store';
import { useAixAudienceOverviewFormQueryStringParamsStore } from '../form/queryStringParams.store';
import { useAixAudienceOverviewFormStore } from '../form/index.store';
import { useGlobalGameStore } from '@/store/global/game.store';
import { storeToRefs } from 'pinia';
import { getUpdateFormFnMap } from  './batchUpdateForm';
import dayjs from 'dayjs';
import type { IAudienceTable } from 'common/service/audience/overview/type';

const genDateArr = (num: number) => {
  if (num >= 0) {
    return [];
  }
  const desSTNum = num === - 1 ? num : num + 1;
  return [
    dayjs().subtract(Math.abs(desSTNum), 'days').
      startOf('d')
      .format('YYYY-MM-DD'),
    num === - 1
      ? dayjs().subtract(Math.abs(desSTNum), 'days')
        .startOf('d')
        .format('YYYY-MM-DD')
      : dayjs()
        .endOf('d')
        .format('YYYY-MM-DD'),
  ];
};


function initFormDataByExtinfo(extInfo: any) {
  const { audienceServerMap } = storeToRefs(useAixAudienceOverviewFormStore());
  const { camel }  = getUpdateFormFnMap();
  const { setMedia, setAudienceType, setModelName,
    setPercentScore, setCreateby, setCountry, setOs, setInstallDate,
    setRegisterDate, setActiveDate, setExcludeActiveDate } = useAixAudienceOverviewFormUpdateStore();
  /**
   *   0318 表示是从launcher页面跳转到此页面中
    optimize_goal: 0-空1-活跃向优化2-付费向优化
    optimize_retarget:1-newinstallretention%2-newinstallpurchase3-reattributionreattribution
    media_type:1-GG
    created_by: 1-model2-rule-based
    high_type: 1-highact2-highmodel3-highattribution
    high_percent:
    audience_type: 1-event
    frequency: 频率1-daily
    country: all 代表所有
    os: 'Android'
    install_range:"<0负数代表前x天"
    install_register:"<0负数代表前x天"
    install_active: "<0负数代表前x天"
    install_ex_active:"<0负数代表前x天"
    */
  const {
    media_type: mediaType = '',
    created_by: createdBy = '', high_type: highType = '',
    high_percent: highPercent = 0, audience_type: audiencetype = '', frequency = '',
    country = [], os = '',
    install_range: installRange = 0, install_register: installRegister = 0,
    install_active: installActive = 0, install_ex_active: installExActive = 0,
  } = extInfo;
  const {
    media_type: serverMedia,
    audience_type: serverAT, frequency: serverFrequency,
    created_by: serverCreatedBy, high_type: serverHT, os: serverOs = {},
  } = audienceServerMap.value || {};

  const newCreateby = serverCreatedBy[createdBy] || 'rules';
  // 更新
  // setTarget(target);
  setMedia(serverMedia[mediaType] || 'Google');
  setAudienceType(serverAT[audiencetype] || '');
  setModelName(serverHT[highType] || '');
  setPercentScore(highPercent * 100);
  setCreateby(newCreateby);
  setCountry(country || []);
  setOs(serverOs[os] || 'All');
  setInstallDate(genDateArr(installRange));
  setRegisterDate(genDateArr(installRegister));
  setActiveDate(genDateArr(installActive));
  setExcludeActiveDate(genDateArr(installExActive));
  // if (`${targetCode}Target` as string === 'newTarget') {
  //   setNewTarget(childTarget);
  // }
  // if (`${targetCode}Target` as string  === 'reTarget') {
  //   setReTarget(childTarget);
  // }
  if (camel[`${newCreateby}UpdateFrequency`]) {
    camel[`${newCreateby}UpdateFrequency`](serverFrequency[frequency] || '');
  }
}

function initFormDataByDetail(detail: IAudienceTable) {
  const { snake }  = getUpdateFormFnMap();
  const { setAudienceType, setInstallDate,
    setRegisterDate, setActiveDate, setExcludeActiveDate, setUninstallDate,
    setNewTarget, setReTarget, setOpenUserTtl, setUserTtl,
    setProfile, setLanguage, setPurchaseTimes, setPurchaseAmount } = useAixAudienceOverviewFormUpdateStore();
  const tempDetail = cloneDeep(detail);
  tempDetail[`${detail.createby}_update_frequency`] = detail.update_frequency;
  // 批量是初始化表单
  Object.keys(snake).forEach((key) => {
    if (has(tempDetail, key)) {
      snake[key](tempDetail[key]);
    }
  });
  setAudienceType(detail.type);
  setOpenUserTtl(String(detail.user_ttl).length > 0);
  setUserTtl(String(detail.user_ttl).length > 0 ? Number(detail.user_ttl) : '');
  // global字段处理
  if (detail.target === 'new_install') {
    setNewTarget(detail.child_target);
  } else {
    setReTarget(detail.child_target);
  }
  // createby=rules处理
  if (detail.createby === 'rules') {
    setProfile(detail.profile ?? '');
    // newFormData.profile = profile;
    setLanguage(detail.language || []);
    const { purchase_times: purchaseTimes = [], purchase_amount: purchaseAmount,
      install_date: installDate, uninstall_date: uninstallDate,
      active_date: activeDate, register_date: registerDate,
      exclude_active_date: excludeActiveDate } = detail.rules || {};
    if (isArray(installDate) && installDate.length === 2) {
      setInstallDate(installDate);
    }
    if (isArray(uninstallDate) && uninstallDate.length === 2) {
      setUninstallDate(uninstallDate);
    }
    if (isArray(activeDate) && activeDate.length === 2) {
      setActiveDate(activeDate);
    }
    if (isArray(registerDate) && registerDate.length ===  2) {
      setRegisterDate(registerDate);
    }
    if (isArray(excludeActiveDate) && excludeActiveDate.length === 2) {
      setExcludeActiveDate(excludeActiveDate);
    }
    if (isArray(purchaseTimes) && purchaseTimes.length === 2) {
      setPurchaseTimes(purchaseTimes);
    }
    if (isArray(purchaseAmount) && purchaseAmount.length === 2) {
      setPurchaseAmount(purchaseAmount);
    }
  }
}

export function initFormData(detail: IAudienceTable) {
  console.log('detail', detail);
  const { osMapObj, audienceTypeList, formData,
    appTokenObj, locationList, languageList } = storeToRefs(useAixAudienceOverviewFormStore());
  const { setMedia, setAudienceType, setCountry, setOs, setLanguage,
    setAdAccountId, setAppToken } = useAixAudienceOverviewFormUpdateStore();

  const { audienceTypeUrl, osUrl, accountIdUrl, mediaUrl } = useAixAudienceOverviewFormQueryStringParamsStore();

  /** *add by euphro for pc game start */
  const { isPcGame } = useGlobalGameStore();
  if (isPcGame()) {
    setOs('Web');
  }
  /** *add by euphro for pc game end */

  if (has(detail, 'ext_info')) {
    initFormDataByExtinfo(detail.ext_info || {});
  } else if (has(detail, 'name')) {
    initFormDataByDetail(detail);
  } else {
    if (!isEmpty(osUrl)) {
      // 广告创建页面传来的os不一定能匹配上值，使用配置的映射取值
      if (Object.keys(osMapObj.value).some(key => osMapObj.value[key].includes(osUrl))) {
        setOs(osUrl);
      }
    }
    if (!isEmpty(accountIdUrl)) {
      setAdAccountId(accountIdUrl);
    }
    if (!isEmpty(mediaUrl)) {
      setMedia(mediaUrl);
    }
    if (!isEmpty(audienceTypeUrl)) {
      const curAudienceType = (audienceTypeList.value[formData.value.media] || [])
        .filter(item => audienceTypeUrl === item.text.toLocaleLowerCase());
      setAudienceType(curAudienceType[0]?.value || '');
    }
  }
  setAppToken((appTokenObj.value[detail.os || 'iOS']) || '');
  if (isArray(detail?.country) && detail.country[0] === 'all') {
    setCountry(locationList.value.map(item => item.value));
  }
  if (isArray(detail?.language) && detail.language[0] === 'all') {
    setLanguage(languageList.value.map(item => item.value));
  }
}

