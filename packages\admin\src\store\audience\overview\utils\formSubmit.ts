import type { StringKeyAnyValueObject } from 'common/types/report';
import type { IIAudienceForm } from '../type';
import { updateFrequencyModelValueMap } from '@/views/audience/overview/form/const';
import { useAixAudienceOverviewFormVisible } from '../form/visible.store';
import { useAixAudienceOverviewFormStore } from '../form/index.store';
import { useAixAudienceOverviewStore } from '@/store/audience/overview/index.store';
import { storeToRefs } from 'pinia';
import { random } from 'lodash-es';
import {
  updateAudience as updateAudienceService,
  addAudience as addAudienceService,
} from 'common/service/audience/overview/index';
import type { IAudienceTable } from 'common/service/audience/overview/type';
import { useGlobalGameStore } from '@/store/global/game.store';
import { v4 as uuidv4 } from 'uuid';
import { getUserName } from 'common/utils/auth';
// import { AUDIENCE_OVERVIEW_LIST_DEMO } from '../const';

function getCreateParamsByCreateby(form: IIAudienceForm, countryInner: string[]) {
  const {
    createby,
    modelName,
    percentScore,
    isCombine,
    tableName,
    installDate,
    registerDate,
    activeDate,
    uninstallDate,
    excludeActiveDate,
    profile,
    purchaseTimes,
    purchaseAmount,
    percentScoreLower,
  } = form;
  const params: StringKeyAnyValueObject = {};
  // 当created_by为modeling时, 添加mode_name，percent_score字段,rules字段，rule为对象，属性为country
  if (createby === 'modeling') {
    params.model_name = modelName;
    params.percent_score = percentScore;
    params.percent_score_lower = percentScoreLower;
    params.rules = { country: countryInner };
  }
  // 当created_by为sql时, 添加table_name字段
  if (createby === 'sql') {
    params.is_combine = isCombine;
    params.table_name = tableName;
  }
  // 当created_by为rules时,添加rules对象，里面install_date, register_date, active_date,uninstall_date,
  // exclude_active_date,level_end,profile等对象
  if (createby === 'rules') {
    params.rules = {};
    params.rules.country = countryInner;
    // createParams.rules.language = language;
    if (installDate.length === 2) {
      params.rules.install_date = installDate;
    }
    if (registerDate.length === 2) {
      params.rules.register_date = registerDate;
    }
    if (activeDate.length === 2) {
      params.rules.active_date = activeDate;
    }
    if (uninstallDate.length === 2) {
      params.rules.uninstall_date = uninstallDate;
    }
    if (excludeActiveDate.length === 2) {
      params.rules.exclude_active_date = excludeActiveDate;
    }
    if (profile) {
      params.rules.profile = profile;
    }
    // APP_ADVANCED_ACTION需要改造成[start,end]
    // const keys = advancedList.map(({ value = '' }) => value);
    // APP_ADVANCED_ACTION.forEach(key => {
    //   // 页面上显示的选项传给后台具体值，否则为[]
    //   new_payload['rules'][key] = keys.includes(key) ? [param[`${key}_start`], param[`${key}_end`]] : [];
    // })
    params.rules.purchase_times = purchaseTimes;
    params.rules.purchase_amount = purchaseAmount;
  }
  return params;
}

// 新增Audience
export async function createAudience(form: IIAudienceForm) {
  const {
    // target,
    // newTarget,
    // reTarget,
    createby,
    media,
    adAccountId,
    audienceType,
    os,
    name,
    remark,
    blockAlarms,
    openTest,
    testParam,
    country,
    appToken,
    eventToken,
    openEventValue,
    openUserTtl,
    userTtl,
    idType,
    subIdType,
    tiktokStdEvent,
  } = form;

  const {
    isShowTesting, isShowEventValue,
    isShowDuration, isShowIdType, isShowSubIdType, isShowTiktokStdEvent,
  } = storeToRefs(useAixAudienceOverviewFormVisible());
  const { locationList } = storeToRefs(useAixAudienceOverviewFormStore());
  // install_date, register_date, active_date, uninstall_date, exclude_active_date,
  // modeling、rules、sql类型下的公共字段
  const createParams: StringKeyAnyValueObject = {
    // target,
    // child_target: target === 'new_install' ? newTarget : reTarget,
    update_frequency: updateFrequencyModelValueMap()[createby].value || '',
    media,
    ad_account_id: adAccountId,
    createby,
    audience_type: audienceType,
    name,
    os,
    remark,
    block_alarms: blockAlarms,
  };
  // 若出现add时有temp_audience_id且url中也有temp_audience_id，以url中为主
  // if (tempAudienceId && operationTypeUrl) {
  //   createParams.temp_audience_id = tempAudienceId;
  // }
  // 支持testing且open_test为打开状态才需要传给后台
  if (isShowTesting.value && openTest === 1) {
    createParams.open_test = 1;
    createParams.test_param = testParam;
  }
  let countryInner = country;
  // country和language默认传all
  // if (country && (country.length === location_list.length || country.length === 0)) country = ['all'];
  if (country.length === locationList.value.length || country.length === 0) {
    countryInner = ['all'];
  }
  // if (language && (language.length === language_list.length || language.length === 0)) language = ['all'];
  // media为adjust，ad_account_id为空,有app_token与event_token字段
  if (media === 'Adjust') {
    createParams.ad_account_id = '';
    createParams.app_token = appToken;
    createParams.event_token = eventToken;
  }
  // media为Appsflyer，没有ad_account选项，默认传空
  if (media === 'Appsflyer') {
    createParams.ad_account_id = '';
  }
  Object.assign(createParams, getCreateParamsByCreateby(form, countryInner));
  // isShowEventValue为true代表显示开关，此时可以需要传给后台
  if (isShowEventValue.value) {
    createParams.open_event_value = openEventValue;
  }
  if (isShowDuration.value) {
    // user_ttl为打开状态时传1~180，关闭时传0
    createParams.user_ttl = openUserTtl === 1 ? Number(userTtl) : 0;
  }
  if (isShowIdType.value) {
    createParams.id_type = idType;
  }
  if (isShowSubIdType.value) {
    createParams.sub_id_type = subIdType;
  }
  if (isShowTiktokStdEvent.value) {
    createParams.tiktok_std_event = tiktokStdEvent;
  }

  // 假如是demo游戏，新增之后需将数据写入到 sessionStorage中。
  const { isDemoGame } = useGlobalGameStore();
  const { gameCode } = storeToRefs(useGlobalGameStore());
  if (isDemoGame()) {
    const { demoListInSeesionByAdd } = storeToRefs(useAixAudienceOverviewStore());
    createParams.id = `audience_${uuidv4()}`;
    const createbyMap: StringKeyAnyValueObject = {
      modeling: 'Modeling',
      rules: 'Rule-based',
      sql: 'Custom Table',
    };
    demoListInSeesionByAdd.value.unshift({
      ...createParams,
      creator: 'tester',
      upload_size: random(1000, 1000000),
      status: 'Eligible',
      createby_text: createbyMap[createParams.createby],
      type_text: createParams.audience_type === 'event' ? 'Event' : 'Audience',
      channel_size: `${random(1000, 100000)}-${random(100000, 900000)}`,
    } as any);
    return true;
  }
  return await addAudienceService({
    ...createParams,
    game_code: gameCode.value,
    creator: await getUserName(),
  });
}

// 更新Audience
export async function updateAudience(form: IIAudienceForm) {
  const {
    percentScore,
    percentScoreLower,
    remark,
    createby,
    installDate = [],
    activeDate = [],
    uninstallDate = [],
    registerDate = [],
    excludeActiveDate = [],
    purchaseTimes,
    purchaseAmount,
    openTest,
    testParam,
    openEventValue,
    openUserTtl,
    userTtl,
    blockAlarms,
    modelName,
    tableName,
  } = form;
  const { isShowTesting, isShowEventValue, isShowDuration } = storeToRefs(useAixAudienceOverviewFormVisible());

  const { audienceTableRowState } = storeToRefs(useAixAudienceOverviewStore());
  let updateParams: StringKeyAnyValueObject = {};
  // 不同created_by中能修改的参数不同
  // @zhiming 4.19 frequency和audience name先不让修改
  if (createby === 'modeling') {
    updateParams = {
      percent_score: percentScore,
      percent_score_lower: percentScoreLower,
      remark,
      model_name: modelName,
    };
  }
  if (createby === 'sql') {
    updateParams = { remark, table_name: tableName };
  }
  console.log('form', form);
  if (createby === 'rules') {
    updateParams.install_date = installDate.length === 2 ? installDate : [];
    updateParams.register_date = registerDate.length === 2 ? registerDate : [];
    updateParams.active_date = activeDate.length === 2 ? activeDate : [];
    updateParams.uninstall_date = uninstallDate.length === 2 ? uninstallDate : [];
    updateParams.exclude_active_date = excludeActiveDate.length === 2 ? excludeActiveDate : [];
    updateParams.purchase_times = purchaseTimes;
    updateParams.purchase_amount = purchaseAmount;
    updateParams.remark = remark;
  }
  if (isShowTesting.value) {
    updateParams.open_test = openTest;
    updateParams.test_param = openTest === 1 ? testParam : 0;
  }
  // isShowEventValue为true代表显示开关，此时可以修改
  if (isShowEventValue.value) {
    updateParams.open_event_value = openEventValue;
  }
  if (isShowDuration.value) {
    // user_ttl为打开状态时传1~180，关闭时传0
    updateParams.user_ttl = openUserTtl === 1 ? Number(userTtl) : 0;
  }
  return await updateAudienceService({
    audience_id: (audienceTableRowState.value as IAudienceTable).id,
    block_alarms: blockAlarms,
    ...updateParams,
  });
}

