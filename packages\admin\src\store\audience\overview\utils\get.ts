import type { IOptionItem, IIAudienceForm } from '../type';
import { ALL_ID_TYPE_MAP, ALL_SUB_ID_TYPE_MAP, ID_TYPE_BY_CONDITION } from '../const';
import { useAixAudienceOverviewFormVisible } from '../form/visible.store';
import { storeToRefs } from 'pinia';
import { isArray, isString, camelCase } from 'lodash-es';

function getIdTypeListByMedia(media: string, os: string, createby: string, audienceType: string) {
  const osKey = ['Android', 'iOS'].includes(os) ? 'mobile' : os === 'Web' ? 'pc' : 'mobile';
  const audienceTypeKey = audienceType === 'event' ? 'event' : 'others';
  const createByKey = createby === 'rules' ? 'rules' : 'others';
  return (ID_TYPE_BY_CONDITION as any)[media][osKey][audienceTypeKey][createByKey];
}

// idtype 单选按钮组
export function getIdTypeList(os: string, createby: string, audienceType: string, media: string): IOptionItem[] {
  if (['Google', 'Facebook', 'TikTok', 'Twitter'].includes(media)) {
    return getIdTypeListByMedia(media, os, createby, audienceType);
  }
  if (['Appsflyer', 'Adjust'].includes(media)) {
    if (audienceType === 'event' && ['Android', 'iOS'].includes(os)) {
      return [ALL_ID_TYPE_MAP.adsId];
    }
  }
  return [];
}

export function getSubIdTypeList(
  os: string,
  createby: string,
  audienceType: string,
  media: string,
  idType: string,
): IOptionItem[] {
  const result: IOptionItem[] = [];
  if (media === 'Google') {
    if (audienceType === 'event' && idType === 'ga4_id' && ['Android', 'iOS'].includes(os) && createby !== 'rules') {
      result.push(ALL_SUB_ID_TYPE_MAP.appInstanceId);
    }
    if (
      audienceType === 'event'
      && idType === 'click_id'
      && ['Android', 'iOS', 'Web'].includes(os)
      && createby !== 'rules'
    ) {
      result.push(ALL_SUB_ID_TYPE_MAP.wbraId);
      result.push(ALL_SUB_ID_TYPE_MAP.gbraId);
      result.push(ALL_SUB_ID_TYPE_MAP.gclId);
      result.push(ALL_SUB_ID_TYPE_MAP.mixed);
    }
    if (audienceType === 'event' && idType === 'ga4_id' && os === 'Web' && createby !== 'rules') {
      result.push(ALL_SUB_ID_TYPE_MAP.clientId);
    }
  }
  if (media === 'Facebook' && createby !== 'rules') {
    if (audienceType === 'event' && os === 'Web' && idType === 'click_id') {
      result.push(ALL_SUB_ID_TYPE_MAP.fbc);
    }
  }
  return result;
}

export function getAdvancedList(valueInner: string, formDataInner: IIAudienceForm) {
  if (isString(formDataInner[camelCase(valueInner)]) || isArray(formDataInner[camelCase(valueInner)])) {
    return formDataInner[camelCase(valueInner)].length === 2;
  }
  return false;
}

/**
 * @description 生成 session id
 * @return
 */
export function genUuid() {
  let d = new Date().getTime();
  const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (str: string) => replaceStr(str));
  function replaceStr(str: string) {
    const r = (d + Math.random() * 16) % 16 | 0;
    d = Math.floor(d / 16);
    return (str === 'x' ? r : (r & 0x3) | 0x8).toString(16);
  }
  return uuid;
}

export function getTempAudienceId() {
  const { isToCampaign } = storeToRefs(useAixAudienceOverviewFormVisible());
  return isToCampaign.value ? `fake_${genUuid()}` : '';
}

export function getSwitchValue(val: any): number {
  if (![0, 1, '0', '1', true, false].includes(val)) {
    return 0;
  }
  return Number(val);
}

// 判断pc是否禁用
export function getPcStatus(media: string, audienceType: string) {
  if (['Google', 'Facebook'].includes(media)) {
    return media === 'Facebook' && audienceType !== 'event';
  }
  return true;
}
