export const logData = {
  table: [
    {
      audience_id: 'audience_89ndozjjpgqqngh4gypk_a',
      audience_name: 'meta_rf_glb_audience_0601_group_2_seg_0',
      data_time_range: '2023-06-26 03:00:00~2023-06-26 03:59:59',
      date: '2023-06-26 15:08:21',
      details: 'data upload complete',
      period: '2023062603',
      size: 924,
      status: 'successed',
      task_id: 'ins_8b039a7b_aaf2_333d_a00e_f106ac46bfe9',
      type: 'upload',
    },
    {
      audience_id: 'audience_89ndozjjpgqqngh4gypk_a',
      audience_name: 'meta_rf_glb_audience_0601_group_2_seg_0',
      data_time_range: '2023-06-26 02:00:00~2023-06-26 02:59:59',
      date: '2023-06-26 14:17:26',
      details: 'data upload complete',
      period: '2023062602',
      size: 800,
      status: 'successed',
      task_id: 'ins_b6e4c887_b541_3072_bf3d_1019b9251569',
      type: 'upload',
    },
  ],
  total: 183,
  log_columns: [
    {
      key: 'date',
      title: 'Date',
    },
    {
      key: 'data_time_range',
      width: 300,
      title: 'Date Time Range',
    },
    {
      key: 'audience_name',
      title: 'Audiencen Name',
    },
    {
      key: 'task_id',
      title: 'Task ID',
    },
    {
      key: 'type',
      title: 'Type',
    },
    {
      key: 'status',
      title: 'Status',
    },
    {
      key: 'size',
      title: 'Size',
      format: 'number',
    },
    {
      key: 'details',
      title: 'Details',
    },
  ],
  log_options: {
    type_list: [
      {
        value: 'upload',
        text: 'upload',
      },
      {
        value: 'delete',
        text: 'delete',
      },
    ],
    status_list: [
      {
        value: 'failed',
        text: 'failed',
      },
      {
        value: 'running',
        text: 'running',
      },
      {
        value: 'rerunning',
        text: 'rerunning',
      },
      {
        value: 'successed',
        text: 'successed',
      },
    ],
  },
};
