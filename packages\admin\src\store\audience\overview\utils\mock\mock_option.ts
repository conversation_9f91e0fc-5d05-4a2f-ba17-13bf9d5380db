export const optionData = {
  tabs: [
    {
      id: 'customized',
      label: 'Customized Audience',
    },
    {
      id: 'modeling',
      label: 'Modeling Audience',
    },
  ],
  device_list: {
    iOS: [
      'iPad6thGen',
      'iPad7thGen',
      'iPad8thGen(WiFi)',
      'iPad8thGen(WiFi+Cellular)',
      'iPadAir',
      'iPadAir2',
      'iPadAir3',
      'iPadAir4thGen(WiFi)',
      'iPadAir4thGen(WiFi+Cellular)',
      'iPadMini2',
      'iPadMini3',
      'iPadMini4',
      'iPadMini5',
      'iPadPro',
      'iPadPro2ndGen',
      'iPadPro3rdGen',
      'iPadPro4thGen',
      'iPhone11',
      'iPhone11Pro',
      'iPhone11ProMax',
      'iPhone12',
      'iPhone12Mini',
      'iPhone12Pro',
      'iPhone12ProMax',
      'iPhone5S',
      'iPhone6',
      'iPhone6Plus',
      'iPhone6S',
      'iPhone6SPlus',
      'iPhone7',
      'iPhone7Plus',
      'iPhone8',
      'iPhone8Plus',
      'iPhoneSE',
      'iPhoneSE2ndGen',
      'iPhoneX',
      'iPhoneXR',
      'iPhoneXS',
      'iPhoneXSMax',
      'iPod6thGen',
      'iPod7thGen',
    ],
    Android: [
      'AGM::AGM X3',
      'aiuto::AT1001',
      'aiuto::AT1002',
      'aiuto::AT702',
      'Alldocube::iPlay_20',
      'ALLDOCUBE::M8',
      'ALLDOCUBE::T1006',
      'ALLDOCUBE::T701',
      'ALLDOCUBE::T806',
      'ALLDOCUBE::T806MH',
      'ALLDOCUBE::U1005',
      'ALLDOCUBE::U1006',
      'ALLDOCUBE::U1006H',
      'ALPHALING::A94GT_Plus',
      'ALPHALING::A94GT-B_A94GT-W',
      'Amazon::KFAUWI',
      'Amazon::KFDOWI',
      'Amazon::KFGIWI',
      'Amazon::KFKAWI',
      'Amazon::KFMAWI',
      'Amazon::KFMUWI',
      'Amazon::KFONWI',
      'Amazon::KFSUWI',
      'amd7::nox_amd7',
      'Android::FiiO M11 Pro',
      'Android::MuMu',
      'Android::Nexus 6',
      'asus::ASUS_A001',
      'asus::ASUS_A007',
      'asus::ASUS_I001D',
      'asus::ASUS_I001DA',
      'asus::ASUS_I001DC',
      'asus::ASUS_I001DE',
      'asus::ASUS_I002D',
      'asus::ASUS_I003D',
      'asus::ASUS_I003DD',
      'asus::ASUS_I01WD',
      'asus::ASUS_P00I',
      'asus::ASUS_T00P',
      'asus::ASUS_X008DB',
      'asus::ASUS_X00DDA',
      'asus::ASUS_X00HD',
      'asus::ASUS_X00ID',
      'asus::ASUS_X00LD',
      'asus::ASUS_X00PD',
      'asus::ASUS_X00QD',
      'asus::ASUS_X00RD',
      'asus::ASUS_X00TD',
      'asus::ASUS_X00TDB',
      'asus::ASUS_X013DB',
      'asus::ASUS_X017DA',
      'asus::ASUS_X018D',
      'asus::ASUS_X01AD',
      'asus::ASUS_X01BDA',
      'asus::ASUS_Z00AD',
      'asus::ASUS_Z00ED',
      'asus::ASUS_Z00XS',
      'asus::ASUS_Z010DB',
      'asus::ASUS_Z011D',
      'asus::ASUS_Z012DA',
      'asus::ASUS_Z016D',
      'asus::ASUS_Z017DA',
      'asus::ASUS_Z01BDA',
      'asus::ASUS_Z01FD',
      'asus::ASUS_Z01GD',
      'asus::ASUS_Z01HDA',
      'asus::ASUS_Z01KD',
      'asus::ASUS_Z01KDA',
      'asus::ASUS_Z01MDA',
      'asus::ASUS_Z01QD',
      'asus::ASUS_Z01RD',
      'asus::K00R',
      'asus::K014',
      'asus::K015',
      'asus::P002',
      'asus::P008',
      'asus::P00A',
      'asus::P00C',
      'asus::P00L',
      'asus::P01MA',
      'asus::P01T_1',
      'asus::P024',
      'asus::P027',
      'asus::P028',
      'BEISTA::X104',
      'blackberry::BBB100-6',
      'blackberry::BBE100-4',
      'blackberry::BBF100-6',
      'blackberry::BBF100-8',
      'blackberry::BBF100-9',
      'blackshark::DLT-H0',
      'blackshark::SHARK KLE-A0',
      'blackshark::SHARK KLE-H0',
      'blackshark::SHARK MBU-H0',
      'blackshark::SKR-H0',
      'blackshark::SKW-H0',
      'Blackview::A60',
      'Blackview::A60Pro',
      'Blackview::A80',
      'Blackview::A80Pro',
      'Blackview::BV5500',
      'Blackview::BV5500Plus',
      'Blackview::BV5500Pro',
      'Blackview::BV5900',
      'Blackview::BV9100',
      'Blackview::BV9500',
      'Blackview::BV9600',
      'Blackview::BV9800Pro',
      'Blackview::BV9900',
      'BLUEDOT::BNT-1012',
      'BLUEDOT::BNT-1013',
      'BLUEDOT::BNT-791(2G)',
      'BLUEDOT::BNT-801W',
      'BLUEDOT::BNT-802',
      'BungBungame::Kalos 2',
      'Cat::S41',
      'Cat::S60',
      'CHUWI::Hi10 pro',
      'CHUWI::Hi9Air',
      'CHUWI::Hi9plus',
      'CHUWI::Hi9Pro',
      'COVIA::CP-J55aX',
      'CUBE::U83-D',
      'DOCOMO::DM-01J',
      'DOCOMO::F-01F',
      'DOCOMO::F-01H',
      'DOCOMO::F-01J',
      'DOCOMO::F-01K',
      'DOCOMO::F-01L',
      'DOCOMO::F-02F',
      'DOCOMO::F-02G',
      'DOCOMO::F-02H',
      'DOCOMO::F-02K',
      'DOCOMO::F-02L',
      'DOCOMO::F-03G',
      'DOCOMO::F-03H',
      'DOCOMO::F-03K',
      'DOCOMO::F-04G',
      'DOCOMO::F-04H',
      'DOCOMO::F-04J',
      'DOCOMO::F-04K',
      'DOCOMO::F-05F',
      'DOCOMO::F-05J',
      'DOCOMO::F-06E',
      'DOCOMO::F-41A',
      'DOCOMO::F-42A',
      'DOCOMO::F-51A',
      'DOCOMO::F-52A',
      'docomo::samsungG955F',
      'DOCOMO::SH-01F',
      'DOCOMO::SH-01FDQ',
      'DOCOMO::SH-01G',
      'DOCOMO::SH-01H',
      'DOCOMO::SH-01K',
      'DOCOMO::SH-01L',
      'DOCOMO::SH-01M',
      'DOCOMO::SH-02F',
      'DOCOMO::SH-02G',
      'DOCOMO::SH-02H',
      'DOCOMO::SH-02J',
      'DOCOMO::SH-02M',
      'DOCOMO::SH-03G',
      'DOCOMO::SH-03J',
      'DOCOMO::SH-03K',
      'DOCOMO::SH-04F',
      'DOCOMO::SH-04G',
      'DOCOMO::SH-04H',
      'DOCOMO::SH-04L',
      'DOCOMO::SH-05F',
      'DOCOMO::SH-05G',
      'DOCOMO::SH-06E',
      'DOCOMO::SH-06F',
      'DOCOMO::SH-08E',
      'DOCOMO::SH-41A',
      'DOCOMO::SH-51A',
      'DOCOMO::SH-53A',
      'docomo::SO-01F',
      'docomo::SO-01G',
      'docomo::SO-01H',
      'docomo::SO-01J',
      'docomo::SO-01K',
      'docomo::SO-01L',
      'docomo::SO-01M',
      'docomo::SO-02E',
      'docomo::SO-02F',
      'docomo::SO-02G',
      'docomo::SO-02H',
      'docomo::SO-02J',
      'docomo::SO-02K',
      'docomo::SO-02L',
      'docomo::SO-03F',
      'docomo::SO-03G',
      'docomo::SO-03H',
      'docomo::SO-03J',
      'docomo::SO-03K',
      'docomo::SO-03L',
      'docomo::SO-04E',
      'docomo::SO-04F',
      'docomo::SO-04G',
      'docomo::SO-04H',
      'docomo::SO-04J',
      'docomo::SO-04K',
      'docomo::SO-05F',
      'docomo::SO-05G',
      'docomo::SO-05K',
      'docomo::SO-41A',
      'docomo::SO-51A',
      'docomo::SO-52A',
      'DOOGEE::S60',
      'DOOGEE::S70',
      'DOOGEE::S90',
      'DOOGEE::S95Pro',
      'DOOGEE::S96Pro',
      'Dragon_Touch::K10',
      'Dragon_Touch::M7',
      'Dragon_Touch::MAX 10',
      'Dragon_Touch::MAX_10',
      'Dragon_Touch::Notepad 102',
      'Dragon_Touch::Notepad_K10',
      'Dragon_Touch::NotePad_Y80',
      'Dragon_Touch::Y80',
      'Dragon_Touch::Y88X__PLUS',
      'Dragon_Touch::Y88X_PRO',
      'dtab::d-01G',
      'dtab::d-01H',
      'dtab::d-01J',
      'dtab::d-01K',
      'dtab::d-02H',
      'dtab::d-02K',
      'dtab::d-41A',
      'essential::PH-1',
      'EveryPhone::EP171DX',
      'EveryPhone::EP171HG',
      'EveryPhone::EP172BZ',
      'EveryPhone::EP172PR',
      'FFFSMARTLIFE::FFFTAB10',
      'FFFSMARTLIFE::FFFTAB7',
      'freetel::FTJ152C',
      'FREETEL::FTJ161B',
      'FREETEL::FTJ162B',
      'FREETEL::FTJ162D',
      'FREETEL::FTJ162E',
      'FREETEL::FTJ17A00',
      'FUJITSU::901FJ',
      'FUJITSU::arrowsM03',
      'FUJITSU::arrowsM04',
      'FUJITSU::arrowsM04-PREMIUM',
      'FUJITSU::arrowsM05',
      'FUJITSU::arrowsRX',
      'FUJITSU::M02',
      'FUJITSU::M305',
      'FUJITSU::M555',
      'FUJITSU::TONE-m17',
      'geaneepro::JT10-90',
      'Goo::View Prime',
      'google::G011A',
      'google::GA00747-UK',
      'google::Intel Apollo Lake Chromebook',
      'google::kukui',
      'google::Mediatek MT8173 Chromebook',
      'google::Nexus 5',
      'google::Nexus 5X',
      'google::Nexus 6',
      'google::Nexus 6P',
      'google::Nexus 7',
      'google::Nexus 9',
      'google::octopus',
      'google::Pixel',
      'google::Pixel 2',
      'google::Pixel 2 XL',
      'google::PIXEL 2 XL',
      'google::PIXEL 2XL',
      'google::Pixel 3',
      'google::Pixel 3 XL',
      'google::Pixel 3a',
      'google::Pixel 3a XL',
      'google::Pixel 4',
      'google::Pixel 4 XL',
      'google::Pixel 4a',
      'google::Pixel 4a (5G)',
      'google::Pixel 5',
      'google::Pixel XL',
      'google::scarlet',
      'GPD::XD Plus',
      'HONOR::ARE-AL00',
      'HONOR::COL-L29',
      'HONOR::FRD-L02',
      'HONOR::HLK-AL00',
      'HONOR::JSN-AL00a',
      'HONOR::PCT-L29',
      'HONOR::RVL-AL09',
      'HONOR::STF-L09',
      'HONOR::TNY-AL00',
      'HONOR::YAL-AL00',
      'HONOR::YAL-AL10',
      'htc::601HT',
      'HTC::HTC 10',
      'HTC::HTC Desire 19s',
      'htc::HTC One M9',
      'htc::HTC U11',
      'htc::HTC U11 Life',
      'htc::HTC U12+',
      'htc::HTL22',
      'htc::HTL23',
      'htc::HTV31',
      'htc::HTV32',
      'htc::HTV33',
      'htc::X2-HT',
      'Huawei::503HW',
      'HUAWEI::608HW',
      'HUAWEI::704HW',
      'HUAWEI::AGS2-L09',
      'HUAWEI::AGS2-W09',
      'HUAWEI::AGS-L09',
      'HUAWEI::AGS-W09',
      'HUAWEI::ANE-LX2',
      'HUAWEI::ANE-LX2J',
      'HUAWEI::BAH2-L09',
      'HUAWEI::BAH2-W19',
      'HUAWEI::BAH-L09',
      'HUAWEI::BAH-W09',
      'HUAWEI::BG2-W09',
      'HUAWEI::BGO-DL09',
      'HUAWEI::BLA-L09',
      'HUAWEI::BLA-L29',
      'HUAWEI::BTV-DL09',
      'HUAWEI::BTV-W09',
      'HUAWEI::CLT-L29',
      'HUAWEI::CMR-W09',
      'HUAWEI::CMR-W19',
      'HUAWEI::CPN-L09',
      'HUAWEI::CPN-W09',
      'HUAWEI::ELE-AL00',
      'HUAWEI::ELE-L29',
      'HUAWEI::EML-L09',
      'HUAWEI::EML-L29',
      'HUAWEI::EVA-L09',
      'HUAWEI::EVR-AL00',
      'HUAWEI::EVR-L29',
      'HUAWEI::EVR-N29',
      'HUAWEI::FDR-A01w',
      'HUAWEI::FIG-LA1',
      'HUAWEI::HDN-W09',
      'HUAWEI::HMA-AL00',
      'HUAWEI::HMA-L29',
      'HUAWEI::HUAWEI CAN-L12',
      'HUAWEI::HUAWEI M2-801W',
      'HUAWEI::HUAWEI M2-802L',
      'HUAWEI::HUAWEI MLA-AL10',
      'Huawei::HUAWEI MT7-J1',
      'Huawei::HUAWEI P7-L10',
      'HUAWEI::HUAWEI P8max',
      'HUAWEI::HUAWEI VNS-L22',
      'HUAWEI::HUAWEI VNS-L52',
      'HUAWEI::HW-01K',
      'HUAWEI::HW-02L',
      'Huawei::HWT31',
      'HUAWEI::HWV31',
      'HUAWEI::HWV32',
      'HUAWEI::HWV33',
      'HUAWEI::INE-AL00',
      'HUAWEI::INE-LX2',
      'HUAWEI::JDN2-L09',
      'HUAWEI::JDN2-W09',
      'Huawei::JDN-L01',
      'Huawei::JDN-W09',
      'HUAWEI::JKM-LX2',
      'HUAWEI::KOB-L09',
      'HUAWEI::KOB-W09',
      'HUAWEI::LIO-AL00',
      'HUAWEI::LIO-AN00',
      'HUAWEI::LYA-AL10',
      'HUAWEI::LYA-L09',
      'HUAWEI::LYA-L29',
      'HUAWEI::MAR-LX1M',
      'HUAWEI::MAR-LX2',
      'HUAWEI::MAR-LX2J',
      'HUAWEI::MAR-LX3A',
      'HUAWEI::MHA-AL00',
      'HUAWEI::MHA-L29',
      'HUAWEI::PAR-AL00',
      'HUAWEI::PAR-LX9',
      'Huawei::PE-TL10',
      'HUAWEI::PIC-LX9',
      'Huawei::PLE-701L',
      'HUAWEI::POT-LX2J',
      'HUAWEI::PRA-LX2',
      'HUAWEI::RNE-L22',
      'huawei::samsungG955F',
      'HUAWEI::SHT-AL09',
      'HUAWEI::SHT-W09',
      'HUAWEI::SNE-LX1',
      'HUAWEI::SNE-LX2',
      'HUAWEI::TAS-AL00',
      'HUAWEI::VKY-AL00',
      'HUAWEI::VKY-L29',
      'HUAWEI::VOG-AL00',
      'HUAWEI::VOG-AL10',
      'HUAWEI::VOG-L29',
      'HUAWEI::VRD-W10',
      'HUAWEI::VTR-L09',
      'HUAWEI::VTR-L29',
      'HUAWEI::WAS-LX2J',
      'HUAWEI::YAL-L21',
      'JTY::F18Plus',
      'JTY::KT107',
      'Just5::MD-02P',
      'JUSTSYSTEMS::SZJ-JS201',
      'jyonetsu_kakaku::YMR8',
      'K106::K106',
      'KDDI::AST21',
      'KDDI::FJL22',
      'KDDI::FJT21',
      'KDDI::KYL22',
      'KDDI::KYT31',
      'KDDI::KYV31',
      'KDDI::KYV32',
      'KDDI::KYV33',
      'KDDI::KYV34',
      'KDDI::KYV35',
      'KDDI::KYV36',
      'KDDI::KYV37',
      'KDDI::KYY22',
      'KDDI::KYY23',
      'KDDI::KYY24',
      'KDDI::LGL22',
      'KDDI::LGL23',
      'KDDI::LGL24',
      'KDDI::LGT31',
      'KDDI::LGT32',
      'KDDI::LGV31',
      'KDDI::LGV32',
      'KDDI::LGV33',
      'KDDI::LGV34',
      'KDDI::LGV35',
      'KDDI::LGV36',
      'KDDI::SCL22',
      'KDDI::SCL23',
      'KDDI::SCL24',
      'KDDI::SCT21',
      'KDDI::SCV31',
      'KDDI::SCV32',
      'KDDI::SCV33',
      'KDDI::SCV35',
      'KDDI::SCV36',
      'KDDI::SCV37',
      'KDDI::SCV38',
      'KDDI::SCV39',
      'KDDI::SCV40',
      'KDDI::SCV41',
      'KDDI::SCV42',
      'KDDI::SCV43',
      'KDDI::SCV43-j',
      'KDDI::SCV44',
      'KDDI::SCV45',
      'KDDI::SCV46',
      'KDDI::SCV46-j',
      'KDDI::SCV46-u',
      'KDDI::SHG01',
      'KDDI::SHG02',
      'KDDI::SHG03',
      'KDDI::SHL23',
      'KDDI::SHL24',
      'KDDI::SHL25',
      'KDDI::SHT22',
      'KDDI::SHV31',
      'KDDI::SHV32',
      'KDDI::SHV33',
      'KDDI::SHV34',
      'KDDI::SHV35',
      'KDDI::SHV36',
      'KDDI::SHV37',
      'KDDI::SHV38',
      'KDDI::SHV39',
      'KDDI::SHV40',
      'KDDI::SHV41',
      'KDDI::SHV42',
      'KDDI::SHV43',
      'KDDI::SHV44',
      'KDDI::SHV45',
      'KDDI::SHV46',
      'KDDI::SHV47',
      'KDDI::SHV48',
      'KDDI::SOG01',
      'KDDI::SOG02',
      'KDDI::SOL22',
      'KDDI::SOL23',
      'KDDI::SOL24',
      'KDDI::SOL25',
      'KDDI::SOL26',
      'KDDI::SOT21',
      'KDDI::SOT31',
      'KDDI::SOV31',
      'KDDI::SOV32',
      'KDDI::SOV33',
      'KDDI::SOV34',
      'KDDI::SOV35',
      'KDDI::SOV36',
      'KDDI::SOV37',
      'KDDI::SOV38',
      'KDDI::SOV39',
      'KDDI::SOV40',
      'KDDI::SOV41',
      'KDDI::SOV42',
      'KDDI::SOV43',
      'KDDI_u::SCV43-u',
      'KDDI_u::SHV37_u',
      'KDDI_u::SHV40_u',
      'KDDI_u::SHV43-u',
      'KDDI_u::SHV45-u',
      'KTE::KT107',
      'KYOCERA::404KC',
      'KYOCERA::503KC',
      'KYOCERA::602KC',
      'KYOCERA::704KC',
      'KYOCERA::705KC',
      'KYOCERA::901KC',
      'KYOCERA::A001KC',
      'KYOCERA::KC-01',
      'KYOCERA::KC-S301AE',
      'KYOCERA::KC-S702',
      'KYOCERA::KYG01',
      'KYOCERA::KYT32',
      'KYOCERA::KYT33',
      'KYOCERA::KYV38',
      'KYOCERA::KYV39',
      'KYOCERA::KYV40',
      'KYOCERA::KYV40U',
      'KYOCERA::KYV41',
      'KYOCERA::KYV42',
      'KYOCERA::KYV42_u',
      'KYOCERA::KYV43',
      'KYOCERA::KYV43_j',
      'KYOCERA::KYV44',
      'KYOCERA::KYV44_u',
      'KYOCERA::KYV45',
      'KYOCERA::KYV46',
      'KYOCERA::KYV47',
      'KYOCERA::KYV47-u',
      'KYOCERA::KYV48',
      'KYOCERA::S2',
      'KYOCERA::S4-KC',
      'KYOCERA::S6-KC',
      'KYOCERA::S8-KC',
      'KYOCERA::X3-KC',
      'LaVieTab::PC-TE510BAL',
      'LAVIETabE::PC-TE510HAW',
      'LEAGOO::T5',
      'LeEco::LEX820',
      'Lenovo::701LV',
      'Lenovo::801LV',
      'Lenovo::d-42A',
      'Lenovo::EveryPad2',
      'Lenovo::EveryPad3',
      'Lenovo::Lenovo B6000-H',
      'Lenovo::Lenovo B8080-F',
      'Lenovo::Lenovo L38111',
      'Lenovo::Lenovo L78032',
      'Lenovo::Lenovo L78051',
      'Lenovo::Lenovo L78071',
      'Lenovo::Lenovo PB2-690M',
      'Lenovo::Lenovo TB-7305F',
      'Lenovo::Lenovo TB-7504X',
      'Lenovo::Lenovo TB-8304F1',
      'Lenovo::Lenovo TB-8504F',
      'Lenovo::Lenovo TB-8505F',
      'Lenovo::Lenovo TB-8704F',
      'Lenovo::Lenovo TB-8704X',
      'Lenovo::Lenovo TB-8705F',
      'Lenovo::Lenovo TB-X104F',
      'Lenovo::Lenovo TB-X304F',
      'Lenovo::Lenovo TB-X306F',
      'Lenovo::Lenovo TB-X505F',
      'Lenovo::Lenovo TB-X606F',
      'Lenovo::Lenovo TB-X606FA',
      'Lenovo::Lenovo TB-X606X',
      'Lenovo::Lenovo TB-X704F',
      'Lenovo::Lenovo TB-X705F',
      'Lenovo::Lenovo TB-X705L',
      'Lenovo::Lenovo YB1-X90L',
      'Lenovo::Lenovo YT3-850L',
      'Lenovo::Lenovo YT3-X50F',
      'Lenovo::Lenovo YT-X703F',
      'Lenovo::Lenovo YT-X705F',
      'Lenovo::PC-TE507JAW',
      'Lenovo::vivo Y13L',
      'Lenovo::YOGA Tablet 2-830L',
      'lge::801LG',
      'lge::802LG',
      'lge::901LG',
      'lge::A001LG',
      'lge::DM-01G',
      'lge::DM-01K',
      'lge::DM-02H',
      'lge::L-01J',
      'lge::L-01K',
      'lge::L-01L',
      'lge::L-02K',
      'lge::L-03K',
      'lge::L-41A',
      'lge::L-51A',
      'lge::L-52A',
      'lge::LGS02',
      'lge::LGT02',
      'lge::LM-Q710XM',
      'lge::LM-V510N',
      'lge::X5-LG',
      'Mode1::MD-03P',
      'Mode1::MD-04P',
      'motorola::moto e5',
      'motorola::moto e6s',
      'motorola::Moto G (4)',
      'motorola::Moto G (5)',
      'motorola::Moto G (5S)',
      'motorola::Moto G (5S) Plus',
      'motorola::moto g pro',
      'motorola::moto g(6) play',
      'motorola::moto g(6) plus',
      'motorola::moto g(7)',
      'motorola::moto g(7) plus',
      'motorola::moto g(7) power',
      'motorola::moto g(8)',
      'motorola::moto g(8) plus',
      'motorola::moto g(8) power',
      'motorola::moto g(8) power lite',
      'motorola::moto g(9) play',
      'motorola::moto x4',
      'motorola::Moto Z2 Play',
      'motorola::Moto Z3 Play',
      'motorola::motorola one hyper',
      'motorola::XT1052',
      'motorola::XT1635-02',
      'motorola::XT1650',
      'Moxnice::P63',
      'MOXNICE::P63',
      'NEC::LAVIE T11 11QHD1',
      'NEC::LAVIE Tab E 10FHD1',
      'NEC::LAVIE Tab E 10FHD2',
      'NEC::LAVIE Tab E 7SD1',
      'NEC::LAVIE Tab E 8FHD1',
      'NEC::LAVIE Tab E 8HD1',
      'NEC::LaVieTab PC-TE508S1',
      'NEC::PC-TE410JAW',
      'NEC::PC-TE507FAW',
      'NEC::PC-TE508HAW',
      'NEC::PC-TE510JAW',
      'NEC::PC-TS508FAM',
      'NEC::PC-TS508T1W',
      'Nokia::Nokia 7.1',
      'Nokia::TA-1004',
      'NuAns::NEO [Reloaded]',
      'nubia::NX531J',
      'nubia::NX629J',
      'nubia::NX659J',
      'OnePlus::A5010',
      'OnePlus::GM1900',
      'OnePlus::GM1910',
      'OnePlus::GM1913',
      'OnePlus::HD1900',
      'OnePlus::HD1910',
      'OnePlus::IN2010',
      'OnePlus::IN2020',
      'OnePlus::ONEPLUS A3010',
      'OnePlus::ONEPLUS A5000',
      'OnePlus::ONEPLUS A5010',
      'OnePlus::ONEPLUS A6000',
      'OnePlus::ONEPLUS A6003',
      'OnePlus::ONEPLUS A6010',
      'Onkyo::DP-CMX1',
      'OPPO::A001OP',
      'OPPO::A002OP',
      'OPPO::CPH1701',
      'OPPO::CPH1719',
      'OPPO::CPH1729',
      'OPPO::CPH1805',
      'OPPO::CPH1823',
      'OPPO::CPH1833',
      'OPPO::CPH1851',
      'OPPO::CPH1875',
      'OPPO::CPH1877',
      'OPPO::CPH1879',
      'OPPO::CPH1893',
      'OPPO::CPH1903',
      'OPPO::CPH1907',
      'OPPO::CPH1919',
      'OPPO::CPH1937',
      'OPPO::CPH1943',
      'OPPO::CPH1969',
      'OPPO::CPH1983',
      'OPPO::CPH1989',
      'OPPO::CPH2013',
      'OPPO::CPH2099',
      'OPPO::OPG01',
      'OPPO::PAFM00',
      'OPPO::PBCM30',
      'OPPO::PCCM00',
      'OPPO::PCGM00',
      'OPPO::PCHM30',
      'OPPO::PCLM10',
      'OPPO::RMX1807',
      'OPPO::X9009',
      'OUKITEL::C11_Pro',
      'OUKITEL::C12 Pro',
      'OUKITEL::C15 Pro',
      'OUKITEL::C16_Pro',
      'OUKITEL::C17 Pro',
      'OUKITEL::C18_Pro',
      'OUKITEL::C19',
      'OUKITEL::C21',
      'OUKITEL::K12',
      'OUKITEL::K7',
      'OUKITEL::WP5',
      'OUKITEL::WP5 Pro',
      'OUKITEL::WP7',
      'OUKITEL::Y1000',
      'OUKITEL::Y4800',
      'Planet::Cosmo_Communicator',
      'POCO::M2007J20CG',
      'POCO::POCO F2 Pro',
      'POPTEL::P9000_MAX',
      'PRITOM::M10 Pro',
      'Rakuten::C330',
      'Rakuten::P710',
      'RAKUTEN::SH-M14',
      'RAKUTEN::SH-M16',
      'RAKUTEN::SH-RM11',
      'RAKUTEN::SH-RM12',
      'RAKUTEN::SH-RM15',
      'razer::Phone',
      'razer::Phone 2',
      'razer::RazerPhone2',
      'Realme::RMX1821',
      'realme::RMX1919',
      'realme::RMX1931',
      'realme::RMX1971',
      'realme::RMX2063',
      'Redmi::A001XM',
      'Redmi::M2010J19SG',
      'Redmi::M2101K6R',
      'Redmi::Redmi Note 8 Pro',
      'Redmi::Redmi Note 9 Pro',
      'Redmi::Redmi Note 9S',
      'samsung::Galaxy Feel SC-04J',
      'samsung::GT-N7100',
      'samsung::SAMSUNG-SM-G890A',
      'samsung::SAMSUNG-SM-N900A',
      'samsung::SC-01F',
      'samsung::SC-01G',
      'samsung::SC-01H',
      'samsung::SC-01K',
      'samsung::SC-01L',
      'samsung::SC-01M',
      'samsung::SC-02E',
      'samsung::SC-02F',
      'samsung::SC-02G',
      'samsung::SC-02H',
      'samsung::SC-02J',
      'samsung::SC-02K',
      'samsung::SC-02L',
      'samsung::SC-02M',
      'samsung::SC-03E',
      'samsung::SC-03G',
      'samsung::SC-03J',
      'samsung::SC-03K',
      'samsung::SC-03L',
      'samsung::SC-04E',
      'samsung::SC-04F',
      'samsung::SC-04G',
      'samsung::SC-04J',
      'samsung::SC-04L',
      'samsung::SC-05G',
      'samsung::SC-05L',
      'samsung::SC-41A',
      'samsung::SC-42A',
      'samsung::SC-51A',
      'samsung::SC-51B',
      'samsung::SC-52A',
      'samsung::SC-53A',
      'samsung::SC-54A',
      'samsung::SCG01',
      'samsung::SCG02',
      'samsung::SCG03',
      'samsung::SCG05',
      'samsung::SCG06',
      'samsung::SCG07',
      'samsung::SCG08',
      'samsung::SCG10',
      'samsung::SCV47',
      'samsung::SCV48',
      'samsung::SCV49',
      'samsung::SM-A107F',
      'samsung::SM-A205U',
      'samsung::SM-A505F',
      'samsung::SM-A507FN',
      'samsung::SM-A515F',
      'samsung::SM-A7050',
      'samsung::SM-A715F',
      'samsung::SM-A750C',
      'samsung::SM-A750GN',
      'samsung::SM-A805N',
      'samsung::SM-A908N',
      'samsung::SM-F700N',
      'samsung::SM-F900U1',
      'samsung::SM-F907N',
      'samsung::SM-G3812',
      'samsung::SM-G610F',
      'samsung::SM-G610Y',
      'samsung::SM-G770F',
      'samsung::SM-G887N',
      'samsung::SM-G920F',
      'samsung::SM-G925F',
      'samsung::SM-G930K',
      'samsung::SM-G930L',
      'samsung::SM-G930S',
      'samsung::SM-G9350',
      'samsung::SM-G935F',
      'samsung::SM-G950F',
      'samsung::SM-G950N',
      'samsung::SM-G950U1',
      'samsung::SM-G955F',
      'samsung::SM-G955N',
      'samsung::SM-G9600',
      'samsung::SM-G960F',
      'samsung::SM-G960N',
      'samsung::SM-G9650',
      'samsung::SM-G965F',
      'samsung::SM-G965N',
      'samsung::SM-G965U',
      'samsung::SM-G9700',
      'samsung::SM-G970F',
      'samsung::SM-G970N',
      'samsung::SM-G9730',
      'samsung::SM-G973C',
      'samsung::SM-G973F',
      'samsung::SM-G973N',
      'samsung::SM-G973U1',
      'samsung::SM-G9750',
      'samsung::SM-G975F',
      'samsung::SM-G975N',
      'samsung::SM-G975U',
      'samsung::SM-G975U1',
      'samsung::SM-G977N',
      'samsung::SM-G9810',
      'samsung::SM-G981N',
      'samsung::SM-G9860',
      'samsung::SM-G986N',
      'samsung::SM-G986U1',
      'samsung::SM-G9880',
      'samsung::SM-G988B',
      'samsung::SM-G988N',
      'samsung::SM-J415FN',
      'samsung::SM-J530S',
      'samsung::SM-J710K',
      'samsung::SM-N770F',
      'samsung::SM-N935K',
      'samsung::SM-N9500',
      'samsung::SM-N950F',
      'samsung::SM-N950N',
      'samsung::SM-N9600',
      'samsung::SM-N960F',
      'samsung::SM-N960N',
      'samsung::SM-N960U',
      'samsung::SM-N960U1',
      'samsung::SM-N9700',
      'samsung::SM-N971N',
      'samsung::SM-N9750',
      'samsung::SM-N975C',
      'samsung::SM-N975F',
      'samsung::SM-N975U',
      'samsung::SM-N976N',
      'samsung::SM-N9860',
      'samsung::SM-N986N',
      'samsung::SM-T510',
      'samsung::SM-T670',
      'samsung::SM-T710',
      'samsung::SM-T800',
      'samsung::SM-T805',
      'samsung::SM-T820',
      'samsung::SM-T830',
      'samsung::SM-T835',
      'samsung::SM-T860',
      'samsung::SM-T865',
      'samsung::SM-T865N',
      'SBM::301F',
      'SBM::401SO',
      'SBM::402LG',
      'SBM::402ZT',
      'SBM::403SC',
      'SBM::404SC',
      'SBM::601LV',
      'SBM::602LV',
      'SBM::602ZT',
      'SBM::605HW',
      'SBM::606HW',
      'SBM::701HW',
      'SBM::801FJ',
      'SBM::SBM302SH',
      'SBM::SBM303SH',
      'SG::304SH',
      'SG::305SH',
      'SG::402SH',
      'SG::403SH',
      'SG::404SH',
      'SG::502SH',
      'SG::503SH',
      'SG::506SH',
      'SG::507SH',
      'SG::509SH',
      'SG::603SH',
      'SG::605SH',
      'SG::606SH',
      'SG::701SH',
      'SG::702SH',
      'SG::704SH',
      'SG::706SH',
      'SG::801SH',
      'SG::803SH',
      'SG::808SH',
      'SG::901SH',
      'SG::906SH',
      'SG::907SH',
      'SG::908SH',
      'SG::A001SH',
      'SG::A002SH',
      'SG::A003SH',
      'SG::A004SH',
      'SG::S1',
      'SG::S3-SH',
      'SG::S5-SH',
      'SG::S7-SH',
      'SG::SH-M12-y',
      'SG::SW001SH',
      'SG::X1',
      'SG::X4-SH',
      'Sharp::SH-01K',
      'sharp::SH-04L',
      'SHARP::SH-L02',
      'SHARP::SH-M02',
      'SHARP::SH-M03',
      'SHARP::SH-M05',
      'SHARP::SH-M06',
      'SHARP::SH-M07',
      'SHARP::SH-M08',
      'SHARP::SH-M09',
      'SHARP::SH-M10',
      'SHARP::SH-M11',
      'SHARP::SH-M12',
      'SHARP::SH-M13',
      'SHARP::SH-M15',
      'SHARP::SH-M16',
      'SHARP::SH-RM02',
      'SoftBank::902ZT',
      'Sony::402SO',
      'Sony::501SO',
      'Sony::502SO',
      'Sony::601SO',
      'Sony::602SO',
      'Sony::701SO',
      'Sony::702SO',
      'Sony::801SO',
      'Sony::802SO',
      'Sony::901SO',
      'Sony::902SO',
      'Sony::A001SO',
      'Sony::A002SO',
      'Sony::E5506',
      'Sony::E5823',
      'Sony::E6633',
      'Sony::E6683',
      'Sony::E6853',
      'Sony::E6883',
      'Sony::F5122',
      'Sony::F8132',
      'Sony::F8331',
      'Sony::F8332',
      'Sony::G3116',
      'Sony::G3226',
      'Sony::G3312',
      'Sony::G3412',
      'Sony::G8142',
      'Sony::G8188',
      'Sony::G8232',
      'Sony::G8341',
      'Sony::G8342',
      'Sony::G8441',
      'Sony::H4233',
      'Sony::H4493',
      'Sony::H8166',
      'Sony::H8296',
      'Sony::H8324',
      'Sony::H9493',
      'Sony::I3123',
      'Sony::I4113',
      'Sony::I4193',
      'Sony::I4293',
      'Sony::J3173',
      'Sony::J3273',
      'Sony::J8110',
      'Sony::J9110',
      'Sony::J9150',
      'Sony::J9210',
      'Sony::J9260',
      'Sony::SGP311',
      'Sony::SGP412',
      'Sony::SGP511',
      'Sony::SGP512',
      'Sony::SGP611',
      'Sony::SGP612',
      'Sony::SGP712',
      'Sony::SGP771',
      'sony::SO-01L',
      'sony::SOV40',
      'Sony::SOV42-u',
      'Sony::XQ-AT42',
      'Sony::XQ-AT52',
      'Sony::XQ-AU42',
      'Sony::XQ-AU51',
      'Sony::XQ-AU52',
      'SonyAudio::NW-A100Series',
      'STA::BDF K107H',
      'STA::K960_MT6580_32_P',
      'TCL::6055D',
      'TCL::9061',
      'TCL::T770B',
      'TCL::T780H',
      'TCL::T799B',
      'Teclast::KX20',
      'Teclast::M20 4G',
      'Teclast::M30',
      'TECLAST::M40_EEA',
      'Teclast::P10_HD_EEA',
      'Teclast::P10_HD_ROW',
      'Teclast::P10S_EEA',
      'Teclast::P20HD_EEA',
      'Teclast::P20HD_ROW',
      'teclast::P80 Pro',
      'Teclast::P80_EEA',
      'Teclast::P80X_EEA',
      'Teclast::P80X_ROW',
      'Teclast::T30_EEA',
      'Teclast::T30_ROW',
      'Teclast::TLA002',
      'TECLAST::TLA016',
      'Teclast::X_EEA',
      'Teclast::X10',
      'TESPRO::Mayumi U1',
      'TONE::TONE e19',
      'TONE::TONE e20',
      'TONE::TONE m15',
      'TOSHIBA::A205',
      'Ulefone::Armor 7E',
      'Ulefone::Armor X5',
      'ulefone::Armor_3',
      'Ulefone::Armor_3W',
      'Ulefone::Armor_6',
      'Ulefone::Armor_6E',
      'Ulefone::Armor_7',
      'Ulefone::Armor_X2',
      'Ulefone::S10_Pro',
      'Ulefone::Ulefone_Note 7',
      'UMIDIGI::A3',
      'UMIDIGI::A3_Pro',
      'UMIDIGI::A3S',
      'UMIDIGI::A3X',
      'UMIDIGI::A5_Pro',
      'UMIDIGI::A7',
      'UMIDIGI::A7 Pro',
      'UMIDIGI::A7S',
      'UMIDIGI::F1',
      'UMIDIGI::F2',
      'UMIDIGI::One Max',
      'UMIDIGI::Power',
      'UMIDIGI::Power 3',
      'UMIDIGI::S3_Pro',
      'UMIDIGI::S5 Pro',
      'UMIDIGI::UMIDIGI X',
      'UMIDIGI::Z2',
      'UMIDIGI::Z2_PRO',
      'Unihertz::Titan',
      'unknown::unknown',
      'VAIO::VPA051',
      'VANKYO::S20',
      'VANKYO::S30',
      'VANKYO::S7',
      'VANKYO::S8',
      'VANKYO::Vankyo_S10',
      'VANKYO::Z1',
      'Vankyo::Z10',
      'VANKYO::Z4',
      'Vastking::KingPad_SA10',
      'Vastking::KingPad_SA8',
      'Vertex::G1701',
      'vivo::V1809A',
      'vivo::V1955A',
      'vivo::V1981A',
      'vivo::V1986A',
      'vivo::vivo 1713',
      'vivo::vivo 1805',
      'vivo::vivo 1906',
      'vivo::vivo 1907',
      'vivo::vivo 1933',
      'vivo::vivo NEX S',
      'vivo::vivo X20Plus A',
      'VUCATIMES::N10',
      'WIKO::P4903JP',
      'WIKO::W-V600',
      'WINNOVO::H7_US',
      'Winnovo::T10',
      'Winnovo::T8',
      'WINNOVO::TS10',
      'Xiaomi::Mi 10',
      'Xiaomi::Mi 10 Pro',
      'xiaomi::mi 4lte',
      'Xiaomi::MI 5',
      'Xiaomi::MI 8',
      'Xiaomi::MI 8 Pro',
      'Xiaomi::MI 8 SE',
      'Xiaomi::MI 9',
      'Xiaomi::Mi 9 SE',
      'Xiaomi::MI 9 Transparent Edition',
      'Xiaomi::Mi 9T',
      'Xiaomi::Mi 9T Pro',
      'xiaomi::Mi A2 Lite',
      'Xiaomi::MI CC9 Pro Premium Edition',
      'Xiaomi::MI MAX 2',
      'Xiaomi::MI MAX 3',
      'Xiaomi::Mi MIX 2',
      'Xiaomi::Mi MIX 2S',
      'Xiaomi::Mi MIX 3',
      'Xiaomi::Mi MIX 3 5G',
      'Xiaomi::Mi Note 10',
      'Xiaomi::Mi Note 10 Lite',
      'Xiaomi::Mi Note 10 Pro',
      'Xiaomi::MI PAD 4',
      'Xiaomi::MI PAD 4 PLUS',
      'Xiaomi::MIX 2S',
      'Xiaomi::POCO F1',
      'Xiaomi::POCOPHONE F1',
      'Xiaomi::Redmi K20 Pro',
      'xiaomi::Redmi Note 5',
      'xiaomi::Redmi Note 7',
      'xiaomi::Redmi Note 8',
      'Xiaomi::XIG01',
      'YELLYOUTH::TAB-101',
      'Ymobile::901ZT',
      'ZONKO::K105',
      'ZONKO::K106',
      'ZONKO::K108',
      'ZTE::A001ZT',
      'ZTE::BLADE E01',
      'ZTE::Blade V580',
      'ZTE::MO-01J',
      'ZTE::MO-01K',
      'ZTE::Z-01K',
      'ZTE::ZR01',
      'ZTE::ZTE A2017G',
      'ZTE::ZTE BLADE V0710',
      'ZTE::ZTE BLADE V0720',
      'ZTE::ZTE BLADE V0800',
      'ZTE::ZTE T920',
      'ZTE::ZTG01',
      'ZTE::ZTU31',
    ],
  },
  os_version_list: {
    iOS: [
      '10.0.1',
      '10.0.2',
      '10.0.3',
      '10.1',
      '10.1.1',
      '10.2',
      '10.2.1',
      '10.3',
      '10.3.1',
      '10.3.2',
      '10.3.3',
      '11',
      '11.0.1',
      '11.0.2',
      '11.0.3',
      '11.1',
      '11.1.1',
      '11.1.2',
      '11.2',
      '11.2.1',
      '11.2.2',
      '11.2.5',
      '11.2.6',
      '11.3',
      '11.3.1',
      '11.4',
      '11.4.1',
      '12',
      '12.0.1',
      '12.1',
      '12.1.1',
      '12.1.2',
      '12.1.3',
      '12.1.4',
      '12.2',
      '12.3',
      '12.3.1',
      '12.3.2',
      '12.4',
      '12.4.1',
      '12.4.2',
      '12.4.3',
      '12.4.4',
      '12.4.5',
      '12.4.6',
      '12.4.7',
      '12.4.8',
      '12.4.9',
      '12.5',
      '12.5.1',
      '12.5.2',
      '13',
      '13.1',
      '13.1.1',
      '13.1.2',
      '13.1.3',
      '13.2',
      '13.2.2',
      '13.2.3',
      '13.3',
      '13.3.1',
      '13.4',
      '13.4.1',
      '13.5',
      '13.5.1',
      '13.6',
      '13.6.1',
      '13.7',
      '14',
      '14.0.1',
      '14.1',
      '14.2',
      '14.2.1',
      '14.3',
      '14.4',
      '14.4.1',
      '14.4.2',
    ],
    Android: [
      '4.2.2',
      '4.3',
      '4.4.2',
      '4.4.4',
      '5',
      '5.0.1',
      '5.0.2',
      '5.1',
      '5.1.1',
      '6',
      '6.0.1',
      '7',
      '7.1.1',
      '7.1.2',
      '8.0.0',
      '8.1.0',
      '9',
      '10',
      '11',
    ],
  },
  location_list: [
    {
      value: 'af',
      text: 'Afghanistan',
    },
    {
      value: 'al',
      text: 'Albania',
    },
    {
      value: 'dz',
      text: 'Algeria',
    },
    {
      value: 'as',
      text: 'American Samoa',
    },
    {
      value: 'ad',
      text: 'Andorra',
    },
    {
      value: 'ao',
      text: 'Angola',
    },
    {
      value: 'ai',
      text: 'Anguilla',
    },
    {
      value: 'ag',
      text: 'Antigua and Barbuda',
    },
    {
      value: 'aq',
      text: 'aq',
    },
    {
      value: 'ar',
      text: 'Argentina',
    },
    {
      value: 'am',
      text: 'Armenia',
    },
    {
      value: 'aw',
      text: 'Aruba',
    },
    {
      value: 'au',
      text: 'Australia',
    },
    {
      value: 'at',
      text: 'Austria',
    },
    {
      value: 'ax',
      text: 'ax',
    },
    {
      value: 'az',
      text: 'Azerbaijan',
    },
    {
      value: 'bs',
      text: 'Bahamas (The)',
    },
    {
      value: 'bh',
      text: 'Bahrain',
    },
    {
      value: 'bd',
      text: 'Bangladesh',
    },
    {
      value: 'bb',
      text: 'Barbados',
    },
    {
      value: 'by',
      text: 'Belarus',
    },
    {
      value: 'be',
      text: 'Belgium',
    },
    {
      value: 'bz',
      text: 'Belize',
    },
    {
      value: 'bj',
      text: 'Benin',
    },
    {
      value: 'bm',
      text: 'Bermuda',
    },
    {
      value: 'bt',
      text: 'Bhutan',
    },
    {
      value: 'bl',
      text: 'bl',
    },
    {
      value: 'bo',
      text: 'Bolivia',
    },
    {
      value: 'ba',
      text: 'Bosnia and Herzegovina',
    },
    {
      value: 'bw',
      text: 'Botswana',
    },
    {
      value: 'br',
      text: 'Brazil',
    },
    {
      value: 'io',
      text: 'British Indian Ocean Territory (the)',
    },
    {
      value: 'bn',
      text: 'Brunei Darussalam',
    },
    {
      value: 'bg',
      text: 'Bulgaria',
    },
    {
      value: 'bf',
      text: 'Burkina Faso',
    },
    {
      value: 'bi',
      text: 'Burundi',
    },
    {
      value: 'bv',
      text: 'bv',
    },
    {
      value: 'kh',
      text: 'Cambodia',
    },
    {
      value: 'cm',
      text: 'Cameroon',
    },
    {
      value: 'ca',
      text: 'Canada',
    },
    {
      value: 'cv',
      text: 'Cape Verde',
    },
    {
      value: 'bq',
      text: 'Caribbean Netherlands',
    },
    {
      value: 'ky',
      text: 'Cayman Islands (the)',
    },
    {
      value: 'cc',
      text: 'cc',
    },
    {
      value: 'cf',
      text: 'Central African Republic (the)',
    },
    {
      value: 'td',
      text: 'Chad',
    },
    {
      value: 'cl',
      text: 'Chile',
    },
    {
      value: 'cn',
      text: 'China',
    },
    {
      value: 'co',
      text: 'Colombia',
    },
    {
      value: 'km',
      text: 'Comoros',
    },
    {
      value: 'ck',
      text: 'Cook Islands (the)',
    },
    {
      value: 'cr',
      text: 'Costa Rica',
    },
    {
      value: 'ci',
      text: 'Côte d\'Ivoire',
    },
    {
      value: 'hr',
      text: 'Croatia',
    },
    {
      value: 'cu',
      text: 'Cuba',
    },
    {
      value: 'cw',
      text: 'Curacao',
    },
    {
      value: 'cx',
      text: 'cx',
    },
    {
      value: 'cy',
      text: 'Cyprus',
    },
    {
      value: 'cz',
      text: 'Czech Republic (the)',
    },
    {
      value: 'cd',
      text: 'Democratic Republic of the Congo',
    },
    {
      value: 'dk',
      text: 'Denmark',
    },
    {
      value: 'dj',
      text: 'Djibouti',
    },
    {
      value: 'dm',
      text: 'Dominica',
    },
    {
      value: 'do',
      text: 'Dominican Republic (the)',
    },
    {
      value: 'ec',
      text: 'Ecuador',
    },
    {
      value: 'eg',
      text: 'Egypt',
    },
    {
      value: 'eh',
      text: 'eh',
    },
    {
      value: 'sv',
      text: 'El Salvador',
    },
    {
      value: 'gq',
      text: 'Equatorial Guinea',
    },
    {
      value: 'er',
      text: 'er',
    },
    {
      value: 'ee',
      text: 'Estonia',
    },
    {
      value: 'et',
      text: 'Ethiopia',
    },
    {
      value: 'fk',
      text: 'Falkland Islands',
    },
    {
      value: 'fo',
      text: 'Faroe Islands (the)',
    },
    {
      value: 'fj',
      text: 'Fiji',
    },
    {
      value: 'fi',
      text: 'Finland',
    },
    {
      value: 'fr',
      text: 'France',
    },
    {
      value: 'gf',
      text: 'French Guiana',
    },
    {
      value: 'pf',
      text: 'French Polynesia',
    },
    {
      value: 'ga',
      text: 'Gabon',
    },
    {
      value: 'gm',
      text: 'Gambia (The)',
    },
    {
      value: 'ge',
      text: 'Georgia',
    },
    {
      value: 'de',
      text: 'Germany',
    },
    {
      value: 'gh',
      text: 'Ghana',
    },
    {
      value: 'gi',
      text: 'Gibraltar',
    },
    {
      value: 'gr',
      text: 'Greece',
    },
    {
      value: 'gl',
      text: 'Greenland',
    },
    {
      value: 'gd',
      text: 'Grenada',
    },
    {
      value: 'gp',
      text: 'Guadeloupe',
    },
    {
      value: 'gu',
      text: 'Guam',
    },
    {
      value: 'gt',
      text: 'Guatemala',
    },
    {
      value: 'gg',
      text: 'Guernsey',
    },
    {
      value: 'gn',
      text: 'Guinea',
    },
    {
      value: 'gw',
      text: 'Guinea-Bissau',
    },
    {
      value: 'gy',
      text: 'Guyana',
    },
    {
      value: 'ht',
      text: 'Haiti',
    },
    {
      value: 'va',
      text: 'Holy See (the) [Vatican City State]',
    },
    {
      value: 'hn',
      text: 'Honduras',
    },
    {
      value: 'hk',
      text: 'Hong Kong',
    },
    {
      value: 'hu',
      text: 'Hungary',
    },
    {
      value: 'is',
      text: 'Iceland',
    },
    {
      value: 'in',
      text: 'India',
    },
    {
      value: 'id',
      text: 'Indonesia',
    },
    {
      value: 'ir',
      text: 'Iran (the Islamic Republic of)',
    },
    {
      value: 'iq',
      text: 'Iraq',
    },
    {
      value: 'ie',
      text: 'Ireland',
    },
    {
      value: 'im',
      text: 'Isle of Man',
    },
    {
      value: 'il',
      text: 'Israel',
    },
    {
      value: 'it',
      text: 'Italy',
    },
    {
      value: 'jm',
      text: 'Jamaica',
    },
    {
      value: 'jp',
      text: 'Japan',
    },
    {
      value: 'je',
      text: 'Jersey',
    },
    {
      value: 'jo',
      text: 'Jordan',
    },
    {
      value: 'kz',
      text: 'Kazakhstan',
    },
    {
      value: 'ke',
      text: 'Kenya',
    },
    {
      value: 'ki',
      text: 'Kiribati',
    },
    {
      value: 'kp',
      text: 'Korea (the Democratic People\'s Republic of)',
    },
    {
      value: 'kr',
      text: 'Korea (the Republic of)',
    },
    {
      value: 'kw',
      text: 'Kuwait',
    },
    {
      value: 'kg',
      text: 'Kyrgyzstan',
    },
    {
      value: 'la',
      text: 'Lao People\'s Democratic Republic (the)',
    },
    {
      value: 'lv',
      text: 'Latvia',
    },
    {
      value: 'lb',
      text: 'Lebanon',
    },
    {
      value: 'ls',
      text: 'Lesotho',
    },
    {
      value: 'lr',
      text: 'Liberia',
    },
    {
      value: 'ly',
      text: 'Libyan Arab Jamahiriya (the)',
    },
    {
      value: 'li',
      text: 'Liechtenstein',
    },
    {
      value: 'lt',
      text: 'Lithuania',
    },
    {
      value: 'lu',
      text: 'Luxembourg',
    },
    {
      value: 'mo',
      text: 'Macao',
    },
    {
      value: 'mg',
      text: 'Madagascar',
    },
    {
      value: 'mw',
      text: 'Malawi',
    },
    {
      value: 'my',
      text: 'Malaysia',
    },
    {
      value: 'mv',
      text: 'Maldives',
    },
    {
      value: 'ml',
      text: 'Mali',
    },
    {
      value: 'mt',
      text: 'Malta',
    },
    {
      value: 'mh',
      text: 'Marshall Islands (the)',
    },
    {
      value: 'mq',
      text: 'Martinique',
    },
    {
      value: 'mr',
      text: 'Mauritania',
    },
    {
      value: 'mu',
      text: 'Mauritius',
    },
    {
      value: 'yt',
      text: 'Mayotte',
    },
    {
      value: 'mx',
      text: 'Mexico',
    },
    {
      value: 'fm',
      text: 'Micronesia (the Federated States of)',
    },
    {
      value: 'md',
      text: 'Moldova (the Republic of)',
    },
    {
      value: 'mc',
      text: 'Monaco',
    },
    {
      value: 'mn',
      text: 'Mongolia',
    },
    {
      value: 'me',
      text: 'Montenegro',
    },
    {
      value: 'ma',
      text: 'Morocco',
    },
    {
      value: 'mz',
      text: 'Mozambique',
    },
    {
      value: 'ms',
      text: 'ms',
    },
    {
      value: 'mm',
      text: 'Myanmar',
    },
    {
      value: 'na',
      text: 'Namibia',
    },
    {
      value: 'nr',
      text: 'Nauru',
    },
    {
      value: 'np',
      text: 'Nepal',
    },
    {
      value: 'nl',
      text: 'Netherlands (the)',
    },
    {
      value: 'nc',
      text: 'New Caledonia',
    },
    {
      value: 'nz',
      text: 'New Zealand',
    },
    {
      value: 'ni',
      text: 'Nicaragua',
    },
    {
      value: 'ne',
      text: 'Niger (the)',
    },
    {
      value: 'ng',
      text: 'Nigeria',
    },
    {
      value: 'nf',
      text: 'Norfolk Island',
    },
    {
      value: 'mp',
      text: 'Northern Mariana Islands',
    },
    {
      value: 'no',
      text: 'Norway',
    },
    {
      value: 'nu',
      text: 'nu',
    },
    {
      value: 'om',
      text: 'Oman',
    },
    {
      value: 'pk',
      text: 'Pakistan',
    },
    {
      value: 'pw',
      text: 'Palau',
    },
    {
      value: 'ps',
      text: 'Palestinian Territory (the Occupied)',
    },
    {
      value: 'pa',
      text: 'Panama',
    },
    {
      value: 'pg',
      text: 'Papua New Guinea',
    },
    {
      value: 'py',
      text: 'Paraguay',
    },
    {
      value: 'pe',
      text: 'Peru',
    },
    {
      value: 'ph',
      text: 'Philippines (the)',
    },
    {
      value: 'pn',
      text: 'pn',
    },
    {
      value: 'pl',
      text: 'Poland',
    },
    {
      value: 'pt',
      text: 'Portugal',
    },
    {
      value: 'pr',
      text: 'Puerto Rico',
    },
    {
      value: 'qa',
      text: 'Qatar',
    },
    {
      value: 'mk',
      text: 'Republic of Macedonia (FYROM)',
    },
    {
      value: 'cg',
      text: 'Republic of the Congo',
    },
    {
      value: 're',
      text: 'Réunion',
    },
    {
      value: 'ro',
      text: 'Romania',
    },
    {
      value: 'ru',
      text: 'Russian Federation',
    },
    {
      value: 'rw',
      text: 'Rwanda',
    },
    {
      value: 'kn',
      text: 'Saint Kitts and Nevis',
    },
    {
      value: 'lc',
      text: 'Saint Lucia',
    },
    {
      value: 'mf',
      text: 'Saint Martin (France)',
    },
    {
      value: 'pm',
      text: 'Saint Pierre and Miquelon',
    },
    {
      value: 'vc',
      text: 'Saint Vincent and the Grenadines',
    },
    {
      value: 'ws',
      text: 'Samoa',
    },
    {
      value: 'sm',
      text: 'San Marino',
    },
    {
      value: 'st',
      text: 'Sao Tome and Principe',
    },
    {
      value: 'sa',
      text: 'Saudi Arabia',
    },
    {
      value: 'sn',
      text: 'Senegal',
    },
    {
      value: 'rs',
      text: 'Serbia',
    },
    {
      value: 'sc',
      text: 'Seychelles',
    },
    {
      value: 'sh',
      text: 'sh',
    },
    {
      value: 'sl',
      text: 'Sierra Leone',
    },
    {
      value: 'sg',
      text: 'Singapore',
    },
    {
      value: 'sx',
      text: 'Sint Maarten',
    },
    {
      value: 'sk',
      text: 'Slovakia',
    },
    {
      value: 'si',
      text: 'Slovenia',
    },
    {
      value: 'sb',
      text: 'Solomon Islands (the)',
    },
    {
      value: 'so',
      text: 'Somalia',
    },
    {
      value: 'za',
      text: 'South Africa',
    },
    {
      value: 'ss',
      text: 'South Sudan',
    },
    {
      value: 'es',
      text: 'Spain',
    },
    {
      value: 'lk',
      text: 'Sri Lanka',
    },
    {
      value: 'sd',
      text: 'Sudan (the)',
    },
    {
      value: 'sr',
      text: 'Suriname',
    },
    {
      value: 'sz',
      text: 'Swaziland',
    },
    {
      value: 'se',
      text: 'Sweden',
    },
    {
      value: 'ch',
      text: 'Switzerland',
    },
    {
      value: 'sy',
      text: 'Syrian Arab Republic (the)',
    },
    {
      value: 'tw',
      text: 'Taiwan (Province of China)',
    },
    {
      value: 'tj',
      text: 'Tajikistan',
    },
    {
      value: 'tz',
      text: 'Tanzania United Republic of',
    },
    {
      value: 'th',
      text: 'Thailand',
    },
    {
      value: 'tl',
      text: 'Timor-Leste',
    },
    {
      value: 'tg',
      text: 'Togo',
    },
    {
      value: 'tk',
      text: 'Tokelau',
    },
    {
      value: 'to',
      text: 'Tonga',
    },
    {
      value: 'tt',
      text: 'Trinidad and Tobago',
    },
    {
      value: 'tn',
      text: 'Tunisia',
    },
    {
      value: 'tr',
      text: 'Turkey',
    },
    {
      value: 'tm',
      text: 'Turkmenistan',
    },
    {
      value: 'tc',
      text: 'Turks and Caicos Islands (the)',
    },
    {
      value: 'tv',
      text: 'Tuvalu',
    },
    {
      value: 'ug',
      text: 'Uganda',
    },
    {
      value: 'ua',
      text: 'Ukraine',
    },
    {
      value: 'um',
      text: 'um',
    },
    {
      value: 'ae',
      text: 'United Arab Emirates (the)',
    },
    {
      value: 'gb',
      text: 'United Kingdom (the)',
    },
    {
      value: 'us',
      text: 'United States (the)',
    },
    {
      value: 'uy',
      text: 'Uruguay',
    },
    {
      value: 'uz',
      text: 'Uzbekistan',
    },
    {
      value: 'vu',
      text: 'Vanuatu',
    },
    {
      value: 've',
      text: 'Venezuela',
    },
    {
      value: 'vn',
      text: 'Viet Nam',
    },
    {
      value: 'vg',
      text: 'Virgin Islands (British)',
    },
    {
      value: 'vi',
      text: 'Virgin Islands (U.S.)',
    },
    {
      value: 'wf',
      text: 'Wallis and Futuna',
    },
    {
      value: 'ye',
      text: 'Yemen',
    },
    {
      value: 'zm',
      text: 'Zambia',
    },
    {
      value: 'zw',
      text: 'Zimbabwe',
    },
    {
      value: 'zz',
      text: 'zz',
    },
  ],
  language_list: [
    {
      value: 'zh',
      text: 'Chinese',
    },
    {
      value: 'da',
      text: 'Danish',
    },
    {
      value: 'en',
      text: 'English',
    },
    {
      value: 'fr',
      text: 'French',
    },
    {
      value: 'de',
      text: 'German',
    },
    {
      value: 'id',
      text: 'Indonesian',
    },
    {
      value: 'it',
      text: 'Italian',
    },
    {
      value: 'ja',
      text: 'Japanese',
    },
    {
      value: 'ko',
      text: 'Korean',
    },
    {
      value: 'pt',
      text: 'Portuguese',
    },
    {
      value: 'ro',
      text: 'Romanian',
    },
    {
      value: 'ru',
      text: 'Russian',
    },
    {
      value: 'es',
      text: 'Spanish',
    },
    {
      value: 'th',
      text: 'Thai',
    },
    {
      value: 'vi',
      text: 'Vietnamese',
    },
  ],
  media_list: [
    {
      id: 'Google',
      name: 'Google',
      text: 'Google',
      value: 'Google',
    },
    {
      id: 'Facebook',
      name: 'Facebook',
      text: 'Facebook',
      value: 'Facebook',
    },
    {
      id: 'Appsflyer',
      name: 'Appsflyer',
      text: 'Appsflyer',
      value: 'Appsflyer',
    },
    {
      id: 'Adjust',
      name: 'Adjust',
      text: 'Adjust',
      value: 'Adjust',
    },
    {
      id: 'TikTok',
      name: 'TikTok',
      text: 'TikTok',
      value: 'TikTok',
    },
    {
      id: 'Twitter',
      name: 'Twitter',
      text: 'Twitter',
      value: 'Twitter',
    },
  ],
  model_type_list: [
    {
      text: 'High Engagement Rate Audience',
      value: 'high_roi',
    },
    {
      text: 'User Activity Clusters',
      value: 'high_act',
    },
  ],
  high_engagement_list: [
    {
      text: 'Select One',
      value: '',
    },
    {
      text: 'Top 5% engagement rate',
      value: '5',
    },
    {
      text: 'Top 10% engagement rate',
      value: '10',
    },
    {
      text: 'Top 15% engagement rate',
      value: '15',
    },
    {
      text: 'Top 20% engagement rate',
      value: '20',
    },
    {
      text: 'Top 25% engagement rate',
      value: '25',
    },
    {
      text: 'Top 30% engagement rate',
      value: '30',
    },
    {
      text: 'Top 35% engagement rate',
      value: '35',
    },
    {
      text: 'Top 40% engagement rate',
      value: '40',
    },
    {
      text: 'Top 45% engagement rate',
      value: '45',
    },
    {
      text: 'Top 50% engagement rate',
      value: '50',
    },
    {
      text: 'Top 55% engagement rate',
      value: '55',
    },
    {
      text: 'Top 60% engagement rate',
      value: '60',
    },
    {
      text: 'Top 65% engagement rate',
      value: '65',
    },
    {
      text: 'Top 70% engagement rate',
      value: '70',
    },
    {
      text: 'Top 75% engagement rate',
      value: '75',
    },
    {
      text: 'Top 80% engagement rate',
      value: '80',
    },
    {
      text: 'Top 85% engagement rate',
      value: '85',
    },
    {
      text: 'Top 90% engagement rate',
      value: '90',
    },
    {
      text: 'Top 95% engagement rate',
      value: '95',
    },
    {
      text: 'Top 100% engagement rate',
      value: '100',
    },
  ],
  low_engagement_list: [
    {
      text: 'Select One',
      value: '',
    },
    {
      text: 'Top 5% low engagement rate',
      value: '5',
    },
    {
      text: 'Top 10% low engagement rate',
      value: '10',
    },
    {
      text: 'Top 15% low engagement rate',
      value: '15',
    },
    {
      text: 'Top 20% low engagement rate',
      value: '20',
    },
  ],
  high_act_list: [
    {
      text: 'High activity',
      value: '1',
    },
    {
      text: 'Activity decrease',
      value: '2',
    },
    {
      text: 'Activity increase',
      value: '3',
    },
    {
      text: 'Low activity',
      value: '4',
    },
  ],
  new_install_list: [
    {
      value: 'Install',
      text: 'Install',
    },
    {
      value: 'CPI',
      text: 'CPI',
    },
    {
      value: 'Register',
      text: 'Register',
    },
    {
      value: 'Retention',
      text: 'Retention',
    },
    {
      value: 'Purchase times',
      text: 'Purchase times',
    },
    {
      value: 'ROAS',
      text: 'ROAS',
    },
  ],
  re_list: [
    {
      value: 'Reattribution',
      text: 'Reattribution',
    },
    {
      value: 'CPR',
      text: 'CPR',
    },
    {
      value: 'Retention',
      text: 'Retention',
    },
    {
      value: 'Purchase times',
      text: 'Purchase times',
    },
    {
      value: 'ROAS',
      text: 'ROAS',
    },
  ],
  frequency_list: [
    {
      value: 'hourly',
      text: 'hourly',
    },
    {
      value: 'daily',
      text: 'daily',
    },
    {
      value: 'once',
      text: 'once',
    },
  ],
  frequency_list_obj: {
    default: [
      {
        value: 'hourly',
        text: 'hourly',
      },
      {
        value: 'daily',
        text: 'daily',
      },
    ],
    high_act_model_1: [
      {
        value: 'hourly',
        text: 'hourly',
      },
      {
        value: 'daily',
        text: 'daily',
      },
    ],
    high_act_model_2: [
      {
        value: 'hourly',
        text: 'hourly',
      },
      {
        value: 'daily',
        text: 'daily',
      },
    ],
    pay_model: [
      {
        value: 'hourly',
        text: 'hourly',
      },
      {
        value: 'daily',
        text: 'daily',
      },
    ],
    social_model: [
      {
        value: 'hourly',
        text: 'hourly',
      },
      {
        value: 'daily',
        text: 'daily',
      },
    ],
    mixed_newtutorial: [
      {
        value: 'hourly',
        text: 'hourly',
      },
      {
        value: 'daily',
        text: 'daily',
      },
    ],
    warmbattle: [
      {
        value: 'hourly',
        text: 'hourly',
      },
      {
        value: 'daily',
        text: 'daily',
      },
    ],
  },
  model_name_list: [
    /* {
      text: 'ua_pubgm_return_uace30_event_result_forGG',
      value: 'ua_pubgm_return_uace30_event_result_forGG',
      enable_value: 1,
    },
    {
      text: 'high_act_c_global',
      value: 'high_act_c_global',
      enable_value: 0,
    },
    {
      text: 'high_act_a_global',
      value: 'high_act_a_global',
      enable_value: 0,
    },
    {
      text: 'high_act_b_global',
      value: 'high_act_b_global',
      enable_value: 0,
    },
    {
      text: 'tmp_ios_firebase_backfill',
      value: 'tmp_ios_firebase_backfill',
      enable_value: 0,
    },
    {
      text: 'pubgm_uace30_instanceid',
      value: 'pubgm_uace30_instanceid',
      enable_value: 1,
    },
    {
      text: 'test_instance_id_mixed',
      value: 'test_instance_id_mixed',
      enable_value: 0,
    },
    {
      text: 'fb_exp_social_g1_221019',
      value: 'fb_exp_social_g1_221019',
      enable_value: 0,
    },
    {
      text: 'fb_exp_social_g2_221019',
      value: 'fb_exp_social_g2_221019',
      enable_value: 0,
    },
    {
      text: 'social_battle_xgb_v3',
      value: 'social_battle_xgb_v3',
      enable_value: 0,
    },
    {
      text: 'onlinetime_decrease_model',
      value: 'onlinetime_decrease_model',
      enable_value: 0,
    },
    {
      text: 'high_act_playerlang_en',
      value: 'high_act_playerlang_en',
      enable_value: 0,
    },
    {
      text: 'high_act_playerlang_tr',
      value: 'high_act_playerlang_tr',
      enable_value: 0,
    },
    {
      text: 'high_act_playerlang_fr',
      value: 'high_act_playerlang_fr',
      enable_value: 0,
    },
    {
      text: 'high_act_playerlang_ru',
      value: 'high_act_playerlang_ru',
      enable_value: 0,
    },
    {
      text: 'high_act_playerlang_ar',
      value: 'high_act_playerlang_ar',
      enable_value: 0,
    },
    {
      text: 'high_act_playerlang_zh',
      value: 'high_act_playerlang_zh',
      enable_value: 0,
    },
    {
      text: 'loading_time_high',
      value: 'loading_time_high',
      enable_value: 0,
    },
    {
      text: 'mid_ping_rule',
      value: 'mid_ping_rule',
      enable_value: 0,
    },
    {
      text: 'pubgm_top_return_prob_crowd_20_50',
      value: 'pubgm_top_return_prob_crowd_20_50',
      enable_value: 0,
    },
    {
      text: 'pubgm_uplift_prechurn_top50_level_10_ios',
      value: 'pubgm_uplift_prechurn_top50_level_10_ios',
      enable_value: 0,
    },
    {
      text: 'reinstall_hourly',
      value: 'reinstall_hourly',
      enable_value: 0,
    },
    {
      text: 'pubgm_churn_after_prechurn_7day_crowd',
      value: 'pubgm_churn_after_prechurn_7day_crowd',
      enable_value: 0,
    },
    {
      text: 'pubgm_uninstall_users_crowd',
      value: 'pubgm_uninstall_users_crowd',
      enable_value: 0,
    },
    {
      text: 'pubgm_uninstall_users_crowd_sa',
      value: 'pubgm_uninstall_users_crowd_sa',
      enable_value: 0,
    },
    {
      text: 'pubgm_uninstall_users_crowd_br',
      value: 'pubgm_uninstall_users_crowd_br',
      enable_value: 0,
    },
    {
      text: 'pubgm_uninstall_users_crowd_eg',
      value: 'pubgm_uninstall_users_crowd_eg',
      enable_value: 0,
    },
    {
      text: 'pubgm_uninstall_users_crowd_id',
      value: 'pubgm_uninstall_users_crowd_id',
      enable_value: 0,
    },
    {
      text: 'pubgm_uninstall_users_crowd_mx',
      value: 'pubgm_uninstall_users_crowd_mx',
      enable_value: 0,
    },
    {
      text: 'pubgm_uninstall_users_crowd_my',
      value: 'pubgm_uninstall_users_crowd_my',
      enable_value: 0,
    },
    {
      text: 'pubgm_uninstall_users_crowd_pk',
      value: 'pubgm_uninstall_users_crowd_pk',
      enable_value: 0,
    },
    {
      text: 'pubgm_uninstall_users_crowd_th',
      value: 'pubgm_uninstall_users_crowd_th',
      enable_value: 0,
    },
    {
      text: 'pubgm_uninstall_users_crowd_us',
      value: 'pubgm_uninstall_users_crowd_us',
      enable_value: 0,
    },
    {
      text: 'active_uac3',
      value: 'active_uac3',
      enable_value: 1,
    },
    {
      text: 'pubgm_top_return_prob_crowd_top30',
      value: 'pubgm_top_return_prob_crowd_top30',
      enable_value: 0,
    },
    {
      text: 'pubgm_top_return_prob_crowd_top31_60',
      value: 'pubgm_top_return_prob_crowd_top31_60',
      enable_value: 0,
    },
    {
      text: 'pubgm_top_return_prob_crowd_top61_100',
      value: 'pubgm_top_return_prob_crowd_top61_100',
      enable_value: 0,
    },
    {
      text: 'pay_uac3',
      value: 'pay_uac3',
      enable_value: 1,
    },
    {
      text: 'high_act_a_instance_id',
      value: 'high_act_a_instance_id',
      enable_value: 0,
    },
    {
      text: 'high_act_b_instance_id',
      value: 'high_act_b_instance_id',
      enable_value: 0,
    },
    {
      text: 'high_act_c_instance_id',
      value: 'high_act_c_instance_id',
      enable_value: 0,
    },
    {
      text: 'full_new_v2',
      value: 'full_new_v2',
      enable_value: 0,
    },
    {
      text: 'test_instance_id_idfaonly',
      value: 'test_instance_id_idfaonly',
      enable_value: 0,
    },
    {
      text: 'high_act_playerlang_de',
      value: 'high_act_playerlang_de',
      enable_value: 0,
    },
    {
      text: 'weapon',
      value: 'weapon',
      enable_value: 0,
    },
    {
      text: 'pubgm_uninstall_users_crowd_iq',
      value: 'pubgm_uninstall_users_crowd_iq',
      enable_value: 0,
    },
    {
      text: 'pubgm_uninstall_users_crowd_tr',
      value: 'pubgm_uninstall_users_crowd_tr',
      enable_value: 0,
    },
    {
      text: 'high_smart_global_instance_id',
      value: 'high_smart_global_instance_id',
      enable_value: 0,
    },
    {
      text: 'social',
      value: 'social',
      enable_value: 0,
    },
    {
      text: 'high_act',
      value: 'high_act',
      enable_value: 0,
    },
    {
      text: 'churn_high_active',
      value: 'churn_high_active',
      enable_value: 0,
    },
    {
      text: 'return_uace30',
      value: 'return_uace30',
      enable_value: 1,
    },
    {
      text: 'thirduc_migrate',
      value: 'thirduc_migrate',
      enable_value: 0,
    },
    {
      text: 'high_act_es_c',
      value: 'high_act_es_c',
      enable_value: 0,
    },
    {
      text: 'ios_churn_top30',
      value: 'ios_churn_top30',
      enable_value: 0,
    },
    {
      text: 'churn_top_users_low_level',
      value: 'churn_top_users_low_level',
      enable_value: 1,
    },
    {
      text: 'weapon_full_old',
      value: 'weapon_full_old',
      enable_value: 0,
    },
    {
      text: 'weapon_full_new',
      value: 'weapon_full_new',
      enable_value: 0,
    },
    {
      text: 'churn_top_users_high_level',
      value: 'churn_top_users_high_level',
      enable_value: 1,
    },
    {
      text: 'churn_top_ad_return_model',
      value: 'churn_top_ad_return_model',
      enable_value: 0,
    },
    {
      text: 'pubgm_uplift_prechurn_top50_level_10_20',
      value: 'pubgm_uplift_prechurn_top50_level_10_20',
      enable_value: 0,
    },
    {
      text: 'pubgm_uplift_prechurn_top31_50_level_20',
      value: 'pubgm_uplift_prechurn_top31_50_level_20',
      enable_value: 0,
    },
    {
      text: 'churn_top30',
      value: 'churn_top30',
      enable_value: 0,
    },
    {
      text: 'high_act_LAL',
      value: 'high_act_LAL',
      enable_value: 0,
    },
    {
      text: 'high_act_LAL_group1',
      value: 'high_act_LAL_group1',
      enable_value: 0,
    },
    {
      text: 'high_act_LAL_group2',
      value: 'high_act_LAL_group2',
      enable_value: 0,
    },
    {
      text: 'mid_smart_global_instance_id',
      value: 'mid_smart_global_instance_id',
      enable_value: 0,
    },
    {
      text: 'high_smart_sea_instance_id',
      value: 'high_smart_sea_instance_id',
      enable_value: 0,
    },
    {
      text: 'mid_smart_sea_instance_id',
      value: 'mid_smart_sea_instance_id',
      enable_value: 0,
    },
    {
      text: 'newbie_guide_metro',
      value: 'newbie_guide_metro',
      enable_value: 0,
    },
    {
      text: 'pubgm_gen_revenue_churn_users_crowd',
      value: 'pubgm_gen_revenue_churn_users_crowd',
      enable_value: 1,
    },
    {
      text: 'pubgm_uace30_revenue_instanceid',
      value: 'pubgm_uace30_revenue_instanceid',
      enable_value: 0,
    },
    {
      text: 'pubgm_my_anti_churn_after_holiday',
      value: 'pubgm_my_anti_churn_after_holiday',
      enable_value: 0,
    },
    {
      text: 'pubgm_model_hftncituol6mz6wstc525g',
      value: 'pubgm_model_hftncituol6mz6wstc525g',
      enable_value: 0,
    },
    {
      text: 'pubgm_high_difficulty_return_crowd',
      value: 'pubgm_high_difficulty_return_crowd',
      enable_value: 0,
    },
    {
      text: 'pubgm_revenue_prechurn_top30_level20',
      value: 'pubgm_revenue_prechurn_top30_level20',
      enable_value: 0,
    },
    {
      text: 'pubgm_active_users_hourly',
      value: 'pubgm_active_users_hourly',
      enable_value: 0,
    },
    {
      text: 'new_potential_pay_global_all_230512',
      value: 'new_potential_pay_global_all_230512',
      enable_value: 0,
    },
    {
      text: 'new_pp_global_all_230515',
      value: 'new_pp_global_all_230515',
      enable_value: 0,
    },
    {
      text: 'new_shapp_global_all_230515',
      value: 'new_shapp_global_all_230515',
      enable_value: 0,
    },
    {
      text: 'pubgm_model_5xclrrsfmzq3cizj_fzqsq',
      value: 'pubgm_model_5xclrrsfmzq3cizj_fzqsq',
      enable_value: 0,
    },
    {
      text: 'pubgm_prechurn_reactive_crowd',
      value: 'pubgm_prechurn_reactive_crowd',
      enable_value: 0,
    },
    {
      text: 'pubgm_aix_audience_builder_exclude_list_d',
      value: 'pubgm_aix_audience_builder_exclude_list_d',
      enable_value: 0,
    },
    {
      text: 'pubgm_model_gqv8mfqxmrm_9wtctmxnsw',
      value: 'pubgm_model_gqv8mfqxmrm_9wtctmxnsw',
      enable_value: 0,
    },
    {
      text: 'weapon_pure_new',
      value: 'weapon_pure_new',
      enable_value: 0,
    },
    {
      text: 'pubgm_new_os_patch_global_all_230313',
      value: 'pubgm_new_os_patch_global_all_230313',
      enable_value: 0,
    },
    {
      text: 'pubgm_250_reattr_high_potential_top20_daily_audience',
      value: 'pubgm_250_reattr_high_potential_top20_daily_audience',
      enable_value: 0,
    },
    {
      text: 'pubgm_250_reattr_rp_daily_audience',
      value: 'pubgm_250_reattr_rp_daily_audience',
      enable_value: 0,
    },
    {
      text: 'pubgm_250_reattr_treasure_box_daily_audience',
      value: 'pubgm_250_reattr_treasure_box_daily_audience',
      enable_value: 0,
    },
    {
      text: 'pubgm_250_reattr_spin_daily_audience',
      value: 'pubgm_250_reattr_spin_daily_audience',
      enable_value: 0,
    }, */
    {
      text: 'high_act',
      value: 'high_act',
      enable_value: 0,
    },
    {
      text: 'high_act_LAL',
      value: 'high_act_LAL',
      enable_value: 0,
    },
    {
      text: 'pay_uac3',
      value: 'pay_uac3',
      enable_value: 0,
    },
    {
      text: 'social',
      value: 'social',
      enable_value: 0,
    },
  ],
  profile_value_list: {
    pubgm: [
      {
        value: 'technical ',
        text: '社交型',
      },
      {
        value: 'Killers',
        text: '杀手型',
      },
      {
        value: 'marginal ',
        text: '边缘型 ',
      },
      {
        value: 'explore',
        text: '探索型',
      },
      {
        value: 'achievement',
        text: '成就型',
      },
    ],
  },
  created_by_list: [
    {
      text: 'Modeling',
      value: 'modeling',
    },
    {
      text: 'Rule-based',
      value: 'rules',
    },
    {
      text: 'Custom Table',
      value: 'sql',
    },
  ],
  audience_type_list: {
    Facebook: [
      {
        text: 'Event',
        value: 'event',
      },
      {
        text: 'Audience',
        value: 'custom_audience',
      },
    ],
    Google: [
      {
        text: 'Event',
        value: 'event',
      },
      {
        text: 'Audience',
        value: 'retargeting',
      },
    ],
    Adjust: [
      {
        text: 'Event',
        value: 'event',
      },
      {
        text: 'Audience',
        value: 'retargeting',
        disabled: true,
      },
    ],
    Appsflyer: [
      {
        text: 'Event',
        value: 'event',
      },
      {
        text: 'Audience',
        value: 'retargeting',
        disabled: true,
      },
    ],
    TikTok: [
      {
        text: 'Event',
        value: 'event',
      },
      {
        text: 'Audience',
        value: 'retargeting',
      },
    ],
    Twitter: [
      {
        text: 'Event',
        value: 'event',
      },
      {
        text: 'Audience',
        value: 'retargeting',
        disabled: true,
      },
    ],
  },
  app_token_obj: {
    Android: 'p4y2qfrbb9j4',
    iOS: '33osj816m3wg',
  },
  audience_server_map: {
    optimize_retarget: {
      1: {
        target: 'new_install',
        target_code: 'new',
        child_target: 'Retention',
      },
      2: {
        target: 'new_install',
        target_code: 'new',
        child_target: 'Purchase times',
      },
      3: {
        target: 're_attribution',
        target_code: 're',
        child_target: 'Reattribution',
      },
    },
    media_type: {
      1: 'Google',
    },
    audience_type: {
      1: 'event',
    },
    frequency: {
      1: 'daily',
    },
    created_by: {
      1: 'modeling',
      2: 'rules',
    },
    high_type: {
      1: 'high_act',
      2: 'paymodel',
      3: 'highattribution',
    },
    os: {
      AND: 'Android',
      IOS: 'iOS',
    },
  },
  account_obj: {
    Google: [
      {
        id: '**********',
        name: '980-904-4543',
      },
      {
        id: '**********',
        name: 'LH-PUBG-0318-iOS',
      },
      {
        id: '**********',
        name: 'PUBGM-BRANDING',
      },
      {
        id: '**********',
        name: 'PUBGM-电竞',
      },
      {
        id: '**********',
        name: 'PUGM-TW-UACE',
      },
      {
        id: '**********',
        name: 'Tencent-0919',
      },
      {
        id: '**********',
        name: 'tencent-0720-1',
      },
    ],
    Facebook: [
      {
        id: '****************',
        name: 'bluefocus-pubgm-SEA',
      },
      {
        id: '***************',
        name: 'bluefocus-pubgm-MENA',
      },
      {
        id: '***************',
        name: 'bluefocus-pubgm-HQ',
      },
      {
        id: '****************',
        name: 'bluefocus-pubgm-MENA',
      },
      {
        id: '***************',
        name: 'bluefocus-pubgm-OTHERS',
      },
      {
        id: '5656576781059117',
        name: 'bluefocus-pubgm-SEA',
      },
      {
        id: '1634320866665982',
        name: 'iGame其他',
      },
      {
        id: '1634320869999315',
        name: 'iGame亚洲区',
      },
      {
        id: '****************',
        name: 'iGame欧洲区',
      },
      {
        id: '****************',
        name: 'iGame美洲区',
      },
      {
        id: '****************',
        name: 'iGame-印度01',
      },
      {
        id: '***************',
        name: 'iGame-台湾',
      },
      {
        id: '****************',
        name: 'iGame-HQ',
      },
      {
        id: '****************',
        name: 'igame-TH',
      },
      {
        id: '***************',
        name: 'igame account 1',
      },
      {
        id: '***************',
        name: 'igame account 2',
      },
      {
        id: '****************',
        name: 'igame account 3',
      },
      {
        id: '***************',
        name: 'igame account 4',
      },
    ],
  },
  root_account_obj: {
    Google: [
      {
        id: '**********',
        name: 'GG MCC',
      },
    ],
    Facebook: [
      {
        id: '****************',
        name: 'FB BM',
      },
    ],
  },
  has_advanced_action: ['aov_nsa', 'pubgm', 'mmp', 'mmp_hmt', 'mmp_usa', 'nikke', 'nikke_hmt'],
  has_profile: [],
  advanced_action_list: [
    {
      text: 'Purchase times',
      value: 'purchase_times',
      min: 0,
      max: ********,
    },
    {
      text: 'Purchase value',
      value: 'purchase_amount',
      before: '$',
      min: 0,
      max: ********.99,
      precision: 2,
    },
  ],
  send_audience_obj: {
    ads: {
      label: 'Sync with the Ad Platform',
      desc: 'Sync the audience/event you\'ve created with the ad platform, which can be used when creating a new campaign.',
    },
    campaign: {
      label: 'Create a Campaign',
      desc: 'Use this audience/event to create a campaign that will be directly published on the ad platform.',
      disabled: {
        media: 'Adjust',
      },
    },
  },
  has_test: [
    'aov_nsa',
    'pubgm',
    'mmp',
    'mmp_usa',
    'mmp_hmt',
    'streetfighter',
    'sskjp',
    'apgame_hk',
    'crafantacy_tw',
    'crafantacy',
  ],
  not_has_popup: ['aov_nsa', 'pubgm', 'mmp', 'streetfighter', 'sskjp', 'apgame_hk'],
  has_combine_list: [],
  os_map_obj: {
    Android: ['Android', 'android', 'ANDROID'],
    iOS: ['iOS', 'ios', 'IOS'],
  },
  score_list: [
    {
      value: 0,
      text: 'top0%',
    },
    {
      value: 1,
      text: 'top1%',
    },
    {
      value: 2,
      text: 'top2%',
    },
    {
      value: 3,
      text: 'top3%',
    },
    {
      value: 4,
      text: 'top4%',
    },
    {
      value: 5,
      text: 'top5%',
    },
    {
      value: 6,
      text: 'top6%',
    },
    {
      value: 7,
      text: 'top7%',
    },
    {
      value: 8,
      text: 'top8%',
    },
    {
      value: 9,
      text: 'top9%',
    },
    {
      value: 10,
      text: 'top10%',
    },
    {
      value: 11,
      text: 'top11%',
    },
    {
      value: 12,
      text: 'top12%',
    },
    {
      value: 13,
      text: 'top13%',
    },
    {
      value: 14,
      text: 'top14%',
    },
    {
      value: 15,
      text: 'top15%',
    },
    {
      value: 16,
      text: 'top16%',
    },
    {
      value: 17,
      text: 'top17%',
    },
    {
      value: 18,
      text: 'top18%',
    },
    {
      value: 19,
      text: 'top19%',
    },
    {
      value: 20,
      text: 'top20%',
    },
    {
      value: 21,
      text: 'top21%',
    },
    {
      value: 22,
      text: 'top22%',
    },
    {
      value: 23,
      text: 'top23%',
    },
    {
      value: 24,
      text: 'top24%',
    },
    {
      value: 25,
      text: 'top25%',
    },
    {
      value: 26,
      text: 'top26%',
    },
    {
      value: 27,
      text: 'top27%',
    },
    {
      value: 28,
      text: 'top28%',
    },
    {
      value: 29,
      text: 'top29%',
    },
    {
      value: 30,
      text: 'top30%',
    },
    {
      value: 31,
      text: 'top31%',
    },
    {
      value: 32,
      text: 'top32%',
    },
    {
      value: 33,
      text: 'top33%',
    },
    {
      value: 34,
      text: 'top34%',
    },
    {
      value: 35,
      text: 'top35%',
    },
    {
      value: 36,
      text: 'top36%',
    },
    {
      value: 37,
      text: 'top37%',
    },
    {
      value: 38,
      text: 'top38%',
    },
    {
      value: 39,
      text: 'top39%',
    },
    {
      value: 40,
      text: 'top40%',
    },
    {
      value: 41,
      text: 'top41%',
    },
    {
      value: 42,
      text: 'top42%',
    },
    {
      value: 43,
      text: 'top43%',
    },
    {
      value: 44,
      text: 'top44%',
    },
    {
      value: 45,
      text: 'top45%',
    },
    {
      value: 46,
      text: 'top46%',
    },
    {
      value: 47,
      text: 'top47%',
    },
    {
      value: 48,
      text: 'top48%',
    },
    {
      value: 49,
      text: 'top49%',
    },
    {
      value: 50,
      text: 'top50%',
    },
    {
      value: 51,
      text: 'top51%',
    },
    {
      value: 52,
      text: 'top52%',
    },
    {
      value: 53,
      text: 'top53%',
    },
    {
      value: 54,
      text: 'top54%',
    },
    {
      value: 55,
      text: 'top55%',
    },
    {
      value: 56,
      text: 'top56%',
    },
    {
      value: 57,
      text: 'top57%',
    },
    {
      value: 58,
      text: 'top58%',
    },
    {
      value: 59,
      text: 'top59%',
    },
    {
      value: 60,
      text: 'top60%',
    },
    {
      value: 61,
      text: 'top61%',
    },
    {
      value: 62,
      text: 'top62%',
    },
    {
      value: 63,
      text: 'top63%',
    },
    {
      value: 64,
      text: 'top64%',
    },
    {
      value: 65,
      text: 'top65%',
    },
    {
      value: 66,
      text: 'top66%',
    },
    {
      value: 67,
      text: 'top67%',
    },
    {
      value: 68,
      text: 'top68%',
    },
    {
      value: 69,
      text: 'top69%',
    },
    {
      value: 70,
      text: 'top70%',
    },
    {
      value: 71,
      text: 'top71%',
    },
    {
      value: 72,
      text: 'top72%',
    },
    {
      value: 73,
      text: 'top73%',
    },
    {
      value: 74,
      text: 'top74%',
    },
    {
      value: 75,
      text: 'top75%',
    },
    {
      value: 76,
      text: 'top76%',
    },
    {
      value: 77,
      text: 'top77%',
    },
    {
      value: 78,
      text: 'top78%',
    },
    {
      value: 79,
      text: 'top79%',
    },
    {
      value: 80,
      text: 'top80%',
    },
    {
      value: 81,
      text: 'top81%',
    },
    {
      value: 82,
      text: 'top82%',
    },
    {
      value: 83,
      text: 'top83%',
    },
    {
      value: 84,
      text: 'top84%',
    },
    {
      value: 85,
      text: 'top85%',
    },
    {
      value: 86,
      text: 'top86%',
    },
    {
      value: 87,
      text: 'top87%',
    },
    {
      value: 88,
      text: 'top88%',
    },
    {
      value: 89,
      text: 'top89%',
    },
    {
      value: 90,
      text: 'top90%',
    },
    {
      value: 91,
      text: 'top91%',
    },
    {
      value: 92,
      text: 'top92%',
    },
    {
      value: 93,
      text: 'top93%',
    },
    {
      value: 94,
      text: 'top94%',
    },
    {
      value: 95,
      text: 'top95%',
    },
    {
      value: 96,
      text: 'top96%',
    },
    {
      value: 97,
      text: 'top97%',
    },
    {
      value: 98,
      text: 'top98%',
    },
    {
      value: 99,
      text: 'top99%',
    },
    {
      value: 100,
      text: 'top100%',
    },
  ],
  custom_table_list: [
    /* {
      remark: 'test123',
      source_table: 'app_pubgm_full_brazil_user_loss_register_8d_to_30d',
    },
    {
      remark: '',
      source_table: 'app_pubgm_full_brazil_user_loss_register_90d',
    },
    {
      remark: '',
      source_table: 'app_pubgm_full_brazil_user_loss_register_d',
    },
    {
      remark: '',
      source_table: 'app_pubgm_full_mexico_user_loss_register_8d_to_30d',
    },
    {
      remark: '',
      source_table: 'app_pubgm_full_mexico_user_loss_register_90d',
    },
    {
      remark: '',
      source_table: 'app_pubgm_full_mexico_user_loss_register_d',
    },
    {
      remark: '',
      source_table: 'app_pubgm_full_ssa_user_loss_register_8d_to_30d',
    },
    {
      remark: '',
      source_table: 'app_pubgm_full_ssa_user_loss_register_90d',
    },
    {
      remark: '',
      source_table: 'app_pubgm_full_ssa_user_loss_register_d',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_audience_for_exploration_test_exclude_left',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_audience_for_exploration_test_include_left',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_for_exploration_5_groups_group_1',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_for_exploration_5_groups_group_2',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_for_exploration_5_groups_group_3',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_for_exploration_5_groups_group_4',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_for_exploration_5_groups_group_5',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_for_exploration_6_groups_group_1',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_for_exploration_8_groups_group_1',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_for_exploration_8_groups_group_2',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_for_exploration_8_groups_group_3',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_for_exploration_8_groups_group_4',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_for_exploration_8_groups_group_5',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_for_exploration_8_groups_group_6',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_for_exploration_8_groups_group_7',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_for_exploration_8_groups_group_8',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_for_exploration_group_1',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_v2_group_1',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_v2_group_2_segment_0',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_v2_group_2_segment_1',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_v2_group_2_segment_2',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_v2_group_2_segment_3',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_v2_group_2_segment_4',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_v2_group_2_segment_5',
    },
    {
      remark: '',
      source_table: 'churned_ua_day_split_audience_v2_group_2_segment_left',
    },
    {
      remark: '第二轮rp稳活abc合并包',
      source_table: 'pubg_2_active_non_rp_audience_d',
    },
    {
      remark: '第二轮rp回流ab合并包',
      source_table: 'pubg_2_churn_ab_non_rp_audience_d',
    },
    {
      remark: '第二轮rp回流c合并包',
      source_table: 'pubg_2_churn_c_non_rp_audience_d',
    },
    {
      remark: '活跃nonrp_uc等于0',
      source_table: 'pubg_active_nonrp_uc_is_0_audience_d',
    },
    {
      remark: '活跃rp_uc大于0',
      source_table: 'pubg_active_rp_uc_over_0_audience_d',
    },
    {
      remark: '260回流人群包合并',
      source_table: 'pubg_churn_260_merge_audience_d',
    },
    {
      remark: '流失rp_a_nonrp_b',
      source_table: 'pubg_churn_nonrp_a_rp_b_audience_d',
    },
    {
      remark: '流失rp_b',
      source_table: 'pubg_churn_rp_b_audience_d',
    },
    {
      remark: '5/21之后购买rp',
      source_table: 'pubg_rp_after_521_audience_d',
    },
    {
      remark: '5/21之后购买rp_小时回传',
      source_table: 'pubg_rp_after_521_audience_h',
    },
    {
      remark: '6/21之后购买rp_小时回传',
      source_table: 'pubg_rp_after_621_audience_h',
    },
    {
      remark: '周年庆活跃流失',
      source_table: 'pubgm_250_reattr_anniversary_churned_audience',
    },
    {
      remark: '周年庆付过费用户',
      source_table: 'pubgm_250_reattr_anniversary_paid_audience',
    },
    {
      remark: '周年庆付费活跃流失',
      source_table: 'pubgm_250_reattr_anniversary_paid_churned_audience',
    },
    {
      remark: '周年庆付费静默',
      source_table: 'pubgm_250_reattr_anniversary_recent_10_90days_non_paid_audience',
    },
    {
      remark: '社交流失',
      source_table: 'pubgm_250_reattr_high_social_audience',
    },
    {
      remark: '七日日活非R高潜top10%',
      source_table: 'pubgm_250_reattr_hp_model_top10_audience',
    },
    {
      remark: '七日日活非R高潜top15%',
      source_table: 'pubgm_250_reattr_hp_model_top15_audience',
    },
    {
      remark: '七日日活非R高潜top20%',
      source_table: 'pubgm_250_reattr_hp_model_top20_audience',
    },
    {
      remark: '过去两月活跃过但目前已流失高潜top20%',
      source_table: 'pubgm_250_reattr_hp_model_top20_churned_audience',
    },
    {
      remark: '过去两月活跃过但目前已流失高潜top20%',
      source_table: 'pubgm_250_reattr_hp_model_top20_new_audience',
    },
    {
      remark: '七日日活非R高潜top5%',
      source_table: 'pubgm_250_reattr_hp_model_top5_audience',
    },
    {
      remark: '道具标签种子包',
      source_table: 'pubgm_250_reattr_item_target_audience',
    },
    {
      remark: '斋月活跃用户（补充 ID MY TR PK）',
      source_table: 'pubgm_250_reattr_ramadan_active_add_audience',
    },
    {
      remark: '斋月活跃流失',
      source_table: 'pubgm_250_reattr_ramadan_churned_audience',
    },
    {
      remark: '斋月付费用户（补充 ID MY TR PK）',
      source_table: 'pubgm_250_reattr_ramadan_paid_add_audience',
    },
    {
      remark: '斋月庆付过费用户',
      source_table: 'pubgm_250_reattr_ramadan_paid_audience',
    },
    {
      remark: '斋月庆付费活跃流失',
      source_table: 'pubgm_250_reattr_ramadan_paid_churned_audience',
    },
    {
      remark: '斋月付费静默用户（补充 ID MY TR PK）',
      source_table: 'pubgm_250_reattr_ramadan_recent_10_90days_non_paid_add_audience',
    },
    {
      remark: '斋月庆付费静默',
      source_table: 'pubgm_250_reattr_ramadan_recent_10_90days_non_paid_audience',
    },
    {
      remark: '消费第一偏好：RP',
      source_table: 'pubgm_250_reattr_rp_audience',
    },
    {
      remark: '消费第一偏好：转盘',
      source_table: 'pubgm_250_reattr_spin_audience',
    },
    {
      remark: '圣装/跑车用户',
      source_table: 'pubgm_250_reattr_suit_car_audience',
    },
    {
      remark: '消费第一偏好：宝箱',
      source_table: 'pubgm_250_reattr_treasure_box_audience',
    },
    {
      remark: 'pubgm 世界杯期间流失用户定义- 在11.14-12.7期间有活跃行为并且连续七天未活跃的devices',
      source_table: 'pubgm_aix_S2S_all_churned_device_1mth_d',
    },
    {
      remark: '',
      source_table: 'pubgm_aix_S2S_churned_exlude_highadreturn_result_d',
    },
    {
      remark: 'alick instance ci test',
      source_table: 'pubgm_alick_instanceid_test',
    },
    {
      remark: '',
      source_table: 'pubgm_bp_30days_once',
    },
    {
      remark: '',
      source_table: 'pubgm_bp_60days_once',
    },
    {
      remark: '',
      source_table: 'pubgm_bp_90days_once',
    },
    {
      remark: '高潜付费标签',
      source_table: 'pubgm_churn_high_potential_audience_d',
    },
    {
      remark: '社交偏好标签',
      source_table: 'pubgm_churn_high_social_audience_d',
    },
    {
      remark: '道具偏好标签',
      source_table: 'pubgm_churn_item_targeted_260_audience_d',
    },
    {
      remark: '',
      source_table: 'pubgm_eg_reattri180_0708',
    },
    {
      remark: '',
      source_table: 'pubgm_eg_reattri500_0708',
    },
    {
      remark: '',
      source_table: 'pubgm_meta_rf_churned_users_split_audience_v1_group_1',
    },
    {
      remark: '',
      source_table: 'pubgm_meta_rf_churned_users_split_audience_v1_group_2',
    },
    {
      remark: 'meta rf回流测试包',
      source_table: 'pubgm_meta_rf_churned_users_split_audience_v2',
    },
    {
      remark: '',
      source_table: 'pubgm_meta_rf_churned_users_split_audience_v2_group_1',
    },
    {
      remark: '',
      source_table: 'pubgm_meta_rf_churned_users_split_audience_v2_group_2',
    },
    {
      remark: 'meta rf回流测试包v3',
      source_table: 'pubgm_meta_rf_churned_users_split_audience_v3',
    },
    {
      remark: 'meta rf回流测试包v3 group1',
      source_table: 'pubgm_meta_rf_churned_users_split_audience_v3_group_1',
    },
    {
      remark: 'meta rf回流测试包v3 group2',
      source_table: 'pubgm_meta_rf_churned_users_split_audience_v3_group_2',
    },
    {
      remark: 'meta rf回流测试包v3 group3',
      source_table: 'pubgm_meta_rf_churned_users_split_audience_v3_group_3',
    },
    {
      remark: '',
      source_table: 'pubgm_reattribution_ua_total',
    },
    {
      remark: '',
      source_table: 'pubgm_reattribution_ua_total_level11_20',
    },
    {
      remark: '',
      source_table: 'pubgm_reattribution_ua_total_level21_30',
    },
    {
      remark: '',
      source_table: 'pubgm_reattribution_ua_total_level31_40',
    },
    {
      remark: 'gg',
      source_table: 'pubgm_reattribution_ua_total_level41plus',
    },
    {
      remark: '',
      source_table: 'pubgm_sa_reattri180_0708',
    },
    {
      remark: '',
      source_table: 'pubgm_sa_reattri500_0708',
    },
    {
      remark: 'pubgm v230 发版本拉回流，临时种子包',
      source_table: 'pubgm_tmp_20221201_v230_a',
    },
    {
      remark: '',
      source_table: 'pubgm_tmp_20221201_v230_b',
    },
    {
      remark: '',
      source_table: 'pubgm_tmp_20221201_v230_c',
    },
    {
      remark: '',
      source_table: 'pubgm_uninstall_analyse_one_month_half_1',
    },
    {
      remark: '',
      source_table: 'pubgm_uninstall_analyse_one_month_half_2',
    },
    {
      remark: '',
      source_table: 'pubgm_uninstall_analyse_one_year_half_1',
    },
    {
      remark: '',
      source_table: 'pubgm_uninstall_analyse_one_year_half_2',
    },
    {
      remark: '',
      source_table: 'ua_pubgm_weapon_rank_google_result_h',
    },
    {
      remark: '',
      source_table: 'ua_reattr_10countries_long_churned_21_63d_facebook_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_10countries_long_churned_21_63d_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_10countries_long_churned_top80_21_63d_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_all_friendlapsing_balanced_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_all_friendlapsing_gangqiang_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_all_friendlapsing_gouju_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_all_otherreason_balanced_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_all_otherreason_gangqiang_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_all_otherreason_gouju_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_all_performancedecline_balanced_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_all_performancedecline_gangqiang_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_all_performancedecline_gouju_google_result',
    },
    {
      remark: 'test',
      source_table: 'ua_reattr_eg_id_iq_my_tr_long_churned_21d_exclude_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_eg_id_iq_my_tr_long_churned_21d_google_result',
    },
    {
      remark: 'test1234',
      source_table: 'ua_reattr_eg_id_iq_my_tr_long_churned_35d_exclude_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_eg_id_iq_my_tr_long_churned_35d_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_eg_id_iq_my_tr_long_churned_49d_exclude_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_eg_id_iq_my_tr_long_churned_49d_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_eg_id_iq_my_tr_long_churned_63d_exclude_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_eg_id_iq_my_tr_long_churned_63d_google_result',
    },
    {
      remark: '',
      source_table: 'ua_reattr_eg_id_iq_my_tr_long_churned_exclude_google_result',
    }, */
    {
      remark: '',
      source_table: 'churned_audience_group_1',
    },
    {
      remark: '',
      source_table: 'churned_audience_group_2',
    },
    {
      remark: '',
      source_table: 'churned_audience_group_3',
    },
  ],

  TikTok: [
    {
      label: 'Add Payment Info',
      value: 'Add Payment Info',
    },
    {
      label: 'Add To Cart',
      value: 'Add To Cart',
    },
    {
      label: 'Add To Wishlist',
      value: 'Add To Wishlist',
    },
    {
      label: 'Click Button',
      value: 'Click Button',
    },
    {
      label: 'Complete Payment',
      value: 'Complete Payment',
    },
    {
      label: 'CompleteRegistration',
      value: 'CompleteRegistration',
    },
    {
      label: 'Contact',
      value: 'Contact',
    },
    {
      label: 'Download',
      value: 'Download',
    },
    {
      label: 'Initiate Checkout',
      value: 'Initiate Checkout',
    },
    {
      label: 'Place An Order',
      value: 'Place An Order',
    },
    {
      label: 'Search',
      value: 'Search',
    },
    {
      label: 'Submit Form',
      value: 'Submit Form',
    },
    {
      label: 'Subscribe',
      value: 'Subscribe',
    },
    {
      label: 'View Content',
      value: 'View Content',
    },
  ],
};
