/**
 * @description demo游戏，列表数据为mock；
 *
 * 0、新增表单页中， 所有下拉列表需调整为mock数据。
 * 1、操作新增时，数据存储到本地localStorage中，成功之后返回列表页，列表页需多一条数据
 *
 */
export const getMockOverviewData = (game: string) => {
  const os = game === 'demo' ? 'Web' : 'Android';
  return {
    table: [
      {
        ad_account_id: '***************',
        api_version: 'v3',
        app_token: '',
        block_alarms: 0,
        channel_size: '1000-1000',
        child_target: 'Reattribution',
        country: [],
        createby: 'sql',
        created_time: '2023-06-03 10:16:53',
        creator: 'tester',
        event_token: '',
        first_open_time: '',
        game_code: 'pubgm',
        id: 'audience_89ndozjjpgqqngh4gypk_a',
        id_type: 'ads_id',
        media: 'Facebook',
        model_name: '',
        name: 'meta_rf_glb_audience_0601_group_2_seg_0',
        open_event_value: 0,
        open_test: 0,
        os,
        percent_score: 0,
        percent_score_lower: 0,
        remark: '',
        rules: {},
        status: 'Pending',
        sub_id_type: '',
        table_name: 'churned_ua_day_split_audience_v2_group_2_segment_0',
        target: 're_attribution',
        test_param: '100',
        type: 'custom_audience',
        update_frequency: 'once',
        update_time: '2023-06-03 10:16:53',
        updator: '',
        upload_size: 0,
        user_ttl: 0,
        type_text: 'Audience',
        createby_text: 'Custom Table',
      },
      {
        ad_account_id: '***************',
        api_version: 'v3',
        app_token: '',
        block_alarms: 0,
        channel_size: '1800000-2100000',
        child_target: 'Reattribution',
        country: [],
        createby: 'sql',
        created_time: '2023-06-03 10:17:18',
        creator: 'tester',
        event_token: '',
        first_open_time: '',
        game_code: 'pubgm',
        id: 'audience_ipg02lh_n_ipdch4kmpexg',
        id_type: 'ads_id',
        media: 'Facebook',
        model_name: '',
        name: 'meta_rf_glb_audience_0601_group_2_seg_1',
        open_event_value: 0,
        open_test: 0,
        os,
        percent_score: 0,
        percent_score_lower: 0,
        remark: '',
        rules: {},
        status: 'Eligible',
        sub_id_type: '',
        table_name: 'churned_ua_day_split_audience_v2_group_2_segment_1',
        target: 're_attribution',
        test_param: '100',
        type: 'custom_audience',
        update_frequency: 'once',
        update_time: '2023-06-03 10:17:18',
        updator: '',
        upload_size: 6617508,
        user_ttl: 0,
        type_text: 'Audience',
        createby_text: 'Custom Table',
      },
      {
        ad_account_id: '***************',
        api_version: 'v3',
        app_token: '',
        block_alarms: 0,
        channel_size: '3900000-4600000',
        child_target: 'Reattribution',
        country: [],
        createby: 'sql',
        created_time: '2023-06-03 10:17:42',
        creator: 'tester',
        event_token: '',
        first_open_time: '',
        game_code: 'pubgm',
        id: 'audience_rps_x5ggm_megulb5uuucq',
        id_type: 'ads_id',
        media: 'Facebook',
        model_name: '',
        name: 'meta_rf_glb_audience_0601_group_2_seg_2',
        open_event_value: 0,
        open_test: 0,
        os,
        percent_score: 0,
        percent_score_lower: 0,
        remark: '',
        rules: {},
        status: 'Eligible',
        sub_id_type: '',
        table_name: 'churned_ua_day_split_audience_v2_group_2_segment_2',
        target: 're_attribution',
        test_param: '100',
        type: 'custom_audience',
        update_frequency: 'once',
        update_time: '2023-06-03 10:17:42',
        updator: '',
        upload_size: 9034353,
        user_ttl: 0,
        type_text: 'Audience',
        createby_text: 'Custom Table',
      },
      {
        ad_account_id: '***************',
        api_version: 'v3',
        app_token: '',
        block_alarms: 0,
        channel_size: '3100000-3600000',
        child_target: 'Reattribution',
        country: [],
        createby: 'sql',
        created_time: '2023-06-03 10:18:02',
        creator: 'tester',
        event_token: '',
        first_open_time: '',
        game_code: 'pubgm',
        id: 'audience_rp7qc6wipiux9i7e7vioig',
        id_type: 'ads_id',
        media: 'Facebook',
        model_name: '',
        name: 'meta_rf_glb_audience_0601_group_2_seg_3',
        open_event_value: 0,
        open_test: 0,
        os,
        percent_score: 0,
        percent_score_lower: 0,
        remark: '',
        rules: {},
        status: 'Eligible',
        sub_id_type: '',
        table_name: 'churned_ua_day_split_audience_v2_group_2_segment_3',
        target: 're_attribution',
        test_param: '100',
        type: 'custom_audience',
        update_frequency: 'once',
        update_time: '2023-06-03 10:18:02',
        updator: '',
        upload_size: 8940989,
        user_ttl: 0,
        type_text: 'Audience',
        createby_text: 'Custom Table',
      },
      {
        ad_account_id: '***************',
        api_version: 'v3',
        app_token: '',
        block_alarms: 0,
        channel_size: '5800000-6900000',
        child_target: 'Reattribution',
        country: [],
        createby: 'sql',
        created_time: '2023-06-03 10:18:24',
        creator: 'tester',
        event_token: '',
        first_open_time: '',
        game_code: 'pubgm',
        id: 'audience_z45jqditmtq6ohkinoqeqq',
        id_type: 'ads_id',
        media: 'Facebook',
        model_name: '',
        name: 'meta_rf_glb_audience_0601_group_2_seg_4',
        open_event_value: 0,
        open_test: 0,
        os,
        percent_score: 0,
        percent_score_lower: 0,
        remark: '',
        rules: {},
        status: 'Eligible',
        sub_id_type: '',
        table_name: 'churned_ua_day_split_audience_v2_group_2_segment_4',
        target: 're_attribution',
        test_param: '100',
        type: 'custom_audience',
        update_frequency: 'once',
        update_time: '2023-06-03 10:18:24',
        updator: '',
        upload_size: 19816754,
        user_ttl: 0,
        type_text: 'Audience',
        createby_text: 'Custom Table',
      },
    ],
    typeObj: {
      lookalike: {
        text: 'Lookalike Audience',
        games: ['sskjp'],
      },
      retargeting: {
        text: 'Retargeting Audience',
        games: ['sskjp'],
      },
      retargeting_sql: {
        text: 'Retargeting Audience By Sql',
        games: ['pubgm', 'pubgm_put', 'pubgm_data'],
      },
    },
    tableTabs: [
      {
        id: 'user_list',
        label: 'Audience List',
        type: 'user_list',
      },
    ],
    columnsOption: [
      {
        key: 'name',
        title: 'Audience Name',
        width: 270,
      },
      {
        key: 'remark',
        title: 'Audience Description',
        width: 270,
      },
      {
        key: 'creator',
        title: 'Creator',
        format: 'string',
      },
      {
        key: 'type_text',
        title: 'Type',
        format: 'string',
      },
      {
        key: 'createby_text',
        title: 'Created By',
        format: 'string',
      },
      {
        key: 'os',
        title: 'Data source',
        format: 'string',
      },
      {
        key: 'media',
        title: 'Media',
        format: 'string',
      },
      {
        key: 'status',
        title: 'Status',
        format: 'string',
      },
      {
        key: 'created_time',
        title: 'Date Created',
        formatter: 'date|YYYY-MM-DD',
      },
      {
        key: 'upload_size',
        title: 'Audience Size',
        format: 'number',
      },
      {
        key: 'channel_size',
        title: 'Channel Size',
        format: 'number',
        tips: 'Channel Size is applicable for:\n- Event/audience created with Advertising ID and uploaded to Google\n- Audience created with Advertising ID and uploaded to Facebook\nOthers will be displayed as N/A.',
      },
    ],
    filterOption: {
      mediaOpt: [
        {
          text: 'Google',
          value: 'Google',
        },
        {
          text: 'Facebook',
          value: 'Facebook',
        },
        {
          text: 'TikTok',
          value: 'TikTok',
        },
        {
          text: 'Twitter',
          value: 'Twitter',
        },
        // {
        //   text: 'Appsflyer',
        //   value: 'Appsflyer',
        // },
        // {
        //   text: 'Adjust',
        //   value: 'Adjust',
        // },
      ],
      createdByOpt: [
        {
          text: 'Modeling',
          value: 'Modeling',
        },
        // {
        //   text: 'Rule-based',
        //   value: 'Rule-based',
        // },
        {
          text: 'Custom Table',
          value: 'Custom Table',
        },
      ],
      statusOpt: [
        {
          text: 'Pending',
          value: 'Pending',
        },
        {
          text: 'Paused',
          value: 'Paused',
        },
        {
          text: 'Delivery error',
          value: 'Delivery error',
        },
        {
          text: 'Eligible',
          value: 'Eligible',
        },
      ],
      tagOpt: [
        {
          value: 'open_test',
          text: 'Lab audience',
        },
        {
          value: 'open_event_value',
          text: 'Event with value',
        },
      ],
      typeOpt: [
        {
          value: 'Event',
          text: 'Event',
        },
        {
          value: 'Audience',
          text: 'Audience',
        },
      ],
      platformOpt: [
        {
          value: 'iOS',
          text: 'iOS',
        },
        {
          value: 'Android',
          text: 'Android',
        },
        {
          value: 'Web',
          text: 'Web',
        },
      ],
      blockAlarmsOpt: [
        {
          text: 'True',
          value: 1,
        },
        {
          text: 'False',
          value: 0,
        },
      ],
    },
  };
};
