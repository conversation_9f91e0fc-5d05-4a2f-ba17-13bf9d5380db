
import type { IFilterOptionItem } from 'common/service/audience/overview/type';
import type { IOptionItem } from '../type';
import { isArray } from 'lodash-es';

export function transformOptions(list: IFilterOptionItem[]): IOptionItem[] {
  if (!isArray(list)) return [];
  return list.map(item => ({
    label: item.text,
    value: item.value,
  }));
}

export function transformOptionsArguments(list: IFilterOptionItem[]): IOptionItem[] {
  if (!isArray(list)) return [];
  return list.map(item => ({
    ...item,
    label: item.text,
    value: item.value,
  }));
}
