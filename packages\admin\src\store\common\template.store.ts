/**
 * CommonView Template Parsing View
 * @auth: <EMAIL>
 * @create_date: 2023-07-01
 */
import { STORE_KEY } from '@/config/config';
import { defineStore } from 'pinia';
import { computed, ComputedRef, Ref, ref, shallowReactive, unref, watch } from 'vue';
import { AxiosMethod } from 'common/service/backendApi/base';
import TemplateParsingEngine, { fetchOptions, $TYPE, isCanEmit, modifyApiTemplate } from 'common/utils/template/templateParsingEngine';
import { isPlainObject, merge, pick, debounce, cloneDeep, DebouncedFunc } from 'lodash-es';
import { useGlobalGameStore } from '@/store/global/game.store';
import { COMPONENTS_MAP } from 'common/const/components';
import { IFormDynamicItem } from 'common/components/FormContainer';
import DynamicContainer from 'common/components/DynamicContainer';
import { TEmitOptions } from 'common/utils/template/type';
import templateCfg from '../../config/template/index';
import { NotifyPlugin } from 'tdesign-vue-next';

type TTabItem = {
  id: string;
  name: string;
  game: string;
  system: string;
  type: AxiosMethod;
  fold_list: string[];
  param: Record<string, unknown>;
};

export type TInitParams = {
  commonParams?: Record<string, unknown>;
  tabSelectId?: string;
  game: string;
  payload?: Record<string, unknown>;
};

const ALL_GAME_FLAG = 'allgame';
const ADD_FETCH_DATA_COMP_LIST = ['business-table'];
const ADD_FORM_MODEL_VALUE = ['business-chart'];
// const getPageCfg = (key: string) => new Promise((resolve) => {
//   setTimeout(() => {
//     resolve({});
//     throw new Error(`TODO：通过固定接口获取pageCfg，并且接口得添加cache: ${key}`);
//   }, 0);
// });

export const useCommonTemplateStore = defineStore(STORE_KEY.COMMON_TEMPLATE, () => {
  const gameStore = useGlobalGameStore();
  // --------- Data With CurRouter Status ---------
  const engine = new TemplateParsingEngine();
  const cacheByGame: { [game: string]: Map<any, any> } = {};
  const storeData = shallowReactive<{
    tabList: Ref<TTabItem[] | null> | undefined;
    pageCfg: Record<string, any>;
  }>({
    tabList: undefined,
    pageCfg: {},
  });
  const pageViewKey = computed(() => storeData.pageCfg.store.view_key);
  const tabSelectId = ref('');
  const isPageIniting = ref<boolean>(true);
  const setPageIniting = (status: boolean) => {
    isPageIniting.value = status;
  };
  // --------- Data Auto Change Status ---------
  const tabList = computed(() => {
    const list = (Array.isArray(storeData.tabList?.value) ? storeData.tabList?.value : [])
      ?.filter(item => !item.game || item.game === ALL_GAME_FLAG || item.game === gameStore.gameCode)
      ?.map(item => ({
        id: item.id,
        game: item.game || ALL_GAME_FLAG,
        value: item.id,
        label: item.name,
        type: item.type,
      }));
    return list ?? [];
  });
  const curTabCfg = computed(() => {
    const item = storeData.tabList?.value?.find?.(item => item.id === tabSelectId.value);
    return item;
  });
  const formModelValue = ref<Record<string, any>>({});
  const setFormModelValue = (v: Record<string, any>) => formModelValue.value = v;
  const foldList = computed(() => curTabCfg?.value?.fold_list);
  const formEmitStatusList: Ref<Array<ComputedRef<boolean>>> = ref([]);
  const formList = computed<IFormDynamicItem[]>(() => {
    const curTabFormData: any = curTabCfg?.value?.param?.form || {};
    const dependToEmitList: Record<string, Array<DebouncedFunc<any>>> = {};
    formEmitStatusList.value = []; // Clear
    const list = storeData.pageCfg?.form?.list?.map((item: any) => {
      // - - - - - - - - Init Component - - - - - - - -
      let component = (COMPONENTS_MAP as any)[item.name];
      if (!component) {
        component = item.name;
        console.warn(`Form Component is not found in cfg: ${item.name}`);
        return undefined;
      }
      // - - - - - - - - Init Props (SourceData) - - - - - - - -
      const hasDepend = Array.isArray(item.ext.dependKeyList);
      const emitPropsNameList = Array.isArray(item.ext.emitPropsNameList) ? item.ext.emitPropsNameList : [];
      const key = item?.ext?.key;

      const props = (() => {
        const props = item.props || {};
        return Object.keys(props).reduce((record, propsKey) => {
          let template = props[propsKey];
          // 当有依赖项时，不立即触发emit，因为这时候触发的emit不会带上依赖项
          if (hasDepend && isCanEmit(template)) {
            template = modifyApiTemplate(template, { options: { immediately: false } });
          }
          const { value, ext = {} } = engine.use$TemplateParse(template);
          const hasEmit = isCanEmit(template) && typeof ext?.emit === 'function';
          const isEmitPropsName = emitPropsNameList.includes(propsKey);

          if (hasEmit) {
            formEmitStatusList.value.push(ext?.loading);
            if (isEmitPropsName) {
              ext.emit._debounceEmit ??= debounce(ext.emit, 1000);
              dependToEmitList[key] ??= [];
              dependToEmitList[key].push(ext.emit._debounceEmit);
            }
          }

          return {
            ...record,
            [propsKey]: value,
          };
        }, {});
      })();
      // - - - - - - - - Init ModelValue - - - - - - -
      const defaultValue = item.ext.default
        ? engine.use$TemplateParse(item.ext.default, { value: curTabFormData })?.value
        : curTabFormData?.[item.ext.key];
      // - - - - - - - - Gen Final Cfg - - - - - - -
      const cfg = {
        name: component,
        props,
        ext: {
          ...(item.ext || {}),
          default: defaultValue, // default by useFetchWrapper
          onDependChange: hasDepend
            ? function ({ modelValue, isFlush = false }: any) {
              const payload = pick(modelValue, [...item.ext.dependKeyList]);
              dependToEmitList[key].forEach((emit) => {
                emit(payload);
                // emit 由上面debounce生成，所以emit的触发会有防抖延迟，flush可以无视延迟立即触发
                // flush 为 true 时，立即触发emit
                // 场景：当页面初始化触发时，emit由于加了防抖会延迟触发（此时请求的上下文如 body.where.game=A）
                //      此时如果用户切换了游戏（url.game从A变为B），会导致createAxios中取url.game变为了切换后的游戏b
                //      导致后台接口校验game一致性不通过，此时可以将 isFlush 为 true 让页面初始化时立即触发emit
                isFlush && emit.flush();
              });
            }
            : undefined,
        },
      };
      return cfg;
    });
    return list ?? [];
  });

  // 进入页面时触发
  const createWatch = () => {
    // 监听formList初始化，如果配置了dependKeyList，则立即触发onDependChange事件
    const formListStop = watch(formList, (newFormList) => {
      // 还在加载getView时不触发其他请求
      if (unref(isPageIniting)) return;
      newFormList.forEach((item) => {
        if (item.ext?.dependKeyList?.length > 0) {
          const modelValue = curTabCfg?.value?.param?.form;
          // 立即触发emit
          modelValue && item.ext?.onDependChange?.({ modelValue, isFlush: true });
        }
      });
    }, { immediate: true });
    return [formListStop];
  };

  const parseModCfg = (item: any, ctx: any) => {
    // - - - - - - - - Init Component - - - - - - - -
    let component = (COMPONENTS_MAP as any)[item.name];
    if (!component) {
      component = item.name;
      console.warn(`Form Component is not found in cfg: ${item.name}, it will be treated as an HTML default tag`);
      // return undefined;
    }
    const { curTabFormData, shadowChildCfgList, isFromChildren } = ctx;
    // - - - - - - - - Init Props (SourceData && modelValue)- - - - - - - -
    const ext = (isPlainObject(item?.ext) ? item.ext : {}) as {
      key: string; emitPropsNameList: string[];
    };
    const { key, emitPropsNameList = [] } = ext;
    const modelValue = ref(curTabFormData?.[key] ? curTabFormData?.[key] : {}); // Gen ModelValue By Key
    const emitList: Function[] = [];
    const loadingStatusList: Array<ComputedRef<boolean>> = [];
    const props = (() => {
      const props = item.props || {};
      const finalProps: Record<string, any> = Object.keys(props).reduce((record, propsName) => {
        let template = props[propsName];

        if (propsName === 'extFormList' && Array.isArray(template)) {
          template = [{
            ...item.props[propsName][0],
            name: template.map((item: any) => (COMPONENTS_MAP as any)[item.name] || item.name)[0],
          }];
        }
        const isInEmitPropsNameList = emitPropsNameList?.includes(propsName);
        const { isReturnEmit: isEmitByManual, finalTemplate } = (() => {
          const defaultRet = {
            isReturnEmit: false,
            finalTemplate: template,
          };
          if (!isInEmitPropsNameList || !isCanEmit(template)) return defaultRet;
          switch (template?.$) {
            case $TYPE.api: {
              const templateValue = {
                ...(template?.value || {}),
                options: (template?.value?.options || fetchOptions.manual), // Delay to emit
              };
              return {
                isReturnEmit: true,
                finalTemplate: {
                  ...template,
                  value: templateValue,
                },
              };
            };
            case $TYPE.queue: {
              const extendIndex = template?.value?.findIndex((item: any) => item?.$ === $TYPE.extend);
              const isReturnEmit = extendIndex > -1;
              const templateValue = !isReturnEmit
                ? template.value
                : template.value.map((item: any, index: number) => {
                  if (index !== extendIndex) return item;
                  return {
                    ...item,
                    value: {
                      ...(item?.value || {}),
                      options: (item?.value?.options || fetchOptions.manual), // Delay to emit
                    },
                  };
                });
              return {
                isReturnEmit,
                finalTemplate: {
                  ...template,
                  value: templateValue,
                },
              };
            }
            default: return defaultRet;
          }
        })();
        const { value, ext } = engine.use$TemplateParse(finalTemplate);
        if (isEmitByManual && typeof ext?.emit === 'function') {
          emitList.push(ext.emit); // Collect the update emit event
          loadingStatusList?.push(ext.loading);
        }
        return {
          ...record,
          [propsName]: value,
        };
      }, {
        outsideFormModelValue: ADD_FORM_MODEL_VALUE.includes(item.name) ? formModelValue.value : undefined,
        fetchDataList: ADD_FETCH_DATA_COMP_LIST.includes(item.name)
          ? (newModelValue: any) => Promise.all(emitList.map(emitFn => new Promise((resolve) => {
            const payload = cloneDeep({
              where: { ...unref(formModelValue.value) },
              ...unref(newModelValue),
            });
            emitFn(payload, (data: any) => {
              resolve(data);
            });
          })))
          : undefined,
      });
      if (loadingStatusList.length) {
        finalProps.isLoading = computed(() => loadingStatusList.some(isLoading => isLoading?.value));
      }
      return finalProps;
    })();
    // - - - - - - - - Gen Final Cfg - - - - - - -
    const childCfgList = item?.children?.map((child: any) => parseModCfg(child, { ...ctx, isFromChildren: true }));
    const childProps = {
      children: Array.isArray(childCfgList) ? {
        name: DynamicContainer,
        props: {
          list: childCfgList,
        },
      } : undefined,
    };
    const extendCfg = (!!key && emitList.length) ? {
      ext: {
        emitList,
      },
      emit: {
        'onUpdate:modelValue': (value: any) => (modelValue.value
          ? (modelValue.value = value)
          : console.warn(`onUpdate:modelValue fail, bcz modelValue is undefined which key = ${key}`)),
      },
      props: { modelValue },
    } : {};
    const cfg = merge({}, {
      ...item,
      name: component,
      props,
      ...childProps,
    }, extendCfg);

    if (Array.isArray(shadowChildCfgList) && isFromChildren
      && cfg?.props?.modelValue && cfg?.ext?.emitList?.length > 0) {
      shadowChildCfgList.push({
        name: 'div',
        props: {
          style: {
            display: 'none',
          },
          'data-key': `common-template-shadow-node-${cfg.ext?.key}`,
          modelValue: cfg.props.modelValue,
        },
        ext: cfg.ext,
      });
    }

    return cfg;
  };
  const modList = computed<IFormDynamicItem[]>(() => {
    const curTabFormData: any = curTabCfg?.value?.param || {};
    const footCfg = {
      name: 'div',
      props: {
        style: {
          'min-height': '50px',
          width: '100%',
        },
      },
    };
    const shadowChildCfgList: any[] = [footCfg];
    const list = storeData.pageCfg?.mod?.list
      ?.map((item: any) => {
        // const isLatest = storeData.pageCfg.mod.list.length - 1 === index;
        const itemWithSpace = { // Add spacing between mods
          ...item,
          props: {
            ...(item?.props || {}),
            style: {
              marginBottom: '24px', // isLatest ? '50px' : '24px',
              ...(item?.props?.style || {}),
            },
          },
        };
        const cfg = parseModCfg(itemWithSpace, { curTabFormData, shadowChildCfgList });
        return cfg;
      });
    const res = (list ?? []).concat(shadowChildCfgList);
    return res;
  });

  // @ts-ignore
  window.__testModList__ = modList; // 临时把该变量暴露，定位问题后再删除

  const modModelValue = computed(() => {
    const result = modList.value?.reduce((record, item) => {
      const modelValue = item?.props?.modelValue;
      const key = item?.ext?.key;
      return key
        ? {
          ...record,
          [key]: modelValue,
        }
        : record;
    }, {}) ?? {};
    return result;
  });
  let tabSelectIdCache: string | undefined;
  async function init(
    this: any,
    keyOrPageCfg: Record<string, unknown> | string,
    params: TInitParams = { game: '' },
    emitOptionsCtx?: TEmitOptions,
    emitCb?: Function,
  ) {
    const { game } = params;
    if (!game) throw new Error('game empty');
    setPageIniting(true);

    tabSelectIdCache = params?.tabSelectId;
    const cacheKey = keyOrPageCfg;
    cacheByGame[game] ??= new Map();
    const cache = cacheByGame[game];
    const hasInit = cache.has(cacheKey);
    if (!hasInit) {
      // ----------- Get Page JSON Cfg ------------
      const isStardCfg = isPlainObject(keyOrPageCfg);
      const originalPageCfg: any = isStardCfg ? keyOrPageCfg : (templateCfg[keyOrPageCfg as string]);
      if (!isPlainObject(originalPageCfg)) {
        NotifyPlugin.error({
          duration: 0,
          closeBtn: true,
          title: 'Configuration missing',
          content: 'Please check the game type.',
        });
        throw new Error('Configuration missing, please check the game type.');
      }

      const commonParams = {
        game: gameStore.gameCode,
        ...(params?.commonParams ?? {}) as Object,
      };
      const engineParams = {
        commonParams,
      };
      const pageCfg: any = engine.init(originalPageCfg, engineParams);

      // ----------- Init Api ------------
      const template = pageCfg.event.init;
      if (![$TYPE.api].includes(template?.$)) {
        throw new Error('init fail, unknown type', template);
      }
      const initCfg = {
        commonParams: {
          callback: (rawData: TTabItem[]) => {
            storeData.tabList = data as Ref<TTabItem[]>;
            // storeData.pageCfg = pageCfg;
            // getView接口完成后，将结果存入storeData.pageCfg，重新初始化一次模板
            // 例：自定义campaign配置的版本 custom_campaign_version 依赖 getView 接口返回的数据
            storeData.pageCfg = engine.init(pageCfg, {
              ...engineParams,
              cur_view: rawData?.[0],
            }, false); // reInit
            tabSelectId.value = tabSelectIdCache ?? rawData?.[0]?.id ?? '';
            setPageIniting(false);
          },
        },
        options: {
          ...fetchOptions.manual,
          storage: (payload: any) => {
            const newPayload = Object.assign(
              {},
              isPlainObject(payload) ? payload : {},
              { url: template?.value?.url },
            );
            return JSON.stringify(newPayload);
          },
        },
      };
      const { value: data, ext = {} } = engine.use$TemplateParse({
        ...template,
        value: {
          ...(template?.value || {}),
          ...initCfg,
          ...({
            payload: {
              ...(template?.value?.payload || {}),
              id: tabSelectIdCache,
            },
          }),
        },
      });
      // ----------- Cache API Event ------------
      const { emit } = ext;
      cache.set(cacheKey, {
        emit,
        pageCfg,
        engineParams,
      });
    }
    // ----------- Fetch TabView Data Api ------------
    const { emit, pageCfg, engineParams }: any = cache.get(cacheKey);
    storeData.pageCfg = pageCfg;
    hasInit && engine.setCtx(pageCfg, engineParams); // reInit
    emit({}, emitCb, emitOptionsCtx);
  };
  return {
    pageViewKey,
    tabSelectId,
    tabList,
    formList,
    foldList,
    modList,
    formEmitStatusList,
    setFormModelValue,
    isPageIniting,
    setPageIniting,
    modModelValue,
    storeData,
    init,
    createWatch,
  };
});
