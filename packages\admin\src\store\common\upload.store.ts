import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { useLoading } from 'common/compose/loading';
import { uploadUserExcel } from 'common/service/intelligence/user/user';
import { AllCountryModal, ExcelFormat } from 'common/service/intelligence/user/user.d';
import { useWatchGameChange } from 'common/compose/request/game';
import { ref } from 'vue';
import { useGlobalGameStore } from '@/store/global/game.store';
import { getAllCountry } from 'common/service/intelligence/common/common';
import { uploadChannelExcel } from 'common/service/intelligence/market/channel/channel';
import { ChannelExcelFormat } from 'common/service/intelligence/market/channel/channel.d';

export const useIntelligenceUploadStore = defineStore(STORE_KEY.INTELLIGENCE.USER_UPLOAD, () => {
  const { isLoading, hideLoading, showLoading } = useLoading();
  const result = ref([]); // 处理过后的表格的数据
  const countryList = ref<AllCountryModal>([]);
  const gameStore = useGlobalGameStore();
  const failTotal = ref<number>(0); // 有多少条校验不通过的数据？


  async function getCountry() {
    const data = await getAllCountry({ game: gameStore.gameCode });
    return data;
  }

  async function uploadUserDataExcel(excelData: ExcelFormat, sheetName: string) {
    showLoading();
    const data = await uploadUserExcel({ excelData, sheetName });
    hideLoading();
    return data;
  }

  async function uploadChannelDataExcel(excelData: ChannelExcelFormat, country: string) {
    showLoading();
    const data = await uploadChannelExcel({ data: excelData, country });
    hideLoading();
    return data;
  }

  // ---------------------------- 初始化 ------------------------------
  async function init() {
    useWatchGameChange(async () => {
      try {
        showLoading();
        const response = await getCountry();
        countryList.value = response.default;
      } catch (error) {
        // Handle errors here
      } finally {
        hideLoading();
      }
    });
  }


  return {
    init,
    result,
    failTotal,
    countryList,
    uploadUserDataExcel,
    uploadChannelDataExcel,
    isLoading,
    hideLoading,
    showLoading,
  };
});
