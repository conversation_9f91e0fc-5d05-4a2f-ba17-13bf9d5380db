import { Ref, ref, computed } from 'vue';
import { COMPONENTS_MAP } from 'common/const/components';
import { FirstLabelType } from 'common/service/creative/common/type';
import { AccountsItem, ChannelTypes } from 'common/service/configuration/adaccounts/type';
export const getAccountFilterConfig = (
  channelLists?: Ref<ChannelTypes[] | null>,
  data?: Ref<AccountsItem[] | null>,
) => {
  const labelTypeList = ref<FirstLabelType[]>([]); // 表格顶部的筛选
  const totalLabelList = ref<string[]>([]); // 一级-二级标签列表
  const accountIdList = ref<Record<string, any>[]>([]);
  const accountNameList = ref<Record<string, any>[]>([]);
  const channelList = ref<Record<string, any>[]>([]);

  const createAccountList = (key: string) => {
    if (data?.value) {
      const newDataList = data.value.filter(item => (item as any)[key]);
      const optionList = Array.from(new Set(newDataList.map(item => (item as any)[key])));
      return optionList.map(item => ({
        label: item,
        value: item,
      }));
    }
    return [];
  };

  const createChannelList = () => {
    const channelData = createAccountList('channel');
    if (channelLists?.value) {
      return channelLists?.value.map(item => ({
        label: item.display_name,
        value: item.channel_name.toLocaleLowerCase(),
      }));
    }
    return channelData.map((item: Record<string, string>) => ({
      ...item,
      label: item.label.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase()),
    }));
  };

  function initOptions() {
    accountIdList.value = createAccountList('account_id');
    accountNameList.value = createAccountList('account_name');
    channelList.value = createChannelList();
  }
  const isInitOptions = computed(
    () => accountIdList.value.length === 0 && accountNameList.value.length === 0 && channelList.value.length === 0,
  );
  const formList = computed(() => [
    {
      name: COMPONENTS_MAP['a-select'],
      props: {
        title: 'Channel',
        multiple: true,
        list: channelList,
      },
      ext: {
        key: 'channel',
        label: 'Channel',
        isAllowClose: true,
      },
    },
    {
      name: COMPONENTS_MAP['a-select'],
      props: {
        title: 'Account ID',
        multiple: true,
        list: accountIdList,
      },
      ext: {
        key: 'account_id',
        label: 'Account ID',
        isAllowClose: true,
      },
    },
    {
      name: COMPONENTS_MAP['a-select'],
      props: {
        title: 'Account Name',
        multiple: true,
        list: accountNameList,
      },
      ext: {
        key: 'account_name',
        label: 'Account Name',
        isAllowClose: true,
      },
    },
  ]);

  return {
    initOptions,
    labelTypeList,
    totalLabelList,
    formList,
    isInitOptions,
  };
};
