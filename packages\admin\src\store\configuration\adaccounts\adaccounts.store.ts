import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { computed, ref, h, watch, WatchStopHandle } from 'vue';
import { Router, useRoute, useRouter } from 'vue-router';
import {
  AccountsItem,
  ChannelAccountsResType,
  ChannelTypes,
  checkTokenValidity,
} from 'common/service/configuration/adaccounts/type';
import { useWatchGameChange } from 'common/compose/request/game';
import { getChannelList, getTokenValidity } from 'common/service/configuration/adaccounts/channel';
import { useFetchWrapper } from 'common/compose/request/request';
import {
  deleteAccount,
  getAccountsList,
  linkAccount,
  unlinkAccount,
} from 'common/service/configuration/adaccounts/accounts';
import { getChannelAccountsList } from 'common/service/configuration/adaccounts/channel_accounts_list';
import { setAccountsList } from 'common/service/configuration/adaccounts/set_accounts_list';
import { useConfirmDialog } from 'common/compose/useDialog';
import { useGlobalGameStore } from '@/store/global/game.store';
import { getAccountFilterConfig } from './adaccounts-filler-config';
import { FormModel } from 'common/service/creative/label/insight/type';
import { cloneDeep } from 'lodash-es';
import { MessageInstance, MessagePlugin, Link as TLink } from 'tdesign-vue-next';
import { setOrSendWXWorkMessageText } from 'common/service/wxwork';
export const useAdAccountsStore = defineStore(STORE_KEY.CONFIGURATION.ADACCOUNT, () => {
  const gameStore = useGlobalGameStore();
  const router: Router = useRouter();
  const route = useRoute();
  /** channel get start */
  const {
    loading: channelListLoading,
    emit: refreshChannelList,
    data: channelData,
  } = useFetchWrapper<any, ChannelTypes[]>(getChannelList, null, {
    storage: `aix-adaccounts-get-channel-list-${gameStore.gameCode}`,
    immediately: true,
  });
  /** channel get end */

  const {
    loading: tokenValidityLoading,
    emit: refreshTokenValidity,
    data: tokenValidityInfo,
  } = useFetchWrapper<any, checkTokenValidity>(getTokenValidity, null, {});
  const channelList = computed(() => (channelData.value as any)?.list);
  const currentChannels = ref<string>('');

  /**
   * account list start
   * accounts 获取当前系统内这个游戏，这个渠道的数据
   * */
  const formModelValue = ref<any>({
    channel: [],
    account_id: [],
    account_name: [],
  });
  const {
    loading: accountsLoading,
    emit: refreshAccountsList,
    data: accountsList,
  } = useFetchWrapper<string, AccountsItem[]>(getAccountsList as any, '', {
    storage: `adadresses-get-accounts-list-${gameStore.gameCode}`,
    immediately: false,
    trailing: true,
  });
  const initFormModelData = {
    channel: [],
    account_id: [],
    account_name: [],
  };
  const sendWxwork = (linkItems: Record<string, string>[], tyep: 'Unlink' | 'Link' | 'Delete') => {
    const { channel } = linkItems[0];
    const accountIds = getSendWxWorkIds(linkItems);
    setOrSendWXWorkMessageText(channel, 'text', tyep, accountIds, true);
  };
  const fillerModelValue = () => Object.fromEntries(
    Object.entries(formModelValue.value).filter(([key, value]) => key && value),
  );
  // TODO link unlink

  const unlinkAccounts = (item: AccountsItem | AccountsItem[]) => {
    useConfirmDialog({
      theme: 'warning',
      header: 'Unlink Account',
      body:
        'Are you sure to unlink this account？ \n'
        + 'Only authorized ad accounts can be managed or get performance\n'
        + 'data.',
      onConfirm: () => {
        const linkItems = Array.isArray(item) ? item : [item];
        unlinkAccount(linkItems).then(() => {
          getTableList();
          sendWxwork(linkItems, 'Unlink');
        });
      },
    });
  };

  const linkAccounts = async (item: AccountsItem | AccountsItem[]) => {
    const linkItems = Array.isArray(item) ? item : [item];
    await linkAccount(linkItems);
    getTableList();
    sendWxwork(linkItems, 'Link');
  };

  const deleteAccounts = async (item: AccountsItem | AccountsItem[]) => {
    useConfirmDialog({
      theme: 'warning',
      header: 'Delete Account',
      body:
        'Are you sure to delete the account？\n' + 'Once deleted,  AiX will no longer have access to the ad account.',
      onConfirm: () => {
        const linkItems = Array.isArray(item) ? item : [item];
        deleteAccount(linkItems).then(() => {
          getTableList();
          sendWxwork(linkItems, 'Delete');
        });
      },
    });
  };

  /**
   * account list end
   * */

  /**
   * channel accounts list start
   * 渠道的账号列表 */
  const {
    loading: channelAccountsLoading,
    data: channelAccountsList,
    emit: refreshChannelAccountsList,
    error: channelAccountsListError,
  } = useFetchWrapper<string, ChannelAccountsResType>(getChannelAccountsList as any, '', {
    storage: false,
    immediately: false,
  });

  const selectedChannelAccounts = async (list: any, authUser: any, channel: string) => {
    await setAccountsList(
      channelAccountsList.value?.accounts.filter(i => list.includes(i.tempId)),
      authUser,
      channel,
    );
    refreshAccountsList(initFormModelData);
  };
  /**
   * channel accounts list end
   * 渠道的账号列表 */

  const isInitAccountList = ref<Boolean>(false);
  const init = async () => {
    getInitSeachData();
    useWatchGameChange(async () => {
      // 拉取channel列表
      getTableList();
      refreshChannelList();
      refreshTokenValidity();
    });
    initOptions();
  };
  const refreshLoading = ref(false);

  const getTableList = async () => {
    await (refreshAccountsList as any)(fillerModelValue(), () => {
      isInitAccountList.value = true;
    });
    refreshLoading.value = false;
  };

  // 更新formModelValue数据
  const updateFormModelValue = (formModel: FormModel) => {
    formModelValue.value = {
      ...formModelValue.value,
      ...formModel,
    };
  };
  // getAccountFilterConfig 配置信息
  const { formList, initOptions } = getAccountFilterConfig(channelData, accountsList);

  const getInitSeachData = () => {
    formModelValue.value = cloneDeep(initFormModelData);
  };
  const onReset = async () => {
    getInitSeachData();
    await getTableList();
    initOptions();
  };
  // - - - - - - - - - - - - 在其他页面显示的 AD Accounts 入口 start - - - - - - - - - - - -
  // 是否有权限
  const hasPermission = computed(() => {
    const routes = router.getRoutes();
    return routes.some((route) => {
      if (route.path === '/config' && route.children) {
        return route.children.some(child => child.path === 'accounts');
      }
      return false;
    });
  });

  // 是否已经提示展示过提示
  const isAlreadyShowTips = ref<Boolean>(false);

  // 是否存在 ad accounts
  const hasAdAccounts = computed(() => Array.isArray(accountsList.value) && accountsList.value.length > 0);

  const isShowTips = computed(() => {
    const showTipsModuleList = ['Creative Center', 'Trading Desk', 'BI'];
    const firstLevelMenu = route.meta.parent?.[0] ?? '';
    // 只在 creative、td、bi 中提示
    if (!showTipsModuleList.includes(firstLevelMenu)) return false;
    return isInitAccountList.value && hasPermission.value && !hasAdAccounts.value && !isAlreadyShowTips.value;
  });

  // 初始化accountList
  const fetchAccountList = () => {
    if (isInitAccountList.value) return; // 已经调用过接口，则不需要再次调用
    getTableList();
  };

  const stopWatch = ref<WatchStopHandle | null>(null);
  const messageBox = ref<MessageInstance | null>(null);

  const closeAdAccountTip = () => {
    messageBox.value?.close();
  };

  if (!stopWatch.value) {
    stopWatch.value = watch(isShowTips, async () => {
      if (isShowTips.value && !messageBox.value) {
        messageBox.value = await MessagePlugin.info({
          content: h('span', [
            'Please go to ',
            h(
              TLink,
              {
                theme: 'primary',
                hover: 'color',
                onClick: () => {
                  closeAdAccountTip();
                  router.push('/config/accounts');
                },
              },
              'Settings and add your AD accounts',
            ),
            ' to view data on Media Metrics.',
          ]) as any,
          closeBtn: true,
          duration: 0,
          onClose: () => {
            messageBox.value = null;
          },
          onCloseBtnClick: () => {
            isAlreadyShowTips.value = true;
          },
        });
      }
    });
    // 切换游戏后需重新获取accountList
    gameStore.setBeforeChangeGameHook(
      STORE_KEY.CONFIGURATION.ADACCOUNT,
      ({ oldGame, newGame }: { oldGame: string; newGame: string }) => {
        if (oldGame === newGame) return;
        messageBox.value?.close();
        isInitAccountList.value = false;
        isAlreadyShowTips.value = false;
        accountsList.value = null;
      },
    );
  }
  // - - - - - - - - - - - - 在其他页面显示的 AD Accounts 入口 end - - - - - - - - - - - -

  const getSendWxWorkIds = (item: Record<string, string>[]) => item.map(i => i.account_id).join(',');
  const foldList = ref<string[]>([]);
  const setFoldList = (val: string[]) => {
    foldList.value = val;
    val.forEach(item => (formModelValue.value[item] = []));
  };
  return {
    init,
    channelListLoading,
    channelList,
    currentChannels,
    accountsLoading,
    accountsList,
    unlinkAccounts,
    linkAccounts,
    deleteAccounts,
    channelAccountsLoading,
    channelAccountsList,
    refreshChannelAccountsList,
    selectedChannelAccounts,
    formList,
    formModelValue,
    foldList,
    setFoldList,
    getTableList,
    updateFormModelValue,
    onReset,
    channelAccountsListError,
    fetchAccountList,
    hasAdAccounts,
    hasPermission,
    closeAdAccountTip,
    refreshLoading,
    tokenValidityInfo,
    tokenValidityLoading,
    initFilterOption: () => initOptions(),
  };
});
