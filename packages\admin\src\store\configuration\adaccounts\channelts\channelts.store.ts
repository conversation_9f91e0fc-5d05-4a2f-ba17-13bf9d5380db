import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useFetchWrapper } from 'common/compose/request/request';
import { ChannelTypes, TWarnData } from 'common/service/configuration/adaccounts/type';
import { getChannelList, setChannelInfoApi } from 'common/service/configuration/adaccounts/channel';
import { useWatchGameChange } from 'common/compose/request/game';
import { ref, watch, computed } from 'vue';
import { formModel } from './manualAuth';
import { useTips } from 'common/compose/tips';
import { useLoading } from 'common/compose/loading';
import { setOrSendWXWorkMessageText } from 'common/service/wxwork';
import { initStatusByChannel } from '@/store/configuration/adaccounts/channelts/manualAuth';
import { useAdAccountsStore } from '../adaccounts.store';
import { has, isArray, isPlainObject } from 'lodash-es';
export const useChanneltsStore = defineStore(STORE_KEY.CONFIGURATION.CHANNELS, () => {
  const gameStore = useGlobalGameStore();
  const adAccountsStore = useAdAccountsStore();
  /** channel get start */
  const {
    loading: channelListLoading,
    emit: refreshChannelList,
    data: channelList,
  } = useFetchWrapper<any, ChannelTypes[]>(getChannelList, null, {
    storage: `aix-adaccounts-get-channel-lists-${gameStore.gameCode}`,
    immediately: false,
  });
  /** channel get end */
  const popularAndOtherObject = ref({ popular: [] as ChannelTypes[], other: [] as ChannelTypes[] });

  const init = async () => {
    useWatchGameChange(async () => {
      // 拉取channel列表
      refreshChannelList();
      popularAndOtherObject.value = getPopularAndOther();
    });
  };
  const initIconUrl = async () => {
    await refreshChannelList();
    return channelList.value;
  };
  // 搜索内容
  const searchValue = ref('');
  const updateChannelList = (newList: ChannelTypes[] | null) => {
    channelList.value = newList;
  };

  watch(
    () => channelList.value,
    () => {
      const newList = getPopularAndOther();
      popularAndOtherObject.value = { ...newList };
    },
  );

  // 构建 popular和OtherList 的数据，进行页面渲染
  const getPopularAndOther = () => {
    if (searchValue.value) {
      // 搜索模式直接返回
      return { popular: channelList.value };
    }
    if (!channelList.value) {
      return { popular: [], other: [] };
    }
    return channelList.value.reduce(
      (acc: any, item: ChannelTypes) => {
        const isBlackGame = item.black_game_list
          ? JSON.parse(item.black_game_list).includes(gameStore.gameCode)
          : false;
        if (!isBlackGame) {
          if (typeof item.popular === 'string') {
            item.popular === 'true' && !!item.popular ? acc.popular.push(item) : acc.other.push(item);
          } else {
            item.popular && !!item.popular ? acc.popular.push(item) : acc.other.push(item);
          }
        }
        return acc;
      },
      { popular: [], other: [] },
    );
  };
  const { success, err } = useTips();
  const {
    isLoading: isChanelApiLoading,
    showLoading: showChannelApiLoading,
    hideLoading: hideChannelApiLoading,
  } = useLoading();
  const setChannelInfo = async (channelItem: ChannelTypes, isAdd = true) => {
    const { channel = '' } = channelItem;
    initStatusByChannel(channel);
    const opt = {
      formData: formModel.value.data,
      channel: channel.toLowerCase(),
    };

    const result = ref(false);
    showChannelApiLoading();
    await setChannelInfoApi(opt as any)
      .then(async (res) => {
        // 保存之前会先校验一把。接口校验失败的提示
        if (isPlainObject(res) && has(res, 'ret_code') && res.ret_code !== 0) {
          const { title: warnTitle, message: warnMessage } = res as TWarnData;
          const { warn } = useTips({ title: warnTitle });
          warn(warnMessage);
          return;
        }
        success('');
        if (!isAdd) {
          await adAccountsStore.getTableList();
        }
        const optionType = isAdd ? 'Add' : 'Edit';
        setOrSendWXWorkMessageText(
          channel,
          'text',
          optionType,
          opt.formData.account_id as string,
          true,
        );
        result.value = true;
      })
      .catch((error) => {
        err(error.message);
        result.value = false;
      });
    hideChannelApiLoading();
    return result;
  };
  const manualChannelTabelName = (lowerChannelName: string) => {
    const list = channelList.value;
    if (list && isArray(list) && lowerChannelName) {
      const channel = list?.find(
        item => item?.channel_name.toLowerCase() === lowerChannelName);
      return channel?.channel_name;
    }
  };
  const channelNameList = computed(() => (channelList.value || []).reduce(
    (acc: { [key: string]: string }, item: any) => {
      acc[item.channel_name.toLowerCase()] = item.display_name;
      return acc;
    }, {}),
  );

  return {
    init,
    searchValue,
    channelListLoading,
    channelList,
    updateChannelList,
    popularAndOtherObject,
    setChannelInfo,
    isChanelApiLoading,
    initIconUrl,
    manualChannelTabelName,
    channelNameList,
  };
});
