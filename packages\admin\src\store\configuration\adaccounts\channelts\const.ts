export const PLACEHOLDER_LIST: Record<string, string> = {
  account: 'email for login',
  organization_id: 'Enter“Growth→Setting”page and copy',
  org_core_id: 'Enter“Growth→Setting”page and copy',
  api_key: 'Enter“Growth→API Managment”page and copy',
  secret_key: 'Enter“Growth→API Managment→Service account”page and copy',
  key_id: 'Enter“Growth→API Managment→Service account”page and copy',
};

export const CHANNEL_STEP_TEXT: Record<string, string> = {
  default: 'Step2: Obtain Your Account Information',
  dv360: 'Step2: Copy and paste these provided information in Ads platform',
  moloco: 'Step2: Copy and paste these provided information in Ads platform',
};

export const CHANNELS_OFFICIAL_WEBSITE: Record<string, string> = {
  kwai: 'https://creatormarketplace.kwai.com/login',
  appLovin: 'https://dash.applovin.com/login',
  ironSource: 'https://www.is.com',
  vibe: 'https://www.vibe.co/sign-in',
  unity: 'https://id.unity.com/',
  moloco: 'https://portal.moloco.cloud/signin',
  dv360: 'https://displayvideo.google.com/ng_nav/home_v3',
  apple: 'https://app.searchads.apple.com/',
  liftoff: 'https://signin.liftoff.io/signin?',
  the_trade_desk: 'https://desk.thetradedesk.com',
  rtb_house: 'https://panel.rtbhouse.com/#/dashboard',
};

export const CHANNELS_GUIDE_URL: Record<string, string> = {
  kwai: 'https://aix.levelinfinite.com/docs/user_guide/references/kwai_setup.html',
  appLovin: 'https://aix.levelinfinite.com/docs/user_guide/references/applovin_setup.html',
  ironSource: 'https://aix.levelinfinite.com/docs/user_guide/references/ironsource_setup.html',
  vibe: 'https://aix.levelinfinite.com/docs/user_guide/references/vibe_setup.html',
  unity: 'https://aix.levelinfinite.com/docs/user_guide/references/unity_setup.html',
  moloco: 'https://aix.levelinfinite.com/docs/user_guide/references/moloco_setup.html',
  dv360: 'https://aix.levelinfinite.com/docs/user_guide/references/dv360_setup.html',
  apple: 'https://aix.levelinfinite.com/docs/user_guide/references/apple_setup.html',
  liftoff: 'https://aix.levelinfinite.com/docs/user_guide/references/liftoff_setup.html',
  the_trade_desk: 'https://aix.levelinfinite.com/docs/user_guide/references/ttd_setup.html',
  rtb_house: 'https://aix.levelinfinite.com/docs/user_guide/references/rtbhouse_setup.html',
};

export interface FormDataApi {
  formData: FormData;
  channel: string;
}

interface FormColumn {
  label: string;
  key: keyof FormData;
  type?: string;
  tips?: string;
}

export interface FormData {
  game_code?: string;
  account_id?: string | number;
  account_name?: string;
  client_id?: string;
  client_secret?: string;
  refresh_token?: string;
  corp_id?: string;
  campaign_package_name?: string[];
  report_key?: string;
  developer_token?: string;
  customer_id?: number;
  secret_key?: string;
  x_api_key?: string;
  advertiser_id?: string;
  type?: string;
  access_key_id?: string;
  store_id?: string;
  campaign_set_id?: string;
  client_email?: string;
  organization_core_id?: string;
  game_id?: string;
  storage_provider?: string;
  bucket_name?: string;
  path?: string;
  key_id?: string;
  api_key?: string;
  secret_access_key?: string;
  bucket_region?: string;
  big_query_project_id?: string;
  big_query_dataset_id?: string;
  link?: string;
  org_id?: string;
  team_id?: string;
  time_zone?: string;
  public_key?: string;
  api_secret?: string;
  api_token?: string;
  report_name_yesterday?: string;
  report_name_last_week?: string;
  template_id?: string;
  advertiser_hash?: string;
  expiration_date?: string;
  report_name_backfill?: string;
  app_id?: string[];
  access_token?: string;
}

export interface Form {
  column: FormColumn[];
  data: FormData;
}
export interface Channel {
  id: number;
  updated_at: string;
  created_at: string;
  creator: string;
  updater: string;
  channel_id: string;
  channel_name: string;
  icon: string;
  game_code: string;
  status: string;
  icon_image: string;
  display_name: string;
}

export interface FormListData {
  kwai: FormColumn[];
  appLovin: FormColumn[];
  ironSource: FormColumn[];
  vibe: FormColumn[];
  unity: FormColumn[];
  moloco: FormColumn[];
  dv360: FormColumn[];
  apple: FormColumn[];
  liftoff: FormColumn[];
  the_trade_desk: FormColumn[];
  rtb_house: FormColumn[];
  [key: string]: FormColumn[];
}
interface FormModelData {
  kwai: FormData;
  appLovin: FormData;
  ironSource: FormData;
  vibe: FormData;
  unity: FormData;
  moloco: FormData;
  dv360: FormData;
  apple: FormData;
  liftoff: FormData;
  the_trade_desk: FormData;
  rtb_house: FormData;
  [key: string]: FormData;
}
export const CUSTOMIZEDRULESKEY = ['report_name_backfill'];
export const FORMMODELCOLUMN: FormListData = {
  kwai: [
    {
      label: 'Account ID',
      key: 'account_id',
    },
    {
      label: 'Account Name',
      key: 'account_name',
    },
    {
      label: 'Client ID',
      key: 'client_id',
    },
    {
      label: 'Secret Key',
      key: 'secret_key',
    },
    {
      label: 'Refresh Token',
      key: 'refresh_token',
    },
    {
      label: 'Corp ID',
      key: 'corp_id',
    },
  ],
  appLovin: [
    {
      label: 'Account ID',
      key: 'account_id',
    },
    {
      label: 'Account Name',
      key: 'account_name',
    },
    {
      label: 'Client ID',
      key: 'client_id',
    },
    {
      label: 'Client Secret',
      key: 'secret_key',
    },
    {
      label: 'Report Key',
      key: 'report_key',
    },
    {
      label: 'Campaign Package Name',
      type: 'tagInput',
      key: 'campaign_package_name',
    },
  ],
  ironSource: [
    {
      label: 'Account ID',
      key: 'account_id',
    },
    {
      label: 'Account Name',
      key: 'account_name',
    },
    {
      label: 'Secret Key',
      key: 'secret_key',
    },
    {
      label: 'Refresh Token',
      key: 'refresh_token',
    },
  ],
  vibe: [
    {
      label: 'Account ID',
      key: 'account_id',
    },
    {
      label: 'Account Name',
      key: 'account_name',
    },
    {
      label: 'Api Key',
      key: 'api_key',
    },
  ],
  unity: [
    {
      label: 'Account Name',
      key: 'account_name',
    },
    {
      label: 'Game ID',
      key: 'game_id',
    },
    {
      label: 'Organization core ID',
      key: 'organization_core_id',
    },
    {
      label: 'Campaign Set ID',
      key: 'campaign_set_id',
    },
    {
      label: 'Store ID',
      key: 'store_id',
    },
    {
      label: 'Key ID',
      key: 'key_id',
    },
    {
      label: 'Secret Key',
      key: 'secret_key',
    },
  ],
  moloco: [
    {
      label: 'Path',
      type: 'disabled',
      key: 'path',
    },
    {
      label: 'Storage Provider',
      type: 'disabled',
      key: 'storage_provider',
    },
    {
      label: 'Bucket Name',
      type: 'disabled',
      key: 'bucket_name',
    },

    {
      label: 'Bucket Region',
      type: 'disabled',
      key: 'bucket_region',
    },
    {
      label: 'Access Key ID',
      type: 'disabled',
      key: 'access_key_id',
    },
    {
      label: 'Secret Access Key',
      type: 'disabled',
      key: 'secret_access_key',
    },
    {
      type: 'step3',
      label: 'Account ID',
      key: 'account_id',
    },
    {
      label: 'Account Name',
      key: 'account_name',
    },
    {
      label: 'App ID',
      type: 'tagInput',
      key: 'app_id',
    },
    {
      label: 'Api Key',
      key: 'api_key',
    },
  ],
  dv360: [
    {
      label: 'BigQuery Project ID',
      type: 'disabled',
      key: 'big_query_project_id',
    },
    {
      label: 'BigQuery Dataset ID',
      type: 'disabled',
      key: 'big_query_dataset_id',
    },
    {
      label: 'Account ID',
      key: 'account_id',
      type: 'step3',
    },
    {
      label: 'Account Name',
      key: 'account_name',
    },
    {
      label: 'Link',
      key: 'link',
    },
  ],
  apple: [
    {
      label: 'Account ID',
      key: 'account_id',
    },
    {
      label: 'Account Name',
      key: 'account_name',
    },
    {
      label: 'Client ID',
      key: 'client_id',
    },
    {
      label: 'Team ID',
      key: 'team_id',
    },
    {
      label: 'Key ID',
      key: 'key_id',
    },
    {
      label: 'Public Key',
      key: 'public_key',
    },
    {
      label: 'Time Zone',
      key: 'time_zone',
    },
  ],
  liftoff: [
    {
      label: 'Account ID',
      key: 'account_id',
    },
    {
      label: 'Account Name',
      key: 'account_name',
    },
    {
      label: 'Api Key',
      key: 'api_key',
    },
    {
      label: 'Api Secret',
      key: 'api_secret',
    },
  ],
  the_trade_desk: [
    {
      label: 'Account ID',
      key: 'account_id',
    },
    {
      label: 'Account Name',
      key: 'account_name',
    },
    {
      label: 'Report Name (yesterday)',
      key: 'report_name_yesterday',
    },
    {
      label: 'Report Name (last 7 days)',
      key: 'report_name_last_week',
    },
    {
      label: 'Report Name Backfill',
      tips: 'Fill in this field only if you need to supplement data from 7 days ago.',
      key: 'report_name_backfill',
    },
  ],
  rtb_house: [
    {
      label: 'Account ID',
      key: 'account_id',
    },
    {
      label: 'Account Name',
      key: 'account_name',
    },
  ],
};
export const FORMMODELDATA: FormModelData = {
  kwai: {
    account_id: '',
    game_code: '',
    account_name: '',
    client_id: '',
    secret_key: '',
    refresh_token: '',
    corp_id: '',
  },
  appLovin: {
    account_id: '',
    account_name: '',
    game_code: '',
    campaign_package_name: [],
    report_key: '',
    client_id: '',
    secret_key: '',
  },
  ironSource: {
    account_id: '',
    account_name: '',
    game_code: '',
    secret_key: '',
    refresh_token: '',
  },
  vibe: {
    game_code: '',
    account_id: '',
    account_name: '',
    api_key: '',
  },
  unity: {
    account_id: '',
    account_name: '',
    organization_core_id: '',
    campaign_set_id: '',
    store_id: '',
    key_id: '',
    game_id: '',
    game_code: '',
    secret_key: '',
  },
  moloco: {
    game_code: '',
    storage_provider: 'Amazon Web Services S3',
    bucket_name: 'ua-moloco-ads',
    path: '',
    bucket_region: 'ap-southeast-1',
    access_key_id: '********************',
    secret_access_key: '+5SCgq/xfWp6n6MTUAjfwtT/YMhZJJLbSurAOz/K',
    account_id: '',
    account_name: '',
    api_key: '',
    app_id: [],
  },
  dv360: {
    game_code: '',
    big_query_project_id: 'tencent-ua',
    big_query_dataset_id: 'aix_dv360_ds',
    link: '',
    account_id: '',
    account_name: '',
  },
  apple: {
    game_code: '',
    account_id: '',
    account_name: '',
    key_id: '',
    team_id: '',
    client_id: '',
    public_key: '',
    time_zone: '',
  },
  liftoff: {
    game_code: '',
    api_key: '',
    api_secret: '',
    account_id: '',
    account_name: '',
  },
  the_trade_desk: {
    game_code: '',
    api_token: 'dMBSR9ygslmgE1O3rAr+Jje/3r54Vyn/JAk3KCHUDjvDfVE8B1BLlnPrqWLZyJ/+',
    expiration_date: '2025-11-28T09:24:45',
    account_id: '',
    account_name: '',
    report_name_yesterday: '',
    report_name_last_week: '',
    template_id: '3688173',
    report_name_backfill: '',
  },
  rtb_house: {
    game_code: '',
    account_id: '',
    account_name: '',
  },
};
