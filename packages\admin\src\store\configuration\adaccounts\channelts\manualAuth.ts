import { ref, computed } from 'vue';
import { Form, FORMMODELDATA, FORMMODELCOLUMN } from './const';
import { ChannelTypes } from 'common/service/configuration/adaccounts/type';
import { cloneDeep } from 'lodash-es';
import { useGlobalGameStore } from '@/store/global/game.store';

export const formModel = ref<Form>({
  column: [],
  data: {},
});

export const upDateFormModel = (selectedChannel: ChannelTypes, isAdd = true) => {
  const channelName = selectedChannel?.channel_name;
  const initChannelItem = cloneDeep(FORMMODELDATA[channelName]);
  const initchannelColumn = cloneDeep(FORMMODELCOLUMN[channelName]);
  const cloneDeepItem = cloneDeep(selectedChannel);
  const formModelData = isAdd ? initChannelItem : initChannelItemfun(initChannelItem, cloneDeepItem);
  formModel.value = {
    column: initchannelColumn,
    data: formModelData,
  };
  initStatusByChannel(channelName, isAdd);
};

const formModelData = computed(() => formModel.value.data);
const channelAddMaps: Record<string, any> = {
  moloco: (gameCode: string) => ({
    path: gameCode,
  }),
  unity: () => ({
    account_id: formModelData.value.campaign_set_id,
  }),
};
const channelEditMaps: Record<string, any> = {
  appLovin: () => ({
    campaign_package_name: parseChannelInfo('campaign_package_name'),
  }),
  moloco: () => ({
    app_id: parseChannelInfo('app_id'),
  }),
  unity: () => ({
    account_id: formModelData.value.campaign_set_id,
  }),
};
const parseChannelInfo = (key: string): object | any[] => {
  try {
    return JSON.parse((formModelData.value as any)[key]);
  } catch (e) {
    console.error('JSON parsing error:', e);
    return [];
  }
};
export const initStatusByChannel = (channel: string, isAdd = true) => {
  const { gameCode } = useGlobalGameStore();
  formModel.value.data.game_code = gameCode;
  if (!channel) {
    return formModel.value.data;
  }
  // 检查是否存在对应的 channel 处理函数
  const channelHandler = isAdd ? channelAddMaps[channel] : channelEditMaps[channel];
  if (channelHandler) {
    // 调用处理函数并更新 formModel 数据
    const updatedFormData = channelHandler(gameCode);
    Object.assign(formModel.value.data, updatedFormData);
  }
  return formModel.value.data;
};

export const onClose = () => {};
const initChannelItemfun = (initChannelItem: any, channelItem: any) => {
  const margeObject = initChannelItem;
  Object.keys(margeObject).forEach((key) => {
    if (Object.prototype.hasOwnProperty.call(channelItem, key)) {
      margeObject[key] = channelItem[key];
    }
  });
  margeObject.id = channelItem.id;
  margeObject.channel_user_id = channelItem.channel_user_id;
  return margeObject;
};
