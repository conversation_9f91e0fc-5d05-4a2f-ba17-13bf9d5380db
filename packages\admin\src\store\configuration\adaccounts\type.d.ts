export interface SpecialCondition {
  key: string;
  condition: (value: any) => boolean;
  action: (value: any) => Record<string, string | number>; // 返回一个新的对象
}
export enum Action {
  Link = 1,
  UnLink = 2,
  Delete = 3,
  Edit = 4,
}

export const actionOptions = [
  {
    content: 'Link',
    value: Action.Link,
  },
  {
    content: 'UnLink',
    value: Action.UnLink,
  },
  {
    content: 'Delete',
    value: Action.Delete,
  },
  {
    content: 'Edit',
    value: Action.Edit,
  },
];
