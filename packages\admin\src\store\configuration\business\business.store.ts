import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { useLoading } from 'common/compose/loading';
import { useStudio } from './useStudio';
import { useGame } from './useGame';
import { useAuthStageStore } from '@/store/global/auth.store';
import { computed, ref } from 'vue';

export const useBusinessStore = defineStore(STORE_KEY.CONFIGURATION.BUSINESS, () => {
  const { companyId: rawCompanyId, checkRoleExistByGame } = useAuthStageStore();
  const studioInstance = useStudio();
  const gameInstance = useGame();

  const companyId = ref<number>(rawCompanyId);
  const isCompanyAdmin = computed(() => checkRoleExistByGame('CompanyAdmin'));
  const isGameAdmin = computed(() => checkRoleExistByGame('Admin'));
  const { isLoading, showLoading, hideLoading } = useLoading(false);

  const init = async () => {
    showLoading();
    await studioInstance.init(isCompanyAdmin.value, rawCompanyId);
    hideLoading();
  };

  const reset = () => {
    resetCompanyId();
    gameInstance.reset();
    studioInstance.reset();
  };

  const changeCompanyId = (newCompanyId: number) => {
    companyId.value = newCompanyId;
  };

  const resetCompanyId = () => {
    companyId.value = rawCompanyId;
  };

  return {
    init,
    reset,

    companyId,
    changeCompanyId,

    isLoading,
    studio: studioInstance,
    game: gameInstance,
    isCompanyAdmin,
    isGameAdmin,
  };
});

export default useBusinessStore;
