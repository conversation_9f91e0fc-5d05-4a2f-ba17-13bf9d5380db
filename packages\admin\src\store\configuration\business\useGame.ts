import { IRoleOption } from '@/views/configuration/business/type';
import { useLoading } from 'common/compose/loading';
import { useTips } from 'common/compose/tips';
import { useVisible } from 'common/compose/useVisible';
import {
  getGameDetail,
  getGameAuthList as getGameAuthListService,
  modifyUser as modifyUserService,
} from 'common/service/configuration/business/game';
import {
  GetGameAuthListParam,
  InvitedUser, GetStudioListGameType,
  GetGameMetaReturnDataType, ModifyUserForGameParam,
  RoleAndCount,
} from 'common/service/configuration/business/type/type.d';
import { SortInfo } from 'tdesign-vue-next';
import { ref, watch } from 'vue';


export type TGame = GetStudioListGameType | GetGameMetaReturnDataType | null;
export function useGame() {
  const { success, err } = useTips();

  const curGame = ref<TGame>(null);

  // 邀请人员表格相关
  const total = ref<number>(0);
  const curPageSize = ref<number>(10);
  const curPageNum = ref<number>(1);
  const { isLoading: tableLoading, showLoading: showTableLoading, hideLoading: hideTableLoading } = useLoading(false);
  const invitedUsers = ref<InvitedUser[]>([]);
  const tableSort = ref<SortInfo[]>([{ sortBy: 'updated_at', descending: true }]);
  const serializerSort = ref<{ [key in keyof InvitedUser]?: 'ASC' | 'DESC' }>({});
  const tableFilterValue = ref<{ [key in keyof InvitedUser]?: Array<number | string> }>({
    role_id: [],
    invite_status: [],
  });


  const roles = ref<RoleAndCount[]>([]);
  const roleOptions = ref<IRoleOption[]>([]);
  const adminRole = ref<RoleAndCount | null>(null);

  const { isLoading: drawerLoading, showLoading: showDrawerLoading,
    hideLoading: hideDrawerLoading } = useLoading(false);
  const { visible: delGameDialogVisible, hide: hideDelGameDialog, show: showDelGameDialog } = useVisible(false);

  const drawerVisible = ref<boolean>(false);

  const changeCurrentGame = (newGame: TGame = null) => {
    curGame.value = newGame;
  };

  const reset = () => {
    changeTotal(0);
    changeCurrentGame();
    curPageNum.value = 1;
    curPageSize.value = 10;
    updateInvitedUsers([]);
    updateRoles([]);
    changeTableFilterValue({
      role_id: [],
      invite_status: [],
    });
    changeTableSort([{ sortBy: 'updated_at', descending: true }]);
  };


  const initTable = async (params: Partial<GetGameAuthListParam<InvitedUser>> = {}) => {
    try {
      showTableLoading();
      await getGameAuthList<InvitedUser>(params);
    } catch (error) {
      err('Table data load error');
    } finally {
      hideTableLoading();
    }
  };

  const changeTotal = (newTotal: number) => {
    total.value = newTotal;
  };
  const changePage = (pageNum: number, pageSize: number) => {
    curPageSize.value = pageSize;
    curPageNum.value = pageNum;
    initTable({
      page_size: pageSize,
      page_num: pageNum,
    });
  };
  const changePageSize = (pageSize: number) => {
    curPageSize.value = pageSize;
    initTable({
      page_size: pageSize,
    });
  };
  const changePageNum = (pageNum: number) => {
    curPageNum.value = pageNum;
    initTable({
      page_num: pageNum,
    });
  };

  const updateRoles = (newRoles: RoleAndCount[]) => {
    roles.value = newRoles;
  };

  const showDrawer = () => {
    drawerVisible.value = true;
    initDrawer();
  };

  const initDrawer = async () => {
    if (curGame.value) {
      showDrawerLoading();

      const curGameId = curGame.value.game_id;
      if (curGameId === undefined) {
        return;
      }

      Promise.all([getGameDetail({ game_id: curGameId }), getGameAuthList({ game_id: curGameId })])
        .then((resultList) => {
          const [gameInfo] = resultList;
          changeCurrentGame({
            ...curGame.value,
            ...gameInfo,
          });
        })
        .catch((e) => {
          err((e as any)?.message || 'Game data failed to load, please try again');
        })
        .finally(() => {
          hideDrawerLoading();
        });
    }
  };

  const hideDrawer = () => {
    drawerVisible.value = false;
    reset();
  };

  const getGameAuthList = async <T>(params: Partial<GetGameAuthListParam<T>>) => {
    try {
      showTableLoading();
      const invitedUserList = await getGameAuthListService<T>({
        game_id: params.game_id ?? curGame.value!.game_id!,
        page_num: params.page_num ?? curPageNum.value,
        page_size: params.page_size ?? curPageSize.value,
        cond: params?.cond ?? tableFilterValue.value,
        orderby: params?.orderby ?? serializerSort.value,
      });
      updateInvitedUsers(invitedUserList?.members ?? []);
      changeTotal(invitedUserList?.totalMember ?? 0);
      updateRoles(invitedUserList?.roles ?? []);
    } finally {
      hideTableLoading();
    }
  };

  const modifyUser = async (params: ModifyUserForGameParam) => {
    try {
      await modifyUserService(params);
      success('Modified successfully');
    } catch (error) {
      err((error as any)?.message || 'Modified failed');
    }
  };

  const updateInvitedUsers = (newInviteUsers: InvitedUser[]) => {
    invitedUsers.value = newInviteUsers;
  };

  const changeTableSort = (newSort: SortInfo[]) => {
    tableSort.value = newSort;
  };
  const changeTableFilterValue = (filters: { [key in keyof InvitedUser]?: Array<number | string> }) => {
    tableFilterValue.value = filters;
  };


  watch(roles, (newRole) => {
    const tempRoles: IRoleOption[] = [];
    for (const role of newRole) {
      const { role_id: roleId, role_name: roleName } = role;
      tempRoles.push({
        value: roleId,
        label: roleName,
      });
      if (roleName === 'admin') {
        adminRole.value = role;
      }
    }
    roleOptions.value = tempRoles;
  });

  watch(tableSort, (val) => {
    const orderby: { [key in keyof InvitedUser]?: 'DESC' | 'ASC' } = {};
    val.forEach((element) => {
      const { sortBy, descending } = element;
      orderby[sortBy as keyof InvitedUser] = descending ? 'DESC' : 'ASC';
    });
    serializerSort.value = orderby;
  }, {
    immediate: true,
  });

  // watch();
  watch([tableFilterValue, serializerSort], () => {
    if (drawerVisible.value === false) return;
    changePageNum(1);
  });

  return {
    reset,

    // table config
    total,
    pageSize: curPageSize,
    changePageSize,
    pageNum: curPageNum,
    changePage,
    changePageNum,
    initTable,
    tableSort,
    changeTableSort,
    tableFilterValue,
    changeTableFilterValue,

    invitedUsers,
    updateInvitedUsers,

    tableLoading,
    showTableLoading,
    hideTableLoading,

    drawerVisible,
    hideDrawer,
    showDrawer,
    initDrawer,
    drawerLoading,
    showDrawerLoading,
    hideDrawerLoading,

    curGame,
    changeCurrentGame,


    delGameDialogVisible,
    showDelGameDialog,
    hideDelGameDialog,


    roles,
    roleOptions,
    adminRole,

    modifyUser,
  };
}

