import { useLoading } from 'common/compose/loading';
import { useTips } from 'common/compose/tips';
import { useVisible } from 'common/compose/useVisible';
import { getCompanyDetail } from 'common/service/configuration/business/company';
import { getStudioList } from 'common/service/configuration/business/studio';
import { GetStudioListItemType, Studio } from 'common/service/configuration/business/type/type';
import { ref } from 'vue';
import dayjs from 'dayjs';

export function useStudio() {
  const { err } = useTips();
  const curActiveStudio = ref<Studio | null>(null);
  const studioList = ref<GetStudioListItemType[]>([]);
  // dialog
  const { visible: studioFormDialogVisible, hide: hideStudioFormDialog, show: showStudioFormDialog } = useVisible();
  const { isLoading, hideLoading, showLoading } = useLoading(false);

  const isCompanyAdmin = ref<boolean>();
  const rawCompanyId = ref<number>();

  const init = async (hasCompanyRoot: boolean, companyId: number) => {
    isCompanyAdmin.value = hasCompanyRoot;
    rawCompanyId.value = companyId;

    showLoading();
    await updateStudioList();
    hideLoading();
  };

  const updateStudioList = async () => {
    try {
      let result = null;
      if (isCompanyAdmin.value) {
        result = await getCompanyDetail({ company_id: rawCompanyId.value! });
      } else {
        result = await getStudioList();
      }
      studioList.value = (result?.list ?? []).sort((preStudio, curStudio) => (
        dayjs(preStudio.updated_at).isBefore(curStudio.updated_at) ? 1 : -1));
    } catch (error) {
      err('Studio data load error');
    }
  };

  const changeCurActiveStudio = (studio: Studio | null = null) => {
    curActiveStudio.value = studio;
  };

  const reset = () => {
    curActiveStudio.value = null;
  };

  return {
    init,
    reset,
    isLoading,
    updateStudioList,
    studioList,

    studioFormDialogVisible,

    curActiveStudio,
    changeCurActiveStudio,

    hideStudioFormDialog,
    showStudioFormDialog,
  };
}
