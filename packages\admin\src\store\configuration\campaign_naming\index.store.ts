import { STORE_KEY } from '@/config/config';
import { COMPONENT_TYPE, DEFAULT_RULES, FORMAT_OPTIONS } from '@/views/configuration/management/campaign_naming/const';
import { useLoading } from 'common/compose/loading';
import { useTips } from 'common/compose/tips';
import { getCampaignNameRules, updateCampaignNameRule } from 'common/service/configuration/campaign_naming/index';
import { IListItem } from 'common/service/configuration/campaign_naming/type';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import { defineStore } from 'pinia';
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, reactive, ref } from 'vue';
import type { IFormData, IUpdateResponse } from './type';

export const useCampaignNamingStore = defineStore(STORE_KEY.CONFIGURATION.MANAGEMENT.CAMPAIGN_NAMING, () => {
  const { isLoading, showLoading, hideLoading } = useLoading();
  const { err } = useTips();
  const previousRulesListItem = ref<IListItem[]>([]);
  const previousDelimiter = ref<string>('');
  const curVersion = ref<number>(0);
  const curVersionSign = ref<string>('');

  const newRulesListFormData = ref<IFormData>({
    listItem: [],
    newSelectionInput: '',
    delimiter: '-',
  });
  const newSelectionInputError = ref<boolean>(false);
  const selectedRuleIndex = ref();

  // 用于控制二次保存的配置，打开或关闭操作的功能，true为关闭功能
  const isLockCfg = reactive({
    removeRule: false, // 删除设定的规则
    changeOrder: false, // 编辑排序
    editType: false, // 编辑类型
  });

  // 更新接口是否有返回正在更新或有新的版本
  const isCustomCampaignUpdating = ref(false);
  const hasNewVersion = ref(false);

  const isPreviousRule = computed(() => {
    const defaultValue = previousRulesListItem.value.map((item: IListItem) => item.key);
    return defaultValue.includes(newRulesListFormData.value?.listItem[selectedRuleIndex.value].key);
  });

  // 获取最初的OS和Spend Type的key值用于判断输入框
  const defaultKey = computed(() => ({
    os: previousRulesListItem.value
      .filter((item: IListItem) => item.name === 'OS')
      .map(item => item.key),
    spendType: previousRulesListItem.value
      .filter((item: IListItem) => item.name === 'Spend Type')
      .map(item => item.key),
  }));
  // 判断是否是最初的OS和Spend Type
  const isOSAndSpendType = computed(() => {
    const selectedItem = newRulesListFormData.value?.listItem[selectedRuleIndex.value];
    return DEFAULT_RULES.includes(selectedItem?.name)
      && (defaultKey.value.os.includes(selectedItem?.key) || defaultKey.value.spendType.includes(selectedItem?.key));
  });

  const isTypeDropdownList = computed(() => newRulesListFormData.value?.listItem[selectedRuleIndex.value].type
      === COMPONENT_TYPE.DROPDOWN_LIST);
  const isTypeCreateDropdownList = computed(() => newRulesListFormData.value?.listItem[selectedRuleIndex.value].type
      === COMPONENT_TYPE.CUSTOM_DROPDOWN_LIST);
  const isTypeText = computed(() => newRulesListFormData.value?.listItem[selectedRuleIndex.value].type
      === COMPONENT_TYPE.TEXT);
  const isTypeDate = computed(() => newRulesListFormData.value?.listItem[selectedRuleIndex.value].type
      === COMPONENT_TYPE.DATE);

  const setName = (val: string) => {
    const listItem = newRulesListFormData.value?.listItem[selectedRuleIndex.value];
    if (listItem) listItem.name = val.trim();
  };

  const setType = (val: string) => {
    const listItem = newRulesListFormData.value?.listItem[selectedRuleIndex.value];
    const previousValue = previousRulesListItem.value.find(item => item.key === listItem.key);
    if (listItem) {
      if (val === COMPONENT_TYPE.DROPDOWN_LIST) {
        listItem.type = previousValue?.type === COMPONENT_TYPE.CUSTOM_DROPDOWN_LIST
          ? COMPONENT_TYPE.CUSTOM_DROPDOWN_LIST : val;
        listItem.data = previousValue?.data || [];
        listItem.default_value = '';
      } else if (val === COMPONENT_TYPE.TEXT) {
        listItem.type = val;
        listItem.data = [];
        listItem.default_value = previousValue?.type === COMPONENT_TYPE.DATE ? '' : previousValue?.default_value || '';
        newRulesListFormData.value.newSelectionInput = '';
      } else if (val === COMPONENT_TYPE.DATE) {
        listItem.type = val;
        listItem.data = { format: FORMAT_OPTIONS[0].value }; // 格式化规则
        listItem.default_value = previousValue?.default_value || dayjs().format('YYYYMMDD');
        newRulesListFormData.value.newSelectionInput = '';
      }
    }
  };

  const setFormat = (val: string) => {
    const listItem = newRulesListFormData.value?.listItem[selectedRuleIndex.value];
    if (listItem?.data) (listItem.data as Record<string, any>).format = val;
  };

  const setAllowInputInPreview = (val: string) => {
    const listItem = newRulesListFormData.value?.listItem[selectedRuleIndex.value];
    listItem.type = val;
  };

  const setDefaultValue = (val: string) => {
    const listItem = newRulesListFormData.value?.listItem[selectedRuleIndex.value];
    if (listItem) listItem.default_value = val.trim();
  };

  const setNewDropdown = (val: string) => {
    newRulesListFormData.value = {
      ...newRulesListFormData.value,
      newSelectionInput: val.trim(),
    };
  };
  const addDropdown = () => {
    if (!newSelectionInputError.value) {
      const currentRule = newRulesListFormData.value.listItem[selectedRuleIndex.value];
      const newDropdownItem = newRulesListFormData.value.newSelectionInput;

      const newDropdown = [
        ...(currentRule.data as string[]),
        newDropdownItem,
      ];
      newRulesListFormData.value.listItem[selectedRuleIndex.value] = {
        ...currentRule,
        data: newDropdown,
      };
      newRulesListFormData.value.newSelectionInput = '';
    }
  };

  const removeDropdown = (index: number) => {
    if (newRulesListFormData.value && selectedRuleIndex.value !== undefined) {
      const data = newRulesListFormData.value.listItem[selectedRuleIndex.value].data || [];
      (data as string[]).splice(index, 1);
      newRulesListFormData.value.listItem[selectedRuleIndex.value].data = data;
    }
  };

  const getCampaignNameRulesService = async () => {
    showLoading();
    try {
      const {
        list,
        delimiter,
        version,
        version_sign: versionSign,
      } = await getCampaignNameRules();
      previousRulesListItem.value = cloneDeep(list);
      previousDelimiter.value = cloneDeep(delimiter);
      newRulesListFormData.value.listItem = cloneDeep(list);
      newRulesListFormData.value.delimiter = cloneDeep(delimiter);
      curVersion.value = version;
      curVersionSign.value = versionSign;

      // 把下面的设回默认值
      selectedRuleIndex.value = undefined;
    } catch (error) {
      console.log(error);
    }
    hideLoading();
  };

  const updateCampaignNameRuleService = async () => {
    showLoading();
    try {
      const formDataList = newRulesListFormData.value?.listItem.map(({ key, ...item }: IListItem) => item);
      const updateRes = await updateCampaignNameRule({
        list: formDataList,
        delimiter: newRulesListFormData.value.delimiter,
        version: curVersion.value,
        version_sign: curVersionSign.value,
      });
      const result = updateRes as unknown as IUpdateResponse;
      if (result?.isUpdating) {
        isCustomCampaignUpdating.value = result.isUpdating;
        hasNewVersion.value = !result.isUpdating;
      } else if (result?.hasNewVersion) {
        hasNewVersion.value = result.hasNewVersion;
        isCustomCampaignUpdating.value = !result.hasNewVersion;
      } else {
        hasNewVersion.value = false;
        isCustomCampaignUpdating.value = false;
        MessagePlugin.success('Save successfully');
      }
    } catch (error) {
      console.log(error);
      err((error as any)?.message ?? 'Failed to save.');
    }
    hideLoading();
  };

  const init = async () => {
    await getCampaignNameRulesService();
  };

  return {
    init,
    previousRulesListItem,
    previousDelimiter,
    curVersion,
    curVersionSign,
    newRulesListFormData,
    isLoading,
    selectedRuleIndex,
    isLockCfg,
    isPreviousRule,
    isOSAndSpendType,
    setName,
    setType,
    setFormat,
    setAllowInputInPreview,
    setDefaultValue,
    setNewDropdown,
    addDropdown,
    removeDropdown,
    getCampaignNameRulesService,
    updateCampaignNameRuleService,
    isTypeCreateDropdownList,
    isTypeDropdownList,
    isTypeText,
    isTypeDate,
    newSelectionInputError,
    isCustomCampaignUpdating,
    hasNewVersion,
  };
});
