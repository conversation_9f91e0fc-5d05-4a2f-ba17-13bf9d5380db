import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { ref } from 'vue';
import { useLoading } from 'common/compose/loading';
import { IMetric, IMetricForm, IOption, METRIC_TYPE } from './type.d';
import type { IMetricGroup } from 'common/components/CustomizeColumnsDialog/type';
import { getDescDetail } from 'common/utils/template/templateUtils';
import {
  getCustomMetricList,
  getEventList,
  addCustomMetric,
  updateCustomMetric,
  delCustomMetric,
  switchCustomMetricVisible,
  sortCustomMetric,
  getAllCustomMetricConfig,
} from 'common/service/configuration/management/metric';
import { useGlobalGameStore } from '@/store/global/game.store';
import { DialogPlugin, DragSortContext, LoadingPlugin, MessagePlugin, PageInfo } from 'tdesign-vue-next';
import { pick } from 'lodash-es';

export const useBIManagementMetricStore = defineStore(STORE_KEY.CONFIGURATION.MANAGEMENT.METRIC, () => {
  // 选择的tab，cohort列表、daily列表
  const metricType = ref<METRIC_TYPE>(METRIC_TYPE.COHORT);
  // 自定义metric配置列表（根据不同metricType拉取数据）
  const metricList = ref<IMetric[]>([]);
  // 是否显示抽屉
  const isShowDrawer = ref<boolean>(false);
  // 是否是编辑抽屉
  const isEditDrawer = ref<boolean>(false);
  // 当前编辑抽屉的数据
  const currentEditData = ref<IMetric | null>(null);
  // 事件下拉数据源列表
  const eventList = ref<IOption[]>([]);
  // 是否显示预览弹窗
  const previewDialogVisible = ref<boolean>(false);
  // 预览自定义metric配置在pivot页的展示数据
  const previewMetricList = ref<IMetricGroup[]>([]);
  // 分页配置
  const pagination = ref({
    total: 0,
    pageIndex: 1,
    pageSize: 1000,
    pageSizeOptions: [10, 20, 100, 200, 1000],
  });
  const gameStore = useGlobalGameStore();

  const {
    isLoading: isTableLoading,
    showLoading: showTableLoading,
    hideLoading: hideTableLoading,
  } = useLoading(false);

  const {
    isLoading: isPreviewDialogLoading,
    showLoading: showPreviewDialogLoading,
    hideLoading: hidePreviewDialogLoading,
  } = useLoading(false);

  // 获取自定义metric配置列表
  const getMetricList = async (clearTable = true) => {
    showTableLoading();
    // 先清空列表
    clearTable && (metricList.value = []);
    // 获取自定义metric配置列表数据
    const res = await getCustomMetricList({
      game: gameStore.gameCode,
      metric_type: metricType.value,
      pageIndex: pagination.value.pageIndex,
      pageSize: pagination.value.pageSize,
    })
      .finally(() => {
        hideTableLoading();
      });
    metricList.value = res.list;
    pagination.value.total = res.count;
  };

  // 获取事件下拉数据源列表
  const getEventOptList = async () => {
    const res = await getEventList({ game: gameStore.gameCode });
    eventList.value = res.list;
  };

  // 切换tab（cohort列表、daily列表）
  const onTabChange = (key: METRIC_TYPE) => {
    metricType.value = key;
    pagination.value.pageIndex = 1;
    getMetricList();
  };

  // 新增自定义metric配置提交时触发
  const handleCreateMetric = async (formData: IMetricForm) => {
    // 全屏loading
    LoadingPlugin(true);
    const metricType = formData.metric_type;
    // 不同类型的metric配置参数
    const cohortParamList = ['metric_name', 'metric_type', 'description', 'event_name', 'value_type', 'is_cumulative', 'format'];
    const dailyParamList = ['metric_name', 'metric_type', 'description', 'event_name', 'value_type'];
    const params = pick(formData, metricType === METRIC_TYPE.COHORT ? cohortParamList : dailyParamList);
    await addCustomMetric({
      ...params,
      game: gameStore.gameCode,
    })
      .then((res) => {
        LoadingPlugin(false);
        // 新增成功后刷新列表
        if (res?.id) {
          MessagePlugin.success('Create metric success!');
          hideDrawer();
          getMetricList();
        }
      })
      .catch(() => {
        LoadingPlugin(false);
      });
  };

  // 删除自定义metric配置时触发
  const handleDeleteMetric = async () => {
    const confirmInstance = DialogPlugin.confirm({
      header: 'Are you sure to delete this metric?',
      theme: 'warning',
      body: 'Once deleted, its historical data will be cleared.',
      cancelBtn: 'Cancel',
      confirmBtn: 'Confirm',
      onConfirm: () => {
        hideDrawer();
        confirmInstance.hide();
        // 全屏loading
        LoadingPlugin(true);
        delCustomMetric({
          id: currentEditData.value?.id,
          game: gameStore.gameCode,
        })
          .then(() => {
            LoadingPlugin(false);
            pagination.value.pageIndex = 1;
            getMetricList();
          })
          .catch(() => {
            LoadingPlugin(false);
          });
      },
      onClose: () => {
        confirmInstance.hide();
      },
    });
  };

  // 点击state列中的开关按钮触发
  const handleSwitchMetricVisible = async (newVal: 0 | 1, row: IMetric) => {
    // eslint-disable-next-line no-param-reassign
    row.is_visible = newVal === 1 ? 0 : 1; // 先阻止switch的默认行为
    // 确认弹窗
    const confirmInstance = DialogPlugin.confirm({
      header: `Are you sure to ${row.is_visible === 1 ? 'hide' : 'display'} this metric?`,
      theme: 'warning',
      body: `Are you sure to ${row.is_visible === 1 ? 'hide' : 'display'} this metric?`,
      cancelBtn: 'Cancel',
      confirmBtn: 'Confirm',
      onConfirm: () => {
        confirmInstance.hide();
        showTableLoading();
        // 切换自定义metric配置状态
        switchCustomMetricVisible({
          id: row.id,
          is_visible: newVal,
          game: gameStore.gameCode,
        })
          .then(() => {
            // eslint-disable-next-line no-param-reassign
            row.is_visible = newVal;
          })
          .finally(() => {
            hideTableLoading();
          });
      },
      onClose: () => {
        confirmInstance.hide();
      },
    });
  };

  // 编辑自定义metric配置（只允许修改metric_name和description）
  const handleUpdateMetric = async (formData: IMetricForm) => {
    LoadingPlugin(true);
    const { metric_name: metricName, description } = formData;
    await updateCustomMetric({
      id: currentEditData.value?.id,
      // 如果前端的metric_name没有修改，则传undefined，后端不做修改
      metric_name: metricName === currentEditData.value?.metric_name ? undefined : metricName,
      description,
      game: gameStore.gameCode,
    })
      .then((res) => {
        LoadingPlugin(false);
        // 修改成功后刷新列表
        if (res?.id) {
          hideDrawer();
          getMetricList();
        }
      })
      .catch(() => {
        LoadingPlugin(false);
      });
  };

  // 打开【新增/编辑】抽屉
  const openDrawer = (row?: IMetric) => {
    isShowDrawer.value = true;
    isEditDrawer.value = !!row?.id;
    currentEditData.value = row ?? null;
  };

  // 隐藏抽屉
  const hideDrawer = () => isShowDrawer.value = false;

  // 分页数据改变时触发
  const onPageChange = ({ current, pageSize }: PageInfo) => {
    pagination.value.pageIndex = current;
    pagination.value.pageSize = pageSize;
    getMetricList();
  };

  /**
   * 拖拽排序时触发
   * @param sort 拖拽类型
   * @param current 当前拖拽的数据
   * @param targetIndex 目标位置的索引
   * @param newData 拖拽后的新数据
   */
  const onDragSort = async ({ sort, current, targetIndex, newData }: DragSortContext<IMetric>) => {
    // 行拖拽排序事件
    if (sort === 'row') {
      metricList.value = newData;
      // 移动到目标后该行前一行的index，如果是首行则为0
      const prevIndex = newData[targetIndex - 1]?.index || 0;
      // 移动到目标后该行后一行的index，如果是末行则为前一行的 index+1000
      const lastIndex = newData[targetIndex + 1]?.index || (prevIndex + 1000);
      // 根据前后行，计算新的index
      const newIndex = (prevIndex + lastIndex) / 2;
      // 设置新的index
      // eslint-disable-next-line no-param-reassign
      current.index = newIndex;
      showTableLoading();
      await sortCustomMetric({
        id: current.id,
        index: newIndex,
        game: gameStore.gameCode,
      })
        .finally(() => {
          getMetricList(false);
        });
    }
  };

  // 打开预览弹窗
  const openPreviewDialog = async () => {
    previewDialogVisible.value = true;
    showPreviewDialogLoading();
    const res = await getAllCustomMetricConfig({ game: gameStore.gameCode }).finally(() => {
      hidePreviewDialogLoading();
    });
    // 预览弹窗的数据结构：{ groupName: string, list: { title: string, colKey: string, tips: string }[] }[]
    previewMetricList.value = res.list.map(group => ({
      groupName: group.label,
      list: group.children.map((item) => {
        // 获取展示文本
        const tips = getDescDetail({
          descRulesList: res.descRuleList,
          title: item.label,
          ctx: {},
        }) || '';
        return {
          title: item.label,
          colKey: item.value,
          tips,
        };
      }),
    }));
  };

  // 初始化
  const init = async () =>  {
    getMetricList();
    getEventOptList();
  };

  return {
    isShowDrawer,
    isTableLoading,
    isPreviewDialogLoading,
    metricType,
    metricList,
    pagination,
    isEditDrawer,
    currentEditData,
    eventList,
    previewDialogVisible,
    previewMetricList,
    init,
    onTabChange,
    openDrawer,
    openPreviewDialog,
    handleCreateMetric,
    handleDeleteMetric,
    handleUpdateMetric,
    handleSwitchMetricVisible,
    onPageChange,
    onDragSort,
  };
});
