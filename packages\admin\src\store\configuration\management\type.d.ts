
export enum METRIC_TYPE {
  COHORT = 'cohort',
  DAILY = 'daily',
}

export enum METRIC_FORMAT {
  PROPORTION = 'proportion',
  ABSOLUTE = 'absolute',
}
export enum VALUE_TYPE {
  USER_NUM = 'user_num',
  EVENT_NUM = 'event_num',
  EVENT_VALUE = 'event_value',
}

type TMetricType = METRIC_TYPE.COHORT | METRIC_TYPE.DAILY;

export interface IMetric {
  id: string;
  metric_name: string;
  metric_type: TMetricType;
  description?: string;
  event_name: string;
  value_type: VALUE_TYPE;
  status: 0 | 1;
  is_visible: 0 | 1;
  format?: METRIC_FORMAT;
  index?: number;
  is_cumulative?: 0 | 1;
  create_time?: {
    value: string;
  };
  creator?: string;
  update_time?: {
    value: string;
  };
}

export interface IMetricForm {
  metric_name: string;
  metric_type: TMetricType;
  description?: string;
  event_name: string;
  value_type: VALUE_TYPE;
  format?: METRIC_FORMAT;
  is_cumulative?: 0 | 1;
}

export interface IOption {
  label: string,
  value: string,
}

export interface IPreviewMetric {
  label: string,
  value: string,
  children: IOption[],
}
