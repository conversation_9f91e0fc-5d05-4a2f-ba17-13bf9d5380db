import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { useTips } from 'common/compose/tips';
import { ref } from 'vue';
import { useLoading } from 'common/compose/loading';
import { getUserDetail } from 'common/service/configuration/profile';
import { GetUserDetailReturn } from 'common/service/configuration/profile/type';

export const useProfileStore = defineStore(STORE_KEY.CONFIGURATION.PROFILE, () => {
  const { err } = useTips();

  const user = ref<GetUserDetailReturn|null>(null);
  const { isLoading, showLoading, hideLoading } = useLoading(false);

  const init = async () => {
    try {
      showLoading();
      const user = await getUserDetail();
      changeUser(user);
    } catch (error) {
      err((error as any)?.message ?? 'Load user data failed.');
    } finally {
      hideLoading();
    }
  };

  const changeUser = (newUser: GetUserDetailReturn) => {
    user.value = newUser;
  };


  return {
    init,
    isLoading,
    user,
    changeUser,
  };
});

export default useProfileStore;
