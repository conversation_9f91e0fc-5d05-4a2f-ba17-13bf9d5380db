import { STORE_KEY } from '@/config/config';
import { defineStore, storeToRefs } from 'pinia';
import { computed, ref } from 'vue';
import { useGlobalGameStore } from '@/store/global/game.store';
import { RoleItem, ParentDictItem, SYSTEM_ROLE_IDS } from '@/views/configuration/role/components/type.d';
import { useTips } from 'common/compose/tips';
import { useLoading } from 'common/compose/loading';
import {
  getAllRolesByGame,
  getFunctionAccessList,
  getMetricList,
  getRegionList,
  getMediaSource,
  getKolRegionList,
} from 'common/service/configuration/role/role';
import { useFetchWrapper } from 'common/compose/request/request';
import { useWatchGameChange } from 'common/compose/request/game';

export const useRoleStore = defineStore(STORE_KEY.CONFIGURATION.ROLE, () => {
  const { err } = useTips();
  const { gameCode } = storeToRefs(useGlobalGameStore());;
  const { isLoading, showLoading, hideLoading } = useLoading();
  const roleInfoLoading = ref(false);
  const roleListDisabled = ref(false);
  const sysMenuParentDictionary = ref<ParentDictItem[]>([]);
  const sysMenuLeavesValues = ref<string[]>([]);

  // 当前游戏的所有角色
  const curRolesByGame = ref<any[]>([]);

  // 当前选择的角色
  const curRole = ref<RoleItem>();

  // 路由目录
  const sysMenuTree = computed(() => functionAccessList?.value || []);

  // 指标
  const metricTree = computed(() => (metricAccessList.value?.list) || []);

  // 素材目录
  const mediaSrcTree = computed(() => (mediaSourceAccessList.value) || []);

  // 区域
  const geoTree = computed(() => (regionAccessList.value) || []);

  const kolRegion = computed(() => (kolRegionAccessList.value) || []);

  const { data: functionAccessList, emit: getFunctionAccessListEmit } = useFetchWrapper(getFunctionAccessList, {}, {
    storage: 'FunctionAccessList',
    immediately: true,
  });

  const { data: metricAccessList, emit: getMetricListEmit } = useFetchWrapper(getMetricList, {}, {
    storage: 'MetricAccessList',
    immediately: true,
  });

  const { data: regionAccessList, emit: getRegionListEmit } = useFetchWrapper(getRegionList, {}, {
    storage: 'RegionAccessList',
    immediately: true,
  });

  const { data: mediaSourceAccessList, emit: getMediaSourceEmit } = useFetchWrapper(getMediaSource, {}, {
    storage: 'mediaSourceAccessList',
    immediately: true,
  });

  const { data: kolRegionAccessList, emit: getKolRegionListEmit } = useFetchWrapper(getKolRegionList, {}, {
    storage: 'KolRegionAccessList',
    immediately: true,
  });

  const init = async () => {
    useWatchGameChange(async () => {
      getMetricListEmit();
      getRegionListEmit();
      getKolRegionListEmit();
      getMediaSourceEmit();
      getFunctionAccessListEmit();
      initGameList();
    });
  };

  async function initGameList() {
    try {
      showLoading();
      curRolesByGame.value = [];
      curRole.value = undefined;
      const data = await getAllRolesByGame();
      curRolesByGame.value = data as unknown as any[];
    } catch (e) {
      err((e as any)?.message || 'Role list data load error');
    } finally {
      hideLoading();

      // 进入页面默认选中 Admin
      curRole.value =         curRolesByGame.value.find(item => item.id === SYSTEM_ROLE_IDS.ADMIN)
        || curRolesByGame.value[0];
    }
  }

  function duplicateRole() {
    curRolesByGame.value.push({ ...curRole.value, id: 0, role_name: `${curRole.value?.role_name} copy`, count: undefined });
    curRole.value = curRolesByGame.value[curRolesByGame.value.length - 1];
  }

  function cancelDuplicate() {
    curRolesByGame.value = curRolesByGame.value.filter(item => item.id !== 0);
  }

  function resetRoleManagePage() {
    if (curRole.value) {
      curRole.value = undefined;
      roleListDisabled.value = false;
      roleInfoLoading.value = false;
      cancelDuplicate();
    }
  }

  return {
    init,
    gameCode,
    curRolesByGame,
    curRole,
    sysMenuParentDictionary,
    sysMenuLeavesValues,
    sysMenuTree,
    metricTree,
    mediaSrcTree,
    geoTree,
    kolRegion,
    roleInfoLoading,
    roleListDisabled,
    initGameList,
    duplicateRole,
    cancelDuplicate,
    resetRoleManagePage,
    isLoading,
  };
});


