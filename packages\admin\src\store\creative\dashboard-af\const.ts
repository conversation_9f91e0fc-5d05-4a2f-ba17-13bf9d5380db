import { COMPONENTS_MAP } from 'common/const/components';
import type { IFormDynamicItem } from 'common/components/FormContainer';
import { BaseCommonSearchInput } from 'common/components/CommonSearchInputBox';
import type { IModelValue as TBusinessTableModelValue } from 'common/components/BusinessTable';
import dayjs from 'dayjs';
import { h, defineComponent } from 'vue';
import Text from 'common/components/Text';


const ImpressionDate = defineComponent({
  render() {
    return h('div', { class: 'flex items-center gap-x-[4px]' }, [
      h(Text, { content: 'Impression Date' }),
      h(COMPONENTS_MAP['date-time-picker'], { ...this.$attrs }),
    ]);
  },
});

export const FORM_LIST: IFormDynamicItem[] = [
  // Dateframe
  {
    name: COMPONENTS_MAP['date-time-picker'],
    props: {
      dateRangePickerWidth: 260,
    },
    ext: {
      key: 'dtstatdate',
      label: 'Dateframe',
    },
  },
  // Asset Name
  {
    name: BaseCommonSearchInput,
    props: {
      label: 'Asset Name',
      class: 'w-[220px]',
    },
    ext: {
      key: 'asset_name',
      label: 'Asset Name',
    },
  },
  // Asset Type
  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      title: 'Asset Type',
      multiple: true,
    },
    ext: {
      key: 'asset_type',
      label: 'Asset Type',
      isAllowClose: true,
    },
  },
  // Media Source
  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      title: 'Media Source',
      multiple: true,
    },
    ext: {
      key: 'network',
      label: 'Media Source',
      isAllowClose: true,
    },
  },
  {
    name: ImpressionDate,
    props: {
      dateRangePickerWidth: 260,
    },
    ext: {
      key: 'impression_date',
      label: 'Impression Date',
      isAllowClose: true,
    },
  },
  //   下面的默认折叠
  // OS
  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      title: 'OS',
      multiple: true,
    },
    ext: {
      key: 'platform',
      label: 'OS',
      isAllowClose: true,
      isHide: true,
    },
  },

  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      title: 'Country',
      multiple: true,
    },
    ext: {
      key: 'country',
      label: 'Country',
      isAllowClose: true,
      isHide: true,
    },
  },

  {
    name: BaseCommonSearchInput,
    props: {
      label: 'Campaign Name',
    },
    ext: {
      key: 'campaign_name',
      label: 'Campaign Name',
      isAllowClose: true,
      isHide: true,
    },
  },

  {
    name: BaseCommonSearchInput,
    props: {
      label: 'Adgroup Name',
    },
    ext: {
      key: 'ad_group_name',
      label: 'Adgroup Name',
      isAllowClose: true,
      isHide: true,
    },
  },

  {
    name: BaseCommonSearchInput,
    props: {
      label: 'Ad Name',
      class: 'w-[200px]',
    },
    ext: {
      key: 'ad_name',
      label: 'Ad Name',
      isAllowClose: true,
      isHide: true,
    },
  },

  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      title: 'Asset Age',
      multiple: true,
    },
    ext: {
      key: 'asset_age',
      label: 'Asset Age',
      isAllowClose: true,
      isHide: true,
    },
  },
  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      title: 'Duration',
      multiple: true,
    },
    ext: {
      key: 'asset_duration',
      label: 'Duration',
      isAllowClose: true,
      isHide: true,
    },
  },
  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      title: 'Orientation',
      multiple: true,
    },
    ext: {
      key: 'orientation',
      label: 'Orientation',
      isAllowClose: true,
      isHide: true,
    },
  },
  {
    name: COMPONENTS_MAP['new-cascader'],
    props: {
      title: 'Ratio',
      mode: 'flat',
      levelList: [
        { label: 'Ratio', value: 'ratio' },
        { label: 'Resolution', value: 'resolution' },
      ],
    },
    ext: {
      key: 'resolution',
      label: 'Ratio',
      isAllowClose: true,
      isHide: true,
    },
  },

  {
    name: BaseCommonSearchInput,
    props: {
      label: 'Asset Link',
      class: 'w-[200px]',
    },
    ext: {
      key: 'asset_url',
      label: 'Asset Link',
      isAllowClose: true,
      isHide: true,
    },
  },
  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      title: 'Asset Serial',
      multiple: true,
    },
    ext: {
      key: 'asset_serial_id',
      label: 'Asset Serial',
      isAllowClose: true,
      isHide: true,
    },
  },
  {
    name: COMPONENTS_MAP['new-cascader'],
    props: {
      title: 'Label',
      mode: 'flat',
      levelList: [
        { label: 'First Label', value: 'first_label' },
        { label: 'Second Label', value: 'second_label' },
      ],
    },
    ext: {
      key: 'label',
      label: 'Label',
      isAllowClose: true,
      isHide: true,
    },
  },
];

export const FORM_KESY = FORM_LIST.map(item => item.ext.key);

export const FORM_DATA_SOURCE_KEY = {
  asset_type: 'list',
  network: 'list',
  platform: 'list',
  country: 'list',
  asset_age: 'list',
  asset_duration: 'list',
  orientation: 'list',
  resolution: 'options',
  asset_serial_id: 'list',
  label: 'options',
};

export const DEFAULT_FORM_MODEL_VALUE = {
  dtstatdate: [
    dayjs().subtract(7, 'day')
      .format('YYYYMMDD'),
    dayjs().subtract(1, 'day')
      .format('YYYYMMDD'),
  ],
  asset_type: [],
  network: [],
  impression_date: [],
  asset_name: [],
  platform: [],
  country: [],
  asset_age: [],
  orientation: [],
  campaign_name: [],
  ad_group_name: [],
  ad_name: [],
  asset_url: [],
  asset_duration: [],
  resolution: [],
  asset_serial_id: [],
  label: [],
};

export const DEFAULT_FORM_FOLD_LIST = [
  'platform',
  'country',
  'campaign_name',
  'ad_group_name',
  'ad_name',
  'asset_age',
  'asset_duration',
  'orientation',
  'resolution',
  'asset_url',
  'asset_serial_id',
  'label',
];

export const DEFAULT_TABLE_MODEL_VALUE: TBusinessTableModelValue = {
  groupby: ['asset_name'],
  metric: [
    'spend',
    'impressions',
    'clicks',
    'installs',
    'cpm',
    'ctr',
    'cvr',
    'ipm',
    'd1_roas',
    'd7_roas',
  ],
  orderby: [
    {
      by: 'spend',
      order: 'desc',
    },
  ],
  pageIndex: 1,
  pageSize: 20,
  filterTotal: false,
};


export const VIEW_MOUDEL = 'creative_dashboard_af';

export const TOTAL_TEXT = 'TOTAL';
