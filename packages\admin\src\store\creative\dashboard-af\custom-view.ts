import { Ref, computed, ref, ComputedRef } from 'vue';
import { cloneDeep } from 'lodash-es';
import { useCustomView } from 'common/compose/useCustomView';
import { ICustomViewItem, IFormData } from 'common/components/NewViewTab/type';
import { useLoading } from 'common/compose/loading';
import { useUrlSearchParams } from '@vueuse/core';

type THandelCustomViews = {
  module: string, // 模块
  gameCode: Ref<string>, // 游戏
  defaultViewParam: Ref<Record<string, any>> | ComputedRef<Record<string, any>>,
  shareViewParams: Ref<any> | ComputedRef<any>,
  onViewChange: Function,
  refreshData?: Function, // 重新数据回调函数
  isAllGame?: boolean,
  needSetViewIdToUrl?: boolean
};


function deleteUrlParam(key: string) {
  const currentUrl = new URL(window.location.href);
  currentUrl.searchParams.delete(key);
  // 使用 replaceState 更新 URL
  window.history.replaceState({}, '', currentUrl);
}

function setUrlParam(key: string, value: string) {
  // 获取当前 URL
  const currentUrl = new URL(window.location.href);
  // 替换或添加参数
  currentUrl.searchParams.set(key, value);
  // 使用 replaceState 更新 URL
  window.history.replaceState({}, '', currentUrl);
}


export function handelCustomViews(params: THandelCustomViews) {
  const {
    getList: getViewList, getShare, addView: addViewService,
    updateView: updateViewService, deleteView: deleteViewService,
  } = useCustomView();
  const {
    module, gameCode, defaultViewParam, shareViewParams, refreshData, onViewChange,
    isAllGame = false, needSetViewIdToUrl = false,
  } = params;
  const { isLoading: isViewLoading, showLoading: showViewLoading, hideLoading: hideViewLoading } = useLoading();

  // 用户自定义视图
  const customViewList = ref<ICustomViewItem[]>([]); // 视图列表
  // 分享视图
  const shareView = ref<ICustomViewItem | null>(null);

  // 默认视图
  const defaultView = computed<ICustomViewItem>(() => ({
    label: 'Default',
    value: 'default',
    game: gameCode.value,
    type: 'default',
    param: cloneDeep(defaultViewParam.value),
    module,
  }));

  // 视图列表
  const viewList = computed(() => [
    ...(shareView.value ? [shareView.value] : []),
    defaultView.value,
    ...customViewList.value,
  ]);

  const viewGames = computed(() => [gameCode.value, ...(isAllGame ? ['allgame'] : [])]);
  const viewId = ref<string>('default'); // 当前选择的视图的id
  const setViewId = (val: string) => {
    viewId.value = val;
    if (needSetViewIdToUrl) {
      setUrlParam('viewId', viewId.value);
    }
  };

  // 拉取自定义视图
  const fechCustomViewList = async () => {
    const res =  await getViewList({
      game: viewGames.value,
      module,
      type: 'custom',
    });
    customViewList.value = (res as ICustomViewItem[]).filter(item => viewGames.value.includes(item.game));
  };


  const initViewIdByUrl = (params: {urlCode?: string, urlViewId?: string, isUseShareView: boolean}) => {
    const { urlViewId, urlCode, isUseShareView } = params;
    if (!urlCode && !urlViewId) {
      setViewId('default');
      return;
    }
    // 有viewId, 优先使用viewId，不管是不是分享视图进来
    if (urlViewId) {
      const newViewId = getViewItem(urlViewId) ? urlViewId :  'default';
      setViewId(newViewId);
      return;
    }
    // 没有viewId， 并且是分享视图进来的情况下
    if (urlCode && !urlViewId) {
      const newViewId = isUseShareView ? 'share' : 'default';
      setViewId(newViewId);
    }
  };

  /**
   * 初始化视图，
   * 调用时机， 页面加载
  */
  const init = async () => {
    showViewLoading();
    shareView.value = null;
    const urlParams = useUrlSearchParams<{ code: string, viewId: string }>('history');
    const { code: urlCode, viewId: urlViewId } =  urlParams || {};

    // 先把视图和分享视图拉回来
    const [shareRes] = await Promise.all([
      urlCode ? getShare(urlCode) : null,
      fechCustomViewList(),
    ]);

    // 判断要不要使用分享视图
    const isUseShareView = !shareRes ? false : viewGames.value.includes(shareRes.game);
    if (isUseShareView) {
      shareView.value = shareRes;
    }
    if (needSetViewIdToUrl) {
      initViewIdByUrl({ urlCode, urlViewId, isUseShareView });
    } else {
      const newViewId = isUseShareView ? 'share' : 'default';
      setViewId(newViewId);
    }
    // 删除url中的code参数
    if (urlCode && !isUseShareView) {
      deleteUrlParam('code');
    }
    hideViewLoading();
  };

  // 生成新增或者更新视图时，接口需要的参数
  const generateViewParams = ({ formData }: { formData: IFormData}) => {
    const { viewType, viewName } = formData;
    return {
      ...shareViewParams.value,
      param: {
        ...shareViewParams.value.param,
        game: viewType === 'all' && isAllGame ? 'allgame' : gameCode.value,
      },
      game: gameCode.value,
      type: 'custom',
      name: viewName,
    };
  };

  // 新增视图
  const onAddView = async (data: { formData: IFormData}) => {
    await addViewService(generateViewParams(data));
    await fechCustomViewList();
  };

  // 更新视图
  const onUpdateView = async (data: { formData: IFormData}) => {
    await updateViewService({
      ...generateViewParams(data),
      id: viewId.value,
    });
    await fechCustomViewList();
  };

  // 删除视图
  const onDeleteView = async (viewItem: ICustomViewItem) => {
    await deleteViewService({ id: viewItem.value });
    setViewId(defaultView.value.value);
    onViewChange(defaultView.value);
    await fechCustomViewList();
    refreshData?.();
  };

  // 根据当前视图id查找视图
  const getViewItem = (id: string) => viewList.value.find(item => item.value === id)!;

  return {
    viewList, viewId, isViewLoading,
    isDefaultView: computed(() => viewId.value === 'default'),
    setViewId, onAddView, onUpdateView, onDeleteView, getViewItem, initViewList: init,
  };
}
