import { defineStore, storeToRefs } from 'pinia';
import { STORE_KEY } from '@/config/config';
import {
  DEFAULT_FORM_MODEL_VALUE, DEFAULT_FORM_FOLD_LIST,
  DEFAULT_TABLE_MODEL_VALUE, VIEW_MOUDEL, TOTAL_TEXT,
} from './const';
import { computed, ref } from 'vue';
import {
  getOptionsService, getLineChartDataService, getTableDataService,
  getMaxMinDateService,
} from 'common/service/creative/dashboard-af/index';
import {
  generateFormRenderList, generateApiReqParams,
  replaceDateByLimit, generateMetricSwiperList,
} from './utils';
import { useGlobalGameStore } from '@/store/global/game.store';
import { cloneDeep, groupBy, intersection, isPlainObject, isFinite } from 'lodash-es';
import type { IModelValue as TBusinessTableModelValue } from 'common/components/BusinessTable';
import type { IItem as IMetricCardItem } from 'common/components/MetricCard/type';
import { handelCustomViews } from './custom-view';
import type { IDashboardAfViewParam, TFormModelValue } from './type';
import { useLoading } from 'common/compose/loading';
import type { TDateLimit, IGetOptionRes } from 'common/service/creative/dashboard-af/type';


export const useCreativeDashboardAfStore = defineStore(
  STORE_KEY.CREATIVE.DASHBOARD_AF, () => {
    const gameStore = useGlobalGameStore();
    const { gameCode } = storeToRefs(gameStore);
    /* ---------------------------------loading开始----------------------------------*/
    const { isLoading: isTableLoading, showLoading: showTableLoading, hideLoading: hideTableLoading } = useLoading();
    const { isLoading: isChartLoading, showLoading: showChartLoading, hideLoading: hideChartLoading } = useLoading();
    const { isLoading: isInitLoading, showLoading: showInitLoading, hideLoading: hideInitLoading } = useLoading();
    /* ---------------------------------loading结束----------------------------------*/

    /* ---------------------------------表单开始----------------------------------*/
    const dateLimit = ref<TDateLimit>({ maxDate: '', minDate: '', minImpressionDate: '' });
    const formDataSource = ref<Record<string, any>>({});
    const formRenderList = computed(() => generateFormRenderList(formDataSource.value, dateLimit.value));
    const formModelValue = ref<TFormModelValue>(cloneDeep(DEFAULT_FORM_MODEL_VALUE));
    const formFoldList = ref<string[]>([...DEFAULT_FORM_FOLD_LIST]);
    const updateFormFoldList = (val: string[]) => formFoldList.value = [...val];
    const updateFormModelValue = (val: TFormModelValue) => formModelValue.value = cloneDeep(val);
    const onFormSubmit = () => {
      updateTableModelValue({ ...tableModelValue.value, pageIndex: 1 });
      getTableData();
      getLineChart();
    };
    const onFormReset = () => {
      updateCondition(getViewItem(viewId.value));
      getTableData();
      getLineChart();
    };
    /* ---------------------------------表单结束----------------------------------*/


    /* ---------------------------------自定义视图开始----------------------------------*/
    // 默认视图的param
    const defaultViewParam = ref<IDashboardAfViewParam>({
      form: DEFAULT_FORM_MODEL_VALUE as TFormModelValue,
      table: {
        metric: DEFAULT_TABLE_MODEL_VALUE.metric,
        groupby: DEFAULT_TABLE_MODEL_VALUE.groupby as string[],
        orderby: DEFAULT_TABLE_MODEL_VALUE.orderby,
        pageSize: DEFAULT_TABLE_MODEL_VALUE.pageSize,
      },
      fold_list: formFoldList.value,
    });
    // 分享视图的参数
    const shareViewParams = computed(() => ({
      game: gameCode.value,
      param: {
        form: { ...formModelValue.value },
        table: {
          pageSize: tableModelValue.value.pageSize,
          groupby: tableModelValue.value.groupby,
          orderby: tableModelValue.value.orderby,
          metric: tableModelValue.value.metric,
        },
        fold_list: formFoldList.value, // 多存一份
      },
      fold_list: formFoldList.value, // 分享视图接口中有返回， 但是视图列表接口中没有返回
      system: VIEW_MOUDEL,
    }));
    // 视图处理的方法（curd， 分享都在里面）
    const {
      viewId, viewList, isViewLoading,
      onAddView, onUpdateView, onDeleteView, initViewList, getViewItem, setViewId,
    } = handelCustomViews({
      module: VIEW_MOUDEL,
      gameCode,
      defaultViewParam,
      shareViewParams,
      needSetViewIdToUrl: true,
      onViewChange: () => {
        updateCondition(getViewItem(viewId.value));
      },
      refreshData: () => {
        getTableData();
        getLineChart();
      },
    });
    // 页面上切换视图
    function onViewChange(newViewId: string) {
      setViewId(newViewId);
      updateCondition(getViewItem(viewId.value));
      getTableData();
      getLineChart();
    };
    /* ---------------------------------自定义视图结束----------------------------------*/

    /* ---------------------------------表格开始----------------------------------*/
    // 筛选条件
    const attrList = ref<IGetOptionRes['attr_list']>([]);
    const metricList = ref<IGetOptionRes['metric_list']>([]);
    const groupMetricList = computed(() => groupBy(metricList.value, 'key'));
    const tableModelValue = ref<TBusinessTableModelValue>(cloneDeep(DEFAULT_TABLE_MODEL_VALUE));
    const updateTableModelValue = (val: TBusinessTableModelValue) => {
      tableModelValue.value = cloneDeep(val);
    };
    // 数据
    const tableDataList = ref<Record<string, any>[]>([]);
    const tableDataCount = ref<number>(0);
    /* ---------------------------------表格结束----------------------------------*/

    /* ---------------------------------折线图结束----------------------------------*/
    const lineChartData =  ref<Record<string, any>[]>([]);
    /* ---------------------------------折线图结束----------------------------------*/


    /* ---------------------------------指标卡开始----------------------------------*/
    const cardActiveMetric = ref<string>(DEFAULT_TABLE_MODEL_VALUE.metric[0]);
    const metricCardActiveIndex = computed(() => (
      tableModelValue.value.metric.findIndex(item => item === cardActiveMetric.value)
    ));
    const cardActiveMetricLabel = computed(() => (
      groupMetricList.value[cardActiveMetric.value]?.[0]?.title ?? cardActiveMetric.value
    ));
    const metricSwiperList = ref<IMetricCardItem[]>([]);
    const onMetricCardChange = (activeIndex: number, item: IMetricCardItem) => {
      cardActiveMetric.value = item.colKey as string;
    };
    /* ---------------------------------指标卡结束----------------------------------*/


    function updateCondition(condition: {param: IDashboardAfViewParam}) {
      // 所有的metric和attr
      const fullMetric = metricList.value.map(item => item.key);
      const fullAttrs = attrList.value.map(item => item.key);

      const { param } = condition;
      const { fold_list: paramFoldList } = param;
      const { dtstatdate: paramDtstatdate = [], impression_date: paramImpressionDate = [] } = param.form;
      const { groupby: paramGroupby, metric: paramMetric, orderby: paramOrderby = [] } = param.table;
      // 表单折叠
      updateFormFoldList(paramFoldList ?? DEFAULT_FORM_FOLD_LIST);

      // 防止报错: orderby 必须要存在于metric中
      const newOrderby = paramOrderby.filter(item => fullMetric.includes(item.by));

      // 表单的值
      // 要限制最大时间和最小时间
      const newFormValue = {
        ...param.form,
        dtstatdate: replaceDateByLimit(paramDtstatdate, [dateLimit.value.minDate, dateLimit.value.maxDate]),
        impression_date: replaceDateByLimit(paramImpressionDate, [dateLimit.value.minImpressionDate, dateLimit.value.maxDate]),
      };
      updateFormModelValue(newFormValue);
      // 更新attr和group
      updateTableModelValue({
        ...tableModelValue.value,
        // 防止报错：保险起见先求交集，再赋值，防止历史视图的中出现已经被删除的指标
        groupby: [...intersection(paramGroupby, fullAttrs)],
        metric: [...intersection(paramMetric, fullMetric)],
        orderby: newOrderby.length > 0 ? newOrderby : [{ by: fullMetric[0], order: 'desc' }],
        pageSize: param.table.pageSize,
      });

      // 卡片的默认选中
      // eslint-disable-next-line prefer-destructuring
      cardActiveMetric.value = tableModelValue.value.metric[0];
    }


    // 初始化页面配置
    async function infnPageConfig() {
      showInitLoading();
      // 拉视图， 配置
      const [dateLimitRes, options] = await Promise.all([
        getMaxMinDateService(),
        getOptionsService(),
        initViewList(),
      ]);
      hideInitLoading();
      // 表单说数据源
      formDataSource.value = options.filters;
      // 表格的groupby和metric
      attrList.value = options.attr_list;
      metricList.value = options.metric_list;
      // 最大最小时间
      dateLimit.value = dateLimitRes;
      // 设置筛选条件
      updateCondition(getViewItem(viewId.value));
      Promise.all([
        getTableData(),
        getLineChart(),
      ]);
    }

    async function getTableData() {
      const { tableParams } = generateApiReqParams(formModelValue.value, tableModelValue.value);
      showTableLoading();
      const res = await getTableDataService({ ...tableParams });
      const { filterTotal, groupby } = tableModelValue.value;
      // 把total追加到表格的第一行
      const isAppendTotal = !filterTotal && isPlainObject(res.total);
      tableDataList.value = [
        ...(isAppendTotal ? [{ ...res.total, [groupby[0]]: TOTAL_TEXT }] : []),
        ...(res.data.map(item => ({
          ...item,
          asset_url: item.extra_asset_url,
          asset_type: item.extra_asset_type,
          asset_serial_id: item.extra_asset_serial_id,
        }))),
      ];
      if (isFinite(res.count)) {
        tableDataCount.value = res.count as number;
      }
      // 处理卡片
      if (res.total) {
        metricSwiperList.value = generateMetricSwiperList(tableParams.metric, res.total, groupMetricList.value);
      }
      hideTableLoading();
    }

    async function getLineChart() {
      showChartLoading();
      const { chartParams } = generateApiReqParams(formModelValue.value, tableModelValue.value);
      const res = await getLineChartDataService(chartParams);
      lineChartData.value = res.data;
      hideChartLoading();
    }

    return {
      formRenderList,
      formModelValue,
      formFoldList,
      attrList,
      metricList,
      tableModelValue,
      metricSwiperList,
      metricCardActiveIndex,
      tableDataCount,
      tableDataList,
      lineChartData,
      cardActiveMetric,
      cardActiveMetricLabel,
      groupMetricList,
      // 自定义视图相关
      viewList, viewId, isViewLoading, shareViewParams,
      // loading
      isTableLoading, isChartLoading, isInitLoading,
      infnPageConfig,
      onFormSubmit,
      onFormReset,
      updateFormFoldList,
      updateFormModelValue,
      updateTableModelValue,
      onMetricCardChange,
      getTableData,
      getLineChart,
      // 自定义视图相关
      onViewChange, onAddView, onUpdateView, onDeleteView, getViewItem,
    };
  },
);
