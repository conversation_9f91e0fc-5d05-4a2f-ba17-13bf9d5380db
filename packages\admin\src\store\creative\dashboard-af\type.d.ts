import type { <PERSON><PERSON><PERSON><PERSON> } from 'common/components/BusinessTable';

export interface IDashboardAfViewParam {
  form: TFormModelValue,
  fold_list: string[]
  table: {
    pageSize: number,
    groupby: string[],
    metric: string[],
    orderby: IOrderby[]
  },
}

export type TFormModelValue = {
  dtstatdate: string[],
  asset_name: string[],
  asset_type: string[],
  network: string[],
  impression_date: string[],
  platform: string[],
  country: string[],
  campaign_name: string[],
  ad_group_name: string[],
  ad_name: string[],
  asset_age: string[],
  asset_duration: string[],
  orientation: string[],
  resolution: string[],
  asset_url: string[],
  asset_serial_id: string[]
  label: string[]
}
