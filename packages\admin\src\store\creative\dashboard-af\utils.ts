import { FORM_DATA_SOURCE_KEY, FORM_LIST } from './const';
import type { TDateLimit, IGetOptionRes, IMetricItem } from 'common/service/creative/dashboard-af/type';
import { groupBy, omit, isArray, difference } from 'lodash-es';
import { IBusinessTableMetricGroup } from 'common/components/BusinessTable';
import type { IModelValue as TBusinessTableModelValue } from 'common/components/BusinessTable';
import { TFormModelValue } from './type';
import type { IItem as IMetricCardItem } from 'common/components/MetricCard/type';
import dayjs from 'dayjs';
import TemplateHelper from 'common/utils/template/templateHelper';
import { getHasNALabelValue } from '../top/utils';
import { NA_LABEL_SON_STR } from '../labels/const';

function getDatePresets(dateRange: string[]) {
  const [minDate, maxDate] = dateRange;
  const dataPresets = {
    'Last 7 Days': [dayjs(maxDate).subtract(7, 'day')],
    'Last 30 Days': [dayjs(maxDate).subtract(30, 'day')],
    'Last 90 Days': [dayjs(maxDate).subtract(90, 'day')],
    'Last 180 Days': [dayjs(maxDate).subtract(180, 'day')],
    'Last 365 Days': [dayjs(maxDate).subtract(365, 'day')],
    'Last 4 Weeks': [dayjs(maxDate).subtract(4, 'week')],
    'Last 12 Weeks': [dayjs(maxDate).subtract(12, 'week')],
    'Last 24 Weeks': [dayjs(maxDate).subtract(24, 'week')],
    // [maxData当前年第一天,maxData]
    'Year to Date': [dayjs(maxDate).startOf('year')],
    'All of Time': [dayjs(minDate || '20200101')],
  };
  const presetKeys = Object.keys(dataPresets);
  const result: Record<string, [string, string]> = {};
  presetKeys.forEach((presetKey) => {
    let startDate = dataPresets[presetKey as keyof typeof dataPresets][0].format('YYYYMMDD');
    if (minDate && Number(startDate) < Number(minDate)) {
      startDate = minDate;
    }
    result[presetKey] = [startDate, dayjs(maxDate).format('YYYYMMDD')];
  });
  return result;
}

// 生成表单
export function generateFormRenderList(formDataSource: Record<string, any>, dateLimit: TDateLimit) {
  const allowDateRangeObj: Record<string, string[]> = {
    dtstatdate: [dateLimit.minDate, dateLimit.maxDate],
    impression_date: [dateLimit.minImpressionDate, dateLimit.maxDate],
  };
  return FORM_LIST.map((formCfgItem) => {
    const formItemKey = formCfgItem.ext.key;
    const dataSourcekey = (FORM_DATA_SOURCE_KEY as Record<string, string>)[formItemKey];
    let newFormCfgItem = formCfgItem;
    // 数据源
    if (dataSourcekey) {
      newFormCfgItem = {
        ...newFormCfgItem,
        props: {
          ...newFormCfgItem.props,
          [dataSourcekey]: formDataSource[formItemKey] ?? [],
        },
      };
    }
    // 最大最小时间限制
    if (Object.keys(allowDateRangeObj).includes(formItemKey)) {
      const allowDateRange = allowDateRangeObj[formItemKey];
      newFormCfgItem = {
        ...newFormCfgItem,
        props: {
          ...newFormCfgItem.props,
          maxDate: allowDateRange[1],
          minDate: allowDateRange[0],
          presets: getDatePresets(allowDateRange),
        },
      };
    }
    return newFormCfgItem;
  });
}

// metric弹窗分组
export function formatMetriclist(metricList: IGetOptionRes['metric_list']) {
  const displayMetricList = metricList.filter(item => !item.hidden);
  const groupMetric = groupBy(displayMetricList, 'type');
  const typeList = Object.keys(groupMetric);
  const result: IBusinessTableMetricGroup[] = [];
  typeList.forEach((typeItem) => {
    const children = (groupMetric[typeItem] ?? []).map(item => ({
      label: item.title,
      value: item.key,
      tips: item.tooltip,
    }));
    result.push({
      label: typeItem,
      value: typeItem,
      children,
    });
  });
  return result;
}

// 生成调api的参数
export function generateApiReqParams(formModelValue: TFormModelValue, tableModelValue: TBusinessTableModelValue) {
  const keywordKeys = ['asset_name', 'campaign_name', 'ad_group_name', 'ad_name', 'asset_url'];
  const { dtstatdate } = formModelValue;
  const { groupby, orderby, metric, pageIndex, pageSize } = tableModelValue;

  /** -----------------------表格和折线图的公共参数 开始 ------------------------ */
  // 搜索框的数据， 全部放到keywords中
  const keywordsList = keywordKeys
    .map(item => ({
      key: item,
      val: formModelValue[item as keyof typeof formModelValue] || [],
    }))
    .filter(item => item.val.length > 0);

  // 排除掉一些传给接口时 需要转格式的字段
  const whereFormData = omit(
    {
      ...formModelValue,
      country_code: formModelValue.country,
    },
    [...keywordKeys, 'dtstatdate', 'country'],
  );
  const whereFormKeys = Object.keys(whereFormData);
  // 过滤掉空值
  const whereData: Record<string, any> = {};
  whereFormKeys.forEach((key) => {
    const value = whereFormData[key as keyof typeof whereFormData];
    if (isArray(value) && value.length > 0) {
      whereData[key] = value;
    }
  });
  const commonParams = {
    where: {
      ...whereData,
      label: whereData.label?.filter((v: string) => v !== NA_LABEL_SON_STR),
      ...(keywordsList.length > 0 ? { keywords: keywordsList } : {}),
      startDate: dtstatdate[0],
      endDate: dtstatdate[1],
    },
    orderby,
    metric,
    has_na_label: getHasNALabelValue(whereData.label || [], false),
  };
  /** -----------------------表格和折线图的公共参数 结束 ------------------------ */

  // 处理group
  let groupbyData = [...(groupby as string[])];
  // 如果包含了asset_name， 就要把asset_url，asset_type， asset_serial_id 给排除掉
  if (groupbyData.includes('asset_name')) {
    groupbyData = difference(groupbyData, ['asset_url', 'asset_type', 'asset_serial_id', 'youtube_id']);
  }

  return {
    tableParams: {
      ...commonParams,
      group: groupbyData,
      pageIndex: pageIndex - 1,
      pageSize,
      needCount: pageIndex === 1,
      needTotal: pageIndex === 1,
    },
    chartParams: {
      ...commonParams,
      group: ['dtstatdate'],
    },
  };
}

export function generateMetricSwiperList(
  metric: string[],
  totalData: Record<string, any>,
  groupMetricList: Record<string, IMetricItem[]>,
) {
  const result: IMetricCardItem[] = [];
  const instance = new TemplateHelper();
  metric.forEach((key) => {
    const value = totalData[key];
    const metricItem = groupMetricList[key]?.[0];
    const { format, title } = metricItem;
    const formatValue = getFormatRule({ isCard: true })[format] ?? [];
    const formatData = instance.formatData({ [key]: value }, [
      {
        name: key,
        value: formatValue,
        type: '',
      },
    ]);
    result.push({
      title: title ?? key,
      colKey: key,
      toolTip: metricItem.tooltip,
      indicatorValues: formatData[key] as string,
    });
  });
  return result;
}

// 替换时间，开始时间不能小于minDate， 结束时间不能大于maxDate
export function replaceDateByLimit(date: string[], dateRange: string[]) {
  const [minDate, maxDate] = dateRange;
  const newDate = [...date];
  const startDate = Number(date[0]);
  const endDate = Number(date[0]);
  if (maxDate && endDate > Number(maxDate)) newDate[1] = maxDate;
  if (minDate && startDate < Number(minDate)) newDate[0] = minDate;
  return newDate;
}

export function getFormatRule(params?: { isCard?: boolean; decimalLen?: number }): Record<string, string[]> {
  const { isCard = false, decimalLen = 2 } = params ?? {};
  return {
    numShort: ['emptyShow', 's1000'],
    percent: ['emptyShow', 'percent'],
    money: ['emptyShow', `decimal(${decimalLen})`, `s1000(${decimalLen})`, ...(isCard ? ['stradd(head,$)'] : [])],
  };
}
