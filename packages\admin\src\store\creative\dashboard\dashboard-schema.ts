import { UseCommonSearchReturn } from '@/compose/useCommonSearch';
import { COMMON_SEARCH_SCHEMA, GPP_SEARCH_SCHEMA, HOK_SEARCH_SCHEMA, IMPRESSION_DATE_SCHEMA, PUBGM_SEARCH_SCHEMA } from '@/views/creative/common/components/const';
import { unRefObj } from 'common/utils/reactive';
import dayjs from 'dayjs';
import { DtstattimeItem } from './dashboard';
import { DEMO_DELETE_FILTER_KEY, FORMAT } from './dashboard.const';
import { schemaAddOptions } from './utils';
import { needImpressionDateSchema } from './utils-schema';

export const setSearchSchema = ({
  version,
  creativeSearchStore,
  updateVersion,
  searchSortList = [],
  maxDate,
  minDate,
  options = {},
  dtstattimeList = [],
  game,
  needReset,
  onReset,
  hideSchema,
  dtstattimePresets,
  changeDtstattimePresets,
}: {
  version?: string,
  dtstattimePresets?: string,
  creativeSearchStore?: UseCommonSearchReturn<any, any>,
  updateVersion?: Function,
  changeDtstattimePresets?: Function,
  searchSortList?: string[],
  maxDate?: string,
  minDate?: string,
  options?: any,
  dtstattimeList?: DtstattimeItem[],
  game?: string,
  needReset?: string[],
  onReset?: boolean,
  hideSchema?: string[],
}) => {
  // 通过searchSortList来排序
  let searchSchema = COMMON_SEARCH_SCHEMA.map((item) => {
    if (item.ext.key === 'dtstattime') {
      return {
        ...item,
        props: {
          ...item.props,
          version,
          dtstattimePresets,
          options: dtstattimeList,
          disableDate: {
            before: dayjs(minDate).format(FORMAT),
            after: dayjs(maxDate).format(FORMAT),
          },
          onChangeVersion: (value: string) => {
            // version.value = value;
            updateVersion?.(value);
            // 修改schema中的值
            creativeSearchStore?.updateSchema(creativeSearchStore.schema.value.map((item: any) => {
              const tempItem = unRefObj(item);
              if (tempItem.ext.key === 'dtstattime') {
                return {
                  ...tempItem,
                  props: {
                    ...tempItem.props,
                    version: value,
                  },
                };
              }
              return tempItem;
            }));
          },
          onChangePresets: (value: string) => {
            changeDtstattimePresets?.(value);
            // 修改schema中的值
            // creativeSearchStore?.updateSchema(creativeSearchStore.schema.value.map((item: any) => {
            //   const tempItem = unRefObj(item);
            //   if (tempItem.ext.key === 'dtstattime') {
            //     return {
            //       ...tempItem,
            //       props: {
            //         ...tempItem.props,
            //         dtstattimePresets: value,
            //       },
            //     };
            //   }
            //   return tempItem;
            // }));
          },
        },
        ext: {
          ...item.ext,
          isAllowClose: false,
          isHide: !searchSortList?.includes('dtstattime'),
        },
      };
    }
    if (item.ext.key === 'impression_date') {
      const tempItem = {
        ...item,
        props: {
          ...item.props,
          maxDate,
          disableDate: {
            before: dayjs(minDate).format(FORMAT),
            after: dayjs(maxDate).format(FORMAT),
          },
        },
      };
      return schemaAddOptions(tempItem, options, searchSortList);
    }
    if (needReset?.includes(item.ext.key)) {
      const schema = schemaAddOptions(item, options, searchSortList);
      return {
        ...schema,
        props: {
          ...schema.props,
          outReset: onReset,
          onOutReset: (value: boolean) => {
            creativeSearchStore?.updateSchema(creativeSearchStore.schema.value.map((sItem: any) => {
              const tempItem = unRefObj(sItem);
              if (tempItem.ext.key === item.ext.key) {
                return {
                  ...tempItem,
                  props: {
                    ...tempItem.props,
                    outReset: value,
                  },
                };
              }
              return tempItem;
            }));
          },
        },
      };
    }
    return schemaAddOptions(item, options, searchSortList);
  });
  if (game === 'pubgm') {
    searchSchema = searchSchema
      .concat(PUBGM_SEARCH_SCHEMA.map(item => schemaAddOptions(item, options, searchSortList)) as any);
  }
  if (game !== 'pubgm') {
    // gpp 游戏的
    searchSchema = searchSchema
      .concat(GPP_SEARCH_SCHEMA.map(item => schemaAddOptions(item, options, searchSortList)) as any);
  }
  if (game === 'hok_prod') {
    searchSchema = searchSchema
      .concat(HOK_SEARCH_SCHEMA.map(item => schemaAddOptions(item, options, searchSortList)) as any);
  }
  if (game === 'demo') {
    searchSchema = searchSchema.filter(item => !DEMO_DELETE_FILTER_KEY.includes(item.ext.key));
  }
  if (needImpressionDateSchema(game || '')) {
    searchSchema = searchSchema.concat(IMPRESSION_DATE_SCHEMA);
  }
  // demo需要啊去掉的参数
  searchSchema = searchSchema.filter(item => !hideSchema?.includes(item.ext.key));
  creativeSearchStore?.updateSchema(searchSchema);
};

