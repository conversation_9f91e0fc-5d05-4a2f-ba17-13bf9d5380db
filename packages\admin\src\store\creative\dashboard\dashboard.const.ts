import { OrderByType } from './dashboard';

export const COLOR: string[] = ['var(--aix-text-color-brand)', 'var(--aix-text-color-black-secondary)', 'var(--aix-text-color-brand)'];
export const DOWNLOAD_PAGE_SIZE = 2500;
export const PARALLEL_NUM = 10;
export const GRID = {
  top: 30,
  left: 0,
  right: 0,
  bottom: 40,
  containLabel: true,
};
export const getOriginMetric = (game: string) => INIT_METRIC.common.concat(game in INIT_METRIC
  ? INIT_METRIC[game] : INIT_METRIC.default);

export const getOriginCampaignType = (game: string) => (game === 'pubgm' ? ['newinstall', 'active', 'reattribution'] : []);

export const INIT_METRIC: {
  common: string[],
  default: string[],
  [key: string]: string[]
} = {
  common: [
    'spend',
    'impressions',
    'clicks',
    'ctr',
    'cvr',
    'ipm',
    'cpi',
  ],
  default: [
    'conversions',
    'installs',
  ],
  hok_prod: [
    'installs',
  ],
};

export const INIT_GROUPBY = ['asset_name'];
export const TOP_INIT_GROUPBY = ['country_name_en', 'theme'];
export const PAGE_SIZE = 20;
export const CA_PAGE_SIZE = 20;
export const INIT_ORDERBY: OrderByType[] = [{ by: 'spend', order: 'desc' }];
export const FORMAT = 'YYYY-MM-DD';
export const PARAM_FORMAT = 'YYYYMMDD';
export const SYSTEM = 'creative_pivot_';
export const REPORT_SYSTEM = 'creative_weekly_report_';


// 搜索框中的 Serial, Play, Custom 这三个选项不要了, 对应的key是 asset_custom_name,  asset_play, asset_serial_id
// 同时把原来的搜索框干掉,换成新的(根据原来的搜索框的筛选条件)搜索框,所以 string_search 不要了
// 需求: https://tapd.woa.com/tapd_fe/********/story/detail/10********120443746
export const getInitForm = (game: string, dtstattime: string[], isTopReport = false) => ({
  game,
  dtstattime,
  dtstatdate: dtstattime,
  asset_format: [], // 素材名称中解析的类型
  // asset_custom_name: [], // 素材名称自定义
  // asset_play: [], // 素材名称中玩法
  asset_perform: [], // 素材名称中的表现形式
  asset_stage: [], // 素材名称中阶段
  asset_delivery_date: '', // 素材名称中日期
  campaign_name: [],
  asset_size: [],
  campaign_type: getOriginCampaignType(game),
  asset_language: [], // 素材名称中的语言
  asset_name: [],
  // asset_serial_id: [],
  youtube_id: [],
  platform: [],
  network: [],
  asset_type: [],
  country_code: [],
  account_id: [],
  label: [],
  label_name: [],
  extract_label4: [],
  groupby: isTopReport ? TOP_INIT_GROUPBY : INIT_GROUPBY,
  orderby: INIT_ORDERBY,
  metric: getOriginMetric(game),
  pageIndex: 0,
  all_label: [], // label
  pageSize: PAGE_SIZE,
  where: {}, // table header中填写的筛选值
  region: [],
  // string_search: [],
  impression_date: [],
  conversion_action_name: [],
  ad_group_name: [],
  ad_name: [],
  top: [10],
  topKey: isTopReport ? TOP_INIT_GROUPBY : INIT_GROUPBY,
});
export const NEED_ADD_DAY_FROM_MAXDATE = { 1: [8, 2], 6: [12, 6] };

// 搜索框中的 Serial, Play, Custom 这三个选项不要了, 对应的key是 asset_custom_name,  asset_play, asset_serial_id
// 需求: https://tapd.woa.com/tapd_fe/********/story/detail/10********120443746
export const NEED_SPLIT_KEY_FORM: string[] = [
  'asset_name',
  // 'asset_serial_id',
  'youtube_id',
  // 'asset_play',
  // 'asset_custom_name',
  'ad_group_name',
  'ad_name',
  'campaign_name',
];
export const NEED_LOWER_CASE_KEY: string[] = [];
export const NEED_UPPER_LOWER_CASE_KEY: string[] = ['country_code'];
export const TIME_KEY_FORM: string[] = ['dtstattime', 'dtstatdate', 'impression_date'];
export const HAVING_KEY_FORM: string[] = [];
export const STRING_KEY_FORM: string[] = ['asset_delivery_date'];
export const IN_NOT_IN_FORM: string[] = [];
export const PARAM_FORMAT_KEY: string[] = ['dtstatdate', 'dtstattime'];
export enum SQLType {
  LIKE = 3,
  IN = 1,
  NotIn = 5,
  SCOPE = 2,
  HAVING = 4,
  SQL = 6,
  LikeOr = 12,
}
export enum EqualSignal {
  EQUAL = 1,
  NOT_EQUAL = 0,
  NOT = -1
}
export const CONDITIONOPTIONS = {
  1: {
    is_upper_equal: EqualSignal.NOT_EQUAL,
  },
  2: {
    is_lower_equal: EqualSignal.NOT_EQUAL,
  },
  3: {
    is_upper_equal: EqualSignal.EQUAL,
    is_lower_equal: EqualSignal.EQUAL,
  },
  4: {
    is_upper_equal: EqualSignal.EQUAL,
  },
  5: {
    is_lower_equal: EqualSignal.EQUAL,
  },
};
export const FORMAT_MAP = {
  numShort: 'money',
  int: 'money',
};

export const DTSTATTIME_MAP = {
  'Creative Pivot Weekly': [
    {
      content: 'Weekly',
      value: 'weekly',
    },
  ],
  'Creative Pivot Daily': [
    {
      content: 'Daily',
      value: 'daily',
    },
  ],
};


export const ASYNC_PAGE_SIZE = 20000;
export const DEMO_DELETE_FILTER_KEY = ['all_label', 'impression_date', 'conversion_action_name', 'account_id'];

export const WEEKLY_TOP_ATTRIBUTE = ['asset_name'];

export const WEEKLY_TOP_CUSTOM_ATTRIBUTE = ['asset_url', 'preview'];

export enum TopType {
  Top3 = 3,
  Top5 = 5,
  Top10 = 10,
}
