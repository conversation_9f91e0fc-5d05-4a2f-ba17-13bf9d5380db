import { shallowRef, ComputedRef, Ref, ShallowRef } from 'vue';
import { UseCommonSearchReturn } from '@/compose/useCommonSearch';
import { ViewOperationReturn } from '@/compose/useHeadPanelView';
import { IData } from 'common/components/BasicChart/type';
import { ViewItem } from '@/views/creative/dashboard/components/TabView';
export interface OrderByType {
  order: 'desc' | 'asc',
  by: string,
}
export enum ArrayStringList {
  COUNTRYCODE = 'country_code',
  DTSTATTIME = 'dtstattime',
  SIZE = 'asset_size',
  PLATFORM = 'platform',
  CAMPAIGN = 'campaign',
  CAMPAIGNTYPE = 'campaign_type',
  LABEL = 'label',
  ASSETTYPE = 'asset_type',
  NETWORK = 'network',
  LANGUAGE = 'asset_language',
  ASSETFORMAT = 'asset_format',
  ASSETPERFORM = 'asset_perform',
  ASSETSTAGE = 'asset_stage',
  EXTRACTLABEL4 = 'extract_label4',
  LABELNAME = 'label_name',
  IMPRESSIONDATE = 'impression_date',
  CONVERSIONACTIONNAME = 'conversion_action_name',
  ADGROUPNAME = 'ad_group_name',
  ADNAME = 'ad_name',
  CAMPAIGNNAME = 'campaign_name',
  ACCOUNTID = 'account_id',
}
// type ArrayStringList = 'countryCode' | 'dtstattime';
export enum StringList {
  ASSETNAME = 'asset_name',
  ASSETSERIAL = 'asset_serial_id',
  YOUTUBEID = 'youtube_id',
  CUSTOMNAME = 'asset_custom_name',
  PLAY = 'asset_play',
  DELIVERYDATE = 'asset_delivery_date',
}
export enum ObjectList {
}
export type FormType = {
  metric: string[],
  orderby: OrderByType[],
  groupby: string[],
  pageSize: number,
  pageIndex: number,
  where: any,
  region?: string[],
  top: number[],
  topKey: string[],
} & {
  [key in ArrayStringList]: string[];
} & {
  [key in StringList]: string;
} & {
  [key in ObjectList]: any;
};
export interface AttributesType {
  category: string,
  colKey: string,
  title: string,
  label: string,
  key: string,
  width: number,
  cell?: Function,
}
export interface CreativeDashboardType {
  // game: Ref<string>,
  view: ViewOperationReturn,
  form: UseCommonSearchReturn<FormType, any>['form'],
  schema: UseCommonSearchReturn<FormType, any>['schema'],
  chartData: Ref<IData[]>,
  metricList: ShallowRef<MetricItemType[]>,
  swiperList: Ref<IItem[]>,
  color: string[],
  yAxisLabelFormat: (value: any, index: number) => any,
  tableLoading: Ref<boolean>,
  table: ComputedRef<any[]>,
  tableTotal: Ref<number>,
  allLoading: ComputedRef<boolean>,
  attributeSchema: ComputedRef<(IFormItem | IFormDynamicItem)[]>,
  init: () => void,
  chartLoading: Ref<boolean>,
  swiperKey: Ref<string>,
  displayColumns: shallowRef<string[]>
  tableAllColumns: ComputedRef<(MetricItemType | AttributesType)[]>,
  configLoading: Ref<boolean>,
  filterTotal: Ref<boolean>,
  getTableData: () => void,
  getChartData: () => void,
  downloadHandler: () => Promise<any[]>,
  updateViewList: (params: ViewItem) => void,
  attribute: Ref<AttributesType[]>,
  onResetHandler: () => void,
  getAllData: () => void,
  onFormSubmit: (newValue: any) => void,
  addViewHandler: (item: ViewItem) => void,
  hideAttributeMetricByOther: () => void,
  asyncDownloadHandler: (param: {user: string, fileName: string}) => void,
  getTableParam: Ref<Function>
  rbacWhere: ShallowRef<any[]>
  hideAttributeMetricByOther: () => void
  showSwiper: Ref<boolean>,
  filterAssetName: Ref<boolean>,
  options: shallowRef<any>,
  dtstattimePresets: ref<string>,
}
export interface GetTableParamParam {
  pageIndex: number,
  pageSize: number,
  rbacWhere: ShallowRef<any[]>,
}
export interface TableAbout {
  tableLoading: CreativeDashboardType['tableLoading'],
  tableData: Ref<GetTableReturn>,
  getTableData: Ref<Function>,
  getTableParam: Ref<Function>,
}
export interface TableAboutParam {
  form: CreativeDashboardType['form'],
  maxDate: Ref<string>,
  displayColumns: CreativeDashboardType['displayColumns'],
  metricList: CreativeDashboardType['metricList'],
  options?: any,
  game: string,
  rbacWhere: ShallowRef<any[]>,
  otherAttribute?: string[],
  filterAssetName?: Ref<boolean>,
}
export type ChartAboutParam = Omit<TableAboutParam, 'displayColumns'>;
export interface ChartAbout extends Pick<CreativeDashboardType, 'chartLoading'> {
  chart: Ref<GetChartReturn>,
  getChartData: Ref<Function>,
}
export interface TableAbout {
  tableLoading: CreativeDashboardType['tableLoading'],
  tableData: Ref<GetTableReturn>,
  getTableData: () => void,
  getTableParam: (GetTableParamParam) => GetTableParam,
}

export interface MetricItemType {
  format: string,
  key: string,
  title: string,
  label: string,
  type: string,
  colKey: string,
  type: string,
  groupName: string,
  width?: number,
  value?: string,
  tips?: string;
  opt?: number;
}
export interface MetricMapType {
  frontLink: MetricItemType[],
  postLink: MetricItemType[],
}
export interface GetChartParam {
  metric: MetricItemType[],
  otherWhere: any[],
  where: any[],
}
export interface GetChartReturn {
  period: any[],
  total: any[],
  date: any[]
}
export interface GetTableParam {
  group: string[],
  metric: string[],
  orderby: OrderByType[],
  pageIndex?: number,
  pageSize?: number,
  realMetric: MetricItemType[],
  where: any[],
  otherMetric: string[],
  user?: string,
  fileName?: string,
  top: number,
}
export interface GetTableReturn {
  columns: string[],
  count: string,
  data: any[]
}

export interface DtstattimeItem {
  content: string,
  value: string,
}
