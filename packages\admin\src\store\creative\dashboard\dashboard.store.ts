import { asyncDownload } from './../../../../../common/service/creative/dashboard/async-download';
import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { ref, computed, watch, Ref, toRaw, shallowRef, watchEffect } from 'vue';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useCommonSearch } from '@/compose/useCommonSearch';
import { IItem } from 'common/components/MetricCard/type';
import { useHeadPanelView } from '@/compose/useHeadPanelView';
import { addShare } from 'common/service/customView/addShare';
import { addView } from 'common/service/creative/dashboard/add-view';
import { getViewList } from 'common/service/creative/dashboard/get-view-list';
import { deleteView } from 'common/service/creative/dashboard/delete-view';
import { updateView } from 'common/service/creative/dashboard/update-view';
import { getMaxMinDate } from 'common/service/creative/dashboard/get-max-min-date';
import { getAttribute } from 'common/service/creative/dashboard/get-attribute';
import { COLOR, getInitForm, SYSTEM, FORMAT_MAP, getOriginMetric, REPORT_SYSTEM, WEEKLY_TOP_ATTRIBUTE, ASYNC_PAGE_SIZE } from './dashboard.const';
import { CreativeDashboardType, AttributesType, MetricMapType, GetChartReturn, MetricItemType, DtstattimeItem } from './dashboard.d';
import { groupByAttribute, schemaSortByList, getDateLimit, viewParamByOptions, connectAllLabel, linkToPreview } from './utils';
import { cloneDeep, flattenDeep, intersection, isEqual, uniq, uniqWith, omit } from 'lodash-es';
// import { useCreativeStringSearch } from '@/compose/useCreativeStringSearch';
import { IFormItem, IFormDynamicItem } from 'common/components/FormContainer';
import AttributeComponents from '@/views/creative/dashboard/components/Attribute.vue';
import AssetName from '@/views/creative/dashboard/components/AssetName.vue';
import AssetUrl from '@/views/creative/dashboard/components/AssetUrl.vue';
import { getOptions } from 'common/service/creative/dashboard/get-options';
import { setSearchSchema } from './dashboard-schema';
import { ViewItem } from '@/views/creative/dashboard/components/TabView';
import { TTableItemDesc, useRenderFormat } from 'common/compose/table/render-format';
import { unRefObj } from 'common/utils/reactive';
import TableHeader from '@/views/creative/dashboard/components/TableHeader.vue';
import { FilterItem } from '@/views/creative/dashboard/components';
import { getShare } from 'common/service/customView/getShare';
import { getTableHandler } from './getTable';
import { getChartHandler } from './getChart';
import { filterOptionsFromRbac } from './rbacFilter';
import { IData } from 'common/components/BasicChart/type';
// import { ISearchValue } from 'common/components/SearchBox';
import { downloadTableData } from './download';
import { useRoute } from 'vue-router';
import { DOLLAR_PREFIX } from 'common/utils/format';
import { EXCLUDE_SEARCH_BOX_KEYS } from '@/views/creative/common/components/search';
import { getVideoUrl } from '@/views/creative/label/manage/utils';

// TODO 拆分结构
export const useCreativeDashboardStore = defineStore(
  STORE_KEY.CREATIVE.DASHBOARD,
  (): CreativeDashboardType => creativeStoreHandler(),
);


export const creativeStoreHandler = (routeName?: string): CreativeDashboardType => {
  const route = useRoute();
  const showSwiper = ref((routeName || route?.meta.name) !== 'Top Report');
  const originView = ref<ViewItem>();
  // echart color 配置
  const gameStore = useGlobalGameStore();
  // 获取链接上得不同来
  // 获取通过链接上得参数获取version
  const version = ref('daily');
  const dtstattimePresets = ref('');
  const changeVersion = (value: string) => version.value = value;
  const changeDtstattimePresets = (value: string) => dtstattimePresets.value = value;
  const versionSystem =  `${showSwiper.value ? SYSTEM : REPORT_SYSTEM}daily`;
  const filterAssetName = ref(!!showSwiper.value);

  const hideSchema = shallowRef<string[]>([]);
  // 可选时间的最小值
  const minDate = ref('');
  // 可选时间的最大值
  const maxDate = ref('');
  const creativeSearchStore = useCommonSearch<any, any>();
  const view = useHeadPanelView({
    system: versionSystem,
    shareViewRequest: addShare,
    getViewRequest: getViewList,
    addViewRequest: addView,
    deleteViewRequest: deleteView,
    updateViewRequest: updateView,
    getShare,
    filterAllGame: gameStore.gameCode === 'pubgm',
  });
  const addViewHandler = (item: ViewItem) => {
    view.addView({
      ...item,
      param: {
        ...item.param,
        ...creativeSearchStore.form.value,
        game: item.game,
        version: version.value,
        dtstattimePresets: dtstattimePresets.value,
      },
    });
  };
  const updateViewList = (params: ViewItem) => {
    // 重新赋值currentView 然后在进行update
    view.currentView.value.param = {
      ...view.currentView.value.param,
      ...unRefObj(creativeSearchStore.form.value),
      version: version.value,
      dtstattimePresets: dtstattimePresets.value,
    };
    view.updateViewList({
      ...params,
      param: {
        ...params.param,
        ...view.currentView.value.param,
        game: params.game,
      },
    });
  };

  const allAttribute = shallowRef<AttributesType[]>([]);
  const attribute = shallowRef<AttributesType[]>([]);
  const configLoading = ref<boolean>(false);
  const swiperKey = ref<string>('');
  const allMetricMap = shallowRef<MetricMapType>({
    frontLink: [],
    postLink: [],
  });

  const displayColumns = shallowRef<string[]>([]);
  const filterTotal = ref(!showSwiper.value);
  const tableTotal = ref(0);
  const options = shallowRef<any>({});
  const searchSortList = ref<string[]>([]);
  const allOptions = shallowRef<any>({});
  const dtstattimeOptions = ref<DtstattimeItem[]>([]);
  const confirmLoading = ref<boolean>(false);
  const metricList = shallowRef<MetricItemType[]>([]);
  const rbacWhere = shallowRef<any[]>([]);
  // const allMetricList = computed<MetricItemType[]>(() => allMetricMap.value
  //   .frontLink.concat(allMetricMap.value.postLink));
  const allMetricList = computed<MetricItemType[]>(() => [
    ...(allMetricMap.value.frontLink || []),
    ...(allMetricMap.value.postLink || []),
  ]);

  const initSearchSortList = (form: any) => {
    searchSortList.value = [];
    Object.keys(form).forEach((key) => {
      if (form[key].length > 0) {
        searchSortList.value.push(key);
      }
    });
  };

  // echart y 轴格式化的回调， 如果不传入则显示原始值
  const yAxisLabelFormat = (value: any) => {
    const key = swiperKey.value;
    const item = metricList.value.find(item => item.colKey === key);
    return useRenderFormat({
      format: ((item?.format as keyof typeof FORMAT_MAP) in FORMAT_MAP
        ? FORMAT_MAP[item?.format as keyof typeof FORMAT_MAP]
        : item?.format) as TTableItemDesc['format'],
      value,
    });
  };

  // 用来记录是否已经watch过如果watch过就把上一次取消
  const stopWatchCurrentViewId = ref();
  const init = () => {
    if (stopWatchCurrentViewId.value) {
      // 避免watch多次重复选择
      stopWatchCurrentViewId.value();
    }

    view.init();
    // 先后去ViewList
    view.getViewList();
    // 为了重新拉配置项
    allAttribute.value = [];

    // 当currentView 不为空了以后可以拿对应的param数据赋值给form
    stopWatchCurrentViewId.value = watch(
      () => view.currentViewId.value,
      async () => {
        await getConfig();
        await rbacFilterOptions();
        viewHideAttributeMetric();
        originView.value = cloneDeep(view.currentView.value);
        onResetHandler(false);
        // 初始化swiperKey
        if (!swiperKey.value) {
          const [initSwiperKey] = creativeSearchStore.form.value.metric;
          swiperKey.value = initSwiperKey;
        }
        getAllData();
      }, {
        deep: true,
      },
    );
  };

  const viewHideAttributeMetric = () => {
    const { hidden = {} } = view.currentView.value;
    Object.keys(hidden).forEach((key) => {
      if (key === 'attributes') {
        allAttribute.value = allAttribute.value.filter(item => !hidden[key].includes(item.colKey));
      }
      if (key === 'metrics') {
        Object.keys(allMetricMap.value).forEach((type) => {
          allMetricMap.value[type as keyof MetricMapType] = allMetricMap.value[type as keyof MetricMapType]
            .filter(item => !hidden[key].includes(item.colKey));
        });
      }
    });
  };

  const confirmLoadingTrue = () => {
    confirmLoading.value = true;
  };

  const getAllData = () => {
    getTableData();
    if (showSwiper.value) {
      getChartData();
    }
  };

  const getConfig = async () => {
    // 通过attribute 的数组长度来判断是否拉取过配置
    if (allAttribute.value.length === 0) {
      configLoading.value = true;
      // 所有需要拉取的配置都在这里拉取
      const {
        maxDate: originMaxDate,
        minDate: originMinDate,
      } = await getMaxMinDate();
      if (originMinDate) minDate.value = originMinDate.toString();
      if (originMaxDate) maxDate.value = originMaxDate.toString();
      const selectOptions = await getOptions(gameStore.gameCode);
      allAttribute.value = showSwiper.value ? selectOptions.attr_list : await getAttribute('weekly_top');
      allMetricMap.value = selectOptions.metric_list;
      allOptions.value = selectOptions;
      // options.value.needAddMetric = needAddMetric;
      // options.value.needDivisionMertic = needDivisionMertic;
      configLoading.value = false;
    }
  };
  const rbacFilterOptions = async () => {
    configLoading.value = true;
    const {
      options: optionsMap,
      metricList,
      dtstattimeList,
      rbacDaily,
      tempRbacWhere,
      attributeList,
      minDate: min,
      hideSchema: tempHideSchema,
    } = filterOptionsFromRbac({
      options: allOptions.value,
      game: gameStore.gameCode,
      id: view.currentViewId.value,
      metric: allMetricMap.value,
      attribute: allAttribute.value,
      minDate: minDate.value,
    });
      // 更新schema
    hideSchema.value = tempHideSchema;
    allAttribute.value = attributeList;
    allMetricMap.value = metricList;
    options.value = optionsMap;
    dtstattimeOptions.value = dtstattimeList;
    version.value = rbacDaily;
    rbacWhere.value = tempRbacWhere;
    minDate.value = min;
    configLoading.value = false;
  };

  /**
     * @returns tableLoading 获取table的loading状态
     * @returns tableData 请求table接口后获取数据
     * @returns getTableData 触发重新请求table数据
     * @returns getTableParam 获取table请求的参数
     */
  const {
    tableLoading,
    tableData,
    getTableData: getTableRef,
    getTableParam,
  } = getTableHandler({
    form: creativeSearchStore.form,
    maxDate,
    displayColumns,
    metricList,
    rbacWhere,
    options: allOptions,
    game: gameStore.gameCode,
    otherAttribute: showSwiper.value ? [] : (filterAssetName.value ? [] : WEEKLY_TOP_ATTRIBUTE),
    filterAssetName,
  });
  const getTableData = () => {
    // 如果dtstatdate不是maxDate就需要加一个hour = 23
    getTableRef.value();
  };

  /**
     * @returns chartLoading 获取chart的loading状态
     * @returns chart 请求chart接口后获取数据
     * @returns getChartData 触发重新请求chart数据
     */
  const {
    chart,
    chartLoading,
    getChartData: getChartDataRef,
  } = getChartHandler({
    form: creativeSearchStore.form,
    rbacWhere,
    maxDate,
    metricList,
    game: gameStore.gameCode,
    options: allOptions,
  });
  const getChartData = () => {
    chart.value = {
      period: [], total: [], date: [],
    };
    getChartDataRef.value();
  };
    // attribute 的schema
    // 根据 groupby 排序
  const attributeSchema = computed<(IFormItem | IFormDynamicItem)[]>(() => {
    const createItem = (item: AttributesType): any => ({
      name: AttributeComponents,
      props: {
        label: item.title,
      },
      ext: {
        key: item.key,
        label: item.title,
        isAllowClose: true,
        isHide: false,
      },
    });
    return schemaSortByList(
      cloneDeep(creativeSearchStore.form.value.groupby),
      (attribute.value.length > 0 ? attribute : allAttribute).value.map(item => createItem(item)),
    );
  });

  // 自定义头部参数处理
  const tableHeader = (list: (MetricItemType | AttributesType)[], showPanel = true) => list.map(item => ({
    ...item,
    title: (_h: any, title: any) => {
      const props = {
        ...title.col,
        orderby: cloneDeep(creativeSearchStore.form.value.orderby),
        name: title.col.key,
        filters: cloneDeep(creativeSearchStore.form.value.where[title.col.key] || []),
        onSubmit: (list: FilterItem[]) => {
          // 判断是删除还是新增
          if (list.length === 0) {
            delete creativeSearchStore.form.value.where[title.col.key];
          } else {
            creativeSearchStore.form.value.where[title.col.key] = list
              .map(item => ({ ...item, value: Number(item.value) }));
          }
          getTableData();
        },
        onOrderBy: (item: {
          colKey: string,
          value: string,
        }) => {
          // 重新排序
          creativeSearchStore.form.value.orderby = [{
            order: item.value,
            by: item.colKey,
          }];
          getTableData();
        },
        showPanel,
      };
      delete props.title;
      return _h(
        TableHeader,
        props,
        '',
      );
    },
  }));

  // table 的所有columns
  const tableAllColumns = computed(() => {
    hideAttributeMetricByOther();
    const metricCell = (key: string, format: string, row: any) => (format ? useRenderFormat({
      format: (format in FORMAT_MAP
        ? FORMAT_MAP[format as keyof typeof FORMAT_MAP]
        : format) as TTableItemDesc['format'],
      value: row[key],
    }) : row[key]);
      // 处理自定义头部
    const metricListValue = cloneDeep(metricList.value);
    // 要现根据metric排序
    const sortMeticList = uniqWith((creativeSearchStore.form.value.metric || [])
      .map((key: string) => metricListValue.find(item => item.colKey === key))
      .concat(metricListValue), isEqual);
      // 根据form排序
    const metric: MetricItemType[] = tableHeader(sortMeticList) as any;
    const deepCloneAttribute = cloneDeep(attribute.value);
    if (!showSwiper.value) {
      deepCloneAttribute.push({
        category: 'attr',
        colKey: 'preview',
        key: 'preview',
        label: 'Preview',
        title: 'Preview',
        width: 100,
      });
    }
    const attributeList = tableHeader(deepCloneAttribute, false);
    // 这里是为了排序更好展示group by
    if (attributeList.length > 0 && metric.length > 0) {
      return (displayColumns.value.map((key, index) => {
        const attributeItem: any = attributeList.find(item => item.colKey === key);
        if (!attributeItem) return false;
        const fixed: {
          fixed?: string
        } = {
          fixed: 'left',
        };
        if (index !== 0) {
          delete fixed.fixed;
        }
        if (key === 'asset_name') {
          const tempItem: any = {
            ...attributeItem,
            ...fixed,
            cell: (_h: any, { row }: any) => {
              let url = row.asset_url === 'TOTAL' || !row.asset_url ? row.asset_url_real : row.asset_url;
              if (row.asset_type === 'VIDEO' && row.asset_id) url = getVideoUrl(gameStore.gameCode, row.asset_id);
              return _h(
                AssetName,
                {
                  url,
                  type: row.asset_type?.toLowerCase(),
                  title: row.asset_name,
                  key: row.asset_name,
                  description: row.description_list_str,
                  headline: row.headline_list_str,
                  asset_id: row.asset_id,
                },
                '',
              );
            },
          };
          return tempItem;
        }
        if (key === 'asset_type') {
          const tempItem: any = {
            ...attributeItem,
            ...fixed,
            ellipsis: true,
            cell: (_h: any, { row }: any) => (row.asset_type?.includes(',') ? 'Mix' : row.asset_type),
          };
          return tempItem;
        }
        if (key === 'asset_url') {
          const tempItem: any = {
            ...attributeItem,
            ...fixed,
            cell: (_h: any, { row }: any) => {
              if (row.asset_url === 'TOTAL') {
                return 'TOTAL';
              }
              return _h(
                AssetUrl,
                {
                  url: row.asset_url,
                },
                '',
              );
            },
          };
          return tempItem;
        }
        if (key === 'preview') {
          const tempItem: any = {
            ...attributeItem,
            ...fixed,
            cell: (_h: any, { row }: any) => {
              const previewUrl = linkToPreview(row.asset_type, row.asset_url);
              return _h(
                AssetName,
                {
                  showType: 'img',
                  url: previewUrl,
                  type: 'image',
                  title: previewUrl,
                },
                '',
              );
            },
          };
          return tempItem;
        }
        return {
          ...attributeItem,
          ...fixed,
          ellipsis: true,
        };
      }).filter(item => item) as (MetricItemType | AttributesType)[])
        .concat(metric.map(item => ({
          ...item,
          ellipsis: true,
          width: item.width || 200,
          cell: (_h: any, { row }: any) => metricCell(item.colKey, item.format, row),
        })));
    }
    return [];
  });

  // 过滤数据
  const table = computed(() => {
    tableTotal.value = Number(tableData?.value?.count || tableTotal.value);
    return groupByAttribute({
      showTop: showSwiper.value ? [] :  creativeSearchStore.form.value.top,
      data: cloneDeep(tableData?.value?.data || []),
      group: uniq(cloneDeep(creativeSearchStore.form.value.groupby).concat(showSwiper.value ? [] : (filterAssetName.value ? [] : ['asset_name']))),
      orderby: creativeSearchStore.form.value.orderby,
      metric: creativeSearchStore.form.value.metric,
      filterTotal: filterTotal.value,
      options: options.value,
      game: gameStore.gameCode,
      pageIndex: creativeSearchStore.form.value.pageIndex,
    });
  });
    // 根据 date 来选x轴坐标
  const chartData = computed<IData[]>(() => {
    if ((chart as Ref<GetChartReturn>)?.value) {
      const metric = cloneDeep((metricList as Ref<MetricItemType[]>).value);
      if (uniq((chart as Ref<GetChartReturn>)?.value?.date).length === 1) {
        // return (chart as Ref<GetChartReturn>)?.value?.total[0];
        const item: any = {};
        item[swiperKey.value] = (chart as Ref<GetChartReturn>)?.value?.total[0][swiperKey.value];
        item.dtstatdate = (chart as Ref<GetChartReturn>)?.value?.date[0];
        // 换成对应的Key值
        item.key = metric.find(metricItem => metricItem.colKey === swiperKey.value)?.label || swiperKey.value;
        return [item];
      }
      return (chart as Ref<GetChartReturn>)?.value?.period.map((item) => {
        const tempItem = item;
        Object.keys(item).forEach((key) => {
          tempItem[key] = String(item[key] || 0);
        });
        tempItem.key = metric.find(metricItem => metricItem.colKey === swiperKey.value)?.label || swiperKey.value;
        return tempItem;
      });
    }
    return [];
  });
  const swiperList = computed<IItem[]>(() => {
    const list: IItem[] = [];
    const metric = cloneDeep((metricList as Ref<MetricItemType[]>).value);
    if ((chart as Ref<GetChartReturn>)?.value?.total[0]) {
      Object.keys((chart as Ref<GetChartReturn>).value.total[0]).forEach((key) => {
        const item = metric.find(item => item.colKey === key);
        const title = item?.title || key;
        const format = item?.format as TTableItemDesc['format'];
        list.push({
          title,
          colKey: key,
          toolTip: item?.tips,
          indicatorValues: useRenderFormat({
            format,
            opt: format === 'money' ? {
              separator: ',',
              prefix: (DOLLAR_PREFIX as any)[gameStore.gameCode] || DOLLAR_PREFIX.default,
            }
              : format === 'float' ? 2 : undefined,
            value: (chart as Ref<GetChartReturn>).value.total[0][key],
          }),
        });
      });
    }
    const INIT_METRIC = getOriginMetric(gameStore.gameCode);
    const top5 = JSON.stringify(list.map(item => item.colKey)) === JSON.stringify(INIT_METRIC);
    return top5 ? list.slice(0, 5) : list;
  });
  const downloadHandler = () => downloadTableData({
    tableTotal: showSwiper.value ? tableTotal.value : 1,
    game: gameStore.gameCode,
    getTableParam: getTableParam.value,
    options: options.value,
    form: {
      ...creativeSearchStore.form.value,
      group: uniq(creativeSearchStore.form.value.groupby.concat(showSwiper.value ? [] : (filterAssetName.value ? [] : ['asset_name']))),
    },
    filterTotal: filterTotal.value,
    rbacWhere: rbacWhere.value,
    top: showSwiper.value ? [] : creativeSearchStore.form.value.top,
    showSwiper: showSwiper.value,
    filterAssetName: filterAssetName.value,
    showMetric: tableAllColumns.value.map(item => item.key),
  });

  const asyncDownloadHandler = (param: {
    user: string,
    fileName: string
  }) => {
    const otherParam = getTableParam.value({
      pageIndex: 0,
      pageSize: ASYNC_PAGE_SIZE,
      rbacWhere,
    });
    asyncDownload({
      ...param,
      ...otherParam,
    });
  };

  const allLoading = computed(() => {
    if (!tableLoading.value || !chartLoading.value) {
      confirmLoading.value = false;
    }
    return (
      tableLoading.value
        && chartLoading.value
        && !confirmLoading.value
    )
      || configLoading.value
      || view.loading.value;
  });

  // 先给一个初始值
  creativeSearchStore.setForm(getInitForm(gameStore.gameCode, [], !showSwiper.value));

  const onResetHandler = (reset = true) => {
    const item = originView.value?.param;
    version.value = item?.version || version.value;
    dtstattimePresets.value = item?.dtstattimePresets || dtstattimePresets.value;
    const initTime = getDateLimit({
      version: version.value,
      maxDate: maxDate.value,
    });
    const tempForm = viewParamByOptions(toRaw(item || {}), toRaw(options.value));
    const allKeyTempForm = {
      ...getInitForm(gameStore.gameCode, initTime, !showSwiper.value),
      ...tempForm,
    };
    /**
     * 需求: https://tapd.woa.com/tapd_fe/20427967/story/detail/1020427967120443746
     * 改动: 1.搜索框从一个拆成多个
     *       2. 搜索框中的 Serial, Play, Custom 这三个选项不要了
     *          所以调用setForm方法的传参的时候, 要把三个中对应的值从参数对象中排除掉,
     *          同时string_search这个不需要了, 这个是老的搜索框的
     */
    // creativeSearchStore.setForm({
    //   ...allKeyTempForm,
    //   string_search: useCreativeStringSearch(
    //     gameStore.gameCode,
    //     allKeyTempForm,
    //   ), // search字段
    // });
    creativeSearchStore.setForm(
      omit({
        ...allKeyTempForm,
      }, [...EXCLUDE_SEARCH_BOX_KEYS, 'string_search']),
    );
    // 重置 搜索区域 排序列表
    initSearchSortList(creativeSearchStore.form.value);
    // 重新赋值搜索区域schema
    setSearchSchema({
      version: version.value,
      creativeSearchStore,
      updateVersion: changeVersion,
      changeDtstattimePresets,
      searchSortList: cloneDeep(searchSortList.value),
      maxDate: maxDate.value,
      minDate: minDate.value,
      options: options.value,
      dtstattimeList: cloneDeep(dtstattimeOptions.value),
      game: gameStore.gameCode,
      needReset: ['country_code', 'all_label'],
      onReset: Boolean(reset),
      hideSchema: hideSchema.value,
      dtstattimePresets: dtstattimePresets.value,
    });
  };

  const onFormSubmit = (newValue: typeof creativeSearchStore.form.value) => {
    const tempValue: {[key: string]: string[]} = {
      label_name: [],
      label: [],
      /** 下面的这几个字段不要了 */
      // asset_serial_id: [],
      // youtube_id: [],
      // asset_name: [],
      // ad_group_name: [],
      // ad_name: [],
    };
      // label_name-first_label-second_label
    const labelValueList = newValue.all_label.map((item: string) => {
      const list = item.split('%-%');
      return {
        label_name: list[0],
        label: list.slice(1).join('---'),
      };
    });
    tempValue.label_name = uniq(labelValueList.reduce((total: string[], current: {
      label: string,
      label_name: string,
    }) => total.concat(current.label_name), []).filter((item: string) => item));
    tempValue.label = uniq(labelValueList.reduce((total: string[], current: {
      label: string,
      label_name: string,
    }) => total.concat(current.label), []).filter((item: string) => item));
    const tempLabel = connectAllLabel(newValue.all_label);
    // string_search 处理
    /**
     *  需求: https://tapd.woa.com/tapd_fe/20427967/story/detail/1020427967120443746
     * 改动: 搜索框从一个拆成多个 老搜索框对应的  这个字段 string_search 是不需要了
     */
    // newValue.string_search.forEach((item: ISearchValue) => {
    //   tempValue[item.field as keyof typeof tempValue] = cloneDeep(item.condition);
    // });
    // 根据options 做一次过滤
    creativeSearchStore.form.value = {
      ...creativeSearchStore.form.value,
      ...newValue,
      ...tempValue,
      ...tempLabel,
      pageIndex: 0,
    };
    // 更新currentView
    view.currentView.value.param = {
      ...view.currentView.value.param,
      ...creativeSearchStore.form.value,
      ...newValue,
      ...tempValue,
      version: version.value,
      dtstattimePresets: dtstattimePresets.value,
      pageIndex: 0,
    };
    confirmLoadingTrue();
    // 请求数据
    getAllData();
  };

  watchEffect(() => {
    const orderKey = creativeSearchStore.form.value.orderby?.[0].by || '';
    const metricList = creativeSearchStore.form.value.metric || [];
    const groupList = creativeSearchStore.form.value.groupby || [];
    if (!metricList.concat(groupList).includes(orderKey) && orderKey) {
      const [newOrderBy] = metricList;
      creativeSearchStore.form.value.orderby[0].by = newOrderBy;
    }
  });

  // 添加attribute和metric互斥逻辑
  const hideAttributeMetricByOther = () => {
    // 先判断attribute中是否有在select_attribute_hide_mertic中
    const { select_attribute_hide_mertic: selectAttributeHideMetric = {} } = allOptions.value;
    const attributeNeedHideMerticKey = creativeSearchStore.form.value.groupby
      .filter((key: string) => key in selectAttributeHideMetric);
    if (attributeNeedHideMerticKey.length > 0) {
      const needMetric = flattenDeep(attributeNeedHideMerticKey.map((key: string) => selectAttributeHideMetric[key]));
      metricList.value = allMetricList.value.filter(metric => !needMetric.includes(metric.colKey));
    } else {
      metricList.value = allMetricList.value;
    }
    // 判断是否有metric在
    const metricNeedHideAttribute = Object.keys(attributeNeedHideMerticKey).filter(key => intersection(
      attributeNeedHideMerticKey[key],
      creativeSearchStore.form.value.metric,
    ).length > 0);
    if (metricNeedHideAttribute.length > 0) {
      attribute.value = allAttribute.value.filter(item => !metricNeedHideAttribute.includes(item.colKey));
    } else {
      attribute.value = allAttribute.value;
    }
  };

  setSearchSchema({
    version: version.value,
    creativeSearchStore,
    dtstattimePresets: dtstattimePresets.value,
  });

  return {
    view,
    showSwiper,
    form: creativeSearchStore.form,
    schema: creativeSearchStore.schema,
    chartData,
    swiperList,
    color: COLOR,
    yAxisLabelFormat,
    tableLoading,
    table,
    tableTotal,
    allLoading,
    init,
    attributeSchema,
    attribute: allAttribute,
    chartLoading: chartLoading as Ref<boolean>,
    swiperKey,
    metricList,
    displayColumns,
    tableAllColumns,
    configLoading,
    filterTotal,
    getTableData,
    getChartData,
    downloadHandler,
    updateViewList,
    onResetHandler,
    getAllData,
    onFormSubmit,
    addViewHandler,
    hideAttributeMetricByOther,
    filterAssetName,
    asyncDownloadHandler,
    getTableParam,
    rbacWhere,
    options,
    dtstattimePresets,
  };
};

