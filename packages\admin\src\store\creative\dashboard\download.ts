import { getTable } from 'common/service/creative/dashboard/get-table';
import { cloneDeep } from 'lodash-es';
import { GetTableReturn } from './dashboard';
import { DOWNLOAD_PAGE_SIZE, PARALLEL_NUM, WEEKLY_TOP_ATTRIBUTE, WEEKLY_TOP_CUSTOM_ATTRIBUTE } from './dashboard.const';
import { checkTypeNoImgOrVideo, groupByAttribute } from './utils';

export const downloadTableData = async ({
  tableTotal,
  getTableParam,
  game,
  options,
  form,
  filterTotal,
  rbacWhere,
  top,
  showSwiper,
  filterAssetName,
  showMetric,
}: {
  filterTotal: boolean;
  form: any,
  game: string,
  options: any,
  tableTotal: number,
  getTableParam: any,
  rbacWhere: any[],
  top: number[],
  showSwiper: boolean,
  filterAssetName: boolean
  showMetric: string[],
}) => {
  // 总共下载的页数 一次下载2500条
  const downloadPage = Math.ceil(tableTotal / DOWNLOAD_PAGE_SIZE);
  let data: any[] = [];
  for (
    let i = 0;
    // 每一轮下载10次
    i < Math.ceil(downloadPage / PARALLEL_NUM);
    i++
  ) {
    const downloadData = await Promise.all([...Array((downloadPage - (i * PARALLEL_NUM)) >= PARALLEL_NUM
      ? PARALLEL_NUM
      : (downloadPage - (i * PARALLEL_NUM))).keys(),
    ].map((key) => {
      const pageIndex = key * DOWNLOAD_PAGE_SIZE + i * PARALLEL_NUM * DOWNLOAD_PAGE_SIZE;

      const param = getTableParam({
        pageIndex,
        pageSize: DOWNLOAD_PAGE_SIZE,
        rbacWhere: { value: rbacWhere },
      });
      return getTable(param);
    }));

    // eslint-disable-next-line no-loop-func
    data = data.concat(downloadData.reduce((sum: any[], data: GetTableReturn) => sum.concat(groupByAttribute({
      data: cloneDeep(data.data),
      group: form.groupby.concat(!showSwiper ? filterAssetName ? [] : ['asset_name'] : []),
      orderby: form.orderby,
      metric: showMetric,
      filterTotal,
      options,
      game,
      download: true,
      showTop: top,
      otherGroup: !showSwiper
        ? ([...(filterAssetName ? [] : WEEKLY_TOP_ATTRIBUTE), 'asset_type'].concat(checkTypeNoImgOrVideo(form.asset_type) ? [] : (filterAssetName ? [] : WEEKLY_TOP_CUSTOM_ATTRIBUTE)))
        : [],
    })), []));
  }
  return data;
};
