import { watchThrottled } from '@vueuse/core';
import { useFetchWrapper } from 'common/compose/request/request';
import { getChart } from 'common/service/creative/dashboard/get-chart';
// import { formatFunctionParams } from 'common/utils/format';
import { isReactive, reactive, ref } from 'vue';
import { ChartAbout, ChartAboutParam, GetChartParam, GetChartReturn, MetricItemType } from './dashboard.d';
import { filterOtherKey, getCreativePivotChartParam, valueMatchOptions } from './utils';

export const getChartHandler = ({
  form,
  metricList,
  maxDate,
  options,
  rbacWhere,
}: ChartAboutParam): ChartAbout => {
  // 参数更改里面的值也需要更改
  const sourceParams = {
    form,
    maxDate,
    metricList,
    options,
    rbacWhere,
  };
  const getChartData = ref<Function>(() => {});
  const source = isReactive(sourceParams) ? sourceParams : reactive(sourceParams);
  watchThrottled(source, () => {
    getChartData.value = () => {
      chartAbort();
      const param = {
        where: formToChartParam(maxDate.value),
        metric: metricList.value.filter((item: MetricItemType) => form.value.metric.includes(item.key)),
        otherWhere: rbacWhere.value,
      };
      getChartDataHandler(param);
    };
  }, { throttle: 1000 });
  // chart 数据获取
  const {
    loading: chartLoading,
    emit: getChartDataHandler,
    data: chart,
    abort: chartAbort,
  } = useFetchWrapper<GetChartParam, GetChartReturn | null>(
    getChart,
    {
      where: [],
      metric: [],
      otherWhere: [],
    },
  );

  // 讲form转化为获取Chart接口参数
  const formToChartParam = (maxDate: string) => {
    const tempForm = valueMatchOptions(filterOtherKey(form.value), options.value);
    return getCreativePivotChartParam({
      form: tempForm,
      maxDate,
      options: options.value,
    });
  };


  return {
    chartLoading: chartLoading as ChartAbout['chartLoading'],
    chart: chart as ChartAbout['chart'],
    getChartData,
  };
};
