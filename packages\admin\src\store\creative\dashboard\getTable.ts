import { useFetch<PERSON>rapper } from 'common/compose/request/request';
import { GetTableParam, GetTableParamParam, GetTableReturn, MetricItemType, OrderByType, TableAbout, TableAboutParam } from './dashboard.d';
import { getTable } from 'common/service/creative/dashboard/get-table';

import {
  checkTypeNoImgOrVideo,
  filterOtherKey,
  getCreativePivotGetTableParam,
  valueMatchOptions,
} from './utils';
import { isReactive, reactive, ref } from 'vue';
import { watchThrottled } from '@vueuse/core';
import { flattenDeep, uniq } from 'lodash-es';
import { WEEKLY_TOP_CUSTOM_ATTRIBUTE } from './dashboard.const';

export const getTableHandler = ({
  form,
  maxDate,
  displayColumns,
  metricList,
  rbacWhere,
  options,
  game,
  otherAttribute,
  filterAssetName,
}: TableAboutParam): TableAbout => {
  const sourceParams = {
    form,
    maxDate,
    displayColumns,
    metricList,
    options,
    game,
    rbacWhere,
  };
  const getTableData = ref<Function>(() => {});
  const getTableParam = ref<Function>(() => {});
  const source = isReactive(sourceParams) ? sourceParams : reactive(sourceParams);
  watchThrottled(source, () => {
    const formToTableParam = () => {
      const tempForm = valueMatchOptions(filterOtherKey(form.value), options.value);
      return getCreativePivotGetTableParam({
        form: tempForm,
        date: {
          maxDate: maxDate.value,
        },
        metric: form.value.metric,
        options: options.value,
      });
    };
    getTableParam.value = ({
      pageIndex,
      pageSize,
      rbacWhere,
    }: GetTableParamParam): GetTableParam => {
      let allMetric = uniq(form.value.groupby.concat(!filterAssetName?.value ? (otherAttribute || []) : [])
        .concat(form.value.metric));
      if (allMetric.includes('asset_name')) {
        allMetric = allMetric.concat(options.value.requestMetricList);
        if (game in options.value.pcGameMetric) {
          allMetric = allMetric.concat(options.value.pcGameMetric[game].asset_name);
        }
      }
      const otherMetric = flattenDeep(allMetric.map((key) => {
        if (
          game === 'pubgm'
          && key in options.value.needDivisionMertic
          && `${key}_${game}` in options.value.needDivisionMertic
        ) {
          const list = options.value.needDivisionMertic[`${key}_${game}`];
          return list.filter((key: string) => !allMetric.includes(key));
        }
        return '';
      }).filter(item => item));
      return {
        where: formToTableParam().concat(rbacWhere.value),
        group: form.value.groupby.concat(!filterAssetName?.value ? (otherAttribute || []) : []),
        metric: allMetric,
        orderby: form.value.orderby,
        pageIndex,
        pageSize,
        top: (otherAttribute?.length || 0) > 0 ? form.value.top[0] : 0,
        realMetric: metricList.value.filter((item: MetricItemType) => form.value.metric.includes(item.key)),
        otherMetric,
      };
    };

    getTableData.value = () => {
      tableAbort();
      const param = getTableParam.value({
        pageIndex: form.value.pageIndex,
        pageSize: form.value.pageSize,
        rbacWhere,
        top,
      });
      // order by校验
      if (!param.orderby.every((item: OrderByType) => param.metric.includes(item.by))) {
        const [newBy] = param.realMetric;
        param.orderby[0].by = newBy;
      }
      // eslint-disable-next-line no-param-reassign
      displayColumns.value = uniq(form.value.groupby.concat(!filterAssetName?.value ? (otherAttribute || []) : [])
        .concat(otherAttribute && !checkTypeNoImgOrVideo(form.value.asset_type) && !filterAssetName?.value
          ? WEEKLY_TOP_CUSTOM_ATTRIBUTE
          : [])
        .concat(form.value.metric) || []);
      getTableDataHandler(param);
    };
  }, { throttle: 1000 });

  // table数据获取
  const {
    loading: tableLoading,
    emit: getTableDataHandler,
    data: tableData,
    abort: tableAbort,
  } = useFetchWrapper<GetTableParam, GetTableReturn>(
    getTable,
    {
      where: [],
      group: [],
      metric: [],
      orderby: [],
      pageIndex: 0,
      pageSize: form.value.pageSize,
      realMetric: [],
      otherMetric: [],
      top: 0,
    },
  );

  return {
    tableLoading: tableLoading as TableAbout['tableLoading'],
    tableData: tableData as TableAbout['tableData'],
    getTableData,
    getTableParam,
  };
};

