import { OptionsItem } from 'common/types/cascader';
import { CommonOptionsItemType } from '@@/index';
import { ref, shallowRef } from 'vue';
import { AttributesType, MetricItemType, MetricMapType } from './dashboard';
import { DTSTATTIME_MAP, PARAM_FORMAT } from './dashboard.const';
import { sqlList, sqlInList } from './sqlObj';
import { useAuthStageStore } from '@/store/global/auth.store';
import dayjs from 'dayjs';
import { flattenDepth } from 'lodash-es';

export const filterOptionsFromRbac = ({
  options,
  game,
  id,
  metric,
  attribute,
  minDate,
}: {
  options: any,
  game: string,
  id: string,
  metric: MetricMapType,
  attribute: AttributesType[],
  minDate: string,
}) => {
  // TODO 逻辑放到后台去做
  // 参数更改里面的值也需要更改
  const filterOptions = ref(options);
  const metricList = ref(metric);
  const tempRbacWhere = shallowRef<any[]>([]);
  const attributeList = shallowRef<AttributesType[]>(attribute);
  const hideSchema = shallowRef<string[]>([]);
  const dtstattime = ref(Object.keys(DTSTATTIME_MAP).
    reduce((total: any[], key: string) => total.concat(DTSTATTIME_MAP[key as keyof typeof DTSTATTIME_MAP]), []));
  const version = ref('daily');
  const min = ref(minDate);
  const useAuthStage = useAuthStageStore();
  if (useAuthStage.validGameRbac.some(({ action = '' }) => action === 'view')) {
    // 过滤视图
    const viewList = useAuthStage.validGameRbac
      .filter(({ action = '', dimension: { game: Game = '' } = {} }) => action === 'view' && Game === game);
    // 找到view权限list中是有当前id的控制
    const view: any = viewList.find((view: any) => view.dimension.view?.includes(id));
    // 如果没有当前id 则需要取合集
    const item = view ? view : viewList[0];
    // 过滤region
    if (item?.dimension?.region) {
      // 先取出对应的region
      if (item?.dimension?.country_code) {
        item.dimension.region = item?.dimension?.region.map((item: string) => item.split('---')[1]);
      }
      filterOptions.value.country_code = options.country_code
        .filter((region: CommonOptionsItemType) => item?.dimension?.region.includes(region.label));
      // 在过滤对应region中的country
      if (item?.dimension?.country_code) {
        filterOptions.value.country_code.map((region: OptionsItem) => ({
          ...region,
          children: region?.children?.filter(country => item?.dimension?.country_code.includes(country)),
        }));
      }
      const whereList: string[] = flattenDepth(filterOptions.value.country_code
        .map((item: OptionsItem) => item.children?.map(item => item.value)));
      tempRbacWhere.value.push(sqlInList({
        key: 'country_code',
        list: whereList.map(value => value.toLowerCase()).concat(whereList.map(value => value.toUpperCase())),
      }));
    }
    // 过滤metric
    if (item?.dimension?.metric) {
      Object.keys(metric).forEach((key: string) => {
        metricList.value[key as keyof MetricMapType] = metric[key as keyof MetricMapType]
          .filter((metric: MetricItemType) => item?.dimension?.metric.includes(metric.colKey));
      });
    }
    // 添加额外筛选条件
    if (item?.dimension?.asset_serial_id) {
      const assetSerialId = item?.dimension?.asset_serial_id;
      let name = '';
      if (assetSerialId.in) {
        name = `asset_serial_id in [${assetSerialId.in.map((item: string) => `'${item}'`).join(',')}]`;
      }
      if (assetSerialId.like) {
        name = `${name + (name ? 'or'  : '')} asset_serial_id like '${assetSerialId.like}%'`;
      }
      tempRbacWhere.value.push(sqlList({ sql: name }));
    }
    if (item?.dimension?.account_id) {
      const accountId = item?.dimension?.account_id;
      filterOptions.value.account_id = filterOptions.value.account_id.map((item: CommonOptionsItemType) => {
        const children = item.children!.filter((cItem: CommonOptionsItemType) => accountId.includes(cItem.value));
        return {
          ...item,
          children,
        };
      });
      const name = `account_id in [${accountId.map((value: string) => `'${value}'`).join(',')}]`;
      tempRbacWhere.value.push(sqlList({ sql: name }));
    }
    // 判断是daily还是weekly
    if (item?.dimension?.front_menu_sub) {
      // front_menu_sub 应该只有一个
      const pivotMenuSub = item?.dimension?.front_menu_sub.filter((key: string) => key.includes('Creative Pivot'));
      if (pivotMenuSub.length === 1) {
        dtstattime.value = DTSTATTIME_MAP[pivotMenuSub[0] as keyof typeof DTSTATTIME_MAP];
        version.value = pivotMenuSub[0].includes('Weekly') ? 'weekly' : version.value;
        // 如果 version 限制是weekly的话代表是区域运营 需要将最早时间限制在2023年1月1号
        min.value = dayjs('********').format(PARAM_FORMAT);
      }
    }

    if (item?.dimension?.hideAttribute) {
      attributeList.value = attributeList.value
        .filter(attribute => !item.dimension.hideAttribute.includes(attribute.colKey));
    }
    if (item?.dimension?.hideSchema) {
      hideSchema.value = item.dimension.hideSchema;
    }
    if (item?.dimension?.hideMetric) {
      Object.keys(metric).forEach((key: string) => {
        metricList.value[key as keyof MetricMapType] = metric[key as keyof MetricMapType]
          .filter((metric: MetricItemType) => !item?.dimension?.hideMetric.includes(metric.colKey));
      });
    }
  }

  return {
    options: filterOptions.value,
    metricList: metricList.value,
    dtstattimeList: dtstattime.value,
    rbacDaily: version.value,
    tempRbacWhere: tempRbacWhere.value,
    attributeList: attributeList.value,
    hideSchema: hideSchema.value,
    minDate: min.value,
  };
};
