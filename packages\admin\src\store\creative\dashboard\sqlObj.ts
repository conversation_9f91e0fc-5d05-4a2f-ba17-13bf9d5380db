import { CONDITIONOPTIONS, EqualSignal, SQLType } from './dashboard.const';

// 生成sql like的方法
export const sqlLike = ({
  key,
  like,
}: {
  key: string,
  like: string,
}) => ({
  name: key,
  type: SQLType.LIKE,
  like: `%${like}%`,
  need_lowercase: true,
});

export const sqlLikeOr = ({
  key,
  list,
}: {
  key: string,
  list: string[],
}) => ({
  name: key,
  type: SQLType.LikeOr,
  like_list: list.map((v: string) => `%${v}%`),
  need_lowercase: true,
});
// 生成sql in的方法
export const sqlInList = ({
  key,
  list,
}: {
  key: string,
  list: (string | number)[],
}) => ({
  name: key,
  type: SQLType.IN,
  in_list: list,
});
// 直接写sql的方法
export const sqlList = ({
  sql,
}: {
  sql: string,
}) => ({
  name: `(${sql})`,
  type: SQLType.SQL,
});
// 生成sql not in 的方法
export const sqlNotInList = ({
  key,
  list,
}: {
  key: string,
  list: (string | number)[],
}) => ({
  name: key,
  type: SQLType.NotIn,
  in_list: list,
});

// 0 代表不等于 1 代表等于 -1代表没有限制
export const sqlScope = ({
  key,
  upper,
  lower,
  is_upper_equal,
  is_lower_equal,
  haveToIncludes,
  valueType = '',
}: {
  key: string,
  upper: string,
  lower: string,
  is_upper_equal: number,
  is_lower_equal: number,
  haveToIncludes?: string,
  valueType?: string,
}) => ({
  name: key,
  type: SQLType.SCOPE,
  upper,
  lower,
  is_upper_equal,
  is_lower_equal,
  haveToIncludes,
  valueType,
});

export const sqlHaving = ({
  key,
  item,
}: {
  key: string,
  item: {
    condition: string,
    value: string,
  }
}) => {
  const equal: {
    is_upper_equal?: number,
    is_lower_equal?: number
  } = CONDITIONOPTIONS[Number(item.condition) as keyof typeof CONDITIONOPTIONS];
  const param = {
    upper: '',
    lower: '',
    is_upper_equal: -1,
    is_lower_equal: -1,
  };
  if (equal.is_upper_equal !== EqualSignal.NOT && typeof equal.is_upper_equal !== 'undefined') {
    param.upper = item.value;
    param.is_upper_equal = equal.is_upper_equal as number;
  }
  if (equal.is_lower_equal !== EqualSignal.NOT && typeof equal.is_lower_equal !== 'undefined') {
    param.lower = item.value;
    param.is_lower_equal = equal.is_lower_equal as number;
  }
  return {
    name: key,
    type: SQLType.HAVING,
    ...param,
  };
};
