import { OptionsItem } from './../../../../../common/types/cascader.d';
import {
  NEED_SPLIT_KEY_FORM,
  NEED_LOWER_CASE_KEY,
  TIME_KEY_FORM,
  PARAM_FORMAT,
  HAVING_KEY_FORM,
  EqualSignal,
  FORMAT,
  STRING_KEY_FORM,
  IN_NOT_IN_FORM,
  PARAM_FORMAT_KEY, NEED_UPPER_LOWER_CASE_KEY,
} from './dashboard.const';
import { StringList, OrderByType, ArrayStringList, ObjectList } from './dashboard.d';
import dayjs from 'dayjs';
import { groupBy, flattenDeep, cloneDeep, flattenDepth, uniq } from 'lodash-es';
import { IFormDynamicItem, IFormItem } from 'common/components/FormContainer';
import { percentBack, moneyBack } from 'common/utils/format';
import { CommonOptionsItemType } from '@@/index';
import { sqlHaving, sqlInList, sqlLike, sqlLikeOr, sqlNotInList, sqlScope } from './sqlObj';

/**
 *
 * @param str 需要分割的字符串
 * @returns string[] 经过trim()后过滤
 */
// const splitStringToArray = (str: string) => str.split(',')
//   .map((v: string) => v.trim())
//   .filter((v: string) => v);

/**
 *
 * @param key form中的key值
 * @param str form中key对应的value值
 * @returns string[] 经过trim()和filter处理过的数据列表
 */
const formNeedSplit = (key: string, str: string) => {
  /**
    * 去掉逗号分割成数组的逻辑
    * 例如 按照素材名是 '2024,11,08'  进行搜索 这样子带有英文逗号的
    *  在传给接口的的时候前端代码里面做了 把 '2024,11,08'   变成了
    * 接口的传参是:  {where: {asset_name: { in: true, list: ['2024', '11', '08']}} }
   */
  // let list: string[] = splitStringToArray(str);
  // 这里4个\, 传给接口json序列化的时候, 就变成两个\了
  let list: string[] = [str.replaceAll(',', '\\\\,')];
  if (key === 'youtube_id') {
    list = list.map((item) => {
      const reg = '[0-9a-zA-Z_-]{11}';
      return item.match(reg)?.[0] || '';
    });
  }
  return list;
};


const formatUpperLowerCaseList = (key: string, list: string[]) => {
  let newList = [...list];
  if (NEED_UPPER_LOWER_CASE_KEY.includes(key)) {
    newList = list.map((key: string) => key.toLowerCase())
      .concat(list.map((key: string) => key.toUpperCase()));
  } else if (NEED_LOWER_CASE_KEY.includes(key)) {
    newList = list.map((key: string) => key.toLowerCase());
  }
  return newList;
};

export const getCreativePivotChartParam = ({
  form,
  maxDate,
  options,
}: {
  form: any,
  maxDate: string,
  options: any
}) => {
  const where: any = {};
  Object.keys(form).forEach((key) => {
    if (form[key].length === 0) {
      // 代表没有筛选该字段
      return;
    }
    if (IN_NOT_IN_FORM.includes(key)) {
      where[key] = {
        in: form[key].type === 'in',
        list: form[key].value,
      };
      return ;
    }
    if (NEED_SPLIT_KEY_FORM.includes(key)) {
      const list: string[] = flattenDepth(form[key].map((value: string) => formNeedSplit(key, value)));
      where[key] = {
        in: true,
        list,
      };
    }
    if (TIME_KEY_FORM.includes(key)) {
      const formList = form[key].map((item: any) => dayjs(item)
        .format(PARAM_FORMAT_KEY.includes(key) ? PARAM_FORMAT : FORMAT));
      const tempKey = key === 'dtstattime' ? 'dtstatdate' : key;
      where[key === 'dtstattime' ? 'dtstatdate' : key] = {
        in: true,
        list: formList,
      };
      if (tempKey === 'dtstatdate') {
        where.hour = {
          in: true,
          list: formList[1] !== maxDate ? '23' : '',
        };
      }
      return;
    }
    if ((Object.values(StringList) as string[]).includes(key)) {
      where[key] = {
        in: true,
        list: form[key],
      };
      return;
    }
    if (Array.isArray(form[key])) {
      // 如果值是数组的话就用in
      // 含有others的用not in
      const list = form[key].filter((value: string) => value !== 'OTHERS');

      where[key] = {
        in: !form[key].includes('OTHERS'),
        list: formatUpperLowerCaseList(key, list),
      };
      if (form[key].includes('OTHERS')) {
        where[key].list = options[key].filter((item: any) => item.value !== 'OTHERS' && !list.includes(item.value))
          .map((item: any) => item.value);
      }
      return;
    }
  });
  return where;
};


// 通过值来组装creative pivot的请求参数
export const getCreativePivotGetTableParam = ({
  form,
  metric,
  date,
  options,
}: {
  form: any,
  date: {
    maxDate?: string,
    minDate?: string,
  },
  metric?: string[],
  options: any
}) => Object.keys(form).map((key: string) => {
  if (IN_NOT_IN_FORM.includes(key)) {
    if (form[key].value.length === 0) {
      return '';
    }
    return form[key].type === 'in'
      ? sqlInList({
        key,
        list: form[key].value,
      }) : sqlNotInList({
        key,
        list: form[key].value,
      });
  }
  if (form[key].length === 0) {
    // 代表没有筛选该字段
    return '';
  }
  if (STRING_KEY_FORM.includes(key)) {
    return sqlLike({
      key,
      like: form[key].trim(),
    });
  }
  if (NEED_SPLIT_KEY_FORM.includes(key)) {
    const list: string[] = flattenDepth(form[key].map((value: string) => formNeedSplit(key, value)));
    return list.length > 1
      ? sqlLikeOr({
        key,
        list,
      }) : sqlLike({
        key,
        like: list[0],
      });
  }
  if (TIME_KEY_FORM.includes(key)) {
    const param = {
      // 将dtstattime转化为dtstatdate
      key: key === 'dtstattime' ? 'dtstatdate' : key,
      is_upper_equal: EqualSignal.EQUAL,
      is_lower_equal: EqualSignal.EQUAL,
      valueType: '',
    };
    const formList = form[key].map((item: any) => dayjs(item)
      .format(PARAM_FORMAT_KEY.includes(key) ? PARAM_FORMAT : FORMAT));
    if (!PARAM_FORMAT_KEY.includes(key)) {
      param.valueType = 'string';
    }
    const item = sqlScope({
      ...param,
      upper: formList[1],
      lower: formList[0],
    });
    if (param.key === 'dtstatdate' && formList[1] !== date.maxDate) {
      return [item, sqlLike({
        key: 'hour',
        like: '23',
      })];
    }
    return item;
  }
  // 所有mertic都是having
  // where参数中放的都是having
  if (key === 'where') {
    return Object.keys(form[key])
      .filter(whereKey => HAVING_KEY_FORM.includes(whereKey) || (metric as string[]).includes(whereKey))
      .map(whereKey => form[key][whereKey]
        .reduce((total: any, current: {value: string, condition: string, index: number}) => {
          const param = {
            ...total,
            ...sqlHaving({
              key: whereKey,
              item: current,
            }),
          };
          if (current.index === form[key][whereKey].length - 1) {
            param.is_lower_equal = typeof param.is_lower_equal === 'undefined' ? EqualSignal.NOT : param.is_lower_equal;
            param.is_upper_equal = typeof param.is_upper_equal === 'undefined' ? EqualSignal.NOT : param.is_upper_equal;
          }
          return param;
        }, {}));
  }
  if (Array.isArray(form[key])) {
    // 如果值是数组的话就用in
    // 含有others的用not in

    const param = {
      key,
      list: formatUpperLowerCaseList(key, form[key]),
    };
    // 过滤others
    param.list = param.list.filter((value: string) => value !== 'OTHERS');
    if (form[key].includes('OTHERS')) {
      param.list = options[key]
        .filter((item: any) => item.value !== 'OTHERS' && !param.list.includes(item.value))
        .map((item: any) => item.value);
      return sqlNotInList(param);
    }
    return sqlInList(param);
  }
  // if (key in NEED_LOWER_CASE_KEY) {
  //   form[key] = form[key].map((v: string) => v.toLowerCase());
  // }
  return form[key];
})
  .flat()
  .filter(item => typeof item === 'object');

const sliceTopByGroupBy = (
  data: any,
  key: string,
  top: number,
) => {
  const origin = groupBy(data, key);
  Object.keys(origin).forEach((key, index) => {
    if (top && index >= top) {
      delete origin[key];
    }
  });
  return origin;
};

export const groupByAttribute = ({
  data = [],
  download = false,
  group,
  orderby,
  filterTotal,
  options,
  game,
  metric,
  pageIndex = 1,
  showTop = [],
  otherGroup = [],
}: {
  data: any[],
  filterTotal: boolean,
  metric: string[],
  group: string[],
  download?: boolean,
  orderby: OrderByType[],
  game: string,
  options?: any,
  pageIndex?: number,
  showTop: number[],
  otherGroup?: string[],
}) => {
  let newData: any = [];
  let total: any = [];
  const allHeader = uniq(group.concat(otherGroup).concat(metric));
  if (!download && pageIndex === 0) {
    total = data.shift();
    total = total ? [total] : [];
  }
  group.forEach((key, index) => {
    // todo 进行top筛选
    if (index === 0) {
      // 进行第一个字段的group by
      newData = sliceTopByGroupBy(data, key, Number(showTop[index]));
    } else {
      // 递归获取最近一层
      const def = (level: number, data: any) => {
        const tempData = data;
        Object.keys(tempData).forEach((groupKey) => {
          tempData[groupKey] = level === 0
            ? sliceTopByGroupBy(tempData[groupKey], key, Number(showTop[index]))
            : def(level - 1, tempData[groupKey]);
        });
        return tempData;
      };
      // 将上一个字段的数据进行下一个字段的group by
      def(index - 1, newData);
    }
  });
  if (!filterTotal) {
    totalDef(0, newData, group, metric, options, game);
  }
  newData = flattenDeep(flatData(0, newData, orderby, group));
  if (download) {
    const pcNeedAddList: any = [];
    // 删除多余key
    newData = newData.map((item: any) => {
      const temp = item;
      if (item.asset_type?.includes(',')) {
        const nameList = item.asset_name?.split(',');
        const urlList = item.asset_url?.split(',') || [];
        const typeList = item.asset_type?.split(',') || [];

        nameList.forEach((name: string, index: number) => {
          const addTemp = cloneDeep(temp);
          if ('asset_url' in temp) {
            if (index === 0) {
              temp.asset_url = urlList[index];
            } else {
              addTemp.asset_url = urlList[index];
            }
          }
          if ('asset_type' in temp) {
            if (index === 0) {
              temp.asset_type = typeList[index];
            } else {
              addTemp.asset_type = typeList[index];
            }
          }
          if ('asset_name' in temp) {
            if (index === 0) {
              temp.asset_name = name;
            } else {
              addTemp.asset_name = name;
            }
          }
          Object.keys(addTemp).forEach((key) => {
            if (!allHeader.includes(key)) {
              delete addTemp[key];
            }
          });
          if (index !== 0) {
            pcNeedAddList.push(addTemp);
          }
        });
      }
      Object.keys(temp).forEach((key) => {
        if (!allHeader.includes(key)) {
          delete temp[key];
        }
      });
      return temp;
    });
    // pc 广告需要扩散
    newData = newData.concat(pcNeedAddList);
  }
  // 数据格式转化
  const tableData = total.concat(newData).map((item: any) => {
    const temp = item;
    if (temp.asset_url?.includes(',')) {
      temp.asset_url = temp.asset_url.split(',');
      temp.asset_url = temp.asset_url.map((url: string) => {
        if (url.indexOf('youtube.com') !== -1 && !download) {
          return `https://${url}`;
        }
        return url;
      }).join(',');
    } else {
      if ('asset_url' in temp && temp.asset_url?.indexOf('youtube.com') !== -1 && temp.asset_url) {
        temp.asset_url = `https://${temp.asset_url}`;
      }
    }
    return temp;
  });

  return tableData;
};
// 将对象扁平化变为数组, 并且排序
const flatData = (level: number, data: any, orderby: OrderByType[], group: string[]): any => {
  const tempData = data;
  return Object.keys(tempData).reduce((sum: any[], groupKey: string) => {
    if (Array.isArray(tempData[groupKey])) {
      let list = sum;
      if (level === group.length - 1) {
        list = sum.concat(tempData[groupKey]);
      } else {
        list.push(tempData[groupKey]);
      }
      return orderByHandler(
        list,
        orderby,
        level,
        group,
      );
    }
    const list = flatData(level + 1, tempData[groupKey], orderby, group);
    if (list.every((item: any) => Array.isArray(item))) {
      // 此时sum为 [[{groupKey: 'Total'}, {}, {}], [{groupKey: 'Total'}, {}, {}], [{groupKey: 'Total'}, {}, {}]]
      // 将list插入到对应sum中
      const totalArr = list.map((arr: any[]) => arr[0]);
      const sortTotalArr = orderByHandler(
        totalArr,
        orderby,
        level,
        group,
      );
      const newSum = new Array(list.length);

      sortTotalArr.forEach((item: any, index: number) => {
        const findIndex = totalArr.findIndex((arr: any) => JSON.stringify(arr) === JSON.stringify(item));
        newSum[index] = list[findIndex];
      });
      sum.push(flattenDeep(newSum));
    } else {
      sum.push(list);
    }
    return sum;
  }, []);
};
// 根据某一字段排序
const orderByHandler = (
  list: any[] | any,
  orderby: OrderByType[],
  level: number,
  group: string[],
) => {
  let tempList = cloneDeep(list);
  if (Array.isArray(tempList)) {
    const totalRaw = tempList.find(item => group[level] in (item || {}) && item[group[level]] === 'TOTAL');
    tempList = tempList.filter(item => !(group[level] in (item || {}) && item[group[level]] === 'TOTAL'));

    tempList.sort((a: any, b: any) => {
      // 如果两个值相等则判断当前key值
      if (orderby[0].order === 'desc') {
        return b[orderby[0].by] - a[orderby[0].by];
      }
      return a[orderby[0].by] - b[orderby[0].by];
    });
    if (totalRaw) {
      tempList.unshift(totalRaw);
    }
  }
  return tempList;
};
// 计算metric指标合
const metricSum = (metric: string[], options: any, list: any[], game: string) => list
  .filter(item => JSON.stringify(item) !== '{}').
  reduce((sumItem, item) => {
    const tempSumItem: any = sumItem;
    metric.forEach((key) => {
      let sumFenziValue = 0;
      let sumFenmuValue = 0;
      if (key in options.needDivisionMertic) {
        const [fenzi, fenmu] = options.needDivisionMertic[key];
        if (game === 'pubgm' && `${key}_${game}` in options.needDivisionMertic) {
          const specialList = options.needDivisionMertic[`${key}_${game}`];
          sumFenziValue = (item[fenzi] || 0) + (sumItem[fenzi] || 0)
            + (fenzi === specialList[0] ? 0 : item[specialList[0]]);
          sumFenmuValue = (item[fenmu] || 0) + (sumItem[fenmu] || 0)
            + (fenmu === specialList[1] ? 0 : item[specialList[1]]);
        } else {
          // old:
          // sumFenziValue = (item[fenzi] || 0) + (sumItem[fenzi] || 0);
          // sumFenmuValue = (item[fenmu] || 0) + (sumItem[fenmu] || 0);
          // new: item[fenzi]的值 已经计算在了 sumItem[fenzi] 中了
          sumFenziValue = sumItem[fenzi] || 0;
          sumFenmuValue = sumItem[fenmu] || 0;
        }
        const multiple = options.needDivisionMertic[`${key}_multiple`];
        if (multiple) {
          sumFenziValue = (multiple * sumFenziValue * 10000) / 10000;
        }
        tempSumItem[key] = sumFenmuValue === 0 ? 0 : Math.round((sumFenziValue / sumFenmuValue) * 10000) / 10000;
      } else {
        tempSumItem[key] = Number(tempSumItem[key] || 0) + Number(item[key] || 0);
      }
    });
    return tempSumItem;
  }, {});
// 添加total
const totalDef = (level: number, data: any, group: string[], metric: string[], options: any, game: string) => {
  const tempData = data;
  return Object.keys(tempData).reduce((sum: any[], key) => {
    if (Array.isArray(tempData[key])) {
      // 处理最底层
      return sum.concat(tempData[key]);
    }
    const children: any[] = totalDef(level + 1, tempData[key], group, metric, options, game);
    if (Object.keys(tempData[key]).length > 1) {
      const item = cloneDeep(children[0]);
      const real = item[group[level + 1]];
      // 删除掉item group[level + 1] 以后的所有属性
      for (let i = level + 1;i < group.length;i++) {
        if (group[i] === 'asset_url') {
          item.asset_url_real = item.asset_url || item.asset_url_real;
        }
        delete item[group[i]];
      }
      // childe是子集item的数组
      const total = {
        ...item,
        // 计算属性的metric
        ...metricSum(metric, options, children, game),
        id: `${item.id}-${level}`,
        [`${group[level + 1]}_real`]: real,
        [group[level + 1]]: 'TOTAL',
      };
      tempData[key].__total = [total];
      return sum.concat(total);
    }
    return sum.concat(children);
  }, []);
};

export const filterOtherKey = (form: any) => {
  // 剔除掉不必要的值
  const keyList: string[] = (Object.values(ArrayStringList) as string[])
    .concat(Object.values(StringList) as string[])
    .concat(ObjectList ? Object.values(ObjectList) as string[] : '')
    .concat(form.metric)
    // where是having的
    .concat('where');
  const tempForm = cloneDeep(form);
  Object.keys(tempForm).forEach((key) => {
    if (!keyList.includes(key)) {
      delete tempForm[key];
    }
  });
  return tempForm;
};


export const schemaSortByList = (
  list: string[],
  schema: (IFormItem | IFormDynamicItem)[],
): (IFormItem | IFormDynamicItem)[] => {
  const listSchema: (IFormItem | IFormDynamicItem | null)[] = list.map((key) => {
    const tempItem = schema.find(item => item.ext.key === key);
    if (tempItem) {
      return {
        ...tempItem,
        ext: {
          ...tempItem.ext,
          isHide: false,
        },
      };
    }
    return null;
  });
  const filterSchema = schema.map(item => ((list.includes(item.ext.key) ? null : {
    ...item,
    ext: {
      ...item.ext,
      isHide: true,
    },
  })));
  return listSchema.concat(filterSchema).filter(item => item) as (IFormItem | IFormDynamicItem)[];
};

export const chartFormat = (data: string) => percentBack(moneyBack(data));

/**
 * weekly: 去上一周的周天和周一
 * daily: maxDate
 */
export const getDateLimit = ({
  version,
  maxDate,
}: {
  version: string,
  maxDate: string,
}) => {
  if (version === 'weekly') {
    const lastWeekEnd = dayjs(maxDate)
      .startOf('week')
      .format(FORMAT);
    const lastWeekStart = dayjs(lastWeekEnd)
      .subtract(1, 'day')
      .startOf('week')
      .add(1, 'day')
      .format(FORMAT);
    return [lastWeekStart, lastWeekEnd];
  }
  return [dayjs(maxDate).subtract(6, 'day')
    .format(FORMAT), maxDate];
};

// 处理视图中存在的param 主要是region转换为countryCode
export const viewParamByOptions = (
  param: any,
  options: any,
) => {
  let tempParam = cloneDeep(param);
  if (param.region) {
    tempParam.country_code = tempParam.region.length > 0
      ? flattenDeep(options.country_code.map((item: CommonOptionsItemType) => {
        if (tempParam.region.find((region: string) => region.includes(item.value as string))) {
          return item.children?.map((region: CommonOptionsItemType) => region.value);
        }
        return [];
      })) : tempParam.country_code;
  }
  // 隐藏筛选条件不会展示在筛选项中
  if (options.where) {
    tempParam = {
      ...tempParam,
      ...cloneDeep(options.where),
    };
  }
  if (param.label?.length > 1 || param.label_name?.length > 1) {
    tempParam.all_label = tempParam.label.map((item: string) => item.split('---').join('%-%'));
    options.all_label.forEach((item: OptionsItem) => {
      item.children?.forEach((cItem: OptionsItem) => {
        for (let i = 0;i <= tempParam.all_label.length - 1;i += 1) {
          const lItem = tempParam.all_label[i];
          if (lItem.split('%-%')[0] === cItem.value) {
            tempParam.all_label[i] = `${item.value}%-%${lItem}`;
          }
        }
      });
    });
  }
  if (param.campaign_name && !Array.isArray(param.campaign_name)) {
    tempParam.campaign_name = tempParam.campaign_name.value;
  }
  NEED_SPLIT_KEY_FORM.forEach((key) => {
    if (key in param && !Array.isArray(tempParam[key])) {
      tempParam[key] = [param[key]].filter(item => item);
    }
  });
  return tempParam;
};

// 筛选值和下拉选项做对比 如果长度相等代表全选 变成[]
export const valueMatchOptions = (form: any, options: any) => {
  const tempForm = cloneDeep(form);
  Object.keys(tempForm).forEach((key) => {
    if (Array.isArray(tempForm[key])
      && key in options
      && Array.isArray(options[key])
      && tempForm[key].length === options[key].length) {
      if (key === 'country_code') {
        if (tempForm[key].length !== flattenDeep(options[key].map((item: OptionsItem) => item.children))) {
          return tempForm;
        }
      }
      tempForm[key] = [];
    }
  });
  return tempForm;
};

// schema上添加options key
export const schemaAddOptions = (item: any, options: any, list: string[]) => {
  if (item.ext.key in options) {
    return {
      ...item,
      props: {
        ...item.props,
        options: cloneDeep(options[item.ext.key]),
      },
      ext: {
        ...item.ext,
        isHide: !list.includes(item.ext.key),
      },
    };
  }
  return {
    ...item,
    ext: {
      ...item.ext,
      isHide: !list.includes(item.ext.key),
    },
  };
};


export const connectAllLabel = (all_label: string[]) => {
  const temp: {
    label_name: string[],
    label: string[],
  } = {
    label_name: [],
    label: [],
  };
  const labelValueList = all_label.map((item: string) => {
    const list = item.split('%-%');
    return {
      label_name: list[0],
      label: list.slice(1).join('---'),
    };
  });
  temp.label_name = uniq(labelValueList.reduce((total: string[], current: {
    label: string,
    label_name: string,
  }) => total.concat(current.label_name), []).filter((item: string) => item));
  temp.label = uniq(labelValueList.reduce((total: string[], current: {
    label: string,
    label_name: string,
  }) => total.concat(current.label), []).filter((item: string) => item));
  return temp;
};


export const linkToPreview = (type: string, url: string) => {
  if (type === 'IMAGE') {
    return url;
  }
  if (type === 'VIDEO') {
    const reg = '[0-9a-zA-Z_-]{11}';
    const id = url?.match(reg)?.[0] || '';
    return `https://i.ytimg.com/vi/${id}/hqdefault.jpg`;
  }
  return url;
};

export const checkTypeNoImgOrVideo = (list: string[]) => {
  if (list.includes('VIDEO') || list.includes('IMAGE') || list.length === 0) {
    return false;
  }
  if ((!list.includes('VIDEO') || !list.includes('IMAGE')) && list.includes('OTHER')) {
    return false;
  }
  return true;
};
