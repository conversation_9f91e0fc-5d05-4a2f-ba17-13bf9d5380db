import type { MetricItemType, AttributesType } from '../dashboard/dashboard.d';

export type CreativeReportType = {
  metric: MetricItemType[],
  attribute: AttributesType[],
  rbac?: string[],
};

export type FilterFormType = {
  asset_size?: string[],
  dtstattime?: string[],
  platform?: string[],
  country_code?: string[],
  all_label?: string[],
  campaign_type?: string[],
  network?: string[],
  asset_language?: string[],
  impression_date?: string[],
  charList?: any[]
  show_filter_details?: boolean,
};
