import { useHeadPanelView } from '@/compose/useHeadPanelView';
import { STORE_KEY } from '@/config/config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { defineStore } from 'pinia';
import { getInitForm, SYSTEM } from './const';
import { getShare } from 'common/service/customView/getShare';
import { addShare } from 'common/service/customView/addShare';
import { addView } from 'common/service/creative/dashboard/add-view';
import { getView } from 'common/service/creative/insight/get-view';
import { deleteView } from 'common/service/creative/dashboard/delete-view';
import { updateView } from 'common/service/creative/dashboard/update-view';
import { watch, ref, shallowRef, toRaw, computed, ComputedRef } from 'vue';
import { useCommonSearch } from '@/compose/useCommonSearch';
import { cloneDeep, flattenDeep, groupBy, orderBy } from 'lodash-es';
import { AttributesType, DtstattimeItem, MetricItemType } from '../../dashboard/dashboard';
import { setFilterSchema } from './schema';
import { ViewItem } from '@/views/creative/dashboard/components/TabView';
import { getDateLimit } from '../../dashboard/utils';
import { getMaxMinDate } from 'common/service/creative/dashboard/get-max-min-date';
import { getOptions } from 'common/service/creative/dashboard/get-options';
import { getMetricAndAttribute } from 'common/service/creative/insight/get-attribute-and-metric';
import { DTSTATTIME_MAP } from '../../dashboard/dashboard.const';
import { ChartItemType } from '@/views/creative/insight/report';
import { FilterFormType } from './index.d';
import { unRefObj } from 'common/utils/reactive';
import { CHARTCOLORSET20, CHARTCOLORSET20OPACITY30, CHARTCOLORSET20OPACITY50 } from 'common/components/BasicChart/colorset';
import { useCreativeStringSearch } from '@/compose/useCreativeStringSearch';

export const useCreativeInsightReportStore = defineStore(
  STORE_KEY.CREATIVE.INSIGHT.REPORT,
  () => {
    const originView = ref<ViewItem>(); // 记录原始值在reset的时候方便回退
    const gameStore = useGlobalGameStore();
    const reportFilterStore = useCommonSearch<any, any>();
    const version = ref('daily');
    const dtstattimePresets = ref('');
    const changeVersion = (value: string) => version.value = value;
    const changeDtstattimePresets = (value: string) => {
      console.log(value);
      dtstattimePresets.value = value;
    };
    const searchSortList = ref<string[]>([]);
    // 可选时间的最小值
    const minDate = ref('');
    // 可选时间的最大值
    const maxDate = ref('');
    const options = shallowRef<any>({});
    const dtstattimeOptions = ref<DtstattimeItem[]>([]);
    // 用来记录是否已经watch过如果watch过就把上一次取消
    const stopWatchCurrentViewId = ref();
    const allAttribute = shallowRef<AttributesType[]>([]);
    const allMetric = shallowRef<MetricItemType[]>([]);
    const configLoading = ref(false);
    const allOptions = shallowRef<any>({});
    const loading = ref(false);
    const value2Color = ref<any>({});
    // 视图
    const view = useHeadPanelView({
      system: SYSTEM,
      shareViewRequest: addShare,
      getViewRequest: getView,
      addViewRequest: addView,
      deleteViewRequest: deleteView,
      updateViewRequest: updateView,
      getShare,
      filterAllGame: gameStore.gameCode === 'pubgm',
    });

    const updateViewList = (item: ViewItem) => {
      // 删除掉所有tempData
      // 重新赋值currentView 然后在进行update
      const form = unRefObj(reportFilterStore.form.value);
      form.chartList = form.chartList.map((chart: ChartItemType) => {
        const tempChart = cloneDeep(chart);
        delete tempChart.tempData;
        return tempChart;
      });
      view.currentView.value.param = {
        ...view.currentView.value.param,
        ...form,
        version: version.value,
        dtstattimePresets: dtstattimePresets.value,
      };
      view.updateViewList({
        ...item,
        param: {
          ...item.param,
          ...view.currentView.value.param,
          game: item.game,
        },
      });
    };

    const addViewHandler = (item: ViewItem) => {
      const form = unRefObj(reportFilterStore.form.value);
      form.chartList.map((chart: ChartItemType) => {
        const tempChart = cloneDeep(chart);
        delete tempChart.tempData;
        return tempChart;
      });
      view.addView({
        ...item,
        param: {
          ...item.param,
          ...form,
          game: item.game,
          version: version.value,
          dtstattimePresets: dtstattimePresets.value,
        },
      });
    };

    const getConfig = async () => {
      if (allAttribute.value.length === 0) {
        configLoading.value = true;
        // 所有需要拉取的配置都在这里拉取
        const {
          maxDate: originMaxDate,
          minDate: originMinDate,
        } = await getMaxMinDate();
        minDate.value = originMinDate;
        maxDate.value = originMaxDate;
        const {
          metric = [],
          attribute = [],
        } = await getMetricAndAttribute({ system: 'report' });
        allAttribute.value = attribute;
        allMetric.value = metric;
        allOptions.value = await getOptions(gameStore.gameCode);
        configLoading.value = false;
      }
    };

    const rbacFilterOptions = async () => {
      // TODO: filter option by rbac
      options.value = allOptions.value;
      dtstattimeOptions.value = flattenDeep(Object.values(DTSTATTIME_MAP));
    };

    const onResetHandler = ({
      reset = true,
      resetChart = false,
    }) => {
      const item = cloneDeep(originView.value?.param);
      if (!resetChart) {
        item.chartList = toRaw(reportFilterStore.form.value.chartList);
      }
      version.value = item?.version || version.value;
      dtstattimePresets.value = item?.dtstattimePresets || dtstattimePresets.value;
      const initTime = getDateLimit({
        version: version.value,
        maxDate: maxDate.value,
      });

      const allKeyTempForm = {
        ...getInitForm(initTime),
        ...item,
      };

      reportFilterStore.setForm({
        ...allKeyTempForm,
        string_search: useCreativeStringSearch(
          gameStore.gameCode,
          allKeyTempForm,
        ), // search字段
      });

      setFilterSchema({
        version: version.value,
        reportFilterStore,
        updateVersion: changeVersion,
        changeDtstattimePresets,
        searchSortList: cloneDeep(searchSortList.value),
        maxDate: maxDate.value,
        minDate: minDate.value,
        options: options.value,
        dtstattimeList: cloneDeep(dtstattimeOptions.value),
        onReset: Boolean(reset),
        dtstattimePresets: dtstattimePresets.value,
        game: gameStore.gameCode,
      });
    };

    const addChart = (item: ChartItemType) => {
      reportFilterStore.form.value.chartList.push(getColorByData(item));
    };

    reportFilterStore.setForm(getInitForm([]));

    const init = () => {
      if (stopWatchCurrentViewId.value) {
        // 避免watch多次重复选择
        stopWatchCurrentViewId.value();
      }
      view.init();
      // 先后去ViewList
      view.getViewList();
      allAttribute.value = [];

      // 当currentView 不为空了以后可以拿对应的param数据赋值给form
      stopWatchCurrentViewId.value = watch(
        () => view.currentViewId.value,
        async () => {
          loading.value = true;
          value2Color.value = {};
          // 获取表里maxDate minDate
          await getConfig();
          // rbac过滤
          await rbacFilterOptions();
          originView.value = cloneDeep(view.currentView.value);
          onResetHandler({
            reset: false,
            resetChart: true,
          });
          loading.value = false;
        }, {
          deep: true,
        },
      );
    };

    const metricOptions: ComputedRef<{
      label: string,
      value: string,
      format?: string,
    }[]> = computed(() => allMetric.value.map(item => ({
      label: item.title,
      value: item.key,
      format: item.format,
    })));
    const attributeOptions: ComputedRef<{
      label: string,
      value: string,
    }[]> = computed(() => allAttribute.value.map(item => ({
      label: item.title,
      value: item.key,
    })));

    const updateChartListFilter = () => {
      // 判断哪些是全局的值
      reportFilterStore.form.value.chartList.map((item: ChartItemType) => {
        const tempItem = cloneDeep(item);
        item.globalFilterKey.forEach((key: keyof FilterFormType) => {
          if (key in tempItem) {
            tempItem.filter[key] = reportFilterStore.form.value[key];
          }
        });
        return tempItem;
      });
    };

    const updateChartList = (list: any) => {
      reportFilterStore.form.value.chartList = list;
    };

    const updateChartListItem = (param: ChartItemType) => {
      const index = reportFilterStore.form.value.chartList.findIndex((item: ChartItemType) => item.id === param.id);

      reportFilterStore.form.value.chartList[index] = getColorByData(param);
    };

    const getColorByData = (param: ChartItemType) => {
      // 先查找value2Color中有没有这个value 如果没有则添加分配
      // 如果有则直接使用
      const realGroup = attributeOptions.value.find(item => item.value === param.groupby[0])
        ? param.groupby
        : param.metric;
      const color: string[] = [];

      if (realGroup.length === 2 && param.tempData.length > 0) {
        const groupByValue = groupBy(param.tempData, param.tempData[0][realGroup[1]]);
        let colorSort = Object.keys(groupByValue);
        if (param.type === 'pie') {
          // 用于图表不能改变
          color.push('#FFFFFF');
          colorSort = orderBy(param.tempData.map((item: any) => {
            const temp = item;
            temp.value = temp.children
              .reduce((sum: number, current: {name: string, value: number}) => current.value + sum, 0);
            return temp;
          }), ['value'], ['desc']).map(item => item.name);
        }
        colorSort.forEach((value: any) => {
          if (value in value2Color.value) {
            color.push(value2Color.value[value]);
          } else {
            const valueColor = CHARTCOLORSET20.concat(CHARTCOLORSET20OPACITY30).concat(CHARTCOLORSET20OPACITY50)
              .find(item => !Object.values(value2Color.value).includes(item)) || '#FFCCCC';
            value2Color.value[value] = valueColor;
            color.push(valueColor);
          }
        });
      }
      return {
        ...param,
        color,
      };
    };

    const deleteChartList = (item: ChartItemType) => {
      reportFilterStore.form.value.chartList = reportFilterStore.form.value.chartList
        .filter((chart: ChartItemType) => chart.id !== item.id);
    };

    const updateCardFilter = () => {
      reportFilterStore.form.value.chartList = reportFilterStore.form.value.chartList.map((item: any) => {
        const temp = item;

        Object.keys(item.filter).forEach((key) => {
          if (item.globalFilterKey.includes(key)) {
            temp.filter[key] = reportFilterStore.form.value[key];
          }
        });
        return temp;
      });
    };
    return {
      view,
      updateViewList,
      addViewHandler,
      addChart,
      init,
      form: reportFilterStore.form,
      schema: reportFilterStore.schema,
      metricOptions,
      attributeOptions,
      options,
      minDate,
      maxDate,
      dtstattimeList: dtstattimeOptions,
      onResetHandler,
      updateChartListFilter,
      loading,
      updateChartListItem,
      updateChartList,
      deleteChartList,
      updateCardFilter,
    };
  },
);

