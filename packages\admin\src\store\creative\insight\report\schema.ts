import { UseCommonSearchReturn } from '@/compose/useCommonSearch';
import components from '@/views/creative/common/components/components';
import { OptionsItem } from 'common/types/cascader';
import { button } from 'common/utils/format';
import { unRefObj } from 'common/utils/reactive';
import dayjs from 'dayjs';
import { DtstattimeItem } from '../../dashboard/dashboard';
import { FORMAT } from '../../dashboard/dashboard.const';
import { schemaAddOptions } from '../../dashboard/utils';
import { countrySelectButton, selectButton } from './utils';

const COMMON_FILTER_SCHEMA = [
  {
    name: components.DateRangePicker,
    props: {
      firstDayOfWeek: 1,
    },
    ext: {
      key: 'dtstattime',
      label: 'Date',
      isAllowClose: false,
      isHide: false,
    },
  },
  {
    name: components.SearchBox,
    props: {
      title: 'Search',
    },
    ext: {
      key: 'string_search',
      label: 'Search',
    },
  },
  {
    name: components.CommonSelect,
    props: {
      title: 'NetWork',
      multiple: true,
    },
    ext: {
      key: 'network',
      label: 'NetWork',
      isAllowClose: true,
    },
  },
  {
    name: components.Country,
    props: {
      commonInfo: {
        module: 'creative_pivot',
      },
    },
    ext: {
      key: 'country_code',
      label: 'Country/Market',
      isAllowClose: true,
    },
  },
  {
    name: components.CommonSelect,
    props: {
      title: 'Language',
      multiple: true,
    },
    ext: {
      key: 'asset_language',
      label: 'Language',
      isAllowClose: true,
    },
  },
  {
    name: components.CommonSelect,
    props: {
      title: 'Type',
      multiple: true,
    },
    ext: {
      key: 'asset_type',
      label: 'Type',
      isAllowClose: true,
    },
  },
  {
    name: components.CommonSelect,
    props: {
      title: 'Size',
      multiple: true,
      componentsKey: 'size',
      commonInfo: {
        module: 'creative_pivot',
      },
    },
    ext: {
      key: 'asset_size',
      label: 'Size',
      isAllowClose: true,
      isHide: false,
    },
  },
  {
    name: components.CommonSelect,
    props: {
      title: 'OS',
      multiple: true,
      componentsKey: 'os',
      commonInfo: {
        module: 'creative_pivot',
      },
    },
    ext: {
      key: 'platform',
      label: 'OS',
      isAllowClose: true,
      isHide: true,
    },
  },
  {
    name: components.Label,
    props: {
      valueType: 'every',
    },
    ext: {
      key: 'all_label',
      label: 'Label',
      isAllowClose: true,
    },
  },
  {
    name: components.CommonSelect,
    props: {
      title: 'Conversion Action',
      multiple: true,
      maxWidth: 'auto',
    },
    ext: {
      key: 'conversion_action_name',
      label: 'Conversion Action',
      isAllowClose: true,
    },
  },
  {
    name: components.ImpressionDate,
    props: {
      title: 'Impression Date',
    },
    ext: {
      key: 'impression_date',
      label: 'Impression Date',
      isAllowClose: true,
    },
  },
  {
    name: components.CommonSelect,
    props: {
      title: 'Campaign Type',
      multiple: true,
    },
    ext: {
      key: 'campaign_type',
      label: 'Campaign Type',
      isAllowClose: true,
    },
  },
  {
    name: components.AccountId,
    props: {
      commonInfo: {
        module: 'creative_pivot',
      },
    },
    ext: {
      key: 'account_id',
      label: 'Account Id',
      isAllowClose: true,
    },
  },
];
const PUBGM_SEARCH_SCHEMA = [
  {
    name: components.DateRangePicker,
    props: {
      firstDayOfWeek: 1,
    },
    ext: {
      key: 'dtstattime',
      label: 'Date',
      isAllowClose: false,
      isHide: false,
    },
  },
  {
    name: components.CommonSelect,
    props: {
      title: 'Size',
      multiple: true,
      componentsKey: 'size',
      commonInfo: {
        module: 'creative_pivot',
      },
    },
    ext: {
      key: 'asset_size',
      label: 'Size',
      isAllowClose: true,
      isHide: false,
    },
  },
  {
    name: components.CommonSelect,
    props: {
      title: 'OS',
      multiple: true,
      componentsKey: 'os',
      commonInfo: {
        module: 'creative_pivot',
      },
    },
    ext: {
      key: 'platform',
      label: 'OS',
      isAllowClose: true,
      isHide: true,
    },
  },
  {
    name: components.CommonSelect,
    props: {
      title: 'Campaign Type',
      multiple: true,
    },
    ext: {
      key: 'campaign_type',
      label: 'Campaign Type',
      isAllowClose: true,
    },
  },
  {
    name: components.Country,
    props: {
      commonInfo: {
        module: 'creative_pivot',
      },
    },
    ext: {
      key: 'country_code',
      label: 'Country',
      isAllowClose: true,
    },
  },
  {
    name: components.Label,
    props: {
      valueType: 'every',
    },
    ext: {
      key: 'all_label',
      label: 'Label',
      isAllowClose: true,
    },
  },
  {
    name: components.CommonSelect,
    props: {
      title: 'Language',
      multiple: true,
    },
    ext: {
      key: 'asset_language',
      label: 'Language',
      isAllowClose: true,
    },
  },
  {
    name: components.ImpressionDate,
    props: {
      title: 'Impression Date',
    },
    ext: {
      key: 'impression_date',
      label: 'Impression Date',
      isAllowClose: true,
    },
  },
  {
    name: components.Switch,
    props: {
      title: 'Show Filter Details',
    },
    ext: {
      key: 'show_filter_details',
      label: 'Show Filter Details',
      isAllowClose: false,
      isHide: false,
    },
  },
];

export const setFilterSchema = ({
  reportFilterStore,
  maxDate,
  minDate,
  dtstattimePresets,
  changeDtstattimePresets,
  searchSortList = [],
  version,
  updateVersion,
  options = {},
  dtstattimeList = [],
  onReset,
  showDetail = true,
  game,
}: {
  dtstattimePresets?: string,
  changeDtstattimePresets?: Function,
  maxDate?: string,
  minDate?: string,
  reportFilterStore?: UseCommonSearchReturn<any, any>,
  version?: string,
  updateVersion?: Function,
  searchSortList?: string[],
  dtstattimeList?: DtstattimeItem[],
  options?: any,
  onReset?: boolean,
  showDetail?: boolean,
  game?: string,
}) => {
  const list = game === 'pubgm' ? PUBGM_SEARCH_SCHEMA : COMMON_FILTER_SCHEMA;
  reportFilterStore?.updateSchema((list as typeof PUBGM_SEARCH_SCHEMA)
    .filter(item => item.ext.key in reportFilterStore.form.value).map((item) => {
      if (item.ext.key === 'dtstattime') {
        return {
          ...item,
          props: {
            ...item.props,
            version,
            dtstattimePresets,
            options: dtstattimeList,
            disableDate: {
              before: dayjs(minDate).format(FORMAT),
              after: dayjs(maxDate).format(FORMAT),
            },
            onChangeVersion: (value: string) => {
            // version.value = value;
              updateVersion?.(value);
              // 修改schema中的值
              reportFilterStore?.updateSchema(reportFilterStore.schema.value.map((item: any) => {
                const tempItem = unRefObj(item);
                if (tempItem.ext.key === 'dtstattime') {
                  return {
                    ...tempItem,
                    props: {
                      ...tempItem.props,
                      version: value,
                    },
                  };
                }
                return tempItem;
              }));
            },
            onChangePresets: (value: string) => {
              changeDtstattimePresets?.(value);
              // 修改schema中的值
              reportFilterStore?.updateSchema(reportFilterStore.schema.value.map((item: any) => {
                const tempItem = unRefObj(item);
                if (tempItem.ext.key === 'dtstattime') {
                  return {
                    ...tempItem,
                    props: {
                      ...tempItem.props,
                      dtstattimePresets: value,
                    },
                  };
                }
                return tempItem;
              }));
            },
          },
          ext: {
            ...item.ext,
            isAllowClose: false,
            isHide: !searchSortList?.includes('dtstattime'),
          },
        };
      }
      if (item.ext.key === 'impression_date') {
        const tempItem = {
          ...item,
          props: {
            ...item.props,
            maxDate,
            disableDate: {
              before: dayjs(minDate).format(FORMAT),
              after: dayjs(maxDate).format(FORMAT),
            },
          },
        };
        return schemaAddOptions(tempItem, options, searchSortList);
      }
      if (['country_code', 'all_label'].includes(item.ext.key)) {
        const schema = schemaAddOptions(item, options, searchSortList);
        return {
          ...schema,
          props: {
            ...schema.props,
            outReset: onReset,
            onOutReset: (value: boolean) => {
              reportFilterStore?.updateSchema(reportFilterStore.schema.value.map((sItem: any) => {
                const tempItem = unRefObj(sItem);
                if (tempItem.ext.key === item.ext.key) {
                  return {
                    ...tempItem,
                    props: {
                      ...tempItem.props,
                      outReset: value,
                    },
                  };
                }
                return tempItem;
              }));
            },
            button: showDetail ? (textArr: string[], country: OptionsItem[]) => countrySelectButton(
              textArr,
              schema.props.options,
              country,
            ) : button,
          },
        };
      }
      const tempOption = item.ext.key in options ? options[item.ext.key] : [];
      const temp = {
        ...item,
        props: {
          ...item.props,
          button: (textArr: string[]) => selectButton(textArr, tempOption),
        },
      };

      return schemaAddOptions(temp, options, searchSortList);
    }));
};
