import { flattenDepth } from 'lodash-es';
import { OptionsItem } from './../../../../../../common/types/cascader.d';
export const selectButton = (textArr: string[], options: OptionsItem[]) => {
  if (Array.isArray(textArr) && textArr.length > 0) {
    return textArr.length === options.length ? 'ALL' : textArr.join(',');
  }
  return 'ALL';
};
export const countrySelectButton = (
  textArr: string[],
  regionOptions: OptionsItem[],
  countryOptions: OptionsItem[],
) => {
  // 转化诚region
  const region = regionOptions.filter(item => item.children?.every(key => textArr.includes(String(key.label))));
  const regionToCountry = flattenDepth(region.map(item => item.children?.map(cli => cli.label)));
  const country = textArr.filter(item => !regionToCountry.includes(item));
  if (Array.isArray(textArr) && textArr.length > 0) {
    if (textArr.length === countryOptions.length) {
      return 'ALL';
    }
    return  region.map(item => item.label).join(',') + country.join(',');
  }
  return 'ALL';
};
