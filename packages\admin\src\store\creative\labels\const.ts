/** update by euphr<PERSON>en @0103 start **/
export const METRIC_GROUP = [
  {
    groupName: 'Delivery metrics (Media data)',
    list: [
      { title: 'Cost', colKey: 'spend', index: 10 },
      { title: 'Creatives', col<PERSON>ey: 'asset_num', index: 20 },
      { title: 'Impressions', colKey: 'impressions', index: 40 },
      { title: 'Clicks', colKey: 'clicks', index: 50 },
    ],
  },
  {
    groupName: 'AiX calculated metrics (Based media data)',
    list: [{ title: 'CTR', colKey: 'ctr', index: 60 }],
  },
];
export const CREATIVE_METRIC = {
  format: 'numShort',
  title: 'Creatives',
  key: 'asset_num',
  type: 'Delivery metrics (Media data)',
  tooltip: '',
  default: true,
  width: 300,
  hidden: false,
};
export const MOCK_BACKEND_METRIC_LIST = METRIC_GROUP.reduce((acc, cur) => acc.concat(cur.list), [] as any[]).map(
  item => ({
    title: item.title,
    key: item.colKey,
    type: item.groupName,
    tooltip: '',
    format: '',
    default: true,
  }),
);

export const YOUTUBE_INDEX = METRIC_GROUP.filter(item => item.groupName === 'YouTube Analytics')?.[0]?.list?.map(
  item => item.colKey,
);
/** update by euphrochen @0103 end **/

export const COLOR_LIST = [
  '#5086F3',
  '#69D19C',
  '#F5D242',
  '#F17C86',
  '#5EC8F1',
  '#06B6D4',
  '#6E7794',
  '#F19E55',
  '#7F6FF1',
  '#488E89',
];

export const SEARCH_BOX_DEFAULT_VALUE = [
  {
    text: 'Asset Name',
    field: 'asset_name',
    condition: [],
  },
  {
    text: 'Serial',
    field: 'asset_serial_id',
    condition: [],
  },
  {
    text: 'Campaign Name',
    field: 'campaign_name',
    condition: [],
  },
  {
    text: 'Ad Group Name',
    field: 'ad_group_name',
    condition: [],
  },
];

export const TOP_LABEL_TD_CLASS_NAME = {
  1: 'top-one-label-background',
  2: 'top-tow-label-background',
  3: 'top-there-label-background',
} as const;

export const OPTIONS_SOURCE = {
  LABELS: 'LABELS', // label insight 首页，表格中需要creatives，折线图metric下拉列表中不需要creatives，不需要展示Unlabeled(AiX)
  LABELS_ASSETS: 'LABELS_ASSETS', // label detail，即asset table 页，页面均不需要creatives
  TOP_CREATIVES: 'TOP_CREATIVES', // top creatives 页，需要展示Unlabeled(AiX)
  ASSETS: 'ASSETS', // asset detail 页
};

/** 需注意：label 和value 需保持完全相同，组件内部 有的地方将label当做了value 使用 **/
export const ROOT_LABEL_NAME = 'LabelList';
export const NA_ITEM = {
  label: 'Unlabeled(AiX)',
  value: 'Unlabeled(AiX)',
  children: [{ label: 'Unlabeled(AiX)', value: 'Unlabeled(AiX)' }],
};
export const NA_LABEL_STR = 'Unlabeled(AiX)---Unlabeled(AiX)';
export const NA_LABEL_SON_STR = 'Unlabeled(AiX)';
export const DEFAULT_BUBBLE_METRICS = ['spend', 'ctr'];
export const DEFAULT_LINE_METRIC = 'spend';

export const DOWNLOAD_METRICS = [
  { title: 'Spend', cols: ['Spend', '%Spend'], keys: ['spend', 'spend_rate'] },
  { title: 'Creatives', cols: ['Creatives', '%Creatives'], keys: ['asset_num', 'asset_num_rate'] },
  { title: 'Newinstalls', cols: ['Newinstalls', '%Newinstalls'], keys: ['installs', 'installs_rate'] },
];
