import dayjs from 'dayjs';
import { GAME_WITH_SUFFIX } from '@/store/creative/dashboard/utils-schema';
import components from '@/views/creative/common/components/components';
import LibraryLabel from '@/views/creative/library/components/LibraryLabel.vue';
import { BaseCommonSearchInput } from 'common/components/CommonSearchInputBox';
import type { IOption } from 'common/components/NewCascader';
import { COMPONENTS_MAP } from 'common/const/components';
import { getOptions } from 'common/service/creative/common/options';
import {
  AssetType,
  CampaignType,
  CountryCode,
  FirstLabelType,
  Network,
  Platform,
} from 'common/service/creative/common/type';
import { getOptionsService } from 'common/service/creative/dashboard-af';
import { IMetricItem, TOption } from 'common/service/creative/dashboard-af/type';
import { cloneDeep } from 'lodash-es';
import { computed, Ref, ref } from 'vue';
import { CREATIVE_METRIC, MOCK_BACKEND_METRIC_LIST, OPTIONS_SOURCE } from './const';
import { getDefaultDate, getLabelsData, transformMetrics, getMetricFilterOptions, groupMetrics } from './utils';
import { IMetricGroup } from 'common/components/CustomizeColumnsDialog/type';
import type { IOption as IMetricFilterOption } from 'common/components/MetricFilterDialog';
import { FormModel } from 'common/service/creative/label/insight/type';

const customMetricList = ['Custom Calculate Metrics', 'Custom Daily Metrics'];

export const getLabelsInsightFilterConfig = (gameCode: Ref<string>, formModelValue?: Ref<FormModel>, showCompare = false, ignoreScore = false) => {
  const campaignTypeList = ref<CampaignType[]>([]);
  const typeList = ref<AssetType[]>([]);
  const countryList = ref<CountryCode[]>([]);
  const netWorkList = ref<Network[]>([]);
  const osList = ref<Platform[]>([]);
  const ageList = ref<TOption[]>([]);
  const labelList = ref<IOption[]>([]); // 筛选条件的label
  const labelTypeList = ref<FirstLabelType[]>([]); // 表格顶部的筛选
  const initLabelValueList = ref<string[]>([]); // 初始化label下拉列表选中值，Label默认筛选除NA之外的所有标签
  const initLabelTypeList = ref<FirstLabelType[]>([]); // 切换回default时，在其他视图拖拽的排序要重置
  const totalLabelList = ref<string[]>([]); // 一级-二级标签列表

  const metricsGroup = ref<IMetricFilterOption[]>([]); // 所有指标分组
  const customMetricGroup = ref<IMetricGroup[]>([]); // 自定义指标分组
  const allMetrics = ref<IMetricItem[]>([]); // 所有前链路指标
  const customMetrics = ref<IMetricItem[]>([]);

  const isAFGame = GAME_WITH_SUFFIX.includes(gameCode.value);
  const metricCfgList = ref<IMetricItem[]>([]);

  async function initOptions({ source } = { source: '' }) {
    const hideNA = source === OPTIONS_SOURCE.LABELS;
    const { labelOptions, firstLabelList, totalLabels, initLabelValue } = await getLabelsData(gameCode.value, undefined, hideNA);
    let metricCfg = MOCK_BACKEND_METRIC_LIST as IMetricItem[];
    if (isAFGame) {
      const { filters, metric_list: metric } = await getOptionsService();
      typeList.value = filters.asset_type as AssetType[];
      countryList.value = filters.country as CountryCode[];
      netWorkList.value = filters.network as Network[];
      osList.value = filters.platform as Platform[];
      ageList.value = filters.asset_age;
      if (metric?.length > 0) {
        metricCfg = [...metric.slice(0, 1), CREATIVE_METRIC, ...metric.slice(1)];
      }
    } else {
      const options = await getOptions();
      campaignTypeList.value = options.campaign_type;
      typeList.value = options.asset_type;
      countryList.value = options.country_code;
      netWorkList.value = options.network;
      osList.value = options.platform;
    }
    labelList.value = labelOptions;
    labelTypeList.value = firstLabelList;

    // 在其他视图拖拽tab的排序后，回到default视图时，tab的排序重置为接口回来的顺序
    // cloneDeep一把， 防止labelTypeList后续发生变化，影响initLabelTypeList
    initLabelTypeList.value = cloneDeep(firstLabelList);
    totalLabelList.value = totalLabels;
    initLabelValueList.value = initLabelValue;

    metricCfgList.value = transformMetrics({ source, metrics: metricCfg });
    allMetrics.value = cloneDeep(metricCfgList.value);
    if (ignoreScore) {
      allMetrics.value = allMetrics.value.filter(item => item.key !== 'asset_score'); // 过滤掉Score指标
    }
    customMetrics.value = cloneDeep(metricCfgList.value).filter(item => customMetricList.includes(item.type));

    metricsGroup.value = getMetricFilterOptions(allMetrics.value);
    customMetricGroup.value = groupMetrics(customMetrics.value);
  }
  /** update by euphrochen @01013 外网做些屏蔽**/
  const formList = computed(() => {
    const curDate = getDefaultDate(gameCode.value)[1];
    let dateCompare = '';
    if (showCompare && formModelValue?.value?.date) {
      const [sDate, eDate] = formModelValue?.value.date;
      // 计算eDate和sDate的天数差
      const dayDiff = dayjs(eDate).diff(dayjs(sDate), 'day');
      const preEndDate = dayjs(sDate).subtract(1, 'day')
        .format('YYYY-MM-DD');
      const preStartDate = dayjs(preEndDate).subtract(dayDiff, 'day')
        .format('YYYY-MM-DD');
      dateCompare = `Compared to: ${preStartDate} ~ ${preEndDate}`;
    }
    return [
      {
        name: components.DateRangePicker,
        props: {
          firstDayOfWeek: 1,
          maxDate: curDate,
          disableDate: {
            after: curDate,
          },
          onChangePresets: () => {}, // 必传，不然报错
          isShowPopupHeader: showCompare,
          popupHeaderText: dateCompare,
        },
        ext: {
          key: 'date',
          label: 'Date',
        },
      },
      {
        name: BaseCommonSearchInput,
        props: {
          label: 'Asset Name',
          tagInputProps: {
            class: 'w-[216px]',
          },
        },
        ext: {
          key: 'asset_name',
          label: 'Asset Name',
          isAllowClose: false,
          isHide: false,
        },
      },
      {
        name: COMPONENTS_MAP['a-select'],
        props: {
          title: 'Asset Type',
          multiple: true,
          list: [
            { label: 'IMAGE', value: 'IMAGE' },
            { label: 'VIDEO', value: 'VIDEO' },
          ],
        },
        ext: {
          key: 'type',
          label: 'Asset Type',
          isAllowClose: true,
        },
      },
      {
        name: COMPONENTS_MAP['a-select'],
        props: {
          title: 'Media Source',
          multiple: true,
          list: netWorkList,
        },
        ext: {
          key: 'network',
          label: 'Media Source',
          isAllowClose: true,
        },
      },
      GAME_WITH_SUFFIX.includes(gameCode.value)
        ? {
          name: components.ImpressionDate,
          props: {
            title: 'Impression Date',
            maxDate: curDate,
            disableDate: {
              after: curDate,
            },
          },
          ext: {
            default: [],
            date: [],
            key: 'impression_date',
            label: 'Impression Date',
            isAllowClose: true,
          },
        }
        : undefined,
      GAME_WITH_SUFFIX.includes(gameCode.value)
        ? {
          name: COMPONENTS_MAP['a-select'],
          props: {
            title: 'OS',
            multiple: true,
            list: osList,
          },
          ext: {
            key: 'platform',
            label: 'OS',
            isAllowClose: true,
            isHide: true,
          },
        }
        : undefined,
      GAME_WITH_SUFFIX.includes(gameCode.value)
        ? {
          name: COMPONENTS_MAP['a-select'],
          props: {
            title: 'Country',
            multiple: true,
            list: countryList,
          },
          ext: {
            key: 'country_code',
            label: 'Country',
            isAllowClose: true,
            isHide: true,
          },
        }
        : undefined,
      {
        name: BaseCommonSearchInput,
        props: {
          label: 'Campaign Name',
          tagInputProps: {
            class: 'w-[240px]',
          },
        },
        ext: {
          key: 'campaign_name',
          label: 'Campaign Name',
          isAllowClose: true,
          isHide: true,
        },
      },
      {
        name: BaseCommonSearchInput,
        props: {
          label: 'Ad Group Name',
          tagInputProps: {
            class: 'w-[240px]',
          },
        },
        ext: {
          key: 'ad_group_name',
          label: 'Ad Group Name',
          isAllowClose: true,
          isHide: true,
        },
      },
      {
        name: BaseCommonSearchInput,
        props: {
          label: 'Ad Name',
          tagInputProps: {
            class: 'w-[240px]',
          },
        },
        ext: {
          key: 'ad_name',
          label: 'Ad Name',
          isAllowClose: true,
          isHide: true,
        },
      },
      GAME_WITH_SUFFIX.includes(gameCode.value)
        ? {
          name: COMPONENTS_MAP['a-select'],
          props: {
            title: 'Asset Age',
            multiple: true,
            list: ageList,
          },
          ext: {
            key: 'asset_age',
            label: 'Asset Age',
            isAllowClose: true,
            isHide: true,
          },
        }
        : undefined,
      {
        name: BaseCommonSearchInput,
        props: {
          label: 'Asset Serial',
          tagInputProps: {
            class: 'w-[216px]',
          },
        },
        ext: {
          key: 'serial_name',
          label: 'Asset Serial',
          isAllowClose: true,
          isHide: true,
        },
      },
      {
        name: LibraryLabel,
        props: {
          valueType: 'everyWithLowest',
          options: labelList,
          labels: ['Label Name', 'First Label', 'Second Label'],
        },
        ext: {
          key: 'label',
          label: 'Label',
          isAllowClose: true,
        },
      },
    ].filter(item => !!item);
  });

  return {
    initOptions,
    labelTypeList,
    totalLabelList,
    initLabelValueList,
    initLabelTypeList,
    formList,
    metricsGroup,
    customMetricGroup,
    allMetrics,
    customMetrics,
    metricCfgList,
  };
};
