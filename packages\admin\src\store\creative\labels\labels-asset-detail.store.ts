import { computed, ref, watch } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import dayjs from 'dayjs';
import { STORE_KEY } from '@/config/config';
import { CommonInfo, Label, Timeline } from 'common/service/creative/label/insight/type';
import { isIgnoreMetrics } from '@/store/creative/top/utils';
import { getAssetTimeline } from 'common/service/creative/label/insight';
import { COLOR_LIST, OPTIONS_SOURCE } from './const';
import { useCustomView } from 'common/compose/useCustomView';
import { getLabelsInsightFilterConfig } from '@/store/creative/labels/insight-filter-config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { MetricItemType } from '../dashboard/dashboard';
import { IMetricItem } from 'common/service/creative/dashboard-af/type';

export const useLabelAssetDetailStore = defineStore(STORE_KEY.CREATIVE.LABELS.DETAILS, () => {
  const sDate = ref('');
  const eDate = ref('');
  const defaultIndex = ref('spend');
  const tabList = ref([
    { name: 'Timeline', value: 'timeline' },
    { name: 'Metric', value: 'metric' },
  ]);
  const curTab = ref('');
  const commonInfo = ref<CommonInfo>({
    asset_name: '',
    asset_serial_id: '',
    asset_id: '',
    asset_type: '',
    url: '', // 图片类型使用
    youtube_id: '',
  });
  const commonList = ref<
  {
    label: string;
    value: string | number;
  }[]
  >([]);
  const labelList = ref<Label[]>([]);
  const labelTimeline = ref<Timeline[]>([]);
  const initCb = ref<Function>();
  const { getShare } = useCustomView(); // 获取assetNames参数
  const topAssetNames = ref<string[]>([]); // url传的参数，通过code转换

  const filterParams = ref<Record<string, any>>();

  const setInitCb = (cb: Function) => {
    initCb.value = cb;
  };

  // 监听回调，延迟执行
  watch(
    () => initCb.value,
    () => {
      setTimeout(initCb.value!, 0);
    },
  );

  const changeTab = (tab: string) => {
    curTab.value = tab;
  };

  // 设置从url传来的基础信息
  const setBase = (
    startDate: string,
    endDate: string,
    assetName: string,
    assetType: string,
    url: string,
    serialId: string,
    assetId: string,
    youtubeId: string,
    index: string,
    filter?: string,
  ) => {
    sDate.value = startDate;
    eDate.value = endDate;
    commonInfo.value.asset_name = assetName;
    commonInfo.value.asset_serial_id = serialId;
    commonInfo.value.asset_id = assetId;
    commonInfo.value.asset_type = assetType;
    commonInfo.value.url = url;
    commonInfo.value.youtube_id = youtubeId;
    if (!isIgnoreMetrics(index) && index) {
      defaultIndex.value = index;
    }
    if (filter) {
      filterParams.value = JSON.parse(filter);
    }
  };

  const getData = async (code?: string) => {
    const res = await Promise.all([
      getAssetTimeline(commonInfo.value.asset_serial_id, commonInfo.value.asset_name),
      code ? getShare(code) : Promise.resolve(),
      initOptions({ source: OPTIONS_SOURCE.ASSETS }),
    ]);

    const [timeLineData, codeData] = res;

    backendMetricList.value = [...metricCfgList.value]; // 浅拷贝
    const { info, labels, timeline } = timeLineData;

    if (codeData?.param) {
      topAssetNames.value = codeData.param;
    }

    if (timeLineData.code !== 0) return;

    commonList.value = [
      { label: 'Serial Name', value: info.asset_serial_id },
      { label: 'Asset Name', value: info.asset_name },
      { label: 'File Size', value: info.size ? `${info.size}MB` : '' },
      { label: 'File Type', value: commonInfo.value.asset_type as string },
      {
        label: 'Impression Date',
        value: info.impression_date ? dayjs(info.impression_date, 'YYYYMMDD').format('YYYY-MM-DD') : '',
      },
    ];

    const timelineData = timeline.map((item, index) => {
      const color = COLOR_LIST[index % COLOR_LIST.length];
      // eslint-disable-next-line no-param-reassign
      item.timeline = item.timeline.map((lineItem) => {
        const start = Number(lineItem.t_start);
        const duration = Number(lineItem.t_end) - start;
        const durationRatio = duration / (info.duration as number); // 时长占比
        const startRatio = start / (info.duration as number); // 开始时长占比
        return {
          ...lineItem,
          start,
          duration,
          duration_ratio: durationRatio > 1 ? 1 : durationRatio,
          start_ratio: startRatio > 1 ? 1 : startRatio,
        };
      });
      return {
        ...item,
        color,
      };
    });

    if (info.url) commonInfo.value.url = info.url;
    commonInfo.value = Object.assign(info, commonInfo.value);

    labelList.value = labels;
    labelTimeline.value = timelineData;
  };

  const { gameCode } = storeToRefs(useGlobalGameStore());
  const { initOptions, metricCfgList } = getLabelsInsightFilterConfig(gameCode);

  /** update by euphrochen @0407 start 调整成 后端接口 统一返回，保证跟 dashboard 页一致， 通过admin 配置端管理 start **/
  const backendMetricList = ref<IMetricItem[]>([]);
  const allMetrics = computed((): MetricItemType[] => backendMetricList.value?.map(item => ({
    ...item, // 保留原有属性字段，用于chart format
    type: 'group',
    colKey: item.key,
    groupName: item.type,
    value: item.key,
    label: item.title,
  })),
  );
  /** update by euphrochen @0407 start 调整成 后端接口 统一返回，保证跟 dashboard 页一致， 通过admin 配置端管理 end **/
  return {
    tabList,
    sDate,
    eDate,
    curTab,
    defaultIndex,
    commonInfo,
    commonList,
    labelList,
    labelTimeline,
    topAssetNames,
    setBase,
    getData,
    setInitCb,
    changeTab,
    allMetrics,
  };
});
