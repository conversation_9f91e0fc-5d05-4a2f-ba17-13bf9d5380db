import { STORE_KEY } from '@/config/config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { IMetric } from 'common/components/CustomizeColumnsDialog/type';
import { useLoading } from 'common/compose/loading';
import { IMetricItem } from 'common/service/creative/dashboard-af/type';
import { getTopAssets } from 'common/service/creative/label/insight';
import { LabelAssetFilter, LabelAssetRes } from 'common/service/creative/label/insight/type';
import { NEW_API_HOST } from 'common/utils/auth';
import { defineStore, storeToRefs } from 'pinia';
import { SortInfo } from 'tdesign-vue-next';
import { computed, ref, watch } from 'vue';
import { formatMetriclist } from '../dashboard-af/utils';
import { OPTIONS_SOURCE } from './const';
import { getLabelsInsightFilterConfig } from './insight-filter-config';
import { useDownloadFile } from 'common/compose/download-file';
import { pick } from 'lodash-es';
import { formatVal, excelFormatFn } from '@/views/creative/label/insight/utils';

export const useLabelAssetsStore = defineStore(STORE_KEY.CREATIVE.LABELS.ASSETS, () => {
  const { gameCode } = storeToRefs(useGlobalGameStore());
  const labels = ref<string[]>([]);
  const serialIds = ref<string[]>([]);
  const filterParams = ref<LabelAssetFilter>();
  const pageIndex = ref(1);
  const pageSize = ref(10);
  const totalCount = ref(0);
  const tableData = ref<LabelAssetRes[]>([]);

  const { isLoading, showLoading, hideLoading } = useLoading();
  const {
    isLoading: isDownloadLoading,
    showLoading: showDownloadLoading,
    hideLoading: hideDownloadLoading,
  } = useLoading();
  // const metricList = ref<IBusinessTableMetric[]>([]); // 展示列
  /** update by euphrochen @0407 start 调整成 后端接口 统一返回，保证跟 dashboard 页一致， 通过admin 配置端管理 start **/
  const { metricCfgList, initOptions } = getLabelsInsightFilterConfig(gameCode);
  const backendMetricList = ref<IMetricItem[]>([]);
  const allMetrics = computed((): IMetricItem[] => backendMetricList.value?.map(item => ({
    ...item, // 保留原有属性字段，用于chart format
    type: 'group',
    colKey: item.key,
    groupName: item.type,
    value: item.key,
    label: item.title,
  })),
  );

  const colDialogList = computed(() => {
    if (!backendMetricList.value?.length) return [];
    return formatMetriclist([...backendMetricList.value])?.map(group => ({
      groupName: group.label,
      list: group.children.map(item => ({
        colKey: item.value,
        title: item.label,
        tips: item.tips,
      })),
    }));
  });
  const colCheckedList = ref<{ colKey: string }[]>([]); // 后续需要对其重新赋值，不能是计算属性
  const tableColumns = computed(() => {
    // 空值保护：若数据未初始化，直接返回基础列
    const attrCols = [
      { colKey: 'preview', title: 'Preview', width: 100, fixed: 'left', index: 0 },
      {
        colKey: 'asset_name',
        title: 'Asset Name',
        width: 240,
        fixed: 'left',
        ellipsis: { showArrow: true, disabled: true },
        index: 1,
      },
    ];
    if (!backendMetricList.value?.length || !colCheckedList.value?.length) {
      return attrCols;
    }
    const metricCols = colCheckedList.value.map((item) => {
      const curMetric = backendMetricList.value?.find(m => m.key === item.colKey);
      return {
        colKey: item.colKey,
        title: curMetric?.title ?? 'Unknown', // 兜底默认值
        sorter: true,
        align: 'right',
        index: ((curMetric as any)?.index ?? 0) + 10,
        width: curMetric?.width,
        format: curMetric?.format,
      };
    });

    return [...attrCols, ...metricCols];
  });
  const changeCheckedColList = (list: IMetric[]) => {
    colCheckedList.value = list;
    pageIndex.value = 1;
    getTopData();
  };
  // 直接通过链接打开，不是从label insight首页进入时 需重新查配置类数据
  async function initOptionsLabelsAssets() {
    await initOptions({ source: OPTIONS_SOURCE.LABELS_ASSETS });
    // asset table 不需要 creatives 列
    backendMetricList.value = [...metricCfgList.value].filter(item => item.key !== 'asset_num'); // 浅拷贝
    colCheckedList.value = [...metricCfgList.value]
      ?.filter(item => item.key !== 'asset_num' && item.default)
      .map(item => ({ colKey: item.key }));
  }
  /** update by euphrochen @0407 end 调整成 后端接口 统一返回，保证跟 dashboard 页一致， 通过admin 配置端管理 end **/

  // 表格排序
  const defaultSort = [{ sortBy: 'spend', descending: true }];
  const tableSort = ref<SortInfo[]>([{ sortBy: 'spend', descending: true }]);

  const setParams = (labelList: string[], serialList: string[], params: LabelAssetFilter) => {
    filterParams.value = params;
    labels.value = labelList;
    serialIds.value = serialList;
  };

  const getParams = () => {
    const order = tableSort.value.map(item => ({
      by: item.sortBy,
      order: item.descending ? 'DESC' : 'ASC',
    }));

    return {
      serial_ids: serialIds.value,
      order,
      ...filterParams.value,
      pageSize: pageSize.value,
      pageIndex: pageIndex.value,
      labels: labels.value,
      label_search_type: 1,
      metrics: colCheckedList.value.map(item => item.colKey),
    };
  };

  const onPageSizeChange = (size: number) => {
    pageIndex.value = 1;
    pageSize.value = size;
    getTopData();
  };

  const onPageIndexChange = () => {
    getTopData();
  };

  // 获取分页表格数据
  const getTopData = async () => {
    showLoading();
    const params = getParams();
    const res = await getTopAssets(params);

    const { topList, total, count } = res;
    total.is_total = true;
    totalCount.value = count;

    const data = [total].concat(topList);
    data.forEach((item) => {
      const id = encodeURIComponent(`id:${item.asset_id}`);
      if (item.asset_type === 'VIDEO') {
        // eslint-disable-next-line no-param-reassign
        item.preview_url = `https://${NEW_API_HOST}/api_v2/creative/common/thumbnail?game=${gameCode.value}&path=${id}`;
      }
    });
    tableData.value = data;

    hideLoading();
  };

  const downloadExcel = async () => {
    showDownloadLoading();
    // metric 弹窗中选中的列有哪些
    const checkedColKeys = colCheckedList.value.map(item => item.colKey);
    // 当前选中的列
    const checkedCols = tableColumns.value.filter(colItem => checkedColKeys.includes(colItem.colKey));
    // 选项的列，colKey对应的title表头
    const checkedColKeyToTitleMap = checkedCols.reduce((obj, item) => {
      /* eslint-disable no-param-reassign */
      obj[item.colKey] = item.title;
      return obj;
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    }, {} as Record<string, string>);
    // 表头
    const header = {
      asset_name: 'Asset Name',
      ...checkedColKeyToTitleMap,
    };
    const pickKeys = Object.keys(header);
    const params = getParams();
    // 根据页面上的总条数，全部查出来
    const res = await getTopAssets({
      ...params,
      pageIndex: 1,
      pageSize: totalCount.value,
    });
    const { topList = [] } = res || {};
    // 要下载的数据
    const downloadData = topList.map((topItem) => {
      // 这一行中要保留的字段
      const pickRowData = pick(topItem, pickKeys);
      // 格式化之后的数据
      const formattedRowData = pickKeys.reduce((row, key) => {
        const value = pickRowData[key as keyof typeof pickRowData];
        let formattedValue = value;
        if (isFinite(value as number)) {
          formattedValue = formatVal(value as number, key, allMetrics.value);
        }
        /* eslint-disable no-param-reassign */
        row[key] = formattedValue;
        return row;
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      }, {} as Record<string, any>);
      return formattedRowData;
    });
    // 执行下载
    const { isLoading } = useDownloadFile(downloadData, 'label-assets.xlsx', {
      header,
      formatDataFn: excelFormatFn,
    });
    watch(
      () => isLoading.value,
      val => !val && hideDownloadLoading(),
    );
  };

  return {
    tableData,
    tableColumns,
    isLoading,
    tableSort,
    allMetrics,
    colDialogList,
    colCheckedList,
    defaultSort,
    pageIndex,
    pageSize,
    totalCount,
    filterParams,
    isDownloadLoading,
    setParams,
    getTopData,
    changeCheckedColList,
    onPageSizeChange,
    onPageIndexChange,
    downloadExcel,
    initOptionsLabelsAssets, // add by euphrochen to getMetric fr remote admin cfg
  };
});
