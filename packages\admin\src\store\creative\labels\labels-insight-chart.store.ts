import { ref, watchEffect } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { getInsightBubbleData, getLabelInsightTrendData } from 'common/service/creative/label/insight';
import { BubbleChartData, TLabelDetailInsightDataParams } from 'common/service/creative/label/insight/type';
import { useLabelsInsightStore } from './labels-insight.store';
import { useLoading } from 'common/compose/loading';
import { DEFAULT_BUBBLE_METRICS, DEFAULT_LINE_METRIC } from './const';
import { insightEventBus } from './utils';
import { isArray, isPlainObject, isString, cloneDeep } from 'lodash-es';

export const INSIGHT_EVENT_TYPE = {
  REFRESH: 'refresh',
  UPDATE_DATE: 'update-date',
  UPDATE_CAHRT_PARAMS: 'update-chart-params',
  WATCH_CAHRT_PARAMS: 'watch-chart-params',
} as const;

export type TChartParam = {
  scatter: {
    xAxis: string,
    yAxis: string,
  },
  line: {
    date: string[],
    metric: string,
  }
};

export const useLabelsInsightChartStore = defineStore(STORE_KEY.CREATIVE.LABELS.INSIGHT_CHART, () => {
  const {
    isLoading: isLineChartLoading,
    showLoading: showLineChartLoading,
    hideLoading: hideLineChartLoading,
  } = useLoading();

  const labelsInsightStore = useLabelsInsightStore();
  const { getParams } = labelsInsightStore;
  const { formModelValue, labelType } = storeToRefs(labelsInsightStore);

  const lineChartMetric = ref<string>(DEFAULT_LINE_METRIC);
  const updateLineChartMetric = (val: string) => (lineChartMetric.value = val);
  const lineChartDtstatdate = ref([
    formModelValue.value.date[0].replaceAll('-', ''),
    formModelValue.value.date[1].replaceAll('-', ''),
  ]);
  const updateLineChartDtstatdate = (val: string[]) => {
    lineChartDtstatdate.value = val.map(item => item.replaceAll('-', ''));
  };

  const lineChartData = ref<Record<string, any>[]>([]);
  const secondLabelList = ref<string[]>([]);

  const getLineChartParams = (): TLabelDetailInsightDataParams => {
    const {
      impression_date: impressionDate,
      campaign_type: campaignType,
      asset_type: assetType,
      country_code: countryCode,
      network,
      platform,
      keywords,
      label_search_type: labelsSearchType,
      labels,
      startDate, endDate, // 默认使用顶部的筛选时间
    } = getParams();

    const firstLabel = labelType.value.length > 0 ? { first_label: [labelType.value] } : {};
    return {
      startDate,
      endDate,
      impression_date: impressionDate,
      campaign_type: campaignType,
      asset_type: assetType,
      country_code: countryCode,
      metric: [lineChartMetric.value],
      network,
      platform,
      ...firstLabel,
      label_search_type: labelsSearchType,
      labels,
      group: ['labels', 'dtstatdate'],
      orderby: [{ by: 'dtstatdate', order: 'ASC' }],
      keywords: keywords as any,
    };
  };

  const getLabelInsightLineChartData = async () => {
    showLineChartLoading();
    const [startDate, endDate] = lineChartDtstatdate.value;
    // 折线图内部的时间，覆盖顶部的时间
    const res = await getLabelInsightTrendData({
      ...getLineChartParams(),
      startDate, endDate,
    });
    lineChartData.value = res.chartDataList;
    secondLabelList.value = res.secondLabelList;
    hideLineChartLoading();
  };

  // 气泡图配置
  const bubbleMetricX = ref(DEFAULT_BUBBLE_METRICS[0]);
  const bubbleMetricY = ref(DEFAULT_BUBBLE_METRICS[1]); // ref('installs');
  const avgX = ref(120); // X轴平均值
  const avgY = ref(310); // Y轴平均值
  const bubbleLabelData = ref<BubbleChartData[]>([]);
  const bubbleLoading = ref(false);
  const updateBubbleMetricX = (val: string) => {
    bubbleMetricX.value = val;
    getBubbleLabelsData();
  };
  const updateBubbleMetricY = (val: string) => {
    bubbleMetricY.value = val;
    getBubbleLabelsData();
  };

  // 获取气泡图数据
  const getBubbleLabelsData = async () => {
    let params = getLineChartParams();
    const metric = [...new Set(['spend', bubbleMetricX.value, bubbleMetricY.value])];
    params = {
      ...params,
      group: ['labels'],
      metric,
      orderby: [
        { by: 'spend', order: 'DESC' },
      ],
    };
    bubbleLoading.value = true;
    const res = await getInsightBubbleData(params);
    avgX.value = res.avg ? res.avg[bubbleMetricX.value] || 0 : 0;
    avgY.value = res.avg ? res.avg[bubbleMetricY.value] || 0 : 0;
    bubbleLabelData.value = (res.chart || []).slice(0, 20).map(item => ({
      xVal: item[bubbleMetricX.value],
      yVal: item[bubbleMetricY.value],
      name: item.labels,
      asset_num: item.asset_num,
    }));
    bubbleLoading.value = false;
  };


  // 监听图表参数的变化
  watchEffect(() => {
    insightEventBus.emit(INSIGHT_EVENT_TYPE.WATCH_CAHRT_PARAMS, {
      scatter: {
        xAxis: bubbleMetricX.value,
        yAxis: bubbleMetricY.value,
      },
      line: {
        date: cloneDeep(lineChartDtstatdate.value),
        metric: lineChartMetric.value,
      },
    });
  });

  // 监听顶部筛选条件变化
  insightEventBus.on((event: string, data?: any) => {
    if (event === INSIGHT_EVENT_TYPE.REFRESH) {
      getLabelInsightLineChartData();
      getBubbleLabelsData();
    }
    if (event === INSIGHT_EVENT_TYPE.UPDATE_DATE) {
      (isArray(data) && data.length === 2) && updateLineChartDtstatdate(data);
    }
    if (event === INSIGHT_EVENT_TYPE.UPDATE_CAHRT_PARAMS) {
      if (!isPlainObject(data)) return;
      const { scatter, line } = data as TChartParam;
      if (isPlainObject(scatter)) {
        const { xAxis, yAxis } = scatter;
        if (isString(xAxis)) bubbleMetricX.value = xAxis;
        if (isString(yAxis)) bubbleMetricY.value = yAxis;
      }
      if (isPlainObject(line)) {
        const { date, metric } = line;
        (isArray(date) && date.length === 2) && updateLineChartDtstatdate(date);
        if (isString(metric)) updateLineChartMetric(metric);
      }
    }
  });

  return {
    isLineChartLoading,
    getLabelInsightLineChartData,
    updateLineChartMetric,
    updateLineChartDtstatdate,
    lineChartData, lineChartMetric, lineChartDtstatdate,
    secondLabelList, bubbleMetricX, bubbleMetricY, avgX, avgY, bubbleLabelData, bubbleLoading,
    updateBubbleMetricX, updateBubbleMetricY, getBubbleLabelsData,
  };
});
