import { STORE_KEY } from '@/config/config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { IMetric } from 'common/components/CustomizeColumnsDialog/type';
import type { ICustomViewItem } from 'common/components/NewViewTab';
import type { ICondition } from 'common/components/SearchBox';
import { useLoading } from 'common/compose/loading';
import { FirstLabelType } from 'common/service/creative/common/type';
import { IMetricItem } from 'common/service/creative/dashboard-af/type';
import { getInsightData, getUnlabeledData } from 'common/service/creative/label/insight';
import { FormModel, TableRes } from 'common/service/creative/label/insight/type';
import { cloneDeep, isArray, isPlainObject, uniq } from 'lodash-es';
import { defineStore, storeToRefs } from 'pinia';
import { SortInfo } from 'tdesign-vue-next';
import { computed, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { formatMetriclist } from '../dashboard-af/utils';
import {
  DEFAULT_BUBBLE_METRICS,
  DEFAULT_LINE_METRIC,
  NA_LABEL_STR,
  OPTIONS_SOURCE,
  SEARCH_BOX_DEFAULT_VALUE,
} from './const';
import { getLabelsInsightFilterConfig } from './insight-filter-config';
import type { TChartParam } from './labels-insight-chart.store';
import { INSIGHT_EVENT_TYPE } from './labels-insight-chart.store';
import { useLabelViews } from './labels-views';
import { getDefaultDate, getReqLabels, insightEventBus } from './utils';

export const useLabelsInsightStore = defineStore(STORE_KEY.CREATIVE.LABELS.INSIGHT, () => {
  const viewModule = 'create-labels-insight';
  const options = ref([]);
  const pageIndex = ref(1);
  const pageSize = ref(10);
  const tableData = ref<TableRes[]>([]);
  const totalCount = ref(0);
  const downloading = ref(false);
  const isFilterUnlabeled = ref(true);
  const route = useRoute();

  const code = route.query?.code as string; // 判断是不是分享视图进来的
  const { gameCode } = storeToRefs(useGlobalGameStore());

  /* ---------------------------------loading开始----------------------------------*/
  const { isLoading: isTableLoading, showLoading: showTableLoading, hideLoading: hideTableLoading } = useLoading();
  const { isLoading: isInitLoading, showLoading: showInitLoading, hideLoading: hideInitLoading } = useLoading();
  /* ---------------------------------loading结束----------------------------------*/

  const baseModelValue = {
    date: getDefaultDate(gameCode.value),
    asset_name: [],
    serial_name: [],
    campaign_name: [],
    ad_group_name: [],
    impression_date: [],
    label: {
      labelList: [] as string[],
      labelsSearchType: 1,
    },
    campaign_type: [],
    type: ['VIDEO'],
    /* update by euphrochen @0407 内网级联，外网多选，无region start */
    country: {
      country: [],
    },
    country_code: [],
    /* update by euphrochen @0407 内网级联，外网多选，无region end*/
    network: [],
    platform: [],
    keywords: SEARCH_BOX_DEFAULT_VALUE as ICondition[],
    asset_age: [],
  };

  const formModelValue = ref<FormModel>(cloneDeep(baseModelValue));

  const labelType = ref(''); // 一级标签筛选

  const defaultFoldList = [
    'serial_name',
    'campaign_name',
    'ad_group_name',
    'ad_name',
    'platform',
    'country_code',
    'asset_age',
    'label',
  ];
  const foldList = ref<string[]>([...defaultFoldList]); // 默认折叠的筛选项
  const setFoldList = (val: string[]) => (foldList.value = [...val]);

  // 折线图和气泡图的参数
  const chartParams = ref<TChartParam>();
  insightEventBus.on((event: string, data: TChartParam) => {
    if (event === INSIGHT_EVENT_TYPE.WATCH_CAHRT_PARAMS) {
      chartParams.value = data;
    }
  });

  // const metricList = ref<IBusinessTableMetric[]>([]); // 展示列
  /** update by euphrochen @0407 start 调整成 后端接口 统一返回，保证跟 dashboard 页一致， 通过admin 配置端管理 start **/
  const backendMetricList = ref<IMetricItem[]>([]);
  // csv下载 和 metric下拉列表 均需使用allMetrics
  const allMetrics = computed((): IMetricItem[] => backendMetricList.value?.map(item => ({
    ...item, // 保留原有属性字段，用于chart format
    type: 'group',
    colKey: item.key,
    groupName: item.type,
    value: item.key,
    label: item.title,
  })),
  );
  const colDialogList = computed(() => {
    if (!backendMetricList.value?.length) return [];
    return formatMetriclist([...backendMetricList.value])?.map(group => ({
      groupName: group.label,
      list: group.children.map(item => ({
        colKey: item.value,
        title: item.label,
        tips: item.tips,
      })),
    }));
  });
  const colCheckedList = ref<{ colKey: string }[]>([]); // 后续需要对其重新赋值，不能是计算属性
  const tableColumns = computed(() => {
    const attrCols = [
      { colKey: 'top', title: 'Top', width: 80, fixed: 'left', index: 0 },
      { colKey: 'label', title: 'Label', width: 150, fixed: 'left', index: 1 },
    ];
    // 空值保护：若数据未初始化，直接返回基础列
    if (!backendMetricList.value?.length || !colCheckedList.value?.length) {
      return attrCols;
    }
    const metricCols = colCheckedList.value.map((item) => {
      const curMetric = backendMetricList.value?.find(m => m.key === item.colKey);
      return {
        colKey: item.colKey,
        title: curMetric?.title ?? 'Unknown', // 兜底默认值
        sorter: true,
        align: 'right',
        index: ((curMetric as any)?.index ?? 0) + 10,
        width: curMetric?.width,
        format: curMetric?.format,
      };
    });

    return [...attrCols, ...metricCols];
  });
  const changeCheckedColList = (list: IMetric[]) => {
    colCheckedList.value = list;
    pageIndex.value = 1;
    getTableData();
  };
  /** update by euphrochen @0407 end 调整成 后端接口 统一返回，保证跟 dashboard 页一致， 通过admin 配置端管理 end **/

  // 表格排序
  const defaultSort = [{ sortBy: 'spend', descending: true }];
  const tableSort = ref<SortInfo[]>([{ sortBy: 'spend', descending: true }]);

  const getParams = (isAll = false) => {
    const {
      asset_name: assetName,
      serial_name: serialName,
      campaign_name: campaignName,
      ad_group_name: adGroupName,
      date,
      campaign_type: campaignType,
      type,
      country_code: countryCode,
      network,
      label,
      platform,
    } = formModelValue.value;
    let { impression_date: impressionDate } = formModelValue.value;

    const { labelList = [], labelsSearchType = 1 } = label;
    const labels = getReqLabels(totalLabelList.value, labelList);

    const orderby = tableSort.value.map(item => ({
      by: item.sortBy,
      order: item.descending ? 'DESC' : 'ASC',
    }));

    // 过滤空数据
    impressionDate = impressionDate.filter(item => !!item);

    const keywords = [];
    if (assetName!.length > 0) keywords.push({ key: 'asset_name', val: assetName });
    if (serialName!.length > 0) keywords.push({ key: 'asset_serial_id', val: serialName });
    if (campaignName!.length > 0) keywords.push({ key: 'campaign_name', val: campaignName });
    if (adGroupName!.length > 0) keywords.push({ key: 'ad_group_name', val: adGroupName });

    return {
      startDate: date[0].replaceAll('-', ''),
      endDate: date[1].replaceAll('-', ''),
      pageSize: isAll ? undefined : pageSize.value,
      pageNum: isAll ? undefined : pageIndex.value,
      impression_date: impressionDate.map(item => item.replaceAll('-', '')),
      campaign_type: campaignType,
      asset_type: type,
      // country_code: convertArrayCase(country.country),
      country_code: countryCode,
      network,
      platform,
      labels: labels.filter(v => v !== NA_LABEL_STR),
      keywords,
      label_search_type: labelsSearchType,
      has_na_label: isFilterUnlabeled.value ? 1 : 0,
      first_label: labelType.value ? [labelType.value] : [],
      orderby,
      metric: colCheckedList.value.map(item => item.colKey),
      group: ['labels'],
    };
  };

  // 获取分页表格数据
  const getFullTableData = async (isAll = false) => {
    const params = getParams(isAll);
    const res = await getInsightData(params);

    let totalNum = 0;
    let tableDatalist: TableRes[] = [];

    const { list, total, count } = res;

    // 最后一页，且开启了Unlabeled，在最后补充一行Unlabeled数据
    const unlabeledDataList: TableRes[] = [];
    if ((isLastPage.value || isAll) && isFilterUnlabeled.value) {
      const unLabeledData = await getUnlabeledData(params);
      unLabeledData.top = -1;
      unLabeledData.second_label = 'Unlabeled';
      unLabeledData.first_label = 'All Label';
      unLabeledData.asset_num_rate = unLabeledData.asset_num / total.asset_num;
      unLabeledData.spend_rate = unLabeledData.spend / total.spend;
      unLabeledData.installs_rate = unLabeledData.installs / total.installs;
      console.log('unLabeledData', unLabeledData);
      unlabeledDataList.push(unLabeledData);
    }

    totalNum = count;
    tableDatalist = total!.spend !== null ? [total as TableRes].concat(list) : list;
    if (unlabeledDataList.length > 0) {
      tableDatalist = tableDatalist.concat(unlabeledDataList);
    }

    return {
      totalNum,
      tableDatalist,
    };
  };

  const getTableData = async () => {
    showTableLoading();

    const { totalNum, tableDatalist } = await getFullTableData();

    totalCount.value = totalNum;
    tableData.value = tableDatalist;

    hideTableLoading();
  };

  // 获取全量数据
  const downloadAll = async () => {
    downloading.value = true;
    const { tableDatalist } = await getFullTableData(true);
    downloading.value = false;
    return tableDatalist;
  };

  const onPageSizeChange = (size: number) => {
    pageIndex.value = 1;
    pageSize.value = size;
    getTableData();
  };

  const onPageIndexChange = () => {
    getTableData();
  };

  const isLastPage = computed(() => pageIndex.value === Math.ceil(totalCount.value / pageSize.value));

  watch(() => labelType.value, (val, prevVal) => {
    pageIndex.value = 1;
    // 只有从全部标签切换到其他标签时，才需要更新isFilterUnlabeled
    if (prevVal === '' && val) {
      isFilterUnlabeled.value = false;
    }
    getTableData();
    insightEventBus.emit(INSIGHT_EVENT_TYPE.REFRESH);
  });

  // 分享视图所需的参数
  const shareViewParams = computed(() => ({
    game: gameCode.value,
    param: {
      form: { ...formModelValue.value },
      table: {
        metric: colCheckedList.value.map(item => item.colKey),
        page_size: pageSize.value,
        table_sort: tableSort.value,
      },
      tab: {
        label_type_drag_list: labelTypeList.value.map(item => item.value), // tab选项卡的拖拽顺序
        label_type: labelType.value, // 选中的tab
      },
      ...(isPlainObject(chartParams.value)
        ? {
          chart: chartParams.value,
        }
        : {}),
      fold_list: foldList.value, // 为什么这里也放个fold_list？ 因为与param平级的外层的fold_list，视图的查询接口没有返回这个字段。
    },
    fold_list: foldList.value,
    system: viewModule,
  }));

  // 视图功能
  const {
    viewId,
    viewList,
    isViewLoading,
    isDefaultView,
    addView,
    updateView,
    deleteView,
    initViewList,
    getViewItem,
    setViewId,
    initView,
  } = useLabelViews({
    module: viewModule,
    code,
    gameCode,
    baseModelValue,
    shareViewParams,
    refreshData: getTableData,
    onViewChange: updateFormAndTagModelValue,
  });

  // 表单组件渲染时用到的数据源
  const {
    formList, metricCfgList, labelTypeList, totalLabelList, initLabelTypeList, initOptions,
  } = getLabelsInsightFilterConfig(gameCode, formModelValue);

  const firstLabelList = computed<FirstLabelType[]>(() => [
    {
      label: 'All Labels',
      value: '',
      label_method: '',
      label_type: '',
      multiple: false,
      mutex: false,
      required: false,
    },
    ...labelTypeList.value,
  ]);

  async function init() {
    showInitLoading();
    await initOptions({ source: OPTIONS_SOURCE.LABELS });
    backendMetricList.value = [...metricCfgList.value]; // 浅拷贝
    colCheckedList.value = [...metricCfgList.value]
      ?.filter(item => item.default)
      .map(item => ({ colKey: item.key }));
    initView(); // 进来一开始必须是默认视图
    hideInitLoading();

    if (code) {
      await Promise.all([initViewList()]); // 先拉配置, 取视图和分享视图
      const firstView = viewList.value[0]; // 更新表单和labelType的值
      updateFormAndTagModelValue(firstView);
      getTableData(); // 再拉取表格
      insightEventBus.emit(INSIGHT_EVENT_TYPE.REFRESH); // 初始化表格上方的图表
    } else {
      Promise.all([initViewList(), getTableData()]);
      insightEventBus.emit(INSIGHT_EVENT_TYPE.REFRESH);
    }
  }

  const onSubmit = () => {
    insightEventBus.emit('update-date', formModelValue.value.date);
    getTableData();
    insightEventBus.emit(INSIGHT_EVENT_TYPE.REFRESH);
  };

  const onReset = () => {
    foldList.value = [...defaultFoldList];
    formModelValue.value = cloneDeep(baseModelValue);
    colCheckedList.value = backendMetricList.value
      ?.filter(item => item.default)
      .map(item => ({ colKey: item.key }));
    labelType.value = '';
    tableSort.value = [{ sortBy: 'spend', descending: true }];
    pageIndex.value = 1;
    pageSize.value = 10;
    getTableData();
    insightEventBus.emit(INSIGHT_EVENT_TYPE.REFRESH);
    // 更新气泡图和折线图中的参数
    insightEventBus.emit(INSIGHT_EVENT_TYPE.UPDATE_CAHRT_PARAMS, {
      scatter: {
        xAxis: DEFAULT_BUBBLE_METRICS[0],
        yAxis: DEFAULT_BUBBLE_METRICS[1],
      },
      line: {
        date: formModelValue.value.date,
        metric: DEFAULT_LINE_METRIC,
      },
    });
  };

  // 分享视图进来或者，视图切换时，
  function updateFormAndTagModelValue(viewItem: ICustomViewItem) {
    console.log('viewItem', viewItem);
    // 外层的param接口目前没有返回， 如果后面接口返回了，也可以用这个
    const { fold_list: outerFoldList = [] } = viewItem.param;
    const { form = {}, table = {}, fold_list: innerFoldList = [], tab = {}, chart } = viewItem.param || {};
    const { label_type_drag_list: labelTypeDragList, label_type: selectedLabelType } = tab;
    const { metric, page_size: viewPageSize, table_sort: viewTableSort } = table;
    const uniqFoldList = uniq([...outerFoldList, ...innerFoldList]);
    // 顶部表单
    setFoldList(isDefaultView.value ? defaultFoldList : uniqFoldList);
    formModelValue.value = {
      ...cloneDeep(baseModelValue),
      ...form,
    };
    // pageSize
    pageSize.value = viewPageSize || 10;
    // 这里需要等待labelTypeList的数据从接口拉回来
    const viewLabelTypeIsInLabelTypeList = labelTypeList.value.some(item => item.value === selectedLabelType);
    // tab选项卡的选中
    if (viewLabelTypeIsInLabelTypeList) {
      labelType.value = selectedLabelType;
    } else {
      labelType.value = '';
    }
    // tab选项卡的拖拽顺序
    if (isArray(labelTypeDragList) && labelTypeDragList.length > 0) {
      // 由于 labelTypeList是从接口回来的
      // 会出现视图中保存的数据和接口返回的不一样。
      // 所以，先求交集，把视图中保存和接口回来的，都有的部分排在前面
      const intersectionlabelTypeList: FirstLabelType[] = [];
      labelTypeDragList.forEach((value) => {
        const labelItem = labelTypeList.value.find(item => item.value === value);
        if (labelItem) {
          intersectionlabelTypeList.push(labelItem);
        }
      });
      // 然后，求差集把接口中有的，但是视图中保存的没有的，排在后面
      const differencelabelTypeList = labelTypeList.value.filter(item => !labelTypeDragList.includes(item.value));
      labelTypeList.value = [...intersectionlabelTypeList, ...differencelabelTypeList];
    } else {
      // tab选项 使用默认的排序
      // cloneDeep一把， 防止tab选项卡拖拽排序时labelTypeList的变化影响到initLabelTypeList
      labelTypeList.value = cloneDeep(initLabelTypeList.value);
    }
    // 设置要显示的表格列
    if (isArray(metric) && metric.length > 0) {
      colCheckedList.value = metric.map(item => ({ colKey: item }));
    } else {
      // 使用默认展示的列
      colCheckedList.value = backendMetricList.value
        ?.filter(item => item.default)
        .map(item => ({ colKey: item.key }));
    }
    // 表格排序
    if (isArray(viewTableSort) && viewTableSort.length > 0) {
      tableSort.value = cloneDeep(viewTableSort);
    } else {
      tableSort.value = cloneDeep(defaultSort);
    }
    // 更新气泡图和折线图中的参数
    insightEventBus.emit(
      INSIGHT_EVENT_TYPE.UPDATE_CAHRT_PARAMS,
      chart || {
        scatter: {
          xAxis: DEFAULT_BUBBLE_METRICS[0],
          yAxis: DEFAULT_BUBBLE_METRICS[1],
        },
        line: {
          date: formModelValue.value.date,
          metric: DEFAULT_LINE_METRIC,
        },
      },
    );
  }

  return {
    options,
    pageIndex,
    pageSize,
    totalCount,
    tableData,
    formList,
    formModelValue,
    tableColumns,
    allMetrics,
    labelTypeList,
    labelType,
    downloading,
    tableSort,
    colDialogList,
    colCheckedList,
    defaultSort,
    foldList,
    firstLabelList,
    isFilterUnlabeled,
    getTableData,
    downloadAll,
    onPageSizeChange,
    onPageIndexChange,
    changeCheckedColList,
    init,
    onReset,
    getParams,
    setFoldList,
    onSubmit,
    initOptions,
    viewList,
    viewId,
    isViewLoading,
    shareViewParams: computed(() => ({ ...shareViewParams.value, type: 'default' })),
    setViewId,
    updateFormAndTagModelValue,
    addView,
    updateView,
    deleteView,
    getViewItem,
    isInitLoading,
    isTableLoading,
  };
});
