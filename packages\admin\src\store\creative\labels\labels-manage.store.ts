/* eslint-disable prefer-destructuring */
import { computed, ref } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { getLabelsManageFilterConfig } from './manage-filter-config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { FormModel, RuleItem } from 'common/service/creative/label/manage/type.d';
import { getLabelRuleAssets, getLabelRules } from 'common/service/creative/label/manage';
import { BaseTableCol, TableProps } from 'tdesign-vue-next';
import { cloneDeep } from 'lodash-es';
import {
  getImpressionParams, getOfflineDateParams, getReqLabels, MANAGE_TABLE_COLS_ORDERS, MANAGE_TABLE_COLS_WIDTH,
} from './utils';
import { IMetric, IMetricGroup } from 'common/components/CustomizeColumnsDialog/type';

const initOrderColsStr = localStorage.getItem(MANAGE_TABLE_COLS_ORDERS) || '[]';
let initOrderCols = JSON.parse(initOrderColsStr) as IMetric[];
const initColsWidthStr = localStorage.getItem(MANAGE_TABLE_COLS_WIDTH) || '{}';
let initColsWidth = JSON.parse(initColsWidthStr) as { [col: string]: number };

export const useLabelsManageStore = defineStore(STORE_KEY.CREATIVE.LABELS.MANAGE, () => {
  const { gameCode } = storeToRefs(useGlobalGameStore());
  const totalCount = ref(0);
  const pageSize = ref(10);
  const pageIndex = ref(1);
  const tableData = ref<RuleItem[]>([]);
  const tableLoading = ref(false);
  const initCheckedCols = ref('[]');
  const curColsWidth = ref<{ [col: string]: number}>(initColsWidth);
  const expandedRowKeys = ref<string[]>([]);  // 展开的行id
  const expandDataList = ref<{ [key: string]: RuleItem[] }>({});

  function getColW(col: string, initW = 150) {
    return curColsWidth.value[col] || initW;
  }

  const baseModelValue = {
    serial_name: [],
    impression_status: '',
    impression_date: [],
    offline_date: [],
    label: {
      labelList: [],
      labelsSearchType: 1,
    },
  };
  const formModelValue = ref<FormModel>(cloneDeep(baseModelValue));

  // 获取顶部筛选表单配置，labels数据
  const { formList, totalLabelList, initOptions, firstLabels } = getLabelsManageFilterConfig(gameCode, formModelValue);

  // 从后台数据获取以及标签作为扩展列
  const firstLabelCols = computed(() => firstLabels.value.map((item) => {
    const isIntelligent = item.label_method === 'intelligent';
    return {
      colKey: item.value as string,
      title: `title-${item.label}`, // isIntelligent ? `${item.label}（Intelligent）` : item.label,
      width: isIntelligent ? 220 : 150,
      minWidth: isIntelligent ? 220 : 150,
      showName: item.label_method === 'intelligent' ? `${item.label}（Intelligent）` : item.label,
      label_level: item.label_level,
      ellipsis: true,
    };
  }));

  // 表格展示列
  const tableColumns = computed(() => {
    // 合并原始的列数据
    const totalColumns: BaseTableCol[] = [
      { colKey: 'name', title: 'Asset Name', width: getColW('name'), ellipsis: { showArrow: true, disabled: true } },
      ...firstLabelCols.value,
    ];

    // 根据当前选勾选展示的列，和当前宽度数据，计算出需要展示哪些列
    let metricCols = colCheckedList.value.map((item) => {
      const target = totalColumns.find(m => m.colKey === item.colKey) as BaseTableCol;
      if (!target) return undefined;
      target.width = getColW(target!.colKey as string);
      return target;
    }).filter(item => !!item) as BaseTableCol[];
    metricCols = ([
      { colKey: 'rule', title: 'Serial Name', width: getColW('rule'), ellipsis: { showArrow: true, disabled: true } },
    ] as BaseTableCol[]).concat(metricCols)
      .concat([{ colKey: 'actions', title: 'Actions', width: getColW('actions'), fixed: 'right' }]);

    return metricCols;
  });

  const colCheckedList = ref<IMetric[]>([]);
  const metricGroup = computed<IMetricGroup[]>(() => {
    const creativeLabelCols: IMetric[] = [];
    const contentLabelCols: IMetric[] = [];
    firstLabels.value.forEach((item, index) => {
      const orderIndex = index * 2 + 1;
      if (item.label_type === 'creative') {
        creativeLabelCols.push({ colKey: item.value, title: item.value, index: orderIndex });
      } else {
        contentLabelCols.push({ colKey: item.value, title: item.value, index: orderIndex });
      }
    });
    return [
      { groupName: 'Creative Label', list: creativeLabelCols },
      { groupName: 'Content Label', list: contentLabelCols },
      {
        groupName: 'Other Label Metrics',
        list: [
          { title: 'Asset Name', colKey: 'name', index: 0 },
        ],
      },
    ];
  });

  // 列宽度大小调整，本地存储
  const onSizeChange: TableProps['onColumnResizeChange'] = (context: { columnsWidth: { [colKey: string]: number }; }) => {
    const { columnsWidth } = context;
    curColsWidth.value = columnsWidth;
    localStorage.setItem(MANAGE_TABLE_COLS_WIDTH, JSON.stringify(columnsWidth));
  };

  const changeCheckedColList = (list: IMetric[]) => {
    colCheckedList.value = list;
    localStorage.setItem(MANAGE_TABLE_COLS_ORDERS, JSON.stringify(list));
  };

  async function init() {
    await initOptions();

    const others = metricGroup.value[2].list;
    const checkedCols = (firstLabels.value.map((item, index) => ({
      title: item.value, colKey: item.value, index: index * 2 + 1,
    })) as IMetric[]).concat(others)
      .sort((x, y) => (x.index || 0) - (y.index || 0));
    initCheckedCols.value = JSON.stringify(checkedCols);

    if (initOrderCols.length > 0) {
      colCheckedList.value = initOrderCols; // 有本地列配置缓存，优先使用
    } else {
      colCheckedList.value = checkedCols;
    }
  }

  // 构造请求参数
  function getParams(all = false) {
    const {
      serial_name: serialName, label,
      impression_status: impressionStatus, impression_date: impressionDate,
      offline_date: offlineDate,
    } = formModelValue.value;

    const { labelList = [], labelsSearchType = 1 } = label;
    const labels = getReqLabels(totalLabelList.value, labelList);
    const labelsData = labels.map(item => ({
      first_label: item.split('---')[0],
      second_label: item.split('---')[1],
    }));

    const impressionParams = getImpressionParams(impressionStatus, impressionDate);
    const offlineDateParams = getOfflineDateParams(offlineDate);

    return {
      game_code: gameCode.value,
      ...(all ? {} : {
        page: pageIndex.value - 1, // 分页数，从0开始
        page_size: pageSize.value, // 每页条数，最多100条
      }),
      names: serialName,
      labels: labelsData,
      label_search_type: labelsSearchType, // 0-默认(取并集), 1-取并集; 2-取交集
      ...impressionParams,
      ...offlineDateParams,
    };
  }

  const onPageSizeChange = (size: number) => {
    pageIndex.value = 1;
    pageSize.value = size;
    getTableData();
  };

  const onPageIndexChange = () => {
    getTableData();
  };
  const curTableList = ref<RuleItem[]>([]);  // 记录当前表格初始数据
  const getTableData = async (resetPage = false) => {
    if (resetPage) pageIndex.value = 1;  // 重置页数

    tableLoading.value = true;
    const params = getParams();
    const { list, count } = await getLabelRules(params);
    tableLoading.value = false;
    totalCount.value = count;
    tableData.value = list;
    curTableList.value = cloneDeep(list);

    expandedRowKeys.value = [];
    expandDataList.value = {};  // 重置展开行数据
  };

  const getAssetTable = async (ruleItem: RuleItem) => {
    const res = await getLabelRuleAssets({
      game_code: gameCode.value,
      rule_id: ruleItem.id,
      page: 0,
      page_size: 100,
    });
    const assets = res.map(item => ({
      ...item,
      id: `${ruleItem.id}-${item.asset_id}`,
      rule: '',
      offline_date: ruleItem.offline_date,
      isAsset: true,
    })) as RuleItem[];
    expandDataList.value[ruleItem.id] = assets;
    return assets;
  };

  // 重置筛选条件，列配置
  const onReset = () => {
    formModelValue.value = cloneDeep(baseModelValue);
    pageIndex.value = 1;
    pageSize.value = 10;
    localStorage.removeItem(MANAGE_TABLE_COLS_ORDERS);  // 清楚列配置本地缓存
    localStorage.removeItem(MANAGE_TABLE_COLS_WIDTH);
    colCheckedList.value = JSON.parse(initCheckedCols.value); // 设置初始化列
    initOrderCols = [];
    initColsWidth = {};
    getTableData();
  };

  return {
    formModelValue, formList, totalLabelList, totalCount, tableData, tableColumns, tableLoading, pageIndex, pageSize,
    firstLabels, metricGroup, colCheckedList, expandedRowKeys, firstLabelCols, curTableList, expandDataList,
    init, getTableData, onPageSizeChange, onPageIndexChange, getParams, changeCheckedColList, onReset, onSizeChange,
    getAssetTable,
  };
});
