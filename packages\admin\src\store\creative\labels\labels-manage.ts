import { ref } from 'vue';
import { useGlobalGameStore } from '@/store/global/game.store';
import { storeToRefs } from 'pinia';
import { getLabelsData } from './utils';
import { IOption } from 'common/components/NewCascader/type';

export function useLabelManager() {
  const { gameCode } = storeToRefs(useGlobalGameStore());
  const labelOptionList = ref<IOption[]>([]);
  async function init() {
    const { labelOptions } = await getLabelsData(gameCode.value);
    labelOptionList.value = labelOptions;
  }

  return {
    labelOptionList,
    init,
  };
}
