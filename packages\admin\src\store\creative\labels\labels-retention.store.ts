import { ref } from 'vue';
import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { TrafficSourceRes, RetentionRes, YTVideoConfig } from 'common/service/creative/label/insight/type';
import { getYtVideoConfig } from 'common/service/creative/label/insight';

export const useLabelRetentionStore = defineStore(STORE_KEY.CREATIVE.LABELS.RETENTION, () => {
  const youtubeId = ref('');
  const trafficSources = ref<TrafficSourceRes[]>([]);
  const retentions = ref<RetentionRes[]>([]);
  const ytVideoConfig = ref<YTVideoConfig>();
  const hasRateData = ref(false);  // 是否有跳出率相关数据

  const getConfig = async (ytId: string) => {
    youtubeId.value = ytId;

    const config = await getYtVideoConfig({
      youtube_id: youtubeId.value,
    });
    ytVideoConfig.value = config;

    // 授权且有impressionDate和maxDateSource，才展示跳出率数据
    hasRateData.value = config.authorized && !!config.impressionDate && !!config.maxDateSource;
  };

  return {
    ytVideoConfig, trafficSources, retentions, youtubeId, hasRateData,
    getConfig,
  };
});
