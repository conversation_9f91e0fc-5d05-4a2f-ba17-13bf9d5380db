/* eslint-disable prefer-destructuring */
import { ref } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { SystemLabelItem } from 'common/service/creative/label/manage/type.d';
import { useRemoteConfigStore } from '@/store/global/configRemote.store';

export const useLabelsSystemStore = defineStore(STORE_KEY.CREATIVE.LABELS.SYSTEM, () => {
  const { gameCode } = storeToRefs(useGlobalGameStore());
  const { initConfigTable } = useRemoteConfigStore();
  const totalCount = ref(0);
  const pageSize = ref(10);
  const pageIndex = ref(1);
  const totalData = ref<SystemLabelItem[]>([]);
  const tableData = ref<SystemLabelItem[]>([]);
  const tableLoading = ref(false);

  const { getConfig, removeConfig, createConfig, updateConfig } = initConfigTable({
    tableName: 'aix_web.creative_label',
    dataSource: 'common_config',
  });

  const tableColumns = ref([
    { title: '', colKey: 'drag', width: 40 },
    { title: 'First Label', colKey: 'name', width: 250, ellipsis: true },
    { title: 'Filling Requirement', colKey: 'required', width: 160 },
    { title: 'Choice Requirement', colKey: 'multiple', width: 160 },
    { title: 'Second Label', colKey: 'options', ellipsis: true },
    { title: 'Label Type', colKey: 'label_type', width: 140 },
    { title: 'Label Level', colKey: 'label_level', width: 140 },
    { title: 'Labeling Method', colKey: 'label_method', width: 160 },
    { title: 'Actions', colKey: 'actions', width: 200 },
  ]);

  const setCurData = () => {
    tableData.value = totalData.value.slice((pageIndex.value - 1) * pageSize.value, pageIndex.value * pageSize.value);
  };

  const onPageSizeChange = (size: number) => {
    pageIndex.value = 1;
    pageSize.value = size;
    setCurData();
  };

  const onPageIndexChange = () => {
    setCurData();
  };

  const getTableData = async () => {
    const { list = [], count } = await getConfig({
      condition: { game_code: gameCode.value },
      orderBy: JSON.stringify([['label_order', 'desc']]),
    }) as {
      list: SystemLabelItem[],
      count: number,
    };
    const data = list.map((item) => {
      let optionList: string[] = [];
      try {
        optionList = JSON.parse(item.options || '[]') || [];
      } catch {
        optionList = [];
      }
      return { ...item, optionList };
    });
    totalCount.value = count;
    totalData.value = data;
    setCurData();
  };

  const createEditLabel = async (data: SystemLabelItem) => {
    const params = {
      name: data.name,
      label_method: data.label_method,
      label_type: data.label_type,
      label_level: data.label_level,
      label_order: data.label_order,
      multiple: data.multiple,
      required: data.required,
      options: JSON.stringify(data.optionList),
      mutex: false,
      optionsOnly: false,
      game_code: gameCode.value,
    };
    if (data.id) return updateConfig([{ id: data.id, ...params }]);
    return createConfig([params]);
  };

  const updateLabelOrder = async (data: {id: number, label_order: number}[]) => await updateConfig([...data]);

  const deleteLabel = async (id: number) => removeConfig([id]);

  return {
    totalCount, tableData, tableLoading, tableColumns, totalData, pageIndex, pageSize,
    getTableData, onPageSizeChange, onPageIndexChange, createEditLabel, deleteLabel,
    updateLabelOrder,
  };
});

