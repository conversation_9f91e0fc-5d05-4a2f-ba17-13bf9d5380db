import { Ref, computed, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { useCustomView } from 'common/compose/useCustomView';
import { ICustomViewItem, IFormData } from 'common/components/NewViewTab/type';
import { useLoading } from 'common/compose/loading';

// 标签通用视图功能
export function useLabelViews(params: {
  module: string,
  code?: string,
  gameCode: Ref<string>,
  baseModelValue: any, // 初始值
  shareViewParams: Ref<any>, // 外部传入的待保存视图参数
  refreshData?: Function, // 重新数据回调函数
  onViewChange: Function, // 视图切换时，调用外部传入的处理方法
  isAllGame?: boolean
}) {
  const {
    getList: getViewList, getShare, addView: addViewService,
    updateView: updateViewService, deleteView: deleteViewService,
  } = useCustomView();
  const {
    module, gameCode, baseModelValue, shareViewParams, refreshData, onViewChange, code,
    isAllGame = false,
  } = params;
  const { isLoading: isViewLoading, showLoading: showViewLoading, hideLoading: hideViewLoading } = useLoading();
  const viewList = ref<ICustomViewItem[]>([]); // 视图列表

  const viewGames = computed(() => [gameCode.value, ...(isAllGame ? ['allgame'] : [])]);
  const viewId = ref<string>('default'); // 当前选择的视图的id
  const setViewId = (val: string) => viewId.value = val;

  const initView = () => {
    if (viewId.value !== defaultView.value.value) {
      setViewId(defaultView.value.value);
      onViewChange(defaultView.value);
    }
  };

  // 默认视图
  const defaultView = computed<ICustomViewItem>(() => ({
    label: 'Default',
    value: 'default',
    game: gameCode.value,
    type: 'default',
    param: cloneDeep(baseModelValue),
    module,
  }));

  const initViewList = async () => {
    showViewLoading();
    const [list = [], share] = await Promise.all([
      getViewList({
        game: viewGames.value,
        module,
        type: 'custom',
      }),
      code ? getShare(code) : null,
    ]);
    // 只要game为当前游戏获取allgame的
    const customViewList = (list as ICustomViewItem[]).filter(item => viewGames.value.includes(item.game));
    viewList.value = [
      ...(share !== null ? [{ ...share, label: 'Share' }] : []),
      defaultView.value, ...customViewList,
    ];
    // 如果有分享视图，把选中的默认视图改成分享视图
    if (share) {
      setViewId(share.value);
    }
    hideViewLoading();
  };

  // 生成新增或者更新视图时，接口需要的参数
  const generateViewParams = ({ formData }: { formData: IFormData}) => {
    const { viewType, viewName } = formData;
    return {
      ...shareViewParams.value,
      param: {
        ...shareViewParams.value.param,
        game: viewType === 'all' && isAllGame ? 'allgame' : gameCode.value,
      },
      // game: viewType === 'all' ? 'allgame' : gameCode.value,
      game: gameCode.value,
      type: 'custom',
      name: viewName,
    };
  };

  // 新增视图
  const addView = async (data: { formData: IFormData}) => {
    await addViewService(generateViewParams(data));
    initViewList();
  };

  // 更新视图
  const updateView = async (data: { formData: IFormData}) => {
    await updateViewService({
      ...generateViewParams(data),
      id: viewId.value,
    });
    initViewList();
  };

  // 删除视图
  const deleteView = async (viewItem: ICustomViewItem) => {
    await deleteViewService({ id: viewItem.value });
    setViewId(defaultView.value.value);
    onViewChange(defaultView.value);
    initViewList();
    refreshData?.();
  };

  // 根据当前视图id查找视图
  const getViewItem = (id: string) => viewList.value.find(item => item.value === id)!;

  return {
    viewList, viewId, isViewLoading,
    isDefaultView: computed(() => viewId.value === 'default'),
    setViewId, addView, updateView, deleteView, getViewItem, initViewList, initView,
  };
}
