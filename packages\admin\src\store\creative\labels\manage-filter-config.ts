import { Ref, ref, computed } from 'vue';
import { getLabelsData } from '@/store/creative/labels/utils';
import { BaseCommonSearchInput } from 'common/components/CommonSearchInputBox';
import components from '@/views/creative/common/components/components';
import LibraryLabel from '@/views/creative/library/components/LibraryLabel.vue';
import { IOption } from 'common/components/NewCascader/type';
import { FirstLabelType } from 'common/service/creative/common/type';
import { FormModel } from 'common/service/creative/label/manage/type';
import dayjs from 'dayjs';

export const getLabelsManageFilterConfig = (gameCode: Ref<string>, formModelValue: Ref<FormModel>) => {
  const labelOptions = ref<IOption[]>([]); // 筛选条件的label
  const totalLabelList = ref<string[]>([]); // 一级-二级标签列表
  const firstLabels = ref<FirstLabelType[]>([]); // 一级标签

  async function initOptions() {
    const {
      labelOptions: labels, totalLabels, firstLabelList,
    } = await getLabelsData(gameCode.value);

    labelOptions.value = labels;
    totalLabelList.value = totalLabels;
    firstLabels.value = firstLabelList;
  }

  const formList = computed(() => {
    const curDate = dayjs().format('YYYY-MM-DD');
    const impressionDateDisabled = formModelValue.value.impression_status !== 'Published';
    return [
      {
        name: BaseCommonSearchInput,
        props: {
          label: 'Serial Name',
          tagInputProps: {
            class: 'w-[240px]',
          },
        },
        ext: {
          key: 'serial_name',
          label: 'Serial Name',
          isAllowClose: false,
          isHide: false,
        },
      },
      {
        name: LibraryLabel,
        props: {
          valueType: 'everyWithLowest',
          options: labelOptions,
          labels: ['Label Name', 'First Label', 'Second Label'],
        },
        ext: {
          key: 'label',
          label: 'Label',
          isAllowClose: false,
        },
      },
      {
        name: components['t-select'],
        props: {
          placeholder: 'Status',
          class: 'w-[160px]',
          options: [
            { label: 'ALL', value: '' },
            { label: 'Published', value: 'Published' },
            { label: 'Unpublished', value: 'Unpublished' },
          ],
          clearable: true,
          label: 'Status:',
        },
        ext: {
          key: 'impression_status',
          isAllowClose: false,
          default: undefined,
        },
      },
      {
        name: components.ImpressionDate,
        props: {
          title: 'Impression Date',
          disabled: impressionDateDisabled,
          maxDate: curDate,
          disableDate: {
            after: curDate,
          },
        },
        ext: {
          default: [],
          date: [],
          key: 'impression_date',
          label: 'Impression Date',
          isAllowClose: false,
        },
      },
    ];
  });

  return {
    formList, labelOptions, totalLabelList, firstLabels,
    initOptions,
  };
};
