import { useRemoteConfigStore } from '@/store/global/configRemote.store';
import { useEventBus } from '@vueuse/core';
import { FirstLabelType } from 'common/service/creative/common/type';
import { IMetricItem } from 'common/service/creative/dashboard-af/type';
import { SystemLabelItem } from 'common/service/creative/label/manage/type';
import { NA_ITEM, NA_LABEL_STR, OPTIONS_SOURCE, ROOT_LABEL_NAME } from './const';
import { GAME_WITH_SUFFIX } from '../dashboard/utils-schema';
import type { IOption as IMetricFilterOption } from 'common/components/MetricFilterDialog/type';
import { IMetricGroup } from 'common/components/CustomizeColumnsDialog/type';
import { uniq, groupBy } from 'lodash-es';
import dayjs from 'dayjs';

export function getReqLabels(tableList: string[], selectedLabels: string[]) {
  let labels: string[] = [];
  if (selectedLabels.length === 1 && selectedLabels[0] === ROOT_LABEL_NAME) {
    labels = tableList;
  } else {
    const list = selectedLabels.map(item => item.split('%-%').slice(1)
      .join('---'));
    list.forEach((item) => {
      if (item.includes('---')) {
        labels.push(item);
      } else {
        const validList = tableList.filter(item2 => item2.includes(`${item}---`));
        labels = labels.concat(validList);
      }
    });
  }
  return labels;
}

export async function getLabelsData(gameCode: string, env = 'prod', hideNA = false) {
  const { initConfigTable } = useRemoteConfigStore();
  const { getConfig } = initConfigTable({
    tableName: 'aix_web.creative_label',
    dataSource: 'common_config',
    env,
  });

  const totalLabels: string[] = []; // 所有一级、二级标签数据
  const { list } = (await getConfig({
    condition: { game_code: gameCode },
    orderBy: JSON.stringify([
      ['label_order', 'desc'],
      ['label_method', 'asc'], // intelligent 在manual 前面
    ]),
  })) as {
    list: SystemLabelItem[];
    count: number;
  };

  const labelTypeList: FirstLabelType[] = [];
  const searchBoxOptions = list.map((item: any) => {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const { name, multiple, mutex, required, label_method, label_type, label_level, label_order } = item;
    let { options } = item;

    try {
      options = JSON.parse(options);
    } catch {
      options = [];
    }

    options = options.map((i: string) => ({ label: i, value: i }));
    options.forEach((second: { label: string }) => {
      totalLabels.push(`${name}---${second.label}`);
    });

    labelTypeList.push({
      label: name,
      value: name,
      label_method,
      label_type,
      multiple,
      mutex,
      required,
      label_level,
      children: options,
      label_order,
    });

    return { label: name, value: name, children: options };
  });

  const initLabelValue = searchBoxOptions.map(({ value }: { value: string }) => `${ROOT_LABEL_NAME}%-%${value}`);

  if (!hideNA) {
    const naLabelItem = NA_ITEM;
    searchBoxOptions.push(naLabelItem);
    totalLabels.push(NA_LABEL_STR);
  }

  return {
    labelOptions: [
      {
        label: ROOT_LABEL_NAME,
        value: ROOT_LABEL_NAME,
        children: searchBoxOptions,
      },
    ],
    firstLabelList: labelTypeList,
    totalLabels,
    initLabelValue,
  };
}

export function getImpressionParams(impressionStatus: string, impressionDate: string[]) {
  const impressionParams: {
    impression_status?: string;
    start_impression_date?: string;
    end_impression_date?: string;
  } = {};
  if (impressionStatus === 'Published') {
    const [d1, d2] = impressionDate;
    impressionParams.impression_status = 'Published';
    if (d1) impressionParams.start_impression_date = d1.replaceAll('-', '');
    if (d2) impressionParams.end_impression_date = d2.replaceAll('-', '');
  }
  return impressionParams;
}

export function getOfflineDateParams(offlineDate: string[]) {
  const offlineDateParams: {
    start_offline_date?: string;
    end_offline_date?: string;
  } = {};
  if (offlineDate[0]) offlineDateParams.start_offline_date = offlineDate[0].replaceAll('-', '');
  if (offlineDate[1]) offlineDateParams.end_offline_date = offlineDate[1].replaceAll('-', '');

  return offlineDateParams;
}

export const MANAGE_TABLE_COLS_ORDERS = 'label-management-cols-orders';

export const MANAGE_TABLE_COLS_WIDTH = 'label-management-cols-width';

export const insightEventBus = useEventBus<string>('label-insight');

/**
 * add by euphrochen 后续通过此方法抹平 label insight下各页面表格指标之间的差异
 */
export function transformMetrics({ source, metrics }: { source: string; metrics: IMetricItem[] }) {
  // todo: metrics排序，key=spend,creatives,installs的优先排前面
  const sortedMetrics = metrics.sort((a, b) => {
    const orderA = a.key === 'spend' ? 1 : a.key === 'asset_num' ? 2 : a.key === 'installs' ? 3 : 4;
    const orderB = b.key === 'spend' ? 1 : b.key === 'asset_num' ? 2 : b.key === 'installs' ? 3 : 4;
    return orderA - orderB;
  });
  console.log('sortedMetrics', sortedMetrics);
  if (source === OPTIONS_SOURCE.LABELS) {
    return sortedMetrics.filter(item => !item.hidden);
  }
  return sortedMetrics.filter(item => item.key !== 'asset_num' && !item.hidden);
}

/**
 * add by euphrochen max date fix
 */
export function getDefaultDate(game: string): string[] {
  const isAFGame = GAME_WITH_SUFFIX.includes(game);
  const maxDate = isAFGame ? dayjs().subtract(1, 'day') : dayjs();
  return [maxDate.subtract(6, 'day').format('YYYY-MM-DD'), maxDate.format('YYYY-MM-DD')];
}

// 将指标分组
export function groupMetrics(metrics: IMetricItem[]) {
  const metricGroup: IMetricGroup[] = [];
  metrics.forEach((item) => {
    const target = metricGroup.find(i => i.groupName === item.type);
    if (target) {
      target.list.push({ title: item.title, colKey: item.key });
    } else {
      metricGroup.push({
        groupName: item.type,
        list: [{ title: item.title, colKey: item.key }],
      });
    }
  });
  return metricGroup;
}

export function getMetricFilterOptions(metrics: IMetricItem[]) {
  const groupMetric = groupBy(metrics, 'type');
  const metricTypeList = uniq(metrics.map(item => item.type));
  return metricTypeList.reduce((options, type) => {
    const children = groupMetric[type].map(item => ({
      ...item,
      label: item.title,
      value: item.key,
    }));
    options.push({
      label: type,
      value: type,
      children,
    });
    return options;
  }, [] as IMetricFilterOption[]);
}
