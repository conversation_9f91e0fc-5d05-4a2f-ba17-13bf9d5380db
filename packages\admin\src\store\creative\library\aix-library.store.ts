import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { ref } from 'vue';
import { useDictionary } from '@/store/creative/library/dictionary';
import { useMaterialList } from '@/store/creative/library/metrialList';
import { useLoading } from 'common/compose/loading';
import { LibraryType } from '@/views/creative/library/define';
import { useWatchGameChange } from 'common/compose/request/game';
import { useLabelManager } from '@/store/creative/labels/labels-manage';

export const useAixLibraryStore = defineStore(STORE_KEY.CREATIVE.AIX_LIBRARY, () => {
  const CURREN_LIBRARY = LibraryType.Aix;
  const filters = ref();
  const { isLoading, showLoading, hideLoading } = useLoading();

  const dictionary = useDictionary(CURREN_LIBRARY);
  const material = useMaterialList(CURREN_LIBRARY, dictionary.activeFolderId);

  const setFilters = (filter: any) => {
    filters.value = filter;
  };

  const label = useLabelManager();

  const init = async (formatType = 0) => {
    useWatchGameChange(async () => {
      showLoading();
      material.setFormatType(formatType);
      material.resetSearch();
      dictionary.initDictionary();
      dictionary.getSyncNodeList();
      label.init();
      hideLoading();
    });
  };

  return {
    CURREN_LIBRARY,
    init,
    label,
    isLoading,
    setFilters,
    dictionary,
    material,
  };
});

export default useAixLibraryStore;
