import type { IMetrialListCondition, ITaskCondition } from './type';
import dayjs from 'dayjs';

export const DEFAULT_TASK_LIST_CONDITION: ITaskCondition = {
  pageIndex: 1,
  pageSize: 10,
  directories: [], // text: null,
  text: [],
  channel: null,
  formatType: null,
  status: null,
  dateType: 'start_date', // || 'synced_data',
  taskType: 'all', // || 'manual',
  ruleId: '',
  dateRange: [
    dayjs().subtract(6, 'day')
      .format('YYYY-MM-DD') as unknown as string,
    dayjs().format('YYYY-MM-DD') as unknown as string,
  ],
};

export const METRIALLIST_CONDITION: IMetrialListCondition = {
  formatTypeList: [],
  uploadCloudTime: ['', ''],
  syncMedia: [],
  pageSize: 20,
  pageNum: 1,
  text: '',
  filteronlinestatus: 0,
  onlineStatus: null, // searchType: 1,
  searchType: 2, // 2 或者 4
  labels: [],
  names: undefined,
  labelsSearchType: 1,
  syncedStatus: 0,
};

export enum SyncStatus {
  SYNCING = 1,
  SYNCED = 2,
}

export const syncedListMap = {
  1: 'Google',
  2: 'Facebook',
  3: 'TikTok',
  4: 'Twitter', // 5: 'asa',
  // 6: 'Reddit',
  7: 'Unity',
  8: 'Snapchat',
  9: 'AppLovin',
} as const;
