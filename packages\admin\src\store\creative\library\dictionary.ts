import {
  addFolder,
  getFolderList,
  getRootDicInfo,
  getRootId,
  initFolderFromNode,
  searchDirs,
} from 'common/service/creative/library/get-dictionary-list';
import { computed, nextTick, ref, watch } from 'vue';
import { useLoading } from 'common/compose/loading';
import { useRouteQuery } from '@vueuse/router';
import { LibraryType } from 'common/service/creative/library/type';
import { getSyncNodes, getSyncStatus } from 'common/service/creative/library/sync-dictionary';
import { useFetchWrapper } from 'common/compose/request/request';
import { useTree } from 'common/compose/tree';
import { ITree } from 'common/components/TreeMenu/type';
import { LibraryType as LibraryTypeDefine } from '@/views/creative/library/define';
import { useTips } from 'common/compose/tips';
import { get, toValue } from '@vueuse/core';
import type { ISyncNode } from './type';
import { transformSyncNode } from './utils';
import { transFullPathName } from '@/views/creative/library/utils';
import { isArray, uniq } from 'lodash-es';
import { SyncStatus } from './const';

/**
 *
 * 获取 creative 目录的过程:
 * getRootId  获取根节点的信息，得到folder id 这个将作为后续接口的参数，后续接口可以进行并行请求
 *     |-> getRootDicInfo  获取根目录的信息 -> 获取目录的更新时间，素材总数
 *     |-> getFolderDetail 获取目录的文件详情， 子文件数据特别多需要进行分页加载
 *     |-> getFolderList   获取子目录列表， 如果子列表特别多，需要对子列表进行分页加载
 *
 */


// TODO 优化 -> 移除fetchWrapper，这里的缓存体系，不应该使用fetchWrapper
// 应该有自己的独立的存储处理

export function useDictionary(libraryType: LibraryType) {
  const { isLoading: dicLoading, showLoading, hideLoading } = useLoading();
  const { err } = useTips();
  const baseFetchOpt = { storage: false };

  // 最终的目录树
  const { list: dictionaryList, addChildren, findNodeByValue, findParentNode, clearTree } = useTree();

  // 当前选中的目录 id
  const activeFolderId = useRouteQuery<string>('node', '');
  // 当前选中的目录名
  const activeDictionary = ref<string>();
  // 默认展开的节点关系
  const defaultExpanded = ref<string[]>([]);
  const isMediaLibrary = libraryType === LibraryTypeDefine.Media;

  // 正在同步的节点
  const syncNodeList = ref<ISyncNode[]>([]);

  const activeFolderItem = computed(() => findNodeByValue(activeFolderId.value));

  // 树的父级
  const treeParentIdList = computed(() => findParentNode(activeFolderItem.value)
    .map(i => i.value));

  // 同步按钮的loading
  const refreshLoading = computed(() => getRefreshLoading(
    syncNodeList.value,
    activeFolderId.value as string,
  ));

  // 根据不同的目录 判断按钮是否loading
  const getRefreshLoading = (list: ISyncNode[], id: string) => {
    const result = list.some(item => item.nodeId === id && item.syncStatus === 1);
    return result;
  };

  // 根据不同的 设置按钮的tip文字

  /**
   * 请求关系声明
   */
  const { data: rootItem, emit: emitGetRootId } = useFetchWrapper(getRootId, {}, baseFetchOpt);
  const { data: rootDicInfo, emit: emitGetRootInfo } = useFetchWrapper(
    getRootDicInfo,
    {
      id: rootItem.value?.depot_id,
      dir_type: isMediaLibrary ? 1 : 0,
    },
    baseFetchOpt,
  );

  // const {
  //   emit: emitGetChildFolder,
  // } = useFetchWrapper(
  //   getFolderList,
  //   {
  //     limit: 200,
  //     offset: 0,
  //     parent_id: undefined,
  //     list_type: '',
  //   },
  //   baseFetchOpt,
  // );

  // 决定携带"nodeId"时，是否进行初始化操作
  let isInitFolderFromNode = true;

  // 从某个搜索的节点开始初始化
  let shouldScrollToActive = true; // 是否需要滚动到当前点击的目录
  function initFromActiveId(id: string) {
    clearTree();
    activeFolderId.value = id;
    isInitFolderFromNode = true;
    shouldScrollToActive = true;
  }

  // 存放initFolderFromNode()方法的数据
  let result: any = null;
  // 节点ID的数组
  let folderPathArray: any = null;
  // 节点ID的索引
  let folderPathIndex = 0;
  // 存放 子目录获取 getFolderList()方法的数据
  const childrenObject: { [str: string]: any[] } = {};

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async function initChildFolder(id: string, dropdownLoad = false, callback?: (children: any) => void, noCache = false) {
    const parent = findNodeByValue(id);

    if (!parent?.isDir) return;
    if (!activeFolderId.value) {
      if (libraryType === LibraryTypeDefine.Aix) {
        activeFolderId.value = id;
        parent.isExpand = true;
      } else {
        parent.isExpand = true;
      }
    } else {
      if (libraryType !== LibraryTypeDefine.Aix) activeFolderId.value = '';
    }
    if (isInitFolderFromNode) {
      if (libraryType === LibraryTypeDefine.Aix) {
        result = await initFolderFromNode({
          node: activeFolderId.value || id,
          libraryType,
        });
        folderPathArray = result?.data?.folderPath?.split(',') || [];

        folderPathArray.forEach((pid: string) => {
          childrenObject[pid] = [];
        });
      } else {
        folderPathArray = [];
        childrenObject[id] = [];
      }
    }

    folderPathArray?.forEach((pid: string, idx: number, self: string[]) => {
      if (self.indexOf(id) > -1 && idx > self.indexOf(id)) childrenObject[pid] = [];
    });

    // "nodeId"是否在加载的节点的范围内
    let inRange = dropdownLoad;
    const { isLoading: parenLoading, hideLoading: hideParentLoading } = useLoading(true);
    parent.loading = parenLoading;

    const folderlist: any = await getFolderList({
      limit: 200,
      offset: childrenObject[id]?.length || 0,
      parent_id: id,
      list_type: libraryType,
      __no_cache: noCache ? 1 : undefined,
    });
    folderlist.total = +folderlist.total;

    // 如果没有目录了，直接结束掉递归
    if (!folderlist.total) {
      parent.isEnd = true;
      hideParentLoading();
      return;
    }

    // 如果本地目录列表没有拉完
    if (folderlist.data?.length !== +folderlist.total) {
      // 判断子目录列表没有拉完，则等待子目录列表拉完
      if (childrenObject[id]?.length !== +folderlist.total) {
        let children = await folderlist.data.map((item: any) => {
          if (!inRange) {
            if (folderPathArray?.indexOf(id) > -1) {
              folderPathIndex = folderPathArray.indexOf(id) + 1;
              inRange = folderPathArray[folderPathIndex] ? folderPathArray[folderPathIndex] === item.id : true;
            } else {
              inRange = true;
            }
          }

          return {
            isDir: item.direct_directory_count > 0,
            text: item.name,
            value: item.id,
            ext: item,
            isExpand: item.direct_directory_count > 0 && folderPathArray?.includes(item.id),
          };
        });
        childrenObject[id].push(...children);
        const childrenLen = childrenObject[id].length;
        children = null;
        if (!inRange) {
          parent.isMore = true;

          const folderlist: any = await getFolderList({
            limit: 1,
            offset: 0,
            parent_id: folderPathArray[folderPathIndex],
            list_type: libraryType,
          });

          children = folderlist.data.map((item: any) => ({
            isDir: !!item,
            text: item.parent_name,
            value: item.parent_id,
            ext: item,
            isExpand: item && folderPathArray?.includes(item.parent_id),
          }));
        } else {
          parent.isMore = false;

          parent.isEnd = childrenLen === +folderlist.total;
        }
        // 隐藏more按钮（去除没必要的交互）
        if ((childrenLen + children?.length) === +folderlist.total) parent.isMore = false;
        addChildren(parent.id!, [...childrenObject[id], ...(children || [])]);
        hideParentLoading();
        isInitFolderFromNode = false;
        if (folderPathArray?.includes(id)) {
          await initChildFolder(folderPathArray[dropdownLoad ? folderPathIndex - 1 : folderPathIndex]);
        }
      } else {
        parent.isEnd = true;
        hideParentLoading();
      }

      return;
    }

    const children = folderlist.data.map((item: any) => {
      if (!inRange) {
        if (folderPathArray?.indexOf(id) > -1) {
          folderPathIndex = folderPathArray.indexOf(id) + 1;
          inRange = folderPathArray[folderPathIndex] ? folderPathArray[folderPathIndex] === item.id : true;
        } else {
          inRange = true;
        }
      }

      return {
        isDir: item.direct_directory_count > 0,
        text: item.name,
        value: item.id,
        ext: item,
        isExpand: item.direct_directory_count > 0 && folderPathArray?.includes(item.id),
      };
    });
    addChildren(parent.id!, children);
    hideParentLoading();
    isInitFolderFromNode = false;
    if (folderPathArray?.includes(id)) {
      // 处理下一级目录的拉取
      await initChildFolder(folderPathArray[dropdownLoad ? folderPathIndex - 1 : folderPathIndex]);
    }

    setTimeout(() => {
      if (shouldScrollToActive) {
        document.querySelector('.active-folder')?.scrollIntoView({ behavior: 'smooth', block: 'center' });
        shouldScrollToActive = false;
      }
    });
  }

  function initRootItem() {
    const rootNode: ITree = {
      loading: true,
      isDir: true,
    };
    emitGetRootId({
      callback(rootInfo: any) {
        if (!rootInfo.depot_id) return;
        // initRootActiveId(isMediaLibrary ? '' : rootInfo.depot_id);
        emitGetRootInfo({
          id: isMediaLibrary ? rootInfo.game_code : rootInfo.depot_id,
          callback: (dicInfo: any) => {
            rootNode.text = dicInfo.name;
            rootNode.value = dicInfo.id;
            rootNode.isExpand = !!activeFolderId.value;
            addChildren('root', rootNode);
            initChildFolder(dicInfo.id);
          },
        });
      },
    });
  }

  // 初始化子路由
  async function initDictionary(nodeId = activeFolderId) {
    initRootItem();
    if (nodeId) {
      // 如果指定了展开节点，则需要初始化到展开节点
      // TODO 初始化到展开目录
    }
  }

  const changeActiveDictionary = async (val: string, isActive = false) => {
    if (libraryType === LibraryTypeDefine.Aix) {
      const result: any = await initFolderFromNode({
        node: val,
        libraryType,
      });
      folderPathArray = result.data?.folderPath?.split(',') || [];
    }

    childrenObject[val] = [];

    isActive ? await initChildFolder(val) : activeFolderId.value = val;
  };

  const creatDictionary = async (newDicName: string, level: string) => {
    showLoading();
    const currentId = get(activeFolderId);
    let parentId = '';
    const parentNode = findNodeByValue(currentId)?.parent;
    if (level === 'child') {
      parentNode && (parentNode.isDir = true);
      parentId = currentId;
    } else {
      const parent = toValue(parentNode?.value);
      if (parent) {
        parentId = parent;
      } else { // 如果没有，说明是根节点
        parentId = currentId;
      }
    }

    try {
      const res = await addFolder({
        parent_id: parentId,
        name: newDicName,
        rename_repeated_category: 0,
      });
      const { id } = res.data; // 新节点id
      if (res) {
        nextTick()
          .then(() => {
            activeFolderId.value = id;
            initChildFolder(parentId, false, () => {
              defaultExpanded.value = uniq(defaultExpanded.value.concat(parentId));
            }, true);
          });
      } else {
        await err('Cannot create directory with the same name');
      }
      hideLoading();
    } catch (err) {
      console.error('add folder err', err);
      hideLoading();
    }
  };

  const getSyncNodeList = async () => {
    const data: any = await getSyncNodes();
    const list = data?.list ?? [];
    setSyncNode(transformSyncNode(list));
  };

  const setSyncNode = (syncNode: ISyncNode[]) => {
    syncNode.forEach((item) => {
      const index = syncNodeList.value.findIndex(sync => sync.nodeId === item.nodeId);
      if (index !== -1) {
        syncNodeList.value[index] = Object.assign(syncNodeList.value[index], item);
      } else {
        syncNodeList.value.push(item);
      }
    });
  };

  // 目录变化的时候在拉一下 获取同步节点的接口
  watch(() => activeFolderId.value, () => {
    getSyncNodeList();
  });

  const refreshData = async (materialUpdate: Function) => {
    const { list = [] }: any = await getSyncStatus({
      nodes: syncNodeList.value.filter(item => item.syncStatus === SyncStatus.SYNCING)
        .map(item => item.nodeId),
    });
    if (isArray(list)) {
      const syncNodes: ISyncNode[] = transformSyncNode(list);
      // 吧syncNodes 同步到store
      setSyncNode(syncNodes);
      const syncedNodes = syncNodes.filter(item => item.syncStatus === SyncStatus.SYNCED);
      if (syncedNodes.length > 0) {
        const firstNodeName = transFullPathName(`${syncedNodes[0].fullPathName}${syncedNodes[0].name}`);
        const nodeNames = syncedNodes.length === 1
          ? `【${firstNodeName}】Dictionary`
          : `【${firstNodeName}】and other${syncedNodes.length}】folder`;
        const { success } = useTips({ title: 'Update succeeded' });
        success(`${nodeNames}Update succeeded`);
        // 重新拉接口
        // 先判断当前所在的树节点中有没有包含要同步的节点
        if (syncNodeList.value.filter(item => treeParentIdList.value.includes(item.nodeId)).length > 0) {
          materialUpdate();
        }

        // initRootItem();
        initChildFolder(activeFolderId.value);
      }
    }
  };

  // 搜索目录
  const searchDictionary = async (keyword: string) => {
    try {
      const res = await searchDirs({
        name: keyword,
        page: 0,
        page_size: 100, // 最大支持100个
      });
      return res.dirs || [];
    } catch (e) {
      return [];
    }
  };

  return {
    arthubCode: computed(() => rootItem.value?.arthub_code || rootItem.value?.game_code),
    type: computed(() => (rootItem.value?.type === '2' // 2 是google drive, 3 是dropbox
      ? 'google-drive' : rootItem.value?.type === '3'
        ? 'dropbox' : 'arthub')),
    publicToken: computed(() => rootItem.value?.public_token),
    activeFolderItem,
    defaultExpanded: computed(() => defaultExpanded.value),
    activeFolderId,
    rootDicInfo,
    dictionaryList,
    activeDictionary,
    dicLoading,
    refreshLoading,
    syncNodeList,
    initChildFolder,
    initDictionary,
    changeActiveDictionary,
    creatDictionary,
    setSyncNode,
    getSyncNodeList,
    refreshData,
    searchDictionary,
    initFromActiveId,
  };
}
