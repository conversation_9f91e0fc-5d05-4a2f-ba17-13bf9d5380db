import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { useDictionary } from '@/store/creative/library/dictionary';
import { ref } from 'vue';
import { useMaterialList, useMediaMaterialList } from '@/store/creative/library/metrialList';
import { useLoading } from 'common/compose/loading';
import { LibraryType } from '@/views/creative/library/define';
import { useWatchGameChange } from 'common/compose/request/game';
import { useLabelManager } from '@/store/creative/labels/labels-manage';

export const useMediaLibraryStore = defineStore(STORE_KEY.CREATIVE.MEDIA_LIBRARY, () => {
  const filters = ref();
  const { isLoading, showLoading, hideLoading } = useLoading();
  const CURREN_LIBRARY = LibraryType.Media;
  const setFilters = (filter: any) => {
    filters.value = filter;
  };
  const dictionary = useDictionary(CURREN_LIBRARY);
  const material = useMaterialList(CURREN_LIBRARY, dictionary.activeFolderId);
  const label = useLabelManager();

  const init = async () => {
    useWatchGameChange(async () => {
      showLoading();
      dictionary.initDictionary();
      material.resetAllMaterialMap();
      label.init();
      !dictionary.activeFolderId.value && material.update();
      hideLoading();
    });
  };
  return {
    isLoading,
    init,
    setFilters,
    label,
    dictionary,
    material,
  };
});

// td模块已同步渠道素材
export const useTDMediaLibraryStore = defineStore(STORE_KEY.CREATIVE.TD_MEDIA_LIBRARY, () => {
  const filters = ref();
  const { isLoading } = useLoading();
  const CURREN_LIBRARY = LibraryType.Media;
  const setFilters = (filter: any) => {
    filters.value = filter;
  };
  const dictionary = useDictionary(CURREN_LIBRARY);
  const material = useMediaMaterialList();
  const init = async (formatType = 0) => {
    material.setFormatType(formatType);
    useWatchGameChange(async () => {
      dictionary.initDictionary(); // 不展示目录，用于获取预览接口的token
      material.resetSearch(); // 重置筛选条件
      material.update();
    });
  };
  return { isLoading, init, setFilters, dictionary, material };
});

export default useMediaLibraryStore;
