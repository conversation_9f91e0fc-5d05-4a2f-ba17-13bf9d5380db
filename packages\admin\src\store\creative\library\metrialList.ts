import { getMaterialList, getMediaMaterialList } from 'common/service/creative/library/get-material-list';
import type { TMaterialReqParam, TDMaterialReqParam } from 'common/service/creative/library/get-material-list';
import { computed, ref, Ref, reactive, toRefs, watch } from 'vue';
import { LibraryType } from 'common/service/creative/library/type';
import { useFetchWrapper } from 'common/compose/request/request';
import { METRIALLIST_CONDITION } from './const';
import type { IMetrialListCondition } from './type';
import { useGlobalGameStore } from '@/store/global/game.store';
import { get } from '@vueuse/core';

export function useMaterialList(libraryType: LibraryType, activeFolderId: Ref<string>) {
  const gameStore = useGlobalGameStore();
  const pageInfo = reactive({
    pageNum: METRIALLIST_CONDITION.pageNum,
    pageSize: METRIALLIST_CONDITION.pageSize,
  });

  const isMediaLibrary = libraryType === 'advertise';

  function setPageInfo(newPageNum: number, newPageSize: number) {
    pageInfo.pageNum = newPageSize === pageInfo.pageSize ? newPageNum : 1;
    pageInfo.pageSize = newPageSize;
  }

  function setFormatType(size: number) {
    setPageInfo(1, pageInfo.pageSize); // 重新设置类型，回到第一页material
    params.format_type = size;
    update();
  }

  const searchList = ref([
    { text: 'AssetName', field: 'asset_names', condition: [] },
  ]);

  function resetSearch() {
    searchList.value = [{ text: 'AssetName', field: 'asset_names', condition: [] }];
    setCondition(METRIALLIST_CONDITION);
  }

  const directoryId = computed(() => (isMediaLibrary
    ? activeFolderId.value === gameStore.gameCode
      ? '' : activeFolderId.value : activeFolderId.value || ''));

  watch(() => directoryId.value, (val, oldValue) => {
    params.directory_id = val;
    if (val !== oldValue) {
      setPageInfo(1, pageInfo.pageSize);
      update();
    }
  });

  const allMaterialMap = ref<Record<string, any>>({});

  function setAllMaterialMap(value?: { list: any, total: any }) {
    if (!value) return;
    const temp = get(allMaterialMap);
    value?.list.forEach((i: any) => {
      temp[i.id] = i;
    });
    allMaterialMap.value = temp;
  }

  const params: TMaterialReqParam = {
    text: computed(() => {
      if (METRIALLIST_CONDITION.text) return METRIALLIST_CONDITION.text;
      const { condition } = searchList.value[0];
      return condition.length > 0 ? condition.join('\n') : undefined;
    }),
    count: computed(() => pageInfo.pageSize),
    directory_id: directoryId.value,
    list_type: libraryType,
    offset: computed(() => pageInfo.pageSize * (pageInfo.pageNum - 1)),
    page: computed(() => pageInfo.pageNum - 1),
    with_detail: 1,
    filter_online_status: METRIALLIST_CONDITION.filteronlinestatus,
    online_status: METRIALLIST_CONDITION.onlineStatus,
    search_type: METRIALLIST_CONDITION.searchType,
    labels: METRIALLIST_CONDITION.labels,
    labels_search_type: METRIALLIST_CONDITION.labelsSearchType,
    names: computed(() => {
      if (METRIALLIST_CONDITION.names) return METRIALLIST_CONDITION.names;
      const { condition } = searchList.value[0];
      return condition.length > 0 ? JSON.stringify(condition) : undefined;
    }),
    format_type: undefined,
    format_type_list: [],
    callback(res) {
      setAllMaterialMap(res);
    },
  };


  const { data: materialList, loading, emit: update, setStorage } = useFetchWrapper(
    getMaterialList,
    params,
    {
      storage: `getMaterialList-${libraryType}`,
      throttle: 200,
      reactive: true,
      immediately: !!activeFolderId.value,
      storageType: params => (params.page === 0 ? 'localStorage' : 'sessionStorage'),
      trailing: true,
    },
  );

  const resetAllMaterialMap = () => {
    allMaterialMap.value = {};
  };
  // syncedStatus
  // syncMedia

  function setCondition(newCondition: IMetrialListCondition) {
    params.text = newCondition.text as any;
    params.filter_online_status = newCondition.filteronlinestatus;
    params.online_status = newCondition.onlineStatus;
    params.search_type = newCondition.searchType;
    params.labels = newCondition.labels;
    params.labels_search_type = newCondition.labelsSearchType;
    params.names = newCondition.names as any;
    params.sync_media_filter = newCondition.syncedStatus || 0;
    params.sync_media_list = newCondition.syncedStatus === 2 ? newCondition.syncMedia : [];
    params.format_type_list = newCondition.formatTypeList || [];
    if (newCondition.uploadCloudTime?.[0] && newCondition.uploadCloudTime?.[1]) {
      [params.start_create_date, params.end_create_date] = newCondition.uploadCloudTime;
    } else {
      params.start_create_date = '';
      params.end_create_date = '';
    }
    setPageInfo(newCondition.pageNum, newCondition.pageSize);
    update();
  }

  const resetNameAndText = () => {
    params.text = computed(() => {
      const { condition } = searchList.value[0];
      return condition.length > 0 ? condition.join('\n') : undefined;
    });
    params.names = computed(() => {
      const { condition } = searchList.value[0];
      return condition.length > 0 ? JSON.stringify(condition) : undefined;
    });
  };

  return {
    materialList: computed(() => materialList.value?.list || []),
    currentAllMaterialList: computed(() => Object.values(get(allMaterialMap))),
    total: computed<number>(() => materialList.value?.total || 0),
    loading,
    ...toRefs(pageInfo),
    params,
    update,
    setPageInfo,
    setCondition,
    setFormatType,
    searchList,
    resetSearch,
    resetNameAndText,
    resetAllMaterialMap,
    setStorage,
  };
}

export function useMediaMaterialList() {
  const formatType = ref<number>(0); // // 0 全部，1 视频 2 图片
  function setFormatType(size: number) {
    setPageInfo(1, pageInfo.pageSize); // 重新设置类型，回到第一页
    formatType.value = size;
  }

  const pageInfo = reactive({ pageNum: 1, pageSize: 48 });

  function setPageInfo(newPageNum: number, newPageSize: number) {
    pageInfo.pageNum = newPageSize === pageInfo.pageSize ? newPageNum : 1;
    pageInfo.pageSize = newPageSize;
  }

  const searchList = ref([
    { text: 'AssetName', field: 'asset_names', condition: [] },
  ]);

  function resetSearch() {
    searchList.value = [{ text: 'AssetName', field: 'asset_names', condition: [] }];
  }

  const params: TDMaterialReqParam = {
    account_id: '',
    count: computed(() => pageInfo.pageSize),
    format_type: computed(() => formatType.value),
    game_code: '',
    media: 1,
    offset: computed(() => pageInfo.pageSize * (pageInfo.pageNum - 1)),
    keyword: computed(() => (searchList.value[0].condition.length === 1
      ? searchList.value[0].condition[0] : undefined)), // 模糊搜索，只有1个文本才传
    asset_names: computed(() => (searchList.value[0].condition.length > 1
      ? searchList.value[0].condition : undefined)), // asset_names精确搜素
    asset_ratios: [],
  };

  function setParams(newParams: {
    accountId?: string,
    gameCode?: string,
    media?: number,
    assetRatios?: string[],
  }) {
    if (newParams.accountId) params.account_id = newParams.accountId;
    if (newParams.gameCode) params.game_code = newParams.gameCode;
    if (newParams.media) params.media = newParams.media;
    if (newParams.assetRatios) params.asset_ratios = newParams.assetRatios;
  }

  const { data: materialList, loading, emit: update, setStorage } = useFetchWrapper(
    getMediaMaterialList,
    params,
    {
      throttle: 200,
      reactive: true,
      immediately: false,
    },
  );

  return {
    materialList: computed(() => materialList.value?.list || []),
    total: computed<number>(() => materialList.value?.total || 0),
    loading,
    update,
    ...toRefs(pageInfo),
    setPageInfo,
    setFormatType,
    searchList,
    resetSearch,
    setParams,
    setStorage,
  };
}
