import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { ref } from 'vue';
import { TDriverType } from '@/views/creative/library/compose/driver-const';
import { Dropbox } from 'dropbox';
import { getDropboxToken } from 'common/service/creative/auth/dropbox';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useAuthStageStore } from '@/store/global/auth.store';
import { getGoogleToken } from 'common/service/creative/auth/google';

export const usePortalStore = defineStore(STORE_KEY.CREATIVE.PORTAL, () => {
  // geme code
  const { gameCode } = useGlobalGameStore();
  const driveStatus = ref<string>('');
  // Token Info
  const arthubToken = ref<string>('');
  const arthubCode = ref<string>('');
  const googleDriverToken = ref<{
    accessToken: string;
    authUser: string;
  }>({ accessToken: '', authUser: '' });
  const dropboxToken = ref<{
    accessToken: string;
    pathRoot: string;
  }>({ accessToken: '', pathRoot: '' });

  const updateDropboxToken = async () => {
    const tokenInfo = await getDropboxToken(gameCode);
    if (!tokenInfo.access_token) {
      throw new Error('auth err! please try again');
    }
    dropboxToken.value.accessToken = tokenInfo?.access_token;
    dropboxToken.value.pathRoot = JSON.stringify({ '.tag': 'root', root: tokenInfo?.name_space_id });
    return tokenInfo?.access_token;
  };

  const updateGoogleToken = async () => {
    const tokenInfo = await getGoogleToken(gameCode, 'drive');
    if (!tokenInfo.accessToken) {
      throw new Error('auth err! please try again');
    }
    googleDriverToken.value.accessToken = tokenInfo.accessToken;
    googleDriverToken.value.authUser = tokenInfo.authUser;
    return tokenInfo;
  };

  // dropbox
  const dropboxInstance = ref<Dropbox>();
  const initDropbox = async () => {
    if (!dropboxToken.value?.accessToken) {
      await updateDropboxToken();
    }
    dropboxInstance.value = new Dropbox({
      accessToken: dropboxToken.value.accessToken,
      pathRoot: dropboxToken.value.pathRoot,
    });
    return dropboxInstance.value;
  };

  const getDropbox = async () => {
    if (dropboxInstance.value) {
      return dropboxInstance.value;
    }
    return await initDropbox();
  };

  type TStep = 0 | 1 | 2 | 3;
  // step control
  const initStepValue: TStep = 0;
  const step = ref<TStep>(initStepValue);
  const nextStep = () => {
    step.value = Math.min(3, step.value + 1) as TStep;
  };
  const initStep = (val: TStep = initStepValue) => {
    step.value = val;
  };

  // driver select
  const supportDriver: TDriverType[] = ['dropbox', 'googledriver', 'arthub'];
  const currentDriver = ref<TDriverType>(supportDriver[0]);

  // email info
  const { currentUser } = useAuthStageStore();
  // 默认通知人是自己
  const notifyEmail = ref([currentUser]);

  // directory_id
  const directoryId = ref<string>();

  return {
    googleDriverToken,
    dropboxToken,
    step,
    nextStep,
    initStep,
    supportDriver,
    currentDriver,
    arthubToken,
    arthubCode,
    notifyEmail,
    getDropbox,
    updateDropboxToken,
    updateGoogleToken,
    directoryId,
    driveStatus,
  };
});
