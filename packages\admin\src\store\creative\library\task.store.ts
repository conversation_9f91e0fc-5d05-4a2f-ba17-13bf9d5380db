import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { computed, reactive, ref, toRefs } from 'vue';
import { useLoading } from 'common/compose/loading';
import {
  cancelUpload as cancelUploadService,
  getUploadMediaTask,
  getUploadMediaTaskOptions,
  getUploadMediaTaskStat,
  resumeUpload as resumeUploadService,
} from 'common/service/creative/library/get-aix-task';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useTips } from 'common/compose/tips';
import { UPLOAD_STATUS } from '@/views/creative/library/compose/const';
import type { ITaskCondition, ITaskStat } from './type';
import { DEFAULT_TASK_LIST_CONDITION } from './const';
import { cloneDeep } from 'lodash-es';
import { useUrlDefault } from 'common/compose/url-default';
import { useFetchWrapper } from 'common/compose/request/request';
import { getAutomaticSyncTaskRule } from 'common/service/creative/rules/config';

const { success } = useTips();

export const useCreativeAixTaskStore = defineStore(STORE_KEY.CREATIVE.TASK, () => {
  // game
  const game = computed(() => useGlobalGameStore().gameCode);
  // 表格数据拉取次数 只在第一次拉取的时候loading 后面轮询不loading
  const requestCount = ref<number>(0);
  // 状态统计
  const taskStatList = ref<ITaskStat[]>([]);
  // 任务批次
  const taskBatchList = ref<any[]>([]);
  // 筛选条件
  const condition = reactive<ITaskCondition>(useUrlDefault(cloneDeep(DEFAULT_TASK_LIST_CONDITION)));
  const { isLoading, showLoading, hideLoading } = useLoading();
  const {
    isLoading: isOperateLoading,
    showLoading: showOperateLoading,
    hideLoading: hideOperateLoading,
  } = useLoading();
  // 勾选的表格数据的id
  const checkedIdList = ref<string[]>([]);

  const { data: taskListRes, emit: emitTaskList } = useFetchWrapper<any, any>(
    getUploadMediaTask,
    {
      __game: computed(() => game.value),
      channel: computed(() => condition.channel),
      count: computed(() => condition.pageSize),
      directories: computed(() => condition.directories),
      format_type: computed(() => condition.formatType),
      page: computed(() => condition.pageIndex - 1),
      status: computed(() => condition.status),
      text: computed(() => condition.text),
      isAutoTask: computed(() => condition.taskType !== 'all'),
      autoTaskRuleId: computed(() => (condition.taskType === 'auto' ? condition.ruleId : '0')),
      dateType: computed(() => condition.dateType),
      startTime: computed(() => `${condition.dateRange[0]} 00:00:00`),
      endTime: computed(() => `${condition.dateRange[1]} 24:00:00`),
    },
    {
      storage: 'getUploadMediaTask',
      storageType: params => (params.page === 0 ? 'localStorage' : 'sessionStorage'),
    },
  );

  // 表格数据源
  const taskList = computed(() => taskListRes.value?.list || []);
  // 总条数
  const pageTotal = computed(() => taskListRes.value?.total || 0);

  const updateTaskList = async () => {
    showLoading();
    await emitTaskList();
    hideLoading();
  };

  const getTaskBatchList = async () => {
    taskBatchList.value = Object.freeze(await getUploadMediaTaskOptions()) as any[];
  };

  const ruleList = ref<{ label: string; value: string }[]>([]);
  const getAutoRuleList = async () => {
    const res = await getAutomaticSyncTaskRule({
      game_code: game.value,
      page: 0,
      page_size: 100,
    });
    ruleList.value = res.task_rules.map(rule => ({
      label: rule.name,
      value: rule.id!,
    }));
  };


  // 分页事件
  async function setPages(newPageIndex: number, newPageSize: number) {
    condition.pageIndex = newPageIndex;
    if (newPageSize !== condition.pageSize) {
      condition.pageIndex = 1;
    }
    condition.pageSize = newPageSize;
    // init();
    await updateTaskList();
  }

  // 重新上传
  const resumeUpload = async (idList: string[]) => {
    showOperateLoading();
    const res: any = await resumeUploadService({ task_ids: idList });
    hideOperateLoading();
    if (res.ret) return;
    await success('Sync task has been reactivated.');
    await init();
  };

  // 取消上传
  const cancelUpload = async (idList: string[]) => {
    showOperateLoading();
    const res: any = await cancelUploadService({ task_ids: idList });
    hideOperateLoading();
    if (res.ret) return;
    await success('Upload canceled');
    await init();
  };

  // 判断 每一列的checkbox是否禁用
  const isDisabledRow = (row: any) => {
    const { operations } = UPLOAD_STATUS[row.status];
    if (!operations) {
      return true;
    }
    return !operations?.includes('cancel') && !operations?.includes('resume');
  };

  // 表格复选框的勾选
  const onSelectChange = (selectedRowKeys: string[]) => {
    checkedIdList.value = selectedRowKeys;
  };

  // 从勾选的id里面 拿到表格数据里面要重新上传的取消上传的有多少条
  const canBatch = (list: string[], operation: 'cancel' | 'resume') => {
    const result: string[] = [];
    list.forEach((id) => {
      const item = taskList.value.find((row: any) => row.id === id);
      if (item && UPLOAD_STATUS[item.status]?.operations?.includes(operation)) {
        result.push(id);
      }
    });
    return result;
  };

  // 批量重试的id列表
  const batchResumeList = computed(() => canBatch(checkedIdList.value, 'resume'));
  // 批量取消的id列表
  const batchCancelList = computed(() => canBatch(checkedIdList.value, 'cancel'));
  // 获取状态统计
  const getMediaTaskStat = async () => {
    const res: any = await getUploadMediaTaskStat({
      __game: game.value,
      isAutoTask: condition.taskType !== 'all',
      autoTaskRuleId: condition.taskType === 'auto' ? condition.ruleId : '0',
      dateType: condition.dateType,
      startTime: `${condition.dateRange[0].toString()} 00:00:00`,
      endTime: `${condition.dateRange[1].toString()} 24:00:00`,
      text: condition.text,
      format_type: condition.formatType,
      status: condition.status,
      channel: condition.channel,
      directories: condition.directories,
    });
    taskStatList.value = res?.list || [];
  };

  const statics = computed(() => {
    const success = Number(taskStatList.value.find(i => i.status === 2)?.count) || 0;
    const failed = Number(taskStatList.value.find(i => i.status === 3)?.count) || 0;
    const canceled = Number(taskStatList.value.find(i => i.status === 4)?.count) || 0;
    const total = success + failed + canceled;
    return {
      success,
      failed,
      canceled,
      total,
    };
  });

  // 重新设置搜索条件
  const setCondition = async (newCondition: ITaskCondition) => {
    Object.keys(condition)
      .forEach((key) => {
        condition[key] = newCondition[key];
      });
    requestCount.value = 0;
    await updateTaskList();
    hideLoading();
    requestCount.value += 1;
  };

  // 初始化
  const init = async () => {
    requestCount.value = 0;
    console.log('start fetch task');
    await Promise.all([
      getTaskBatchList(),
      setCondition(useUrlDefault(cloneDeep(DEFAULT_TASK_LIST_CONDITION))),
      getMediaTaskStat(),
      getAutoRuleList()]);
    requestCount.value += 1;
    if (isLoading.value) hideLoading();
  };

  return {
    init,
    updateTaskList,
    setPages,
    resumeUpload,
    cancelUpload,
    isDisabledRow,
    onSelectChange,
    getMediaTaskStat,
    setCondition,
    pageTotal,
    ...toRefs(condition),
    taskList,
    ruleList,
    isLoading: computed(() => isLoading.value),
    taskStatList,
    batchResumeList,
    batchCancelList,
    requestCount,
    taskBatchList,
    isOperateLoading,
    statics,
  };
});
