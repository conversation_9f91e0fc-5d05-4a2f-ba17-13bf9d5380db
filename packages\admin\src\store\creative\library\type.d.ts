import { StringKeyAnyValueObject } from 'common/types/report';

export interface ITaskStat {
  status: number,
  count:  string,
}

export interface ITaskCondition extends StringKeyAnyValueObject {
  pageIndex: number,
  pageSize: number,
  directories: string[],
  // text: null | string,
  text: string[],
  channel: null | string,
  formatType: null | string,
  status: null | string,
  dateType: 'start_date' | 'synced_data',
  taskType: 'auto' | 'manual' | 'all',
  dateRange: [ string, string ],
  ruleId?: string,
}

export interface IMetrialListCondition extends StringKeyAnyValueObject {
  pageSize: number,
  pageNum: number,
  text: string,
  filteronlinestatus: number,
  onlineStatus: string | null,
  searchType: number,
  labels: any[],
  names?: string,
  labelsSearchType: number,
  syncedStatus: number,
  syncMedia: number[],
  formatTypeList: number[], // 0 other 1 video 2 image 4 html
  uploadCloudTime: any[], //  string 2021-01-10
}


export interface ISyncNode {
  nodeId: string,
  syncStatus: 1 | 2,
  fullPathName?: string,
  name?: string,
  syncTime?: string,
  updateTime?: string,
}
