import { useGlobalGameStore } from '@/store/global/game.store';
import { defineStore, storeToRefs } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { useLocalStorage } from '@vueuse/core';
import { OptionData } from 'tdesign-vue-next';
import { CACHE_UNIQUE_ID_MAP, CACHE_CONCEPT_NAME_MAP } from './const';


export const useCreativeNameGeneratorCacheStore = defineStore(STORE_KEY.CREATIVE.NAME_GENERATOR.CACHE, () => {
  const gameStore = useGlobalGameStore();
  const { gameCode } = storeToRefs(gameStore);

  const cacheUniqueIdMap = useLocalStorage<Record<string, OptionData[]>>(CACHE_UNIQUE_ID_MAP, {});
  const appendToCacheUniqueIdMap = (record: OptionData) => {
    if (!cacheUniqueIdMap.value[gameCode.value]) {
      cacheUniqueIdMap.value[gameCode.value] = [];
    }
    if (!cacheUniqueIdMap.value[gameCode.value].some(item => item.value === record.value)) {
      cacheUniqueIdMap.value[gameCode.value] = [...cacheUniqueIdMap.value[gameCode.value], record];
    }
  };
  const clearCurrentUniqueId = () => {
    cacheUniqueIdMap.value[gameCode.value] = [];
  };

  const cacheConceptNameMap = useLocalStorage<Record<string, OptionData[]>>(CACHE_CONCEPT_NAME_MAP, {});
  const appendConceptNameMap = (record: OptionData) => {
    if (!cacheConceptNameMap.value[gameCode.value]) {
      cacheConceptNameMap.value[gameCode.value] = [];
    }
    if (!cacheConceptNameMap.value[gameCode.value].some(item => item.value === record.value)) {
      cacheConceptNameMap.value[gameCode.value] = [...cacheConceptNameMap.value[gameCode.value], record];
    }
  };
  const clearCurrentConceptName = () => {
    cacheConceptNameMap.value[gameCode.value] = [];
  };

  return {
    appendToCacheUniqueIdMap,
    cacheUniqueIdMap,
    cacheConceptNameMap,
    appendConceptNameMap,
    clearCurrentConceptName,
    clearCurrentUniqueId,
  };
});
