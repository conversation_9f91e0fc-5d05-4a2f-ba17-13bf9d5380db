import type { INameRefactorOptions } from 'common/service/creative/name-generator/type';
import { CREATIVE_FORMAT_TYPE } from 'common/service/creative/name-generator/const';
import { TTaskTListFormValue } from './type';
import dayjs  from 'dayjs';

export const FILE_MIME_TYPE = {
  VIDEO: [
    'video/mp4', // .mp4
    'video/x-ms-wmv', // .wmv
    'video/x-msvideo', // .avi
    'video/quicktime', // .mov
    'video/x-flv', // .flv
    'video/x-matroska', // .mkv
    'video/mpeg', // .mpeg
    'video/3gpp', // .3gp
    'video/mpeg', // .mpg
  ],
  IMAGE: [
    'image/vnd.adobe.photoshop', // .psd
    'image/tga', // .tga
    'image/vnd.ms-dds', // .dds
    'image/tiff', // .tif
    'image/tiff', // .tiff
    'image/jpeg', // .jpg
    'image/jpeg', // .jpeg
    'image/png', // .png
    'image/bmp', // .bmp
    'image/gif', // .gif
  ],
  HTML: [
    'text/html', // HTML 文档
    // 'application/xhtml+xml', // XHTML 文档
  ],
};


export const DEFAULT_CREATIVE_NAME_FULE = {
  original_name: '',
  game_name: '',
  unique_id: '',
  upload_month: dayjs().format('MMYY'),
  asset_type: '',
  concept_name: '',
  version_number: '',
  version_name: '',
  production_stage: '',
  creative_type: '',
  language: '',
  duration: '',
  ratio: '',
  dimensions: '',
  source: '',
  target: '',
  producer: '',
  uploader: '',
  type_specifics: '',
  playable_channel: '',
};

export const DEFAULT_TABLE_DROPDOWN_OPTIONS: INameRefactorOptions = {
  concept_name: [],
  unique_id: [],
  asset_type: [],
  production_stage: [],
  creative_type: [],
  source: [],
  target: [],
  producer: [],
  type_specifics: [],
  playable_channel: [],
  game_name: [],
};


export const ASSET_NAME_RECORD_SOST_KEYS = [
  'game_name',
  'unique_id',
  'upload_month',
  'asset_type',
  'concept_name',
  'version_number',
  'version_name',
  'production_stage',
  'creative_type',
  'language',
  'duration_render',
  'ratio_dimensions_render',
];


export const ASSET_TYPE_VALUE = {
  [CREATIVE_FORMAT_TYPE.VIDEO]: 'V',
  [CREATIVE_FORMAT_TYPE.IMAGE]: 'B',
  [CREATIVE_FORMAT_TYPE.HTML]: 'P',
  [CREATIVE_FORMAT_TYPE.OTHER]: 'O',
};


export const CACHE_UNIQUE_ID_MAP = 'creative_name_generator_table_unique_id_map';
export const CACHE_CONCEPT_NAME_MAP = 'creative_name_generator_table_concept_name_map';


export const EXTENDS_PARENT_VALUE_FIELDS = [
  'game_name',
  'unique_id',
  'upload_month',
  'asset_type',
  'concept_name',
  'version_number',
  'version_name',
  'production_stage',
  'creative_type',
  'source',
  'target',
  'producer',
  // 'uploader',
  'type_specifics',
  'playable_channel',
];


// 素材层级的字段
export const ASSET_LEVEL_FIELDS  = [
  'language',
  'duration_render',
  'ratio_dimensions_render',
];


export const TASK_LIST_DEFAULT_FORM_VALUE: TTaskTListFormValue = {
  dateInfo: {
    dateType: 'start_date',
    dateRange: [dayjs().subtract(6, 'day')
      .format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
  },
  assetName: [],
  conceptNames: [],
  assetTypes: [],
  uploaders: [],
  status: [],
  originalName: [],
};


export const TASK_LIST_PAGE_INFO = {
  pageIndex: 1,
  pageSize: 10,
};
