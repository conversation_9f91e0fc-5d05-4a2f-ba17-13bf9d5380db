import { computed, ref } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { Dropbox } from 'dropbox';
import { useGlobalGameStore } from '@/store/global/game.store';
import { getDropboxLatestToken } from 'common/service/creative/auth/dropbox';
import type { files } from 'dropbox/types/index';
import { getRootIdNoCache } from 'common/service/creative/library/get-dictionary-list';
import { TreeOptionData } from 'tdesign-vue-next';

export const useCreativeNameGeneratorDropboxStore = defineStore(STORE_KEY.CREATIVE.NAME_GENERATOR.DROPBOX, () => {
  const gameStore = useGlobalGameStore();
  const { gameCode } = storeToRefs(gameStore);

  // dropbox 授权给素材库的根目录的id
  const dropboxRootFolderId = ref<string>('');
  // 根目录名称
  const dropboxRootPathLower = ref<string>('');
  const dropboxRootPathDisplay = ref<string>('');

  // 选择的目录路径
  const selectedDropboxPathLower = ref<string>('');
  const setSelectedDropboxPathLower = (val: string) => selectedDropboxPathLower.value = val;
  const selectedDropboxPathDisplay = ref<string>('');
  const setSelectedDropboxPathDisplay = (val: string) => selectedDropboxPathDisplay.value = val;
  const selectedDropboxFolderId = ref<string>('');
  const setSelectedDropboxFolderId = (val: string) => selectedDropboxFolderId.value = val;

  const folderTreeList = ref<TreeOptionData[]>([]);
  const setFolderTreeList = (val: TreeOptionData[]) => folderTreeList.value = [...val];

  // dropbox 是否已经授权
  const isDropboxAuthorized = ref(false);
  // dropbox未授权的弹窗
  const dropboxAuthExpiredDialogVisible = ref(false);
  const setDropboxAuthExpiredDialogVisible = (val: boolean) => dropboxAuthExpiredDialogVisible.value = val;

  // 初始化dropbox
  const dropboxInstance = ref<Dropbox>();
  const initDropboxInstance = async () => {
    const dropboxTokenInfo = await getDropboxLatestToken(gameCode.value);
    // console.log('res', dropboxTokenInfo);
    if (!dropboxTokenInfo?.access_token) {
      setDropboxAuthExpiredDialogVisible(true);
    }
    dropboxInstance.value = new Dropbox({
      accessToken: dropboxTokenInfo.access_token,
      pathRoot: JSON.stringify({ '.tag': 'root', root: dropboxTokenInfo?.name_space_id }),
      customHeaders: {
        'Content-Type': 'text/plain; charset=dropbox-cors-hack',
      },
    });
  };

  // 获取授权给 素材的目录的id
  const getDropboxRootFolderId =  async () => {
    const res = await getRootIdNoCache();
    if (res?.depot_id && `${res?.type}` === '3' &&  res?.cloud_drive_status === 'active') {
      dropboxRootFolderId.value = `id:${res.depot_id}`;
      isDropboxAuthorized.value = true;
    } else {
      setDropboxAuthExpiredDialogVisible(true);
    }
  };

  // 根据路径获取获取这个目录下的子目录
  const getDropboxChildFolderListByPath = async (path: string): Promise<files.FolderMetadataReference[]> => {
    let res = await dropboxInstance.value!.filesListFolder({ path });
    let folderList = [...res.result.entries];

    if (res.result.has_more) {
      res = await dropboxInstance.value!.filesListFolderContinue({ cursor: res.result.cursor });
      folderList = folderList.concat(res.result.entries);
    }
    return folderList.filter(item => item['.tag'] === 'folder');
  };

  // 获取根目录信息
  const getRootFolderInfo = async () => {
    const res =  await dropboxInstance.value!.filesGetMetadata({ path: dropboxRootFolderId.value });
    const rootInfo = res.result as files.FolderMetadataReference;
    dropboxRootPathLower.value = rootInfo.path_lower || '';
    dropboxRootPathDisplay.value = rootInfo.path_display || '';
    return rootInfo;
  };

  // 清除选中
  const clearPrevSelectedDirInfo = () => {
    setSelectedDropboxPathLower('');
    setSelectedDropboxPathDisplay('');
    setSelectedDropboxFolderId('');
  };


  return {
    initDropboxInstance: () => Promise.all([initDropboxInstance(), getDropboxRootFolderId()]),
    dropboxInstance: computed(() => dropboxInstance.value),
    dropboxAuthExpiredDialogVisible: computed(() => dropboxAuthExpiredDialogVisible.value),
    setDropboxAuthExpiredDialogVisible,
    dropboxRootFolderId: computed(() => dropboxRootFolderId.value),
    folderTreeList: computed(() => folderTreeList.value),
    getRootFolderInfo,
    setFolderTreeList,
    getDropboxChildFolderListByPath,
    dropboxRootPathDisplay: computed(() => dropboxRootPathDisplay.value),
    dropboxRootPathLower: computed(() => dropboxRootPathLower.value),
    setSelectedDropboxPathLower,
    selectedDropboxPathDisplay: computed(() => selectedDropboxPathDisplay.value),
    selectedDropboxPathLower: computed(() => selectedDropboxPathLower.value),
    setSelectedDropboxPathDisplay,
    selectedDropboxFolderId: computed(() => selectedDropboxFolderId.value),
    setSelectedDropboxFolderId,
    clearPrevSelectedDirInfo,
    isDropboxAuthorized: computed(() => isDropboxAuthorized.value),
  };
});
