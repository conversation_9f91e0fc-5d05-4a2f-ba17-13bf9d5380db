import { computed, ref } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import { STORE_KEY } from '@/config/config';
import type {
  IRenderAssetNameRecord, IUploadAssetNameRecord,
  TImageFileInfo, TVideoFileInfo,
} from './type';
import { useCreativeNameGeneratorIndexedDB } from './indexed-db';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useCreativeNameGeneratorRealTimeUploadStore } from './realtime-upload.store';
import { useLoading } from 'common/compose/loading';
import {
  hashFileSHA256, getImageFileInfo, getVideoFileInfo, generateAssetName,
  renderDimensions, renderDuration, composeCreativeFileList,
} from './utils';
import dayjs from 'dayjs';
import {
  DEFAULT_CREATIVE_NAME_FULE, DEFAULT_TABLE_DROPDOWN_OPTIONS, ASSET_TYPE_VALUE,
  EXTENDS_PARENT_VALUE_FIELDS, ASSET_LEVEL_FIELDS,
} from './const';
import { groupBy, cloneDeep, omit, pick, isNumber } from 'lodash-es';
import { getOptionsService, updateAssetUploadStatusService } from 'common/service/creative/name-generator';
import {
  CREATIVE_FORMAT_TYPE,
  CREATIVE_UPLOAD_STATUS,
} from 'common/service/creative/name-generator/const';
import type {
  INameRefactorOptions,
  IAssetRecordMeta, TCreativeFormatType,
} from 'common/service/creative/name-generator/type';
import { v4 as uuidv4 } from 'uuid';
import { useCreativeNameGeneratorDropboxStore } from './dropbox.store';
import { useCreativeNameGeneratorCacheStore } from './cache.store';
import { useAuthStageStore } from '@/store/global/auth.store';


export const useCreativeNameGeneratorStore = defineStore(STORE_KEY.CREATIVE.NAME_GENERATOR.INDEX, () => {
  const gameStore = useGlobalGameStore();
  const { gameCode } = storeToRefs(gameStore);
  const  useRealTimeUploadStore = useCreativeNameGeneratorRealTimeUploadStore();
  const {  setRealTimeTaskAssetList, startExecuteRealTimeTask, clearRealTimeTaskTaskDatas } = useRealTimeUploadStore;
  const { connectIndexedDB, select, insert, remove, update } = useCreativeNameGeneratorIndexedDB(gameCode);

  const dropboxStore = useCreativeNameGeneratorDropboxStore();
  const { initDropboxInstance, clearPrevSelectedDirInfo } = dropboxStore;
  const { dropboxInstance, isDropboxAuthorized } = storeToRefs(dropboxStore);

  const authStore = useAuthStageStore();

  const creativeNameGeneratorCacheStore = useCreativeNameGeneratorCacheStore();
  const { clearCurrentUniqueId, clearCurrentConceptName } = creativeNameGeneratorCacheStore;
  const { cacheUniqueIdMap } = storeToRefs(creativeNameGeneratorCacheStore);

  /** --------------------------- loading 开始 ---------------------------- */
  const { isLoading: isSaveFileToIDBLoaing, showLoading: showSaveFileToIDBLoading, hideLoading: hideSaveFileToIDBLoaing } =  useLoading();
  const { isLoading: isRemoveIDBFileLoaing, showLoading: showRemoveIDBFileLoading, hideLoading: hideRemoveIDBFileLoaing } =  useLoading();
  const { isLoading: isInitLoaing, showLoading: showInitLoading, hideLoading: hideInitLoaing } =  useLoading();
  const { isLoading: isUpdateIDBFileLoaing, showLoading: showUpdateIDBFileLoading, hideLoading: hideUpdateIDBFileLoaing } =  useLoading();
  const { isLoading: isReadyRealTimeTaskLoading, showLoading: showReadyRealTimeTaskLoading, hideLoading: hideReadyRealTimeTaskLoading } =  useLoading();
  /** --------------------------- loading 结束 ---------------------------- */

  /** --------------------------- 对话框的显示和隐藏 开始 ---------------------------- */
  // 实时上传的弹窗
  const realTimeUploadDialogVisible = ref(false);
  const setRealTimeUploadDialogVisible = (val: boolean) => realTimeUploadDialogVisible.value = val;
  // 选择目录的弹窗
  const chooseFolderDialogVisible = ref(false);
  const setChooseFolderDialogVisible = (val: boolean) => chooseFolderDialogVisible.value = val;
  // 没有选择目录的提示弹窗
  const notChooseFolderTipDialogVisible = ref<boolean>(false);
  const setNotChooseFolderTipDialogVisible = (val: boolean) => notChooseFolderTipDialogVisible.value = val;
  // 未完成任务的弹窗
  const unfinishedTaskDialogVisible = ref(false);
  const setUnfinishedTaskDialogVisible = (val: boolean) => unfinishedTaskDialogVisible.value = val;
  // 历史文件弹窗
  const historyFileTipsDialogVisible = ref(false);
  const setHistoryFileTipsDialogVisible = (val: boolean) => historyFileTipsDialogVisible.value = val;
  /** --------------------------- 对话框的显示和隐藏 结束 ---------------------------- */

  /** -----------------------------------------  页面状态管理 开始 --------------------------------------- */

  // 表格列中的下拉框 的数据源
  const tableDropdownOptions = ref<INameRefactorOptions>(cloneDeep(DEFAULT_TABLE_DROPDOWN_OPTIONS));
  const groupByGameDetail = computed(() => groupBy(tableDropdownOptions.value.game_name ?? [], 'aix_game_code'));
  // 当前游戏的简写
  const currentGameLogogram = computed(() => groupByGameDetail.value[gameCode.value]?.[0]?.game_code ?? '');
  // 未完成的实时上传任务
  const unfinishedRealTimeTaskList = ref<IRenderAssetNameRecord[]>([]);
  const setUnfinishedRealTimeTaskList = (val: IRenderAssetNameRecord[]) => unfinishedRealTimeTaskList.value = [...val];


  const dropboxAccessToken = ref<string>('');
  // dropbox
  /** -----------------------------------------  页面状态管理 开始 --------------------------------------- */

  const creativeFileList = ref<IRenderAssetNameRecord[]>([]);
  const tableDataList = ref<IRenderAssetNameRecord[]>([]);

  /** -----------------------------------------  IndexedDB操作 开始 --------------------------------------- */
  const selectCreativeFile = async () => {
    const res = await select();
    creativeFileList.value = res as IRenderAssetNameRecord[];
    tableDataList.value = composeCreativeFileList([...creativeFileList.value]);
    if (tableDataList.value.length === 0) {
      clearPrevSelectedDirInfo();
      clearCurrentUniqueId();
      clearCurrentConceptName();
    }
  };

  const removeCreativeFile = async (data: { id: number, parentId: number}) => {
    const { parentId, id: deleteId } = data;
    showRemoveIDBFileLoading();
    const currentTypeFiles =  await select({ parentId, isInUploadTask: false });
    // 是否需要删除分组行
    const isNeedDeleteGroupRow = currentTypeFiles.length <= 1;
    // 分组行及其数据一起删除
    if (isNeedDeleteGroupRow) {
      await remove(Object.values(data));
    } else {
      await remove([deleteId]);
    }
    await selectCreativeFile();
    if (!tableDataList.value.length) {
      clearRealTimeTaskTaskDatas();
    }
    hideRemoveIDBFileLoaing();
  };

  const addCreativeFile = async (list: IUploadAssetNameRecord[], uniqueIdMap: Record<string, number>) => {
    showSaveFileToIDBLoading();
    // map 一把， 先把文件分组类型的到
    const currentTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
    const creativeAddList: Omit<IRenderAssetNameRecord, 'id'>[] = [];

    const getRecord = (formatType: TCreativeFormatType) => {
      const uniqueId = uniqueIdMap[formatType];
      return {
        unique_id: uniqueId,
        latest_unique_id: uniqueId,
        game_name: currentGameLogogram.value,
        uploader: authStore.currentUser,
        front_record_uuid: uuidv4(),
        createTime: currentTime,
        game_code: gameCode.value,
        asset_type: ASSET_TYPE_VALUE[formatType],
      };
    };

    for (const item of list) {
      const { format_type: formatType }  = item;
      const fileHash = await hashFileSHA256(item.fileObject);
      const assetRecordMeta: IAssetRecordMeta = {};
      // 图片文件获取宽高
      if (formatType === CREATIVE_FORMAT_TYPE.IMAGE) {
        const imageInfo = await getImageFileInfo(item.fileObject);
        if ((imageInfo as TImageFileInfo).height) {
          const { height, width } = imageInfo as TImageFileInfo;
          assetRecordMeta.image_height = height;
          assetRecordMeta.image_width = width;
        }
      }
      // 视频文件获取宽高， 还有时长, 这里目前只对mp4的做处理
      if (formatType === CREATIVE_FORMAT_TYPE.VIDEO) {
        const fileInfoRes = await getVideoFileInfo(item.fileObject);
        const videoInfo = (fileInfoRes as TVideoFileInfo);
        if (videoInfo.duration) assetRecordMeta.video_duration = videoInfo.duration;
        if (videoInfo.videoHeight) assetRecordMeta.video_height = videoInfo.videoHeight;
        if (videoInfo.videoWidth) assetRecordMeta.video_width = videoInfo.videoWidth;
      }
      const durationValue = renderDuration({ format_type: formatType, ...assetRecordMeta });
      const ratioDimensionsValue = renderDimensions({ format_type: formatType, ...assetRecordMeta });

      creativeAddList.push({
        ...item,
        ...assetRecordMeta,
        format_type: formatType,
        isGroupBy: false,
        file_hash: fileHash,
        ratio_dimensions_render: ratioDimensionsValue,
        duration_render: isNumber(durationValue) ? `${Math.round(durationValue)}S` : durationValue,
        isExtractDurationFail: formatType === CREATIVE_FORMAT_TYPE.VIDEO && !durationValue,
        isExtractRatioDimensionsFail: formatType !== CREATIVE_FORMAT_TYPE.OTHER && !ratioDimensionsValue,
        ...getRecord(item.format_type),
        upload_month: dayjs().format('MMYY'),
        isInUploadTask: false,
      });
    }
    const groupByCreativeAddList = groupBy(creativeAddList, 'format_type');
    // 获取分组行
    const getGroupByRecordByFormatType = async (formatType: TCreativeFormatType) => {
      // db中存在的文件分组类型
      const dbFileGroupByList = await select({ isGroupBy: true, format_type: formatType });
      return dbFileGroupByList?.[0];
    };
    const creativefileGroupTypeList = Object.keys(groupByCreativeAddList) as TCreativeFormatType[];
    // 更新parentId
    const updateParentId = (idList: number[], parentId: number) => update(idList.map(id => ({ id, parentId })));

    // 按照format_type 进行遍历
    for (const formatTypeItem of creativefileGroupTypeList) {
      // 先获取分组行
      const groupByRecord = await getGroupByRecordByFormatType(formatTypeItem);

      let insertList = groupByCreativeAddList[formatTypeItem];

      // 如果有分组行， 说明之前已经新增过，该类型的素材了，
      if (groupByRecord) {
        insertList = insertList.map(item => ({
          ...item,
          ...pick(groupByRecord, [...EXTENDS_PARENT_VALUE_FIELDS]),
        }));
      }
      // 按照分组的类型， 分批次写入到indexedDB
      const insertedIdList =  await insert(insertList);

      // 判断分组类型是否已经存下， 如果已经存在, 执行修改
      if (groupByRecord) {
        // 把parentId更新一把
        await updateParentId(insertedIdList, groupByRecord.id);
      } else {
        // 新增 分组的item
        const [id] = await insert([{
          ...omit(DEFAULT_CREATIVE_NAME_FULE, ['unique_id']),
          isGroupBy: true,
          format_type: formatTypeItem,
          ...getRecord(formatTypeItem),
        }]);
        // 把parentId更新一把
        await updateParentId(insertedIdList, id);
      }
    }
    // 最后再捞一把
    await selectCreativeFile();
    hideSaveFileToIDBLoaing();
  };

  const updateCreativeFile = async (parentId: number, { key, value }: { key: string, value: any}) => {
    const updateRecord = { [key]: value };
    showUpdateIDBFileLoading();
    if (ASSET_LEVEL_FIELDS.includes(key)) {
      await update([{ id: parentId, ...updateRecord }]);
    } else {
      const childList = await select({ parentId });
      const updateList = childList.map(item => ({ id: item.id, ...updateRecord }));
      await update([
        ...updateList,
        { id: parentId, ...updateRecord },
      ]);
    }
    await selectCreativeFile();
    hideUpdateIDBFileLoaing();
  };
  /** -----------------------------------------  IndexedDB操作 结束 -------------------------  -------------- */


  const getTableCellOptions = async () => {
    tableDropdownOptions.value = await getOptionsService(gameCode.value);
  };


  /** -----------------------------------------  上传逻辑 开  始 --------------------------------------- */

  // 上传到网盘
  const uploadToCloudDrive = async (uploadDirPath: string) => {
    showReadyRealTimeTaskLoading();
    /**
     * 1. 从idb存储的记录中，把分组头和历史数据排除掉（历史上传成功，失败，或者已经取消掉）
     * 顺便拿到分组行， 进入任务队列要删除
     */
    // 根据唯一id的数值，去找到他对应的编码
    const getUniqueIdCode = (uniqueId: number) => {
      const fullUniqueIdList = [
        ...(cacheUniqueIdMap?.value[gameCode.value] ?? []),
        ...tableDropdownOptions.value.unique_id,
      ];
      const groupByUniqueIdList = groupBy(fullUniqueIdList, 'value');
      return groupByUniqueIdList[`${uniqueId}`]?.[0]?.label ?? '';
    };

    const assetGroupByRows = creativeFileList.value.filter(item => item.isGroupBy);
    const assetFileRecordList = creativeFileList.value.filter(item => (
      !item.isInUploadTask && !item.isGroupBy
    )).map((item) => {
      const uniqueIdCode = getUniqueIdCode(item.unique_id);
      return {
        ...item, isInUploadTask: true,
        upload_status: CREATIVE_UPLOAD_STATUS.WAITING,
        upload_path: uploadDirPath,
        asset_name: generateAssetName({ ...item, unique_id_code: uniqueIdCode }),
        unique_id_code: uniqueIdCode,
      };
    });

    setRealTimeUploadDialogVisible(true);

    /**
      * 2. 前端的文件列表标记成“1已进入任务队列” 同时， 删除分组行
      * 然后刷新页面的数据
      */
    try {
      await Promise.all([
        update(assetFileRecordList.map(item => ({
          id: item.id, isInUploadTask: true,
          upload_status: CREATIVE_UPLOAD_STATUS.WAITING,
          upload_path: uploadDirPath,
          asset_name: item.asset_name,
          unique_id_code: item.unique_id_code,
        }))),
        remove(assetGroupByRows.map(item => item.id)),
      ]);
      // 然后再更新列表
      await selectCreativeFile();
      hideReadyRealTimeTaskLoading();
      setRealTimeTaskAssetList([...assetFileRecordList]);
      startExecuteRealTimeTask(dropboxInstance.value!);
    } catch (error) {
      console.error('Error occurred during database operations:', error);
      hideReadyRealTimeTaskLoading();
    }
  };

  /** -----------------------------------------  上传逻辑 结束 --------------------------------------- */

  const getUnfinishedRealTimeTask = async () => {
    // 1. 先从indexdb 中读取， 判断是否
    const fullList = await select({ isInUploadTask: true, isGroupBy: false });
    const unfinishedTaskList = fullList.filter((item) => {
      const statusIsPending = ([
        CREATIVE_UPLOAD_STATUS.ERROR,
        CREATIVE_UPLOAD_STATUS.UPLOADING,
        CREATIVE_UPLOAD_STATUS.CANCELLED,
        CREATIVE_UPLOAD_STATUS.WAITING,
      ] as string[]).includes(item.upload_status!);
      return  item.fileObject && statusIsPending;
    });
    setUnfinishedRealTimeTaskList(unfinishedTaskList);
  };
  // 执行 上次未完成的任务
  const runUnfinishedTaskRealTimeTasks = async () => {
    if (unfinishedRealTimeTaskList.value.length < 1) return;
    setRealTimeTaskAssetList(unfinishedRealTimeTaskList.value.map((item) => {
      const { upload_status: currentStatus } = item;
      let newStatus = currentStatus;
      // 如果状态是“上传中”，说明上次上传的过程中被中断了， 得改成“等待中”,
      if (newStatus === CREATIVE_UPLOAD_STATUS.UPLOADING) {
        newStatus = CREATIVE_UPLOAD_STATUS.WAITING;
      }
      return {
        ...item, asset_name: item.asset_name!,
        unique_id_code: item.unique_id_code!,
        upload_status: newStatus,
      };
    }));
    setRealTimeUploadDialogVisible(true);
    startExecuteRealTimeTask(dropboxInstance.value!);
  };
  // 取消未完成的任务
  const cancelUnfinishedTaskRealTimeTasks = async () => {
    if (unfinishedRealTimeTaskList.value.length < 1) return;
    const taskList: Promise<any>[] = [];
    unfinishedRealTimeTaskList.value.forEach((record) => {
      taskList.push(update([{ id: record.id, upload_status: CREATIVE_UPLOAD_STATUS.CANCELLED, fileObject: null }]));
      if (record.taskRecord) {
        taskList.push(updateAssetUploadStatusService({ id: record.taskRecord.id, upload_status: CREATIVE_UPLOAD_STATUS.CANCELLED }));
      }
    });
    await Promise.allSettled(taskList);
  };

  // 清空文件记录
  const clearCreativeFileList = async () => {
    await remove(creativeFileList.value.map(item => item.id));
    await selectCreativeFile();
  };

  const init = async () => {
    showInitLoading();
    clearPrevSelectedDirInfo();
    await Promise.all([connectIndexedDB(), initDropboxInstance()]);
    await Promise.all([
      getUnfinishedRealTimeTask(),
      selectCreativeFile(),
      getTableCellOptions(),
    ]);
    if (tableDataList.value.length > 0) {
      setHistoryFileTipsDialogVisible(true);
    }
    if (unfinishedRealTimeTaskList.value.length > 0 && isDropboxAuthorized.value) {
      // 打开未完成的任务的弹窗
      setUnfinishedTaskDialogVisible(true);
    }
    hideInitLoaing();
  };


  return {
    init,
    creativeFileList,
    tableDataList: computed(() => tableDataList.value),
    addCreativeFile,
    removeCreativeFile,
    selectCreativeFile,
    updateCreativeFile,
    // loading 开始
    isSaveFileToIDBLoaing: computed(() => isSaveFileToIDBLoaing.value),
    isInitLoaing: computed(() => isInitLoaing.value),
    isRemoveIDBFileLoaing: computed(() => isRemoveIDBFileLoaing.value),
    isUpdateIDBFileLoaing: computed(() => isUpdateIDBFileLoaing.value),
    isReadyRealTimeTaskLoading: computed(() => isReadyRealTimeTaskLoading.value),
    // loading 结束
    tableDropdownOptions: computed(() => tableDropdownOptions.value),
    // 页面上用到的数据
    initDropboxInstance,
    dropboxAccessToken: computed(() => dropboxAccessToken.value),
    uploadToCloudDrive,
    // 弹窗显隐控制
    realTimeUploadDialogVisible: computed(() => realTimeUploadDialogVisible.value),
    setRealTimeUploadDialogVisible,
    chooseFolderDialogVisible: computed(() => chooseFolderDialogVisible.value),
    setChooseFolderDialogVisible,
    notChooseFolderTipDialogVisible: computed(() => notChooseFolderTipDialogVisible.value),
    setNotChooseFolderTipDialogVisible,
    unfinishedTaskDialogVisible: computed(() => unfinishedTaskDialogVisible.value),
    setUnfinishedTaskDialogVisible,
    historyFileTipsDialogVisible: computed(() => historyFileTipsDialogVisible.value),
    setHistoryFileTipsDialogVisible,
    cancelUnfinishedTaskRealTimeTasks,
    runUnfinishedTaskRealTimeTasks,
    currentGameLogogram,
    clearCreativeFileList,
  };
});
