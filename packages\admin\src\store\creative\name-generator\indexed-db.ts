/**
 * indexdDB 存储file对象
 */
import { INDEXED_DB_CONFIG } from '@/config/config';
import { ref, computed  } from 'vue';
import type { ComputedRef, Ref } from 'vue';
import { IRenderAssetNameRecord } from './type';
import { omit } from 'lodash-es';


export function useCreativeNameGeneratorIndexedDB(gameCode: ComputedRef<string> | Ref<string>) {
  const currentGameCode = computed(() => gameCode.value);

  const { DB_NAME: dbName, TABLE: tables, VERSION: indexdDBVersion } = INDEXED_DB_CONFIG.AIX_CREATIVE;
  const tableName = tables.CREATIVE_NAME_GENERATOR;

  const indexdDBInstance = ref<IDBDatabase>();

  // 打开数据库， 不存在会自动创建
  const connectIndexedDB = ()  => new Promise((resolve, reject) => {
    const request =  window.indexedDB.open(dbName, indexdDBVersion);
    // 成功的回调
    request.onsuccess = function (event) {
      indexdDBInstance.value = (event.target as IDBOpenDBRequest).result;
      resolve(indexdDBInstance.value);
    };
    request.onupgradeneeded = function (event) {
      const dbInstance = (event.target as IDBOpenDBRequest).result;
      if (!dbInstance.objectStoreNames.contains(tableName)) {
        // 建标，默认使用indexDB 生成的id
        const objectStore =  dbInstance.createObjectStore(tableName, { keyPath: 'id', autoIncrement: true });
        // 所属的游戏
        objectStore.createIndex('aixWebGameCodeIndex', 'game_code', { unique: false });
        objectStore.createIndex('originalNameIndex', 'original_name', { unique: false });
        objectStore.createIndex('formatTypeIndex', 'format_type', { unique: false });
        objectStore.createIndex('isGroupByIndex', 'isGroupBy', { unique: false });
      }
    };
    request.onerror = function (event) {
      reject(event);
      console.error(`连接dbName: ${dbName}, version: ${indexdDBVersion} 时出错`, event);
    };
  });

  // 新增
  const insert = (list: Omit<IRenderAssetNameRecord, 'id'>[]): Promise<number[]> => {
    const transaction = indexdDBInstance.value!.transaction([tableName], 'readwrite');
    const objectStore = transaction.objectStore(tableName);
    // 返回写入成功后的数据
    const insertedIdList: number[] = [];
    list.forEach((row) => {
      const insertRequest = objectStore.add({
        ...row,
      });
      insertRequest.onsuccess = (event) => {
        const insertedItem = (event.target as IDBOpenDBRequest).result as unknown;  // 获取插入记录的 ID
        insertedIdList.push(insertedItem as  number);
      };
    });
    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => {
        resolve(insertedIdList);
      };
      transaction.onerror = (event) => {
        reject(event);
      };
    });
  };

  // 查询
  const select = (where?: Partial<IRenderAssetNameRecord>): Promise<IRenderAssetNameRecord[]> => {
    const transaction = indexdDBInstance.value!.transaction([tableName], 'readonly'); // 创建一个只读事务
    const objectStore = transaction.objectStore(tableName); // 获取对象存储
    const aixWebGameCodeIndex = objectStore.index('aixWebGameCodeIndex'); // 获取索引
    const request = aixWebGameCodeIndex.getAll(currentGameCode.value);
    return new Promise((resolve, reject) => {
      request.onsuccess = (event) => {
        const reseult = ((event.target as IDBOpenDBRequest).result ?? []) as unknown as  IRenderAssetNameRecord[];
        const whereKeys = Object.keys(where ?? {});
        // 没有筛选条件的情况下。 直接返回
        if (!whereKeys.length)  resolve(reseult);
        const filerResult = reseult.filter(item => whereKeys.every(key => (where as Record<string, any>)[key] === item[key as keyof typeof item]));
        resolve(filerResult);
      };
      request.onerror = (event) => {
        console.log('event', event);
        reject(event);
      };
    });
  };

  // 删除
  const remove = (idList: number[]) => {
    const transaction = indexdDBInstance.value!.transaction([tableName], 'readwrite'); // 创建一个只读事务
    const objectStore = transaction.objectStore(tableName); // 获取对象存储
    idList.forEach((id) => {
      const deleteRequest = objectStore.delete(id); // 删除指定 ID 的数据
      deleteRequest.onsuccess = () => {
        console.log(`ID ${id} 的数据删除成功`);
      };

      deleteRequest.onerror = (event) => {
        console.error(`ID ${id} 的数据删除失败:`, event);
      };
    });
    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => {
        resolve(true);
      };
      transaction.onerror = (event) => {
        reject(event);
      };
    });
  };

  // 更新
  const update = (list: ({id: number} & Partial<Omit<IRenderAssetNameRecord, 'id'>>)[]) => {
    const transaction = indexdDBInstance.value!.transaction([tableName], 'readwrite'); // 创建一个只读事务
    const objectStore = transaction.objectStore(tableName); // 获取对象存储
    list.forEach((item) => {
      const getRequest = objectStore.get(item.id); // 先获取现有记录
      getRequest.onsuccess = (event) => {
        const currentRowData = (event.target as IDBOpenDBRequest).result;
        if (currentRowData) {
          const newRowData = {
            ...currentRowData,
            ...omit(item, ['id']),
          };
          const updateRequest = objectStore.put(newRowData); // 更新记录
          updateRequest.onsuccess = () => {
            console.log(`ID ${item.id} 的数据更新成功`);
          };
          updateRequest.onerror = (event) => {
            console.error(`ID ${item.id} 的数据更新失败:`, event);
          };
        } else {
          console.error(`ID ${item.id} 的记录不存在`);
        }
      };
    });
    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => {
        resolve(true);
      };
      transaction.onerror = (event) => {
        reject(event);
      };
    });
  };

  return {
    connectIndexedDB,
    indexdDBInstance: computed(() => indexdDBInstance.value),
    insert,
    select,
    remove,
    update,
  };
}
