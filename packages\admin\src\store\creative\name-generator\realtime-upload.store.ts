import { defineStore, storeToRefs } from 'pinia';
import { ref, computed } from 'vue';
import { STORE_KEY } from '@/config/config';
import { IRealTimeTaskAssetRecord } from './type';
import { useCreativeNameGeneratorIndexedDB } from './indexed-db';
import { useGlobalGameStore } from '@/store/global/game.store';
import { updateAssetUploadStatusService, createAssetNameRecordService } from 'common/service/creative/name-generator/index';
import { CREATIVE_UPLOAD_STATUS } from 'common/service/creative/name-generator/const';
import type { TCreativeUploadStatus } from 'common/service/creative/name-generator/type';
import { uploadToDropbox, getFileExtension } from './utils';
import { Dropbox } from 'dropbox';
import { useLoading } from 'common/compose/loading';


export const useCreativeNameGeneratorRealTimeUploadStore = defineStore(STORE_KEY.CREATIVE.NAME_GENERATOR.REALTIME_UPLOAD, () => {
  // lodiang部分
  const {
    isLoading: isExecuteRealTimeTaskLoading,
    hideLoading: hideExecuteRealTimeTaskLoading,
    showLoading: shwoExecuteRealTimeTaskLoading,
  } = useLoading();

  const {
    isLoading: isBreakRealTimeTaskLoading,
    hideLoading: hideBreakRealTimeTaskLoading,
    showLoading: shwoBreakRealTimeTaskLoading,
  } = useLoading();

  const gameStore = useGlobalGameStore();
  const { gameCode } = storeToRefs(gameStore);
  const { update, connectIndexedDB  } = useCreativeNameGeneratorIndexedDB(gameCode);

  // 页面关闭前的提示
  const handlePageBeforeClose = (event: BeforeUnloadEvent) => {
    const e = event || window.event;
    if (e) {
      e.returnValue = 'Confirm Close? Exiting now will ​​stop the upload​​ and ​​reset the generator​​.';
    }
  };

  // 实时上传的列表
  const realTimeTaskAssetList = ref<IRealTimeTaskAssetRecord[]>([]);
  const setRealTimeTaskAssetList = (val: IRealTimeTaskAssetRecord[]) => {
    console.log('setRealTimeTaskAssetList', val);
    realTimeTaskAssetList.value = [...val];
  };
  const updateRealTimeTaskAssetItem = async (id: number, value: Partial<Omit<IRealTimeTaskAssetRecord, 'id'>>) => {
    const list: IRealTimeTaskAssetRecord[] = realTimeTaskAssetList.value.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          ...(value || {}),
        };
      }
      return item;
    });
    setRealTimeTaskAssetList(list);
    await update([{ id, ...value }]);
  };
  // 已经取消了的任务队列
  const cancelledRealTimeTaskIdtList = ref<number[]>([]);

  const clearRealTimeTaskTaskDatas = () => {
    setRealTimeTaskAssetList([]);
    cancelledRealTimeTaskIdtList.value = [];
  };

  // 是否中断实时上传的任务
  const isBreakRealTimeTask = ref(false);
  const setIsBreakRealTimeTask = (val: boolean) => isBreakRealTimeTask.value = val;
  // 终止上传
  const breakRealTimeTask = async () => {
    shwoBreakRealTimeTaskLoading();
    setIsBreakRealTimeTask(true);
    // 要取消的有哪些？
    const batchCancelTask: any[] = [];
    for (const realTimeTaskAssetRecord of realTimeTaskAssetList.value) {
      const { id, taskRecord, upload_status: uploadStatus } = realTimeTaskAssetRecord;
      // 只有等待上传的 状态的， 才能取消
      if (uploadStatus !== CREATIVE_UPLOAD_STATUS.WAITING) continue;
      batchCancelTask.push(updateRealTimeTaskAssetItem(id, { upload_status: CREATIVE_UPLOAD_STATUS.CANCELLED, fileObject: null! }));
      if (taskRecord) {
        batchCancelTask.push(updateAssetUploadStatusService({ id: taskRecord.id, upload_status: CREATIVE_UPLOAD_STATUS.CANCELLED }));
      }
    }
    await Promise.allSettled(batchCancelTask);
    hideBreakRealTimeTaskLoading();
  };

  // 往表里写一条记录
  const createTaskRecordToDb = async (realTimeAssetRecord: IRealTimeTaskAssetRecord & {
    upload_success_detail?: string, upload_error_detail?: string
  }) => {
    try {
      const { upload_path: uploadPath, file_hash: fileHash } = realTimeAssetRecord;
      const res = await createAssetNameRecordService([{
        ...realTimeAssetRecord,
        upload_path: uploadPath!,
        file_hash: fileHash!,
      }]);
      return res?.[0];
    } catch (err) {
      return undefined;
    }
  };

  // 上传到网盘后到回调
  const uploadDropBoxCallback = async (
    realTimeTaskAssetRecord: IRealTimeTaskAssetRecord,
    newState: {
      upload_success_detail?: string, upload_error_detail?: string, upload_status: TCreativeUploadStatus
    },
  ) => {
    const {  taskRecord, id } = realTimeTaskAssetRecord;
    const { upload_status: newUploadStatus } = newState;
    // 状态为成功时，清除缓存中的文件
    const isClearFileObj = newUploadStatus === CREATIVE_UPLOAD_STATUS.SUCCESS;
    // 这里要区分之前是否已经存在过记录
    if (taskRecord) {
      // 执行更新
      const res = await updateAssetUploadStatusService({ id: taskRecord.id, ...newState });
      const updatedRecord = res?.list?.[0];
      // 说明更新失败了
      if (!updatedRecord) {
        await updateRealTimeTaskAssetItem(id, { upload_status: CREATIVE_UPLOAD_STATUS.ERROR });
      } else {
        await updateRealTimeTaskAssetItem(id, {
          upload_status: newUploadStatus, taskRecord: updatedRecord,
          ...(isClearFileObj ? { fileObject: null! } : {}),
        },
        );
      }
    } else {
      // 执行创建
      const res = await createTaskRecordToDb({
        ...realTimeTaskAssetRecord,
        ...newState,
      });
      // 成功的情况
      if (res) {
        await updateRealTimeTaskAssetItem(id, {
          upload_status: newUploadStatus, taskRecord: res,
          ...(isClearFileObj ? { fileObject: null! } : {}),
        },
        );
      } else {
        // 出错了
        await updateRealTimeTaskAssetItem(id, { upload_status: CREATIVE_UPLOAD_STATUS.ERROR });
      }
    }
  };

  // 开始执行实时上传的任务，走串行，因为走并行的话，请求发出去就无法终止了
  const startExecuteRealTimeTask = async (dropboxInstance: Dropbox) => {
    shwoExecuteRealTimeTaskLoading();
    await connectIndexedDB();
    for (const realTimeTaskAssetRecord of realTimeTaskAssetList.value) {
      const {
        id, original_name: originalName,
        fileObject, upload_status: uploadStatus, upload_path: uploadPath, asset_name: assetName,
      } = realTimeTaskAssetRecord;
      // 判断是否要终止任务
      if (isBreakRealTimeTask.value) break;
      // 判断是否要跳过这一项
      // 已经成功了的。和已经取消了的， 不需要上传
      const notNeedUploadStatusList: string[] = [
        CREATIVE_UPLOAD_STATUS.CANCELLED, CREATIVE_UPLOAD_STATUS.SUCCESS,
        CREATIVE_UPLOAD_STATUS.UPLOADING,
      ];
      const isSkip = notNeedUploadStatusList.includes(uploadStatus!) || cancelledRealTimeTaskIdtList.value.includes(id);
      if (isSkip) continue;
      // 本地记录的状态先改为上传中
      await updateRealTimeTaskAssetItem(id, { upload_status: CREATIVE_UPLOAD_STATUS.UPLOADING });

      const newAssetName = assetName + getFileExtension(originalName);
      // 执行上传的操作
      await uploadToDropbox({
        dropboxInstance,
        fileData: new File([fileObject!], newAssetName),
        uploadDirPath: uploadPath!,
        success: async (res) => {
          console.log('上传成功', res);
          await uploadDropBoxCallback(realTimeTaskAssetRecord, {
            upload_status: CREATIVE_UPLOAD_STATUS.SUCCESS,
            upload_success_detail: JSON.stringify(res),
            upload_error_detail: '',
          });
        },
        error: async  (err) => {
          console.log('上传失败', err);
          await uploadDropBoxCallback(realTimeTaskAssetRecord, {
            upload_status: CREATIVE_UPLOAD_STATUS.ERROR,
            upload_error_detail: JSON.stringify(err),
          });
        },
      });
    }
    hideExecuteRealTimeTaskLoading();
  };

  // 取消任务， 开正在上传的不能取消
  const cancelTask = async (record: IRealTimeTaskAssetRecord) => {
    const { id } = record;
    cancelledRealTimeTaskIdtList.value.push(id);
    await uploadDropBoxCallback(record, {
      upload_status: CREATIVE_UPLOAD_STATUS.CANCELLED,
    });
  };

  // 重新上传
  const retryTask = async (dropboxInstance: Dropbox, record: IRealTimeTaskAssetRecord) => {
    const {
      fileObject, upload_path: uploadPath, id,
      asset_name: assetName, original_name: originalName,
    } = record;
    await updateRealTimeTaskAssetItem(id, { upload_status: CREATIVE_UPLOAD_STATUS.UPLOADING });
    const newAssetName = assetName + getFileExtension(originalName);
    await uploadToDropbox({
      dropboxInstance,
      fileData: new File([fileObject!], newAssetName),
      uploadDirPath: uploadPath!,
      success: async (res) => {
        console.log('重新上传成功', res);
        await uploadDropBoxCallback(record, {
          upload_status: CREATIVE_UPLOAD_STATUS.SUCCESS,
          upload_success_detail: JSON.stringify(res),
          upload_error_detail: '',
        });
      },
      error: async (err) => {
        console.log('重新上传失败', err);
        await uploadDropBoxCallback(record, {
          upload_status: CREATIVE_UPLOAD_STATUS.ERROR,
          upload_error_detail: JSON.stringify(err),
        });
      },
    });
  };


  return {
    realTimeTaskAssetList,
    isBreakRealTimeTask: computed(() => isBreakRealTimeTask.value),
    isExecuteRealTimeTaskLoading: computed(() => isExecuteRealTimeTaskLoading.value),
    isBreakRealTimeTaskLoading: computed(() => isBreakRealTimeTaskLoading.value),
    cancelTask,
    retryTask,
    setRealTimeTaskAssetList,
    startExecuteRealTimeTask,
    breakRealTimeTask,
    clearRealTimeTaskTaskDatas,
    handlePageBeforeClose,
  };
});
