import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { computed, ref } from 'vue';
import { useLoading } from 'common/compose/loading';
import { getTaskListService, getTaskListOptionsService } from 'common/service/creative/name-generator';
import { useGlobalGameStore } from '@/store/global/game.store';
import { cloneDeep } from 'lodash-es';
import { useFetchWrapper } from 'common/compose/request/request';
import type { OptionData } from 'tdesign-vue-next';
import { ITaskListOptions } from 'common/service/creative/name-generator/type';
import { TASK_LIST_DEFAULT_FORM_VALUE, TASK_LIST_PAGE_INFO } from './const';
import { TTaskTListFormValue } from './type';

export const useNameGeneratorTaskStore = defineStore(STORE_KEY.CREATIVE.NAME_GENERATOR_TASK, () => {
  // Game information
  const gameStore = useGlobalGameStore();
  const gameCode = computed(() => gameStore.gameCode);

  // Filter conditions
  const formModelValue  = ref<TTaskTListFormValue>(cloneDeep(TASK_LIST_DEFAULT_FORM_VALUE));
  const updateFormModelValue = (val: TTaskTListFormValue) => formModelValue.value = { ...val };

  const pageInfo = ref({ ...TASK_LIST_PAGE_INFO });


  // Loading for filter options only
  const { isLoading: filterOptionsLoading, showLoading: showFilterOptionsLoading, hideLoading: hideFilterOptionsLoading } = useLoading(false);

  // Available options for filters
  const conceptNameOptions = ref<OptionData[]>([]);
  const uploaderOptions = ref<OptionData[]>([]);
  const assetTypeOptions = ref<OptionData[]>([]);
  const uploadStatusOptions = ref<Record<string, OptionData[]>>({});

  const queryTaskListPayload = computed(() => ({
    game_code: gameCode.value,
    page_index: pageInfo.value.pageIndex,
    page_size: pageInfo.value.pageSize,
    date_type: formModelValue.value.dateInfo.dateType,
    date: formModelValue.value.dateInfo.dateRange,
    asset_name: formModelValue.value.assetName,
    creator: formModelValue.value.uploaders,
    upload_status: formModelValue.value.status,
    original_name: formModelValue.value.originalName,
    concept_name: formModelValue.value.conceptNames,
    asset_type: formModelValue.value.assetTypes,
  }));

  // Wrap API call with fetch wrapper for caching and error handling
  const { data: taskListRes, emit: emitTaskList, loading: mainListLoading } = useFetchWrapper<any, any>(getTaskListService, {
    game_code: computed(() => queryTaskListPayload.value.game_code),
    page_index: computed(() => queryTaskListPayload.value.page_index),
    page_size: computed(() => queryTaskListPayload.value.page_size),
    date_type: computed(() => queryTaskListPayload.value.date_type),
    date: computed(() => queryTaskListPayload.value.date),
    asset_name: computed(() => queryTaskListPayload.value.asset_name),
    creator: computed(() => queryTaskListPayload.value.creator),
    upload_status: computed(() => queryTaskListPayload.value.upload_status),
    original_name: computed(() => queryTaskListPayload.value.original_name),
    concept_name: computed(() => queryTaskListPayload.value.concept_name),
    asset_type: computed(() => queryTaskListPayload.value.asset_type),
  });

  // Table data
  const taskList = computed(() => taskListRes.value?.list || []);

  // Total records for pagination
  const pageTotal = computed(() => {
    if (taskListRes.value?.count !== undefined) {
      return taskListRes.value.count;
    }
    return 0;
  });

  // Fetch filter options from backend
  const fetchTaskListOptions = async () => {
    try {
      showFilterOptionsLoading();
      const res: ITaskListOptions = await getTaskListOptionsService(gameCode.value);
      conceptNameOptions.value = (res.concept_name || []).map((item: OptionData) => ({
        label: item.label ?? '',
        value: item.value ?? '',
      }));
      uploaderOptions.value = (res.creator || []).map((item: OptionData) => ({ label: item.label ?? '', value: item.value ?? '' }));
      assetTypeOptions.value = (res.asset_type || []).map((item: OptionData) => ({ label: item.label ?? '', value: item.value ?? '' }));
      uploadStatusOptions.value = res.upload_status ?? {};
    } catch (e) {
      conceptNameOptions.value = [];
      uploaderOptions.value = [];
      assetTypeOptions.value = [];
    } finally {
      hideFilterOptionsLoading();
    }
  };

  // Handle pagination
  const setPages = async (newPageIndex: number, newPageSize: number) => {
    pageInfo.value.pageSize = newPageSize;
    pageInfo.value.pageIndex = newPageSize !== pageInfo.value.pageSize ? 1 : newPageIndex;
    await emitTaskList();
  };


  // Initialize the store
  const init = async () => {
    await Promise.all([fetchTaskListOptions(), emitTaskList()]);
  };

  // isLoading is true if either main list or filter options are loading
  const isLoading = computed(() => mainListLoading.value || filterOptionsLoading.value);

  return {
    init,
    setPages,
    pageTotal,
    taskList,
    conceptNameOptions,
    uploaderOptions,
    assetTypeOptions,
    uploadStatusOptions,
    isLoading,
    formModelValue,
    updateFormModelValue,
    getTaskList: emitTaskList,
    pageInfo,
    queryTaskListPayload,
  };
});
