import type {
  ICreateAssetNameRecord, TCreativeUploadStatus, ICreativeAssetNameRecord,
  ICreativeAssetNameRecord
} from 'common/service/creative/name-generator/type';
import type { files, DropboxResponse } from 'dropbox/types/index.d';
import { Dropbox } from 'dropbox';


// upload 上传时的字段
export interface IUploadAssetNameRecord extends Omit<
ICreateAssetNameRecord,
'file_hash' | 'version_number' |
 'upload_path' | 'upload_error_detail' | 'upload_success_detail'
> {
  fileObject: File,
  version_number: string,
  asset_name?: string,
  uploader: string
}

// 下划线的 会传给接口。 驼峰的不会
export interface IRenderAssetNameRecord extends Omit<IUploadAssetNameRecord, 'fileObject'> {
  createTime: string,
  game_code: string,
  updateTime?: string,
  id: number,
  isGroupBy: boolean,
  fileObject?: File,
  parentId?: number
  file_hash?: string,
  // 是否已经进入上传的队列
  isInUploadTask?: boolean,
  // 上传状态， 进入上传队列后才会有
  upload_status?: TCreativeUploadStatus,
  upload_path?: string,
  taskRecord?: ICreativeAssetNameRecord,
  latest_unique_id?: number,
  unique_id_code?: string,
  // 提起时长失败
  isExtractDurationFail?: boolean,
  // 提取尺寸失败
  isExtractRatioDimensionsFail?: boolean
}


export type TUploadToDropbox<TError = any> = {
  success?: (res: DropboxResponse<files.FileMetadata>) => void,
  error?: (err: TError) => void,
  uploadDirPath: string,
  fileData: File,
  dropboxInstance: Dropbox
}

export interface   IRealTimeTaskAssetRecord extends IRenderAssetNameRecord {
  asset_name: string,
  unique_id_code: string,
}


export type TGetFileMetaError =  { isError: true, error: unknown }

export type TImageFileInfo = { width: number, height: number }

export type TVideoFileInfo = {
  videoWidth: number,
  videoHeight: number,
  aspectWidth: number,
  aspectHeight: number,
  duration: number
}

export type TTaskTListFormValue = {
  dateInfo: {
    dateType: 'start_date' | 'synced_date',
    dateRange: string[]
  },
  assetName: string[];
  conceptNames: string[];
  assetTypes: string[];
  uploaders: string[];
  status: string[];
  originalName: string[];
}
