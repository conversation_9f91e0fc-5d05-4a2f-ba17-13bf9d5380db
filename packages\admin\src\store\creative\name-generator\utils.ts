import { ASSET_NAME_RECORD_SOST_KEYS, FILE_MIME_TYPE } from  './const';
import { CREATIVE_FORMAT_TYPE } from 'common/service/creative/name-generator/const';
import type { TCreativeFormatType } from 'common/service/creative/name-generator/type';
import type {
  TImageFileInfo, TUploadToDropbox,
  TVideoFileInfo, TGetFileMetaError,
  IRenderAssetNameRecord,
} from './type';
import { groupBy, isUndefined } from 'lodash-es';

export function getFormatType(file: File)  {
  const mimeKeys = Object.keys(FILE_MIME_TYPE);
  let fileGroupType: TCreativeFormatType = CREATIVE_FORMAT_TYPE.OTHER;
  for (let i = 0; i <= mimeKeys.length - 1; i++) {
    const mimeList = FILE_MIME_TYPE[mimeKeys[i] as keyof typeof FILE_MIME_TYPE];
    if (mimeList.includes(file.type)) {
      fileGroupType = CREATIVE_FORMAT_TYPE[mimeKeys[i] as keyof typeof CREATIVE_FORMAT_TYPE];
      continue;
    }
  }
  return fileGroupType;
};

// 获取文件的hash值
export async function hashFileSHA256(file: File) {
  const arrayBuffer = await file.arrayBuffer();
  const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  return hashHex;
}

// 上传到dropbox
export async function uploadToDropbox(params: TUploadToDropbox) {
  const { success, error, fileData, uploadDirPath, dropboxInstance } = params;
  try {
    const dripath = uploadDirPath.endsWith('/') ? uploadDirPath : `${uploadDirPath}/`;
    const res = await dropboxInstance.filesUpload({
      path: dripath + fileData.name,
      contents: fileData,
    });
    await success?.(res);
  } catch (e) {
    await error?.(e);
  }
}

/** *
 * 获取时视频文件的宽高和比例
 */
export async function getVideoFileInfo(fileData: File): Promise<TVideoFileInfo | TGetFileMetaError> {
  return new Promise((resolve) => {
    // 获取上传的视频的宽高
    const videoUrl = URL.createObjectURL(fileData);
    const videoObj = document.createElement('video');
    try {
      videoObj.onloadedmetadata = function () {
        URL.revokeObjectURL(videoUrl);
        const { videoWidth, videoHeight, duration } = videoObj;
        // 计算最大公约数
        const divisor = gcd(videoWidth, videoHeight);
        const aspectWidth = videoWidth / divisor;
        const aspectHeight = videoHeight / divisor;
        resolve({
          videoWidth,
          videoHeight,
          aspectWidth,
          aspectHeight,
          duration,
        });
      };
      videoObj.src = videoUrl;
      videoObj.load();
    } catch (error) {
      resolve({ isError: true, error });
    }
  });
}


// 获取图片文件的宽高
export async function getImageFileInfo(fileData: File): Promise<TImageFileInfo | TGetFileMetaError> {
  return new Promise((resolve) => {
    // 获取上传的图片的宽高
    const reader = new FileReader();
    try {
      reader.readAsDataURL(fileData);
      reader.onload = function (evt) {
        const replaceSrc = evt.target!.result;
        const imageObj = new Image();
        imageObj.src = replaceSrc as string;
        imageObj.onload = function () {
          const { width, height } = imageObj;
          resolve({ width, height });
        };
        imageObj.onerror = function (e) {
          console.log('imageObj.onended', e);
          resolve({ isError: true, error: e });
        };
      };
    } catch (error) {
      resolve({ isError: true, error });
    }
  });
}


// 获取最大公约数
export function gcd(num1: number, num2: number) {
  if (num2 === 0) return num1;
  return gcd(num2, num1 % num2);
}


export function renderDimensions(row: {
  format_type: TCreativeFormatType, image_width?: number,
  image_height?: number, video_width?: number, video_height?: number
}) {
  if (row.format_type === CREATIVE_FORMAT_TYPE.IMAGE) {
    const { image_width: imageWidth, image_height: imageHeight } = row;
    if (imageWidth && imageHeight) return `${imageWidth}X${imageHeight}`;
    return '';
  }
  if (row.format_type === CREATIVE_FORMAT_TYPE.VIDEO) {
    const { video_width: videoWidth, video_height: videoHeight } = row;
    if (videoWidth && videoHeight) {
      const divisor = gcd(videoWidth, videoHeight);
      const aspectWidth = videoWidth / divisor;
      const aspectHeight = videoHeight / divisor;
      return `${aspectWidth}X${aspectHeight}`;
    }
    return '';
  }
  if (row.format_type === CREATIVE_FORMAT_TYPE.HTML) return '';
  return 'NA';
}

export function renderDuration(row: { format_type: TCreativeFormatType, video_duration?: number }) {
  if (row.format_type === CREATIVE_FORMAT_TYPE.VIDEO) {
    const { video_duration: videoDuration } = row;
    if (videoDuration) return videoDuration;
    return '';
  }
  return 'NA';
}

export function generateAssetName(record: IRenderAssetNameRecord) {
  let assetName = '';
  const separator = '_';
  const len = ASSET_NAME_RECORD_SOST_KEYS.length;
  for (let i = 0; i <= len; i++) {
    const keyItem = ASSET_NAME_RECORD_SOST_KEYS[i];
    const isNeedSeparator = i < len - 1;
    const separatorStr = isNeedSeparator ? separator : '';
    switch (keyItem) {
      case 'unique_id':
        assetName += record.game_name + record.unique_id_code + separatorStr;
        break;
      default:
        assetName += (record[keyItem as keyof typeof record] ?? '') + separatorStr;
        break;
    }
  }
  return assetName;
}

export  function getFileExtension(filename: string) {
  const lastDotIndex = filename.lastIndexOf('.');
  if (lastDotIndex === -1 || lastDotIndex === 0) {
    return '';
  }
  return filename.slice(lastDotIndex);
}


// 素材文件分组排序
export function composeCreativeFileList(creativeFileList: IRenderAssetNameRecord[]) {
  let renderList: IRenderAssetNameRecord[] = [];
  const displayList = creativeFileList.filter(item => !item.isInUploadTask);
  // 先分组
  const groupByCreativeFileList = groupBy(displayList, 'format_type');
  // 分组行排前面，对应的数据排后面
  const groupByRowToFirst = (list: IRenderAssetNameRecord[]) => {
    let groupByItem: IRenderAssetNameRecord | undefined;
    const childList: IRenderAssetNameRecord[] = [];
    list.forEach((item) => {
      if (item.isGroupBy) {
        groupByItem =  item;
      } else {
        childList.push(item);
      }
    });
    return [
      ...(!isUndefined(groupByItem) ? [groupByItem] : []),
      ...childList,
    ] as IRenderAssetNameRecord[];
  };
  Object.values(CREATIVE_FORMAT_TYPE).forEach((formatType) => {
    const groupByFileList = groupByCreativeFileList[formatType] ?? [];
    if (groupByFileList.length > 0) {
      renderList = [
        ...renderList,
        ...groupByRowToFirst(groupByFileList),
      ];
    }
  });
  return renderList;
}
