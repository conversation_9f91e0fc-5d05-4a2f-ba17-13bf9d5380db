import { STORE_KEY } from '@/config/config';
import { useLoading } from 'common/compose/loading';
import { useVisible } from 'common/compose/useVisible';
import { ClipsVideo } from 'common/service/creative/aigc_toolkit/type';
import { defineStore } from 'pinia';
import { reactive, ref } from 'vue';

export const useAIClipStore = defineStore(STORE_KEY.CREATIVE.TOOLKIT.AI_CLIP, () => {
  const {
    isLoading: sidebarLoading,
    showLoading: showSidebarLoading,
    hideLoading: hideSidebarLoading,
  } = useLoading(false);
  const {
    isLoading: loadClipsVideoLoading,
    showLoading: showLoadClipsVideoLoading,
    hideLoading: hideLoadClipsVideoLoading,
  } = useLoading(false);
  const { visible: sidebarVisible, hide: hideSidebar, show: showSidebar } = useVisible(true);
  const offset = ref(0);

  // 视频名称计数
  const videoNameCountMap = reactive(new Map<string, number>());

  const init = async () => {
    console.log('toolkit store init');
    setOffset(0);
  };
  // 当前正在剪辑的视频
  const currentClipsVideo = ref<ClipsVideo | null>(null);

  // 已经剪辑完成的视频，等待导出
  const clipsVideos = ref<ClipsVideo[]>([]);

  const setCurrentClipsVideo = (video: ClipsVideo) => {
    currentClipsVideo.value = video;
  };

  const setClipsVideos = (videos: ClipsVideo[]) => {
    const formatVideos = videos.map((video) => {
      if (!video?.video_name) {
        const videoClipsSeqNum = video.cover_url!.replace(/.*_([0-9]+)\..+/gi, '$1');
        let videoName = `${video.origin_video_name}_${videoClipsSeqNum}`;
        if (videoNameCountMap.has(videoName)) {
          const count = videoNameCountMap.get(videoName) ?? 0;
          const newVideoName = `${videoName}(${count})`;
          videoNameCountMap.set(videoName, count + 1);
          videoName = newVideoName;
        }
        videoNameCountMap.set(videoName, 1);

        return {
          ...video,
          video_name: videoName,
        };
      }
      return video;
    });
    clipsVideos.value = formatVideos;
  };

  const setOffset = (newOffset: number) => {
    offset.value = newOffset;
  };

  return {
    init,
    sidebarLoading,
    hideSidebarLoading,
    showSidebarLoading,

    loadClipsVideoLoading,
    showLoadClipsVideoLoading,
    hideLoadClipsVideoLoading,
    offset,
    setOffset,
    currentClipsVideo,
    clipsVideos,
    setCurrentClipsVideo,
    setClipsVideos,

    sidebarVisible,
    hideSidebar,
    showSidebar,
  };
});
