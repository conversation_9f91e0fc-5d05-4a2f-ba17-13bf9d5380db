import { STORE_KEY } from '@/config/config';
import { FavorClip, Folder } from 'common/service/creative/aigc_toolkit/type';
import type { ITree } from 'common/components/TreeMenu';
import { defineStore } from 'pinia';
import { ref } from 'vue';
import {
  getVideoClips, getFavorFolder, createFavorFolder, favorVideoClip, delFavorClip,
} from 'common/service/creative/aigc_toolkit/clips_favor';

export const useAIClipFavorStore = defineStore(STORE_KEY.CREATIVE.TOOLKIT.AI_CLIP_FAVOR, () => {
  const folders = ref<Folder[]>([]);
  const curFolder = ref<string>(''); // 默认文件夹
  const folderList = ref<ITree[]>([]);
  const allFavorClips = ref<FavorClip[]>([]); // 当前用户所有收藏的视频片段
  const favorClips = ref<FavorClip[]>([]); // 某个文件夹收藏的视频片段
  const loading = ref(false);

  const total = ref(0);
  const pageSize = ref(20);
  const pageNum = ref(1);
  const keywords = ref('');

  async function getFolders() {
    folders.value = await getFavorFolder();
    folderList.value = folders.value.map(item => ({
      text: item.name,
      value: String(item.folder_id),
      isExpand: false,
    }));
    if (folderList.value.length > 0) {
      curFolder.value = folderList.value[0].value as string;
      getFolderClips();
    }
  }

  async function getClips() {
    const data = await getVideoClips({});
    allFavorClips.value = data.list;
  }

  // 获取某个文件夹下收藏的视频片段
  async function getFolderClips() {
    loading.value = true;
    const data = await getVideoClips({
      folderId: Number(curFolder.value),
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      keywords: keywords.value,
    });
    loading.value = false;
    total.value = data.total;
    favorClips.value = data.list;
  }

  function onPageChange(page: number) {
    pageNum.value = page;
    getFolderClips();
  }

  function onSizeChange(size: number) {
    pageSize.value = size;
    pageNum.value = 1;
    getFolderClips();
  }

  // 切换收藏夹
  function changeFolder(folderId: string) {
    curFolder.value = folderId;
    getFolderClips();
  }

  return {
    folders, curFolder, folderList, allFavorClips, favorClips,
    total, pageSize, pageNum, keywords, loading,
    getFolders, createFavorFolder, getClips, favorVideoClip, delFavorClip,
    getFolderClips, changeFolder, onPageChange, onSizeChange,
  };
});
