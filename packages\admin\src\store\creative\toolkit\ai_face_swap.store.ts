import { ref, reactive } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { STORE_KEY } from '@/config/config';
import { defineStore } from 'pinia';
import { wait } from 'common/utils/common';
import { Face, FaceDetectRes, FaceTask, FaceParams } from 'common/service/creative/aigc_toolkit/type';
import {
  faceLibrary, faceLabels, faceUpload, faceDetect, facePreview, faceVideoTask, faceGenerate,
} from 'common/service/creative/aigc_toolkit/face_swap';


export const useAIFaceSwapStore = defineStore(STORE_KEY.CREATIVE.TOOLKIT.AI_FACE_SWAP, () => {
  const faceList = ref<Face[]>([]); // 人脸库
  const labelList = ref<string[]>([]); // 人脸标签列表
  const faceLibLoading = ref(false);
  const selectedLabels = ref<string[]>([]); // 选中的人脸标签

  const curAssetId = ref<string>(''); // 当前视频assetid
  const curVidePath = ref<string>(''); // 当前检测视频的路径
  const faceDetecting = ref(false);
  const detectedFaces = ref<FaceDetectRes[]>([]); // 检测到的人脸结果
  const replaceFaces = ref<(Face | null)[]>([]); // 替换的人脸

  const previewDialog = ref(false); // 预览结果弹框
  const previewLoading = ref(false); // 预览生成
  const previewMoreLoading = ref(false);
  const previewList = ref<{source: string, target: string}[]>([]); // 预览展示的对比图

  const faceParams = reactive<FaceParams>({
    facial_restoration: 60,  // int类型
    facial_margin: 60, // int类型
    keep_origin_mouth_shape: false,  // boolean类型
    keep_origin_face_position: false, // boolean类型
    model: 'GFPGAN',  // 字符串类型 GFPGAN or CodeFormer
  });

  const processDialog = ref(false); // 运行中弹框

  const init = async () => {
    getTasks();
    getFaces();
    getLabels();
  };

  // 清除当前信息
  const clearInfo = () => {
    curAssetId.value = '';
    curVidePath.value = '';
    detectedFaces.value = [];
    replaceFaces.value = [];
  };

  const getFaces = async () => {
    faceLibLoading.value = true;
    faceList.value = await faceLibrary(selectedLabels.value);
    faceLibLoading.value = false;
  };

  const getLabels = async () => {
    labelList.value = await faceLabels();
  };

  const detect = async (assetId: string, filePath: string) => {
    faceDetecting.value = true;
    detectedFaces.value = []; // 清空数据
    replaceFaces.value = [];
    const res = await faceDetect({
      asset_id: assetId,
      file_path: filePath,
    });
    if (res.code !== 0) {
      faceDetecting.value = false;
      MessagePlugin.error(res.message);
      return;
    }
    curAssetId.value = assetId;
    curVidePath.value = filePath;
    // 延迟500ms，防止图片404
    await wait(1000);
    if (res.code !== 0) {
      MessagePlugin.error(`face detect fail: ${res.message}`);
      return;
    }
    faceDetecting.value = false;
    detectedFaces.value = res.data;
    replaceFaces.value = Array(detectedFaces.value.length).map(() => null);
  };

  const preview = async (isMore = false) => {
    previewDialog.value = true;

    // 有preview任务在执行，忽略
    if (previewLoading.value || previewMoreLoading.value) {
      return;
    }

    if (isMore) previewMoreLoading.value = true;
    else previewLoading.value = true;

    const sourcePics = detectedFaces.value.map((item, index) => {
      const target = replaceFaces.value[index];
      if (!target) return 'None';
      return JSON.stringify({ Image: target.image_path, Info_path: target.info_path });
    });
    previewLoading.value = true;
    const res = await facePreview({
      target_info: JSON.stringify(detectedFaces.value),
      source_pics: sourcePics,
      video_string: curVidePath.value,
      asset_id: curAssetId.value,
      setting: faceParams,
    });
    await wait(1000);

    previewMoreLoading.value = false;
    previewLoading.value = false;

    if (res.code !== 0) {
      MessagePlugin.error(`preview fail, ${res.message}`);
      return;
    }
    const data = res.data.source_image.map((source, index) => ({
      source,
      target: res.data.target_image[index],
    }));
    if (isMore) previewList.value = previewList.value.concat(data);
    else previewList.value = data;
  };

  const generate = async () => {
    const sourcePics = detectedFaces.value.map((item, index) => {
      const target = replaceFaces.value[index];
      if (!target) return 'None';
      return JSON.stringify({ Image: target.image_path, Info_path: target.info_path });
    });
    await faceGenerate({
      target_info: JSON.stringify(detectedFaces.value),
      source_pics: sourcePics,
      video_string: curVidePath.value,
      asset_id: curAssetId.value,
      setting: faceParams,
    });
    processDialog.value = true;
    getTasks();
  };

  // 换脸视频任务列表
  const taskList = ref<FaceTask[]>([]);
  const taskLoading = ref(false);
  const getTasks = async () => {
    taskLoading.value = true;
    const res = await faceVideoTask();
    taskLoading.value = false;
    taskList.value = res.list;
  };

  return {
    faceList, labelList, selectedLabels, faceLibLoading, detectedFaces, faceDetecting, replaceFaces, previewList,
    previewLoading, previewDialog, processDialog, taskList, previewMoreLoading, taskLoading,
    curAssetId, curVidePath, faceParams,
    init, getLabels, getFaces, faceUpload, detect, preview, generate, clearInfo, getTasks,
  };
});
