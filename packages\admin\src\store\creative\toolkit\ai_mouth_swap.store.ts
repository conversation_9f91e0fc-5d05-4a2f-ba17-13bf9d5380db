import { ref, reactive } from 'vue';
import { STORE_KEY } from '@/config/config';
import { defineStore } from 'pinia';
import { MouthTask } from 'common/service/creative/aigc_toolkit/type';
import { mouthVideoTask } from 'common/service/creative/aigc_toolkit/mouth_swap';

export const useAIMouthSwapStore = defineStore(STORE_KEY.CREATIVE.TOOLKIT.AI_MOUTH_SWAP, () => {
  const formData = reactive({
    video: '',
    audio: '',
  });

  const activeLib = ref<number>(0);

  const taskLoading = ref(false);
  const taskList = ref<MouthTask[]>([]);

  const previewVideo = reactive({
    url: '',
  });

  const getTasks = async () => {
    taskLoading.value = true;
    const res = await mouthVideoTask();
    taskList.value = res.list;
    taskLoading.value = false;
  };

  return {
    formData, taskLoading, taskList, previewVideo, activeLib, getTasks,
  };
});
