import { ref, reactive, computed } from 'vue';
import { STORE_KEY } from '@/config/config';
import { defineStore } from 'pinia';
import {
  ITemplateList, OutputSetting, Scene, TemplateParam, IVideoList, MediaUrl,
} from 'common/service/creative/aigc_toolkit/smart_video.type';
import { getLabelOptions } from 'common/service/creative/library/manage-labels';
import { createTemplate, createVideoTask } from 'common/service/creative/aigc_toolkit/smart_video';
import { TdOptionProps } from 'tdesign-vue-next';

// 初始化scenes
function getInitScenes(): Scene[] {
  return [
    { text: '', url: '', relevance: 0.8, num_clips: '', type: '', media_urls: [] },
  ];
}

export const useAiSVEditorStore = defineStore(STORE_KEY.CREATIVE.TOOLKIT.AI_SMART_VIDEO_EDITOR, () => {
  const labelOptions = ref([]);
  const openingScenes = ref<Scene[]>(getInitScenes());
  const bodyScenes = ref<Scene[]>(getInitScenes());
  const endScenes = ref<Scene[]>(getInitScenes());
  const curTask = ref<IVideoList | null>(null); // 当前任务详情
  const setCurTask = (task: IVideoList | null) => { // 设置当前任务
    curTask.value = task;
  };
  const curTemplate = ref<ITemplateList | null>(null); // 当前模板
  const setCurTemplate = (template: ITemplateList | null) => {
    curTemplate.value = template;
  };

  const showValidate = ref(false);
  const openingValidates = computed(() => openingScenes.value.map(item =>  checkScene(item)));
  const bodyValidates = computed(() => bodyScenes.value.map(item =>  checkScene(item)));
  const endValidates = computed(() => endScenes.value.map(item =>  checkScene(item)));

  function checkScene(scene: Scene) {
    if (!(scene.text || scene.url)) return { valid: false, message: 'text or url is required' };
    if (!scene.relevance) return { valid: false, message: 'relevance is required' };
    return { valid: true };
  }

  const getScenes = (type: string) => {
    if (type === 'opening') return openingScenes.value;
    if (type === 'body') return bodyScenes.value;
    if (type === 'end') return endScenes.value;
    return [];
  };
  const setScenes = (type: string, scenes: Scene[]) => {
    if (type === 'opening') openingScenes.value = scenes;
    if (type === 'body') bodyScenes.value = scenes;
    if (type === 'end') endScenes.value = scenes;
  };

  // 三部分最少保留一个scene
  const canDeleteScene = computed(() => openingScenes.value.length
    + bodyScenes.value.length
    + endScenes.value.length > 1);

  // 获取素材库标签
  const getLabels = async () => {
    const labels = await getLabelOptions();
    const filterLabels = labels.filter((item: TdOptionProps) => item.value === 'LabelList');
    labelOptions.value = filterLabels.length === 0 ? labels : filterLabels;
  };

  const outputSetting = reactive<OutputSetting>({
    ration: '1920*1080',
    mute: true,
    clipLabel: [],
  });

  // 设置参数
  const setSettings = (item: ITemplateList | IVideoList) => {
    openingScenes.value = item.script_scene_opening;
    bodyScenes.value = item.script_scene_body;
    endScenes.value = item.script_scene_ending;
    outputSetting.ration = item.output_ratio;
    outputSetting.mute = item.output_is_mute;
    outputSetting.clipLabel = item.output_clip_label.split('|');
  };

  // 重置参数
  const reset = () => {
    openingScenes.value = getInitScenes();
    bodyScenes.value = getInitScenes();
    endScenes.value = getInitScenes();
    outputSetting.mute = true;
    outputSetting.clipLabel = [];
    curTask.value = null;
  };

  // 保存视频模板
  const saveTemplate = async (params: TemplateParam) => {
    const labels = outputSetting.clipLabel.join('|'); // 多个用|分隔
    return createTemplate({
      ...params,
      script_scene_opening: openingScenes.value,
      script_scene_body: bodyScenes.value,
      script_scene_ending: endScenes.value,
      output_ratio: outputSetting.ration,
      output_is_mute: outputSetting.mute,
      output_clip_label: labels,
      video_url: curTask.value!.video_url, // 没有视频传空
    });
  };

  // 创建视频任务
  const createTasks = async (params: { taskNum: number }) => {
    const labels = outputSetting.clipLabel.join('|'); // 多个用|分隔
    return createVideoTask({
      script_scene_opening: openingScenes.value,
      script_scene_body: bodyScenes.value,
      script_scene_ending: endScenes.value,
      output_ratio: outputSetting.ration,
      output_is_mute: outputSetting.mute,
      output_clip_label: labels,
      task_num: params.taskNum,
    });
  };

  const clipAction = ref<string | undefined>(); // 片段操作类型
  const selectedVideo = ref<{
    clip_id: string | undefined,
    url: string | undefined,
    start_time: number,
    end_time: number,
  }>({
    url: '', start_time: 0, end_time: 0, clip_id: '', // 选择的片段url
  });
  const replaceInfo = reactive<{
    type: string, // 哪个部分
    sceneIndex: number, // 第几个场景
    videoIndex: number, // 第几个视频
    scene: Scene | undefined, // 替换的视频片段
    targetUrl: MediaUrl[], // 上一个&下一个片段
  }>({
    type: '', sceneIndex: 0, videoIndex: 0, scene: undefined,
    targetUrl: [],
  });
  const setClipVideo = (url: string, clip_id: string, start_time = 0, end_time = 0) => {
    selectedVideo.value.url = url;
    selectedVideo.value.clip_id = clip_id;
    selectedVideo.value.start_time = start_time;
    selectedVideo.value.end_time = end_time;
  };

  // 添加片段临时参数
  const addClipInfo = reactive<{
    type: string, // 哪个部分
    sceneIndex: number, // 第几个场景
    text: string, // 搜索文本
    targetUrl: MediaUrl[], // 上一个片段
  }>({
    type: '', sceneIndex: 0, text: '',
    targetUrl: [],
  });

  // 获取上一个和下一个视频，需要处理跨层级和类型
  const getPreNextVideo = (type: string, sceneIndex: number, videoIndex: number): MediaUrl[] => {
    const targetMedias: MediaUrl[] = [];

    // 1.先取上一个
    if (videoIndex > 0) {
      // 当前非第一个视频
      const scene = getScenes(type)[sceneIndex];
      const target = scene.media_urls[videoIndex - 1];
      targetMedias.push(target);
    } else if (sceneIndex > 0) {
      // 当前第一个视频，且有上一个scene, 取最后一个视频
      const scene = getScenes(type)[sceneIndex - 1];
      const target = scene.media_urls[scene.media_urls.length - 1];
      targetMedias.push(target);
    } else {
      // 当前第一个视频，且是第一个scene，需要取第上一个类型最后一个scene的最后一个视频
      let scenes: Scene[] = [];
      if (type === 'end') scenes = getScenes('body');
      if (type === 'body') scenes = getScenes('opening');
      const scene = scenes[scenes.length - 1];
      if (scene) {
        const target = scene.media_urls[scene.media_urls.length - 1];
        targetMedias.push(target);
      }
    }

    // 2. 再取下一个
    const scenes = getScenes(type);
    const currentScene = scenes[sceneIndex];
    if (videoIndex < currentScene.media_urls.length - 1) {
      // 当前非最后一个视频
      const target = currentScene.media_urls[videoIndex + 1];
      targetMedias.push(target);
    } else if (sceneIndex < scenes.length - 1) {
      // 当前最后一个视频，但不是最后一个场景，取下一个场景的第一个视频
      const nextScene = scenes[sceneIndex + 1];
      const target = nextScene.media_urls[0];
      targetMedias.push(target);
    } else {
      // 当前最后一个视频，且是最后一个场景，需要取下一个类型的第一个场景的第一个视频
      let nextScenes: Scene[] = [];
      if (type === 'opening') nextScenes = getScenes('body');
      if (type === 'body') nextScenes = getScenes('end');
      const nextScene = nextScenes[0];
      if (nextScene) {
        const target = nextScene.media_urls[0];
        targetMedias.push(target);
      }
    }

    return targetMedias.map(item => ({
      ...item,
      url: item.cover_url, // 使用封面图替换视频url，目前不支持检索片段url的搜索
      type: 'image/jpg',
    }));
  };

  return {
    openingScenes, bodyScenes, endScenes, outputSetting, labelOptions, curTask, curTemplate, replaceInfo, addClipInfo,
    openingValidates, bodyValidates, endValidates, selectedVideo, showValidate, canDeleteScene, clipAction,
    setScenes, getScenes, getLabels, setClipVideo, createTasks, saveTemplate, setSettings, reset,
    setCurTask, setCurTemplate, getPreNextVideo,
  };
});
