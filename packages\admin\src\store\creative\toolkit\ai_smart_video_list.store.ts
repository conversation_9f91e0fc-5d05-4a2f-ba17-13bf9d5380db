/* eslint-disable no-param-reassign */
import { computed, reactive, ref } from 'vue';
import { STORE_KEY } from '@/config/config';
import { defineStore } from 'pinia';
import {
  getVideos,
  deleteVideoByApi,
  getUnfinishedTaskNum,
} from 'common/service/creative/aigc_toolkit/smart_video';
import type { IVideoList } from 'common/service/creative/aigc_toolkit/smart_video.type.d';
import { useLoading } from 'common/compose/loading';
import { useSessionStorage } from '@vueuse/core';
import dayjs  from 'dayjs';

export const useAiSVideoListStore = defineStore(STORE_KEY.CREATIVE.TOOLKIT.AI_SMART_VIDEO_LIST, () => {
  const { isLoading, showLoading, hideLoading } = useLoading();
  const sessionKey = useSessionStorage('current-video-item', '');

  const unfinishedNum = ref(0); // 未完成的任务数量

  // 模版详情
  const setVideoItem = (val: IVideoList) => {
    sessionKey.value = JSON.stringify(val);
  };

  const statusMap: { [key: string]: string} = {
    2: 'Waiting',
    1: 'Success',
    '-1': 'Failed',
  };
  // 模版页面的指标
  const videoList = ref<IVideoList[]>([]);
  const videoTotal = ref(0);
  const videoPageNum = ref(1);
  const videoName = ref('');
  // 这里调大 有可能出现大屏，无法触发滚动的情况
  const videoPageSize = ref(10);


  // 滚动的ref
  const scrollVideoContainerRef = ref<HTMLDivElement>();

  // 模版是否全部加载完成了，是不是滚动到底，已经没有数据了
  // const isVideoLoadAll = computed(() => videoPageNum.value * videoPageSize.value >= videoTotal.value);
  const setVideoPageNum = (val: number) => (videoPageNum.value = val);

  // 设置模版列表显示的数量
  const setVideoPageSize = (val: number) => (videoPageSize.value = val);

  // 获取模版
  async function getVideosList(taskId?: string, isFindByOne = true) {
    showLoading();
    try {
      const { list = [], total = 0 } = await getVideos({
        page_number: videoPageNum.value,
        page_size: videoPageSize.value,
        task_id: taskId,
        is_find_by_one: isFindByOne,
      });
      const tempList = list.map(item => ({
        ...item,
        video_url: item.video_url,
        statusText: statusMap[item.status],
        created_time: dayjs(new Date(item.created_time)).format('YYYY-MM-DD HH:mm:ss'),
      }));
      const sceneList = ['script_scene_opening', 'script_scene_body', 'script_scene_ending'] as const;
      tempList.forEach((item) => {
        sceneList.forEach((key) => {
          item[key].forEach((scene) => {
            if (!scene.relevance) scene.relevance = 0.8;
          });
        });
      });

      // 分页使用
      videoList.value = tempList;
      videoTotal.value = total;
      hideLoading();
    } catch (error) {
      console.log(error);
    }
  }

  async function getVideo() {
    showLoading();
    const taskId = (new URL(location.href).searchParams).get('task_id');
    try {
      const { list = [] } = await getVideos({
        task_id: taskId,
        is_find_by_one: true,
      });
      const tempList = list.map(item => ({
        ...item,
        video_url: item.video_url,
        statusText: statusMap[item.status],
        created_time: dayjs(new Date(item.created_time)).format('YYYY-MM-DD HH:mm:ss'),
      }));
      hideLoading();
      return tempList[0];
    } catch (error) {
      console.log(error);
    }
  }

  async function deleteVideo(videoId: string) {
    await deleteVideoByApi(videoId);
    getVideosList();
  };

  // 模版滚动加载
  // async function scrollLoad() {
  //   if (!isVideoLoadAll.value && !isLoading.value) {
  //     showLoading();
  //     setVideoPageNum(videoPageNum.value + 1);
  //     await getVideosList();
  //     hideLoading();
  //   }
  // }

  async function getUnfinishedNum() {
    unfinishedNum.value = (await getUnfinishedTaskNum()).num;
  }

  const pagination = reactive({
    total: computed(() => videoTotal.value),
    pageSize: computed(() => videoPageSize.value),
    current: computed(() => videoPageNum.value),
  });

  // 处理切换页面
  const onPageChange = async (pageInfo: { current: number, pageSize: number}) => {
    if (pageInfo.pageSize !== videoPageSize.value) {
      setVideoPageNum(1);
    } else {
      setVideoPageNum(pageInfo.current);
    }
    setVideoPageSize(pageInfo.pageSize);
    await getVideosList();
  };

  return {
    unfinishedNum,
    getUnfinishedNum,
    videoList,
    videoTotal,
    isLoading,
    getVideosList,
    getVideo,
    scrollVideoContainerRef,
    // scrollLoad,
    setVideoItem,
    videoName,
    deleteVideo,
    pagination,
    onPageChange,
  };
});
