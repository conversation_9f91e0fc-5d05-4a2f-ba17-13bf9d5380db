/* eslint-disable no-param-reassign */
import { computed, ref } from 'vue';
import { STORE_KEY } from '@/config/config';
import { defineStore } from 'pinia';
import {
  getTemplates,
  deleteTemplateByApi,
} from 'common/service/creative/aigc_toolkit/smart_video';
import type { ITemplateList } from 'common/service/creative/aigc_toolkit/smart_video.type.d';
import { useLoading } from 'common/compose/loading';
import { useSessionStorage } from '@vueuse/core';

export const useAiSVideoTemplateStore = defineStore(STORE_KEY.CREATIVE.TOOLKIT.AI_SMART_VIDEO_TEMPLATE, () => {
  const { isLoading, showLoading, hideLoading } = useLoading();
  const sessionKey = useSessionStorage('current-template-item', '');
  // 模版详情
  const setTemplateItem = (val: ITemplateList) => {
    sessionKey.value = JSON.stringify(val);
  };

  // 模版页面的指标
  const templateList = ref<ITemplateList[]>([]);
  const templateTotal = ref(0);
  const templatePageNum = ref(1);
  const templateName = ref('');
  // 这里调大 有可能出现大屏，无法触发滚动的情况
  const templatePageSize = 32;

  // 模版弹窗的模版数据
  const templateDialogValue = ref<ITemplateList | undefined>();

  // 模版滚动的ref
  const scrollTemplateContainerRef = ref<HTMLDivElement>();

  // 模版是否全部加载完成了，是不是滚动到底，已经没有数据了
  const isTemplateLoadAll = computed(() => templatePageNum.value * templatePageSize >= templateTotal.value);
  const setTemplatePageNum = (val: number) => (templatePageNum.value = val);

  const resetScrollTop = () => {
    if (scrollTemplateContainerRef.value) {
      scrollTemplateContainerRef.value.scrollTop = 0;
    }
  };

  // 获取模版
  async function getTemplatesList() {
    showLoading();
    try {
      const { list = [], total = 0 } = await getTemplates({
        page_number: templatePageNum.value,
        page_size: templatePageSize,
        template_name: templateName.value,
      });
      const tempList = list.map(item => ({
        ...item,
        video_url: item.video_url,
      }));
      tempList.forEach((item) => {
        const sceneList = ['script_scene_opening', 'script_scene_body', 'script_scene_ending'] as const;
        sceneList.forEach((key) => {
          item[key].forEach((scene) => {
            if (!scene.relevance) scene.relevance = 0.8;
          });
        });
      });
      if (templatePageNum.value === 1) {
        resetScrollTop();
        templateList.value = tempList;
      } else {
        templateList.value = templateList.value.concat(tempList);
      }
      templateTotal.value = total;
      hideLoading();
    } catch (error) {
      console.log(error);
    }
  }

  async function deleteTemplate(templateId: string) {
    await deleteTemplateByApi(templateId);
    getTemplatesList();
  };

  // 模版滚动加载
  async function scrollLoad() {
    if (!isTemplateLoadAll.value && !isLoading.value) {
      showLoading();
      setTemplatePageNum(templatePageNum.value + 1);
      await getTemplatesList();
      hideLoading();
    }
  }

  return {
    templateList,
    isLoading,
    getTemplatesList,
    scrollTemplateContainerRef,
    scrollLoad,
    setTemplateItem,
    templateName,
    deleteTemplate,
    templateDialogValue,
  };
});
