import { defineStore } from 'pinia';
import { reactive, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { STORE_KEY } from '@/config/config';
import { type TemplateFormDataType, type TemplateSlot } from '@/views/creative/AIToolkit/AITemplate/types/index.d';
import { getAllTemplates } from 'common/service/creative/aigc_toolkit/ai_template';
import { GetAllTemplatesResponseDataType } from 'common/service/creative/aigc_toolkit/type.d';
import { MessagePlugin } from 'tdesign-vue-next';
export const useAiTemplateStore = defineStore(STORE_KEY.CREATIVE.TOOLKIT.AI_TEMPLATE, () => {
  const idCount = ref(0);

  const defaultTemplate: TemplateSlot = {
    duration: undefined,
    transform: undefined,
    transformDuration: undefined,

    video_info: {
      can_change_speed: false,
      mute: true,
    },
    audio_info: {
      load_audio: false,
      audio_url: undefined,
      audio_range: undefined,
    },
  };

  const curTemplateSlots = reactive<TemplateSlot[]>([]);
  const curTemplateFormData = reactive<TemplateFormDataType>({
    name: undefined,
    ratio: undefined,
    templateId: undefined,
    templateSlots: curTemplateSlots,
  });

  const templateList = ref<GetAllTemplatesResponseDataType>([]);

  const generateTemplate = (): any => {
    idCount.value += 1;
    return {
      ...cloneDeep(defaultTemplate),
      id: idCount.value,
    };
  };

  const addSlot = (): void => {
    curTemplateSlots.push(generateTemplate());
  };

  const deletedSlot = (id: number): void => {
    const index = curTemplateSlots.findIndex(item => item.id === id);
    if (index !== -1) {
      curTemplateSlots.splice(index, 1);
    }
  };

  const init = async () => {
    initTemplateSlots();
    await getTemplates();
  };

  const getTemplates = async () => {
    try {
      const res = await getAllTemplates();
      templateList.value = res;
    } catch (e) {
      MessagePlugin.error((e as unknown as Error).message ?? 'get template list error,please try again');
    }
  };

  const initTemplateSlots = (): void => {
    curTemplateSlots.length = 0;
    curTemplateFormData.name = undefined;
    curTemplateFormData.ratio = undefined;
    curTemplateFormData.templateId = undefined;
    addSlot();
  };

  return {
    idCount,
    defaultTemplate,
    curTemplateFormData,
    curTemplateSlots,
    addSlot,
    deletedSlot,
    initTemplateSlots,
    init,
    templateList,
    getTemplates,
  };
});
