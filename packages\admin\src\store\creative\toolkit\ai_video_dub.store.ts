import { ref, reactive, computed, watch } from 'vue';
import { STORE_KEY } from '@/config/config';
import { defineStore } from 'pinia';
import { DubTask, TextVoice } from 'common/service/creative/aigc_toolkit/type';
import { getVttTask, vtt, getDubTasks } from 'common/service/creative/aigc_toolkit/ai_dub';
import { checkTextVoices, getNextTimeRange } from '@/views/creative/AIToolkit/VideoDub/utils';
import { SPEAKERS, TONES, LANGUAGES } from '@/views/creative/AIToolkit/VideoDub/const';
import { MessagePlugin } from 'tdesign-vue-next';

export const useAIVideoDubStore = defineStore(STORE_KEY.CREATIVE.TOOLKIT.AI_VIDEO_DUB_EDITOR, () => {
  const formData = reactive({
    video: '',
    duration: 0, // 视频时长
  });
  const videoValid = ref(false);

  const language = ref(LANGUAGES[0].value);

  const textVoices = ref<TextVoice[]>([]); // 文本语音设置列表
  const checkedIndexList = computed(() => {
    const indexList: number[] = [];
    textVoices.value.forEach((item, index) => {
      if (item.checked) indexList.push(index);
    });
    return indexList;
  }); // 勾选的音频列表

  const checkRes = computed(() => checkTextVoices(textVoices.value, formData.duration));

  // 添加音频
  const addVoice = (index?: number) => {
    let timeRange = '';
    if (typeof index === 'number') {
      const curTv = textVoices.value[index];
      timeRange = getNextTimeRange(curTv.timeRange);
    } else {
      timeRange = '00:00:00,000 --> 00:00:05,000';
    }
    const pushItem = {
      text: 'Please input',
      timeRange,
      voice_id: SPEAKERS[0].value,
      style: TONES[0].value,
      checked: false,
      audio_url: '',
    };
    if (index) {
      textVoices.value.splice(index + 1, 0, pushItem);
    } else {
      textVoices.value.push(pushItem);
    }
  };

  // 删除视频
  const deleteVoice = (index: number) => {
    textVoices.value.splice(index, 1);
  };

  const taskLoading = ref(false);
  const taskList = ref<DubTask[]>([]);

  const previewVideo = reactive({
    url: '',
  });

  // 删除已上传视频，执行清除操作
  const clearDub = () => {
    previewVideo.url = '';
    textVoices.value = [];
    curTask.value = undefined;
  };

  // 视频转文本
  const activeIndex = ref(0);
  const vttLoading = ref(false);

  // 轮询获取任务详情
  const canPoll = ref(false); // 控制是否应该发起轮训请求
  const curVttId = ref(0);
  const pollId = ref();
  watch(() => formData.video, async (val) => {
    if (!val && !videoValid.value) return;
    if (!canPoll.value) return;
    regenerateVt();
  });

  const regenerateVt = async () => {
    if (!formData.video) return;
    vttLoading.value = true;
    const res = await vtt(formData.video, language.value.toUpperCase());
    curVttId.value = res.id;
    pollId.value = setInterval(() => {
      getTextVoices();
    }, 5000);
  };

  const getTextVoices = async () => {
    const vvtRes = await getVttTask(curVttId.value);
    if (vvtRes.code !== 0) {
      stopPoll();
      vttLoading.value = false;
      MessagePlugin.error(vvtRes.message);
      return;
    }

    const { status, srt_url: srtUrl, srt_detail: srtList } = vvtRes.data;
    if (status === 3) {
      MessagePlugin.error('Text generation failed');
      vttLoading.value = false;
      stopPoll();
      return;
    }
    if (!srtUrl) return;

    stopPoll();
    textVoices.value = srtList.map(item => ({
      ...item,
      voice_id: SPEAKERS[0].value,
      style: TONES[0].value,
    }));
    vttLoading.value = false;

    return vvtRes.data;
  };

  const stopPoll = () => {
    clearInterval(pollId.value);
  };

  // 获取配音任务
  const curTask = ref<DubTask>();
  const getTasks = async () => {
    taskLoading.value = true;
    const res = await getDubTasks();
    taskList.value = res.tasks;
    taskLoading.value = false;
  };

  return {
    formData, taskLoading, taskList, previewVideo, language, textVoices, vttLoading, activeIndex, checkedIndexList,
    checkRes, curVttId, videoValid, canPoll, curTask, clearDub,
    getTasks, addVoice, deleteVoice, stopPoll, getTextVoices, regenerateVt,
  };
});
