
export const OPERATOR_VALUE_MAP = {
  '<': 1,
  '>': 2,
  '=': 3,
  '<=': 4,
  '>=': 5,
} as const;

export const NEED_FORMAT_KEYS = [
  'spend_rate', 'spend', 'ctr', 'cvr',
  'installs', 'installs_rate', 'ipm', 'cpi', 'd7_roas',
] as const;

export const BOTTOM_METRICS = [
  { label: 'D7 ROAS', value: 'd7_roas' },
  { label: 'CTR', value: 'ctr' },
  { label: 'CVR', value: 'cvr' },
  { label: 'IPM', value: 'ipm' },
  { label: 'CPI', value: 'cpi' },
];

export const FORMAT_KEY_ALIAS = {
  spend: 'cost',
} as const;

export const ORDER_BY_TYPE = {
  cpi: 'ASC',
  default: 'DESC',
} as const;

export const DEFAULT_METRIC_FILTER  = {
  metric: 'spend', rules: [],
};

export const DISPLAY_TYPE = {
  COMBINATION: 'combination',
  INDEPENDENT: 'independent',
} as const;

export const PUBGM_DEFAULT_SHOW_LABELS = [
  '事情', '成功元素', '风格类型', '差异事情', '差异成功元素',
];

export const NA_LABEL_STR = 'Unlabeled(AiX)---Unlabeled(AiX)';
