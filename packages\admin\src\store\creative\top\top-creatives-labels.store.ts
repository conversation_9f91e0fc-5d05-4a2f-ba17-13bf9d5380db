import { ref, computed } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { getTopLabels } from 'common/service/creative/top/creatives';
import { TopLabelRes } from 'common/service/creative/top/type';
import { IModelValue } from 'common/components/MetricFilterDialog/type';
import { cloneDeep, uniq } from 'lodash-es';
import { getLabelsInsightFilterConfig } from '@/store/creative/labels/insight-filter-config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { getFilterHaving, getOrderType, sortLabelData } from './utils';

export const useTopCreativesLabelsStore = defineStore(STORE_KEY.CREATIVE.TOP.CREATIVES_LABELS, () => {
  const { gameCode } = storeToRefs(useGlobalGameStore());
  const topAssets = ref<string[]>([]);
  const startData = ref<string>('');
  const endData = ref<string>('');
  const firstSecondLabels = ref<string[]>([]); // 选中的二级标签列表
  const labelData = ref<TopLabelRes[]>([]);
  const loaded = ref(false);
  const loading = ref(false);

  const defaultMetricFilter = { metric: 'spend', rules: [] };
  const {
    metricsGroup: metricFilterOptions, allMetrics, initOptions, labelTypeList,
    initLabelTypeList: firstLabelList,
  } = getLabelsInsightFilterConfig(gameCode, undefined, true);
  const metricFilter = ref<IModelValue>(cloneDeep(defaultMetricFilter));
  const updateMetricFilter = (val: IModelValue) => {
    metricFilter.value = val;
  };

  const setParams = (sDate: string, eDate: string, assets: string[]) => {
    startData.value = sDate;
    endData.value = eDate;
    topAssets.value = assets;
  };

  const getData = async () => {
    loading.value = true;

    const having = getFilterHaving(metricFilter.value);
    const havingMetric = having.map(item => item.name);
    const orderbyMetric = metricFilter.value.metric;

    const res = await getTopLabels({
      startDate: startData.value,
      endDate: endData.value,
      asset_name: topAssets.value,
      metric: uniq([orderbyMetric, ...havingMetric, 'asset_num']),
      orderby: [
        { by: metricFilter.value.metric, order: getOrderType(orderbyMetric) },
      ],
      group: ['first_label', 'second_label'],
      label: firstSecondLabels.value,
      label_search_type: 2,
      having,
    }, labelTypeList.value);

    const firstLabels = labelTypeList.value.map(item => item.value);
    sortLabelData(res, firstLabels); // 根据一级标签顺序排序

    loaded.value = true;
    loading.value = false;
    labelData.value = res;
  };

  const labelChange = (label: string) => {
    if (firstSecondLabels.value.includes(label)) {
      firstSecondLabels.value = firstSecondLabels.value.filter(item => item !== label);
    } else {
      firstSecondLabels.value.push(label);
    }
    getData();
  };

  const orderLabel = computed(() => {
    const orderbyMetric = metricFilter.value.metric;
    return allMetrics.value.find(item => item.key === orderbyMetric)?.title ?? '-';
  });

  return {
    topAssets, firstSecondLabels, labelData, loading, loaded, metricFilter, metricFilterOptions,
    orderLabel, allMetrics, firstLabelList,
    setParams, getData, labelChange, updateMetricFilter, initOptions,
  };
});
