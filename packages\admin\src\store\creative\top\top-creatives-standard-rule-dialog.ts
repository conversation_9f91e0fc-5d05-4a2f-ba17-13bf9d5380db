import { computed, defineComponent, h, useAttrs } from 'vue';
import MetricFilterDialog from 'common/components/MetricFilterDialog';
import { useCreativesStandardRuleStore } from './top-creatives-standard-rule.store';
import type { ICustomMetric } from 'common/components/MetricFilterDialog';
import type { ICustomRule } from 'common/service/creative/top/type';
import { isFunction, pick, cloneDeep, isUndefined } from 'lodash-es';
import { DEFAULT_METRIC_FILTER } from './const';
import { InstancePropsType } from 'common/types/components';

type TMetricFilterDialogPropsType = InstancePropsType<typeof MetricFilterDialog>;

const TopCreativesStandardRuleDialog = defineComponent({
  setup() {
    const { addCustomRules, updateCustomRule, deleteCustomRule } = useCreativesStandardRuleStore();
    const attrs = computed<Record<string, any>>(() => useAttrs());
    // 调用外部传进来的updateModelValue 方法
    const safeUpdateModelValue = (data: any) => {
      const updateModelValue = attrs.value['onUpdate:modelValue'];
      if (isFunction(updateModelValue)) {
        updateModelValue(pick(data, ['metric', 'rules', 'weightCoefficient']));
      }
    };

    /**
     * 增 删 改的方法用store里面的, 当然, 也可以attr 里面传入覆盖
     */
    // 新增
    const addMetricCallback = async (data: ICustomMetric) => {
      const newData = await addCustomRules(data);
      const isSuccess = !isUndefined(newData);
      if (isSuccess) safeUpdateModelValue(newData);
      return isSuccess;
    };
    // 编辑
    const updateMetricCallback = async (data: ICustomMetric, oldVal: any) => {
      const updatedData = await updateCustomRule((oldVal as ICustomRule).id, data);
      const isSuccess = !isUndefined(updatedData);
      if (isSuccess) safeUpdateModelValue(updatedData);
      return isSuccess;
    };
    // 删除
    const onDeleteMetric = async (data: ICustomMetric) => {
      await deleteCustomRule(data.id);
      safeUpdateModelValue(cloneDeep(DEFAULT_METRIC_FILTER));
    };


    // 增删改和 组件需要的其他props
    const metricFilterDialogProps = computed<TMetricFilterDialogPropsType>(() => ({
      addMetricCallback,
      updateMetricCallback,
      onDeleteMetric,
      ...(attrs.value as Omit<TMetricFilterDialogPropsType, 'addMetricCallback' | 'updateMetricCallback' | 'onDeleteMetric'>),
    }));
    return {
      metricFilterDialogProps,
    };
  },
  render() {
    return h(MetricFilterDialog, {
      ...this.metricFilterDialogProps,
    });
  },
});

export default TopCreativesStandardRuleDialog;
