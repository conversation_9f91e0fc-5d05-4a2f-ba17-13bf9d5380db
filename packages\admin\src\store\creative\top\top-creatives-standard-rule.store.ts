import { computed, ref } from 'vue';
import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import {
  getCustomRulesService, createCustomRuleService, updateCustomRuleService,
  deleteCustomRuleService,
} from 'common/service/creative/top/creatives';
import type { ICustomRule } from 'common/service/creative/top/type';
import { metricToReqBody, ruleItemToMetric } from './utils';
import { useLoading } from 'common/compose/loading';
import type { ICustomMetric } from 'common/components/MetricFilterDialog';
import { useTips } from 'common/compose/tips';

export const useCreativesStandardRuleStore = defineStore(STORE_KEY.CREATIVE.TOP.CREATIVES_STANDARD_RULE, () => {
  const { warn } = useTips();
  const {
    isLoading: isCustomRulesLoading,
    showLoading: showCustomRulesLoading,
    hideLoading: hideCustomRulesLoading,
  } = useLoading();

  const customRuleList = ref<ICustomRule[]>([]);

  // 获取自定义规则
  const getCustomRules = async () => {
    const list = await getCustomRulesService({ module: 'creative' });
    customRuleList.value = list || [];
  };
    // 新增自定义规则
  const addCustomRules = async (ruleData: ICustomMetric) => {
    showCustomRulesLoading();
    const res = await createCustomRuleService({
      ...metricToReqBody(ruleData),
      module: 'creative',
    });
    const isSuccess = res?.code === 0;
    if (isSuccess) {
      await getCustomRules();
      hideCustomRulesLoading();
      return ruleItemToMetric(res.newData);
    }
    warn(res?.message);
    hideCustomRulesLoading();
  };
    // 修改自定义规则
  const updateCustomRule = async (id: number, ruleData: ICustomMetric) => {
    showCustomRulesLoading();
    const res = await updateCustomRuleService({
      id,
      ...metricToReqBody(ruleData),
      module: 'creative',
    });
    const isSuccess = res?.code === 0;
    if (isSuccess) {
      await getCustomRules();
      hideCustomRulesLoading();
      return ruleItemToMetric(res.newData);
    }
    warn(res?.message);
    hideCustomRulesLoading();
  };
    // 删除自定义规则
  const deleteCustomRule = async (id: number) => {
    showCustomRulesLoading();
    await deleteCustomRuleService(id);
    await getCustomRules();
    hideCustomRulesLoading();
  };

  return {
    getCustomRules,
    addCustomRules,
    updateCustomRule,
    deleteCustomRule,
    customRuleList: computed(() => customRuleList.value),
    isCustomRulesLoading,
  };
});
