/* eslint-disable no-param-reassign */
import { ref, computed } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { getLabelsInsightFilterConfig } from '../labels/insight-filter-config';
import { useGlobalGameStore } from '@/store/global/game.store';
import dayjs from 'dayjs';
import { FormModel } from 'common/service/creative/label/insight/type';
import { useLoading } from 'common/compose/loading';
import { cloneDeep, uniq, isPlainObject } from 'lodash-es';
import { OPTIONS_SOURCE, SEARCH_BOX_DEFAULT_VALUE } from '../labels/const';
import { getReqLabels } from '../labels/utils';
import { useRoute } from 'vue-router';
import { HavingType, TopAssetItem, TopData, TotalData } from 'common/service/creative/top/type';
import { getTopData } from 'common/service/creative/top/creatives';
import type { ICondition } from 'common/components/SearchBox';
import type { IModelValue as IMetricFilterModelValue } from 'common/components/MetricFilterDialog';
import { useLabelViews } from '../labels/labels-views';
import type { ICustomViewItem } from 'common/components/NewViewTab';
import { NEED_FORMAT_KEYS, FORMAT_KEY_ALIAS, DEFAULT_METRIC_FILTER } from './const';
import { useDownloadFile } from 'common/compose/download-file';
import { formatVal, excelFormatFn } from '@/views/creative/label/insight/utils';
import { getFilterHaving, getOrderType, getHasNALabelValue } from './utils';
import type { TopAssetItemFormatted } from './type';
import { useCreativesStandardRuleStore } from './top-creatives-standard-rule.store';
import DefaultPreview1 from '@/assets/img/creatives/preview-default-1.png';
import DefaultPreview2 from '@/assets/img/creatives/preview-default-2.png';
import { RATING_NAMES } from '@/views/creative/top/const';
import { IMetricItem } from 'common/service/creative/dashboard-af/type';
import { NEW_API_HOST } from 'common/utils/auth';
import { MessagePlugin } from 'tdesign-vue-next';
import { getDropboxIdBAyNames } from 'common/service/creative/dashboard/get-url-by-names';


// useTopCreativesStore
export const useTopCreativesStore = defineStore(STORE_KEY.CREATIVE.TOP.CREATIVES, () => {
  const creativesStandardRuleStore = useCreativesStandardRuleStore();
  const { customRuleList } = storeToRefs(creativesStandardRuleStore);

  const viewModule = STORE_KEY.CREATIVE.TOP.CREATIVES;
  const defaultFoldList = [
    'serial_name',
    'campaign_name',
    'ad_group_name',
    'ad_name',
    'country_code',
    'asset_age',
    'label',
  ];
  const options = ref([]);
  const assetList = ref<TopAssetItemFormatted[]>([]);
  const downloading = ref(false);
  const route = useRoute();

  const code = route.query?.code as string; // 判断是不是分享视图进来的
  const { gameCode } = storeToRefs(useGlobalGameStore());
  const { isLoading: isFetchDataLoading, showLoading, hideLoading } = useLoading();

  const topVal = ref(20);

  const baseModelValue: FormModel = {
    date: [dayjs().subtract(7, 'day')
      .format('YYYY-MM-DD'), dayjs().subtract(1, 'day')
      .format('YYYY-MM-DD')],
    asset_name: [],
    serial_name: [],
    impression_date: [],
    label: {
      labelList: [],
      labelsSearchType: 1,
    },
    campaign_type: [],
    type: ['VIDEO'],
    country: {
      country: [],
    },
    country_code: [],
    network: [],
    platform: [],
    keywords: SEARCH_BOX_DEFAULT_VALUE as ICondition[],
    campaign_name: [],
    ad_group_name: [],
    ad_name: [],
    asset_age: [],
    top_value: topVal.value,
  };

  const formModelValue = ref<FormModel>(cloneDeep(baseModelValue));

  const foldList = ref<string[]>([...defaultFoldList]); // 默认折叠的筛选项
  const setFoldList = (val: string[]) => (foldList.value = [...val]);

  const metricFilterModelValue = ref<IMetricFilterModelValue>(cloneDeep(DEFAULT_METRIC_FILTER));
  const updateMetricFilterModelValue = (val: IMetricFilterModelValue, isRefreshData = false) => {
    metricFilterModelValue.value = val;
    isRefreshData && getTableData();
  };

  // 表单组件渲染时用到的数据源
  const {
    formList,
    totalLabelList,
    initOptions,
    customMetricGroup,
    customMetrics,
    metricsGroup,
    allMetrics,
  } = getLabelsInsightFilterConfig(gameCode, formModelValue, true);


  const curRuleName = computed(() => {
    const targetRule = allMetrics.value.find(item => item.key === metricFilterModelValue.value.metric);
    return targetRule?.title || '';
  });

  // 构造请求参数
  const getParams = () => {
    const {
      date,
      asset_name: assetName,
      serial_name: serialName,
      campaign_type: campaignType,
      type,
      country_code: countryCode,
      network,
      label,
      platform,
      campaign_name: campaignName,
      ad_group_name: adGroupName,
      ad_name: adName,
      asset_age: assetAge,
    } = formModelValue.value;
    let { impression_date: impressionDate } = formModelValue.value;

    const { labelList = [], labelsSearchType } = label;
    const labels = getReqLabels(totalLabelList.value, labelList);

    const orderbyMetric = metricFilterModelValue.value.metric;
    // 过滤空数据
    impressionDate = impressionDate.filter(item => !!item);

    const keywords: { key: string, val: string[] }[] = [];
    if (assetName!.length > 0) keywords.push({ key: 'asset_name', val: assetName as string[] });
    if (serialName!.length > 0) keywords.push({ key: 'asset_serial_id', val: serialName as string[] });
    if (campaignName!.length > 0) keywords.push({ key: 'campaign_name', val: campaignName as string[] });
    if (adGroupName!.length > 0) keywords.push({ key: 'ad_group_name', val: adGroupName as string[] });
    if (adName!.length > 0) keywords.push({ key: 'ad_name', val: adName as string[] });

    let having: HavingType[] = [];
    let havingMetric: string[] = [];

    // Rating开头的规则是特殊规则，不需要过滤条件
    const ratingMetrics: string[] = [];

    if (curRuleName.value) {
      if (curRuleName.value.startsWith('Rating')) {
        RATING_NAMES.forEach((name) => {
          const targetMetric = customMetrics.value.find(item => item.title === name) as IMetricItem;
          if (targetMetric) {
            ratingMetrics.push(targetMetric.key);
          }
        });
      } else {
        having = getFilterHaving(metricFilterModelValue.value);
        havingMetric = having.map(item => item.name);
      }
    }
    const assetTypes = type.length > 0 ? type : ['VIDEO', 'IMAGE'];

    return {
      startDate: date[0].replaceAll('-', ''),
      endDate: date[1].replaceAll('-', ''),
      pageSize: topVal.value,
      pageNum: 1,
      impression_date: impressionDate.map(item => item.replaceAll('-', '')),
      campaign_type: campaignType,
      asset_type: assetTypes,
      country_code: countryCode,
      asset_age: assetAge,
      metric: uniq(['spend', 'installs', 'clicks', 'impressions', 'ctr', 'cvr', 'ipm', 'cpi', 'd7_roas', orderbyMetric,
        ...havingMetric, ...ratingMetrics]),
      network,
      platform,
      labels: labels.filter(v => v !== 'NA---NA'),
      label_search_type: labelsSearchType,
      has_na_label: getHasNALabelValue(labels),
      group: ['asset_name', 'impression_date'],
      orderby: [{ by: orderbyMetric, order: getOrderType(orderbyMetric) }],
      keywords,
      having,
    };
  };

  // 当前排序的metric 外显的名称
  const orderbyMetricLabel = computed(() => {
    const orderbyMetric = metricFilterModelValue.value.metric;
    const metricLabel = allMetrics.value.find(item => item.key === orderbyMetric)?.title;
    const customRuleLabel = customRuleList.value.find(item => item.metric === orderbyMetric)?.name;
    return metricLabel ?? customRuleLabel ?? '-';
  });
  const topData = ref<TotalData[]>([]);

  // 获取分页表格数据
  const getTableData = async () => {
    const params = getParams();

    showLoading();
    try {
      const { totalData, topList } = await getTopData(params, allMetrics.value);
      topData.value = totalData;

      const newTopList = await getAssetId(topList);

      // 这里数据量不多, map一把格式化
      assetList.value = newTopList.map((row) => {
        const keys = Object.keys(row);
        const newRow: Record<string, any> = { ...row };
        for (const key of keys) {
          const value = row[key as keyof typeof row];
          if (NEED_FORMAT_KEYS.includes(key as (typeof NEED_FORMAT_KEYS)[number])) {
            const formatKey = FORMAT_KEY_ALIAS[key as keyof typeof FORMAT_KEY_ALIAS] || key;
            newRow[`${key}__formatted`] = formatVal(value as number, formatKey, allMetrics.value);
          }
        }

        if (row.asset_id) {
          const id = encodeURIComponent(`id:${row.asset_id}`);
          newRow.preview_url = `https:${NEW_API_HOST}/api_v2/creative/common/thumbnail?game=${gameCode.value}&path=${id}`;
        } else {
          newRow.preview_url = Math.random() > 0.5 ? DefaultPreview1 : DefaultPreview2;
        }

        // 格式化impression_date
        newRow.impression_date = dayjs(String(row.impression_date), 'YYYYMMDD').format('YYYY-MM-DD');

        return newRow as TopAssetItemFormatted;
      });
      hideLoading();
    } catch (err: unknown) {
      MessagePlugin.error((err as Error).message);
      hideLoading();
    }
  };

  async function getAssetId(data: TopAssetItem[]) {
    if (data.length === 0) return data;

    const assetNames = data.map(item => item.asset_name);

    // 批量获取asset_id
    const assets = await getDropboxIdBAyNames(assetNames);
    return data.map(item => ({
      ...item,
      asset_id: assets.find(a => a.asset_name === item.asset_name)?.asset_id || '',
    }));
  }

  // 获取全量数据
  const downloadAll = async () => {
    const params = getParams();
    params.pageSize = 1000000;

    downloading.value = true;
    const { total, topList } = await getTopData(params, allMetrics.value);

    const isRating = curRuleName.value.startsWith('Rating');

    const header: Record<string, string> = {
      index: 'Rank',
      new: 'New In Top Status',
      ...(isRating && { rate_level: 'Score' }),
      asset_name: 'AssetName',
      impression_date: 'ImpressionDate',
      spend: 'Spend',
      impressions: 'Impression',
      clicks: 'Click',
      ctr: 'CTR',
      cvr: 'CVR',
      installs: 'Installs',
      ipm: 'IPM',
      cpi: 'CPI',
      d7_roas: 'D7 RoAs',
    };

    const ratingKeys: string[] = [];
    if (isRating) {
      RATING_NAMES.forEach((name) => {
        const targetMetric = customMetrics.value.find(item => item.title === name) as IMetricItem;
        if (targetMetric) {
          ratingKeys.push(targetMetric.key);
          header[targetMetric.key] = name;
        }
      });
      header.installs_rate = '量级占比';
    }

    const downloadData = [total].concat(topList).map((row, index) => {
      const impressionDate = dayjs(String((row as TopAssetItem).impression_date), 'YYYYMMDD').format('YYYY-MM-DD');
      const target = assetList.value.find(item => item.asset_name === (row as TopAssetItem).asset_name);
      const newLabel = target?.is_new ? 'New' : '-';

      const rowData: Record<string, number | string> = {
        index: index === 0 ? 'Total' : index,
        new: newLabel,
        ...(isRating && { rate_level: index === 0 ? '-' : (row as TopAssetItem).rate_level }),
        asset_name: index === 0 ? '-' : (row as TopAssetItem).asset_name,
        impression_date: index === 0 ? '-' : impressionDate,
        spend: formatVal(row.spend, 'spend', allMetrics.value),
        impressions: formatVal(row.impressions, 'impressions', allMetrics.value),
        clicks: formatVal(row.clicks, 'clicks', allMetrics.value),
        d7_roas: formatVal(row.d7_roas, 'd7_roas', allMetrics.value),
        ctr: formatVal(row.ctr, 'ctr', allMetrics.value),
        cvr: formatVal(row.cvr, 'cvr', allMetrics.value),
        installs: formatVal(row.installs, 'installs', allMetrics.value),
        ipm: formatVal(row.ipm, 'ipm', allMetrics.value),
        cpi: formatVal(row.cpi, 'cpi', allMetrics.value),
      };

      ratingKeys.forEach((metric) => {
        rowData[metric] = formatVal(row[metric as keyof TopData] as number, metric as keyof TopData, customMetrics.value);
      });

      if (isRating) {
        rowData.installs_rate = `${((row.installs / total.installs) * 100).toFixed(2)}%`;
      }

      return rowData;
    });

    useDownloadFile(downloadData, 'creative top.xlsx', {
      header,
      formatDataFn: excelFormatFn,
    });
    downloading.value = false;
  };

  const shareViewParams = computed(() => ({
    game: gameCode.value,
    param: {
      form: { ...formModelValue.value },
      fold_list: foldList.value, // 为什么这里也放个fold_list？ 因为与param平级的外层的fold_list，视图的查询接口没有返回这个字段。
      metric_filter: metricFilterModelValue.value,
      top_value: topVal.value,
    },
    fold_list: foldList.value,
    system: viewModule,
  }));

  // 视图切换, 更新页面的筛选条件
  const updatePageFilters = (viewItem: ICustomViewItem) => {
    // 外层的param接口目前没有返回， 如果后面接口返回了，也可以用这个
    const { fold_list: outerFoldList = [] } = viewItem.param;
    const {
      form = {},
      fold_list: innerFoldList = [],
      metric_filter: metricFilter,
      top_value: topValue = topVal.value,
    } = viewItem.param || {};
    // 顶部表单
    formModelValue.value = {
      ...cloneDeep(baseModelValue),
      ...form,
    };
    const uniqFoldList = uniq([...outerFoldList, ...innerFoldList]);
    setFoldList(isDefaultView.value ? defaultFoldList : uniqFoldList);
    // metric 筛选
    if (isPlainObject(metricFilter)) {
      updateMetricFilterModelValue(metricFilter);
    } else {
      updateMetricFilterModelValue(cloneDeep(DEFAULT_METRIC_FILTER));
    }
    topVal.value = topValue > 50 ? 50 : topValue; // top值
  };
  // 视图功能
  const {
    viewId,
    viewList,
    isViewLoading,
    isDefaultView,
    setViewId,
    initView,
    initViewList,
    deleteView,
    addView,
    updateView,
    getViewItem,
  } = useLabelViews({
    module: viewModule,
    gameCode,
    code,
    onViewChange: updatePageFilters,
    baseModelValue,
    shareViewParams,
  });

  async function init() {
    initView();
    await initOptions({ source: OPTIONS_SOURCE.TOP_CREATIVES }); // 配置信息获取前置
    if (code) {
      await Promise.all([initViewList()]); // 拉取配置 分享视图和视图
      updatePageFilters(viewList.value[0]);
      getTableData();
    } else {
      Promise.all([getTableData(), initViewList()]);
    }
  }

  const onReset = () => {
    const viewItem = getViewItem(viewId.value);
    updatePageFilters(viewItem);
    getTableData();
  };

  return {
    options,
    assetList,
    formList,
    formModelValue,
    isFetchDataLoading,
    topVal,
    downloading,
    foldList,
    customMetricGroup,
    customMetrics,
    topData,
    metricFilterOptions: computed(() => metricsGroup.value),
    metricFilterModelValue,
    isViewLoading,
    viewId,
    viewList,
    shareViewParams,
    allMetrics,
    orderbyMetricLabel,
    initOptions,
    getTableData,
    downloadAll,
    init,
    onReset,
    setFoldList,
    getParams,
    updatePageFilters,
    updateMetricFilterModelValue,
    setViewId,
    deleteView,
    addView,
    updateView,
    getViewItem,
  };
});
