import type { TopAssetItem , ITopLabelItem, ITopCombiItem } from 'common/service/creative/top/type';
import { DISPLAT_TYEP } from './const';

type TFormattedFields =  {
  spend_rate__formatted: string,
  spend__formatted: string,
  ctr__formatted: string,
  cvr__formatted: string,
  installs__formatted: string,
  installs_rate__formatted: string,
  ipm__formatted: string,
  cpi__formatted: string,
  d7_roas__formatted: string,
}

export type TopAssetItemFormatted =  TopAssetItem & TFormattedFields;

export type TTopLabelItemFormatted = ITopLabelItem & TFormattedFields;

export type TDisplayType = typeof DISPLAT_TYEP[keyof typeof DISPLAT_TYEP];

export type TCombiLabelFormatted = TFormattedFields & ITopCombiItem;
