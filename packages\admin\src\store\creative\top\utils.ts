import { NA_LABEL_STR, OPERATOR_VALUE_MAP } from '@/store/creative/top/const';
import { sqlHaving } from '@/store/creative/dashboard/sqlObj';
import { isUndefined } from 'lodash-es';
import { EqualSignal } from '@/store/creative/dashboard/dashboard.const';
import { IModelValue } from 'common/components/MetricFilterDialog/type';
import type { ICustomRule, TCreateCustomRule } from 'common/service/creative/top/type';
import type { ICustomMetric, IOption as ICustomMetricGroupby, TOperator } from 'common/components/MetricFilterDialog';
import { ORDER_BY_TYPE } from './const';
import { NA_LABEL_SON_STR } from '../labels/const';

// 参考了 creative dashboard页表格表头筛选的传参
export const getFilterHaving = (values: IModelValue) => {
  const having = values.rules.map((ruleItem) => {
    const { metric: key, op, value } = ruleItem;
    const condition = OPERATOR_VALUE_MAP[op];
    const havingItem = sqlHaving({ key, item: { condition: `${condition}`, value } });
    const { is_lower_equal: isLowerEqual, is_upper_equal: isUpperEqual } = havingItem;
    if (isUndefined(isLowerEqual)) {
      havingItem.is_lower_equal = EqualSignal.NOT;
    }
    if (isUndefined(isUpperEqual)) {
      havingItem.is_upper_equal = EqualSignal.NOT;
    }
    return havingItem;
  });
  return having;
};

/**
 *
 * @param data 自定义规则的数据
 * @returns 组件需要的数据格式
 */
export const ruleItemToMetric = (data: ICustomRule) => {
  const { filter_metrics: filterMetrics, calculate_metrics: calculateMetrics, name, metric } = data;
  const rules = filterMetrics.map(item => ({
    metric: item.name,
    op: item.operator as TOperator,
    value: item.value,
    type: item.type || 'value',
  }));
  const weightCoefficient = calculateMetrics.map(item => ({
    metric: item.name,
    value: item.weight,
  }));
  return {
    ...data,
    rules,
    weightCoefficient,
    label: name,
    value: metric,
  };
};

// 自定义规则列表, 把接口回来的数据转成组件需要的格式
export const customRuleListToMetrics = (ruleList: ICustomRule[]): ICustomMetricGroupby => {
  const list: ICustomMetric[] = ruleList.map(ruleItem => ruleItemToMetric(ruleItem));
  return {
    label: 'Custom Standard Rules',
    value: 'custom_standard_rules',
    isCustomMetric: true,
    children: list,
  };
};

// 新增或者比编辑时, 将组件内吐出来的值, 转成接口需要的
export const metricToReqBody = (data: ICustomMetric): Omit<TCreateCustomRule, 'module'> => {
  const { rules, weightCoefficient } = data;
  const filterMetrics = rules.map((ruleItem) => {
    const { metric: name, op: operator, value, type } = ruleItem;
    return { name, operator, value, type };
  });
  const calculateMetrics = weightCoefficient.map(item => ({ name: item.metric, weight: item.value }));
  return {
    name: data.label,
    filter_metrics: filterMetrics,
    calculate_metrics: calculateMetrics,
  };
};

export const getOrderType = (metrc: string) => ORDER_BY_TYPE[metrc as keyof typeof ORDER_BY_TYPE] || ORDER_BY_TYPE.default;

// 素材详情页，雷达图页忽略的指标
export const isIgnoreMetrics = (metric: string) => metric === 'asset_score' || metric?.indexOf('rank_by_custom_metric') > -1;

// 动态计算一组数据的最大上限
export function calculateMaxValue(data: number[]): number {
  // 找到数组中的最大值
  const maxVal = Math.max(...data);

  // 判断数组中的所有值是否都小于1
  const allLessThanOne = data.every(num => num < 1);

  if (allLessThanOne) {
    // 如果所有值都小于1，按照小数的逻辑处理
    return Math.ceil(maxVal * 50) / 50; // 四舍五入到0.02的倍数
  }

  const magnitude = Math.floor(Math.log10(maxVal)); // 获取量级1~n

  const step = 10 ** magnitude; // 计算每一步的数据差
  const midStep = step / 2; // 间距的一半

  if (maxVal % step === 0) return maxVal; // 刚好取整，直接返回

  const completeNum = (Math.floor(maxVal / step) + 1) * step; // 补齐步长后
  const diff = completeNum - maxVal > midStep ? midStep : step;

  return Math.floor(maxVal / step) * step + diff;
}

// 根据一级标签顺序对数据进行排序
export function sortLabelData<T extends { first_label: string }>(list: T[], firstLabels: string[]) {
  list.sort((x, y) => {
    const index1 = firstLabels.indexOf(x.first_label);
    const index2 = firstLabels.indexOf(y.first_label);
    return index1 - index2;
  });
}

/**
 * @description 基于labels下拉列表选中值，得出has_na_label 参数值
 */
export function getHasNALabelValue(labels: string[], withParent = true) {
  if (labels?.length === 0) {
    return 2;
  }
  const naLabel = withParent ? NA_LABEL_STR : NA_LABEL_SON_STR;
  if (labels?.length === 1 && labels?.[0] === naLabel) {
    return 1;
  }
  if (!labels?.includes(naLabel)) {
    return 0;
  }
  return 2;
}
