import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import {
  checkLogin,
  checkSuperTokenLogin,
  getLoginType,
  getUserName,
  login,
  loginSuperToken,
  LOGIN_TYPE_COOKIE_NAME,
  LAST_LOGIN_TYPE_COOKIE_NAME,
  cookieOpt,
  SUPER_TOKEN_COOKIE_NAME, isSuperTokenLogin,
} from 'common/utils/auth';
import { computed, ref } from 'vue';
import { logout as userLogout } from 'common/service/user/logout';
import { computedAsync, get } from '@vueuse/core';
import { isArray, union } from 'lodash-es';
import { useRouterStore } from '@/store/global/router.store';
import type { TActionItem } from 'common/service/baseinfo';
import Cookies from 'js-cookie';
import { TRspGetBaseInfoByGame } from 'common/service/baseinfo';

type Privilege = {
  [key: string]: boolean | undefined;
  isAdmin?: boolean;
};

export const useAuthStageStore = defineStore(STORE_KEY.GLOBAL.AUTH, () => {
  const authorized = ref(true);
  const logined = ref(false);
  const currentUser = computedAsync(async () => await getUserName(), '');
  const companyId = ref<number>(1);
  const baseInfo = ref<TRspGetBaseInfoByGame>();

  const setAuthorized = (val: boolean) => {
    authorized.value = val;
  };

  const setLogined = (val: boolean) => {
    logined.value = val;
  };

  async function checkAuthState() {
    if (isSuperTokenLogin()) {
      if (await checkSuperTokenLogin()) {
        setLogined(true);
        return true;
      }
      loginSuperToken();
      return false;
    }
    if (!checkLogin()) {
      login();
    } else {
      setLogined(true);
    }
    return true;
  }

  function logout() {
    // 请求我们的后台登出
    userLogout().then(() => {
      Cookies.set(LAST_LOGIN_TYPE_COOKIE_NAME, getLoginType(), cookieOpt);
      Cookies.remove(LOGIN_TYPE_COOKIE_NAME, cookieOpt);
      Cookies.remove(SUPER_TOKEN_COOKIE_NAME, cookieOpt);
      // 请求yufu的后台登出
      // 回到portal站点
      window.location.href = `https://portal.iam.intlgame.com/logout?return_to=${encodeURIComponent(location.origin)}`;
    });
  }

  // 通过baseInfo获取当前用户的权限详情
  const routerStore = useRouterStore();
  const validGameRbac = computed<TActionItem[]>(() => routerStore.nowRouterActionList);

  const getDimension = (key: string) => {
    const allDimension = get(validGameRbac)
      .map(i => i.dimension?.[key])
      .filter(Boolean) as (string | number)[];
    return union<string | undefined | number>(allDimension);
  };

  const isAdmin = computed(() => get(validGameRbac)
    .filter(i => i.action === 'view')
    .some(i => !i.dimension?.privileges?.length));

  const privilege = computed<Privilege>(() => ({
    isAdmin: get(isAdmin),
    ...Object.fromEntries(getDimension('privileges').map(key => [key, true])),
  }));

  const hasPrivilege = (key: string) => get(privilege)[key] || get(privilege).isAdmin;

  // 检查该游戏下，是否有该角色
  const checkRoleExistByGame = (roleName: string|string[]) => {
    const { role = [] } = baseInfo.value || {};
    if (isArray(roleName)) {
      return role.some(item => roleName.includes(item)) || false;
    }
    return role.includes(roleName) ?? false;
  };

  // 临时提供一个白名单，为adaccounts服务
  const checkWhiteList = () => {
    const whiteList = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ];
    return whiteList.includes(currentUser.value);
  };

  const setBaseInfo = (info: TRspGetBaseInfoByGame) => {
    baseInfo.value = info;
  };

  return {
    checkWhiteList,
    authorized,
    logined,
    setAuthorized,
    setLogined,
    checkAuthState,
    currentUser,
    logout,
    validGameRbac,
    isAdmin,
    getDimension,
    privilege,
    hasPrivilege,
    companyId,
    checkRoleExistByGame,
    baseInfo,
    setBaseInfo,
  };
});
