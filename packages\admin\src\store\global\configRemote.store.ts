import { STORE_KEY } from '@/config/config';
import { CacheMap, CacheMapMode } from '@/util/creative/remoteConfig';
import { createTableData, deleteTableData, getTableData, updateTableData } from 'common/service/remoteConfig';
import { defineStore, storeToRefs } from 'pinia';
import { useAuthStageStore } from './auth.store';
export interface TableConfig {
  tableName: string;
  dataSource: string;
  env?: string;
}
export interface GetConfigParams {
  condition?: Record<string, any>;
  orderBy?: string | Record<string, any>;
  limit?: number;
  page?: number;
}

export const useRemoteConfigStore = defineStore(STORE_KEY.GLOBAL.REMOTE_CONFIG, () => {
  const map = new CacheMap<CacheMapMode.Memory, any>(CacheMapMode.Memory, {
    percent: 10,
  }).instance;
  const authStore = useAuthStageStore();
  const { currentUser } = storeToRefs(authStore);

  const initConfigTable = (tableConfig: TableConfig) => {
    const configTable = new ConfigTable({
      ...tableConfig,
      user: currentUser.value,
    });

    const getBaseCacheKey = (tableConfig: TableConfig) => {
      const { dataSource, tableName, env } = tableConfig;
      return `${[dataSource, tableName, env].filter(Boolean).join('-')}`;
    };
    const getCacheKey = (tableConfig: TableConfig) => {
      const { dataSource, tableName, env, ...rest } = tableConfig;
      return `${getBaseCacheKey(tableConfig)}${rest ? `-${JSON.stringify(rest)}` : ''}`;
    };

    const baseCacheKey = getBaseCacheKey(tableConfig);

    const getConfig = async (params?: GetConfigParams) => {
      const cacheKey = getCacheKey({
        ...tableConfig,
        ...(params ?? {}),
      });

      if (map.map.has(cacheKey)) {
        return map.get(cacheKey);
      }

      const result = await configTable.getData(params);
      map.set(cacheKey, result);
      return result;
    };

    /**
     * 批量更新
     * @param list
     * @returns
     */
    const updateConfig = async (list: Record<string, any>[]) => {
      const result = await configTable.updateData(list);
      map.fuzzyDelete(baseCacheKey);
      return result;
    };

    /**
     * 批量软删除
     * @param ids 需要删除的id数组
     * @returns
     */
    const removeConfig = async (ids: number[]) => {
      const result = await configTable.removeData(ids);
      map.fuzzyDelete(baseCacheKey);
      return result;
    };

    /**
     * 批量创建
     * @param list
     * @returns
     */
    const createConfig = async (list: Record<string, any>[]) => {
      const result = await configTable.createData(list);
      map.fuzzyDelete(baseCacheKey);
      return result;
    };

    // TODO: 批量硬删除
    return {
      getConfig,
      updateConfig,
      removeConfig,
      createConfig,
    };
  };
  return {
    initConfigTable,
  };
});
class ConfigTable {
  private readonly config: TableConfig;
  private readonly currentUser: string;
  constructor(config: TableConfig & { __no_cache?: number; user: string }) {
    const { user, ...rest } = config;
    this.config = rest;
    this.currentUser = user;
  }

  async getData(params?: GetConfigParams) {
    const { limit, page, condition, orderBy } = params ?? {};
    return await getTableData({
      ...this.config,
      limit,
      page,
      condition: condition && JSON.stringify(condition),
      orderBy,
    });
  }

  async updateData(list: Record<string, any>[]) {
    return await updateTableData({
      ...this.config,
      updater: this.currentUser || 'undefined',
      list,
    });
  }

  async createData(list: Record<string, any>[]) {
    return await createTableData({
      ...this.config,
      creator: this.currentUser || 'undefined',
      list,
    });
  }

  async removeData(ids: number[]) {
    return await deleteTableData({
      ...this.config,
      updater: this.currentUser || 'undefined',
      ids,
    });
  }
}
