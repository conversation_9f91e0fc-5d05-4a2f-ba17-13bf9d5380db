import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { useLocalStorage, useStorage } from '@vueuse/core';
import type { GameItem, GameSingleCfg } from '@/components/selectGame/type';
import { flattenDeep, isPlainObject, groupBy, isString, isArray } from 'lodash-es';
import { computed, ref } from 'vue';
import { getLoginType, getUserNameSync } from 'common/utils/auth';

export function getGameIcon(item: Pick<GameSingleCfg, 'icon' | 'code'> | string) {
  const { icon, code } = isString(item) ? { icon: item, code: undefined } : item;
  if (icon?.includes('https://')) return icon;
  return `https://static.aix.intlgame.cn/web/gameicon/${icon || code || 'default'}.png`;
}
const cfgToDrownOptions = (list: GameSingleCfg[]) => list.map(item => ({
  value: item.code,
  connect: item.name,
  icon: getGameIcon(item),
  category: item.category || item.code,
}));

function setValue(target: { value: unknown }, value: unknown) {
  const tempValue = isArray(value) ? value.at(-1) : value;
  // eslint-disable-next-line no-param-reassign
  tempValue && target.value !== tempValue && (target.value = tempValue);
}

enum GameCodeState {
  loading,
  done,
}

export function getGameCodeStorageKeyByLoginType() {
  return `loginType-${getLoginType()}-AiX_GAME_CODE-${getUserNameSync()}`;
}

export const useGlobalGameStore = defineStore(STORE_KEY.GLOBAL.GAME, () => {
  const gameCode = ref<string>('');
  const gameCfg = ref<Record<string, GameSingleCfg> | null>(null);
  const gameList = computed<GameItem[]>(() => (gameCfg.value ? gameCfgToGameList(gameCfg.value) : []));
  const currentGameItem = ref<GameItem>({} as unknown as GameItem);
  const gameCodeState = ref<GameCodeState>(GameCodeState.loading);
  const storageGameCode = useLocalStorage<string>(getGameCodeStorageKeyByLoginType(), '');
  const beforeChangeGameHookMap = ref<Record<string, Function>>({});
  const setGameCodeDone = () => {
    gameCodeState.value = GameCodeState.done;
  };
  const setGameCodeLoading = () => {
    gameCodeState.value = GameCodeState.loading;
  };

  const setBeforeChangeGameHook = (module: string, hook: Function) => {
    beforeChangeGameHookMap.value[module] = hook;
  };
  const isGameCodeDone = computed(() => gameCodeState.value === GameCodeState.done);

  const setGameCode = (newGameCode: string) => {
    setValue(gameCode, newGameCode);
    setValue(storageGameCode, newGameCode);
    if (gameList?.value?.length ?? 0 > 0) {
      setValue(
        currentGameItem,
        flattenDeep(gameList.value!.map(item => item.children || item)).find(item => item.value === newGameCode),
      );
    }
  };

  const setGameCfg = (cfg: Record<string, GameSingleCfg>) => {
    if (isPlainObject(cfg) && Object.keys(cfg).length > 0) {
      gameCfg.value = cfg;
    }
  };
  const gameCfgToGameList = (obj: Record<string, GameSingleCfg>): GameItem[] => {
    const list: GameSingleCfg[] = [];
    Object.keys(obj).forEach((key) => {
      list.push(obj[key]);
    });
    const groupByGameCfg = groupBy(cfgToDrownOptions(list), 'category');
    const tempGameList: GameItem[] = [];
    Object.keys(groupByGameCfg).forEach((key) => {
      const item: any = {
        ...groupByGameCfg[key][0],
      };
      if (groupByGameCfg[key].length > 1) {
        item.code = item.category;
        item.connect = item.category;
        item.children = groupByGameCfg[key].map(i => ({
          ...i,
          parent: item,
          icon: i.icon,
        }));
      }
      tempGameList.push(item);
    });
    return tempGameList;
  };

  const getGameTimezone = (): number => {
    const game = gameCfg.value?.[gameCode.value as string];
    if (game) {
      const reg = /UTC(.*)/.exec(game.timezone as string);
      return reg ? Number(reg[1]) : 0;
    }
    return 0;
  };

  const isPcDemoGame = (game?: string): boolean => isPcGame(game) && isDemoGame(game);
  const isMobileDemoGame = (game?: string): boolean => isDemoGame(game) && isDemoGame(game);
  const isDemoGame = (game?: string): boolean => {
    const dstGame = game || gameCode.value;
    const gameCfgItem = gameCfg.value?.[dstGame];
    return gameCfgItem?.ext1 === 'demo';
  };
  const isPcGame = (game?: string): boolean => gameCfg.value?.[(game || gameCode.value) as string]?.type === 'pc';
  const isMobileGame = (game?: string): boolean => {
    const gameType = gameCfg.value?.[(game || gameCode.value) as string]?.type;
    // 没有gameType 或者 gameType等于mobile 就是mobile游戏
    return !gameType || gameType === 'mobile';
  };
  const newFunctionRed = useStorage('newFunctionRed', JSON.stringify({}));
  const redResult = JSON.parse(newFunctionRed.value);

  const showFunctionRed = (reportId: string, showNewFunctionRed: boolean | unknown) => !(reportId in redResult) && showNewFunctionRed && gameCode.value !== 'pubgm' && gameCode.value;

  const clearFunctionRed = (reportId: string, showNewFunctionRed: boolean | unknown) => {
    // 消除新增功能红点
    // 暂时不添加pubgm
    if (showNewFunctionRed && gameCode.value !== 'pubgm') {
      redResult[reportId] = true;
      newFunctionRed.value = JSON.stringify(redResult);
    }
  };

  return {
    gameCfg,
    gameCode,
    gameList,
    currentGameItem,
    storageGameCode,
    setGameCode,
    setGameCfg,

    setGameCodeDone,
    setGameCodeLoading,
    isGameCodeDone,
    getGameTimezone,

    isDemoGame, // add by euphrochen@626新增isDemoGame方法
    setBeforeChangeGameHook,
    // 新功能红点展示和红点消除
    showFunctionRed,
    clearFunctionRed,
    newFunctionRed,
    isPcGame,
    isMobileGame,
    isPcDemoGame,
    isMobileDemoGame,
  };
});
