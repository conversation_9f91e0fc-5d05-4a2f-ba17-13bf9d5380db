import { reactive } from 'vue';
import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import type { IAccountItem } from './media-account.d';
import { getAccountList } from 'common/service/media_account/get';
import { useGlobalGameStore } from './game.store';
import { StringKeyAnyValueObject } from 'common/types/report';
import { MediaType } from '@/views/trade/ads_creation/common/template/type';

interface IAccountObj extends StringKeyAnyValueObject {
  google: IAccountItem[],
  facebook: IAccountItem[],
  tiktok: IAccountItem[],
  twitter: IAccountItem[],
  asa: IAccountItem[],
}

export const useGlobalMediaAccountStore = defineStore(STORE_KEY.GLOBAL.MEDIA_ACCOUNT, () => {
  const accountObj = reactive<IAccountObj>({
    google: [],
    facebook: [],
    tiktok: [],
    twitter: [],
    asa: [],
  });

  async function setAccountList({ media }: { media: MediaType }) {
    const gameStore = useGlobalGameStore();
    const { gameCode } = gameStore;
    accountObj[media.toLowerCase()] = await getAccountList({ gameCode, media });
  }

  return { accountObj, setAccountList };
});
