import { defineStore, storeToRefs } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { computed, ref, shallowReactive, shallowRef } from 'vue';
import { getBaseInfoByGame, type TRspGetBaseInfoByGame, type TActionItem } from 'common/service/baseinfo';
import { useRoute, type RouteRecordNormalized, type RouteLocationNormalizedGeneric } from 'vue-router';
import { useGlobalGameStore } from './game.store';
import type { RouteRecordDynamic } from '@@/router';
import { notFoundRoute, MENU_LEVEL_SPLIT_FLAG, refreshRoute } from '@/router';
import { useStorage } from '@vueuse/core';
import { getUrlHiddenParams, getFallbackAiXVersion, getQuery } from 'common/utils/url';
import { useRoutesTipsStore } from '@/store/global/routes-tips.store';
import { useEnv } from 'common/compose/env';

enum PageLoadState {
  unload,
  loading,
  error,
}

enum RouterLoadState {
  loading,
  done,
  selectGame,
}

export const genBaseInfoByGameParams = (game?: string) => {
  const { gameCfg, gameCode } = storeToRefs(useGlobalGameStore());

  return {
    game: game || gameCode.value,
    is_get_allow_game_cfg: !gameCfg.value ? 1 : 0,
    split_flag: MENU_LEVEL_SPLIT_FLAG,
    version_path: getFallbackAiXVersion(),
    pass_query: getQuery(getUrlHiddenParams()) as string,
  };
};

export const useRouterStore = defineStore(STORE_KEY.GLOBAL.ROUTER, () => {
  const { isFuncom } = useEnv();
  const route = useRoute();

  // ----------- pageState ------------
  const pageState = ref<PageLoadState>(PageLoadState.unload);
  const setPageLoading = (to: RouteLocationNormalizedGeneric, from: RouteLocationNormalizedGeneric) => {
    // 在同一个页面在切换路由是不显示loading
    if (to?.name && from?.name && to.name === from.name) return;
    pageState.value = PageLoadState.loading;
  };
  const setPageErr = () => {
    pageState.value = PageLoadState.error;
  };
  const setPageLoadEnd = () => {
    pageState.value = PageLoadState.unload;
  };
  const isPageLoading = computed(() => pageState.value === PageLoadState.loading);
  const isPageLoadErr = computed(() => pageState.value === PageLoadState.error);

  // ----------- routerState ------------
  const routerState = ref<RouterLoadState>(RouterLoadState.done);
  const setRouterNeedSelectGame = () => {
    routerState.value = RouterLoadState.selectGame;
  };
  const setRouterLoading = () => {
    routerState.value = RouterLoadState.loading;
  };
  const setRouterDone = () => {
    routerState.value = RouterLoadState.done;
  };
  const isRouterNeedSelectGame = computed(() => routerState.value === RouterLoadState.selectGame);
  const isRouterLoading = computed(() => routerState.value === RouterLoadState.loading);
  const isRouterDone = computed(() => routerState.value === RouterLoadState.done);

  // ----------- routeState ------------
  const gameCode = ref('');
  const setRouterGame = (game: string) => gameCode.value !== game && (gameCode.value = game);

  const firstRouteName = ref<string>();
  const firstRoute = computed<RouteRecordNormalized | undefined>(() => {
    const route = allRoutes.value.find(item => item?.name?.toString() === firstRouteName.value);
    return route;
  });
  const setFirstRoute = (route: RouteRecordNormalized) => {
    firstRouteName.value = route?.name?.toString();
  };

  const gameStore = useGlobalGameStore();
  const { gameCfg } = storeToRefs(gameStore);
  const { isDemoGame } = gameStore;

  const allowRoutes = shallowReactive<{ [game: string]: Array<string> }>({});
  const nowGameAllowRoutes = computed<Array<string>>(() => allowRoutes[gameCode.value] || []);

  const dynamicRoutes = shallowReactive<{ [game: string]: Array<RouteRecordDynamic> }>({});
  const nowGameDynamicRoutes = computed<Array<RouteRecordDynamic>>(() => dynamicRoutes[gameCode.value] || []);

  const routesTipsStore = useRoutesTipsStore();
  const allRoutesInner = shallowRef<RouteRecordNormalized[]>([]);
  const setAllRoutes = (list: RouteRecordNormalized[]) => {
    allRoutesInner.value = list;
  };
  const allRoutes = computed<RouteRecordNormalized[]>(() => {
    const allRoutesWithTipsList = routesTipsStore.attachTipsCfg(allRoutesInner.value);
    return allRoutesWithTipsList;
  });

  const isNowRouterAllow = computed(() => {
    const currentRouteName = route?.name?.toString();
    return (
      notFoundRoute.name === currentRouteName
      || !!(currentRouteName && nowGameAllowRoutes.value.includes(currentRouteName))
    );
  });
  const checkNowGameIsNeedLoad = (gameCode: string) => !allowRoutes[gameCode] || isRouterNeedSelectGame.value;

  const actionList = shallowReactive<{ [game: string]: Record<string, Array<TActionItem>> }>({});
  const nowRouterActionList = computed<Array<TActionItem>>(() => {
    const l1ParentName = route?.meta?.parent?.[0];
    return l1ParentName ? actionList?.[gameCode.value]?.[l1ParentName] || [] : [];
  });

  const getRouterGameCode = (isFirstInit: boolean, params: TRspGetBaseInfoByGame): string | undefined => {
    const { game: legalGame, actions, dynamic_routes: dynamicRoutesFrFetch, allow_games: allowGames, role } = params;
    let { allow_routes: allowRoutesFrFetch } = params;
    // routesTips 初始化
    const routesTipsStore = useRoutesTipsStore();
    routesTipsStore.init();

    if (isFirstInit && allowGames) {
      gameStore.setGameCfg(allowGames);
    }
    // sop这里做个特殊处理，区分日本和非日本区域
    if (legalGame === 'sop') {
      const right = actions?.['Trading Desk']?.find?.((item: TActionItem) => item.dimension?.game === legalGame && !!item.dimension?.accounts);
      if (right) {
        const validMedias = Object.keys(right?.dimension?.accounts || {});
        allowRoutesFrFetch = allowRoutesFrFetch.filter((path) => {
          if (path.includes('Trading Desk / Manage Ads /')) {
            return validMedias.some(media => path.includes(media));
          }
          return true;
        });
      }
    }

    // TODO gg v1路由 等website类型开发后删除
    if (allowRoutesFrFetch.includes('Trading Desk / Manage Ads / Google / Create')) {
      allowRoutesFrFetch.push('Trading Desk / Manage Ads / Google / Create_v1');
    }

    const onlineGames = ['dl2'];
    if (role?.includes('creative-tools') || (isFuncom.value && isDemoGame(legalGame))
      || onlineGames.includes(legalGame)) {
      allowRoutesFrFetch.push('Creative Center / AI Toolkit');
      allowRoutesFrFetch.push('Creative Center / AI Toolkit / Smart Copywriter');
      // 特殊逻辑：poe2暂时不展示该入口
      if (!['poe2'].includes(legalGame)) {
        allowRoutesFrFetch.push('Creative Center / AI Toolkit / Advanced Video');
      }
      allowRoutesFrFetch.push('Creative Center / AI Toolkit / Face Swapping');
      allowRoutesFrFetch.push('Creative Center / AI Toolkit / Smart Video');
      allowRoutesFrFetch.push('Creative Center / AI Toolkit / Smart Video Editor');
      allowRoutesFrFetch.push('Creative Center / AI Toolkit / Lip Sync');
      allowRoutesFrFetch.push('Creative Center / AI Toolkit / Video Dubbing');
    }

    if (legalGame) {
      allowRoutes[legalGame] = allowRoutesFrFetch;
      dynamicRoutes[legalGame] = dynamicRoutesFrFetch;
      actionList[legalGame] = actions;
      // setRouterGame(legalGame);
      return legalGame;
    }
    return;
  };
  // ----------- Get route config ------------
  const initGameRouter = async (game: string): Promise<string | undefined> => {
    const hasInit = !!allowRoutes[game];
    if (hasInit) {
      // setRouterGame(game);
      return game;
    }

    const isFirstInit = !gameCfg.value;

    const params = await getBaseInfoByGame(genBaseInfoByGameParams(game), undefined, (data: TRspGetBaseInfoByGame) => {
      const routerGameCode = getRouterGameCode(isFirstInit, data);
      if (routerGameCode) refreshRoute(routerGameCode);
    });
    return getRouterGameCode(isFirstInit, params);
  };
  // ----------- sidebar and secondLevelSideBar collapsed  ------------
  const collapsed = useStorage('menu.collapsed', false, localStorage);
  const setCollapsed = (val: boolean) => {
    collapsed.value = val;
  };
  const showCollapsed = () => {
    collapsed.value = false;
  };
  const hideCollapsed = () => {
    collapsed.value = true;
  };
  const toggleCollapsed = () => {
    collapsed.value = !collapsed.value;
  };
  const isShowSecondMenu = useStorage('menu.isShowSecondMenu', true, localStorage);
  const setIsShowSecondMenu = (val: boolean) => {
    isShowSecondMenu.value = val;
  };

  // ----------- intelligence-module-methods  ------------
  const isIntelligenceRouteModule = (): boolean => firstRouteName.value === 'Intelligence';

  // 当前是不是在内网环境中
  const isIOAEnvironment = ref<boolean>(false);
  const setIOAEnvironment = (val: boolean) => isIOAEnvironment.value = val;

  // 当路由正在loading时，阻止本次跳转
  const onRouterClick = (event: any) => {
    if (isPageLoading.value) {
      event.preventDefault();
      event.stopImmediatePropagation();
      console.info('router is loading, please wait...');
    }
  };

  return {
    route,
    gameCode,
    allRoutes,
    firstRoute,
    nowGameDynamicRoutes,
    nowGameAllowRoutes,
    isNowRouterAllow,
    checkNowGameIsNeedLoad,
    initGameRouter,
    setAllRoutes,

    setRouterGame,
    setRouterLoading,
    setRouterDone,
    setRouterNeedSelectGame,
    setFirstRoute,
    isRouterLoading,
    isRouterDone,
    isRouterNeedSelectGame,

    nowRouterActionList,

    setPageLoading,
    setPageErr,
    setPageLoadEnd,
    isPageLoading,
    isPageLoadErr,

    setCollapsed,
    showCollapsed,
    hideCollapsed,
    toggleCollapsed,
    collapsed,

    setIsShowSecondMenu,
    isShowSecondMenu,

    allowRoutes,
    firstRouteName,
    isIntelligenceRouteModule,
    isIOAEnvironment,
    setIOAEnvironment,
    onRouterClick,
  };
});
