export interface ConfigMongoType {
    name: string, //
    desc: string, //
    reason: string, //
    config: any,//
};

export interface RoutesTipsType {
    enable?: boolean,
    startTime?: string,
    endTime?: string,
    style: string,
    activeType: string,
    expireTs: string,
    isShowNewTip?: boolean,
    isShowRedPointTip?: boolean,
    // routes: Array<{ name: string, id: string }>,
}
