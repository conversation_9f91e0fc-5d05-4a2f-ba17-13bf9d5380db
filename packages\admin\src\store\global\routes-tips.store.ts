import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { computed, ref } from 'vue';
import { getRoutesTips, TRoutesTips } from 'common/service/mongodb/configs/get-routes-tips';
import { RouteRecordNormalized, RouteRecordRaw } from 'vue-router';
import { RoutesTipsType } from './routes-tips';
import { useStorage as useLocalStorage } from '@vueuse/core';
import { isPlainObject, isString } from 'lodash-es';
import { useEnv } from 'common/compose/env';

export const useRoutesTipsStore = defineStore(STORE_KEY.GLOBAL.ROUTES_TIPS, () => {
  const readRouteNameRecordStorage = useLocalStorage<{ [routeName: string]: string }>('routes_tips', {}); // 路由提示是否已读
  const readRouteNameList = computed<string[]>(() => {
    const record = getActiveRecord(readRouteNameRecordStorage.value);
    return Object.keys(record);
  });

  const allRoutesTipsCfg = ref<TRoutesTips>({}); // 所有的路由提示列表
  const routesTipsCfg = computed<TRoutesTips>(() => {
    const result: TRoutesTips = {};
    const cfg = allRoutesTipsCfg.value;
    Object.keys(cfg ?? {}).forEach((routeName) => {
      const hasRead = readRouteNameList.value?.includes(routeName);
      const nowCfg = cfg[routeName];
      !hasRead && (result[routeName] = {
        ...nowCfg,
        isShowNewTip: nowCfg?.style === 'new',
        isShowRedPointTip: nowCfg?.style === 'redPoint',
      });
    });
    return result;
  });

  /**
   * 点击路由更新本地已读路由列表
   * @param routeName 路由名称
   */
  const updateReadRouteName = async (routeName: string | RouteRecordRaw) => {
    const name = isString(routeName) ? routeName : routeName?.name?.toString();
    if (!name) return;
    // 找出点击的路由是否有红点过期规则
    const { expireTs } = routesTipsCfg.value[name] || {};
    if (expireTs) {
      readRouteNameRecordStorage.value[name] = expireTs;
    }
  };
  // 清空下多余的过期规则
  const cleanReadRouteNameRecordStorage = () => {
    const record = getActiveRecord(readRouteNameRecordStorage.value);
    Object.keys(record).length > 0 && (readRouteNameRecordStorage.value = record);
  };
  const getActiveRecord = (readRouteNameRecordStorage: Record<string, string>) => {
    const record: Record<string, string> = {};
    Object.keys(readRouteNameRecordStorage).forEach((routeName) => {
      const expireTs = readRouteNameRecordStorage[routeName];
      if (new Date().getTime() < +expireTs) {
        record[routeName] = expireTs;
      }
    });
    return record;
  };

  /**
   * 从所有一级路由遍历children路由，判断是否有未读提示，并将未读提示配置标记到meta上，如果chilrenItem子属性有childre,一直递归遍历下去；
   * @param list
   * @returns
   */
  const attachTipsCfg = (allRoutesList: RouteRecordNormalized[]): RouteRecordNormalized[] => {
    if (Object.keys(routesTipsCfg.value).length === 0) {
      return allRoutesList;
    }
    const overWriteTipsCfg = (
      targetRouteName: string,
      parentRouteNameList: string[],
      tipsCfg: RoutesTipsType,
      rootRouteCfg: RouteRecordNormalized | RouteRecordRaw,
      isFirst: boolean,
    ) => {
      const isRootToSelf = tipsCfg.activeType === 'root-to-self';
      const children: typeof rootRouteCfg.children = (rootRouteCfg?.children ?? [])
        .map((routeCfg: RouteRecordRaw): RouteRecordRaw => {
          const nowRouteName = routeCfg?.name?.toString() || '';
          const isBingo =  nowRouteName === targetRouteName;
          const isParent = parentRouteNameList.includes(nowRouteName);
          if (isBingo || (isRootToSelf && isParent)) {
            return {
              ...routeCfg,
              meta: {
                ...(routeCfg?.meta ?? {}),
                tipsCfg,
              },
              children: rootRouteCfg.children
                ? overWriteTipsCfg(targetRouteName, parentRouteNameList, tipsCfg, routeCfg, false)
                : undefined,
            } as any;
          }
          return routeCfg;
        });
      return isFirst ? children[0] : children;
    };
    let fristRouteList: RouteRecordNormalized[] = [];
    const otherRouteList: RouteRecordNormalized[] = [];
    allRoutesList
      ?.forEach((item) => {
        item?.meta?.level === 1
          ? (fristRouteList.push(item))
          : (otherRouteList.push(item));
      });

    let firstRouteCfgByName: {
      [firstRouteName: string]: RouteRecordNormalized;
    } = fristRouteList?.reduce((record, item) => {
      const key = item?.name?.toString() || '';
      return {
        ...record,
        [key]: item,
      };
    }, {});

    allRoutesList.forEach((routeItem: RouteRecordNormalized) => {
      const nowRouterName = routeItem?.name?.toString();
      const tipsCfg = nowRouterName ? routesTipsCfg.value[nowRouterName] : undefined;
      if (!tipsCfg && isPlainObject(routeItem?.meta)) {
        // eslint-disable-next-line no-param-reassign
        // delete routeItem.meta.tipsCfg;
        return;
      }

      const isFirstRoute = routeItem.meta.level === 1;
      let newFristRoute: RouteRecordRaw;
      if (isFirstRoute) {
        newFristRoute =  overWriteTipsCfg(
          nowRouterName!,
          [],
          tipsCfg!,
          { children: [] } as unknown as RouteRecordNormalized,
          true,
        ) as RouteRecordRaw;
      } else {
        const firstRouteName = routeItem?.meta?.parent?.[0];
        const firstRouteCfg = firstRouteCfgByName[firstRouteName!];
        const parentRouteNameList = routeItem?.meta?.parent ?? [];
        newFristRoute = overWriteTipsCfg(
          nowRouterName!,
          parentRouteNameList,
          tipsCfg!,
          { children: [firstRouteCfg] } as unknown as RouteRecordNormalized,
          true,
        ) as RouteRecordRaw;
      }

      const firstRouteName = newFristRoute?.name?.toString();
      if (firstRouteName) {
        firstRouteCfgByName = {
          ...firstRouteCfgByName,
          [firstRouteName]: newFristRoute,
        } as any;
        fristRouteList = Object.values(firstRouteCfgByName) as RouteRecordNormalized[];
      }
    });

    return fristRouteList.concat(otherRouteList);
  };


  /**
   * 获取路由规则列表
   */
  const init = async () => {
    // funcom的红点后台服务还没发布，暂时屏蔽
    const env = useEnv();
    if (env.getIsFuncom()) return;

    cleanReadRouteNameRecordStorage();
    allRoutesTipsCfg.value = await getRoutesTips();
  };
  return {
    routesTipsCfg,
    updateReadRouteName,
    attachTipsCfg,
    init,
  };
});
