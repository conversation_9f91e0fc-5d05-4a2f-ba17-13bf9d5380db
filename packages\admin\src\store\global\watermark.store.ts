import { STORE_KEY } from '@/config/config';
import { defineStore, storeToRefs } from 'pinia';
import { computed, ref } from 'vue';
import { base64ToArrayBuffer, decryptMessage, importDecryptKey } from 'common/utils/string';
import { useAuthStageStore } from '../global/auth.store';
import { useEnv } from 'common/compose/env';

const decryptKey = 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCo2tLlgkXIdGnrbCpJF54LigdK4uLS6TzxOonPGGFwNqYAh4+kFh2kgYjKhIU4zFK7F0toE9DVfGbPmDJ15IhNQ4SziE+RP9IIFfu2o8ygIC487fOYO+nE2qYoE93myJvMORyKw7a1Y/2NXQzguZqrtcTU6uG1FnAAGNAs23ngpitG+S3wo9MhSNM/m6ewso0l4ZouOL+DuJR0X6J5pTEQ1wEdHs3uHu970+UA9hytjNOUuS+Vg7GJ1ptpU1PN1RMavmWOGvu7waGGCEHwgzTfwTiovF8w1zrzsKvZHsms7iwABjL9ndV4H2up04XJdRJayJ/P8QBZ5b/IiXkhssD/AgMBAAECggEALD2hMhbu17WApvkhHyN7+kQA4yMNEPuhRwbyYntd1DvNRKg//1r4C0kRIRaVnSsTpgdVb4B5n+XHPi7/UZo+CBhfpXZaepEBI7M2z0SdcXrdAoPJ5iIUfspW7HtAEZ6QC3MPQmBC98WqAibWH+8hUkn+t1aPKpYlZDEcIhSjGzOhfoOhv0OSHDGH0mnz+a1jMAgNN9zien3oVLbhtCxI7IBqt+TP8y2eGw3GbNzI/71F0BaUqFOubsXRyb86pEddi/9Y2uVwBeeJk/dC8UVukax3Xkid8EQWtLsXci1H9p7zzppVakn8epC1vsUnd5vD7GjAWrFuRxKEe/ZkMQzhWQKBgQDTWgma8fjnTf17T341GxJVMlwyaDnPi5ROCxofgbrGnorBx4BOCAqt2dTpdxfhFL2EIFMfjbj8lSmj5hGZqBU2bl436mCRPvNWLUKQa9W4cVYr1kwZ2XrG0OzyP9ws6RWJkHZIthMQF073n+mX4FZwVILZkS1iZu17JOw3+pT7cwKBgQDMhoo4SnWlcbTO/GsOq7rzwHwJ9AhqETZvyfebSLEqEr3sle2UGbM13BUsunAiCQolu/wy2BBCoLwqX5CPM87zznV8OHRgF0h3GMVaFbSZl2rAVBJAlkqA+Zqto0SjS+uugPGc1WTU2GkKcnD4YVOJWQWFT3rr/U4AK6UBO2BZRQKBgFcsQN/2w9nrkgyFSiHvS412Ww6zbHzNLltOOvUZ1gbMJ6ErfyEwMe9o9GdjJqdAnbZcesx98PnFMEqYv1OoPy+XMCdR9YiS9VzrCBR3VcxgetFT70mOKRTN4Z09d7vSW37E5fSxnIU7h+ecvoVVE3H7eYjxV03J9FOhdAjWwxI/AoGAVNTtGcIQoAnUtBHQmeozBg1tpXc50jzHdoCwGhGBonDZo/rf6xDp20oo4j+GaSeUtOgPCEvEGs6unWi7/hnAGjIlWtvTsVOW9AcrPF1KZnZ+ib2VQILV7j8ZoDHbM1w3Kk9DaAi+5OXKU9H595vu2UY6WvwHgUC0jK+NpkApi30CgYEArSpC58TYhx0eYn17aDXk9Q7WkOw1i4XqX3uWthQVIlW0hRwtpSnMtTiVSo2LWfseXzVkAqyt9Q/Kdqp0lESFC+cMGUBrNfPg3sZ/bawMMXKsBL5qKYq97SktKebAU4owQc9TpyZKmkd5fSHESdLc48y0W4slbW4MFf4ABMzHkKo=';
const expireTime = 3600000;

export const useWaterMarkContentStore = defineStore(STORE_KEY.GLOBAL.WATER_MARK_CONTENT, () => {
  const authStore = useAuthStageStore();
  const { currentUser } = storeToRefs(authStore);
  const watermark = ref<string>('');
  const { getIsDev } = useEnv();
  const watermarkContent = computed<string>(() => watermark.value || currentUser.value || '');
  // 解密水印内容
  const getWatermark = async () => {
    const queryString = location.search;
    const searchParams = new URLSearchParams(queryString);
    const searchParamsWatermark: string | null = searchParams.get('watermark_content');

    if (searchParamsWatermark) {
      if (getIsDev()) {
        watermark.value = searchParamsWatermark;
        return;
      }
      // 导入私钥
      const decrypKey = await importDecryptKey(decryptKey);
      // 对Base64格式水印解码得到ArrayBuffer格式水印
      const watermarkContentBuffer = base64ToArrayBuffer(searchParamsWatermark);
      // 使用私钥进行解密
      const decryptContent = await decryptMessage(decrypKey, watermarkContentBuffer);
      const { username, encryptTime } = decryptContent;
      const currentTime = Date.now();
      if ((currentTime - encryptTime) < expireTime) {
        watermark.value = username;
      }
    }
  };

  getWatermark().catch(error => console.error('decrypt Watermark fail', error));

  return {
    watermarkContent,
    watermark,
  };
});
