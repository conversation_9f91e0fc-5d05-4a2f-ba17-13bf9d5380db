import { defineStore, storeToRefs } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { reactive, ref, computed } from 'vue';
import { cloneDeep } from 'lodash-es';
import { useLoading } from 'common/compose/loading';
import { useWatchGameChange } from 'common/compose/request/game';
import { KolFormParams } from '@/views/influencer/campaignSetUp/modal/kolManage';
import {
  GetKOLManageTableResponseModal,
  KolManageUploadRequestModal,
  RemoveKolManageRecordRequestModal,
} from 'common/service/influencer/campaignSetUp/kolManage.d';
import {
  getKOLManageTableData,
  uploadKolManageExcelData,
  editKolManageExcelData,
  removeKolManageRecord,
  viewKolUploadRecord,
  generateTrackingLinkReq,
  checkChannelReq,
  checkUniqueReq,
  // getLanguageReq,
  getChannelNamesReq,
  getContentReq,
} from 'common/service/influencer/campaignSetUp/kolManage';
import { getSelectOptions, type TSelectOptions } from 'common/service/influencer/common/selectOptions';
import {
  KOL_DEFAULT_FILTER,
  FORMAT_OPTS,
  PLATFORM_OPTS,
  // CONTENT_OPTS,
  RECORD_STATUS_OK,
  TASK_STATUS_OPTIONS,
} from '@/views/influencer/campaignSetUp/const/const';
// import { useGlobalGameStore } from '@/store/global/game.store';
import { useAuthStageStore } from '@/store/global/auth.store';
import { OptionsItem } from 'common/components/Cascader';
import { toOption } from '@/store/intelligence/market/common';
import dayjs from 'dayjs';
import { TableProps } from 'tdesign-vue-next';

export const SYSTEM = 'influencer_campaign_setup';

export const useConfigurationManagementKolStore = defineStore(STORE_KEY.INFLUENCER.CAMPAIGN_SETUP, () => {
  const { isLoading, hideLoading, showLoading } = useLoading();
  // const gameStore = useGlobalGameStore();

  const viewLoading = ref(false);

  // 当前选择的游戏业务
  // const { gameCode } = gameStore;
  // baseinfo_by_game 接口调用，内含 user、role、allow_routers 等信息
  // const { baseInfo } = storeToRefs(useAuthStageStore());

  // 原本共用页面时，用来判断时 spend/cpa 哪个页面，如何读取配置等等
  const managementType = ref('');

  // 是否是管理员
  const isAdmin = ref(false);

  // 默认根据publish_date倒序排序
  const tableSort = ref<TableProps['sort']>({
    sortBy: 'publish_date',
    descending: true,
  });

  // 表格内容
  const table = reactive({
    records: [] as GetKOLManageTableResponseModal['list'],
    filter: {
      country: [] as string[],
      format: [] as string[],
      campaign: [] as string[],
      taskStatus: [] as string[],
      channel: [] as string[],
      platform: [] as string[],
      content: [] as string[],
    },
    order: tableSort.value,
    pageInfo: {
      pageSize: 10,
      pageIndex: 1,
      total: 0,
    },
  });

  // 接口调用传递的值
  const option = reactive({
    payload: {
      countryInputList: [],
      countryOptionList: [],
      formatInputList: [],
      formatOptionList: [],
      campaign: '',
      channelInputList: [],
      channelOptionList: [],
      platformInputList: [],
      platformOptionList: [],
      contentInputList: [],
      contentOptionList: [],
      taskStatusInputList: [],
      taskStatusOptionList: [],
    },
  });

  // 当前选择搜索条件
  const condition = reactive<{ cur: KolFormParams; default: KolFormParams }>({
    cur: cloneDeep(KOL_DEFAULT_FILTER),
    default: cloneDeep(KOL_DEFAULT_FILTER),
  });

  const isFileValidOpitionsLoading = ref<boolean>(true);
  // 页面初始化调用，CommanView.tryOnBeforeMount() 组件挂载前调用
  // 在这个方法中，需要初始化：
  //    1. 表单选项初始化
  //    2. 依赖的一些选项的初始化配置
  //    3. 表格初始化(分页)
  const init = async () => {
    // 监听游戏业务的选择，改变游戏业务，重新初始化页面
    useWatchGameChange(async () => {
      try {
        // 重新初始化form表单筛选项
        condition.cur.country = [];
        condition.cur.format = [];
        condition.cur.campaign = '';
        condition.cur.channel = [];
        condition.cur.platform = [];
        condition.cur.content = [];
        condition.cur.taskStatus = [];
        table.records = [];
        table.filter = {
          country: [] as string[],
          format: [] as string[],
          campaign: [] as string[],
          channel: [] as string[],
          platform: [] as string[],
          content: [] as string[],
          taskStatus: [] as string[],
        };
        tableSort.value = { sortBy: 'publish_date', descending: true };
        table.order = tableSort.value;
        showLoading();

        // 获取 countryOptions 和 formats
        isFileValidOpitionsLoading.value = true;
        await Promise.all([getCountryOptions(), getChannelNames(), getContents(), getLanguageOptions()]).finally(() => {
          isFileValidOpitionsLoading.value = false;
        });

        option.payload = {
          countryOptionList: countryOptions.value as any,
          countryInputList: [],
          formatOptionList: toOption(
            FORMAT_OPTS.map(f => ({ format: f })),
            'format',
          ),
          formatInputList: [],
          taskStatusOptionList: TASK_STATUS_OPTIONS as any,
          taskStatusInputList: [],
          campaign: '',
          channelOptionList: channelOptions.value as any,
          channelInputList: [],
          platformOptionList: toOption(
            PLATFORM_OPTS.map(f => ({ platform: f })),
            'platform',
          ),
          platformInputList: [],
          contentOptionList: contentOptions.value as any,
          contentInputList: [],
        };

        // 获取table列表
        const kolManageTable = await getKolManageTable({ pageNum: table.pageInfo.pageIndex ?? 1, pageSize: table.pageInfo.pageSize ?? 10, status: RECORD_STATUS_OK, filter: table.filter as unknown as { [key: string]: string[] }[], order: table.order });
        if (Array.isArray(kolManageTable?.list) && !isNaN(parseInt(kolManageTable.totalRecord, 10))) {
          table.records = kolManageTable.list;
          table.pageInfo.total = parseInt(kolManageTable.totalRecord, 10);
        }
        // 对是否是管理员进行赋值
        const isAdminRaw = kolManageTable?.isAdmin;
        typeof isAdminRaw === 'boolean' ? (isAdmin.value = isAdminRaw) : (isAdmin.value = false);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        hideLoading();
      }
    });
  };

  function setManagementType(v: string) {
    managementType.value = v;
  }

  // 获取region/country校验的options
  const countryOptions = ref<TSelectOptions[]>([]);
  async function getCountryOptions() {
    const list = await getSelectOptions({
      system: SYSTEM,
      field: 'region',
      sub_field: 'country_code',
    });
    countryOptions.value = list;
  }
  const countryValues = computed(() => {
    const values: string[] = [];
    countryOptions.value.forEach((item) => {
      item?.children?.forEach((item2) => {
        values.push((item2?.value as string) ?? '');
      });
    });
    return values;
  });

  const countryFlatOptions = computed(() => {
    const newOptions: OptionsItem[] = [];
    countryOptions.value.forEach((item) => {
      item?.children?.forEach((item2) => {
        newOptions.push({
          label: (item2?.label as string) ?? '',
          value: (item2?.value as string) ?? '',
        });
      });
    });
    return newOptions.sort((a, b) => a.label.localeCompare(b.label));
  });

  // 查询全量的language
  const languageOptions = ref<TSelectOptions[]>([]);
  const languageValues = computed(() => {
    const values = languageOptions.value?.map(item => item.value) ?? [];
    return values;
  });
  async function getLanguageOptions() {
    const list = await getSelectOptions({
      system: SYSTEM,
      field: 'fix_language',
    });
    languageOptions.value = list ?? [];
  }

  // 查询全量的channel name
  const channelNamesAll = ref<{ name: string }[]>([]);
  async function getChannelNames() {
    channelNamesAll.value = await getChannelNamesReq();
  }
  const channelOptions = computed(() => channelNamesAll.value?.map((item) => {
    const newItem: OptionsItem = {
      label: item.name,
      value: item.name,
    };
    return newItem;
  }),
  );

  // 查询全量的content
  const contentsAll = ref<{ content: string }[]>([]);
  async function getContents() {
    contentsAll.value = await getContentReq();
  }
  const contentOptions = computed(() => contentsAll.value?.map((item) => {
    const newItem: OptionsItem = {
      label: item.content,
      value: item.content,
    };
    return newItem;
  }));

  async function getKolManageTable({
    pageSize,
    pageNum,
    status,
    filter,
    order,
  }: {
    pageSize?: number;
    pageNum?: number;
    status?: number;
    filter?: { [key: string]: string[] }[];
    order?: TableProps['sort'];
  }) {
    const data = await getKOLManageTableData({ pageSize, pageNum, status, filter, order });

    // 调整日期格式
    if (data?.list?.length > 0) {
      data.list.forEach((row) => {
        // eslint-disable-next-line no-param-reassign
        row.publish_date = formatDateTime(row.publish_start_date);
      });
    }
    return data ?? { list: [] };
  }

  // 获取过滤数据
  const getFilterData = async () => {
    showLoading();
    const { pageSize, pageIndex } = table.pageInfo;
    const getKOLManageTableParams: any = {
      pageNum: pageIndex,
      pageSize,
      filter: table.filter,
      order: table.order,
    };

    const res = await getKolManageTable(getKOLManageTableParams);
    if (Array.isArray(res?.list) && !isNaN(parseInt(res.totalRecord, 10))) {
      table.records = res.list;
      table.pageInfo.total = parseInt(res.totalRecord, 10);
    }
    hideLoading();
  };

  // 换日期格式
  function formatDateTime(dateTimeString: string): string {
    return dateTimeString ? dayjs(dateTimeString).format('YYYY-MM-DD') : '-';
  }

  function getCurrentUsername() {
    const { baseInfo } = storeToRefs(useAuthStageStore());
    return baseInfo.value?.user;
  }

  // 确认是否是utf-8 csv文件
  async function isValidUTF8(content: string, fileType: string) {
    const excelMimeTypes = ['text/csv', 'application/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];

    if (!excelMimeTypes.includes(fileType)) {
      return false;
    }

    try {
      const byteArray = new TextEncoder().encode(content);

      const decoder = new TextDecoder('utf-8', { fatal: true });
      const decodedContent = decoder.decode(byteArray);
      if (decodedContent !== content) {
        return false;
      }

      if (decodedContent.includes('\uFFFD')) {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  // 上传文件
  async function uploadExcelData(params: KolManageUploadRequestModal) {
    const data = await uploadKolManageExcelData(params);
    return data;
  }

  async function editExcelData(params: KolManageUploadRequestModal) {
    const data = await editKolManageExcelData(params);
    return data;
  }

  // 删除记录
  async function removeRecord(params: RemoveKolManageRecordRequestModal) {
    const data = await removeKolManageRecord(params);
    return data;
  }

  async function viewKolRecord(params: { id: string }) {
    viewLoading.value = true;
    const res = await viewKolUploadRecord(params);
    viewLoading.value = false;
    return res;
  }

  async function generateTrackingLink(params: {
    id: string | number;
    destination_link: string;
    platform?: string;
    type?: string;
  }) {
    const data = await generateTrackingLinkReq(params);
    return data;
  }

  async function checkChannel(params: { channel_link: string; platform: string }) {
    const data = await checkChannelReq(params);
    return data;
  }

  async function checkUnique(
    params: { channel_id: string; deliverable_link: string; format: string; publish_date: string }[],
  ) {
    const data = await checkUniqueReq(params);
    return data;
  }

  // async function getLanguage(
  //   params: {
  //     temp_id: number;
  //     channel_link: string;
  //     content: string;
  //     format: string;
  //     platform: string;
  //     deliverable_link: string;
  //     date: string;
  //   }[],
  //   game: string,
  // ) {
  //   const data = await getLanguageReq(params, game);
  //   return data;
  // }

  return {
    init,
    isAdmin,
    table,
    option,
    condition,
    setManagementType,
    isLoading,
    getFilterData,
    getCurrentUsername,
    isFileValidOpitionsLoading,
    countryOptions,
    countryValues,
    countryFlatOptions,
    languageOptions,
    languageValues,
    contentOptions,
    isValidUTF8,
    uploadExcelData,
    editExcelData,
    removeRecord,
    viewKolRecord,
    generateTrackingLink,
    checkChannel,
    checkUnique,
    // getLanguage,
    tableSort,
  };
});
