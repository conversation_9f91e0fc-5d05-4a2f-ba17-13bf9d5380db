import { TActionItem } from 'common/service/baseinfo';
import { OptionsItem } from 'common/components/Cascader';

import { isEmpty, intersection, isArray, isMatch } from 'lodash-es';

export function tableDataToCsvStr(options: { tableList: any[], titles?: string, isIncludeHeader?: boolean }) {
  const { tableList = [], titles, isIncludeHeader = true } = options;

  if (!tableList || tableList.length === 0) return '';
  const cols = titles ? titles?.split(',') : Object.keys(tableList[0]);
  let csvContentStr = tableList.map((row) => {
    const values = cols.map(col => row[col]);
    return values.join(',');
  }).join('\r\n');

  const headerStr = `${cols.join(',')}\r\n`;
  csvContentStr = `${isIncludeHeader ? headerStr : ''}${csvContentStr}\r\n`; // 1.是否补上header 2.补上结束空行
  return csvContentStr;
}

export function findValuesInActionsByDimensionKey(options: {
  actions: Record<string, TActionItem[]>,
  filter: {
    module: string, // eg: Intelligence | BI | Creative Center
    action: string, // eg: view | edit | control
    dimension: Record<string, any>,
  },
  dimensionTargetKey: string, // 需要查找内容 例如：show_network
  // dimensionkey: string, // 自定义属性 eg: game | front_menu_sub ……
}) {
  const { actions, filter, dimensionTargetKey } = options;
  const { module, action: actionP, dimension: dimensionP = {} } = filter ?? {};


  const moduleActions = actions?.[module] ?? [];

  // ----- 找到所有符合身份的action ------ 例如所有的spend的actions
  const targetActions = moduleActions.filter((moduleActionitem) => {
    const { action, dimension = {} } = moduleActionitem ?? {};
    return action === actionP && Object.keys(dimensionP).length > 0 && isMatch(dimension, dimensionP);
  });

  // ----- 找目标附着的内容 -----
  const targetValues = targetActions.map((targetActionItem) => {
    const { dimension = {} } = targetActionItem ?? {};

    const value = dimension?.[dimensionTargetKey];

    return value;
  });

  // 是否允许所有目标值
  const isArrowAllTarget = (targetValues ?? []).some((item) => {
    const isArrowAll = (!item) || (isArray(item) && item.length === 0); // 1.没有定义目标值 或者 有一项是空数组 表明允许所有target
    return isArrowAll;
  });

  if (isArrowAllTarget) {
    return []; // 表明允许所有
  }

  return [...new Set(targetValues.flat().filter(i => i) as string [])]; // 限定的目标
}

export function findOptionsByValueList(options: OptionsItem[], valueList: string[] | number[]) {
  if (!options) return [];
  const newOptions: OptionsItem[] = [];
  options.forEach((optionItem) => {
    const { children = [], label, value } = optionItem ?? {};
    const childrenValueList = children?.map(item => item.value);
    const isIncludeFirst = !isEmpty(intersection(valueList, childrenValueList));
    if (isIncludeFirst) {
      const firstChildren = children.filter((item: OptionsItem) => (valueList)?.includes(item.value as never))
        .map((item) => {
          const newItem = {
            label: item.label,
            value: item.value,
          };
          return newItem;
        });

      const firstOption = {
        label,
        value,
        children: firstChildren,
      };
      newOptions.push(firstOption);
    }
  });

  return newOptions;
}
