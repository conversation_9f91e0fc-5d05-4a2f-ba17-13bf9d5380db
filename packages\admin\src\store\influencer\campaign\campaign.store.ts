import { computed, ref } from 'vue';
import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import type {
  IModelValue as IBusinessTableModelValue,
} from 'common/components/BusinessTable';
import { getSelectOptions, type TSelectOptions } from 'common/service/influencer/common/selectOptions';
import { getView, type TViewConfig, type TTabView } from 'common/service/influencer/common/getView';
import { getMetricsToTable } from 'common/service/influencer/campaign/metrics';
import { getFormList } from './const';
import { isEqual, omit, pick } from 'lodash-es';
import { IFormDynamicItem } from 'common/components/FormContainer/type';
import { getQueryString } from 'common/utils/url';
import { TDownloadTableParams } from '../types';
import { TMetricsToTableResult } from 'common/service/influencer/types';

export const SYSTEM = 'influencer_report_campaign';

export const useInfluencerCampaignStore = defineStore(STORE_KEY.INFLUENCER.CAMPAIGN, () => {
  const tabList = ref<TTabView[]>([]);
  const currentTabId = ref<string>('');
  const currentTab = computed(() => tabList.value.find((item: TTabView) => item.id === currentTabId.value));

  const pageConfig = ref<TViewConfig['pageConfig']>({});
  const tableData = ref<TMetricsToTableResult>({
    list: [],
    count: 0,
  });
  const isTableLoading = ref<boolean>(false);

  const isIniting = ref<boolean>(true);
  const isInitError = ref<boolean>(false);
  const formModelValue = ref<Record<string, any>>({});
  const modModelValue = ref<Record<string, any>>({});
  const selectOptions = ref<Record<string, TSelectOptions[]>>({});

  const formKeys = computed(() => getFormList({
    pageConfig: pageConfig.value,
    selectOptions: selectOptions.value }).map(item => item.ext.key));
  const formList = computed<IFormDynamicItem[]>(() => {
    const curFormData: Record<string, any> = currentTab?.value?.param?.form ?? {};
    const FORM_LIST = getFormList({ pageConfig: pageConfig.value, selectOptions: selectOptions.value });
    return FORM_LIST.map((item: IFormDynamicItem) => {
      const defaultValue = item.ext.default ?? curFormData?.[item.ext.key];
      return {
        ...item,
        ext: {
          ...item.ext,
          default: defaultValue,
        },
      };
    });
  });

  const setFormModelValue = (v: Record<string, any>) => formModelValue.value = v;

  const getViewConfig = async () => {
    isIniting.value = true;
    await getView({
      system: SYSTEM,
      code: getQueryString('code') || undefined,
    })
      .then((data) => {
        tabList.value = data.tabList;
        currentTabId.value = data.tabList[0].value;
        formModelValue.value = data.tabList[0].param.form;
        modModelValue.value = omit(data.tabList[0].param, 'form');
        pageConfig.value = data.pageConfig;
        isInitError.value = false;
        isIniting.value = false;
      })
      .catch((err) => {
        console.log('getViewConfig campaign error', err);
        isInitError.value = true;
        isIniting.value = false;
      });
  };

  const fetchTableDataList = async (params?: TDownloadTableParams): Promise<any> => {
    const { isDownload, downloadModelValue } = params || {};
    if (!isDownload) {
      isTableLoading.value = true;
    }
    const res = await getMetricsToTable({
      ...(isDownload ? downloadModelValue : modModelValue.value.table),
      system: SYSTEM,
      where: {
        ...pick(formModelValue.value, formKeys.value),
      },
    }).finally(() => {
      if (!isDownload) {
        isTableLoading.value = false;
      }
    });
    tableData.value.list = res.list;
    tableData.value.count = res.count;
    return [res.list];
  };

  const fetchSelectOptions = async () => {
    const needFetchOptions = formList.value.filter(item => item.ext.isNeedFetchOptions);
    if (needFetchOptions.length > 0) {
      needFetchOptions.forEach(async (item) => {
        getSelectOptions({
          system: SYSTEM,
          field: item.ext.key,
          sub_field: item.ext.subKey,
        }).then((data) => {
          selectOptions.value[item.ext.key] = data;
        });
      });
    }
  };

  const setTableModelValue = (modelValue: IBusinessTableModelValue) => {
    const isDiff = !isEqual(modelValue, modModelValue.value.table);
    if (isDiff) {
      modModelValue.value.table = modelValue;
      fetchTableDataList();
    }
  };

  const fetchAllDataList = async () => {
    fetchTableDataList();
  };

  // ---------------------------- 初始化 ------------------------------
  const init = async () => {
    await getViewConfig();
    if (!isInitError.value) {
      fetchSelectOptions();
      fetchAllDataList();
    }
  };

  return {
    // 基础变量和方法
    init,
    isIniting,
    isInitError,
    modModelValue,
    formModelValue,
    pageConfig,
    formList,
    currentTabId,
    currentTab,
    tabList,
    setFormModelValue,
    fetchAllDataList,

    // 图表组件变量和方法
    tableData,
    setTableModelValue,
    fetchTableDataList,
    isTableLoading,
  };
});
