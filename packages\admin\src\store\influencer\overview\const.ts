import { COMPONENTS_MAP } from 'common/const/components';
import { IFormDynamicItem } from 'common/components/FormContainer/type';
import { TViewConfig } from 'common/service/influencer/common/getView';
import { TSelectOptions } from 'common/service/influencer/common/selectOptions';

type TGetFormListParams = {
  pageConfig: TViewConfig['pageConfig'];
  selectOptions: Record<string, TSelectOptions[]>;
};

export const getFormList: (params: TGetFormListParams) => Array<IFormDynamicItem> = ({ pageConfig, selectOptions }) => [
  {
    name: COMPONENTS_MAP['date-time-picker'],
    props: {
      label: 'Publish Date',
      dateRangePickerWidth: 350,
      valueType: 'YYYYMMDD',
      presetsKey: 'maxDate',
      isShowPopupHeader: false,
      maxDate: pageConfig?.maxDate,
      minDate: pageConfig?.minDate,
    },
    ext: {
      key: 'date',
      label: 'Date',
    },
  },
  {
    name: COMPONENTS_MAP['new-cascader'],
    props: {
      title: 'Region/Country',
      levelList: [
        {
          label: 'Region',
          value: 'region',
        },
        {
          label: 'Country',
          value: 'country_code',
        },
      ],
      isEmptyWhenSelectAll: true,
      mode: 'level',
      isUseDefaultButton: true,
      isDirectUpdateValue: true,
      isOnlyNeedSon: true,
      isShowQuestionMarkForDirtyData: true,
      options: selectOptions?.region,
    },
    ext: {
      key: 'region',
      subKey: 'country_code',
      label: 'Region/Country',
      isAllowClose: true,
      isNeedFetchOptions: true,
    },
  },
  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      list: selectOptions?.campaign_name,
      title: 'Campaign',
      isEmptyWhenSelectAll: true,
      multiple: true,
      isShowQuestionMarkForDirtyData: true,
    },
    ext: {
      key: 'campaign_name',
      label: 'Campaign',
      isAllowClose: true,
      isNeedFetchOptions: true,
    },
  },
  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      list: selectOptions?.fix_language,
      title: 'Language',
      isEmptyWhenSelectAll: true,
      multiple: true,
      isShowQuestionMarkForDirtyData: true,
    },
    ext: {
      key: 'fix_language',
      label: 'Language',
      isAllowClose: true,
      isNeedFetchOptions: true,
    },
  },
  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      list: selectOptions?.channel_name,
      title: 'Channel',
      isEmptyWhenSelectAll: true,
      multiple: true,
      isShowQuestionMarkForDirtyData: true,
    },
    ext: {
      key: 'channel_name',
      label: 'Channel',
      isAllowClose: true,
      isNeedFetchOptions: true,
    },
  },
  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      list: selectOptions?.platform,
      title: 'Platform',
      isEmptyWhenSelectAll: true,
      multiple: true,
      isShowQuestionMarkForDirtyData: true,
    },
    ext: {
      key: 'platform',
      label: 'Platform',
      isAllowClose: true,
      isNeedFetchOptions: true,
    },
  },
  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      list: selectOptions?.content,
      title: 'Content',
      isEmptyWhenSelectAll: true,
      multiple: true,
      isShowQuestionMarkForDirtyData: true,
    },
    ext: {
      key: 'content',
      label: 'Content',
      isAllowClose: true,
      isNeedFetchOptions: true,
    },
  },
  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      list: selectOptions?.format,
      title: 'Format',
      isEmptyWhenSelectAll: true,
      multiple: true,
      isShowQuestionMarkForDirtyData: true,
    },
    ext: {
      key: 'format',
      label: 'Format',
      isAllowClose: true,
      isNeedFetchOptions: true,
    },
  },
];
