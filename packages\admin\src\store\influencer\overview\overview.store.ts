import { computed, ref } from 'vue';
import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import type {
  IModelValue as IBusinessTableModelValue,
} from 'common/components/BusinessTable';
import { getSelectOptions, type TSelectOptions } from 'common/service/influencer/common/selectOptions';
import { getView, type TViewConfig, type TTabView } from 'common/service/influencer/common/getView';
import { getMetricsToTable, getMetricsToChart, getMetricsToPie, getMetricsToCard, getMetricsByCampaign } from 'common/service/influencer/overview/metrics';
import { getFormList } from './const';
import { isEqual, omit, pick } from 'lodash-es';
import { IFormDynamicItem } from 'common/components/FormContainer/type';
import { getQueryString } from 'common/utils/url';
import { TDownloadTableParams, TChartModelValue } from '../types';
import { TMetricsToTableResult } from 'common/service/influencer/types';

export const SYSTEM = 'influencer_report_overview';

export const useInfluencerOverviewStore = defineStore(STORE_KEY.INFLUENCER.OVERVIEW, () => {
  const tabList = ref<TTabView[]>([]);
  const currentTabId = ref<string>('');
  const currentTab = computed(() => tabList.value.find((item: TTabView) => item.id === currentTabId.value));

  const pageConfig = ref<TViewConfig['pageConfig']>({});
  const videoTableData = ref<TMetricsToTableResult>({
    list: [],
    count: 0,
  });
  const streamTableData = ref<TMetricsToTableResult>({
    list: [],
    count: 0,
  });
  const shortsTableData = ref<TMetricsToTableResult>({
    list: [],
    count: 0,
  });
  const barChartDataList = ref<Record<string, any>[]>([]);
  const lineChartDataList = ref<Record<string, any>[]>([]);
  const pie1ChartDataList = ref<Record<string, any>[]>([]);
  const pie2ChartDataList = ref<Record<string, any>[]>([]);
  const cardDataList = ref<Record<string, any>[]>([]);
  const videosCardDataList = ref<Record<string, any>[]>([]);
  const streamsCardDataList = ref<Record<string, any>[]>([]);
  const shortsCardDataList = ref<Record<string, any>[]>([]);
  const campaignChartDataList = ref<Record<string, any>[]>([]);

  const isCardLoading = ref<boolean>(false);
  const isBarChartLoading = ref<boolean>(false);
  const isLineChartLoading = ref<boolean>(false);
  const isPie1ChartLoading = ref<boolean>(false);
  const isPie2ChartLoading = ref<boolean>(false);
  const isVideosCardLoading = ref<boolean>(false);
  const isStreamsCardLoading = ref<boolean>(false);
  const isShortsCardLoading = ref<boolean>(false);
  const isVideoTableLoading = ref<boolean>(false);
  const isStreamTableLoading = ref<boolean>(false);
  const isShortsTableLoading = ref<boolean>(false);
  const isCampaignChartLoading = ref<boolean>(false);

  const isIniting = ref<boolean>(true);
  const isInitError = ref<boolean>(false);
  const formModelValue = ref<Record<string, any>>({});
  const modModelValue = ref<Record<string, any>>({});
  const selectOptions = ref<Record<string, TSelectOptions[]>>({});
  const formatData = ref<string[]>([]);

  const formKeys = computed(() => getFormList(
    { pageConfig: pageConfig.value, selectOptions: selectOptions.value },
  ).map(item => item.ext.key));
  const formList = computed<IFormDynamicItem[]>(() => {
    const curFormData: Record<string, any> = currentTab?.value?.param?.form ?? {};
    const FORM_LIST = getFormList({
      pageConfig: pageConfig.value,
      selectOptions: selectOptions.value,
    });
    return FORM_LIST.map((item: IFormDynamicItem) => {
      const defaultValue = item.ext.default ?? curFormData?.[item.ext.key];
      return {
        ...item,
        ext: {
          ...item.ext,
          default: defaultValue,
        },
      };
    });
  });

  const setFormModelValue = (v: Record<string, any>) => formModelValue.value = v;

  const getViewConfig = async () => {
    isIniting.value = true;
    await getView({
      system: SYSTEM,
      code: getQueryString('code') || undefined,
    })
      .then((data) => {
        tabList.value = data.tabList;
        currentTabId.value = data.tabList[0].value;
        formModelValue.value = data.tabList[0].param.form;
        modModelValue.value = omit(data.tabList[0].param, 'form');
        pageConfig.value = data.pageConfig;
        isInitError.value = false;
        isIniting.value = false;
      })
      .catch((err) => {
        console.log('getViewConfig overview error', err);
        isInitError.value = true;
        isIniting.value = false;
      });
  };

  const fetchCardDataList = async () => {
    isCardLoading.value = true;
    const res = await getMetricsToCard({
      ...modModelValue.value.card,
      system: SYSTEM,
      where: {
        ...pick(formModelValue.value, formKeys.value),
      },
    }).finally(() => {
      isCardLoading.value = false;
    });
    cardDataList.value = res.list;
  };

  const fetchVideosCardDataList = async () => {
    isVideosCardLoading.value = true;
    const res = await getMetricsToCard({
      ...modModelValue.value.videos_card,
      system: SYSTEM,
      where: {
        ...pick(formModelValue.value, formKeys.value),
        format: 'Video',
      },
    }).finally(() => {
      isVideosCardLoading.value = false;
    });
    videosCardDataList.value = res.list;
  };

  const fetchStreamsCardDataList = async () => {
    isStreamsCardLoading.value = true;
    const res = await getMetricsToCard({
      ...modModelValue.value.streams_card,
      system: SYSTEM,
      where: {
        ...pick(formModelValue.value, formKeys.value),
        format: 'Stream',
      },
    }).finally(() => {
      isStreamsCardLoading.value = false;
    });
    streamsCardDataList.value = res.list;
  };

  const fetchShortsCardDataList = async () => {
    isShortsCardLoading.value = true;
    const res = await getMetricsToCard({
      ...modModelValue.value.shorts_card,
      system: SYSTEM,
      where: {
        ...pick(formModelValue.value, formKeys.value),
        format: 'Shorts',
      },
    }).finally(() => {
      isShortsCardLoading.value = false;
    });
    shortsCardDataList.value = res.list;
  };

  const fetchLineDataList = async () => {
    isLineChartLoading.value = true;
    const res = await getMetricsToChart({
      ...modModelValue.value.line,
      system: SYSTEM,
      where: {
        ...pick(formModelValue.value, formKeys.value),
      },
    }).finally(() => {
      isLineChartLoading.value = false;
    });
    lineChartDataList.value = res.list;
  };

  const fetchBarDataList = async () => {
    isBarChartLoading.value = true;
    const res = await getMetricsToChart({
      ...modModelValue.value.bar,
      system: SYSTEM,
      where: {
        ...pick(formModelValue.value, formKeys.value),
      },
    }).finally(() => {
      isBarChartLoading.value = false;
    });
    barChartDataList.value = res.list;
  };

  const fetchVideoTableDataList = async (params?: TDownloadTableParams) => {
    const { isDownload, downloadModelValue } = params || {};
    if (!isDownload) {
      isVideoTableLoading.value = true;
    }
    const res = await getMetricsToTable({
      ...(isDownload ? downloadModelValue : modModelValue.value.video_table),
      system: SYSTEM,
      where: {
        ...pick(formModelValue.value, formKeys.value),
        format: 'Video',
      },
    }).finally(() => {
      isVideoTableLoading.value = false;
    });
    videoTableData.value.list = res.list;
    videoTableData.value.count = res.count;
    return [res.list];
  };

  const fetchStreamTableDataList = async (params?: TDownloadTableParams) => {
    const { isDownload, downloadModelValue } = params || {};
    if (!isDownload) {
      isStreamTableLoading.value = true;
    }
    const res = await getMetricsToTable({
      ...(isDownload ? downloadModelValue : modModelValue.value.stream_table),
      system: SYSTEM,
      where: {
        ...pick(formModelValue.value, formKeys.value),
        format: 'Stream',
      },
    }).finally(() => {
      isStreamTableLoading.value = false;
    });
    streamTableData.value.list = res.list;
    streamTableData.value.count = res.count;
    return [res.list];
  };

  const fetchShortsTableDataList = async (params?: TDownloadTableParams) => {
    const { isDownload, downloadModelValue } = params || {};
    if (!isDownload) {
      isShortsTableLoading.value = true;
    }
    const res = await getMetricsToTable({
      ...(isDownload ? downloadModelValue : modModelValue.value.shorts_table),
      system: SYSTEM,
      where: {
        ...pick(formModelValue.value, formKeys.value),
        format: 'Shorts',
      },
    }).finally(() => {
      isShortsTableLoading.value = false;
    });
    shortsTableData.value.list = res.list;
    shortsTableData.value.count = res.count;
    return [res.list];
  };

  const fetchPie1DataList = async () => {
    isPie1ChartLoading.value = true;
    const res = await getMetricsToPie({
      ...modModelValue.value.pie1,
      system: SYSTEM,
      where: {
        ...pick(formModelValue.value, formKeys.value),
      },
    }).finally(() => {
      isPie1ChartLoading.value = false;
    });
    pie1ChartDataList.value = res.list;
  };

  const fetchPie2DataList = async () => {
    isPie2ChartLoading.value = true;
    const res = await getMetricsToPie({
      ...modModelValue.value.pie2,
      system: SYSTEM,
      where: {
        ...pick(formModelValue.value, formKeys.value),
      },
    }).finally(() => {
      isPie2ChartLoading.value = false;
    });
    pie2ChartDataList.value = res.list;
  };

  const fetchCampaignChartDataList = async () => {
    isCampaignChartLoading.value = true;
    const res = await getMetricsByCampaign({
      ...modModelValue.value.campaign_chart,
      system: SYSTEM,
      where: {
        ...pick(formModelValue.value, formKeys.value),
      },
    }).finally(() => {
      isCampaignChartLoading.value = false;
    });
    campaignChartDataList.value = res.list;
  };

  const fetchSelectOptions = async () => {
    const needFetchOptions = formList.value.filter(item => item.ext.isNeedFetchOptions);
    if (needFetchOptions.length > 0) {
      needFetchOptions.forEach(async (item) => {
        getSelectOptions({
          system: SYSTEM,
          field: item.ext.key,
          sub_field: item.ext.subKey,
        }).then((data) => {
          selectOptions.value[item.ext.key] = data;
        });
      });
    }
  };

  const setVideoTableModelValue = (modelValue: IBusinessTableModelValue) => {
    const isDiff = !isEqual(modelValue, modModelValue.value.video_table);
    if (isDiff) {
      modModelValue.value.video_table = modelValue;
      fetchVideoTableDataList();
    }
  };

  const setStreamTableModelValue = (modelValue: IBusinessTableModelValue) => {
    const isDiff = !isEqual(modelValue, modModelValue.value.stream_table);
    if (isDiff) {
      modModelValue.value.stream_table = modelValue;
      fetchStreamTableDataList();
    }
  };

  const setShortsTableModelValue = (modelValue: IBusinessTableModelValue) => {
    const isDiff = !isEqual(modelValue, modModelValue.value.shorts_table);
    if (isDiff) {
      modModelValue.value.shorts_table = modelValue;
      fetchShortsTableDataList();
    }
  };


  const setLineModelValue = (modelValue: TChartModelValue) => {
    const isDiff = !isEqual(modelValue, modModelValue.value.line);
    if (isDiff) {
      modModelValue.value.line = modelValue;
      fetchLineDataList();
    }
  };

  const setBarModelValue = (modelValue: TChartModelValue) => {
    const isDiff = !isEqual(modelValue, modModelValue.value.bar);
    if (isDiff) {
      modModelValue.value.bar = modelValue;
      fetchBarDataList();
    }
  };

  const setPie1ModelValue = (modelValue: TChartModelValue) => {
    const isDiff = !isEqual(modelValue, modModelValue.value.pie1);
    if (isDiff) {
      modModelValue.value.pie1 = modelValue;
      fetchPie1DataList();
    }
  };

  const setPie2ModelValue = (modelValue: TChartModelValue) => {
    const isDiff = !isEqual(modelValue, modModelValue.value.pie2);
    if (isDiff) {
      modModelValue.value.pie2 = modelValue;
      fetchPie2DataList();
    }
  };

  const setCampaignChartModelValue = (modelValue: TChartModelValue) => {
    const isDiff = !isEqual(modelValue, modModelValue.value.campaign_chart);
    if (isDiff) {
      modModelValue.value.campaign_chart = modelValue;
      fetchCampaignChartDataList();
    }
  };

  const fetchAllDataList = async () => {
    fetchVideoTableDataList();
    fetchStreamTableDataList();
    fetchShortsTableDataList();
    fetchLineDataList();
    fetchBarDataList();
    fetchPie1DataList();
    fetchPie2DataList();
    fetchCardDataList();
    fetchVideosCardDataList();
    fetchStreamsCardDataList();
    fetchShortsCardDataList();
    fetchCampaignChartDataList();
    formatData.value = formModelValue.value.format || [];
  };

  // ---------------------------- 初始化 ------------------------------
  const init = async () => {
    await getViewConfig();
    if (!isInitError.value) {
      fetchSelectOptions();
      fetchAllDataList();
    }
  };

  return {
    // 基础变量和方法
    init,
    isIniting,
    isInitError,
    modModelValue,
    formModelValue,
    pageConfig,
    formList,
    currentTabId,
    currentTab,
    tabList,
    formatData,
    setFormModelValue,
    fetchAllDataList,

    // 图表组件变量和方法
    isBarChartLoading,
    isLineChartLoading,
    isPie1ChartLoading,
    isPie2ChartLoading,
    isCardLoading,
    isVideosCardLoading,
    isStreamsCardLoading,
    isShortsCardLoading,
    isVideoTableLoading,
    isStreamTableLoading,
    isShortsTableLoading,
    isCampaignChartLoading,

    barChartDataList,
    lineChartDataList,
    pie1ChartDataList,
    pie2ChartDataList,
    cardDataList,
    videosCardDataList,
    streamsCardDataList,
    shortsCardDataList,
    videoTableData,
    streamTableData,
    shortsTableData,
    campaignChartDataList,

    setVideoTableModelValue,
    setStreamTableModelValue,
    setShortsTableModelValue,
    setLineModelValue,
    setBarModelValue,
    setPie1ModelValue,
    setPie2ModelValue,
    setCampaignChartModelValue,
    fetchVideoTableDataList,
    fetchStreamTableDataList,
    fetchShortsTableDataList,
  };
});
