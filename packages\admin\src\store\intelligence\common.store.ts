import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { ref } from 'vue';
// service
import { getIsAdmin } from 'common/service/intelligence/common/common';

export const useIntelligenceCommonStore = defineStore(
  STORE_KEY.INTELLIGENCE.COMMON,
  () => {
    const isAdmin = ref<undefined|boolean>(undefined); // 判断用户是否为管理员
    const getIsAdminValue = async () => {
      const res = await getIsAdmin();
      isAdmin.value = res?.isAdmin;
    };
    const init = async () => {
      if (isAdmin.value === undefined) {
        // 判断当前用户是否为admin
        await getIsAdminValue();
      }
    };
    return {
      isAdmin,
      init,
    };
  },
);
