export const unitConversion = (val: number, defaultStr = '') => {
  switch (true) {
    case val > 1E3 && val < 1E6: {
      const num = Math.floor(val / 1E3);
      const str = `${num} K`;
      return str;
    }
    case val > 1E6 && val < 1E9: {
      const num = Math.floor(val / 1E6);
      const str = `${num} M`;
      return str;
    }
    case val > 1E9: {
      const num = Math.floor(val / 1E9);
      const str = `${num} B`;
      return str;
    }
    default:
      return defaultStr || val;
  }
};
