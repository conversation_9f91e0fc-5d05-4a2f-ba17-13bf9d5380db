export interface ISocialMediaVideoItem {
  entity_name: string, // 游戏名称 eg: "Assassin's Creed"
  cover: string, // 游戏封面
  unified_id: string, // 游戏code
  edition_id: string,
  channel_name: string, // 渠道 eg:youtube
  channel_type: string, // 渠道类型 eg:social
  app_id: string, //
  reviewer: string, // 作者 eg:Mods Yattm
  comment_time: string // 视频上线时间（贴子发布时间）format: YYYY-MM-DD HH:mm:ss (2023-10-01 01:12:50)
  comment_score: string, // "-1"
  content: string, // 内容
  content_to_zh: string, // 内容
  content_to_en: string, // 内容
  content_url: string, // 外链 eg: https://www.youtube.com/watch?v=8Og8XL_CiNs
  is_recommend: number, // eg: 0
  sentiment_rating: number, // 情感分 eg: 3
  comment_id: string, // eg: "5800f5f73a8b77614d081db038ab3d55"
  tweets_reply: number, // 评论数量 eg: 1
  tweets_retweet: number, // 转发数量 eg: 0
  tweets_like: number, // 点赞数量
  view: number, // 观看数量 eg: 5
  subscribe: number, // eg: 0
  market: string, // 市场 eg: "global"
  version: string, // eg: ""
  topic: string, // 所属topic eg: "Cheats feedback|Cheat"
  topic_en: string, //
  topic_cn: string, //
  platform: string, //
  keyword_or_account: string, // eg: "keyword"
  keyword_account_name: string, // eg: "pubg mobile хак"
  monitor_source: string, // eg: "pubg mobile хак"
  comment_parent_id: string, // eg: "-1"
  comment_uin: string, // eg: "5e0eeab8fceecb8a6dacb0f8849bbe64"
  account_source: string, // eg: "pubg mobile хак"
  reviews_type: string, // eg: ""
  account_url: string, // eg: ""
  language: string, // 语种 eg: "en"
  match_score: number, // eg: 0
  important_score: string, // eg: ""
  ctgr_1: string, // eg: ""
  ctgr_2: string, // eg: ""
  ctgr_3: string, // eg: ""
  order_number: string, // eg: "-1"
  md5_uin: string, // eg: "a4846f50071248f7c6bee3606b32d610"
  engagement: number, // eg: 3
  create_time: number, // eg: "2023-10-01 05:43:31"
  isvalid: number, // eg: 1
  review_duration: number, // eg: -1
  record_duration: number, // eg: -1
  ext_json: string, //
  video_detail: string, // cover_image：封面图；duration：时长；url：视频地址
  // "{\"cover_image\": \"\", \"duration\": \"255\", \"url\": \"\"}"
  media_type: string, // eg: 'video'
  image: string, // eg: ''
  anchor_md5: string, // eg: 'b890da5999406f4deeb98f95d8850637'
};

export interface ISmvVideoDetail {
  cover_image: string,
  duration: string,
  url: string,
};

export interface ISmvParams {
  game_name?: string // 游戏名称,
  search_text?: string, // 关键字
  game_codes?: Array<string>, // 游戏竞品code
  start_time: string, // 开始时间 YYYY-MM-DD HH:mm:ss
  end_time: string, // 结束时间 YYYY-MM-DD HH:mm:ss
  channels?: Array<string>, // 渠道
  sort_item?: string, // 排序字段
  sort_model?: string, // 排序类型：asc、desc
  page?: number, //
  page_size?: number, //
}


