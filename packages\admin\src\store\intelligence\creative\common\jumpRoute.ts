import { useRouter } from 'vue-router';
import { useRouterStore } from '@/store/global/router.store';
// Intelligence/Creative与Creative/Competitor用的相同store以及route view
/**
  问题一: 页面跳转需要注意跳出对应模块的页面，比如competitor跳到overview中
          intelligence的competitor跳到intelligence的overview上
 */
export interface RoutePathAdaptation {
  [key: string]: {
    [key: string]: string;
  };
}

// export const routePathAdaptation: RoutePathAdaptation = {
//   Intelligence: {
//     Creative: {
//       Competitor: {
//         'Add Game': 'Intelligence / Creative / Competitor / Add Game',
//       },
//       Overview: {
//         'Creative Gallery': 'Intelligence / Creative / Overview / Creative Gallery',
//         Analyze: 'Intelligence / Creative / Overview / Analyze',
//       },
//     },
//   },
//   'Creative Center': {
//     Competitor: {
//       Competitor: {
//         'Add Game': 'Creative Center / Competitor / Competitor / Add Game',
//       },
//       Overview: {
//         'Creative Gallery': 'Creative Center / Competitor / Overview / Creative Gallery',
//         Analyze: 'Creative Center / Competitor / Overview / Analyze',
//       },
//     },
//   },
// };

export const routePathAdaptation: RoutePathAdaptation = {
  Intelligence: {
    Creative_Competitor_AddGame: 'Intelligence / Creative / Competitor / Add Game',
    Creative_Overview_CreativeGallery: 'Intelligence / Creative / Overview / Creative Gallery',
    Creative_Overview_Analyze: 'Intelligence / Creative / Overview / Analyze',
  },
  'Creative Center': {
    Creative_Competitor_AddGame: 'Creative Center / Inspiration / Competitor / Add Game',
    Creative_Overview_CreativeGallery: 'Creative Center / Inspiration / Creative Gallery / Creative Gallery',
    Creative_Overview_Analyze: 'Creative Center / Inspiration / Creative Gallery / Analyze',
  },
};

export function useIntelligenceJumpRoute() {
  const router = useRouter();
  const routerStore = useRouterStore();

  const jumpAddCompetitorPage = (query: Record<string, string> = {}) => {
    // Intelligence / Creative / Competitor / Add Game
    // Creative Center / Competitor / Competitor / Add Game
    const firstRouteName = routerStore.firstRouteName ?? '';
    router.push({ name: routePathAdaptation[firstRouteName]?.Creative_Competitor_AddGame ?? '', query });
  };
  const jumpOverviewCreativeGalleryPage = (query: Record<string, string> = {}) => {
    // Intelligence / Creative / Overview / Creative Gallery
    // Creative Center / Competitor / Overview / Creative Gallery
    const firstRouteName = routerStore.firstRouteName ?? '';
    router.push({ name: routePathAdaptation[firstRouteName]?.Creative_Overview_CreativeGallery ?? '', query });
  };
  const jumpOverviewAnalyzePage = (query: Record<string, string> = {}) => {
    // Intelligence / Creative / Overview / Analyze
    // Creative Center / Competitor / Overview / Analyze
    const firstRouteName = routerStore.firstRouteName ?? '';
    router.push({ name: routePathAdaptation[firstRouteName]?.Creative_Overview_Analyze ?? '', query });
  };
  return {
    jumpAddCompetitorPage,
    jumpOverviewCreativeGalleryPage,
    jumpOverviewAnalyzePage,
  };
}

