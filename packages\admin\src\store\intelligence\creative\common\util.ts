import { ICreativeItem } from '../competitor/competitor';
import { ISocialMediaVideoItem, ISmvVideoDetail } from './index';
import { CreateItemDefault } from '../competitor/competitor.const';
import { unitConversion } from '../../common/util';
import dayjs from 'dayjs';
import { DATABRAIN_CHANNEL_OPTIONS } from '../config/selectOptions.const';


export const smvListToCreativeList = (listRaw: Array<ISocialMediaVideoItem>): Array<ICreativeItem> => {
  const list = listRaw.map((item) => {
    const temp = {
      ...CreateItemDefault,
    };
    temp.impression = '0';
    temp.days = 0;
    temp.heat = '0';
    temp.share_count = '0';
    temp.body = 'No Copywriting';
    // 转成广大大素材数据
    temp.app_logo = item.cover;
    temp.app_name = item.entity_name;
    temp.title = item.topic;
    temp.type = 2; // 视频类型素材
    temp.like_count = unitConversion(item.tweets_like); // 点赞
    temp.comment_count = unitConversion(item.tweets_reply); // 评论
    temp.share_count = unitConversion(item.tweets_retweet); // 转发或分享
    const videoDetail: ISmvVideoDetail = item.video_detail ? (JSON.parse(item.video_detail) as ISmvVideoDetail) : { cover_image: '', duration: '', url: '' };
    temp.preview_img = videoDetail?.cover_image?.split('|')[0] ?? '';
    // databrain独有属性
    temp.smv_view_count = unitConversion(item.view); // 转发或分享
    temp.smv_channel = item.channel_name;
    temp.smv_content_url = item.content_url;
    temp.smv_comment_time = item.comment_time ? dayjs(item.comment_time).format('YYYY-MM-DD HH:mm') : '';
    return temp;
  });
  return list;
};

export const childStrSplitToArrayMergeAll = (rawlist: Array<string>): Array<string> => {
  const listChange: Array<string[]> = rawlist.filter(item => item).map(item => item.split(','));
  let resultList: Array<string> = [];
  listChange.forEach((item: Array<string>) => {
    resultList = resultList.concat(item);
  });
  return resultList;
};

export const databrainChannleSelectAll = (): Array<string> => {
  const list: Array<string> = DATABRAIN_CHANNEL_OPTIONS.map(item => item.value);
  return childStrSplitToArrayMergeAll(list);
};


