import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useRouterStore } from '@/store/global/router.store';
import { cloneDeep } from 'lodash-es';
import { IAddAndDelCompetitor, IAddCompetitor, ICompetitor, IGameCode, ISearchKeyWorld } from './competitor';
import { SearchStatus, NormalStatus } from './competitor.const';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useWatchGameChange } from 'common/compose/request/game';
import { useLoading } from 'common/compose/loading';
// service
import { getGameCompetitor } from 'common/service/intelligence/creative/get-game-competitor';
import { getCompetitorSearch } from 'common/service/intelligence/creative/get-competitor-search';
import { hasBigCompetitor } from 'common/service/intelligence/creative/has-big-competitor';
import { gameCompetitorRelation } from 'common/service/intelligence/creative/game-competitor-relation';
// let
let timer: any = null; // 用于防抖
export const useIntelligenceCreativeAddCompetitorStore = defineStore(
  STORE_KEY.INTELLIGENCE.CREATIVE.ADD_COMPETITOR,
  () => {
    const gameStore = useGlobalGameStore();
    const router = useRouter();
    const routerStore = useRouterStore();
    const originData = ref<Array<ICompetitor>>([]); // 页面初始拉取大包列表(源数据，赋值后不在更改)
    const competitorList = ref<Array<ICompetitor>>([]); // 大包列表（用户渲染视图）
    const {
      isLoading: isLoadingCompetitorList,
      hideLoading: hideLoadingCompetitorList,
      showLoading: showLoadingCompetitorList,
    } = useLoading(); // 请求大包的loading
    const {
      isLoading: isLoadingNewAdd,
      hideLoading: hideLoadingNewAdd,
      showLoading: showLoadingNewAdd,
    } = useLoading(); // 请求大包的loading
    const searchList = ref<Array<IAddCompetitor>>([]); // 搜索的大包列表
    const searchStatus = ref<number>(SearchStatus.LOADING); // 搜索的状态
    const nextStatus = ref<number>(NormalStatus.INIT); // 下一步的状态
    const noDbBigList = ref<Array<IAddCompetitor>>([]); // 在数据库中没有大包列表；
    const newAddBigVisible = ref<boolean>(false); // 有新大包要添加到数据的弹窗
    const delList = ref<Array<string>>([]); // 删除大包的唯一标识
    const delListPush = (val: string) => {
      delList.value.push(val);
    };
    const addList = ref<Array<IAddCompetitor>>([]); // 添加的大包列表
    const addListPush = (val: IAddCompetitor) => {
      addList.value.push(val);
    };
    const listSplice = (val: string) => {
      // 根据大包的唯一删除零时新增或者已关联大包
      const index = addList.value.findIndex(item => item.competitor_code === val);
      if (index !== -1) {
        addList.value.splice(index, 1);
      }
      const index2 = competitorList.value.findIndex(item => item.competitor_code === val);
      if (index2 !== -1) {
        competitorList.value.splice(index2, 1);
        delListPush(val); // 将大包唯一标识存入删除列表中
      }
    };
    const listPush = (val: IAddCompetitor) => {
      /**
       * 判断是原本就绑定了(只是操作软删除了)
       */
      const index = originData.value.findIndex(item => item.competitor_code === val.competitor_code);
      if (index === -1) {
        addListPush(val);
      } else {
        const delIndex = delList.value.indexOf(val.competitor_code);
        if (delIndex !== -1) {
          delList.value.splice(delIndex, 1);
        }
      }
      const temp: ICompetitor = {
        id: 0,
        competitor_code: val.competitor_code,
        competitor_name: val.competitor_name,
        competitor_icon: val.competitor_icon,
        create_time: '',
        dashboard: 0,
        store_ids: [],
      };
      competitorList.value.push(temp);
    };

    // service
    const getGameCompetitorList = async (params: IGameCode) => {
      // 根据game_code获取关联的大包列表
      showLoadingCompetitorList();
      const res = await getGameCompetitor(params);
      competitorList.value = cloneDeep(res);
      originData.value = cloneDeep(res);
      hideLoadingCompetitorList();
    };
    const getCompetitorByKeyWrold = async (params: ISearchKeyWorld) => {
      // 根据输入的关键字搜索大包
      searchStatus.value = SearchStatus.LOADING;
      const rawRes = await getCompetitorSearch(params) ?? [];
      const res = Array.isArray(rawRes) ? rawRes : [];
      for (let i = 0; i < res.length; i++) {
        // 删除存在的
        const index = competitorList.value.findIndex(item => item.competitor_code === res[i].competitor_code);
        if (index !== -1) {
          res.splice(i, 1);
          i = i - 1;
        }
      }
      searchList.value = res;
      searchStatus.value = SearchStatus.SUCCESS;
    };
    const antiShake = (params: ISearchKeyWorld) => {
      if (!params.k) {
        searchList.value = [];
        setTimeout(() => {
          searchList.value = [];
        }, 500);
        return;
      }
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(() => {
        getCompetitorByKeyWrold(params);
      }, 500);
    };
    const delSearchItemByIndex = (index: number) => {
      searchList.value.splice(index, 1);
    };
    const nextHandle = async () => {
      // 点击下一步的操作
      nextStatus.value = NormalStatus.LOADING;
      if (addList.value.length > 0) {
        // 说明有新添加的操作
        const hasResult = await hasBigCompetitor(addList.value.map(item => item.competitor_code));
        nextStatus.value = NormalStatus.SUCCESS;
        const noHasList: Array<IAddCompetitor> = [];
        const keys: Array<string> = Object.keys(hasResult);
        keys.forEach((k: string) => {
          if (!hasResult[k]) {
            const index = addList.value.findIndex(item => item.competitor_code === k);
            noHasList.push(addList.value[index]);
          }
        });
        if (noHasList.length > 0) {
          noDbBigList.value = noHasList;
          newAddBigVisible.value = true;
        } else {
          await operationSubmit();
          newAddBigVisible.value = false;
          // 跳转待续。。。
        }
        nextStatus.value = NormalStatus.SUCCESS;
      } else {
        await operationSubmit();
        nextStatus.value = NormalStatus.SUCCESS;
      }
    };
    const onConfirm = async () => {
      showLoadingNewAdd();
      operationSubmit();
    };
    const operationSubmit = async () => {
      const params: IAddAndDelCompetitor = {
        game_code: gameStore.gameCode,
        addList: addList.value,
        delList: delList.value,
      };
      await gameCompetitorRelation(params);
      // 跳转待续。。。
      hideLoadingNewAdd();
      jumpIndexCompetitorPage();
    };
    const jumpIndexCompetitorPage = () => {
      if (routerStore.isIntelligenceRouteModule()) {
        router.push({ path: '/intelligence/creative/competitor' });
      } else {
        router.push({ path: '/creative/competitor/competitor' });
      };
    };
    const init = async () => {
      useWatchGameChange(async () => {
        // 恢复默认值
        hideLoadingNewAdd();
        newAddBigVisible.value = false;
        addList.value = [];
        delList.value = [];
        noDbBigList.value = [];
        await getGameCompetitorList({ game_code: gameStore.gameCode }); // 获取关联的竞品列表
      });
    };
    return {
      originData,
      competitorList,
      searchList,
      searchStatus,
      nextStatus,
      noDbBigList,
      newAddBigVisible,
      isLoadingCompetitorList,
      isLoadingNewAdd,
      getGameCompetitorList,
      getCompetitorByKeyWrold,
      antiShake,
      delList,
      delListPush,
      addList,
      addListPush,
      listSplice,
      listPush,
      delSearchItemByIndex,
      nextHandle,
      operationSubmit,
      onConfirm,
      hideLoadingNewAdd,
      showLoadingNewAdd,
      init,
    };
  },
);
