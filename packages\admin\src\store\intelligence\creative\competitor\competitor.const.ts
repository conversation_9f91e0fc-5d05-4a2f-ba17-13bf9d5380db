import { ICreativeItem } from './competitor';

// status
export enum SearchStatus {
  LOADING,
  SUCCESS,
  FAIL,
}
export enum NormalStatus {
  INIT,
  LOADING,
  SUCCESS,
  FAIL,
}
// 竞品类型
export enum CompetitorType {
  PC = 'pc', // pc竞品
  MOBILE = 'mobile', // 手游竞品
  CONSOLE = 'console', // 手柄游戏
};

export const CreateItemDefault: ICreativeItem = {
  id: '',
  creative_id: '', // 创意广告id
  app_logo: '', // 广告主 logo
  app_name: '', // 广告主名称
  title: '', // 标题
  body: '', // 文案
  channel: 0, // 渠道
  conversion: '', // 互动
  theme: [], // 主题
  dynamic_is: 0, // 是否动态广告 1-是动态广告，0 非动态广告
  first_seen: '', // 首次看见
  impression: '', // 展现
  interaction: '', // 转化
  last_seen: '', // 最后看见
  os: 0, // 设备
  preview_img: '', // 预览图
  type: 0, // 素材类型
  impression_number: '', // 展现
  impression_distance: '', // 上周展现与上上周展现的差值；
  store_id: '', // 广告主包名
  days: 0, // 持续投放天数
  heat: '', // 热度
  resources: [], // 素材资源
  countries: [], // 投放国家
  cos_url: '', // 资源转录
  key_frames_by_scene: [], // 关键帧或分幕
  tags: [], // 标签
  share_count: '0',
  comment_count: '0',
  like_count: '0',
};

export const NO_IMAGE_AVAILABLE = 'https://static.aix.intlgame.cn/v2/assets/no_image_available.jpg';
export const GRAY_COLOR = 'https://static.aix.intlgame.cn/v2/assets/gray_color.png';
