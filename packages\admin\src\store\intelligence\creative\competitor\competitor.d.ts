export interface IIntenlligenceCreativeCompetitor {
  competitorList: Array<ICompetitor>,
};
// 后端返回的值

export interface ICompetitor {
  id: number,
  competitor_code: string,
  competitor_icon: string,
  competitor_name: string,
  competitor_type?: string,
  create_time: string,
  dashboard: number,
  store_ids: Array<string>,
  release_time?: string | null | undefined,
};
export interface IDayAndWeek {
  dayList: Array<ICreativeItem>,
  weekList: Array<ICreativeItem>,
}
export interface ICreativeItem {
  id: number | string,
  creative_id: string, // 创意广告id
  app_logo: string, // 广告主 logo
  app_name: string, // 广告主名称
  title: string, // 标题
  body: string, // 文案
  channel: number, // 渠道
  conversion: string, // 互动
  theme: Array<string>, // 主题
  dynamic_is: number, // 是否动态广告 1-是动态广告，0 非动态广告
  first_seen: string, // 首次看见
  impression: string, // 展现
  interaction: string, // 转化
  last_seen: string, // 最后看见
  os: number, // 设备
  preview_img: string, // 预览图
  type: number, // 素材类型
  impression_number: string,  // 展现
  impression_distance?: string, // 上周展现与上上周展现的差值；
  store_id: string, // 广告主包名
  days: number, // 持续投放天数
  heat: string, // 热度
  resources: Array<string>, // 素材资源
  countries: Array<string>, // 投放国家
  cos_url: string, // 资源转录
  key_frames_by_scene: Array<string>, // 关键帧或分幕
  tags: Array<string>, // 标签
  share_count?: number | string, // 分享数
  comment_count?: number | string, // 评论数
  like_count?: number | string, // 点赞数
  competitor_type?: string, // 竞品类型 pc mobile
  store_url?: string, // 落地页(跟外链一样意思)

  // databrain社媒视频独有的属性
  smv_view_count?: number | string, // 浏览
  smv_channel?: string, // 渠道
  smv_content_url?: string, // 外链
  smv_comment_time?: string, // 视频上线时间
}

// 传递参数的 type

export interface IGameCode {
  game_code?: string,
};
export interface ISearchKeyWorld {
  k: string,
};

export interface IAddCompetitor {
  competitor_code: string,
  competitor_icon: string,
  competitor_name: string,
  competitor_type: string,
  create_time: string,
  dashboard: number,
  release_time: string | null | undefined,
};

export interface IAddAndDelCompetitor {
  game_code: string,
  delList: Array<string>,
  addList: Array<IAddCompetitor>,
};

// 轮播关键帧的

export interface IKeyFrame {
  img: string, // 关键帧地址
  jumpTime: number, // 跳转到第几帧的时间
  showTime: string, // 展示的时间
}


// 其他util

export interface ITimeRangeResult {
  startStr: string,
  endStr: string,
  startUnix: number,
  endUnix: number,
}

export interface ICountryItem {
  name: string, // 国家英文缩写
  flag: string, // 国家图片地址
  fullName: string, // 国家英文全称（超过11个字符截取前9个后面拼接...）
}

export interface IDayOrWeekParam { // 每日最新的请求参数
  store_ids: Array<string>, // 选中的竞品的子包
  type?: string, // 素材的类型 '1,2' -> 图片和视频；'2' -> 视频；
  page?: number, // 第几页数据
  pageSize?: number, // 一页多少条数据
}
