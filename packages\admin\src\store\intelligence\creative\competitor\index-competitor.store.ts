import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { ref, nextTick, unref, computed } from 'vue';
import { useWatchGameChange } from 'common/compose/request/game';
import { useLoading } from 'common/compose/loading';
// type
import { ICompetitor, IGameCode, ICreativeItem } from './competitor';
import { NormalStatus, CompetitorType } from './competitor.const';
// store
import { useGlobalGameStore } from '@/store/global/game.store';
// service
import { getGameCompetitor } from 'common/service/intelligence/creative/get-game-competitor';
import { getDayWeekData } from 'common/service/intelligence/creative/get-day-week-data';
import { getCreativePageService } from 'common/service/intelligence/creative/get-creative-page';
import { cloneDeep } from 'lodash-es';

import { CREATIVETYPE } from '../config/selectOptions.const';
import { getLastWeekRange, getReleaseTimeForPc, getYestodayRange } from './untis';
import { getSocialMediaVideoPageService } from 'common/service/intelligence/creative/get-databrain-social-media-video-page';
import { smvListToCreativeList, databrainChannleSelectAll } from '../common/util';

export const useIntelligenceCreativeCompetitorStore = defineStore(STORE_KEY.INTELLIGENCE.CREATIVE.COMPETITOR, () => {
  const channelAll = databrainChannleSelectAll();
  const gameStore = useGlobalGameStore();
  const competitorList = ref<Array<ICompetitor>>([]); // 大包列表
  const competitorListStatus = ref<number>(NormalStatus.INIT); // 请求大包列表的状态
  const activeCompetitor = ref<ICompetitor>(); // 选中中竞品
  // 折叠板
  const collapseDefaultAll = ['dailyLaset', 'weekHot', 'uaCreative', 'socialMedia'];
  const collapseValue = ref<Array<string>>(collapseDefaultAll.slice(0));
  // ----- day and week -----
  // 说明移动竞品的数据
  const dayList = ref<Array<ICreativeItem>>([]); // 每日最新
  const weekList = ref<Array<ICreativeItem>>([]); // 每周最热
  const activeIndex = ref<number>(0); // 激活大包索引
  const dayWeekLoading = ref<number>(NormalStatus.INIT); // 列表数据的loading
  // ----- ua-creative ------
  // 说明pc竞品的数据
  const uaCreativeList = ref<Array<ICreativeItem>>([]); // ua-creative的素材数据
  const uaCreativePage = ref<number>(0); // 当前是第几页数据
  const uaCreativeTotal = ref<number>(0); // 总条目数
  const uaCreativePageSize = ref<number>(50); // 总条目数
  const {
    isLoading: isLoadingUaCreativeList,
    showLoading: showLoadingUaCreativeList,
    hideLoading: hideLoadingUaCreativeList,
  } = useLoading(false);
  // ----- databraiin社媒视频数据 ------
  const smvList = ref<Array<ICreativeItem>>([]); // databraiin社媒视频数据list
  const smvPage = ref<number>(1); // 当前是第几页数据
  // const smvTotal = ref<number>(0); // 总条目数
  const smvPageSize = ref<number>(20); // 总条目数
  const {
    isLoading: isLoadingSmvList,
    showLoading: showLoadingSmvList,
    hideLoading: hideLoadingSmvList,
  } = useLoading(false);

  const curGameName = computed(() => {
    const competitorName = unref(activeCompetitor)?.competitor_name;
    return competitorName?.split('|')?.[0] || '';
  });

  const getCompetitorList = async () => {
    // 获取大包列表
    const params: IGameCode = {
      game_code: gameStore.gameCode,
    };
    competitorListStatus.value = NormalStatus.LOADING;
    const res = await getGameCompetitor(params);
    competitorList.value = res;
    competitorListStatus.value = NormalStatus.SUCCESS;
  };
  const getDayWeek = async (storeIds: Array<string>) => {
    // 获取每日最新和和每周最热的列表数据
    dayWeekLoading.value = NormalStatus.LOADING;
    const res = await getDayWeekData(storeIds);
    dayList.value = cloneDeep(res.dayList);
    weekList.value = cloneDeep(res.weekList);
    // let w = cloneDeep(res.dayList);
    // w = w.map(item => ({ ...item, impression_distance: '20000' }));
    // weekList.value = w;
    dayWeekLoading.value = NormalStatus.SUCCESS;
  };

  const getCreativePage = async () => {
    const timeRange = getReleaseTimeForPc(activeCompetitor.value?.release_time, 'YYYY-MM-DD HH:mm:ss');
    const params = {
      creative_time: `${timeRange.startStr},${timeRange.endStr}`,
      // geo: '',
      // channel: '',
      // os: '',
      // str_lang: '',
      creative_type: CREATIVETYPE.map(item => item.value).join(','),
      // daysMax: 0,
      // daysMin: 10,
      // tag: '',
      store_id: activeCompetitor.value?.store_ids?.join(',') || 'nostore',
      page: `${uaCreativePage.value + 1}`,
      page_size: `${uaCreativePageSize.value}`,
      order_by: 'impression_number,desc',
      // keyword: cur.keyword,
    };
    showLoadingUaCreativeList();
    const res = await getCreativePageService(params);
    uaCreativeList.value = res.list;
    uaCreativeTotal.value = res.total;
    hideLoadingUaCreativeList();
  };
  const getSocialMediaVideoPage = async () => {
    // console.log('test test');
    showLoadingSmvList();
    const lastWeek = getLastWeekRange('YYYY-MM-DD HH:mm:ss');
    const yestoday = getYestodayRange('YYYY-MM-DD HH:mm:ss');
    const res = await getSocialMediaVideoPageService({
      // search_text: '',
      game_name: unref(curGameName),
      game_codes: [activeCompetitor.value?.competitor_code || ''].filter(item => item),
      start_time: lastWeek.startStr,
      // start_time: '2023-11-01 00:00:00',
      end_time: yestoday.endStr,
      channels: channelAll,
      sort_item: 'view',
      sort_model: 'desc',
      page: smvPage.value,
      page_size: smvPageSize.value,
    }) || {};
    smvList.value = smvListToCreativeList(res.list || []);
    hideLoadingSmvList();
  };
  const changeActiveIndex = async (index: number) => {
    nextTick(() => collapseValue.value = collapseDefaultAll.slice(0)); // 恢复全部展开
    // 切换大包
    activeIndex.value = index;
    resetData();
    activeCompetitor.value = competitorList.value[activeIndex.value];
    if (activeCompetitor.value.competitor_type === CompetitorType.MOBILE) {
      getDayWeek(activeCompetitor.value.store_ids);
    } else if (activeCompetitor.value.competitor_type === CompetitorType.PC) {
      getCreativePage();
    };
    getSocialMediaVideoPage();
  };
  const changeCollapse = (val: Array<string>) => {
    // console.log(val);
    collapseValue.value = val;
  };
  const resetData = () => {
    dayList.value = [];
    weekList.value = [];
    uaCreativeList.value = [];
  };
  const init = async () => {
    useWatchGameChange(async () => {
      activeIndex.value = 0;
      resetData();
      await getCompetitorList(); // 获取关联的竞品列表
      if (competitorList.value.length > 0) { // 有竞品才去查询数据；
        activeCompetitor.value = competitorList.value[activeIndex.value];
        if (activeCompetitor.value.competitor_type === CompetitorType.MOBILE) {
          getDayWeek(activeCompetitor.value.store_ids);
        } else if (activeCompetitor.value.competitor_type === CompetitorType.PC) {
          getCreativePage();
        }
        getSocialMediaVideoPage();
        return;
      }
    });
  };
  return {
    competitorList,
    competitorListStatus,
    dayList,
    weekList,
    uaCreativeList,
    activeIndex,
    activeCompetitor,
    dayWeekLoading,
    isLoadingUaCreativeList,
    isLoadingSmvList,
    smvList,
    collapseValue,
    curGameName,
    getCompetitorList,
    getDayWeek,
    changeActiveIndex,
    changeCollapse,
    init,
  };
});
