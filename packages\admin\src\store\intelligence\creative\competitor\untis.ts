import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import { ITimeRangeResult, ICountryItem, IKeyFrame } from './competitor';
import { WORLDCODECREATIVE, COUNTRY_FLAG_CODE, CHANNELC_CODE, THEME_CODE, DATABRAIN_CHANNEL_CODE } from '../config/selectOptions.const';
dayjs.extend(isoWeek);
const K_NUM = 1000;
const M_NUM = 1000000;
const B_NUM = 1000000000;
const URL_DEMO = 'https://ua-cdn.intlgame.com/'; // 关键帧兼容处理
const STATIC_CDN_URL = 'https://static.aix.intlgame.cn/';
export const convertionNumToStr = (val: number): string => {
  if (0 <= val && val < K_NUM) {
    return '1K';
  }
  if (K_NUM <= val && val < M_NUM) {
    const temp = Math.floor(val / K_NUM);
    return `${temp}K`;
  }
  if (M_NUM <= val && val < B_NUM) {
    const temp = Math.floor(val / M_NUM);
    return `${temp}M`;
  }
  if (B_NUM <= val) {
    const temp = Math.floor(val / B_NUM);
    return `${temp}B`;
  }
  return '1K';
};

export const getToday = (format: string): string => dayjs().format(format);
export const getYestoday = (format: string): string => dayjs()
  .subtract(1, 'day')
  .format(format);

export const getYestodayRange = (format: string): ITimeRangeResult => {
  const start = dayjs().subtract(1, 'day')
    .startOf('day');
  const end = dayjs().subtract(1, 'day')
    .endOf('day');
  return {
    startStr: start.format(format),
    endStr: end.format(format),
    startUnix: start.unix(),
    endUnix: start.unix(),
  };
};

export const getLastWeekRange = (format: string): ITimeRangeResult => {
  const start = dayjs().subtract(1, 'week')
    .startOf('isoWeek');
  const end = dayjs().subtract(1, 'week')
    .endOf('isoWeek');
  return {
    startStr: start.format(format),
    endStr: end.format(format),
    startUnix: start.unix(),
    endUnix: start.unix(),
  };
};

export const getReleaseTimeForPc = (releaseTimeRaw: string | null | undefined, format: string) => {
  if (releaseTimeRaw) { // 如果有发行时间 拉取上线前1年至T-1日这段时间内
    const releaseTime = dayjs(releaseTimeRaw);
    return {
      startStr: releaseTime.subtract(356 * 1, 'day')
        .startOf('day')
        .format(format),
      endStr: dayjs().subtract(1, 'day')
        .endOf('day')
        .format(format),
      startUnix: releaseTime.subtract(356 * 1, 'day')
        .startOf('day')
        .unix(),
      endUnix: dayjs().subtract(1, 'day')
        .endOf('day')
        .unix(),
    };
  }
  // 没有发行时间 前五年至t-1这段时间内
  return {
    startStr: dayjs().subtract(356 * 5, 'day')
      .startOf('day')
      .format(format),
    endStr: dayjs().subtract(1, 'day')
      .endOf('day')
      .format(format),
    startUnix: dayjs().subtract(356 * 5, 'day')
      .startOf('day')
      .unix(),
    endUnix: dayjs().subtract(1, 'day')
      .endOf('day')
      .unix(),
  };
};
export const timeStrFormat = (val: string, format: string): string => dayjs(val).format(format);

export const convertionCountry = (list: Array<string>, isSubStrName = true): Array<ICountryItem> => {
  // 处理国家国旗及显示名称的内容
  if (list.length === 0) {
    return [];
  }
  const countryList: Array<ICountryItem> = [];
  list.forEach((item) => {
    if (item && WORLDCODECREATIVE[item]) {
      const fullNameStr = WORLDCODECREATIVE[item]
        ? (WORLDCODECREATIVE[item].length > 11 && isSubStrName)
          ? `${WORLDCODECREATIVE[item].substring(0, 9)}...`
          : WORLDCODECREATIVE[item]
        : '';
      const temp: ICountryItem = {
        name: item,
        flag: `v2/intellgence/flag/${COUNTRY_FLAG_CODE[item]}.png`,
        fullName: fullNameStr || item,
      };
      countryList.push(temp);
    }
  });
  return countryList;
};
export const getAssetsFile = (url: string) => new URL(url, import.meta.url).href;
export const getImgByCdn = (suffix: string) => `${STATIC_CDN_URL}${suffix}`;
export const getChannelName = (val: number): string => {
  if (CHANNELC_CODE[val]) {
    return CHANNELC_CODE[val];
  }
  return '';
};
export const getChannelImg = (val: number): string => `v2/intellgence/channel/${val}.jpg`;
export const getDatabrainChannelImg = (val: string): string => {
  if (DATABRAIN_CHANNEL_CODE[val]?.img) {
    return `v2/intellgence/databrain/channel/${DATABRAIN_CHANNEL_CODE[val].img}.svg`;
  };
  return '';
};
export const convertionOs = (val: number): string => {
  switch (val) {
    case 1:
      return 'iOS';
    case 2:
      return 'Android';
    case 101:
      return 'PC';
    default:
      return 'ALL';
  }
};
export const convertionTheme = (list: Array<string>): string => {
  if (!list || list.length === 0) {
    return 'None';
  }
  const tempList = list.map(item => THEME_CODE[item] || '').filter(item => item);
  return tempList.join(',');
};

export const convertionKeyFrames = (list: Array<string>): Array<IKeyFrame> => {
  if (!list || list.length === 0) {
    return [];
  }
  const tempList = list.map((item) => {
    const jumpTimeStr = item.substring(item.lastIndexOf('/') + 1, item.lastIndexOf('/') + 5);
    const jumpTime = parseInt(jumpTimeStr, 10);
    const showTime = keyFrameTimeFormat(jumpTime);
    const img = item.substring(0, 4) === 'http' ? item : URL_DEMO + item;
    const tempItem: IKeyFrame = {
      jumpTime,
      img,
      showTime,
    };
    return tempItem;
  });
  return tempList;
};
export const keyFrameTimeFormat = (time: number): string => {
  let showtime = '';
  const min = Math.floor(time / 60);
  const second = time % 60;
  if (min < 10) {
    showtime += `0${min}:`;
  } else {
    showtime += `${min}:`;
  }
  if (second < 10) {
    showtime += `0${second}`;
  } else {
    showtime += `${second}`;
  }
  return showtime;
};

export const downloadByLink = (url: string, fileName: string) => {
  const link = document.createElement('a');
  document.body.append(link);
  link.style.display = 'none';
  link.href = `${url}&response-content-disposition=${encodeURIComponent(`attachment; filename="${fileName}"`)}`;
  link.download = fileName;
  link.click();
  setTimeout(() => {
    document.body.removeChild(link);
  }, 300);
};
/**
 * 下载图片或视频
 */
export const downloadImgOrVideo = (url: string) => {
  // let url = 'http://vip.fastsales.cn:8082/group1/M00/32/63/rBAEtF_pW7OAfKl4AC4kWQRxaxw426.jpg';
  const str = url.substring(url.lastIndexOf('/') + 1);
  const xhr = new XMLHttpRequest();
  xhr.open('get', url, true);
  xhr.responseType = 'blob';
  xhr.onload = () => {
    if (xhr.status === 200) {
      const blob = new Blob([xhr.response], {
        type: xhr.response.type,
      });
      const a = document.createElement('a');
      const objectUrl = window.URL.createObjectURL(blob);
      a.download = str;
      a.href = objectUrl;
      a.click();
      window.URL.revokeObjectURL(objectUrl);
      a.remove();
      // saveAs(blob, str);
    }
  };
  xhr.send();
};
