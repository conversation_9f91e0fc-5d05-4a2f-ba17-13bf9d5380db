
export const WORLDCODECREATIVE: Record<string, string> = {
  CMR: 'Cameroon',
  BEN: 'Benin',
  MDG: 'Madagascar',
  RWA: 'Rwanda',
  SYC: 'Seychelles',
  CIV: 'Cote d"lvoire',
  EGY: 'Egypt',
  MUS: 'Mauritius',
  BFA: 'Burkina Faso',
  ERI: 'Eritrea',
  STP: 'Sao Tome and Principe',
  AGO: 'Angola',
  LBY: 'Libyan Arab Jamahiriya (the)',
  ZWE: 'Zimbabwe',
  GIN: 'Guinea',
  SLE: 'Sierra Leone',
  REU: 'Reunion',
  GHA: 'Ghana',
  TZA: 'Tanzania',
  MLI: 'Mali',
  SOM: 'Somalia',
  MRT: 'Mauritania',
  UGA: 'Uganda',
  TCD: 'Chad',
  MYT: 'Mayotte',
  COM: 'Comoros',
  BWA: 'Botswana',
  SEN: 'Senegal',
  SWZ: 'Swaziland',
  GNB: 'Guinea Bissau',
  COD: 'DR Congo',
  CAF: 'Central African',
  LSO: 'Lesotho',
  COG: 'Congo',
  ZAF: 'South Africa',
  LBR: 'Liberia',
  TUN: 'Tunisia',
  ZMB: 'Zambia',
  NER: 'Niger',
  ESH: 'Western Sahara',
  TGO: 'Togo',
  NAM: 'Namibia',
  MOZ: 'Mozambique',
  ETH: 'Ethiopia',
  MAR: 'Morocco',
  MWI: 'Malawi',
  NGA: 'Nigeria',
  CPV: 'Cape Verde',
  BDI: 'Burundi',
  DZA: 'Algeria',
  DJI: 'Djibouti',
  GMB: 'Gambia',
  GNQ: 'Equatorial Guinea',
  SDN: 'Sudan',
  KEN: 'Kenya',
  SGP: 'Singapore',
  KOR: 'Korea',
  SYR: 'Syrian',
  UZB: 'Uzbekistan',
  BHR: 'Bahrian',
  JPN: 'Japan',
  JOR: 'Jordan',
  VNM: 'Vietnam',
  KGZ: 'Kirghizia',
  THA: 'Thailand',
  LKA: 'Sri Lanka',
  ARE: 'United Arab Emirates',
  LAO: 'Laos',
  AFG: 'Afghanistan',
  MAC: 'Macau',
  TJK: 'Tajikistan',
  PRK: 'KoreaDPR',
  PAL: 'Palestine',
  HKG: 'Hong Kong',
  IRQ: 'Iraq',
  LBN: 'Lebanon',
  KWT: 'Kuwait',
  BRN: 'Brunei',
  MDV: 'Maldives',
  IDN: 'Indonesia',
  ISR: 'Israel',
  MNG: 'Mongolia',
  OMN: 'Oman',
  IND: 'India',
  MMR: 'Myanmar',
  MYS: 'Malaysia',
  TMP: 'East Timor',
  YEM: 'Yemen',
  BTN: 'Bhutan',
  KHM: 'Cambodia',
  PAK: 'Pakistan',
  BGD: 'Bangladesh',
  SAU: 'Saudi Arabia',
  TKM: 'Turkmenistan',
  QAT: 'Qatar',
  NPL: 'Nepal',
  KAZ: 'Kazakhstan',
  PHL: 'Philippines',
  TWN: 'Taiwan',
  CHN: 'China',
  IRN: 'Iran',
  CRI: 'Costa Rica',
  CUB: 'Cuba',
  DOM: 'Dominican',
  MEX: 'Mexico',
  NIC: 'Nicaragua',
  PAN: 'Panama',
  ANT: 'Netherlands Antilles',
  SLV: 'El Salvador',
  PTR: 'Puerto Rico',
  VAG: 'Saint Vincent and the Grenadines',
  HND: 'Honduras',
  GTM: 'Guatemala',
  GEO: 'Georgia',
  ARM: 'Armenia',
  AZE: 'Azerbaijan',
  BLR: 'Belarus',
  RUS: 'Russia',
  UKR: 'Ukraine',
  HUN: 'Hungary',
  ISL: 'Iceland',
  MLT: 'Malta',
  MCO: 'Monaco',
  NOR: 'Norway',
  ROM: 'Romania',
  SMR: 'San Marino',
  SWE: 'Sweden',
  CHE: 'Switzerland',
  EST: 'Estonia',
  LVA: 'Latvia',
  LTU: 'Lithuania',
  MDA: 'Moldavia',
  TUR: 'Turkey',
  SVN: 'Slovenia',
  CZE: 'Czech',
  SVK: 'Slovak',
  MKD: 'Macedonia',
  BIH: 'Bosnia Hercegovina',
  VAT: 'Vatican City State',
  NLD: 'Netherlands',
  HRV: 'Croatia',
  GRC: 'Greece',
  IRL: 'Ireland',
  BEL: 'Belgium',
  CYP: 'Cyprus',
  DNK: 'Denmark',
  ENG: 'United Kingdom',
  DEU: 'Germany',
  FRA: 'France',
  ITA: 'Italy',
  LUX: 'Luxembourg',
  PRT: 'Portugal',
  POL: 'Poland',
  ESP: 'Spain',
  ALB: 'Albania',
  AND: 'Andorra',
  LIE: 'Liechtenstein',
  'S&M': 'Serbia and Montenegro',
  AUT: 'Austria',
  BGR: 'Bulgaria',
  FIN: 'Finland',
  GIB: 'Gibraltar',
  DMA: 'Dominica',
  BMU: 'Bermuda',
  CAN: 'Canada',
  USA: 'United States',
  GRL: 'Greenland',
  TON: 'Tonga',
  AUS: 'Australia',
  COK: 'Cook Is',
  NRU: 'Nauru',
  NCL: 'New Caledonia',
  VUT: 'Vanuatu',
  SLB: 'Solomon Is',
  WSM: 'Samoa',
  TUV: 'Tuvalu',
  FSM: 'Micronesia',
  MHL: 'Marshall Is Rep',
  KIR: 'Kiribati',
  PYF: 'French Polynesia',
  NZL: 'New Zealand',
  FJI: 'Fiji',
  PNG: 'Papua New Guinea',
  PLW: 'Palau',
  CHL: 'Chile',
  COL: 'Colombia',
  GUY: 'Guyana',
  PRY: 'Paraguay',
  PER: 'Peru',
  SUR: 'Suriname',
  VEN: 'Venezuela',
  URY: 'Uruguay',
  ECU: 'Ecuador',
  ATG: 'Antigua and Barbuda',
  ABW: 'Aruba',
  BHS: 'Bahamas',
  BRB: 'Barbados',
  CYM: 'Cayman Is',
  GRD: 'Grenada',
  HTI: 'Haiti',
  JAM: 'Jamaica',
  MTQ: 'Martinique',
  MSR: 'Montserrat',
  TTO: 'Trinidad and Tobago',
  KNA: 'St Kitts-Nevis',
  SPM: 'St.Pierre and Miquelon',
  ARG: 'Argentina',
  BLZ: 'Belize',
  BOL: 'Bolivia',
  BRA: 'Brazil',
  AS: 'American Samoa',
  AX: 'Aland Islands',
  BL: 'Saint Barthélemy',
  BQ: 'Bonaire Sint Eustatius and Saba',
  BV: 'Bouvet Island',
  CC: 'Cocos (Keeling) Islands',
  CW: 'Curaçao',
  CX: 'Christmas Island',
  FK: 'Falkland Islands (Malvinas)',
  FO: 'Faroe Islands',
  GF: 'French Guiana',
  GG: 'Guernsey',
  GS: 'South Georgia and The South Sandwich Islands',
  GU: 'Guam',
  HM: 'Heard Island and McDonald Islands',
  IM: 'Isle Of Man',
  IO: 'British Indian Ocean Territory',
  JE: 'Jersey',
  LC: 'Saint Lucia',
  MF: 'Saint Martin',
  MP: 'Northern Mariana Islands',
  NF: 'Norfolk Island',
  NU: 'Niue',
  PM: 'Saint Pierre and Miquelon',
  PN: 'Pitcairn Islands',
  SH: 'Saint Helena',
  SJ: 'Svalbard and Jan Mayen Islands',
  SX: 'Sint Maarten',
  TC: 'Turks and Caicos Islands',
  TF: 'French Southern Territories',
  TK: 'Tokelau',
  UM: 'United States Minor Outlying Islands',
  VG: 'The British Virgin Islands',
  VI: 'The United States Virgin Islands',
  WF: 'Wallis and Futuna Islands',
  XK: 'Kosovo',
  GAB: 'Gabon',
  Others: 'Others',
  ci: 'Côte d\'Ivoire',
  cw: 'Curacao',
  eu: 'European Union',
};
export const COUNTRY_FLAG_CODE: Record<string, string> = {
  NZL: 'nz',
  FJI: 'fj',
  PNG: 'pg',
  STP: 'st',
  MHL: 'mh',
  CUB: 'cu',
  SDN: 'sd',
  GMB: 'gm',
  MYS: 'my',
  MYT: 'yt',
  TWN: 'tw',
  POL: 'pl',
  OMN: 'om',
  SUR: 'sr',
  AS: 'as',
  ARE: 'ae',
  KEN: 'ke',
  ARG: 'ar',
  GNB: 'gt',
  ARM: 'am',
  UZB: 'uz',
  SEN: 'sn',
  BTN: 'bt',
  TGO: 'tg',
  IRL: 'ie',
  IRN: 'ir',
  QAT: 'qa',
  BDI: 'bi',
  NLD: 'nl',
  IRQ: 'iq',
  SVK: 'rs',
  SVN: 'sk',
  GNQ: 'gq',
  THA: 'th',
  ABW: 'aw',
  SWE: 'se',
  ISL: 'is',
  SX: 'sx',
  ci: 'ci',
  MKD: 'mk',
  BEL: 'be',
  ISR: 'il',
  KWT: 'kw',
  LIE: 'li',
  DZA: 'dz',
  BEN: 'bj',
  TC: 'tc',
  RUS: 'ru',
  ATG: 'ag',
  cw: 'cw',
  CW: 'fk',
  ITA: 'it',
  SWZ: 'sz',
  TK: 'tk',
  TZA: 'tz',
  PAL: 'ps',
  PAK: 'pk',
  'S&M': 'me',
  BFA: 'bf',
  PAN: 'pa',
  SGP: 'sg',
  UKR: 'ua',
  KGZ: 'kg',
  CHE: 'ch',
  DJI: 'dj',
  REU: 're',
  CHL: 'cl',
  CHN: 'cn',
  PRK: 'kp',
  MLI: 'ml',
  BWA: 'bw',
  HRV: 'hr',
  KHM: 'kh',
  IDN: 'id',
  PRT: 'pt',
  TJK: 'tj',
  VNM: 'vn',
  MLT: 'mt',
  CYM: 'ky',
  PRY: 'py',
  CYP: 'cy',
  SYC: 'sc',
  RWA: 'rw',
  BGD: 'bd',
  AUS: 'au',
  AUT: 'at',
  LKA: 'lk',
  GAB: 'ga',
  ZWE: 'zw',
  BGR: 'bg',
  SYR: 'sy',
  CZE: 'cz',
  NOR: 'no',
  eu: 'eu',
  VG: 'vg',
  MMR: 'mm',
  VI: 'vi',
  KIR: 'ki',
  TKM: 'tm',
  GRD: 'gd',
  GRC: 'gr',
  HTI: 'gy',
  ENG: 'gb',
  YEM: 'ye',
  GRL: 'gl',
  AFG: 'af',
  MNG: 'mn',
  FO: 'fo',
  NPL: 'np',
  BHS: 'bs',
  BHR: 'bh',
  PTR: 'pr',
  WF: 'wf',
  DMA: 'dm',
  GF: 'gf',
  BIH: 'ba',
  GG: 'gg',
  HUN: 'hu',
  AGO: 'ao',
  GU: 'gu',
  WSM: 'wa',
  FRA: 'fr',
  TMP: 'tl',
  MOZ: 'mz',
  NAM: 'na',
  PER: 'pe',
  VAG: 'vc',
  DNK: 'dk',
  GTM: 'gp',
  SLB: 'sb',
  VAT: 'va',
  SLE: 'sl',
  NRU: 'nr',
  SLV: 'sv',
  FSM: 'fm',
  DOM: 'do',
  IM: 'im',
  IO: 'io',
  CMR: 'cm',
  GUY: 'gw',
  AZE: 'az',
  MAC: 'mo',
  GEO: 'ge',
  TON: 'to',
  NCL: 'nc',
  SMR: 'sm',
  JE: 'je',
  MAR: 'ma',
  KNA: 'kn',
  BLR: 'by',
  MRT: 'mr',
  BLZ: 'bz',
  PHL: 'ph',
  COD: 'cd',
  COG: 'cg',
  PYF: 'pf',
  URY: 'uy',
  COK: 'ck',
  COM: 'km',
  COL: 'co',
  USA: 'us',
  ESP: 'es',
  EST: 'ee',
  BMU: 'bm',
  ZMB: 'zm',
  KOR: 'kr',
  SOM: 'so',
  VUT: 'vu',
  ECU: 'ec',
  ALB: 'al',
  LC: 'kn',
  ETH: 'et',
  MCO: 'mc',
  NER: 'ne',
  LAO: 'la',
  VEN: 've',
  GHA: 'gh',
  CPV: 'cv',
  MDA: 'md',
  MTQ: 'jm',
  MDG: 'mg',
  LBN: 'lb',
  MF: 'mf',
  LBR: 'lr',
  MDV: 'mv',
  BOL: 'bo',
  GIB: 'gi',
  LBY: 'ly',
  MP: 'mp',
  HKG: 'hk',
  CAF: 'cf',
  LSO: 'ls',
  NGA: 'ng',
  MUS: 'mu',
  JOR: 'jo',
  GIN: 'gn',
  ROM: 'ro',
  CAN: 'ca',
  TCD: 'td',
  AND: 'ad',
  NF: 'nf',
  CRI: 'cr',
  IND: 'in',
  MEX: 'mx',
  KAZ: 'kz',
  SAU: 'sa',
  JPN: 'jp',
  LTU: 'lt',
  TTO: 'tt',
  PLW: 'pw',
  MWI: 'mw',
  NIC: 'ni',
  FIN: 'fi',
  TUN: 'tn',
  UGA: 'ug',
  LUX: 'lu',
  TUR: 'tr',
  BRA: 'br',
  BRB: 'bb',
  TUV: 'tv',
  DEU: 'de',
  EGY: 'eg',
  LVA: 'lv',
  JAM: 'hn',
  PM: 'pm',
  ZAF: 'za',
  BRN: 'bn',
  HND: 'ht',
};

export const CHANNELC_CODE: Record<string, string> = {
  2: 'FB News Feed',
  39: 'Instagram',
  3: 'Audience Network',
  51: 'Messenger',
  1: 'Google Ads(Admob)',
  6: 'YouTube',
  10: 'Twitter',
  5: 'UnityAds',
  19: 'Vungle',
  20: 'AppLovin',
  24: 'AdColony',
  25: 'Chartboost',
  40: 'Pinterest',
  42: 'ironSource',
  23: 'Yahoo!',
  44: 'reddit',
  43: 'TikTok',
  45: 'TopBuzz',
  46: 'Mintegral (Mobvista)',
  310: 'Pangle(TikTok Audience Network)',
  52: 'Snapchat',
  49: 'MoPub',
  47: 'Tapjoy',
  27: 'NAVER(네이버)',
  26: 'Daum(다음)',
  34: 'Nate(네이트)',
  28: 'Ameba(アメーバ)',
  36: 'Yahoo! Japan',
  301: 'Gunosy(グノシー)',
  303: 'Zucks',
  300: 'SmartNews(スマートニュース)',
  304: 'i-mobile',
  305: 'AkaNe',
  307: 'Nend',
  308: 'AMoAd',
  38: 'INMOBI',
};
export const THEME_CODE: Record<string, string> = {
  3001: 'Legend',
  3005: 'Three Kingdoms',
  3012: 'Xianxia',
  3063: 'Crime',
  3023: 'War',
  3009: 'Magic',
  3024: 'Tower Defence',
  3010: 'Chinese Fantasy',
  3051: 'Idle',
  3022: 'Manage',
  3021: 'Cultivate',
  3035: 'Fighting',
  3037: 'Orientation - Landscape',
  3025: 'Strategy',
  3036: 'Adventure',
  3017: 'Kpop',
  3054: 'Puzzle',
  3018: 'Card',
  3011: 'Wuxia',
  3052: 'Match',
  3013: 'Cartoon',
  3038: 'Jump',
  3014: 'Turn-based',
  3028: 'Shooting',
  3027: 'Simulation',
  3031: 'Sniper',
  3016: 'Metropolis',
  3053: 'Music',
  3043: 'Stand-alone',
  3002: 'Journey to the West',
  3034: 'Naval battle',
  3056: 'Puzzling',
  3049: 'Dodge',
  3019: 'Competitive strategy',
  3061: 'Sport',
  3041: 'Chess',
  3055: 'Fish Hunter',
  3015: 'Immediate',
  3057: 'Football',
  3045: 'Card Games Tabletop',
  3026: 'History',
};
export const WORLD_CREATIVE = [
  {
    label: 'Africa',
    value: 'Africa',
    children: [
      { label: 'Algeria', value: 'DZA', flag: 'dz' },
      { label: 'Benin', value: 'BEN', flag: 'bj' },
      { label: 'Botswana', value: 'BWA', flag: 'bw' },
      { label: 'Burkina Faso', value: 'BFA', flag: 'bf' },
      { label: 'Burundi', value: 'BDI', flag: 'bi' },
      { label: 'Cameroon', value: 'CMR', flag: 'cm' },
      { label: 'Cape Verde', value: 'CPV', flag: 'cv' },
      {
        label: 'Central African',
        value: 'CAF',
        flag: 'cf',
      },
      { label: 'Chad', value: 'TCD', flag: 'td' },
      { label: 'Comoros', value: 'COM', flag: 'km' },
      {
        label: 'DR Congo',
        value: 'COD',
        flag: 'cd',
      },
      { label: 'Djibouti', value: 'DJI', flag: 'dj' },
      { label: 'Egypt', value: 'EGY', flag: 'eg' },
      {
        label: 'Equatorial Guinea',
        value: 'GNQ',
        flag: 'gq',
      },
      { label: 'Ethiopia', value: 'ETH', flag: 'et' },
      { label: 'Gabon', value: 'GAB', flag: 'ga' },
      { label: 'Gambia (The)', value: 'GMB', flag: 'gm' },
      { label: 'Ghana', value: 'GHA', flag: 'gh' },
      { label: 'Guinea', value: 'GIN', flag: 'gn' },
      { label: 'Kenya', value: 'KEN', flag: 'ke' },
      { label: 'Lesotho', value: 'LSO', flag: 'ls' },
      { label: 'Liberia', value: 'LBR', flag: 'lr' },
      {
        label: 'Libyan Arab Jamahiriya (the)',
        value: 'LBY',
        flag: 'ly',
      },
      { label: 'Madagascar', value: 'MDG', flag: 'mg' },
      { label: 'Malawi', value: 'MWI', flag: 'mw' },
      { label: 'Mali', value: 'MLI', flag: 'ml' },
      { label: 'Mauritania', value: 'MRT', flag: 'mr' },
      { label: 'Mauritius', value: 'MUS', flag: 'mu' },
      { label: 'Morocco', value: 'MAR', flag: 'ma' },
      { label: 'Mozambique', value: 'MOZ', flag: 'mz' },
      { label: 'Namibia', value: 'NAM', flag: 'na' },
      { label: 'Niger (the)', value: 'NER', flag: 'ne' },
      { label: 'Nigeria', value: 'NGA', flag: 'ng' },
      {
        label: 'Congo',
        value: 'COG',
        flag: 'cg',
      },
      { label: 'Réunion', value: 'REU', flag: 're' },
      { label: 'Rwanda', value: 'RWA', flag: 'rw' },
      {
        label: 'Sao Tome and Principe',
        value: 'STP',
        flag: 'st',
      },
      { label: 'Senegal', value: 'SEN', flag: 'sn' },
      { label: 'Seychelles', value: 'SYC', flag: 'sc' },
      { label: 'Sierra Leone', value: 'SLE', flag: 'sl' },
      { label: 'Somalia', value: 'SOM', flag: 'so' },
      { label: 'South Africa', value: 'ZAF', flag: 'za' },
      {
        label: 'Tanzania',
        value: 'TZA',
        flag: 'tz',
      },
      { label: 'Tunisia', value: 'TUN', flag: 'tn' },
      { label: 'Uganda', value: 'UGA', flag: 'ug' },
      { label: 'Zambia', value: 'ZMB', flag: 'zm' },
      { label: 'Zimbabwe', value: 'ZWE', flag: 'zw' },
    ],
  },
  {
    label: 'Asia',
    value: 'Asia',

    children: [
      { label: 'China', value: 'CHN', flag: 'cn' },
      { label: 'Afghanistan', value: 'AFG', flag: 'af' },
      { label: 'Armenia', value: 'ARM', flag: 'am' },
      { label: 'Azerbaijan', value: 'AZE', flag: 'az' },
      { label: 'Bahrain', value: 'BHR', flag: 'bh' },
      { label: 'Bangladesh', value: 'BGD', flag: 'bd' },
      { label: 'Bhutan', value: 'BTN', flag: 'bt' },
      {
        label: 'Brunei Darussalam',
        value: 'BRN',
        flag: 'bn',
      },
      { label: 'Cambodia', value: 'KHM', flag: 'kh' },
      { label: 'Hong Kong', value: 'HKG', flag: 'hk' },
      { label: 'India', value: 'IND', flag: 'in' },
      { label: 'Indonesia', value: 'IDN', flag: 'id' },
      {
        label: 'Iran',
        value: 'IRN',
        flag: 'ir',
      },
      { label: 'Iraq', value: 'IRQ', flag: 'iq' },
      { label: 'Israel', value: 'ISR', flag: 'il' },
      { label: 'Japan', value: 'JPN', flag: 'jp' },
      { label: 'Jordan', value: 'JOR', flag: 'jo' },
      { label: 'Kazakhstan', value: 'KAZ', flag: 'kz' },
      {
        label: 'Korea',
        value: 'KOR',
        flag: 'kr',
      },
      { label: 'Kuwait', value: 'KWT', flag: 'kw' },
      { label: 'Kyrgyzstan', value: 'KGZ', flag: 'kg' },
      {
        label: 'Laos',
        value: 'LAO',
        flag: 'la',
      },
      { label: 'Lebanon', value: 'LBN', flag: 'lb' },
      { label: 'Macao', value: 'MAC', flag: 'mo' },
      { label: 'Malaysia', value: 'MYS', flag: 'my' },
      { label: 'Maldives', value: 'MDV', flag: 'mv' },
      { label: 'Mongolia', value: 'MNG', flag: 'mn' },
      { label: 'Myanmar', value: 'MMR', flag: 'mm' },
      { label: 'Nepal', value: 'NPL', flag: 'np' },
      { label: 'Oman', value: 'OMN', flag: 'om' },
      { label: 'Pakistan', value: 'PAK', flag: 'pk' },
      {
        label: 'Palestine',
        value: 'PAL',
        flag: 'ps',
      },
      {
        label: 'Philippines',
        value: 'PHL',
        flag: 'ph',
      },
      { label: 'Qatar', value: 'QAT', flag: 'qa' },
      { label: 'Saudi Arabia', value: 'SAU', flag: 'sa' },
      { label: 'Singapore', value: 'SGP', flag: 'sg' },
      { label: 'Sri Lanka', value: 'LKA', flag: 'lk' },
      { label: 'Sudan (the)', value: 'SDN', flag: 'sd' },
      {
        label: 'Syrian',
        value: 'SYR',
        flag: 'sy',
      },
      {
        label: 'Taiwan',
        value: 'TWN',
        flag: 'tw',
      },
      { label: 'Tajikistan', value: 'TJK', flag: 'tj' },
      { label: 'Thailand', value: 'THA', flag: 'th' },
      {
        label: 'United Arab Emirates',
        value: 'ARE',
        flag: 'ae',
      },
      { label: 'Uzbekistan', value: 'UZB', flag: 'uz' },
      { label: 'Viet Nam', value: 'VNM', flag: 'vn' },
      { label: 'Yemen', value: 'YEM', flag: 'ye' },
    ],
  },
  {
    label: 'Europe',
    value: 'Europe',

    children: [
      { label: 'Albania', value: 'ALB', flag: 'al' },
      { label: 'Andorra', value: 'AND', flag: 'ad' },
      { label: 'Austria', value: 'AUT', flag: 'at' },
      { label: 'Belarus', value: 'BLR', flag: 'by' },
      { label: 'Belgium', value: 'BEL', flag: 'be' },
      {
        label: 'Bosnia Hercegovina',
        value: 'BIH',
        flag: 'ba',
      },
      { label: 'Bulgaria', value: 'BGR', flag: 'bg' },
      { label: 'Croatia', value: 'HRV', flag: 'hr' },
      { label: 'Cyprus', value: 'CYP', flag: 'cy' },
      {
        label: 'Czech',
        value: 'CZE',
        flag: 'cz',
      },
      { label: 'Denmark', value: 'DNK', flag: 'dk' },
      { label: 'Dominica', value: 'DMA', flag: 'dm' },
      {
        label: 'Dominican',
        value: 'DOM',
        flag: 'do',
      },
      { label: 'Estonia', value: 'EST', flag: 'ee' },
      {
        label: 'Faroe Islands',
        value: 'FO',
        flag: 'fo',
      },
      { label: 'Finland', value: 'FIN', flag: 'fi' },
      { label: 'France', value: 'FRA', flag: 'fr' },
      { label: 'Georgia', value: 'GEO', flag: 'ge' },
      { label: 'Germany', value: 'DEU', flag: 'de' },
      { label: 'Gibraltar', value: 'GIB', flag: 'gi' },
      { label: 'Greece', value: 'GRC', flag: 'gr' },
      { label: 'Greenland', value: 'GRL', flag: 'gl' },
      { label: 'Guernsey', value: 'GG', flag: 'gg' },
      { label: 'Hungary', value: 'HUN', flag: 'hu' },
      { label: 'Iceland', value: 'ISL', flag: 'is' },
      { label: 'Ireland', value: 'IRL', flag: 'ie' },
      { label: 'Isle of Man', value: 'IM', flag: 'im' },
      { label: 'Italy', value: 'ITA', flag: 'it' },
      { label: 'Jersey', value: 'JE', flag: 'je' },
      { label: 'Latvia', value: 'LVA', flag: 'lv' },
      { label: 'Liechtenstein', value: 'LIE', flag: 'li' },
      { label: 'Lithuania', value: 'LTU', flag: 'lt' },
      { label: 'Luxembourg', value: 'LUX', flag: 'lu' },
      { label: 'Malta', value: 'MLT', flag: 'mt' },
      { label: 'Mayotte', value: 'MYT', flag: 'yt' },
      {
        label: 'Moldavia',
        value: 'MDA',
        flag: 'md',
      },
      { label: 'Monaco', value: 'MCO', flag: 'mc' },
      {
        label: 'Serbia and Montenegro',
        value: 'S&M',
        flag: 'me',
      },
      {
        label: 'Netherlands (the)',
        value: 'NLD',
        flag: 'nl',
      },
      { label: 'Norway', value: 'NOR', flag: 'no' },
      { label: 'Poland', value: 'POL', flag: 'pl' },
      { label: 'Portugal', value: 'PRT', flag: 'pt' },
      {
        label: 'Macedonia',
        value: 'MKD',
        flag: 'mk',
      },
      { label: 'Romania', value: 'ROM', flag: 'ro' },
      {
        label: 'Russia',
        value: 'RUS',
        flag: 'ru',
      },
      { label: 'San Marino', value: 'SMR', flag: 'sm' },
      { label: 'Slovakia', value: 'SVK', flag: 'rs' },
      { label: 'Slovenia', value: 'SVN', flag: 'sk' },
      { label: 'Spain', value: 'ESP', flag: 'es' },
      { label: 'Swaziland', value: 'SWZ', flag: 'sz' },
      { label: 'Sweden', value: 'SWE', flag: 'se' },
      { label: 'Switzerland', value: 'CHE', flag: 'ch' },
      { label: 'Turkey', value: 'TUR', flag: 'tr' },
      { label: 'Ukraine', value: 'UKR', flag: 'ua' },
      {
        label: 'United Kingdom',
        value: 'ENG',
        flag: 'gb',
      },
    ],
  },
  {
    label: 'Latin America',
    value: 'Latin_America',

    children: [
      { label: 'Angola', value: 'AGO', flag: 'ao' },
      {
        label: 'Antigua and Barbuda',
        value: 'ATG',
        flag: 'ag',
      },
      { label: 'Argentina', value: 'ARG', flag: 'ar' },
      { label: 'Aruba', value: 'ABW', flag: 'aw' },
      { label: 'Bahamas (The)', value: 'BHS', flag: 'bs' },
      { label: 'Barbados', value: 'BRB', flag: 'bb' },
      { label: 'Belize', value: 'BLZ', flag: 'bz' },
      { label: 'Bermuda', value: 'BMU', flag: 'bm' },
      { label: 'Bolivia', value: 'BOL', flag: 'bo' },
      { label: 'Brazil', value: 'BRA', flag: 'br' },
      {
        label: 'Cayman Is',
        value: 'CYM',
        flag: 'ky',
      },
      { label: 'Chile', value: 'CHL', flag: 'cl' },
      { label: 'Colombia', value: 'COL', flag: 'co' },
      {
        label: 'Cook Is',
        value: 'COK',
        flag: 'ck',
      },
      { label: 'Costa Rica', value: 'CRI', flag: 'cr' },
      { label: 'Cuba', value: 'CUB', flag: 'cu' },
      { label: 'Ecuador', value: 'ECU', flag: 'ec' },
      { label: 'El Salvador', value: 'SLV', flag: 'sv' },
      { label: 'French Guiana', value: 'GF', flag: 'gf' },
      { label: 'Grenada', value: 'GRD', flag: 'gd' },
      { label: 'Guatemala', value: 'GTM', flag: 'gp' },
      { label: 'Guinea-Bissau', value: 'GNB', flag: 'gt' },
      { label: 'Guyana', value: 'GUY', flag: 'gw' },
      { label: 'Haiti', value: 'HTI', flag: 'gy' },
      { label: 'Honduras', value: 'HND', flag: 'ht' },
      { label: 'Jamaica', value: 'JAM', flag: 'hn' },
      { label: 'Martinique', value: 'MTQ', flag: 'jm' },
      { label: 'Mexico', value: 'MEX', flag: 'mx' },
      { label: 'Nicaragua', value: 'NIC', flag: 'ni' },
      { label: 'Panama', value: 'PAN', flag: 'pa' },
      { label: 'Paraguay', value: 'PRY', flag: 'py' },
      { label: 'Peru', value: 'PER', flag: 'pe' },
      { label: 'Puerto Rico', value: 'PTR', flag: 'pr' },
      {
        label: 'St Kitts-Nevis',
        value: 'KNA',
        flag: 'kn',
      },
      { label: 'Saint Lucia', value: 'LC', flag: 'kn' },
      {
        label: 'Saint Pierre and Miquelon',
        value: 'PM',
        flag: 'pm',
      },
      {
        label: 'Saint Vincent and the Grenadines',
        value: 'VAG',
        flag: 'vc',
      },
      { label: 'Suriname', value: 'SUR', flag: 'sr' },
      { label: 'East Timor', value: 'TMP', flag: 'tl' },
      { label: 'Togo', value: 'TGO', flag: 'tg' },
      {
        label: 'Trinidad and Tobago',
        value: 'TTO',
        flag: 'tt',
      },
      {
        label: 'Turks and Caicos Islands',
        value: 'TC',
        flag: 'tc',
      },
      { label: 'Uruguay', value: 'URY', flag: 'uy' },
      { label: 'Venezuela', value: 'VEN', flag: 've' },
      {
        label: 'The British Virgin Islands',
        value: 'VG',
        flag: 'vg',
      },
      {
        label: 'The United States Virgin Islands',
        value: 'VI',
        flag: 'vi',
      },
    ],
  },
  {
    label: 'North America',
    value: 'North_America',

    children: [
      { label: 'Canada', value: 'CAN', flag: 'ca' },
      {
        label: 'United States',
        value: 'USA',
        flag: 'us',
      },
    ],
  },
  {
    label: 'Oceania',
    value: 'Oceania',

    children: [
      { label: 'American Samoa', value: 'AS', flag: 'as' },
      { label: 'Australia', value: 'AUS', flag: 'au' },
      { label: 'Fiji', value: 'FJI', flag: 'fj' },
      {
        label: 'French Polynesia',
        value: 'PYF',
        flag: 'pf',
      },
      { label: 'Guam', value: 'GU', flag: 'gu' },
      {
        label: 'Marshall Is Rep',
        value: 'MHL',
        flag: 'mh',
      },
      {
        label: 'Micronesia (the Federated States of)',
        value: 'FSM',
        flag: 'fm',
      },
      { label: 'New Caledonia', value: 'NCL', flag: 'nc' },
      { label: 'New Zealand', value: 'NZL', flag: 'nz' },
      {
        label: 'Northern Mariana Islands',
        value: 'MP',
        flag: 'mp',
      },
      { label: 'Palau', value: 'PLW', flag: 'pw' },
      {
        label: 'Papua New Guinea',
        value: 'PNG',
        flag: 'pg',
      },
      { label: 'Samoa', value: 'WSM', flag: 'wa' },
      {
        label: 'Solomon Islands (the)',
        value: 'SLB',
        flag: 'sb',
      },
      { label: 'Tonga', value: 'TON', flag: 'to' },
      { label: 'Vanuatu', value: 'VUT', flag: 'vu' },
      {
        label: 'Wallis and Futuna',
        value: 'WF',
        flag: 'wf',
      },
    ],
  },
  {
    label: 'Others',
    value: 'Others',
    children: [
      {
        label: 'British Indian Ocean Territory',
        value: 'IO',
        flag: 'io',
      },
      { label: 'Côte d\'Ivoire', value: 'ci', flag: 'ci' },
      { label: 'Curacao', value: 'cw', flag: 'cw' },
      { label: 'European Union', value: 'eu', flag: 'eu' },
      {
        label: 'Curaçao',
        value: 'CW',
        flag: 'fk',
      },
      {
        label: 'Vatican City State',
        value: 'VAT',
        flag: 'va',
      },
      { label: 'Kiribati', value: 'KIR', flag: 'ki' },
      {
        label: 'Korea,DPR',
        value: 'PRK',
        flag: 'kp',
      },
      { label: 'Nauru', value: 'NRU', flag: 'nr' },
      { label: 'Norfolk Island', value: 'NF', flag: 'nf' },
      {
        label: 'Saint Martin',
        value: 'MF',
        flag: 'mf',
      },
      { label: 'Sint Maarten', value: 'SX', flag: 'sx' },
      { label: 'Tokelau', value: 'TK', flag: 'tk' },
      { label: 'Turkmenistan', value: 'TKM', flag: 'tm' },
      { label: 'Tuvalu', value: 'TUV', flag: 'tv' },
      { label: 'Others', value: 'Others' },
    ],
  },
];

export const CHANNEL = [
  { value: '2', label: 'FB News Feed' },
  { value: '39', label: 'Instagram' },
  { value: '3', label: 'Audience Network' },
  { value: '51', label: 'Messenger' },
  { value: '1', label: 'Google Ads(Admob)' },
  { value: '6', label: 'YouTube' },
  { value: '10', label: 'Twitter' },
  { value: '5', label: 'UnityAds' },
  { value: '19', label: 'Vungle' },
  { value: '20', label: 'AppLovin' },
  { value: '24', label: 'AdColony' },
  { value: '25', label: 'Chartboost' },
  { value: '40', label: 'Pinterest' },
  { value: '42', label: 'ironSource' },
  { value: '23', label: 'Yahoo!' },
  { value: '44', label: 'reddit' },
  { value: '43', label: 'TikTok' },
  { value: '45', label: 'TopBuzz' },
  { value: '46', label: 'Mintegral (Mobvista)' },
  { value: '52', label: 'Snapchat' },
  { value: '49', label: 'MoPub' },
  { value: '47', label: 'Tapjoy' },
  { value: '27', label: 'NAVER(네이버)' },
  { value: '26', label: 'Daum(다음)' },
  { value: '34', label: 'Nate(네이트)' },
  { value: '28', label: 'Ameba(アメーバ)' },
  { value: '36', label: 'Yahoo! Japan' },
  { value: '310', label: 'Pangle(TikTok Audience Network)' },
  { value: '301', label: 'Gunosy(グノシー)' },
  { value: '303', label: 'Zucks' },
  { value: '300', label: 'SmartNews(スマートニュース)' },
  { value: '304', label: 'i-mobile' },
  { value: '305', label: 'AkaNe' },
  { value: '307', label: 'Nend' },
  { value: '308', label: 'AMoAd' },
  { value: '38', label: 'INMOBI' },
];

export const LANGUAGE = [
  { value: 'zh-CN', label: 'zh-CN' },
  { value: 'en', label: 'English' },
  { value: 'ja', label: 'Japanese' },
  { value: 'ko', label: 'Korean' },
  { value: 'ar', label: 'Arabic' },
  { value: 'zh-TW', label: 'zh-TW' },
  { value: 'th', label: 'Thai' },
  { value: 'de', label: 'German' },
  { value: 'es', label: 'Spanish' },
  { value: 'fr', label: 'French' },
  { value: 'hi', label: 'Dard' },
  { value: 'it', label: 'Italian' },
  { value: 'ms', label: 'Malay' },
  { value: 'nl', label: 'Dutch' },
  { value: 'pt', label: 'Portuguese' },
  { value: 'ru', label: 'Russian' },
  { value: 'vi', label: 'Vietnamese' },
  { value: 'id', label: 'Indonesian' },
  { value: 'he', label: 'Hebrew' },
  { value: 'tl', label: 'Tagalog language' },
  { value: 'no', label: 'Norwegian' },
  { value: 'tr', label: 'Turkish' },
  { value: 'pl', label: 'Polish' },
];

export const PLATFORM = [
  { label: 'iOS', value: '1' },
  { label: 'Android', value: '2' },
  { label: 'PC', value: '101' },
];

export const CREATIVETYPE = [
  { label: 'Image', value: '1' },
  { label: 'Video', value: '2' },
];

export const COMPETITORTYPE = [
  { label: 'PC', value: 'pc' },
  { label: 'Mobile', value: 'mobile' },
];

export const COMPETITORTYPE_MAP: Record<string, string> = COMPETITORTYPE
  .reduce((acc, { label, value }) => ({ ...acc, [value]: label }), {});

export const DATABRAIN_CHANNEL_OPTIONS = [
  { label: 'Epgame', value: 'epgame' },
  { label: 'Steam', value: 'steam,steam_community' },
  { label: 'Twitch', value: 'twitch,twitch_keyword' },
  { label: 'Twitter', value: 'twitter' },
  // { label: 'Twitch Keyword', value: 'twitch_keyword' },
  { label: 'Vgtime', value: 'vgtime' },
  { label: 'Facebook', value: 'facebook,facebook_gaming' },
  // { label: 'Facebook Gaming', value: 'facebook_gaming' },
  { label: 'Reddit', value: 'reddit' },
  { label: 'Metacritic', value: 'metacritic' },
  // { label: 'Youtube Keyword', value: 'youtube_keyword' },
  { label: 'Youtube', value: 'youtube,youtube_keyword,ytg,youtube_gaming' },
  // { label: 'Youtube', value: 'ytg' },
  // { label: 'Youtube Gaming', value: 'youtube_gaming' },
  { label: 'Opencritic', value: 'opencritic' },
  { label: 'App Store', value: 'app store' },
  { label: 'Google Play', value: 'google play' },
  // { label: 'Tiktok', value: 'tiktok' }, // 排除tiktok
  { label: 'Vk', value: 'vk' },
  { label: 'Cafe', value: 'cafe' },
  { label: 'Naver Cafe', value: 'navercafe' },
  { label: 'Naver Game', value: 'navergame' },
  { label: 'Instagram', value: 'instagram' },
  { label: 'Discord', value: 'discord' },
  { label: 'Taptap', value: 'taptap' },
  { label: 'Kwai', value: 'kwai' },
  { label: 'Ruliweb', value: 'ruliweb' },
  { label: 'Xiaomi Market', value: 'mi' },
  { label: 'Oppo Store', value: 'oppo' },
  { label: 'Line', value: 'line' },
  { label: 'Bilibili', value: 'bilibili' },
  { label: 'Funcom Forum', value: 'funcom forum' },
  // { label: 'Steam Community', value: 'steam_community' },
];

export const DATABRAIN_CHANNEL_CODE: Record<string, { img: string, name: string}> = {
  epgame: { img: 'epgame', name: 'Epgame' },
  steam: { img: 'steam', name: 'Steam' },
  twitch: { img: 'twitch', name: 'Twitch' },
  twitter: { img: 'twitter', name: 'Twitter' },
  twitch_keyword: { img: 'twitch', name: 'Twitch Keyword' },
  vgtime: { img: 'vgtime', name: 'Vgtime' },
  facebook: { img: 'facebook', name: 'Facebook' },
  facebook_gaming: { img: 'facebook_gaming', name: 'Facebook Gaming' },
  reddit: { img: 'reddit', name: 'Reddit' },
  metacritic: { img: 'metacritic', name: 'Metacritic' },
  youtube_keyword: { img: 'youtube', name: 'youtube Keyword' },
  youtube: { img: 'youtube', name: 'Youtube' },
  ytg: { img: 'youtube', name: 'Youtube' },
  youtube_gaming: { img: 'youtube_gaming', name: 'Youtube Gaming' },
  opencritic: { img: 'opencritic', name: 'Opencritic' },
  'app store': { img: 'app_store', name: 'App Store' },
  'google play': { img: 'google_play', name: 'Google Play' },
  tiktok: { img: 'tiktok', name: 'Tiktok' },
  vk: { img: 'vk', name: 'Vk' },
  cafe: { img: 'cafe', name: 'Cafe' },
  navercafe: { img: 'cafe', name: 'Naver Cafe' },
  navergame: { img: 'navergame', name: 'Naver Game' },
  instagram: { img: 'instagram', name: 'Instagram' },
  discord: { img: 'discord', name: 'Discord' },
  taptap: { img: 'taptap', name: 'Taptap' },
  kwai: { img: 'kwai', name: 'kwai' },
  ruliweb: { img: 'ruliweb', name: 'Ruliweb' },
  mi: { img: 'mi', name: 'Xiaomi Market' },
  oppo: { img: 'oppo', name: 'Oppo Store' },
  line: { img: 'line', name: 'Line' },
  bilibili: { img: 'bilibili', name: 'Bilibili' },
  'funcom forum': { img: 'funcom', name: 'Funcom Forum' },
  steam_community: { img: 'steam', name: 'Steam Community' },
};
