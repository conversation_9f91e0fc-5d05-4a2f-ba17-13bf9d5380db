{"Platform": [{"value": "2", "text": "IOS"}, {"value": "1", "text": "Android"}], "world": [{"label": "Africa", "value": "Africa", "children": [{"label": "Algeria", "value": "dz"}, {"label": "Benin", "value": "bj"}, {"label": "Botswana", "value": "bw"}, {"label": "Burkina Faso", "value": "bf"}, {"label": "Burundi", "value": "bi"}, {"label": "Cameroon", "value": "cm"}, {"label": "Cape Verde", "value": "cv"}, {"label": "Central African Republic (the)", "value": "cf"}, {"label": "Chad", "value": "td"}, {"label": "Comoros", "value": "km"}, {"label": "Democratic Republic of the Congo", "value": "cd"}, {"label": "Djibouti", "value": "dj"}, {"label": "Egypt", "value": "eg"}, {"label": "Equatorial Guinea", "value": "gq"}, {"label": "Ethiopia", "value": "et"}, {"label": "Gabon", "value": "ga"}, {"label": "Gambia (The)", "value": "gm"}, {"label": "Ghana", "value": "gh"}, {"label": "Guinea", "value": "gn"}, {"label": "Kenya", "value": "ke"}, {"label": "Lesotho", "value": "ls"}, {"label": "Liberia", "value": "lr"}, {"label": "Libyan Arab <PERSON><PERSON> (the)", "value": "ly"}, {"label": "Madagascar", "value": "mg"}, {"label": "Malawi", "value": "mw"}, {"label": "Mali", "value": "ml"}, {"label": "Mauritania", "value": "mr"}, {"label": "Mauritius", "value": "mu"}, {"label": "Morocco", "value": "ma"}, {"label": "Mozambique", "value": "mz"}, {"label": "Namibia", "value": "na"}, {"label": "Niger (the)", "value": "ne"}, {"label": "Nigeria", "value": "ng"}, {"label": "Republic of the Congo", "value": "cg"}, {"label": "Réunion", "value": "re"}, {"label": "Rwanda", "value": "rw"}, {"label": "Sao Tome and Principe", "value": "st"}, {"label": "Senegal", "value": "sn"}, {"label": "Seychelles", "value": "sc"}, {"label": "Sierra Leone", "value": "sl"}, {"label": "Somalia", "value": "so"}, {"label": "South Africa", "value": "za"}, {"label": "Tanzania,United Republic of", "value": "tz"}, {"label": "Tunisia", "value": "tn"}, {"label": "Uganda", "value": "ug"}, {"label": "Zambia", "value": "zm"}, {"label": "Zimbabwe", "value": "zw"}]}, {"label": "Asia", "value": "Asia", "children": [{"label": "China", "value": "cn"}, {"label": "Afghanistan", "value": "af"}, {"label": "Armenia", "value": "am"}, {"label": "Azerbaijan", "value": "az"}, {"label": "Bahrain", "value": "bh"}, {"label": "Bangladesh", "value": "bd"}, {"label": "Bhutan", "value": "bt"}, {"label": "Brunei Darussalam", "value": "bn"}, {"label": "Cambodia", "value": "kh"}, {"label": "Hong Kong", "value": "hk"}, {"label": "India", "value": "in"}, {"label": "Indonesia", "value": "id"}, {"label": "Iran (the Islamic Republic of)", "value": "ir"}, {"label": "Iraq", "value": "iq"}, {"label": "Israel", "value": "il"}, {"label": "Japan", "value": "jp"}, {"label": "Jordan", "value": "jo"}, {"label": "Kazakhstan", "value": "kz"}, {"label": "Korea (the Republic of)", "value": "kr"}, {"label": "Kuwait", "value": "kw"}, {"label": "Kyrgyzstan", "value": "kg"}, {"label": "Lao People's Democratic Republic (the)", "value": "la"}, {"label": "Lebanon", "value": "lb"}, {"label": "Macao", "value": "mo"}, {"label": "Malaysia", "value": "my"}, {"label": "Maldives", "value": "mv"}, {"label": "Mongolia", "value": "mn"}, {"label": "Myanmar", "value": "mm"}, {"label": "Nepal", "value": "np"}, {"label": "Oman", "value": "om"}, {"label": "Pakistan", "value": "pk"}, {"label": "Palestinian Territory (the Occupied)", "value": "ps"}, {"label": "Philippines (the)", "value": "ph"}, {"label": "Qatar", "value": "qa"}, {"label": "Saudi Arabia", "value": "sa"}, {"label": "Singapore", "value": "sg"}, {"label": "Sri Lanka", "value": "lk"}, {"label": "Sudan (the)", "value": "sd"}, {"label": "Syrian Arab Republic (the)", "value": "sy"}, {"label": "Taiwan (Province of China)", "value": "tw"}, {"label": "Tajikistan", "value": "tj"}, {"label": "Thailand", "value": "th"}, {"label": "United Arab Emirates (the)", "value": "ae"}, {"label": "Uzbekistan", "value": "uz"}, {"label": "Viet Nam", "value": "vn"}, {"label": "Yemen", "value": "ye"}]}, {"label": "Europe", "value": "Europe", "children": [{"label": "Albania", "value": "al"}, {"label": "Andorra", "value": "ad"}, {"label": "Austria", "value": "at"}, {"label": "Belarus", "value": "by"}, {"label": "Belgium", "value": "be"}, {"label": "Bosnia and Herzegovina", "value": "ba"}, {"label": "Bulgaria", "value": "bg"}, {"label": "Croatia", "value": "hr"}, {"label": "Cyprus", "value": "cy"}, {"label": "Czech Republic (the)", "value": "cz"}, {"label": "Denmark", "value": "dk"}, {"label": "Dominica", "value": "dm"}, {"label": "Dominican Republic (the)", "value": "do"}, {"label": "Estonia", "value": "ee"}, {"label": "Faroe Islands (the)", "value": "fo"}, {"label": "Finland", "value": "fi"}, {"label": "France", "value": "fr"}, {"label": "Georgia", "value": "ge"}, {"label": "Germany", "value": "de"}, {"label": "Gibraltar", "value": "gi"}, {"label": "Greece", "value": "gr"}, {"label": "Greenland", "value": "gl"}, {"label": "Guernsey", "value": "gg"}, {"label": "Hungary", "value": "hu"}, {"label": "Iceland", "value": "is"}, {"label": "Ireland", "value": "ie"}, {"label": "Isle of Man", "value": "im"}, {"label": "Italy", "value": "it"}, {"label": "Jersey", "value": "je"}, {"label": "Latvia", "value": "lv"}, {"label": "Liechtenstein", "value": "li"}, {"label": "Lithuania", "value": "lt"}, {"label": "Luxembourg", "value": "lu"}, {"label": "Malta", "value": "mt"}, {"label": "Mayotte", "value": "yt"}, {"label": "Moldova (the Republic of)", "value": "md"}, {"label": "Monaco", "value": "mc"}, {"label": "Montenegro", "value": "me"}, {"label": "Netherlands (the)", "value": "nl"}, {"label": "Norway", "value": "no"}, {"label": "Poland", "value": "pl"}, {"label": "Portugal", "value": "pt"}, {"label": "Republic of Macedonia (FYROM)", "value": "mk"}, {"label": "Romania", "value": "ro"}, {"label": "Russian Federation", "value": "ru"}, {"label": "San Marino", "value": "sm"}, {"label": "Serbia", "value": "rs"}, {"label": "Slovakia", "value": "sk"}, {"label": "Slovenia", "value": "si"}, {"label": "Spain", "value": "es"}, {"label": "Swaziland", "value": "sz"}, {"label": "Sweden", "value": "se"}, {"label": "Switzerland", "value": "ch"}, {"label": "Turkey", "value": "tr"}, {"label": "Ukraine", "value": "ua"}, {"label": "United Kingdom (the)", "value": "gb"}]}, {"label": "Latin America", "value": "Latin_America", "children": [{"label": "Angola", "value": "ao"}, {"label": "<PERSON><PERSON><PERSON>", "value": "ai"}, {"label": "Antigua and Barbuda", "value": "ag"}, {"label": "Argentina", "value": "ar"}, {"label": "Aruba", "value": "aw"}, {"label": "Bahamas (The)", "value": "bs"}, {"label": "Barbados", "value": "bb"}, {"label": "Belize", "value": "bz"}, {"label": "Bermuda", "value": "bm"}, {"label": "Bolivia", "value": "bo"}, {"label": "Brazil", "value": "br"}, {"label": "Cayman Islands (the)", "value": "ky"}, {"label": "Chile", "value": "cl"}, {"label": "Colombia", "value": "co"}, {"label": "Cook Islands (the)", "value": "ck"}, {"label": "Costa Rica", "value": "cr"}, {"label": "Cuba", "value": "cu"}, {"label": "Ecuador", "value": "ec"}, {"label": "El Salvador", "value": "sv"}, {"label": "French Guiana", "value": "gf"}, {"label": "Grenada", "value": "gd"}, {"label": "Guadeloupe", "value": "gp"}, {"label": "Guatemala", "value": "gt"}, {"label": "Guinea-Bissau", "value": "gw"}, {"label": "Guyana", "value": "gy"}, {"label": "Haiti", "value": "ht"}, {"label": "Honduras", "value": "hn"}, {"label": "Jamaica", "value": "jm"}, {"label": "Martinique", "value": "mq"}, {"label": "Mexico", "value": "mx"}, {"label": "Nicaragua", "value": "ni"}, {"label": "Panama", "value": "pa"}, {"label": "Paraguay", "value": "py"}, {"label": "Peru", "value": "pe"}, {"label": "Puerto Rico", "value": "pr"}, {"label": "Saint Kitts and Nevis", "value": "kn"}, {"label": "Saint Lucia", "value": "lc"}, {"label": "Saint Pierre and Miquelon", "value": "pm"}, {"label": "Saint Vincent and the Grenadines", "value": "vc"}, {"label": "Suriname", "value": "sr"}, {"label": "Timor-Leste", "value": "tl"}, {"label": "Togo", "value": "tg"}, {"label": "Trinidad and Tobago", "value": "tt"}, {"label": "Turks and Caicos Islands (the)", "value": "tc"}, {"label": "Uruguay", "value": "uy"}, {"label": "Venezuela", "value": "ve"}, {"label": "Virgin Islands (British)", "value": "vg"}, {"label": "Virgin Islands (U.S.)", "value": "vi"}]}, {"label": "North America", "value": "North_America", "children": [{"label": "Canada", "value": "ca"}, {"label": "United States (the)", "value": "us"}]}, {"label": "Oceania", "value": "Oceania", "children": [{"label": "American Samoa", "value": "as"}, {"label": "Australia", "value": "au"}, {"label": "Fiji", "value": "fj"}, {"label": "French Polynesia", "value": "pf"}, {"label": "Guam", "value": "gu"}, {"label": "Marshall Islands (the)", "value": "mh"}, {"label": "Micronesia (the Federated States of)", "value": "fm"}, {"label": "New Caledonia", "value": "nc"}, {"label": "New Zealand", "value": "nz"}, {"label": "Northern Mariana Islands", "value": "mp"}, {"label": "<PERSON><PERSON>", "value": "pw"}, {"label": "Papua New Guinea", "value": "pg"}, {"label": "Samoa", "value": "ws"}, {"label": "Solomon Islands (the)", "value": "sb"}, {"label": "Tonga", "value": "to"}, {"label": "Vanuatu", "value": "vu"}, {"label": "Wallis and Futuna", "value": "wf"}]}, {"label": "Others", "value": "Others", "children": [{"label": "British Indian Ocean Territory (the)", "value": "io"}, {"label": "Caribbean Netherlands", "value": "bq"}, {"label": "Côte d'Ivoire", "value": "ci"}, {"label": "Curacao", "value": "cw"}, {"label": "European Union", "value": "eu"}, {"label": "Falkland Islands", "value": "fk"}, {"label": "Holy See (the) [Vatican City State]", "value": "va"}, {"label": "Kiribati", "value": "ki"}, {"label": "Korea (the Democratic People's Republic of)", "value": "kp"}, {"label": "Nauru", "value": "nr"}, {"label": "Norfolk Island", "value": "nf"}, {"label": "<PERSON> (France)", "value": "mf"}, {"label": "Sint Maarten", "value": "sx"}, {"label": "South Sudan", "value": "ss"}, {"label": "Tokelau", "value": "tk"}, {"label": "Turkmenistan", "value": "tm"}, {"label": "Tuvalu", "value": "tv"}, {"label": "Others", "value": "Others"}]}], "worldMarket": [{"label": "Africa", "value": "Africa", "children": [{"label": "Algeria", "value": "dz"}, {"label": "Benin", "value": "bj"}, {"label": "Botswana", "value": "bw"}, {"label": "Burkina Faso", "value": "bf"}, {"label": "Burundi", "value": "bi"}, {"label": "Cameroon", "value": "cm"}, {"label": "Cape Verde", "value": "cv"}, {"label": "Central African Republic (the)", "value": "cf"}, {"label": "Chad", "value": "td"}, {"label": "Comoros", "value": "km"}, {"label": "Democratic Republic of the Congo", "value": "cd"}, {"label": "Djibouti", "value": "dj"}, {"label": "Egypt", "value": "eg"}, {"label": "Equatorial Guinea", "value": "gq"}, {"label": "Ethiopia", "value": "et"}, {"label": "Gabon", "value": "ga"}, {"label": "Gambia (The)", "value": "gm"}, {"label": "Ghana", "value": "gh"}, {"label": "Guinea", "value": "gn"}, {"label": "Kenya", "value": "ke"}, {"label": "Lesotho", "value": "ls"}, {"label": "Liberia", "value": "lr"}, {"label": "Libyan Arab <PERSON><PERSON> (the)", "value": "ly"}, {"label": "Madagascar", "value": "mg"}, {"label": "Malawi", "value": "mw"}, {"label": "Mali", "value": "ml"}, {"label": "Mauritania", "value": "mr"}, {"label": "Mauritius", "value": "mu"}, {"label": "Morocco", "value": "ma"}, {"label": "Mozambique", "value": "mz"}, {"label": "Namibia", "value": "na"}, {"label": "Niger (the)", "value": "ne"}, {"label": "Nigeria", "value": "ng"}, {"label": "Republic of the Congo", "value": "cg"}, {"label": "Réunion", "value": "re"}, {"label": "Rwanda", "value": "rw"}, {"label": "Sao Tome and Principe", "value": "st"}, {"label": "Senegal", "value": "sn"}, {"label": "Seychelles", "value": "sc"}, {"label": "Sierra Leone", "value": "sl"}, {"label": "Somalia", "value": "so"}, {"label": "South Africa", "value": "za"}, {"label": "Tanzania,United Republic of", "value": "tz"}, {"label": "Tunisia", "value": "tn"}, {"label": "Uganda", "value": "ug"}, {"label": "Zambia", "value": "zm"}, {"label": "Zimbabwe", "value": "zw"}]}, {"label": "Asia", "value": "Asia", "children": [{"label": "China", "value": "cn"}, {"label": "Afghanistan", "value": "af"}, {"label": "Armenia", "value": "am"}, {"label": "Azerbaijan", "value": "az"}, {"label": "Bahrain", "value": "bh"}, {"label": "Bangladesh", "value": "bd"}, {"label": "Bhutan", "value": "bt"}, {"label": "Brunei Darussalam", "value": "bn"}, {"label": "Cambodia", "value": "kh"}, {"label": "Hong Kong", "value": "hk"}, {"label": "India", "value": "in"}, {"label": "Indonesia", "value": "id"}, {"label": "Iran (the Islamic Republic of)", "value": "ir"}, {"label": "Iraq", "value": "iq"}, {"label": "Israel", "value": "il"}, {"label": "Japan", "value": "jp"}, {"label": "Jordan", "value": "jo"}, {"label": "Kazakhstan", "value": "kz"}, {"label": "Korea (the Republic of)", "value": "kr"}, {"label": "Kuwait", "value": "kw"}, {"label": "Kyrgyzstan", "value": "kg"}, {"label": "Lao People's Democratic Republic (the)", "value": "la"}, {"label": "Lebanon", "value": "lb"}, {"label": "Macao", "value": "mo"}, {"label": "Malaysia", "value": "my"}, {"label": "Maldives", "value": "mv"}, {"label": "Mongolia", "value": "mn"}, {"label": "Myanmar", "value": "mm"}, {"label": "Nepal", "value": "np"}, {"label": "Oman", "value": "om"}, {"label": "Pakistan", "value": "pk"}, {"label": "Palestinian Territory (the Occupied)", "value": "ps"}, {"label": "Philippines (the)", "value": "ph"}, {"label": "Qatar", "value": "qa"}, {"label": "Saudi Arabia", "value": "sa"}, {"label": "Singapore", "value": "sg"}, {"label": "Sri Lanka", "value": "lk"}, {"label": "Sudan (the)", "value": "sd"}, {"label": "Syrian Arab Republic (the)", "value": "sy"}, {"label": "Taiwan (Province of China)", "value": "tw"}, {"label": "Tajikistan", "value": "tj"}, {"label": "Thailand", "value": "th"}, {"label": "United Arab Emirates (the)", "value": "ae"}, {"label": "Uzbekistan", "value": "uz"}, {"label": "Viet Nam", "value": "vn"}, {"label": "Yemen", "value": "ye"}]}, {"label": "Europe", "value": "Europe", "children": [{"label": "Albania", "value": "al"}, {"label": "Andorra", "value": "ad"}, {"label": "Austria", "value": "at"}, {"label": "Belarus", "value": "by"}, {"label": "Belgium", "value": "be"}, {"label": "Bosnia and Herzegovina", "value": "ba"}, {"label": "Bulgaria", "value": "bg"}, {"label": "Croatia", "value": "hr"}, {"label": "Cyprus", "value": "cy"}, {"label": "Czech Republic (the)", "value": "cz"}, {"label": "Denmark", "value": "dk"}, {"label": "Dominica", "value": "dm"}, {"label": "Dominican Republic (the)", "value": "do"}, {"label": "Estonia", "value": "ee"}, {"label": "Faroe Islands (the)", "value": "fo"}, {"label": "Finland", "value": "fi"}, {"label": "France", "value": "fr"}, {"label": "Georgia", "value": "ge"}, {"label": "Germany", "value": "de"}, {"label": "Gibraltar", "value": "gi"}, {"label": "Greece", "value": "gr"}, {"label": "Greenland", "value": "gl"}, {"label": "Guernsey", "value": "gg"}, {"label": "Hungary", "value": "hu"}, {"label": "Iceland", "value": "is"}, {"label": "Ireland", "value": "ie"}, {"label": "Isle of Man", "value": "im"}, {"label": "Italy", "value": "it"}, {"label": "Jersey", "value": "je"}, {"label": "Latvia", "value": "lv"}, {"label": "Liechtenstein", "value": "li"}, {"label": "Lithuania", "value": "lt"}, {"label": "Luxembourg", "value": "lu"}, {"label": "Malta", "value": "mt"}, {"label": "Mayotte", "value": "yt"}, {"label": "Moldova (the Republic of)", "value": "md"}, {"label": "Monaco", "value": "mc"}, {"label": "Montenegro", "value": "me"}, {"label": "Netherlands (the)", "value": "nl"}, {"label": "Norway", "value": "no"}, {"label": "Poland", "value": "pl"}, {"label": "Portugal", "value": "pt"}, {"label": "Republic of Macedonia (FYROM)", "value": "mk"}, {"label": "Romania", "value": "ro"}, {"label": "Russian Federation", "value": "ru"}, {"label": "San Marino", "value": "sm"}, {"label": "Serbia", "value": "rs"}, {"label": "Slovakia", "value": "sk"}, {"label": "Slovenia", "value": "si"}, {"label": "Spain", "value": "es"}, {"label": "Swaziland", "value": "sz"}, {"label": "Sweden", "value": "se"}, {"label": "Switzerland", "value": "ch"}, {"label": "Turkey", "value": "tr"}, {"label": "Ukraine", "value": "ua"}, {"label": "United Kingdom (the)", "value": "uk"}]}, {"label": "Latin America", "value": "Latin_America", "children": [{"label": "Angola", "value": "ao"}, {"label": "<PERSON><PERSON><PERSON>", "value": "ai"}, {"label": "Antigua and Barbuda", "value": "ag"}, {"label": "Argentina", "value": "ar"}, {"label": "Aruba", "value": "aw"}, {"label": "Bahamas (The)", "value": "bs"}, {"label": "Barbados", "value": "bb"}, {"label": "Belize", "value": "bz"}, {"label": "Bermuda", "value": "bm"}, {"label": "Bolivia", "value": "bo"}, {"label": "Brazil", "value": "br"}, {"label": "Cayman Islands (the)", "value": "ky"}, {"label": "Chile", "value": "cl"}, {"label": "Colombia", "value": "co"}, {"label": "Cook Islands (the)", "value": "ck"}, {"label": "Costa Rica", "value": "cr"}, {"label": "Cuba", "value": "cu"}, {"label": "Ecuador", "value": "ec"}, {"label": "El Salvador", "value": "sv"}, {"label": "French Guiana", "value": "gf"}, {"label": "Grenada", "value": "gd"}, {"label": "Guadeloupe", "value": "gp"}, {"label": "Guatemala", "value": "gt"}, {"label": "Guinea-Bissau", "value": "gw"}, {"label": "Guyana", "value": "gy"}, {"label": "Haiti", "value": "ht"}, {"label": "Honduras", "value": "hn"}, {"label": "Jamaica", "value": "jm"}, {"label": "Martinique", "value": "mq"}, {"label": "Mexico", "value": "mx"}, {"label": "Nicaragua", "value": "ni"}, {"label": "Panama", "value": "pa"}, {"label": "Paraguay", "value": "py"}, {"label": "Peru", "value": "pe"}, {"label": "Puerto Rico", "value": "pr"}, {"label": "Saint Kitts and Nevis", "value": "kn"}, {"label": "Saint Lucia", "value": "lc"}, {"label": "Saint Pierre and Miquelon", "value": "pm"}, {"label": "Saint Vincent and the Grenadines", "value": "vc"}, {"label": "Suriname", "value": "sr"}, {"label": "Timor-Leste", "value": "tl"}, {"label": "Togo", "value": "tg"}, {"label": "Trinidad and Tobago", "value": "tt"}, {"label": "Turks and Caicos Islands (the)", "value": "tc"}, {"label": "Uruguay", "value": "uy"}, {"label": "Venezuela", "value": "ve"}, {"label": "Virgin Islands (British)", "value": "vg"}, {"label": "Virgin Islands (U.S.)", "value": "vi"}]}, {"label": "North America", "value": "North_America", "children": [{"label": "Canada", "value": "ca"}, {"label": "United States (the)", "value": "us"}]}, {"label": "Oceania", "value": "Oceania", "children": [{"label": "American Samoa", "value": "as"}, {"label": "Australia", "value": "au"}, {"label": "Fiji", "value": "fj"}, {"label": "French Polynesia", "value": "pf"}, {"label": "Guam", "value": "gu"}, {"label": "Marshall Islands (the)", "value": "mh"}, {"label": "Micronesia (the Federated States of)", "value": "fm"}, {"label": "New Caledonia", "value": "nc"}, {"label": "New Zealand", "value": "nz"}, {"label": "Northern Mariana Islands", "value": "mp"}, {"label": "<PERSON><PERSON>", "value": "pw"}, {"label": "Papua New Guinea", "value": "pg"}, {"label": "Samoa", "value": "ws"}, {"label": "Solomon Islands (the)", "value": "sb"}, {"label": "Tonga", "value": "to"}, {"label": "Vanuatu", "value": "vu"}, {"label": "Wallis and Futuna", "value": "wf"}]}, {"label": "Others", "value": "Others", "children": [{"label": "British Indian Ocean Territory (the)", "value": "io"}, {"label": "Caribbean Netherlands", "value": "bq"}, {"label": "Côte d'Ivoire", "value": "ci"}, {"label": "Curacao", "value": "cw"}, {"label": "European Union", "value": "eu"}, {"label": "Falkland Islands", "value": "fk"}, {"label": "Holy See (the) [Vatican City State]", "value": "va"}, {"label": "Kiribati", "value": "ki"}, {"label": "Korea (the Democratic People's Republic of)", "value": "kp"}, {"label": "Nauru", "value": "nr"}, {"label": "Norfolk Island", "value": "nf"}, {"label": "<PERSON> (France)", "value": "mf"}, {"label": "Sint Maarten", "value": "sx"}, {"label": "South Sudan", "value": "ss"}, {"label": "Tokelau", "value": "tk"}, {"label": "Turkmenistan", "value": "tm"}, {"label": "Tuvalu", "value": "tv"}, {"label": "Others", "value": "Others"}]}], "dayPltv": [{"value": "7", "text": "D7"}, {"value": "30", "text": "D30"}, {"value": "90", "text": "D90"}, {"value": "180", "text": "D180"}, {"value": "360", "text": "D360"}], "worldCode": {"ad": "Andorra", "ae": "United Arab Emirates (the)", "af": "Afghanistan", "ag": "Antigua and Barbuda", "ai": "<PERSON><PERSON><PERSON>", "al": "Albania", "am": "Armenia", "ao": "Angola", "ar": "Argentina", "as": "American Samoa", "at": "Austria", "au": "Australia", "aw": "Aruba", "az": "Azerbaijan", "ba": "Bosnia and Herzegovina", "bb": "Barbados", "bd": "Bangladesh", "be": "Belgium", "bf": "Burkina Faso", "bg": "Bulgaria", "bh": "Bahrain", "bi": "Burundi", "bj": "Benin", "bm": "Bermuda", "bn": "Brunei Darussalam", "bo": "Bolivia", "bq": "Caribbean Netherlands", "br": "Brazil", "bs": "Bahamas (The)", "bt": "Bhutan", "bw": "Botswana", "by": "Belarus", "bz": "Belize", "ca": "Canada", "cd": "Democratic Republic of the Congo", "cf": "Central African Republic (the)", "cg": "Republic of the Congo", "ch": "Switzerland", "ci": "Côte d'Ivoire", "ck": "Cook Islands (the)", "cl": "Chile", "cm": "Cameroon", "cn": "China", "co": "Colombia", "cr": "Costa Rica", "cu": "Cuba", "cv": "Cape Verde", "cw": "Curacao", "cy": "Cyprus", "cz": "Czech Republic (the)", "de": "Germany", "dj": "Djibouti", "dk": "Denmark", "dm": "Dominica", "do": "Dominican Republic (the)", "dz": "Algeria", "ec": "Ecuador", "ee": "Estonia", "eg": "Egypt", "es": "Spain", "et": "Ethiopia", "eu": "European Union", "fi": "Finland", "fj": "Fiji", "fk": "Falkland Islands", "fm": "Micronesia (the Federated States of)", "fo": "Faroe Islands (the)", "fr": "France", "ga": "Gabon", "gb": "United Kingdom (the)", "uk": "United Kingdom (the)", "gd": "Grenada", "ge": "Georgia", "gf": "French Guiana", "gg": "Guernsey", "gh": "Ghana", "gi": "Gibraltar", "gl": "Greenland", "gm": "Gambia (The)", "gn": "Guinea", "gp": "Guadeloupe", "gq": "Equatorial Guinea", "gr": "Greece", "gt": "Guatemala", "gu": "Guam", "gw": "Guinea-Bissau", "gy": "Guyana", "hk": "Hong Kong", "hn": "Honduras", "hr": "Croatia", "ht": "Haiti", "hu": "Hungary", "id": "Indonesia", "ie": "Ireland", "il": "Israel", "im": "Isle of Man", "in": "India", "io": "British Indian Ocean Territory (the)", "iq": "Iraq", "ir": "Iran (the Islamic Republic of)", "is": "Iceland", "it": "Italy", "je": "Jersey", "jm": "Jamaica", "jo": "Jordan", "jp": "Japan", "ke": "Kenya", "kg": "Kyrgyzstan", "kh": "Cambodia", "ki": "Kiribati", "km": "Comoros", "kn": "Saint Kitts and Nevis", "kp": "Korea (the Democratic People's Republic of)", "kr": "Korea (the Republic of)", "kw": "Kuwait", "ky": "Cayman Islands (the)", "kz": "Kazakhstan", "la": "Lao People's Democratic Republic (the)", "lb": "Lebanon", "lc": "Saint Lucia", "li": "Liechtenstein", "lk": "Sri Lanka", "lr": "Liberia", "ls": "Lesotho", "lt": "Lithuania", "lu": "Luxembourg", "lv": "Latvia", "ly": "Libyan Arab <PERSON><PERSON> (the)", "ma": "Morocco", "mc": "Monaco", "md": "Moldova (the Republic of)", "me": "Montenegro", "mf": "<PERSON> (France)", "mg": "Madagascar", "mh": "Marshall Islands (the)", "mk": "Republic of Macedonia (FYROM)", "ml": "Mali", "mm": "Myanmar", "mn": "Mongolia", "mo": "Macao", "mp": "Northern Mariana Islands", "mq": "Martinique", "mr": "Mauritania", "mt": "Malta", "mu": "Mauritius", "mv": "Maldives", "mw": "Malawi", "mx": "Mexico", "my": "Malaysia", "mz": "Mozambique", "na": "Namibia", "nc": "New Caledonia", "ne": "Niger (the)", "nf": "Norfolk Island", "ng": "Nigeria", "ni": "Nicaragua", "nl": "Netherlands (the)", "no": "Norway", "np": "Nepal", "nr": "Nauru", "nz": "New Zealand", "om": "Oman", "Others": "Others", "pa": "Panama", "pe": "Peru", "pf": "French Polynesia", "pg": "Papua New Guinea", "ph": "Philippines (the)", "pk": "Pakistan", "pl": "Poland", "pm": "Saint Pierre and Miquelon", "pr": "Puerto Rico", "ps": "Palestinian Territory (the Occupied)", "pt": "Portugal", "pw": "<PERSON><PERSON>", "py": "Paraguay", "qa": "Qatar", "re": "Réunion", "ro": "Romania", "rs": "Serbia", "ru": "Russian Federation", "rw": "Rwanda", "sa": "Saudi Arabia", "sb": "Solomon Islands (the)", "sc": "Seychelles", "sd": "Sudan (the)", "se": "Sweden", "sg": "Singapore", "si": "Slovenia", "sk": "Slovakia", "sl": "Sierra Leone", "sm": "San Marino", "sn": "Senegal", "so": "Somalia", "sr": "Suriname", "ss": "South Sudan", "st": "Sao Tome and Principe", "sv": "El Salvador", "sx": "Sint Maarten", "sy": "Syrian Arab Republic (the)", "sz": "Swaziland", "tc": "Turks and Caicos Islands (the)", "td": "Chad", "tg": "Togo", "th": "Thailand", "tj": "Tajikistan", "tk": "Tokelau", "tl": "Timor-Leste", "tm": "Turkmenistan", "tn": "Tunisia", "to": "Tonga", "tr": "Turkey", "tt": "Trinidad and Tobago", "tv": "Tuvalu", "tw": "Taiwan (Province of China)", "tz": "Tanzania,United Republic of", "ua": "Ukraine", "ug": "Uganda", "us": "United States (the)", "uy": "Uruguay", "uz": "Uzbekistan", "va": "Holy See (the) [Vatican City State]", "vc": "Saint Vincent and the Grenadines", "ve": "Venezuela", "vg": "Virgin Islands (British)", "vi": "Virgin Islands (U.S.)", "vn": "Viet Nam", "vu": "Vanuatu", "wf": "Wallis and Futuna", "ws": "Samoa", "ye": "Yemen", "yt": "Mayotte", "za": "South Africa", "zm": "Zambia", "zw": "Zimbabwe", "none": "None"}, "tableHead": [{"label": "P-Downloads", "value": "P-Downloads", "children": [{"label": "P-D7 Downloads", "value": "D7D"}, {"label": "P-D30 Downloads", "value": "D30D"}, {"label": "P-D90 Downloads", "value": "D90D"}, {"label": "P-D180 Downloads", "value": "D180D"}, {"label": "P-D360 Downloads", "value": "D360D"}]}, {"label": "P-LTV", "value": "P-LTV", "children": [{"label": "P-D7 LTV", "value": "D7L"}, {"label": "P-D30 LTV", "value": "D30L"}, {"label": "P-D90 LTV", "value": "D90L"}, {"label": "P-D180 LTV", "value": "D180L"}, {"label": "P-D360 LTV", "value": "D360L"}]}], "tableHname": {"Competitor": "Competitor", "Market": "Market", "D7L": "P-D7 LTV", "D30L": "P-D30 LTV", "D90L": "P-D90 LTV", "D180L": "P-D180 LTV", "D360L": "P-D360 LTV", "D7D": "P-D7 Downloads", "D30D": "P-D30 Downloads", "D90D": "P-D90 Downloads", "D180D": "P-D180 Downloads", "D360D": "P-D360 Downloads"}, "tableHnameD": {"D1D": "P-D1 Downloads", "D2D": "P-D2 Downloads", "D3D": "P-D3 Downloads", "D4D": "P-D4 Downloads", "D5D": "P-D5 Downloads", "D6D": "P-D6 Downloads", "D7D": "P-D7 Downloads", "D8D": "P-D8 Downloads", "D9D": "P-D9 Downloads", "D10D": "P-D10 Downloads", "D11D": "P-D11 Downloads", "D12D": "P-D12 Downloads", "D13D": "P-D13 Downloads", "D14D": "P-D14 Downloads", "D15D": "P-D15 Downloads", "D16D": "P-D16 Downloads", "D17D": "P-D17 Downloads", "D18D": "P-D18 Downloads", "D19D": "P-D19 Downloads", "D20D": "P-D20 Downloads", "D21D": "P-D21 Downloads", "D22D": "P-D22 Downloads", "D23D": "P-D23 Downloads", "D24D": "P-D24 Downloads", "D25D": "P-D25 Downloads", "D26D": "P-D26 Downloads", "D27D": "P-D27 Downloads", "D28D": "P-D28 Downloads", "D29D": "P-D29 Downloads", "D30D": "P-D30 Downloads", "D31D": "P-D31 Downloads", "D32D": "P-D32 Downloads", "D33D": "P-D33 Downloads", "D34D": "P-D34 Downloads", "D35D": "P-D35 Downloads", "D36D": "P-D36 Downloads", "D37D": "P-D37 Downloads", "D38D": "P-D38 Downloads", "D39D": "P-D39 Downloads", "D40D": "P-D40 Downloads", "D41D": "P-D41 Downloads", "D42D": "P-D42 Downloads", "D43D": "P-D43 Downloads", "D44D": "P-D44 Downloads", "D45D": "P-D45 Downloads", "D46D": "P-D46 Downloads", "D47D": "P-D47 Downloads", "D48D": "P-D48 Downloads", "D49D": "P-D49 Downloads", "D50D": "P-D50 Downloads", "D51D": "P-D51 Downloads", "D52D": "P-D52 Downloads", "D53D": "P-D53 Downloads", "D54D": "P-D54 Downloads", "D55D": "P-D55 Downloads", "D56D": "P-D56 Downloads", "D57D": "P-D57 Downloads", "D58D": "P-D58 Downloads", "D59D": "P-D59 Downloads", "D60D": "P-D60 Downloads", "D61D": "P-D61 Downloads", "D62D": "P-D62 Downloads", "D63D": "P-D63 Downloads", "D64D": "P-D64 Downloads", "D65D": "P-D65 Downloads", "D66D": "P-D66 Downloads", "D67D": "P-D67 Downloads", "D68D": "P-D68 Downloads", "D69D": "P-D69 Downloads", "D70D": "P-D70 Downloads", "D71D": "P-D71 Downloads", "D72D": "P-D72 Downloads", "D73D": "P-D73 Downloads", "D74D": "P-D74 Downloads", "D75D": "P-D75 Downloads", "D76D": "P-D76 Downloads", "D77D": "P-D77 Downloads", "D78D": "P-D78 Downloads", "D79D": "P-D79 Downloads", "D80D": "P-D80 Downloads", "D81D": "P-D81 Downloads", "D82D": "P-D82 Downloads", "D83D": "P-D83 Downloads", "D84D": "P-D84 Downloads", "D85D": "P-D85 Downloads", "D86D": "P-D86 Downloads", "D87D": "P-D87 Downloads", "D88D": "P-D88 Downloads", "D89D": "P-D89 Downloads", "D90D": "P-D90 Downloads", "D91D": "P-D91 Downloads", "D92D": "P-D92 Downloads", "D93D": "P-D93 Downloads", "D94D": "P-D94 Downloads", "D95D": "P-D95 Downloads", "D96D": "P-D96 Downloads", "D97D": "P-D97 Downloads", "D98D": "P-D98 Downloads", "D99D": "P-D99 Downloads", "D100D": "P-D100 Downloads", "D101D": "P-D101 Downloads", "D102D": "P-D102 Downloads", "D103D": "P-D103 Downloads", "D104D": "P-D104 Downloads", "D105D": "P-D105 Downloads", "D106D": "P-D106 Downloads", "D107D": "P-D107 Downloads", "D108D": "P-D108 Downloads", "D109D": "P-D109 Downloads", "D110D": "P-D110 Downloads", "D111D": "P-D111 Downloads", "D112D": "P-D112 Downloads", "D113D": "P-D113 Downloads", "D114D": "P-D114 Downloads", "D115D": "P-D115 Downloads", "D116D": "P-D116 Downloads", "D117D": "P-D117 Downloads", "D118D": "P-D118 Downloads", "D119D": "P-D119 Downloads", "D120D": "P-D120 Downloads", "D121D": "P-D121 Downloads", "D122D": "P-D122 Downloads", "D123D": "P-D123 Downloads", "D124D": "P-D124 Downloads", "D125D": "P-D125 Downloads", "D126D": "P-D126 Downloads", "D127D": "P-D127 Downloads", "D128D": "P-D128 Downloads", "D129D": "P-D129 Downloads", "D130D": "P-D130 Downloads", "D131D": "P-D131 Downloads", "D132D": "P-D132 Downloads", "D133D": "P-D133 Downloads", "D134D": "P-D134 Downloads", "D135D": "P-D135 Downloads", "D136D": "P-D136 Downloads", "D137D": "P-D137 Downloads", "D138D": "P-D138 Downloads", "D139D": "P-D139 Downloads", "D140D": "P-D140 Downloads", "D141D": "P-D141 Downloads", "D142D": "P-D142 Downloads", "D143D": "P-D143 Downloads", "D144D": "P-D144 Downloads", "D145D": "P-D145 Downloads", "D146D": "P-D146 Downloads", "D147D": "P-D147 Downloads", "D148D": "P-D148 Downloads", "D149D": "P-D149 Downloads", "D150D": "P-D150 Downloads", "D151D": "P-D151 Downloads", "D152D": "P-D152 Downloads", "D153D": "P-D153 Downloads", "D154D": "P-D154 Downloads", "D155D": "P-D155 Downloads", "D156D": "P-D156 Downloads", "D157D": "P-D157 Downloads", "D158D": "P-D158 Downloads", "D159D": "P-D159 Downloads", "D160D": "P-D160 Downloads", "D161D": "P-D161 Downloads", "D162D": "P-D162 Downloads", "D163D": "P-D163 Downloads", "D164D": "P-D164 Downloads", "D165D": "P-D165 Downloads", "D166D": "P-D166 Downloads", "D167D": "P-D167 Downloads", "D168D": "P-D168 Downloads", "D169D": "P-D169 Downloads", "D170D": "P-D170 Downloads", "D171D": "P-D171 Downloads", "D172D": "P-D172 Downloads", "D173D": "P-D173 Downloads", "D174D": "P-D174 Downloads", "D175D": "P-D175 Downloads", "D176D": "P-D176 Downloads", "D177D": "P-D177 Downloads", "D178D": "P-D178 Downloads", "D179D": "P-D179 Downloads", "D180D": "P-D180 Downloads", "D181D": "P-D181 Downloads", "D182D": "P-D182 Downloads", "D183D": "P-D183 Downloads", "D184D": "P-D184 Downloads", "D185D": "P-D185 Downloads", "D186D": "P-D186 Downloads", "D187D": "P-D187 Downloads", "D188D": "P-D188 Downloads", "D189D": "P-D189 Downloads", "D190D": "P-D190 Downloads", "D191D": "P-D191 Downloads", "D192D": "P-D192 Downloads", "D193D": "P-D193 Downloads", "D194D": "P-D194 Downloads", "D195D": "P-D195 Downloads", "D196D": "P-D196 Downloads", "D197D": "P-D197 Downloads", "D198D": "P-D198 Downloads", "D199D": "P-D199 Downloads", "D200D": "P-D200 Downloads", "D201D": "P-D201 Downloads", "D202D": "P-D202 Downloads", "D203D": "P-D203 Downloads", "D204D": "P-D204 Downloads", "D205D": "P-D205 Downloads", "D206D": "P-D206 Downloads", "D207D": "P-D207 Downloads", "D208D": "P-D208 Downloads", "D209D": "P-D209 Downloads", "D210D": "P-D210 Downloads", "D211D": "P-D211 Downloads", "D212D": "P-D212 Downloads", "D213D": "P-D213 Downloads", "D214D": "P-D214 Downloads", "D215D": "P-D215 Downloads", "D216D": "P-D216 Downloads", "D217D": "P-D217 Downloads", "D218D": "P-D218 Downloads", "D219D": "P-D219 Downloads", "D220D": "P-D220 Downloads", "D221D": "P-D221 Downloads", "D222D": "P-D222 Downloads", "D223D": "P-D223 Downloads", "D224D": "P-D224 Downloads", "D225D": "P-D225 Downloads", "D226D": "P-D226 Downloads", "D227D": "P-D227 Downloads", "D228D": "P-D228 Downloads", "D229D": "P-D229 Downloads", "D230D": "P-D230 Downloads", "D231D": "P-D231 Downloads", "D232D": "P-D232 Downloads", "D233D": "P-D233 Downloads", "D234D": "P-D234 Downloads", "D235D": "P-D235 Downloads", "D236D": "P-D236 Downloads", "D237D": "P-D237 Downloads", "D238D": "P-D238 Downloads", "D239D": "P-D239 Downloads", "D240D": "P-D240 Downloads", "D241D": "P-D241 Downloads", "D242D": "P-D242 Downloads", "D243D": "P-D243 Downloads", "D244D": "P-D244 Downloads", "D245D": "P-D245 Downloads", "D246D": "P-D246 Downloads", "D247D": "P-D247 Downloads", "D248D": "P-D248 Downloads", "D249D": "P-D249 Downloads", "D250D": "P-D250 Downloads", "D251D": "P-D251 Downloads", "D252D": "P-D252 Downloads", "D253D": "P-D253 Downloads", "D254D": "P-D254 Downloads", "D255D": "P-D255 Downloads", "D256D": "P-D256 Downloads", "D257D": "P-D257 Downloads", "D258D": "P-D258 Downloads", "D259D": "P-D259 Downloads", "D260D": "P-D260 Downloads", "D261D": "P-D261 Downloads", "D262D": "P-D262 Downloads", "D263D": "P-D263 Downloads", "D264D": "P-D264 Downloads", "D265D": "P-D265 Downloads", "D266D": "P-D266 Downloads", "D267D": "P-D267 Downloads", "D268D": "P-D268 Downloads", "D269D": "P-D269 Downloads", "D270D": "P-D270 Downloads", "D271D": "P-D271 Downloads", "D272D": "P-D272 Downloads", "D273D": "P-D273 Downloads", "D274D": "P-D274 Downloads", "D275D": "P-D275 Downloads", "D276D": "P-D276 Downloads", "D277D": "P-D277 Downloads", "D278D": "P-D278 Downloads", "D279D": "P-D279 Downloads", "D280D": "P-D280 Downloads", "D281D": "P-D281 Downloads", "D282D": "P-D282 Downloads", "D283D": "P-D283 Downloads", "D284D": "P-D284 Downloads", "D285D": "P-D285 Downloads", "D286D": "P-D286 Downloads", "D287D": "P-D287 Downloads", "D288D": "P-D288 Downloads", "D289D": "P-D289 Downloads", "D290D": "P-D290 Downloads", "D291D": "P-D291 Downloads", "D292D": "P-D292 Downloads", "D293D": "P-D293 Downloads", "D294D": "P-D294 Downloads", "D295D": "P-D295 Downloads", "D296D": "P-D296 Downloads", "D297D": "P-D297 Downloads", "D298D": "P-D298 Downloads", "D299D": "P-D299 Downloads", "D300D": "P-D300 Downloads", "D301D": "P-D301 Downloads", "D302D": "P-D302 Downloads", "D303D": "P-D303 Downloads", "D304D": "P-D304 Downloads", "D305D": "P-D305 Downloads", "D306D": "P-D306 Downloads", "D307D": "P-D307 Downloads", "D308D": "P-D308 Downloads", "D309D": "P-D309 Downloads", "D310D": "P-D310 Downloads", "D311D": "P-D311 Downloads", "D312D": "P-D312 Downloads", "D313D": "P-D313 Downloads", "D314D": "P-D314 Downloads", "D315D": "P-D315 Downloads", "D316D": "P-D316 Downloads", "D317D": "P-D317 Downloads", "D318D": "P-D318 Downloads", "D319D": "P-D319 Downloads", "D320D": "P-D320 Downloads", "D321D": "P-D321 Downloads", "D322D": "P-D322 Downloads", "D323D": "P-D323 Downloads", "D324D": "P-D324 Downloads", "D325D": "P-D325 Downloads", "D326D": "P-D326 Downloads", "D327D": "P-D327 Downloads", "D328D": "P-D328 Downloads", "D329D": "P-D329 Downloads", "D330D": "P-D330 Downloads", "D331D": "P-D331 Downloads", "D332D": "P-D332 Downloads", "D333D": "P-D333 Downloads", "D334D": "P-D334 Downloads", "D335D": "P-D335 Downloads", "D336D": "P-D336 Downloads", "D337D": "P-D337 Downloads", "D338D": "P-D338 Downloads", "D339D": "P-D339 Downloads", "D340D": "P-D340 Downloads", "D341D": "P-D341 Downloads", "D342D": "P-D342 Downloads", "D343D": "P-D343 Downloads", "D344D": "P-D344 Downloads", "D345D": "P-D345 Downloads", "D346D": "P-D346 Downloads", "D347D": "P-D347 Downloads", "D348D": "P-D348 Downloads", "D349D": "P-D349 Downloads", "D350D": "P-D350 Downloads", "D351D": "P-D351 Downloads", "D352D": "P-D352 Downloads", "D353D": "P-D353 Downloads", "D354D": "P-D354 Downloads", "D355D": "P-D355 Downloads", "D356D": "P-D356 Downloads", "D357D": "P-D357 Downloads", "D358D": "P-D358 Downloads", "D359D": "P-D359 Downloads", "D360D": "P-D360 Downloads"}, "tableHnameL": {"D1L": "P-D1 LTV", "D2L": "P-D2 LTV", "D3L": "P-D3 LTV", "D4L": "P-D4 LTV", "D5L": "P-D5 LTV", "D6L": "P-D6 LTV", "D7L": "P-D7 LTV", "D8L": "P-D8 LTV", "D9L": "P-D9 LTV", "D10L": "P-D10 LTV", "D11L": "P-D11 LTV", "D12L": "P-D12 LTV", "D13L": "P-D13 LTV", "D14L": "P-D14 LTV", "D15L": "P-D15 LTV", "D16L": "P-D16 LTV", "D17L": "P-D17 LTV", "D18L": "P-D18 LTV", "D19L": "P-D19 LTV", "D20L": "P-D20 LTV", "D21L": "P-D21 LTV", "D22L": "P-D22 LTV", "D23L": "P-D23 LTV", "D24L": "P-D24 LTV", "D25L": "P-D25 LTV", "D26L": "P-D26 LTV", "D27L": "P-D27 LTV", "D28L": "P-D28 LTV", "D29L": "P-D29 LTV", "D30L": "P-D30 LTV", "D31L": "P-D31 LTV", "D32L": "P-D32 LTV", "D33L": "P-D33 LTV", "D34L": "P-D34 LTV", "D35L": "P-D35 LTV", "D36L": "P-D36 LTV", "D37L": "P-D37 LTV", "D38L": "P-D38 LTV", "D39L": "P-D39 LTV", "D40L": "P-D40 LTV", "D41L": "P-D41 LTV", "D42L": "P-D42 LTV", "D43L": "P-D43 LTV", "D44L": "P-D44 LTV", "D45L": "P-D45 LTV", "D46L": "P-D46 LTV", "D47L": "P-D47 LTV", "D48L": "P-D48 LTV", "D49L": "P-D49 LTV", "D50L": "P-D50 LTV", "D51L": "P-D51 LTV", "D52L": "P-D52 LTV", "D53L": "P-D53 LTV", "D54L": "P-D54 LTV", "D55L": "P-D55 LTV", "D56L": "P-D56 LTV", "D57L": "P-D57 LTV", "D58L": "P-D58 LTV", "D59L": "P-D59 LTV", "D60L": "P-D60 LTV", "D61L": "P-D61 LTV", "D62L": "P-D62 LTV", "D63L": "P-D63 LTV", "D64L": "P-D64 LTV", "D65L": "P-D65 LTV", "D66L": "P-D66 LTV", "D67L": "P-D67 LTV", "D68L": "P-D68 LTV", "D69L": "P-D69 LTV", "D70L": "P-D70 LTV", "D71L": "P-D71 LTV", "D72L": "P-D72 LTV", "D73L": "P-D73 LTV", "D74L": "P-D74 LTV", "D75L": "P-D75 LTV", "D76L": "P-D76 LTV", "D77L": "P-D77 LTV", "D78L": "P-D78 LTV", "D79L": "P-D79 LTV", "D80L": "P-D80 LTV", "D81L": "P-D81 LTV", "D82L": "P-D82 LTV", "D83L": "P-D83 LTV", "D84L": "P-D84 LTV", "D85L": "P-D85 LTV", "D86L": "P-D86 LTV", "D87L": "P-D87 LTV", "D88L": "P-D88 LTV", "D89L": "P-D89 LTV", "D90L": "P-D90 LTV", "D91L": "P-D91 LTV", "D92L": "P-D92 LTV", "D93L": "P-D93 LTV", "D94L": "P-D94 LTV", "D95L": "P-D95 LTV", "D96L": "P-D96 LTV", "D97L": "P-D97 LTV", "D98L": "P-D98 LTV", "D99L": "P-D99 LTV", "D100L": "P-D100 LTV", "D101L": "P-D101 LTV", "D102L": "P-D102 LTV", "D103L": "P-D103 LTV", "D104L": "P-D104 LTV", "D105L": "P-D105 LTV", "D106L": "P-D106 LTV", "D107L": "P-D107 LTV", "D108L": "P-D108 LTV", "D109L": "P-D109 LTV", "D110L": "P-D110 LTV", "D111L": "P-D111 LTV", "D112L": "P-D112 LTV", "D113L": "P-D113 LTV", "D114L": "P-D114 LTV", "D115L": "P-D115 LTV", "D116L": "P-D116 LTV", "D117L": "P-D117 LTV", "D118L": "P-D118 LTV", "D119L": "P-D119 LTV", "D120L": "P-D120 LTV", "D121L": "P-D121 LTV", "D122L": "P-D122 LTV", "D123L": "P-D123 LTV", "D124L": "P-D124 LTV", "D125L": "P-D125 LTV", "D126L": "P-D126 LTV", "D127L": "P-D127 LTV", "D128L": "P-D128 LTV", "D129L": "P-D129 LTV", "D130L": "P-D130 LTV", "D131L": "P-D131 LTV", "D132L": "P-D132 LTV", "D133L": "P-D133 LTV", "D134L": "P-D134 LTV", "D135L": "P-D135 LTV", "D136L": "P-D136 LTV", "D137L": "P-D137 LTV", "D138L": "P-D138 LTV", "D139L": "P-D139 LTV", "D140L": "P-D140 LTV", "D141L": "P-D141 LTV", "D142L": "P-D142 LTV", "D143L": "P-D143 LTV", "D144L": "P-D144 LTV", "D145L": "P-D145 LTV", "D146L": "P-D146 LTV", "D147L": "P-D147 LTV", "D148L": "P-D148 LTV", "D149L": "P-D149 LTV", "D150L": "P-D150 LTV", "D151L": "P-D151 LTV", "D152L": "P-D152 LTV", "D153L": "P-D153 LTV", "D154L": "P-D154 LTV", "D155L": "P-D155 LTV", "D156L": "P-D156 LTV", "D157L": "P-D157 LTV", "D158L": "P-D158 LTV", "D159L": "P-D159 LTV", "D160L": "P-D160 LTV", "D161L": "P-D161 LTV", "D162L": "P-D162 LTV", "D163L": "P-D163 LTV", "D164L": "P-D164 LTV", "D165L": "P-D165 LTV", "D166L": "P-D166 LTV", "D167L": "P-D167 LTV", "D168L": "P-D168 LTV", "D169L": "P-D169 LTV", "D170L": "P-D170 LTV", "D171L": "P-D171 LTV", "D172L": "P-D172 LTV", "D173L": "P-D173 LTV", "D174L": "P-D174 LTV", "D175L": "P-D175 LTV", "D176L": "P-D176 LTV", "D177L": "P-D177 LTV", "D178L": "P-D178 LTV", "D179L": "P-D179 LTV", "D180L": "P-D180 LTV", "D181L": "P-D181 LTV", "D182L": "P-D182 LTV", "D183L": "P-D183 LTV", "D184L": "P-D184 LTV", "D185L": "P-D185 LTV", "D186L": "P-D186 LTV", "D187L": "P-D187 LTV", "D188L": "P-D188 LTV", "D189L": "P-D189 LTV", "D190L": "P-D190 LTV", "D191L": "P-D191 LTV", "D192L": "P-D192 LTV", "D193L": "P-D193 LTV", "D194L": "P-D194 LTV", "D195L": "P-D195 LTV", "D196L": "P-D196 LTV", "D197L": "P-D197 LTV", "D198L": "P-D198 LTV", "D199L": "P-D199 LTV", "D200L": "P-D200 LTV", "D201L": "P-D201 LTV", "D202L": "P-D202 LTV", "D203L": "P-D203 LTV", "D204L": "P-D204 LTV", "D205L": "P-D205 LTV", "D206L": "P-D206 LTV", "D207L": "P-D207 LTV", "D208L": "P-D208 LTV", "D209L": "P-D209 LTV", "D210L": "P-D210 LTV", "D211L": "P-D211 LTV", "D212L": "P-D212 LTV", "D213L": "P-D213 LTV", "D214L": "P-D214 LTV", "D215L": "P-D215 LTV", "D216L": "P-D216 LTV", "D217L": "P-D217 LTV", "D218L": "P-D218 LTV", "D219L": "P-D219 LTV", "D220L": "P-D220 LTV", "D221L": "P-D221 LTV", "D222L": "P-D222 LTV", "D223L": "P-D223 LTV", "D224L": "P-D224 LTV", "D225L": "P-D225 LTV", "D226L": "P-D226 LTV", "D227L": "P-D227 LTV", "D228L": "P-D228 LTV", "D229L": "P-D229 LTV", "D230L": "P-D230 LTV", "D231L": "P-D231 LTV", "D232L": "P-D232 LTV", "D233L": "P-D233 LTV", "D234L": "P-D234 LTV", "D235L": "P-D235 LTV", "D236L": "P-D236 LTV", "D237L": "P-D237 LTV", "D238L": "P-D238 LTV", "D239L": "P-D239 LTV", "D240L": "P-D240 LTV", "D241L": "P-D241 LTV", "D242L": "P-D242 LTV", "D243L": "P-D243 LTV", "D244L": "P-D244 LTV", "D245L": "P-D245 LTV", "D246L": "P-D246 LTV", "D247L": "P-D247 LTV", "D248L": "P-D248 LTV", "D249L": "P-D249 LTV", "D250L": "P-D250 LTV", "D251L": "P-D251 LTV", "D252L": "P-D252 LTV", "D253L": "P-D253 LTV", "D254L": "P-D254 LTV", "D255L": "P-D255 LTV", "D256L": "P-D256 LTV", "D257L": "P-D257 LTV", "D258L": "P-D258 LTV", "D259L": "P-D259 LTV", "D260L": "P-D260 LTV", "D261L": "P-D261 LTV", "D262L": "P-D262 LTV", "D263L": "P-D263 LTV", "D264L": "P-D264 LTV", "D265L": "P-D265 LTV", "D266L": "P-D266 LTV", "D267L": "P-D267 LTV", "D268L": "P-D268 LTV", "D269L": "P-D269 LTV", "D270L": "P-D270 LTV", "D271L": "P-D271 LTV", "D272L": "P-D272 LTV", "D273L": "P-D273 LTV", "D274L": "P-D274 LTV", "D275L": "P-D275 LTV", "D276L": "P-D276 LTV", "D277L": "P-D277 LTV", "D278L": "P-D278 LTV", "D279L": "P-D279 LTV", "D280L": "P-D280 LTV", "D281L": "P-D281 LTV", "D282L": "P-D282 LTV", "D283L": "P-D283 LTV", "D284L": "P-D284 LTV", "D285L": "P-D285 LTV", "D286L": "P-D286 LTV", "D287L": "P-D287 LTV", "D288L": "P-D288 LTV", "D289L": "P-D289 LTV", "D290L": "P-D290 LTV", "D291L": "P-D291 LTV", "D292L": "P-D292 LTV", "D293L": "P-D293 LTV", "D294L": "P-D294 LTV", "D295L": "P-D295 LTV", "D296L": "P-D296 LTV", "D297L": "P-D297 LTV", "D298L": "P-D298 LTV", "D299L": "P-D299 LTV", "D300L": "P-D300 LTV", "D301L": "P-D301 LTV", "D302L": "P-D302 LTV", "D303L": "P-D303 LTV", "D304L": "P-D304 LTV", "D305L": "P-D305 LTV", "D306L": "P-D306 LTV", "D307L": "P-D307 LTV", "D308L": "P-D308 LTV", "D309L": "P-D309 LTV", "D310L": "P-D310 LTV", "D311L": "P-D311 LTV", "D312L": "P-D312 LTV", "D313L": "P-D313 LTV", "D314L": "P-D314 LTV", "D315L": "P-D315 LTV", "D316L": "P-D316 LTV", "D317L": "P-D317 LTV", "D318L": "P-D318 LTV", "D319L": "P-D319 LTV", "D320L": "P-D320 LTV", "D321L": "P-D321 LTV", "D322L": "P-D322 LTV", "D323L": "P-D323 LTV", "D324L": "P-D324 LTV", "D325L": "P-D325 LTV", "D326L": "P-D326 LTV", "D327L": "P-D327 LTV", "D328L": "P-D328 LTV", "D329L": "P-D329 LTV", "D330L": "P-D330 LTV", "D331L": "P-D331 LTV", "D332L": "P-D332 LTV", "D333L": "P-D333 LTV", "D334L": "P-D334 LTV", "D335L": "P-D335 LTV", "D336L": "P-D336 LTV", "D337L": "P-D337 LTV", "D338L": "P-D338 LTV", "D339L": "P-D339 LTV", "D340L": "P-D340 LTV", "D341L": "P-D341 LTV", "D342L": "P-D342 LTV", "D343L": "P-D343 LTV", "D344L": "P-D344 LTV", "D345L": "P-D345 LTV", "D346L": "P-D346 LTV", "D347L": "P-D347 LTV", "D348L": "P-D348 LTV", "D349L": "P-D349 LTV", "D350L": "P-D350 LTV", "D351L": "P-D351 LTV", "D352L": "P-D352 LTV", "D353L": "P-D353 LTV", "D354L": "P-D354 LTV", "D355L": "P-D355 LTV", "D356L": "P-D356 LTV", "D357L": "P-D357 LTV", "D358L": "P-D358 LTV", "D359L": "P-D359 LTV", "D360L": "P-D360 LTV"}, "CreativeTableHead": [{"value": "Impression", "text": "Impression"}, {"value": "Days Active", "text": "Days Active"}, {"value": "Popular", "text": "Popular"}, {"value": "Country", "text": "Country"}, {"value": "Channel", "text": "Channel"}, {"value": "Interaction", "text": "Interaction"}, {"value": "Conversion", "text": "Conversion"}, {"value": "Tag", "text": "Tag"}, {"value": "First_seen", "text": "First_seen"}, {"value": "Last_seen", "text": "Last_seen"}], "CreativeTableorder": [{"value": "impression_number", "label": "Impression"}, {"value": "days", "label": "Days Active"}, {"value": "heat", "label": "Popular"}, {"value": "countries_number", "label": "Country"}, {"value": "channel", "label": "Channel"}], "creative_type": [{"value": "1", "text": "Image"}, {"value": "2", "text": "Video"}], "channel": [{"value": "2", "label": "FB News Feed"}, {"value": "39", "label": "Instagram"}, {"value": "3", "label": "Audience Network"}, {"value": "51", "label": "<PERSON>"}, {"value": "1", "label": "Google Ads(Admob)"}, {"value": "6", "label": "YouTube"}, {"value": "10", "label": "Twitter"}, {"value": "5", "label": "UnityAds"}, {"value": "19", "label": "<PERSON><PERSON><PERSON>"}, {"value": "20", "label": "AppLovin"}, {"value": "24", "label": "AdColony"}, {"value": "25", "label": "Chartboost"}, {"value": "40", "label": "Pinterest"}, {"value": "42", "label": "ironSource"}, {"value": "23", "label": "Yahoo!"}, {"value": "44", "label": "reddit"}, {"value": "43", "label": "TikTok"}, {"value": "45", "label": "TopBuzz"}, {"value": "46", "label": "Mintegral (Mobvista)"}, {"value": "52", "label": "Snapchat"}, {"value": "49", "label": "<PERSON><PERSON><PERSON>"}, {"value": "47", "label": "<PERSON><PERSON><PERSON>"}, {"value": "27", "label": "NAVER(네이버)"}, {"value": "26", "label": "<PERSON><PERSON>(다음)"}, {"value": "34", "label": "<PERSON>(네이트)"}, {"value": "28", "label": "<PERSON><PERSON><PERSON>(アメーバ)"}, {"value": "36", "label": "Yahoo! Japan"}, {"value": "310", "label": "<PERSON><PERSON>(TikTok Audience Network)"}, {"value": "301", "label": "<PERSON><PERSON>(グノシー)"}, {"value": "303", "label": "Zucks"}, {"value": "300", "label": "SmartNews(スマートニュース)"}, {"value": "304", "label": "i-mobile"}, {"value": "305", "label": "AkaNe"}, {"value": "307", "label": "Nend"}, {"value": "308", "label": "AMoAd"}], "channelCode": {"2": "FB News Feed", "39": "Instagram", "3": "Audience Network", "51": "<PERSON>", "1": "Google Ads(Admob)", "6": "YouTube", "10": "Twitter", "5": "UnityAds", "19": "<PERSON><PERSON><PERSON>", "20": "AppLovin", "24": "AdColony", "25": "Chartboost", "40": "Pinterest", "42": "ironSource", "23": "Yahoo!", "44": "reddit", "43": "TikTok", "45": "TopBuzz", "46": "Mintegral (Mobvista)", "310": "<PERSON><PERSON>(TikTok Audience Network)", "52": "Snapchat", "49": "<PERSON><PERSON><PERSON>", "47": "<PERSON><PERSON><PERSON>", "27": "NAVER(네이버)", "26": "<PERSON><PERSON>(다음)", "34": "<PERSON>(네이트)", "28": "<PERSON><PERSON><PERSON>(アメーバ)", "36": "Yahoo! Japan", "301": "<PERSON><PERSON>(グノシー)", "303": "Zucks", "300": "SmartNews(スマートニュース)", "304": "i-mobile", "305": "AkaNe", "307": "Nend", "308": "AMoAd"}, "language": [{"value": "zh-CN", "label": "zh-CN"}, {"value": "en", "label": "English"}, {"value": "ja", "label": "Japanese"}, {"value": "ko", "label": "Korean"}, {"value": "ar", "label": "Arabic"}, {"value": "zh-TW", "label": "zh-TW"}, {"value": "th", "label": "Thai"}, {"value": "de", "label": "German"}, {"value": "es", "label": "Spanish"}, {"value": "fr", "label": "French"}, {"value": "hi", "label": "<PERSON><PERSON>"}, {"value": "it", "label": "Italian"}, {"value": "ms", "label": "Malay"}, {"value": "nl", "label": "Dutch"}, {"value": "pt", "label": "Portuguese"}, {"value": "ru", "label": "Russian"}, {"value": "vi", "label": "Vietnamese"}, {"value": "id", "label": "Indonesian"}, {"value": "he", "label": "Hebrew"}, {"value": "tl", "label": "Tagalog language"}, {"value": "no", "label": "Norwegian"}, {"value": "tr", "label": "Turkish"}, {"value": "pl", "label": "Polish"}], "languageCode": {"az": "Azerbaijani", "ar": "Arabic", "my": "Burmese", "be": "Belarusian", "bn": "Bengali", "bs": "Bosnian", "bg": "Bulgarian", "zh-CN": "Chinese(Simplified)", "hr": "Croatian", "ca": "Catalan", "cs": "Czech", "zh-TW": "Chinese(Traditional)", "nl": "Dutch", "da": "Danish", "et": "Estonian", "en": "English", "fr": "French", "tl": "Filipino", "fi": "Finnish", "de": "German", "el": "Greek", "gu": "Gujarati", "ka": "Georgian", "gl": "Galician", "hi": "Hindi", "he": "Hebrew", "hmn": "Hmong", "hu": "Hungarian", "it": "Italian", "ig": "Igbo", "id": "Indonesian", "is": "Icelandic", "ga": "Irish", "ja": "Japanese", "jw": "Javanese", "ko": "Korean", "km": "Khmer", "kk": "Kazakh", "kn": "Kannada", "lv": "Latvian", "lt": "Lithuanian", "la": "Latin", "ms": "Malay", "mg": "Malagasy", "mn": "Mongolian", "mt": "Maltese", "ml": "Malayalam", "mk": "Macedonian", "ne": "Nepali", "no": "Norwegian", "pt": "Portuguese", "fa": "Persian", "pl": "Polish", "pa": "Punjabi", "ru": "Russian", "ro": "Romanian", "so": "Somali", "es": "Spanish", "st": "<PERSON><PERSON><PERSON><PERSON>", "su": "Sundanese", "si": "Sinhala", "sv": "Swedish", "sl": "Slovenian", "sk": "Slovak", "sr": "Serbian", "sd": "Sindhi", "tg": "Tajik", "tr": "Turkish", "ta": "Tamil", "te": "Telugu", "th": "Thai", "uz": "Uzbek", "ur": "Urdu", "uk": "Ukrainian", "vi": "Vietnamese", "zu": "Zulu"}, "theme": [{"value": "3001", "text": "Legend"}, {"value": "3005", "text": "Three Kingdoms"}, {"value": "3012", "text": "Xianxia"}, {"value": "3063", "text": "Crime"}, {"value": "3023", "text": "War"}, {"value": "3009", "text": "Magic"}, {"value": "3024", "text": "Tower Defence"}, {"value": "3010", "text": "Chinese Fantasy"}, {"value": "3051", "text": "Idle"}, {"value": "3022", "text": "Manage"}, {"value": "3021", "text": "Cultivate"}, {"value": "3035", "text": "Fighting"}, {"value": "3037", "text": "Orientation - Landscape"}, {"value": "3025", "text": "Strategy"}, {"value": "3036", "text": "Adventure"}, {"value": "3017", "text": "Kpop"}, {"value": "3054", "text": "Puzzle"}, {"value": "3018", "text": "Card"}, {"value": "3011", "text": "Wuxia"}, {"value": "3052", "text": "Match"}, {"value": "3013", "text": "Cartoon"}, {"value": "3038", "text": "Jump"}, {"value": "3014", "text": "Turn-based"}, {"value": "3028", "text": "Shooting"}, {"value": "3027", "text": "Simulation"}, {"value": "3031", "text": "<PERSON><PERSON><PERSON>"}, {"value": "3016", "text": "Metropolis"}, {"value": "3053", "text": "Music"}, {"value": "3043", "text": "Stand-alone"}, {"value": "3002", "text": "Journey to the West"}, {"value": "3034", "text": "Naval battle"}, {"value": "3056", "text": "Puzzling"}, {"value": "3049", "text": "Dodge"}, {"value": "3019", "text": "Competitive strategy"}, {"value": "3061", "text": "Sport"}, {"value": "3041", "text": "Chess"}, {"value": "3055", "text": "Fish Hunter"}, {"value": "3015", "text": "Immediate"}, {"value": "3057", "text": "Football"}, {"value": "3045", "text": "Card Games Tabletop"}, {"value": "3026", "text": "History"}], "worldCreative": [{"label": "Africa", "value": "Africa", "children": [{"label": "Algeria", "value": "DZA", "flag": "dz"}, {"label": "Benin", "value": "BEN", "flag": "bj"}, {"label": "Botswana", "value": "BWA", "flag": "bw"}, {"label": "Burkina Faso", "value": "BFA", "flag": "bf"}, {"label": "Burundi", "value": "BDI", "flag": "bi"}, {"label": "Cameroon", "value": "CMR", "flag": "cm"}, {"label": "Cape Verde", "value": "CPV", "flag": "cv"}, {"label": "Central African", "value": "CAF", "flag": "cf"}, {"label": "Chad", "value": "TCD", "flag": "td"}, {"label": "Comoros", "value": "COM", "flag": "km"}, {"label": "DR Congo", "value": "COD", "flag": "cd"}, {"label": "Djibouti", "value": "DJI", "flag": "dj"}, {"label": "Egypt", "value": "EGY", "flag": "eg"}, {"label": "Equatorial Guinea", "value": "GNQ", "flag": "gq"}, {"label": "Ethiopia", "value": "ETH", "flag": "et"}, {"label": "Gabon", "value": "GAB", "flag": "ga"}, {"label": "Gambia (The)", "value": "GMB", "flag": "gm"}, {"label": "Ghana", "value": "GHA", "flag": "gh"}, {"label": "Guinea", "value": "GIN", "flag": "gn"}, {"label": "Kenya", "value": "KEN", "flag": "ke"}, {"label": "Lesotho", "value": "LSO", "flag": "ls"}, {"label": "Liberia", "value": "LBR", "flag": "lr"}, {"label": "Libyan Arab <PERSON><PERSON> (the)", "value": "LBY", "flag": "ly"}, {"label": "Madagascar", "value": "MDG", "flag": "mg"}, {"label": "Malawi", "value": "MWI", "flag": "mw"}, {"label": "Mali", "value": "MLI", "flag": "ml"}, {"label": "Mauritania", "value": "MRT", "flag": "mr"}, {"label": "Mauritius", "value": "MUS", "flag": "mu"}, {"label": "Morocco", "value": "MAR", "flag": "ma"}, {"label": "Mozambique", "value": "MOZ", "flag": "mz"}, {"label": "Namibia", "value": "NAM", "flag": "na"}, {"label": "Niger (the)", "value": "NER", "flag": "ne"}, {"label": "Nigeria", "value": "NGA", "flag": "ng"}, {"label": "Congo", "value": "COG", "flag": "cg"}, {"label": "Réunion", "value": "REU", "flag": "re"}, {"label": "Rwanda", "value": "RWA", "flag": "rw"}, {"label": "Sao Tome and Principe", "value": "STP", "flag": "st"}, {"label": "Senegal", "value": "SEN", "flag": "sn"}, {"label": "Seychelles", "value": "SYC", "flag": "sc"}, {"label": "Sierra Leone", "value": "SLE", "flag": "sl"}, {"label": "Somalia", "value": "SOM", "flag": "so"}, {"label": "South Africa", "value": "ZAF", "flag": "za"}, {"label": "Tanzania", "value": "TZA", "flag": "tz"}, {"label": "Tunisia", "value": "TUN", "flag": "tn"}, {"label": "Uganda", "value": "UGA", "flag": "ug"}, {"label": "Zambia", "value": "ZMB", "flag": "zm"}, {"label": "Zimbabwe", "value": "ZWE", "flag": "zw"}]}, {"label": "Asia", "value": "Asia", "children": [{"label": "China", "value": "CHN", "flag": "cn"}, {"label": "Afghanistan", "value": "AFG", "flag": "af"}, {"label": "Armenia", "value": "ARM", "flag": "am"}, {"label": "Azerbaijan", "value": "AZE", "flag": "az"}, {"label": "Bahrain", "value": "BHR", "flag": "bh"}, {"label": "Bangladesh", "value": "BGD", "flag": "bd"}, {"label": "Bhutan", "value": "BTN", "flag": "bt"}, {"label": "Brunei Darussalam", "value": "BRN", "flag": "bn"}, {"label": "Cambodia", "value": "KHM", "flag": "kh"}, {"label": "Hong Kong", "value": "HKG", "flag": "hk"}, {"label": "India", "value": "IND", "flag": "in"}, {"label": "Indonesia", "value": "IDN", "flag": "id"}, {"label": "Iran", "value": "IRN", "flag": "ir"}, {"label": "Iraq", "value": "IRQ", "flag": "iq"}, {"label": "Israel", "value": "ISR", "flag": "il"}, {"label": "Japan", "value": "JPN", "flag": "jp"}, {"label": "Jordan", "value": "JOR", "flag": "jo"}, {"label": "Kazakhstan", "value": "KAZ", "flag": "kz"}, {"label": "Korea", "value": "KOR", "flag": "kr"}, {"label": "Kuwait", "value": "KWT", "flag": "kw"}, {"label": "Kyrgyzstan", "value": "KGZ", "flag": "kg"}, {"label": "Laos", "value": "LAO", "flag": "la"}, {"label": "Lebanon", "value": "LBN", "flag": "lb"}, {"label": "Macao", "value": "MAC", "flag": "mo"}, {"label": "Malaysia", "value": "MYS", "flag": "my"}, {"label": "Maldives", "value": "MDV", "flag": "mv"}, {"label": "Mongolia", "value": "MNG", "flag": "mn"}, {"label": "Myanmar", "value": "MMR", "flag": "mm"}, {"label": "Nepal", "value": "NPL", "flag": "np"}, {"label": "Oman", "value": "OMN", "flag": "om"}, {"label": "Pakistan", "value": "PAK", "flag": "pk"}, {"label": "Palestine", "value": "PAL", "flag": "ps"}, {"label": "Philippines", "value": "PHL", "flag": "ph"}, {"label": "Qatar", "value": "QAT", "flag": "qa"}, {"label": "Saudi Arabia", "value": "SAU", "flag": "sa"}, {"label": "Singapore", "value": "SGP", "flag": "sg"}, {"label": "Sri Lanka", "value": "LKA", "flag": "lk"}, {"label": "Sudan (the)", "value": "SDN", "flag": "sd"}, {"label": "Syrian", "value": "SYR", "flag": "sy"}, {"label": "Taiwan", "value": "TWN", "flag": "tw"}, {"label": "Tajikistan", "value": "TJK", "flag": "tj"}, {"label": "Thailand", "value": "THA", "flag": "th"}, {"label": "United Arab Emirates", "value": "ARE", "flag": "ae"}, {"label": "Uzbekistan", "value": "UZB", "flag": "uz"}, {"label": "Viet Nam", "value": "VNM", "flag": "vn"}, {"label": "Yemen", "value": "YEM", "flag": "ye"}]}, {"label": "Europe", "value": "Europe", "children": [{"label": "Albania", "value": "ALB", "flag": "al"}, {"label": "Andorra", "value": "AND", "flag": "ad"}, {"label": "Austria", "value": "AUT", "flag": "at"}, {"label": "Belarus", "value": "BLR", "flag": "by"}, {"label": "Belgium", "value": "BEL", "flag": "be"}, {"label": "Bosnia Hercegovina", "value": "BIH", "flag": "ba"}, {"label": "Bulgaria", "value": "BGR", "flag": "bg"}, {"label": "Croatia", "value": "HRV", "flag": "hr"}, {"label": "Cyprus", "value": "CYP", "flag": "cy"}, {"label": "Czech", "value": "CZE", "flag": "cz"}, {"label": "Denmark", "value": "DNK", "flag": "dk"}, {"label": "Dominica", "value": "DMA", "flag": "dm"}, {"label": "Dominican", "value": "DOM", "flag": "do"}, {"label": "Estonia", "value": "EST", "flag": "ee"}, {"label": "Faroe Islands", "value": "FO", "flag": "fo"}, {"label": "Finland", "value": "FIN", "flag": "fi"}, {"label": "France", "value": "FRA", "flag": "fr"}, {"label": "Georgia", "value": "GEO", "flag": "ge"}, {"label": "Germany", "value": "DEU", "flag": "de"}, {"label": "Gibraltar", "value": "GIB", "flag": "gi"}, {"label": "Greece", "value": "GRC", "flag": "gr"}, {"label": "Greenland", "value": "GRL", "flag": "gl"}, {"label": "Guernsey", "value": "GG", "flag": "gg"}, {"label": "Hungary", "value": "HUN", "flag": "hu"}, {"label": "Iceland", "value": "ISL", "flag": "is"}, {"label": "Ireland", "value": "IRL", "flag": "ie"}, {"label": "Isle of Man", "value": "IM", "flag": "im"}, {"label": "Italy", "value": "ITA", "flag": "it"}, {"label": "Jersey", "value": "JE", "flag": "je"}, {"label": "Latvia", "value": "LVA", "flag": "lv"}, {"label": "Liechtenstein", "value": "LIE", "flag": "li"}, {"label": "Lithuania", "value": "LTU", "flag": "lt"}, {"label": "Luxembourg", "value": "LUX", "flag": "lu"}, {"label": "Malta", "value": "MLT", "flag": "mt"}, {"label": "Mayotte", "value": "MYT", "flag": "yt"}, {"label": "Moldavia", "value": "MDA", "flag": "md"}, {"label": "Monaco", "value": "MCO", "flag": "mc"}, {"label": "Serbia and Montenegro", "value": "S&M", "flag": "me"}, {"label": "Netherlands (the)", "value": "NLD", "flag": "nl"}, {"label": "Norway", "value": "NOR", "flag": "no"}, {"label": "Poland", "value": "POL", "flag": "pl"}, {"label": "Portugal", "value": "PRT", "flag": "pt"}, {"label": "Macedonia", "value": "MKD", "flag": "mk"}, {"label": "Romania", "value": "ROM", "flag": "ro"}, {"label": "Russia", "value": "RUS", "flag": "ru"}, {"label": "San Marino", "value": "SMR", "flag": "sm"}, {"label": "Slovakia", "value": "SVK", "flag": "rs"}, {"label": "Slovenia", "value": "SVN", "flag": "sk"}, {"label": "Spain", "value": "ESP", "flag": "es"}, {"label": "Swaziland", "value": "SWZ", "flag": "sz"}, {"label": "Sweden", "value": "SWE", "flag": "se"}, {"label": "Switzerland", "value": "CHE", "flag": "ch"}, {"label": "Turkey", "value": "TUR", "flag": "tr"}, {"label": "Ukraine", "value": "UKR", "flag": "ua"}, {"label": "United Kingdom", "value": "ENG", "flag": "gb"}]}, {"label": "Latin America", "value": "Latin_America", "children": [{"label": "Angola", "value": "AGO", "flag": "ao"}, {"label": "Antigua and Barbuda", "value": "ATG", "flag": "ag"}, {"label": "Argentina", "value": "ARG", "flag": "ar"}, {"label": "Aruba", "value": "ABW", "flag": "aw"}, {"label": "Bahamas (The)", "value": "BHS", "flag": "bs"}, {"label": "Barbados", "value": "BRB", "flag": "bb"}, {"label": "Belize", "value": "BLZ", "flag": "bz"}, {"label": "Bermuda", "value": "BMU", "flag": "bm"}, {"label": "Bolivia", "value": "BOL", "flag": "bo"}, {"label": "Brazil", "value": "BRA", "flag": "br"}, {"label": "Cayman Is", "value": "CYM", "flag": "ky"}, {"label": "Chile", "value": "CHL", "flag": "cl"}, {"label": "Colombia", "value": "COL", "flag": "co"}, {"label": "<PERSON>", "value": "COK", "flag": "ck"}, {"label": "Costa Rica", "value": "CRI", "flag": "cr"}, {"label": "Cuba", "value": "CUB", "flag": "cu"}, {"label": "Ecuador", "value": "ECU", "flag": "ec"}, {"label": "El Salvador", "value": "SLV", "flag": "sv"}, {"label": "French Guiana", "value": "GF", "flag": "gf"}, {"label": "Grenada", "value": "GRD", "flag": "gd"}, {"label": "Guatemala", "value": "GTM", "flag": "gp"}, {"label": "Guinea-Bissau", "value": "GNB", "flag": "gt"}, {"label": "Guyana", "value": "GUY", "flag": "gw"}, {"label": "Haiti", "value": "HTI", "flag": "gy"}, {"label": "Honduras", "value": "HND", "flag": "ht"}, {"label": "Jamaica", "value": "JAM", "flag": "hn"}, {"label": "Martinique", "value": "MTQ", "flag": "jm"}, {"label": "Mexico", "value": "MEX", "flag": "mx"}, {"label": "Nicaragua", "value": "NIC", "flag": "ni"}, {"label": "Panama", "value": "PAN", "flag": "pa"}, {"label": "Paraguay", "value": "PRY", "flag": "py"}, {"label": "Peru", "value": "PER", "flag": "pe"}, {"label": "Puerto Rico", "value": "PTR", "flag": "pr"}, {"label": "St Kitts-Nevis", "value": "KNA", "flag": "kn"}, {"label": "Saint Lucia", "value": "LC", "flag": "kn"}, {"label": "Saint Pierre and Miquelon", "value": "PM", "flag": "pm"}, {"label": "Saint Vincent and the Grenadines", "value": "VAG", "flag": "vc"}, {"label": "Suriname", "value": "SUR", "flag": "sr"}, {"label": "East Timor", "value": "TMP", "flag": "tl"}, {"label": "Togo", "value": "TGO", "flag": "tg"}, {"label": "Trinidad and Tobago", "value": "TTO", "flag": "tt"}, {"label": "Turks and Caicos Islands", "value": "TC", "flag": "tc"}, {"label": "Uruguay", "value": "URY", "flag": "uy"}, {"label": "Venezuela", "value": "VEN", "flag": "ve"}, {"label": "The British Virgin Islands", "value": "VG", "flag": "vg"}, {"label": "The United States Virgin Islands", "value": "VI", "flag": "vi"}]}, {"label": "North America", "value": "North_America", "children": [{"label": "Canada", "value": "CAN", "flag": "ca"}, {"label": "United States", "value": "USA", "flag": "us"}]}, {"label": "Oceania", "value": "Oceania", "children": [{"label": "American Samoa", "value": "AS", "flag": "as"}, {"label": "Australia", "value": "AUS", "flag": "au"}, {"label": "Fiji", "value": "FJI", "flag": "fj"}, {"label": "French Polynesia", "value": "PYF", "flag": "pf"}, {"label": "Guam", "value": "GU", "flag": "gu"}, {"label": "Marshall Is Rep", "value": "MHL", "flag": "mh"}, {"label": "Micronesia (the Federated States of)", "value": "FSM", "flag": "fm"}, {"label": "New Caledonia", "value": "NCL", "flag": "nc"}, {"label": "New Zealand", "value": "NZL", "flag": "nz"}, {"label": "Northern Mariana Islands", "value": "MP", "flag": "mp"}, {"label": "<PERSON><PERSON>", "value": "PLW", "flag": "pw"}, {"label": "Papua New Guinea", "value": "PNG", "flag": "pg"}, {"label": "Samoa", "value": "WSM", "flag": "wa"}, {"label": "Solomon Islands (the)", "value": "SLB", "flag": "sb"}, {"label": "Tonga", "value": "TON", "flag": "to"}, {"label": "Vanuatu", "value": "VUT", "flag": "vu"}, {"label": "Wallis and Futuna", "value": "WF", "flag": "wf"}]}, {"label": "Others", "value": "Others", "children": [{"label": "British Indian Ocean Territory", "value": "IO", "flag": "io"}, {"label": "Côte d'Ivoire", "value": "ci", "flag": "ci"}, {"label": "Curacao", "value": "cw", "flag": "cw"}, {"label": "European Union", "value": "eu", "flag": "eu"}, {"label": "Curaçao", "value": "CW", "flag": "fk"}, {"label": "Vatican City State", "value": "VAT", "flag": "va"}, {"label": "Kiribati", "value": "KIR", "flag": "ki"}, {"label": "Korea,DPR", "value": "PRK", "flag": "kp"}, {"label": "Nauru", "value": "NRU", "flag": "nr"}, {"label": "Norfolk Island", "value": "NF", "flag": "nf"}, {"label": "Saint <PERSON>", "value": "MF", "flag": "mf"}, {"label": "Sint Maarten", "value": "SX", "flag": "sx"}, {"label": "Tokelau", "value": "TK", "flag": "tk"}, {"label": "Turkmenistan", "value": "TKM", "flag": "tm"}, {"label": "Tuvalu", "value": "TUV", "flag": "tv"}, {"label": "Others", "value": "Others"}]}], "worldCodeCreative": {"CMR": "Cameroon", "BEN": "Benin", "MDG": "Madagascar", "RWA": "Rwanda", "SYC": "Seychelles", "CIV": "Cote d\"lvoire", "EGY": "Egypt", "MUS": "Mauritius", "BFA": "Burkina Faso", "ERI": "Eritrea", "STP": "Sao Tome and Principe", "AGO": "Angola", "LBY": "Libyan Arab <PERSON><PERSON> (the)", "ZWE": "Zimbabwe", "GIN": "Guinea", "SLE": "Sierra Leone", "REU": "Reunion", "GHA": "Ghana", "TZA": "Tanzania", "MLI": "Mali", "SOM": "Somalia", "MRT": "Mauritania", "UGA": "Uganda", "TCD": "Chad", "MYT": "Mayotte", "COM": "Comoros", "BWA": "Botswana", "SEN": "Senegal", "SWZ": "Swaziland", "GNB": "Guinea Bissau", "COD": "DR Congo", "CAF": "Central African", "LSO": "Lesotho", "COG": "Congo", "ZAF": "South Africa", "LBR": "Liberia", "TUN": "Tunisia", "ZMB": "Zambia", "NER": "Niger", "ESH": "Western Sahara", "TGO": "Togo", "NAM": "Namibia", "MOZ": "Mozambique", "ETH": "Ethiopia", "MAR": "Morocco", "MWI": "Malawi", "NGA": "Nigeria", "CPV": "Cape Verde", "BDI": "Burundi", "DZA": "Algeria", "DJI": "Djibouti", "GMB": "Gambia", "GNQ": "Equatorial Guinea", "SDN": "Sudan", "KEN": "Kenya", "SGP": "Singapore", "KOR": "Korea", "SYR": "Syrian", "UZB": "Uzbekistan", "BHR": "<PERSON><PERSON><PERSON>", "JPN": "Japan", "JOR": "Jordan", "VNM": "Vietnam", "KGZ": "Kirghizia", "THA": "Thailand", "LKA": "Sri Lanka", "ARE": "United Arab Emirates", "LAO": "Laos", "AFG": "Afghanistan", "MAC": "Macau", "TJK": "Tajikistan", "PRK": "KoreaDPR", "PAL": "Palestine", "HKG": "Hong Kong", "IRQ": "Iraq", "LBN": "Lebanon", "KWT": "Kuwait", "BRN": "Brunei", "MDV": "Maldives", "IDN": "Indonesia", "ISR": "Israel", "MNG": "Mongolia", "OMN": "Oman", "IND": "India", "MMR": "Myanmar", "MYS": "Malaysia", "TMP": "East Timor", "YEM": "Yemen", "BTN": "Bhutan", "KHM": "Cambodia", "PAK": "Pakistan", "BGD": "Bangladesh", "SAU": "Saudi Arabia", "TKM": "Turkmenistan", "QAT": "Qatar", "NPL": "Nepal", "KAZ": "Kazakhstan", "PHL": "Philippines", "TWN": "Taiwan", "CHN": "China", "IRN": "Iran", "CRI": "Costa Rica", "CUB": "Cuba", "DOM": "Dominican", "MEX": "Mexico", "NIC": "Nicaragua", "PAN": "Panama", "ANT": "Netherlands Antilles", "SLV": "El Salvador", "PTR": "Puerto Rico", "VAG": "Saint Vincent and the Grenadines", "HND": "Honduras", "GTM": "Guatemala", "GEO": "Georgia", "ARM": "Armenia", "AZE": "Azerbaijan", "BLR": "Belarus", "RUS": "Russia", "UKR": "Ukraine", "HUN": "Hungary", "ISL": "Iceland", "MLT": "Malta", "MCO": "Monaco", "NOR": "Norway", "ROM": "Romania", "SMR": "San Marino", "SWE": "Sweden", "CHE": "Switzerland", "EST": "Estonia", "LVA": "Latvia", "LTU": "Lithuania", "MDA": "Moldavia", "TUR": "Turkey", "SVN": "Slovenia", "CZE": "Czech", "SVK": "Slovak", "MKD": "Macedonia", "BIH": "Bosnia Hercegovina", "VAT": "Vatican City State", "NLD": "Netherlands", "HRV": "Croatia", "GRC": "Greece", "IRL": "Ireland", "BEL": "Belgium", "CYP": "Cyprus", "DNK": "Denmark", "ENG": "United Kingdom", "DEU": "Germany", "FRA": "France", "ITA": "Italy", "LUX": "Luxembourg", "PRT": "Portugal", "POL": "Poland", "ESP": "Spain", "ALB": "Albania", "AND": "Andorra", "LIE": "Liechtenstein", "S&M": "Serbia and Montenegro", "AUT": "Austria", "BGR": "Bulgaria", "FIN": "Finland", "GIB": "Gibraltar", "DMA": "Dominica", "BMU": "Bermuda", "CAN": "Canada", "USA": "United States", "GRL": "Greenland", "TON": "Tonga", "AUS": "Australia", "COK": "<PERSON>", "NRU": "Nauru", "NCL": "New Caledonia", "VUT": "Vanuatu", "SLB": "Solomon Is", "WSM": "Samoa", "TUV": "Tuvalu", "FSM": "Micronesia", "MHL": "Marshall Is Rep", "KIR": "Kiribati", "PYF": "French Polynesia", "NZL": "New Zealand", "FJI": "Fiji", "PNG": "Papua New Guinea", "PLW": "<PERSON><PERSON>", "CHL": "Chile", "COL": "Colombia", "GUY": "Guyana", "PRY": "Paraguay", "PER": "Peru", "SUR": "Suriname", "VEN": "Venezuela", "URY": "Uruguay", "ECU": "Ecuador", "ATG": "Antigua and Barbuda", "ABW": "Aruba", "BHS": "Bahamas", "BRB": "Barbados", "CYM": "Cayman Is", "GRD": "Grenada", "HTI": "Haiti", "JAM": "Jamaica", "MTQ": "Martinique", "MSR": "Montserrat", "TTO": "Trinidad and Tobago", "KNA": "St Kitts-Nevis", "SPM": "St.Pierre and Miquelon", "ARG": "Argentina", "BLZ": "Belize", "BOL": "Bolivia", "BRA": "Brazil", "AS": "American Samoa", "AX": "Aland Islands", "BL": "<PERSON>", "BQ": "Bonaire Sint Eustatius and Saba", "BV": "Bouvet Island", "CC": "Cocos (Keeling) Islands", "CW": "Curaçao", "CX": "Christmas Island", "FK": "Falkland Islands (Malvinas)", "FO": "Faroe Islands", "GF": "French Guiana", "GG": "Guernsey", "GS": "South Georgia and The South Sandwich Islands", "GU": "Guam", "HM": "Heard Island and McDonald Islands", "IM": "Isle Of Man", "IO": "British Indian Ocean Territory", "JE": "Jersey", "LC": "Saint Lucia", "MF": "Saint <PERSON>", "MP": "Northern Mariana Islands", "NF": "Norfolk Island", "NU": "Niue", "PM": "Saint Pierre and Miquelon", "PN": "Pitcairn Islands", "SH": "Saint Helena", "SJ": "Svalbard and Jan Mayen Islands", "SX": "Sint Maarten", "TC": "Turks and Caicos Islands", "TF": "French Southern Territories", "TK": "Tokelau", "UM": "United States Minor Outlying Islands", "VG": "The British Virgin Islands", "VI": "The United States Virgin Islands", "WF": "Wallis and Futuna Islands", "XK": "Kosovo", "GAB": "Gabon", "Others": "Others", "ci": "Côte d'Ivoire", "cw": "Curacao", "eu": "European Union"}, "themeCode": {"3001": "Legend", "3005": "Three Kingdoms", "3012": "Xianxia", "3063": "Crime", "3023": "War", "3009": "Magic", "3024": "Tower Defence", "3010": "Chinese Fantasy", "3051": "Idle", "3022": "Manage", "3021": "Cultivate", "3035": "Fighting", "3037": "Orientation - Landscape", "3025": "Strategy", "3036": "Adventure", "3017": "Kpop", "3054": "Puzzle", "3018": "Card", "3011": "Wuxia", "3052": "Match", "3013": "Cartoon", "3038": "Jump", "3014": "Turn-based", "3028": "Shooting", "3027": "Simulation", "3031": "<PERSON><PERSON><PERSON>", "3016": "Metropolis", "3053": "Music", "3043": "Stand-alone", "3002": "Journey to the West", "3034": "Naval battle", "3056": "Puzzling", "3049": "Dodge", "3019": "Competitive strategy", "3061": "Sport", "3041": "Chess", "3055": "Fish Hunter", "3015": "Immediate", "3057": "Football", "3045": "Card Games Tabletop", "3026": "History"}, "os": [{"value": "1", "text": "IOS"}, {"value": "2", "text": "Android"}], "osCode": {"1": "IOS", "2": "Android", "3": "All Platform"}, "countryFlagCode": {"NZL": "nz", "FJI": "fj", "PNG": "pg", "STP": "st", "MHL": "mh", "CUB": "cu", "SDN": "sd", "GMB": "gm", "MYS": "my", "MYT": "yt", "TWN": "tw", "POL": "pl", "OMN": "om", "SUR": "sr", "AS": "as", "ARE": "ae", "KEN": "ke", "ARG": "ar", "GNB": "gt", "ARM": "am", "UZB": "uz", "SEN": "sn", "BTN": "bt", "TGO": "tg", "IRL": "ie", "IRN": "ir", "QAT": "qa", "BDI": "bi", "NLD": "nl", "IRQ": "iq", "SVK": "rs", "SVN": "sk", "GNQ": "gq", "THA": "th", "ABW": "aw", "SWE": "se", "ISL": "is", "SX": "sx", "ci": "ci", "MKD": "mk", "BEL": "be", "ISR": "il", "KWT": "kw", "LIE": "li", "DZA": "dz", "BEN": "bj", "TC": "tc", "RUS": "ru", "ATG": "ag", "cw": "cw", "CW": "fk", "ITA": "it", "SWZ": "sz", "TK": "tk", "TZA": "tz", "PAL": "ps", "PAK": "pk", "S&M": "me", "BFA": "bf", "PAN": "pa", "SGP": "sg", "UKR": "ua", "KGZ": "kg", "CHE": "ch", "DJI": "dj", "REU": "re", "CHL": "cl", "CHN": "cn", "PRK": "kp", "MLI": "ml", "BWA": "bw", "HRV": "hr", "KHM": "kh", "IDN": "id", "PRT": "pt", "TJK": "tj", "VNM": "vn", "MLT": "mt", "CYM": "ky", "PRY": "py", "CYP": "cy", "SYC": "sc", "RWA": "rw", "BGD": "bd", "AUS": "au", "AUT": "at", "LKA": "lk", "GAB": "ga", "ZWE": "zw", "BGR": "bg", "SYR": "sy", "CZE": "cz", "NOR": "no", "eu": "eu", "VG": "vg", "MMR": "mm", "VI": "vi", "KIR": "ki", "TKM": "tm", "GRD": "gd", "GRC": "gr", "HTI": "gy", "ENG": "gb", "YEM": "ye", "GRL": "gl", "AFG": "af", "MNG": "mn", "FO": "fo", "NPL": "np", "BHS": "bs", "BHR": "bh", "PTR": "pr", "WF": "wf", "DMA": "dm", "GF": "gf", "BIH": "ba", "GG": "gg", "HUN": "hu", "AGO": "ao", "GU": "gu", "WSM": "wa", "FRA": "fr", "TMP": "tl", "MOZ": "mz", "NAM": "na", "PER": "pe", "VAG": "vc", "DNK": "dk", "GTM": "gp", "SLB": "sb", "VAT": "va", "SLE": "sl", "NRU": "nr", "SLV": "sv", "FSM": "fm", "DOM": "do", "IM": "im", "IO": "io", "CMR": "cm", "GUY": "gw", "AZE": "az", "MAC": "mo", "GEO": "ge", "TON": "to", "NCL": "nc", "SMR": "sm", "JE": "je", "MAR": "ma", "KNA": "kn", "BLR": "by", "MRT": "mr", "BLZ": "bz", "PHL": "ph", "COD": "cd", "COG": "cg", "PYF": "pf", "URY": "uy", "COK": "ck", "COM": "km", "COL": "co", "USA": "us", "ESP": "es", "EST": "ee", "BMU": "bm", "ZMB": "zm", "KOR": "kr", "SOM": "so", "VUT": "vu", "ECU": "ec", "ALB": "al", "LC": "kn", "ETH": "et", "MCO": "mc", "NER": "ne", "LAO": "la", "VEN": "ve", "GHA": "gh", "CPV": "cv", "MDA": "md", "MTQ": "jm", "MDG": "mg", "LBN": "lb", "MF": "mf", "LBR": "lr", "MDV": "mv", "BOL": "bo", "GIB": "gi", "LBY": "ly", "MP": "mp", "HKG": "hk", "CAF": "cf", "LSO": "ls", "NGA": "ng", "MUS": "mu", "JOR": "jo", "GIN": "gn", "ROM": "ro", "CAN": "ca", "TCD": "td", "AND": "ad", "NF": "nf", "CRI": "cr", "IND": "in", "MEX": "mx", "KAZ": "kz", "SAU": "sa", "JPN": "jp", "LTU": "lt", "TTO": "tt", "PLW": "pw", "MWI": "mw", "NIC": "ni", "FIN": "fi", "TUN": "tn", "UGA": "ug", "LUX": "lu", "TUR": "tr", "BRA": "br", "BRB": "bb", "TUV": "tv", "DEU": "de", "EGY": "eg", "LVA": "lv", "JAM": "hn", "PM": "pm", "ZAF": "za", "BRN": "bn", "HND": "ht"}, "AnalyzePieL": [{"value": "Impression", "label": "Impression"}, {"value": "Days Active", "label": "Days Active"}, {"value": "Popular", "label": "Popular"}, {"value": "Interaction", "label": "Interaction"}, {"value": "Conversion", "label": "Conversion"}], "AnalyzePieR": [{"value": "Game", "label": "Game"}, {"value": "Media Source", "label": "Media Source"}, {"value": "OS", "label": "OS"}, {"value": "Country", "label": "Country"}, {"value": "Language", "label": "Language"}], "ScatterCode": {"Impression": "impression_number", "Days Active": "days", "Popular": "heat", "Interaction": "interaction_number", "Conversion": "conversion_number"}, "marketTypeCondition": [{"value": "cpi", "text": "CPI"}, {"value": "install", "text": "Install"}, {"value": "impression", "text": "Impression"}, {"value": "cost", "text": "Cost"}, {"value": "click", "text": "Click"}], "marketTableAttribute": [{"value": "category", "label": "Category"}, {"value": "country", "label": "Country"}, {"value": "date", "label": "Date"}, {"value": "platform", "label": "OS"}]}