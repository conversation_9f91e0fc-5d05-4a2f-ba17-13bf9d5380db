import { defineStore } from 'pinia';
import { get } from '@vueuse/core';
import { STORE_KEY } from '@/config/config';
import { computed, reactive, ref, watch, shallowRef, nextTick } from 'vue';
import { useRouter, onBeforeRouteLeave } from 'vue-router';
import dayjs from 'dayjs';
import { useDownloadFile } from 'common/compose/download-file';
import { useWatchGameChange } from 'common/compose/request/game';
// components
// store
import { useGlobalGameStore } from '@/store/global/game.store';
// service
import { getGameCompetitor } from 'common/service/intelligence/creative/get-game-competitor';
import { postPie } from 'common/service/intelligence/creative/post-pie';
// import { getTagsService } from 'common/service/intelligence/creative/get-tags';
// type
import { type ICustomViewItem } from 'common/components/NewViewTab/type.d';
// import type { IFormDynamicItem } from 'common/components/FormContainer';
import { ICompetitor, ICreativeItem, type IGameCode } from '../competitor/competitor';
import { ICreativeParam, IFormOptions, IOrderBy, IFormParams } from './overview';
import { CommonOptionsItemType } from '@@/index';
import type { SortOptions, SortInfo } from 'tdesign-vue-next';

// util
import { converFormGameOption, dayRangeFormat, downDataBypage, toCreativeXlsxData, retBtn, creativeListAttaCompetitorType } from './utis';
import { cloneDeep } from 'lodash-es';
import { useLoading } from 'common/compose/loading';
import { isMobileSite, isPCSite } from 'common/utils/device';
import { useIntelligenceJumpRoute } from '@/store/intelligence/creative/common/jumpRoute';
// const
import {
  DEFAULT_CONDITION,
  DEFAULT_FILED_OBJ,
  DEFAULT_CONDITIONLIST,
  DEFAULT_PAGE,
  DEFAULT_PAGE_SIZE,
  DEFAULT_TABLE_COLUMNS,
  METRICE_OPTIONS,
  DEFAULT_METRICS,
  OverviewTabType,
} from './overview.const';
import {
  LEFT_SIDE_OPTIONS,
  RIGHT_SIDE_OPTIONS,
} from '../analyze/analyze.const';
import { getCreativePageService } from 'common/service/intelligence/creative/get-creative-page';
import { COMPETITORTYPE } from '../config/selectOptions.const';

const TODDAY_FORMAT = 'YYYY-MM-DD HH-mm-ss';

export const useIntelligenceCreativeOverviewStore = defineStore(STORE_KEY.INTELLIGENCE.CREATIVE.OVERVIEW, () => {
  const gameStore = useGlobalGameStore();
  const isNeedInitForm = ref(true); // 是否需要重新初始化form选择
  const isRequetGameOptions = ref(false); // 是否请求完成GameOptions 所有竞品列表
  const test = ref<string>('');
  const router = useRouter();
  const { jumpOverviewCreativeGalleryPage, jumpOverviewAnalyzePage } = useIntelligenceJumpRoute();
  const activeCompetitorCode = ref<string>(''); // url携带的竞品code
  // ------ tab ------
  /**
   *{ value: OverviewTabType.ANALYZE, label: OverviewTabType.ANALYZE,
     game: '', param: {}, hidden: false, type: 'default' },
   */
  const tabList = ref<Array<ICustomViewItem>>([
    { value: OverviewTabType.CREATIVEGALLERY, label: OverviewTabType.CREATIVEGALLERY, game: '', param: {}, hidden: false, type: 'default' },
    { value: OverviewTabType.ANALYZE, label: OverviewTabType.ANALYZE, game: '', param: {}, hidden: false, type: 'default' },
  ]); //
  const tabSelectId = ref<string>(tabList.value[0].value);
  const tabProps = reactive<Object>({
    modelValue: tabSelectId,
    list: tabList,
    showNum: 3,
    shareParams: {},
    hideSaveBtn: true,
    hideShareView: true,
    'onUpdate:modelValue': (newValue: typeof tabSelectId.value) => (tabSelectId.value = newValue),
  });
  // ------ form ------
  const competitorTypes = ref<Array<string>>(COMPETITORTYPE.map(item => item.value));
  const competitorOptions = ref<Array<CommonOptionsItemType>>([]);
  // const tagOptions = ref<Array<CommonOptionsItemType>>([]);
  const formOptions = ref<IFormOptions>({
    fieldObj: cloneDeep(DEFAULT_FILED_OBJ),
    conditionList: cloneDeep(DEFAULT_CONDITIONLIST),
  });

  const getConditionList = ({ src, fieldObj }: { src: any[]; fieldObj: any }) => src.map((item) => {
    const { props = {}, ext: { key = '' } = {} } = item;
    let newProps = props;
    const list = (fieldObj as any)[key];
    switch (key) {
      case 'competitor_type':
        newProps = { ...props, list };
        newProps.button = retBtn(key, list);
        break;
      case 'competitors':
        newProps = { ...props, list };
        newProps.button = retBtn(key, list);
        break;
      case 'geo':
        newProps = { ...props, options: list };
        break;
      case 'channel':
        newProps = { ...props, list };
        newProps.button = retBtn(key, list);
        break;
      case 'os':
        newProps = { ...props, list };
        newProps.button = retBtn(key, list);
        break;
      case 'str_lang':
        newProps = { ...props, list };
        newProps.button = retBtn(key, list);
        break;
      case 'creative_type':
        newProps = { ...props, list };
        newProps.button = retBtn(key, list);
        break;
      case 'activeDayRange':
        newProps = { ...props, list };
        break;
        // case 'tag':
        //   newProps = { ...props, list };
        //   newProps.button = retBtn(key, list);
        //   break;
        // case 'SearchBox':
        //   newProps = { ...props, fieldList: list };
        //   break;
        // case 'MultipleSelect':
        //   newProps = { ...props, list };
        //   break;
    }
    return { ...item, props: newProps };
  });
  const condition = reactive<{ cur: IFormParams; default: IFormParams }>({
    cur: cloneDeep(DEFAULT_CONDITION),
    default: cloneDeep(DEFAULT_CONDITION),
  });
  watch(competitorTypes, () => {
    // console.log('change competitorTypes', competitorTypes.value);
    condition.cur.competitors = (formOptions.value.fieldObj.competitors?.map(item => item.value)) as Array<string>;
    condition.default.competitors = (formOptions.value.fieldObj.competitors?.map(item => item.value)) as Array<string>;
  });
  watch(condition, () => {
    if (competitorTypes.value.length !== condition.cur.competitor_type.length) {
      competitorTypes.value = condition.cur.competitor_type;
    }
    formOptions.value.fieldObj.competitors = getGameOptionsByGameType();
  });
  const conditionList = computed(() => getConditionList({
    fieldObj: formOptions.value.fieldObj,
    src: formOptions.value.conditionList,
  }));
  const resetFormFilter = () => {
    changeGameType();
    const { activeCompetitorCode: activeCompetitorCodeRaw = '', startTime = '', endTime = '' }: any = router.currentRoute.value.query;
    activeCompetitorCode.value = activeCompetitorCodeRaw;
    if (startTime && endTime) {
      condition.default.creative_time = [dayjs(`${startTime} 00:00:00`), dayjs(`${endTime} 23:59:59`)];
    } else {
      condition.default.creative_time = cloneDeep(DEFAULT_CONDITION.creative_time);
    };
    const tempFieldObj = cloneDeep(DEFAULT_FILED_OBJ);
    formOptions.value.fieldObj = tempFieldObj;
    formOptions.value.conditionList = cloneDeep(DEFAULT_CONDITIONLIST);
    nextTick(() => {
      formOptions.value.fieldObj.competitors = getGameOptionsByGameType();
      // formOptions.value.fieldObj.tag = tagOptions.value;
      condition.cur.competitor_type = [...condition.default.competitor_type];
      condition.cur.competitors = [...condition.default.competitors];
      // condition.cur.tag = [...condition.default.tag];
      condition.cur.creative_time = [...condition.default.creative_time];
      condition.cur.activeDayRange = [...condition.default.activeDayRange];
      condition.cur.geo = [...condition.default.geo];
      condition.cur.channel = [...condition.default.channel];
      condition.cur.os = [...condition.default.os];
      condition.cur.str_lang = [...condition.default.str_lang];
      condition.cur.creative_type = [...condition.default.creative_type];
      getGameOptionsCheckedGames();
    });
  };
  // keyword
  // ------ loading ------
  const { isLoading: isLoadingForm, showLoading: showLoadingForm, hideLoading: hideLoadingForm } = useLoading();
  const {
    isLoading: isLoadingCreativeList,
    showLoading: showLoadingCreativeList,
    hideLoading: hideLoadingCreativeList,
  } = useLoading(false);
  // views
  const competitorList = ref<Array<ICompetitor>>([]); // 竞品列表
  /**
   * storeid映射competitor;
   * 1.暂时用竞品列表的competitor_type,同时素材的store_id在storeIdCompetitor映射中找到competitor_type
   * 2.假如competitorList的数据过大，影响映射查询速度
   * 3.假如competitorList采用分页查询，找competitor_type需要在后端做
   */
  const storeIdCompetitor: Record<string, ICompetitor> = {};
  const page = ref<number>(DEFAULT_PAGE);
  const pageSize = ref<number>(DEFAULT_PAGE_SIZE);
  const total = ref<number>(0);
  const buttons = computed(() => {
    const bths = [
      {
        name: !isListType.value ? 'group' : 'more-select',
        label: 'Switch View',
        type: ['aix', 'media'],
        method: () => (isListType.value = !isListType.value),
      },
    ];
    if (!isListType.value) {
      bths.unshift({
        name: 'more',
        label: 'Select More Metrics',
        type: ['aix', 'media'],
        method: () => get(tableRef)?.showMetricsSelect(),
      });
    }
    if (!isListType.value && !isLoadingCreativeDown.value) {
      bths.push({
        name: 'download',
        label: 'Download',
        type: ['aix', 'media'],
        method: () => downladCreativeListByPage(),
      });
    }
    return bths;
  });

  // ------ Creative Gallery ------
  const isListType = ref<boolean>(true); // ture -> list; false -> table;
  const orderBy = ref<IOrderBy>({ key: 'impression_number', rule: 'desc' }); // 排序
  const metric = ref<Array<string>>(cloneDeep(DEFAULT_METRICS)); // 表格展示属性
  const creativeList = ref<Array<ICreativeItem>>([]); // 素材列表数据
  // ------ Creative Gallery table ------
  const tableRef = ref();
  const displayColumns = computed(() => {
    const fixedCol = ['id', 'game', 'resource'];
    if (metric.value.length === 0) {
      return [...fixedCol, ...METRICE_OPTIONS.map(item => item.value)];
    }
    return [...fixedCol, ...metric.value];
  });
  const {
    // 下载素材的loading
    isLoading: isLoadingCreativeDown,
    hideLoading: hideLoadingCreativeDown,
    showLoading: showLoadingCreativeDown,
  } = useLoading(false);
  const percentage = ref<number>(0);
  const tableAllColumns = shallowRef<Array<{ colKey: string; title: string }>>(DEFAULT_TABLE_COLUMNS);
  const columsSlot = ref<Array<string>>(['game']);

  // methods
  const getCompetitorList = async () => {
    // 获取大包列表
    // const { active = '' }: any = router.currentRoute.value.query;
    const params: IGameCode = {
      game_code: '',
    };
    const resOrigin = await getGameCompetitor(params) ?? [];
    const res = resOrigin?.map((item: ICompetitor) => {
      const temp = { ...item };
      if (!item.store_ids || item.store_ids.length === 0) {
        temp.store_ids = ['nostore'];
      } else {
        item.store_ids.forEach((storeItem: string) => {
          storeIdCompetitor[storeItem] = item;
        });
      };
      return temp;
    }) ?? [];
    competitorList.value = cloneDeep(res);
    const gameOptions = converFormGameOption(res);
    competitorOptions.value = cloneDeep(gameOptions);
    getGameOptionsCheckedGames();
  };
  const getGameOptionsCheckedGames = async () => {
    if (activeCompetitorCode.value) {
      const activeCompetitorItem = competitorList.value
        .find(item => item.competitor_code === activeCompetitorCode.value);
      if (activeCompetitorItem) {
        condition.cur.competitor_type = [activeCompetitorItem?.competitor_type || ''];
        competitorTypes.value = [activeCompetitorItem?.competitor_type || ''];
        formOptions.value.fieldObj.competitors = getGameOptionsByGameType();
      };
      nextTick(() => {
        condition.cur.competitors = [activeCompetitorCode.value];
      });
    } else {
      condition.cur.competitors = getGameOptionsByGameType().map(item => item.value) as Array<string>;
    };
    condition.default.competitors = getGameOptionsByGameType().map(item => item.value) as Array<string>;
    isRequetGameOptions.value = true;
  };
  // 设置game的options选项和选中的games
  const getGameOptionsByGameType = (): Array<CommonOptionsItemType> => {
    const competitorTypeLen = condition.cur.competitor_type.length;
    let gameOptionTemp: Array<CommonOptionsItemType> = [];
    if (competitorTypeLen === 2 || competitorTypeLen === 0) {
      gameOptionTemp = competitorOptions.value.map(item => item);
    } else if (condition.cur.competitor_type[0] === 'mobile') {
      gameOptionTemp = competitorOptions.value.filter(item => item.icon === 'mobile');
    } else if (condition.cur.competitor_type[0] === 'pc') {
      gameOptionTemp = competitorOptions.value.filter(item => item.icon === 'pc');
    };
    return gameOptionTemp;
  };
  // const getTagList = async () => {
  //   // 获取标签列表
  //   const res = await getTagsService();
  //   const tagOptionsTemp: Array<CommonOptionsItemType> = res.map(item => ({ label: item.name_en, value: item.id }));
  //   tagOptions.value = cloneDeep(tagOptionsTemp);
  //   formOptions.value.fieldObj.tag = tagOptionsTemp;
  //   condition.cur.tag = tagOptionsTemp.map(item => item.value.toString());
  //   condition.default.tag = tagOptionsTemp.map(item => item.value.toString());
  // };
  const getCreativePage = async () => {
    // 分页获取素材列表
    await nextTick();
    const params = buildCreativeParam();
    showLoadingCreativeList();
    const res = await getCreativePageService(params);
    creativeList.value = creativeListAttaCompetitorType(res.list, storeIdCompetitor);;
    total.value = res.total;
    hideLoadingCreativeList();
  };
  const getPieChart = async (conditionList: IFormParams, selectedChart: string) => {
    showLoadingForm();
    await nextTick();
    const formatDate = 'YYYY-MM-DD';
    const [creativeTimeStart, creativeTimeEnd] = conditionList.creative_time;
    const [daysMin, daysMax] = conditionList.activeDayRange;
    const listData = {
      creative_time: `${dayjs(creativeTimeStart).format(formatDate)} 00:00:00,${dayjs(creativeTimeEnd).format(formatDate)}  23:59:59`,
      geo: conditionList.geo.length !== condition.default.geo.length ? conditionList.geo.join(',') : '',
      channel: conditionList.channel.length !== condition.default.channel.length ? conditionList.channel.join(',') : '',
      os: conditionList.os.length !== condition.default.os.length ? conditionList.os.join(',') : '',
      str_lang: conditionList.str_lang.length !== condition.default.str_lang.length ? conditionList.str_lang.join(',') : '',
      creative_type: conditionList.creative_type.join(','),
      daysMin: dayRangeFormat(daysMin),
      daysMax: dayRangeFormat(daysMax),
      store_id: getStoreIdStrByGameType(),
      chart: selectedChart,
    };

    const res = await postPie(listData);
    hideLoadingForm();
    return res;
  };
  const initGetCreativePage = async () => {
    // 初始化分页获取素材列表
    page.value = DEFAULT_PAGE;
    pageSize.value = DEFAULT_PAGE_SIZE;
    await getCreativePage();
  };
  const buildCreativeParam = (): ICreativeParam => {
    const { cur, default: defaultRaw } = condition;
    const format = 'YYYY-MM-DD';
    const temp: ICreativeParam = {
      creative_time: `${dayjs(cur.creative_time[0]).format(format)} 00:00:00,${dayjs(cur.creative_time[1]).format(format)} 23:59:59`,
      geo: cur.geo.length !== defaultRaw.geo.length ? cur.geo.join(',') : '',
      channel: cur.channel.length !== defaultRaw.channel.length ? cur.channel.join(',') : '',
      os: cur.os.length !== defaultRaw.os.length ? cur.os.join(',') : '',
      str_lang: cur.str_lang.length !== defaultRaw.str_lang.length ? cur.str_lang.join(',') : '',
      creative_type: cur.creative_type.join(','),
      daysMax: dayRangeFormat(cur.activeDayRange[1]),
      daysMin: dayRangeFormat(cur.activeDayRange[0]),
      // tag: cur.tag.length !== defaultRaw.tag.length ? cur.tag.join(',') : '',
      store_id: getStoreIdStrByGameType(),
      page: `${page.value}`,
      page_size: `${pageSize.value}`,
      order_by: `${orderBy.value.key},${isListType.value ? 'desc' : orderBy.value.rule}`,
      // keyword: cur.keyword,
    };
    return temp;
  };
  const getStoreIdStrByGameType = (): string => {
    const { cur, default: defaultRaw } = condition;
    if (cur.competitors.length === 0 || cur.competitors.length === 2) { // 表明游戏类型是全选
      return cur.competitors.length !== defaultRaw.competitors.length ? getSelectStore().join(',') : '';
    };
    return getSelectStore().join(',');
  };
  const getStoreIdAll = (): Array<string> => {
    let arr: Array<string> = [];
    competitorList.value.forEach((item: ICompetitor) => {
      arr = arr.concat(item.store_ids);
    });
    return arr;
  };
  const getSelectStore = (): Array<string> => {
    if (condition.cur.competitors.length === 0) {
      return getStoreIdAll();
    }
    let arr: Array<string> = [];
    competitorList.value
      .filter(item => condition.cur.competitors.includes(item.competitor_code))
      .forEach((item: ICompetitor) => {
        arr = arr.concat(item.store_ids);
      });
    return arr;
  };
  const downladCreativeListByPage = (): any => {
    async function down() {
      showLoadingCreativeDown();
      const dataAll = await downDataBypage(
        '/api/intelligence/creative/overview/download_creative_page',
        buildCreativeParam(),
        total.value,
        50,
        3,
        (val: number) => percentage.value = val,
        true,
        true,
      );
      hideLoadingCreativeDown();
      percentage.value = 0;
      const xlsxData = toCreativeXlsxData(dataAll, storeIdCompetitor);
      useDownloadFile(xlsxData, `AiX Intelligence-Creative Gallery - ${dayjs().format(TODDAY_FORMAT)}.xlsx`);
    };
    down();
    return true;
  };
  const setOrderBy = (val: IOrderBy) => {
    orderBy.value = { ...val };
  };
  const toggleIsListType = () => {
    isListType.value = !isListType.value;
  };
  const showListType = () => {
    isListType.value = true;
  };
  const onSortChange = (sort: SortInfo, options: SortOptions<ICreativeItem>) => {
    // 表格排序回调
    console.log(options);
    switch (sort?.sortBy) {
      case 'id': // id 排序没有发起后端 就是数组排序
        if (sort.descending) {
          creativeList.value = cloneDeep(creativeList.value).sort((a, b) => parseInt(`${b.id}`, 10) - parseInt(`${a.id}`, 10));
        } else {
          creativeList.value = cloneDeep(creativeList.value).sort((a, b) => parseInt(`${a.id}`, 10) - parseInt(`${b.id}`, 10));
        }
        break;
      case 'impression':
        setOrderBy({ key: 'impression_number', rule: sort.descending ? 'desc' : 'asc' });
        break;
      case 'days':
        setOrderBy({ key: 'days', rule: sort.descending ? 'desc' : 'asc' });
        break;
      case 'popular':
        setOrderBy({ key: 'heat', rule: sort.descending ? 'desc' : 'asc' });
        break;
      case 'countries':
        setOrderBy({ key: 'countries_number', rule: sort.descending ? 'desc' : 'asc' });
        break;
      case 'channel':
        setOrderBy({ key: 'channel', rule: sort.descending ? 'desc' : 'asc' });
        break;
    }
  };
  const hideListType = () => {
    isListType.value = false;
  };
  const changeGameType = () => {
    if (isPCSite()) { // 是pc站点
      if (gameStore.isMobileGame()) { //
        condition.cur.competitor_type = ['mobile'];
        condition.default.competitor_type = ['mobile'];
        competitorTypes.value = ['mobile'];
      } else {
        condition.cur.competitor_type = ['pc'];
        condition.default.competitor_type = ['pc'];
        competitorTypes.value = ['pc'];
      };
      // console.log('isMobileGame', gameStore.isMobileGame());
    };
  };

  // ------ Analyze Chart ------
  const model1SelectedDropdownItem = ref(`${LEFT_SIDE_OPTIONS[0].label},${RIGHT_SIDE_OPTIONS.find(item => item.label === 'Media Source')?.label}`);
  const model2SelectedDropdownItem = ref(`${LEFT_SIDE_OPTIONS[0].label},${RIGHT_SIDE_OPTIONS.find(item => item.label === 'Country')?.label}`);
  const nameAxisLabelFormat = (params: string) => {
    const maxLength = 8; // 最多展示8个字体，其余字体以“...”代替
    return params.length > maxLength ? `${params.slice(0, maxLength)}...` : params;
  };
  const numberAxisLabelFormat = (params: number) => {
    if (params >= 1e6) {
      return `${(params / 1e6).toFixed(2)}M`;
    }
    return params;
  };

  const customBeforeRouteLeave = () => {
    onBeforeRouteLeave((to, from, next) => {
      const toParent = to?.meta?.parent ?? [];
      const formParent = from?.meta?.parent ?? [];
      const toPLen = toParent.length;
      const fromPLen = formParent.length;
      const isSameParent = toPLen > 0 && toPLen === fromPLen && toParent[toPLen - 1] === formParent[fromPLen - 1];
      isNeedInitForm.value = !isSameParent;
      // console.log('route--tostr', to.name);
      // console.log('route--to', to);
      // console.log('route--from', from);
      next();
    });
  };

  const reInit = async (isResetForm = ref(isNeedInitForm).value) => {
    await nextTick();
    const { activeCompetitorCode: activeCompetitorCodeRaw = '', startTime = '', endTime = '' }: any = router.currentRoute.value.query;
    activeCompetitorCode.value = activeCompetitorCodeRaw;

    showLoadingForm();
    showLoadingCreativeList();
    page.value = DEFAULT_PAGE;
    pageSize.value = DEFAULT_PAGE_SIZE;
    creativeList.value = [];
    if (isResetForm) {
      isListType.value = true;
      condition.cur = cloneDeep(DEFAULT_CONDITION);
      condition.default = cloneDeep(DEFAULT_CONDITION);
      if (startTime && endTime) {
        const timeRange = [dayjs(`${startTime} 00:00:00`), dayjs(`${endTime} 23:59:59`)];
        condition.cur.creative_time = timeRange;
        condition.default.creative_time = timeRange.slice(0);
      };
      changeGameType();
      if (isMobileSite()) {
        condition.cur.creative_type = ['2'];
        condition.cur.creative_time = [dayjs().subtract(1, 'year'), dayjs().subtract(1, 'day')];
      };
      orderBy.value.key = 'impression_number';
      if (isPCSite()) { // 移动端不请求竞品列表与标签列表；
        await getCompetitorList();
      };
    };
    hideLoadingForm();
    // 区分不同页面所运行的接口
    if (tabSelectId.value === OverviewTabType.CREATIVEGALLERY) {
      // Creative Gallery 页面
      await getCreativePage();
    } else {
      // Analyze 页面
    };
  };


  const init = async () => {
    useWatchGameChange(async ({
      isGameChange,
    }) => {
      const isResetForm = isGameChange ? true : undefined;
      reInit(isResetForm);
    });
  };
  // watch
  watch(tabSelectId, () => {
    // console.log('tabSelectId', tabSelectId.value);
    if (tabSelectId.value === OverviewTabType.CREATIVEGALLERY) {
      jumpOverviewCreativeGalleryPage();
    } else if (tabSelectId.value === OverviewTabType.ANALYZE) {
      jumpOverviewAnalyzePage();
    }
  });
  watch(orderBy, () => {
    initGetCreativePage();
  });
  watch(() => router.currentRoute.value.query, () => {
    init();
  });
  // watch(page, () => {
  //   console.log('page', page.value);
  //   getCreativePage();
  // });
  // watch(pageSize, () => {
  //   console.log('pageSize', pageSize.value);
  //   getCreativePage();
  // });
  // watch(orderBy, () => console.log(orderBy.value));
  // watch(isListType, () => console.log(isListType.value));

  return {
    test,
    tabList,
    tabSelectId,
    tabProps,
    condition,
    conditionList,
    isLoadingForm,
    isLoadingCreativeList,
    model1SelectedDropdownItem,
    model2SelectedDropdownItem,
    isListType,
    orderBy,
    metric,
    creativeList,
    page,
    pageSize,
    total,
    buttons,
    isLoadingCreativeDown,
    percentage,
    tableRef,
    displayColumns,
    tableAllColumns,
    columsSlot,
    isRequetGameOptions,
    resetFormFilter,
    getCompetitorList,
    getCreativePage,
    getPieChart,
    initGetCreativePage,
    setOrderBy,
    toggleIsListType,
    showListType,
    hideListType,
    showLoadingCreativeDown,
    hideLoadingCreativeDown,
    onSortChange,
    nameAxisLabelFormat,
    numberAxisLabelFormat,
    customBeforeRouteLeave,
    init,
  };
});
