import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import { getLastWeekRange, convertionOs } from '../competitor/untis';
import { h } from 'vue';
// import { defineAsyncComponent } from 'vue';
import { WORLD_CREATIVE, CHANNEL, LANGUAGE, PLATFORM, CREATIVETYPE, COMPETITORTYPE } from '../config/selectOptions.const';
import DateRangePicker from 'common/components/DateRangePicker';
// // import InputCascader from 'common/components/InputCascader';
import CountryCascader from 'common/components/CountryCascader';
import MultipleSelect from 'common/components/Select/container.vue';
// import Tinput from 'tdesign-vue-next/es/input';
import TrangeInput from 'tdesign-vue-next/es/range-input';
// import SvgIcon from 'common/components/SvgIcon';
import { PlayCircleIcon } from 'tdesign-icons-vue-next';
import { getChannelName, getChannelImg, getImgByCdn } from '@/store/intelligence/creative/competitor/untis';
import { dayRangeFormat, countryCheckAll } from './utis';
import components from '@/views/creative/common/components/components';
import { IFormParams } from './overview';
import { basePresets } from '@/views/creative/library/define';

// const componentMap = {
//   'date-range': defineAsyncComponent(() => import('common/components/DateRangePicker')),
//   'multiple-select': defineAsyncComponent(() => import('common/components/Select/container.vue')),
//   'country-cascader': defineAsyncComponent(() => import('common/components/CountryCascader')),
//   'search-box': defineAsyncComponent(() => import('common/components/SearchBox')),
//   't-input': defineAsyncComponent(() => import('tdesign-vue-next/es/input')),
//   't-select': defineAsyncComponent(() => import('tdesign-vue-next/es/select')),
//   't-date-picker': defineAsyncComponent(() => import('tdesign-vue-next/es/date-picker')),
// };
// dayjs(time.startStr)
const time = getLastWeekRange('YYYY-MM-DD');
export enum OverviewTabType {
  CREATIVEGALLERY = 'Creative Gallery',
  ANALYZE = 'Analyze',
}
// dayjs().subtract(30, 'day')
export const DEFAULT_CONDITION: IFormParams = {
  creative_time: [dayjs(time.startStr), dayjs().subtract(1, 'day')],
  competitor_type: COMPETITORTYPE.map(item => item.value),
  competitors: [],
  geo: [...countryCheckAll(cloneDeep(WORLD_CREATIVE))],
  channel: [...cloneDeep(CHANNEL).map(item => item.value)],
  os: PLATFORM.map(it => it.value),
  str_lang: [...cloneDeep(LANGUAGE).map(item => item.value)],
  creative_type: CREATIVETYPE.map(it => it.value),
  activeDayRange: ['', ''],
  // tag: [],
  // keyword: '',
};

export const DEFAULT_FILED_OBJ = {
  creative_time: [],
  competitor_type: COMPETITORTYPE,
  competitors: [],
  geo: cloneDeep(WORLD_CREATIVE),
  channel: cloneDeep(CHANNEL),
  os: PLATFORM,
  str_lang: cloneDeep(LANGUAGE),
  creative_type: CREATIVETYPE,
  activeDayRange: [],
  // tag: [],
  // keyword: '',
};
export const DEFAULT_CONDITIONLIST = [
  {
    // name: componentMap['date-range'],
    name: DateRangePicker,
    props: {
      date: [dayjs().subtract(1, 'day'), dayjs().subtract(1, 'day')],
      presets: basePresets,
    },
    ext: {
      key: 'creative_time',
      label: 'creativeTime',
      isAllowClose: false,
    },
  },
  {
    // name: componentMap['multiple-select'],
    name: components.CommonSelect,
    props: {
      list: [],
      title: 'Game Type',
      multiple: true,
      button: (textArr: string[] | string) => {
        if (textArr.length === 0) {
          return 'All';
        }
        if (!Array.isArray(textArr)) return textArr;
        return textArr.length > 1
          ? textArr.length
          : textArr
            .map((item: string) => {
              if (item.length > 8) {
                return  `${item.substring(0, 8)}...`;
              }
              return item;
            })
            .join(',');
      },
    },
    ext: {
      key: 'competitor_type',
      label: 'Game Type', // 包裹容器下拉列表中展示名称
      isAllowClose: false,
      // isHide: true,
    },
  },
  {
    // name: componentMap['multiple-select'],
    name: components.CommonSelect,
    props: {
      list: [],
      title: 'Games',
      multiple: true,
      button: (textArr: string[] | string) => {
        if (textArr.length === 0) {
          return 'All';
        }
        if (!Array.isArray(textArr)) return textArr;
        return textArr.length > 1
          ? textArr.length
          : textArr
            .map((item: string) => {
              if (item.length > 8) {
                return  `${item.substring(0, 8)}...`;
              }
              return item;
            })
            .join(',');
      },
    },
    ext: {
      key: 'competitors',
      label: 'Games', // 包裹容器下拉列表中展示名称
      isAllowClose: false,
      // isHide: true,
    },
  },
  {
    // name: componentMap['country-cascader'],
    name: CountryCascader,
    props: {
      labels: ['Region', 'Country/Market'],
      options: [
        // {
        //   label: '1',
        //   value: '1',
        //   children: [
        //     {
        //       label: '11',
        //       value: '11',
        //     },
        //     {
        //       label: '12',
        //       value: '12',
        //     },
        //   ],
        // },
        // {
        //   label: '2',
        //   value: '2',
        //   children: [
        //     {
        //       label: '21',
        //       value: '21',
        //     },
        //     {
        //       label: '22',
        //       value: '22',
        //     },
        //   ],
        // },
      ],
      title: 'Country/Market',
      outReset: true,
    },
    ext: {
      key: 'geo',
      label: 'Country/Market',
      isAllowClose: false,
      // isHide: true,
    },
  },
  {
    // name: componentMap['multiple-select'],
    name: MultipleSelect,
    props: {
      list: [
        // { label: 'Instagram', value: 1 },
        // { label: 'Audience Network', value: '2' },
      ],
      title: 'Media Source',
      multiple: true,
      button: (textArr: string[] | string) => {
        if (textArr.length === 0) {
          return 'All';
        }
        if (!Array.isArray(textArr)) return textArr;
        return textArr.length > 1 ? textArr.length : textArr.join(',');
      },
    },
    ext: {
      key: 'channel',
      label: 'Media Source', // 包裹容器下拉列表中展示名称
      isAllowClose: false,
      // isHide: true,
    },
  },
  {
    // name: componentMap['multiple-select'],
    name: MultipleSelect,
    props: {
      list: [],
      title: 'Platform',
      multiple: true,
      button: (textArr: string[] | string) => {
        if (textArr.length === 0) {
          return 'All';
        }
        if (!Array.isArray(textArr)) return textArr;
        return textArr.length > 1 ? textArr.length : textArr.join(',');
      },
    },
    ext: {
      key: 'os',
      label: 'Platform', // 包裹容器下拉列表中展示名称
      isAllowClose: false,
      // isHide: true,
    },
  },
  {
    // name: componentMap['multiple-select'],
    name: MultipleSelect,
    props: {
      list: [
        // { label: 'English', value: 'en' },
        // { label: 'Japanese', value: 'ja' },
      ],
      title: 'Language',
      multiple: true,
      button: (textArr: string[] | string) => {
        if (textArr.length === 0) {
          return 'All';
        }
        if (!Array.isArray(textArr)) return textArr;
        return textArr.length > 1 ? textArr.length : textArr.join(',').substring(0, 5);
      },
    },
    ext: {
      key: 'str_lang',
      label: 'Language', // 包裹容器下拉列表中展示名称
      isAllowClose: false,
      // isHide: true,
    },
  },
  {
    // name: componentMap['multiple-select'],
    name: MultipleSelect,
    props: {
      list: [
        // { label: 'Image', value: 1 },
        // { label: 'Video', value: 2 },
      ],
      title: 'Type',
      multiple: true,
      button: (textArr: string[] | string) => {
        if (textArr.length === 0) {
          return 'All';
        }
        if (!Array.isArray(textArr)) return textArr;
        return textArr.length > 1 ? textArr.length : textArr.join(',');
      },
    },
    ext: {
      key: 'creative_type',
      label: 'Type', // 包裹容器下拉列表中展示名称
      isAllowClose: false,
      // isHide: true,
    },
  },
  {
    name: TrangeInput,
    props: {
      autoWidth: true,
      class: 'w-[229px] h-[36px] zh-day-range',
      prefixIcon: h('div', { class: 'pl-[10px] text-[#747d98] ' }, 'Days Active'),
      size: 'medium',
      type: 'number',
      format: dayRangeFormat,
      placeholder: ['min', 'max'],
    },
    ext: {
      key: 'activeDayRange',
      label: 'Active Days',
      isAllowClose: false,
    },
  },
  // // Tags选项去除
  // {
  //   // name: componentMap['multiple-select'],
  //   name: MultipleSelect,
  //   props: {
  //     list: [
  //       { label: 'music_epic', value: 1 },
  //       { label: 'cat', value: 2 },
  //     ],
  //     title: 'Tags',
  //     multiple: true,
  //     button: (textArr: string[] | string) => {
  //       if (textArr.length === 0) {
  //         return 'All';
  //       }
  //       if (!Array.isArray(textArr)) return textArr;
  //       return textArr.length > 1 ? textArr.length : textArr.join(',');
  //     },
  //   },
  //   ext: {
  //     key: 'tag',
  //     label: 'Tages', // 包裹容器下拉列表中展示名称
  //     isAllowClose: true,
  //     // isHide: true,
  //   },
  // },
  // // 关键字搜索暂时不开放（搜索有性能问题）
  // {
  //   name: Tinput,
  //   props: {
  //     placeholder: 'Search',
  //     autoWidth: true,
  //     class: 'w-auto',
  //   },
  //   ext: {
  //     key: 'keyword',
  //     label: 'Search',
  //     isAllowClose: true,
  //   },
  // },
];

export const ORDER_BY_OPTIONS = [
  {
    value: 'impression_number',
    label: 'Impression',
  },
  {
    value: 'days',
    label: 'Days Active',
  },
  {
    value: 'heat',
    label: 'Popular',
  },
  {
    value: 'countries_number',
    label: 'Country',
  },
  {
    value: 'channel',
    label: 'Channel',
  },
];

export const METRICE_OPTIONS = [
  {
    value: 'impression',
    label: 'Impression',
  },
  {
    value: 'days',
    label: 'Days Active',
  },
  {
    value: 'popular',
    label: 'Popular',
  },
  {
    value: 'countries',
    label: 'Country',
  },
  {
    value: 'channel',
    label: 'Channel',
  },
  {
    value: 'interaction',
    label: 'Interaction',
  },
  {
    value: 'conversion',
    label: 'Conversion',
  },
  {
    value: 'tags',
    label: 'Tag',
  },
  {
    value: 'first_seen',
    label: 'First_seen',
  },
  {
    value: 'last_seen',
    label: 'Last_seen',
  },
];

export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 50;

export const DEFAULT_METRICS = [
  'id',
  'game',
  'resource',
  'impression',
  'days',
  'popular',
  'countries',
  'channel',
  'interaction',
  'conversion',
  'tags',
  'first_seen',
  'last_seen',
];

export const DEFAULT_TABLE_COLUMNS = [
  { colKey: 'id', title: '#', width: 70, fixed: 'left', sorter: true, required: true },
  {
    colKey: 'game',
    title: 'Game',
    with: '200px',
    fixed: 'left',
    required: true,
    cell: (_h: any, { row }: any) => _h('div', { class: `flex items-center ${0}` }, [
      _h('img', { src: `${row.app_logo}`, class: `w-[36px] h-[36px] rounded-[5px] ${0}` }),
      _h(
        'div',
        { class: `ml-[5px] max-w-[130px] overflow-hidden overflow-ellipsis whitespace-nowrap ${0}` },
        `${row.app_name}`,
      ),
    ]),
  },
  {
    colKey: 'competitor_type',
    title: 'Game Type',
    with: '200px',
    fixed: 'left',
    required: true,
    // cell: (_h: any, { row }: any) => _h('div', { class: `flex items-center ${0}` }, [
    //   _h('img', { src: `${row.app_logo}`, class: `w-[36px] h-[36px] rounded-[5px] ${0}` }),
    //   _h(
    //     'div',
    //     { class: `ml-[5px] max-w-[130px] overflow-hidden overflow-ellipsis whitespace-nowrap ${0}` },
    //     `${row.app_name}`,
    //   ),
    // ]),
  },
  {
    colKey: 'resource',
    title: 'Creative',
    fixed: 'left',
    required: true,
    cell: (_h: any, { row }: any) => _h(
      'div',
      { class: `w-[36px] h-[36px] bg-background flex justify-center items-center relative cursor-pointer ${0}` },
      [
        _h('img', { src: `${row.preview_img}`, class: `max-w-[36px] max-h-[36px] ${0}` }),
        row.type === 2
          ? _h(PlayCircleIcon, {
            name: 'play-btn',
            color: '#747D98FF',
            class: `absolute bottom-[-7px] right-[-7px] ${0}`,
          })
          : '',
      ],
    ),
  },
  {
    colKey: 'countries',
    title: 'Country/Market',
    sorter: true,
    cell: (_h: any, { row }: any) => (row.countries.length > 0 ? row.countries.length : 'None'),
  },
  {
    colKey: 'channel',
    title: 'Media Source',
    sorter: true,
    cell: (_h: any, { row }: any) => _h('div', { class: `flex items-center ${0}` }, [
      _h('img', { src: getImgByCdn(getChannelImg(row.channel)), class: `w-[30px] h-[30px] ${0}` }),
      _h(
        'div',
        { class: `ml-[5px] max-w-[90px] overflow-hidden overflow-ellipsis whitespace-nowrap ${0}` },
        `${getChannelName(row.channel)}`,
      ),
    ]),
  },

  {
    colKey: 'os',
    title: 'Platform',
    cell: (_h: any, { row }: any) => convertionOs(row.os),
  },
  { colKey: 'impression', title: 'Impression', sorter: true },
  { colKey: 'like_count', title: 'Likes' },
  { colKey: 'comment_count', title: 'Comments' },
  { colKey: 'share_count', title: 'Shares' },
  { colKey: 'days', title: 'Days Active', sorter: true },
  // { colKey: 'popular', title: 'Popular', sorter: true },
  // { colKey: 'interaction', title: 'Interaction' },
  // { colKey: 'conversion', title: 'Conversion' },
  // { colKey: 'tags', title: 'tag', cell: (_h: any, { row }: any) => colRenderTags(_h, row), width: '300px' },
  {
    colKey: 'first_seen',
    title: 'First Seen',
    cell: (_h: any, { row }: any) => dayjs(row.first_seen).format('YYYY-MM-DD'),
  },
  {
    colKey: 'last_seen',
    title: 'Last Seen',
    cell: (_h: any, { row }: any) => dayjs(row.last_seen).format('YYYY-MM-DD'),
  },
];
