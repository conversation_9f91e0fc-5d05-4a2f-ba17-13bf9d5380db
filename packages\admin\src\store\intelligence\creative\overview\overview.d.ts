import { type Dayjs } from 'dayjs';
import { CommonOptionsItemType } from '@@/index';
import { OptionsItem } from 'common/components/Cascader';


export interface IFormParams {
  creative_time: Dayjs[],
  competitor_type: Array<string>,
  competitors: Array<string>,
  geo: Array<string>,
  channel: Array<string>,
  os: Array<string>,
  str_lang: Array<string>,
  creative_type: Array<string>,
  activeDayRange: Array<string>,
  // tag: Array<string>,
  // keyword: string,
};

export interface IOverviewStore {
  test: string,
  tabList: Array<ICustomViewItem>,
  tabSelectId: string,
  condition: Object,
  conditionList: Object,
};

export interface IFormOptions {
  fieldObj: {
    creative_time?: Array<any>,
    competitors?: Array<CommonOptionsItemType>,
    geo?: Array<OptionsItem>,
    channel?: Array<CommonOptionsItemType>,
    os?: Array<CommonOptionsItemType>,
    str_lang?: Array<CommonOptionsItemType>,
    creative_type?: Array<CommonOptionsItemType>,
    // tag: Array<CommonOptionsItemType>,
    // keyword: string,
  },
  conditionList: any[],
}

export interface ICreativeParam {
  creative_time: string,
  // competitors: Array<string>,
  geo: string,
  channel: string,
  os: string,
  str_lang: string,
  creative_type: string,
  daysMin: string,
  daysMax: string,
  // tag: string,
  store_id: string,
  page: string,
  page_size: string,
  order_by: string,
  // keyword: string,
}
export interface IOrderBy {
  key: string, // 排序字段
  rule: string, // 排序规则
}

export interface ICommonViewItem {
  id: string,
  title: string,
  icon?: string,
}

// 后端返回的值

export interface ITagItem {
  id: number,
  name_en: string,
};
export interface IResultPage<T> {
  list: T[],
  total: number,
};

// const test = () => {
//   new Promise((resolve) => {
//     console.log('test');
//     resolve({ status: 1 });
//   })
// }
