import { ICreativeItem, type ICompetitor } from '../competitor/competitor';
import Tooltip from 'tdesign-vue-next/es/tooltip';
import { useBase } from 'common/service/backendApi/base';
import { NotifyPlugin } from 'tdesign-vue-next';
import dayjs from 'dayjs';
import { convertionCountry, convertionOs, getChannelName } from '../competitor/untis';
import { COMPETITORTYPE_MAP } from '../config/selectOptions.const';
const { post } = useBase();
export const converFormGameOption = (list: Array<ICompetitor>) => list.map(item => ({
  label: item.competitor_name,
  value: item.competitor_code,
  icon: item.competitor_type,
}));
export const colRenderTags = (_h: any, row: any) => {
  const { tags } = row;
  if (tags.length === 0 || !tags) {
    return _h('div', {}, '');
  }
  if (tags.length > 0) {
    return _h('div', { class: 'col-tooltip-box' }, [
      _h(
        Tooltip,
        {
          placement: 'bottom-left',
          class: 'max-w-[600px] bg-[rgb(36, 36, 36)]',
        },
        {
          default: () => _h('div', { class: `flex items-center ${0}` }, [
            ...tags.slice(0, 3).map((item: any) => _h(
              'div',
              {
                class: `bg-[#F1F1F6] w-[100px] h-[20px]  text-[12px] rounded-[5px]
          overflow-hidden overflow-ellipsis whitespace-nowrap pl-[3px] pr-[3px]
          ml-[5px] mr-[5px] bg-[#f2f3f5] leading-[20px] text-center ${0}`,
              },
              `${item}`,
            )),
            tags.length >= 3 ? _h('div', { class: 'text-[#619bff] ' }, [`...(${tags.length})`]) : '',
          ]),
          content: () => _h('div', { class: `grid gap-y-4 gap-x-4 grid-cols-5 p-[10px] max-w-[700px] ${0}` }, [
            ...tags.map((item: any) => _h('div', { class: `w-[100px] overflow-hidden overflow-ellipsis whitespace-nowrap ${0}` }, `${item}`)),
          ]),
        },
      ),
    ]);
  }
  return '';
};
export const colRenderCountry = (_h: any, row: any) => {
  const { countries } = row;
  if (countries.length === 0 || !countries) {
    return _h('div', {}, '');
  };
  if (countries.length > 0) {
    return _h('div', { class: 'col-tooltip-box' }, [
      _h(
        Tooltip,
        {
          placement: 'bottom-left',
          class: 'max-w-[600px] bg-[rgb(36, 36, 36)]',
        },
        {
          default: () => _h('div', { class: `flex items-center ${0}` }, [
            ...countries.slice(0, 3).map((item: any) => _h(
              'div',
              {
                class: `bg-[#F1F1F6] w-[100px] h-[20px]  text-[12px] rounded-[5px]
          overflow-hidden overflow-ellipsis whitespace-nowrap pl-[3px] pr-[3px]
          ml-[5px] mr-[5px] bg-[#f2f3f5] leading-[20px] text-center ${0}`,
              },
              `${item}`,
            )),
            countries.length >= 3 ? _h('div', { class: 'text-[#619bff] ' }, [`...(${countries.length})`]) : '',
          ]),
          content: () => _h('div', { class: `grid gap-y-4 gap-x-4 grid-cols-5 p-[10px] max-w-[700px] ${0}` }, [
            ...countries.map((item: any) => _h('div', { class: `w-[100px] overflow-hidden overflow-ellipsis whitespace-nowrap ${0}` }, `${item}`)),
          ]),
        },
      ),
    ]);
  }
  return '';
};

export const dayRangeFormat = (val: string) => {
  // console.log('val', val, typeof val);
  let result: any = '';
  if (typeof val === 'string') {
    const arr = val.split('');
    const res = arr.filter((item: string) => {
      const numReg = /^\d+$/;
      return numReg.test(item);
    });
    if (res.length > 0) {
      const resStr = res.join('');
      const resNum = parseInt(resStr, 10);
      if (resNum > 0) {
        result = `${resNum}`;
      }
    }
  }
  return result;
};

function notificationError() {
  // 有一条数据下载失败的
  NotifyPlugin.warning({
    title: 'Download failed', // 下载失败
    content: 'Some page data failed to download, please try again or try narrowing the filter range.', // 部分分页数据下载失败，请重试，或者尝试缩小筛选范围
    duration: 0, // 不会自动消失
  });
}
/**
 *
 * @param api  请求地址
 * @param searchData 请求参数
 * @param total 请求的总数
 * @param dpageSize 每一页的数量
 * @param promiseCount 一次并发请求的数量
 * @param callback 一次发起请求后的回调
 * @param isNotification 是否需要提示
 * @param isMaxTotal 是否有最大下载条数限制
 * @param maxTotal 是否需要提示
 * @returns
 */
export async function downDataBypage(
  api: string,
  searchData: any,
  total: number,
  dpageSize = 50,
  promiseCount = 3,
  callback: Function,
  isNotification = true,
  isMaxTotal = true,
  maxTotal = 1000,
) {
  // e1
  let downloadAllData: any = [];
  let requestCount = 0;
  const practicalTotal = isMaxTotal ? total > maxTotal ? maxTotal : total : total; // 实际要下载条目数
  const page: number = Math.ceil(practicalTotal / dpageSize);
  if (page >= promiseCount && isNotification && (total < maxTotal)) {
    NotifyPlugin.info({
      title: 'Downloading', // 下载中
      content: 'Data is being prepared, please wait.', // 数据准备中，请等待
    });
  }
  if (page >= promiseCount && isNotification && (total > maxTotal)) {
    NotifyPlugin.info({
      title: 'Downloading', // 下载中
      content: 'The maximum number of downloads per time cannot exceed 1000.', // 下载超过1000的提示文案
    });
  }
  if (total < dpageSize) {
    // 只有一页
    const listData: any = {
      ...searchData,
      page: 1,
      page_size: dpageSize,
    };
    const { list }: any = await post(api, { ...listData });
    if (!list) {
      notificationError();
    }
    downloadAllData = downloadAllData.concat(list);
    requestCount += 1;
    callback((requestCount / page) * 100);
  } else {
    // e2
    if (page < promiseCount) {
      const queueList: any = [];
      for (let i = 1; i <= page; i++) {
        const listData: any = {
          ...searchData,
          page: i,
          page_size: dpageSize,
        };
        queueList.push(new Promise((resolve) => {
          post(api, { ...listData })
            .catch((e: any) => {
              console.log('error', e);
              resolve({ status: 0, data: [] });
            })
            .then((res: any) => {
              const { list } = res;
              if (list) {
                resolve({ status: 0, data: list });
              }
            });
        }));
      }
      const result = await Promise.all(queueList);
      result.forEach((item) => {
        if (item.status === 0) {
          downloadAllData = downloadAllData.concat(item.data);
        }
        requestCount += 1;
        callback((requestCount / page) * 100);
      });
    } else {
      // e3
      const pagePromiseCount = Math.floor(page / promiseCount); // 能发多少完整promiseCount
      for (let i = 1; i < pagePromiseCount * promiseCount; i += promiseCount) {
        // 完整promiseCount请求
        const queueList2: Array<any> = [];
        for (let j = i; j <= i + (promiseCount - 1); j++) {
          const listData = {
            ...searchData,
            page: j,
            page_size: dpageSize,
          };
          queueList2.push(new Promise((resolve) => {
            post(api, { ...listData })
              .catch((e: any) => {
                console.log('error', e);
                notificationError();
                resolve({ status: 1, data: [] });
              })
              .then((res: any) => {
                const { list } = res;
                if (list) {
                  resolve({ status: 0, data: list });
                } else {
                  notificationError();
                  resolve({ status: 1, data: list });
                }
              });
          }));
        }
        const result3: Array<any> = await Promise.all(queueList2);
        for (const item of result3) {
          if (item.status === 0) {
            downloadAllData = downloadAllData.concat(item.data);
          }
          requestCount += 1;
          callback((requestCount / page) * 100);
        }
      }
      // 剩余尾巴请求
      const queueList: Array<any> = [];
      for (let i = pagePromiseCount * promiseCount + 1; i <= page; i++) {
        const listData = {
          ...searchData,
          page: i,
          page_size: dpageSize,
        };
        queueList.push(new Promise((resolve: any) => {
          post(api, { ...listData })
            .catch((e: any) => {
              console.log('error', e);
              notificationError();
              resolve({ status: 1, data: [] });
            })
            .then((res: any) => {
              const { list } = res;
              if (list) {
                resolve({ status: 0, data: list });
              } else {
                notificationError();
                resolve({ status: 1, data: list });
              }
            });
        }));
      }
      const result = await Promise.all(queueList);
      result.forEach((item) => {
        if (item.status === 0) {
          downloadAllData = downloadAllData.concat(item.data);
        }
        requestCount += 1;
        callback((requestCount / page) * 100);
      });
    } // e3
  } // e2
  return downloadAllData;
} // e1

export const creativeListAttaCompetitorType = (
  list: Array<ICreativeItem>,
  storeIdCompetitor: Record<string, ICompetitor>,
): Array<ICreativeItem> => list.map((item: ICreativeItem) => {
  const tempItem = { ...item };
  tempItem.competitor_type = COMPETITORTYPE_MAP[storeIdCompetitor[item.store_id]?.competitor_type ?? ''] ?? '';
  return tempItem;
});

export const toCreativeXlsxData = (list: Array<ICreativeItem>, storeIdCompetitor: Record<string, ICompetitor>) => {
  const tempList = creativeListAttaCompetitorType(list, storeIdCompetitor);
  const resList = tempList.map(item => ({
    Game: item.app_name,
    'Game Type': item.competitor_type,
    Creative: item.resources[0],
    'Country/Market': convertionCountry(item.countries, false)
      .map(item => (item.fullName))
      .join(','),
    'Media Source': getChannelName(item.channel),
    Platform: convertionOs(item.os),
    Impression: item.impression,
    Likes: item.like_count,
    Comments: item.comment_count,
    Shares: item.share_count,
    'Days Active': item.days,
    // popular: item.heat,
    // interaction: item.interaction,
    // conversion: item.conversion,
    // tags: item.tags.join(','),
    'First Seen': dayjs(item.first_seen).format('YYYY.MM.DD'),
    'Last Seen': dayjs(item.last_seen).format('YYYY.MM.DD'),
  }));
  return resList;
};


export const retBtn = (key: string, list: Array<any>): Function => {
  if (key === 'competitors') {
    const button = (textArr: string[] | string) => {
      if (textArr.length === 0 || textArr.length === list.length) {
        return 'All';
      }
      if (!Array.isArray(textArr)) return textArr;
      return textArr.length > 1
        ? textArr.length
        : textArr
          .map((item: string) => {
            if (item && item.length > 8) {
              return  `${item.substring(0, 8)}...`;
            }
            return item;
          })
          .join(',');
    };
    return button;
  };
  const button = (textArr: string[] | string) => {
    if (textArr.length === 0 || textArr.length === list.length) {
      return 'All';
    };
    if (!Array.isArray(textArr)) return textArr;
    return textArr.length > 1 ? textArr.length : textArr.join(',');
  };
  return button;
};

export const countryCheckAll = (list: Array<any>) => {
  const checkAll: Array<any> = [];
  list.forEach((item: any) => {
    if (item.children && item.children.length > 0) {
      item.children.forEach((child: any) => {
        checkAll.push(child.value);
      });
    }
  });
  return checkAll;
};


