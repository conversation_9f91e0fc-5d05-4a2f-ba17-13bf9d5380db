import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { ref, computed, reactive, nextTick, unref } from 'vue';
import { IFormOptions, IFormParams } from '.';
import { DEFAULT_CONDITION, DEFAULT_FILED_OBJ, DEFAULT_CONDITIONLIST, DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from './index.const';
import { retBtn } from 'common/utils/common';
import { cloneDeep } from 'lodash-es';
import { useLoading } from 'common/compose/loading';
import { useWatchGameChange } from 'common/compose/request/game';
import { ICreativeItem } from '../competitor/competitor';
import { getSocialMediaVideoPageService } from 'common/service/intelligence/creative/get-databrain-social-media-video-page';
import { smvListToCreativeList, childStrSplitToArrayMergeAll } from '../common/util';
import { getStartOfAndEndOfRecord, isMoreThanMaxTimeRange, moreThanAYearAgoNotifyPlugin } from './untils';
export const useSocialMediaVideoStore = defineStore(STORE_KEY.INTELLIGENCE.CREATIVE.SOCIAL_MEDIA_VIDEO, () => {
  // --------- from ----------
  const formOptions = ref<IFormOptions>({
    fieldObj: DEFAULT_FILED_OBJ,
    conditionList: DEFAULT_CONDITIONLIST,
  });
  const getConditionList = ({ src, fieldObj }: { src: any[]; fieldObj: any }) => src.map((item) => {
    const { props = {}, ext: { key = '' } = {} } = item;
    let newProps = props;
    const list = (fieldObj as any)[key];
    switch (key) {
      case 'time_range':
      case 'channel':
        newProps = { ...props, list };
        newProps.button = retBtn(key, list);
        break;
    };
    const res = { ...item, props: newProps };
    return res;
  });
  const condition = reactive<{ cur: IFormParams; default: IFormParams }>({
    cur: cloneDeep(DEFAULT_CONDITION),
    default: cloneDeep(DEFAULT_CONDITION),
  });
  const conditionList = computed(() => getConditionList({
    fieldObj: formOptions.value.fieldObj,
    src: formOptions.value.conditionList,
  }));
  // --------- 列表数据 ----------
  const creativeList = ref<Array<ICreativeItem>>([]); // 列表数据
  const page = ref<number>(DEFAULT_PAGE);
  const pageSize = ref<number>(DEFAULT_PAGE_SIZE);
  const total = ref<number>(0);
  const {
    isLoading: isLoadingList,
    showLoading: showLoadingList,
    hideLoading: hideLoadingList,
  } = useLoading(false);
  const startAndEndTimeRecord = computed(() => {
    const { cur } = condition;
    return getStartOfAndEndOfRecord(cur.time_range[0], cur.time_range[1]);
  });
  const getSocialMediaVideoPage = async () => {
    const { cur, default: defaultRaw } = condition;
    const { startTime, endTime } = unref(startAndEndTimeRecord);
    // 选择的时间范围是否超过一年
    if (isMoreThanMaxTimeRange(startTime, endTime)) {
      moreThanAYearAgoNotifyPlugin();
      return;
    };

    showLoadingList();
    const channelRaw = cur.channel.length !== defaultRaw.channel.length ? cur.channel : defaultRaw.channel;
    const selectChannleList = childStrSplitToArrayMergeAll(channelRaw);

    const res = await getSocialMediaVideoPageService({
      search_text: cur.search_text?.trim() || '',
      // start_time: '2023-10-01 00:00:00',
      // end_time: '2023-12-11 23:59:59',
      start_time: startTime,
      end_time: endTime,
      channels: selectChannleList,
      sort_item: 'view', // 排序字段 目前写死
      sort_model: 'desc', // 排序类型 写死
      page: page.value,
      page_size: pageSize.value,
    }) || { list: [], total: 0 };
    creativeList.value = smvListToCreativeList(res.list ?? []);
    total.value = res.total ?? 0;
    hideLoadingList();
  };
  const resetFormFilter = async () => {
    resetData();
    condition.cur.search_text = condition.default.search_text;
    condition.cur.time_range = [...condition.default.time_range];
    condition.cur.channel = [...condition.default.channel];
    nextTick(() => {
      getSocialMediaVideoPage();
    });
  };
  const resetData = () => {
    const { startTime, endTime } = unref(startAndEndTimeRecord);
    if (isMoreThanMaxTimeRange(startTime, endTime)) return;
    creativeList.value = [];
    page.value = DEFAULT_PAGE;
    pageSize.value = DEFAULT_PAGE_SIZE;
  };
  const init = async () =>  {
    useWatchGameChange(async () => {
      resetData();
      await getSocialMediaVideoPage(); // 社媒视频数据
    });
  };
  return {
    formOptions,
    condition,
    conditionList,
    creativeList,
    page,
    pageSize,
    total,
    isLoadingList,
    startAndEndTimeRecord,
    getSocialMediaVideoPage,
    resetFormFilter,
    resetData,
    init,
  };
});
