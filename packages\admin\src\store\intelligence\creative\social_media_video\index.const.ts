import DateTimePicker from 'common/components/DateTimePicker';
import { h } from 'vue';
import MultipleSelect from 'common/components/Select/container.vue';
import Tinput from 'tdesign-vue-next/es/input';
import dayjs from 'dayjs';
import { IFormParams } from '.';
import { getLastWeekRange } from '../competitor/untis';
import { DATABRAIN_CHANNEL_OPTIONS } from '../config/selectOptions.const';
import SvgIcon from 'common/components/SvgIcon';
import { getStartOfAndEndOfRecord, isMoreThanMaxTimeRange, moreThanAYearAgoNotifyPlugin } from './untils';

const time = getLastWeekRange('YYYY-MM-DD');

export const DEFAULT_CONDITION: IFormParams = {
  search_text: '',
  time_range: [dayjs(time.startStr), dayjs().subtract(1, 'day')],
  channel: DATABRAIN_CHANNEL_OPTIONS.map(item => item.value),
};

export const DEFAULT_FILED_OBJ = {
  search_text: '', // 关键字
  time_range: [], // 时间范围
  channel: DATABRAIN_CHANNEL_OPTIONS,
};

export const DEFAULT_CONDITIONLIST = [
  {
    name: Tinput,
    props: {
      placeholder: 'Search',
      clearable: true,
      prefixIcon: (): any => h(SvgIcon as any, { size: '14px', name: 'search', color: 'rgba(0,0,0,.6)' }),
    },
    ext: {
      key: 'search_text',
      label: 'Search',
      isAllowClose: false,
    },
  },
  {
    name: DateTimePicker,
    props: {
      date: [dayjs().subtract(1, 'day'), dayjs().subtract(1, 'day')],
      presets: {
        Today: [dayjs().toDate(), dayjs().toDate()],
        'Last Day': [dayjs().subtract(1, 'day')
          .toDate(), dayjs().subtract(1, 'day')
          .toDate()],
        'Last 7 days': [dayjs().subtract(6, 'day')
          .toDate(), dayjs().toDate()],
        'Last 14 days': [dayjs().subtract(13, 'day')
          .toDate(), dayjs().toDate()],
        'Last 30 days': [dayjs().subtract(29, 'day')
          .toDate(), dayjs().toDate()],
        'This month': [dayjs().startOf('month')
          .toDate(), dayjs().toDate()],
        'Last month': [dayjs().add(-1, 'month')
          .startOf('month')
          .toDate(), dayjs().add(-1, 'month')
          .endOf('month')
          .toDate()],
      },
      isShowPopupHeader: true,
      popupHeaderText: 'Select the start and end time for the query, which should not exceed one month at most.',
      onChange: (value: string[]) => {
        const { startTime, endTime } = getStartOfAndEndOfRecord(value[0], value[1]);
        // 选择的时间范围是否超过一年
        if (isMoreThanMaxTimeRange(startTime, endTime)) {
          moreThanAYearAgoNotifyPlugin();
          return;
        };
      },
    },
    ext: {
      key: 'time_range',
      label: 'timeRange',
      isAllowClose: false,
    },
  },
  {
    name: MultipleSelect,
    props: {
      list: [],
      title: 'Media Source',
      multiple: true,
      button: (textArr: string[] | string) => {
        if (textArr.length === 0) {
          return 'All';
        }
        if (!Array.isArray(textArr)) return textArr;
        return textArr.length > 1 ? textArr.length : textArr.join(',');
      },
    },
    ext: {
      key: 'channel',
      label: 'Media Source', // 包裹容器下拉列表中展示名称
      isAllowClose: true,
      // isHide: true,
    },
  },
];

export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 20;

