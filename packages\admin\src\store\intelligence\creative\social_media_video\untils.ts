import dayjs from 'dayjs';
import { NotifyPlugin } from 'tdesign-vue-next';

export const ONE_MONTH_SECONDS = 60 * 60 * 24 * 31; // 一个月秒值(按照31day计算)

/**
 * 判断选择的时间是否超过最大时间范围
 * @param start 开始时间
 * @param end 结束时间
 * @param maxTimeRange 最大时间范围（单位秒）
 * @returns Boolean
 */
export const isMoreThanMaxTimeRange = (
  start: string | Date,
  end: string | Date,
  maxTimeRange: number = ONE_MONTH_SECONDS,
) => {
  // const oneYear = 60 * 60 * 24 * 365;
  const startUnix = dayjs(start).unix();
  const endUnix = dayjs(end).unix();
  const isMoreThanAYear = (endUnix - startUnix) > maxTimeRange;
  return isMoreThanAYear;
};

export const moreThanAYearAgoNotifyPlugin = (title = 'Choose the wrong time', content = 'The selected time range is more than one month.') => {
  NotifyPlugin.warning({
    title,
    content,
  });
};

export const getStartOfAndEndOfRecord = (start: string | Date, end: string | Date, format = 'YYYY-MM-DD HH:mm:ss') => {
  const startTime = `${dayjs(start).startOf('day')
    .format(format)}`;
  const endTime = `${dayjs(end).endOf('day')
    .format(format)}`;
  return {
    startTime,
    endTime,
  };
};
