import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useWatchGameChange } from 'common/compose/request/game';
import { useLoading } from 'common/compose/loading';
import { reactive, ref } from 'vue';
// modal && type
import { COUNTRY_CHANNEL_METRIC, INIT_CON_OBJ } from './channel.d';
import { useIntelligenceCommonStore } from '../../common.store';
import { catGen, date } from '../const';
import { countryMapModification, inStringCond, toOption } from '../common';
import { WhereModal } from '../common.d';
// service
import { CategoryResponse, DateResponse, GetCountryConditionRequestModal, GetCountryConditionResponseModal, GetCountryResponseModal, GetFilterRequestModal } from 'common/service/intelligence/common/common.d';
import { getAllCountry } from 'common/service/intelligence/common/common';
import { getChannelCountry, getCountryPie, getFilter, getRegionPie } from 'common/service/intelligence/market/channel/channel';
import { OptionsItem } from 'common/types/cascader';
import { GetPieResponseModal } from 'common/service/intelligence/market/channel/channel.d';

export const useIntelligenceMarketChannelStore = defineStore(
  STORE_KEY.INTELLIGENCE.MARKET.CHANNEL,
  () => {
    const { isLoading, hideLoading, showLoading } = useLoading();
    const gameStore = useGlobalGameStore();
    const commonStore = useIntelligenceCommonStore();
    const pieLoading = ref(false);
    const payload = reactive<{
      countryList: GetCountryResponseModal['default'],
      conditionCountry: GetCountryResponseModal['default'],
      regionPieData: any[],
      countryFilterInput: string,
      countryFilterSelection: OptionsItem[],
      countryPieData: any[],
      option_obj: {
        categoryList: any[],
        regionList: any[],
        dateList: any[]
      }, // 查询相关的列表
      dateList: {
        label: string;
        value: string;
      }[],
      initConditionObj: {
        regionInput: string;
        countryInput: string;
        categoryInput: string;
        dateInput: string;
      },
      conditionObj: {
        regionInput: string;
        countryInput: string;
        categoryInput: string;
        dateInput: string;
      },
      isAdmin: boolean;
    }>({
      countryList: [],
      conditionCountry: [],
      regionPieData: [],
      countryPieData: [],
      countryFilterInput: '',
      countryFilterSelection: [],
      option_obj: {
        categoryList: [],
        regionList: [],
        dateList: [],
      },
      dateList: [],
      initConditionObj: {
        regionInput: '',
        countryInput: '',
        categoryInput: '',
        dateInput: '',
      },
      conditionObj: {
        regionInput: '',
        countryInput: '',
        categoryInput: '',
        dateInput: '',
      },
      isAdmin: false,
    });

    async function getCountry() {
      const data = await getAllCountry({ game: gameStore.gameCode });
      return data.default;
    }

    async function getMarketChannelCountry(metric: GetCountryConditionRequestModal) {
      const data = await getChannelCountry(metric);
      return data.default;
    }

    async function getMarketChannelPieData(where: WhereModal, type: 'region' | 'country') {
      const gen = {
        where,
        order: [{ order: 'DESC', by: 'overall_percentage' }],
      };
      let data;
      if (type === 'region') {
        data = await getRegionPie({ gen });
      } else data = await getCountryPie({ gen });
      return data.default;
    }

    async function getMarketChannelFilter<T>(metric: GetFilterRequestModal) {
      const data = await getFilter<T>(metric);
      return data.default;
    }

    // init
    const init = async () => {
      useWatchGameChange(async () => {
        try {
          showLoading();
          // 拿取过滤选择器的数据
          commonStore.init();
          const [categoryList, dateList, list, conditionCountry]:
          [CategoryResponse['default'], DateResponse['default'], GetCountryResponseModal['default'], GetCountryConditionResponseModal['default']] = await Promise.all([
            getMarketChannelFilter<'category'>(catGen),
            getMarketChannelFilter<'date'>(date),
            getCountry(),
            getMarketChannelCountry(COUNTRY_CHANNEL_METRIC),
          ]);
          const modifyCategoryList = categoryList.map((e) => {
            const value = e.category;
            return { label: value.slice(0, 1).toLocaleUpperCase() + value.slice(1), value };
          });

          const modifyDateList = toOption(dateList, 'date').map(({ label = '', value = '' }) => ({
            label: label.toString().slice(0, 6),
            value: value.toString(),
          }));

          const modifyConditionCountry = countryMapModification(list, conditionCountry);

          // 获取区域的 options 并去重
          const regionObj: { [key: string]: boolean } = {};
          const regionList = modifyConditionCountry.map(one => ({
            label: one.region_en,
            value: one.region_abbre,
          })).reduce((prev: { label: string, value: string }[], cur) => {
            if (!regionObj[cur.value]) {
              regionObj[cur.value] = true;
              prev.push(cur);
            }
            return prev;
          }, []);


          // 默认选择区域的第一个来生成国家的 options
          const countryList = modifyConditionCountry
            .filter(one => regionList[0]?.value === one.region_abbre)
            .map(one => ({
              label: one.country_en,
              value: one.country_abbre,
            }));

          // 拿取饼图数据
          const regionConfig = {
            region: regionList[0].value,
            country: countryList.map(one => one.value),
            category: modifyCategoryList[0].value,
            date: modifyDateList[0].value,
          };
          const countryConfig = {
            country: countryList[0].value,
            category: modifyCategoryList[0].value,
            date: modifyDateList[0].value,
          };

          // 设定饼图选择器
          payload.countryFilterInput = countryList[0].value;
          payload.countryFilterSelection = countryList.map(item => (
            { label: item.label, value: item.value }));

          const where = Object.keys(regionConfig).map(k => inStringCond(
            k,
            regionConfig[k as keyof typeof regionConfig],
          ));
          const countryWhere = Object.keys(countryConfig).map(k => inStringCond(
            k,
            countryConfig[k as keyof typeof countryConfig],
          ));

          const [regionPieData, countryPieData] = await Promise.all([
            getMarketChannelPieData(where, 'region'),
            getMarketChannelPieData(countryWhere, 'country'),
          ]);

          // 排列数据成展示5个，其他变去Others
          const modifyRegionPieData = reducePieDataColumn(list, regionPieData);
          const modifyCountryPieData = reducePieDataColumn(list, countryPieData);

          const conditionObj = {
            ...INIT_CON_OBJ,
            regionInput: regionList[0].value,
            countryInput: countryList[0].value,
            dateInput: modifyDateList[0].value,
            categoryInput: modifyCategoryList[0].value,
          };

          payload.countryList = list;
          payload.option_obj = {
            categoryList: modifyCategoryList,
            regionList,
            dateList: modifyDateList,
          };
          payload.conditionCountry = modifyConditionCountry;
          payload.regionPieData = modifyRegionPieData;
          payload.countryPieData = modifyCountryPieData;
          payload.dateList = modifyDateList;
          payload.isAdmin = commonStore.isAdmin ?? false;
          payload.initConditionObj = { ...conditionObj };
          payload.conditionObj = { ...conditionObj };
        } catch (error) {
          // Handle errors here
        } finally {
          hideLoading();
        }
      });
    };

    // get Filter Data
    const getFilterData = async (
      category: string, date: string,
      region: string,
    ) => {
      showLoading();
      // 默认选择区域的第一个来生成国家的option
      const countryList = payload.conditionCountry.filter(one => region === one.region_abbre).map(one => ({
        label: one.country_en,
        value: one.country_abbre,
      }));

      const regionConfig = {
        region,
        country: countryList.map(one => one.value),
        category,
        date,
      };
      const countryConfig = {
        country: countryList[0].value,
        category,
        date,
      };

      // 设定饼图选择器
      payload.countryFilterInput = countryList[0].value;
      payload.countryFilterSelection = countryList.map(item => ({ label: item.label, value: item.value }));

      // eslint-disable-next-line max-len
      const where = Object.keys(regionConfig).map(k => inStringCond(k, regionConfig[k as keyof typeof regionConfig]));
      // eslint-disable-next-line max-len
      const countryWhere = Object.keys(countryConfig).map(k => inStringCond(k, countryConfig[k as keyof typeof countryConfig]));

      const [regionPieData, countryPieData] = await Promise.all([
        getMarketChannelPieData(where, 'region'),
        getMarketChannelPieData(countryWhere, 'country'),
      ]);

      const modifyRegionPieData = reducePieDataColumn(payload.countryList, regionPieData);
      const modifyCountryPieData = reducePieDataColumn(payload.countryList, countryPieData);
      const conditionObj = {
        ...INIT_CON_OBJ,
        regionInput: region,
        countryInput: countryList[0].value,
        categoryInput: category,
        dateInput: date,
      };
      payload.regionPieData = modifyRegionPieData;
      payload.countryPieData = modifyCountryPieData;
      payload.initConditionObj = { ...conditionObj };
      payload.conditionObj = { ...conditionObj };
      hideLoading();
    };

    const getFilterCountryPie = async (country: string) => {
      pieLoading.value = true;
      payload.countryFilterInput = country;
      const countryConfig = {
        country,
        category: payload.conditionObj.categoryInput,
        date: payload.conditionObj.dateInput,
      };
      const countryWhere = Object.keys(countryConfig).map(k => inStringCond(
        k,
        countryConfig[k as keyof typeof countryConfig],
      ));
      const countryPieData = await getMarketChannelPieData(countryWhere, 'country');

      // 排列数据成展示5个，其他变去Others
      const modifyCountryPieData = reducePieDataColumn(payload.countryList, countryPieData);

      payload.countryPieData = modifyCountryPieData;
      pieLoading.value = false;
    };

    const reducePieDataColumn = (
      countryList: GetCountryResponseModal['default'],
      pieData: GetPieResponseModal['default'],
    ) => {
      const data = countryMapModification(countryList, pieData).map(item => ({
        ...item,
        name: item.media_source,
        value: parseFloat(item.overall_percentage) < 0.01
          ? parseFloat(item.overall_percentage).toPrecision(2) : parseFloat(item.overall_percentage).toFixed(2),
      }));

      let reducedData: any[];

      if (data.length > 5) {
        const top5 = data.sort((a, b) => b.overall_percentage - a.overall_percentage).slice(0, 5);
        const othersValue = data.slice(5).reduce((sum, obj) => sum + parseFloat(obj.value), 0);
        reducedData = [...top5, {
          name: 'Others', value: parseFloat(othersValue) < 0.01
            ? parseFloat(othersValue).toPrecision(2) : parseFloat(othersValue).toFixed(2),
        }];
      } else {
        reducedData = data;
      }
      return reducedData;
    };

    return {
      init,
      isLoading,
      pieLoading,
      payload,
      getCountry,
      getFilterCountryPie,
      getFilterData,
      hideLoading,
      showLoading,
    };
  },
);
