// type
export type MetricModal = {
  name: string, as: string, sum?: boolean
}[];

export type WhereModal = {
  name: string;
  in_list: string[];
  type: number;
}[];

export type CountryListModal = {
  country_abbre: string;
  country_en: string;
  region_abbre: string;
  region_en: string;
};

export type CountryOptionsModal =
    Record<string, {
      label: string;
      value: string;
    }[]>;

export type RegionOptionsModal = {
  label: string;
  value: string;
  children?: RegionOptionsModal[];
};
