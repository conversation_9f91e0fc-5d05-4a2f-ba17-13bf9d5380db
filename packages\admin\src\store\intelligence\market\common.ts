import { CountryListModal } from './common.d';

// 调整第一个知母成capital
function capitalizeFirstLetter(str: string | null | undefined) {
  if (typeof str !== 'string' || !str) {
    return str;
  }
  return str.charAt(0).toUpperCase() + str.slice(1);
}

// arrange config attrlist and metriclist
export const columnsConfig = (attrList: {
  key: string;
  header: string;
}[], metricList: {
  render: (row: any) => string;
  key: string;
  header: string;
}[], excludeColumn?: string[]) => [...attrList, ...metricList].map((item: {
  key: string | undefined;
  header: string
}) => {
  const keyMap: { [key: string]: string } = {
    region_abbre: 'region_en',
    country_abbre: 'country_en',
  };
  return {
    colKey: item.key || '',
    title: item.header.charAt(0).toUpperCase() + item.header.slice(1),
    width: 200,
    cell: (_h: any, { row }: { row: Record<string, any> }) => {
      const value = row[item.key || ''];
      if (metricList.some(list => list.key === item.key)
        && !(excludeColumn ?? []).includes(item.key as string)) {
        return value ? parseInt(value, 10).toLocaleString() : '0';
      }
      return keyMap[item.key as keyof typeof keyMap]
        ? row[keyMap[item.key as keyof typeof keyMap]] : capitalizeFirstLetter(value);
    },
    sorter: false,
  };
});

export const inStringCond = (key: string, value: any) => ({
  name: key,
  in_list: Array.isArray(value) ? value : [value],
  type: 1,
});

// 计算功能
export const toOption = (list: any = [], key: string) => list.map((one: { [x: string]: any; }) => ({
  label: one[key].toString(),
  value: one[key],
}));

// 排列国家和州的数据
export const countryMapModification = (countryList: CountryListModal[], array: any[]) => {
  const countryMap: { [key: string]: string } = {};
  const regionMap: { [key: string]: string } = {};

  countryList.forEach((item) => {
    regionMap[item.region_abbre] = item.region_en;
    countryMap[item.country_abbre] = item.country_en;
  });
  return array.map((item, index) => ({
    ...item,
    index,
    region_en: regionMap[item.region_abbre] ?? item.region_abbre,
    country_en: countryMap[item.country_abbre],
  }));
};
