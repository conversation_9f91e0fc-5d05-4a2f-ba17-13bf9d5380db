export const COUNTRY_COMPETITIVE_METRIC = {
  gen: {
    metricKeys: [{
      name: 'region', // -- 区域缩写
      as: 'region_abbre',
    }, {
      name: 'country', // -- 国家缩写
      as: 'country_abbre',
    }],
    where: [{
      name: 'market_type', // 固定传递
      in_list: ['country'],
      type: 1,
    }],
    group: ['region', 'country'],
    order: ['region', 'country'],
    pageSize: 500,
    pageNum: 0,
  },
};

export const INIT_CON_OBJ = {
  regionInputList: [],
  countryInputList: [],
  dateInput: '',
  categoryInput: '',
  platformInput: '',
};
