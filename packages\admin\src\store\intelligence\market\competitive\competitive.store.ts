import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useWatchGameChange } from 'common/compose/request/game';
import { useLoading } from 'common/compose/loading';
import { reactive } from 'vue';
// modal && type
import { COUNTRY_COMPETITIVE_METRIC, INIT_CON_OBJ } from './competitive.d';
import { WhereModal, CountryListModal } from '../common.d';
import { inStringCond, toOption } from '../common';
// service
import { CategoryResponse, DateResponse, EntityTypeResponse, GetCountryConditionRequestModal, GetCountryResponseModal, GetFilterRequestModal } from 'common/service/intelligence/common/common.d';
import { getAllCountry } from 'common/service/intelligence/common/common';
import { GetCompetitiveMapResponseModal } from 'common/service/intelligence/market/competitive/competitve';
import { getCompetitiveCountry, getFilter, getMap } from 'common/service/intelligence/market/competitive/competitive';
import { catGen, date, osGen } from '../const';

export const useIntelligenceMarketCompetitiveStore = defineStore(
  STORE_KEY.INTELLIGENCE.MARKET.COMPETITIVE,
  () => {
    const { isLoading, hideLoading, showLoading } = useLoading();
    const gameStore = useGlobalGameStore();
    const payload = reactive<{
      countryList: GetCountryResponseModal['default'],
      conditionCountry: GetCountryResponseModal['default'],
      competitiveMap: GetCompetitiveMapResponseModal['default'],
      saveRegionInputList: string[],
      dateList: {
        label: string;
        value: string;
      }[],
      categoryList: {
        label: string;
        value: string;
      }[],
      platformList: {
        label: string;
        value: string;
      }[],
      initConditionObj: {
        regionInputList: string[];
        countryInputList: string[];
        dateInput: string;
        categoryInput: string;
        platformInput: string;
      },
    }>({
      countryList: [],
      conditionCountry: [],
      competitiveMap: [],
      saveRegionInputList: [],
      dateList: [],
      categoryList: [],
      platformList: [],
      initConditionObj: {
        regionInputList: [],
        countryInputList: [],
        dateInput: '',
        categoryInput: '',
        platformInput: '',
      },
    });
    let conditionObj = reactive<{
      regionInputList: string[];
      countryInputList: string[];
      dateInput: string;
      categoryInput: string;
      platformInput: string;
    }>({
      regionInputList: [],
      countryInputList: [],
      dateInput: '',
      categoryInput: '',
      platformInput: '',
    });

    async function getMarketCompetitiveFilter<T>(metric: GetFilterRequestModal) {
      const data = await getFilter<T>(metric);
      return data.default;
    }

    async function getCountry() {
      const data = await getAllCountry({ game: gameStore.gameCode });
      return data.default;
    }

    async function getMarketCompetitiveCountry(metric: GetCountryConditionRequestModal) {
      const data = await getCompetitiveCountry(metric);
      return data.default;
    }

    async function getMarketCompetitiveMap(where: WhereModal) {
      const gen = {
        where,
      };
      const data = await getMap({ gen });
      return data.default;
    }

    const countryMapModification = (countryList: CountryListModal[], array: any[]) => {
      const countryMap: { [key: string]: string } = {};
      const regionMap: { [key: string]: string } = {};

      countryList.forEach((item) => {
        regionMap[item.region_abbre] = item.region_en;
        countryMap[item.country_abbre] = item.country_en;
      });
      return array.map((item, index) => ({
        ...item,
        index,
        region_en: regionMap[item.region_abbre],
        country_en: countryMap[item.country_abbre],
      }));
    };

    const mapModification = (countryList: CountryListModal[], array: any[]) => {
      const countryMap: { [key: string]: string } = {};
      countryList.forEach((item) => {
        countryMap[item.country_abbre] = item.country_en;// "AO":"Angola"
      });
      return array.map(item => ({
        ...item,
        country_abbre: countryMap[item.country_abbre],
      }));
    };

    // init
    const init = async () => {
      useWatchGameChange(async () => {
        try {
          showLoading();
          // 拿取filter所需要的数据
          const [categoryList, platformList, dateList]:
          [CategoryResponse['default'], EntityTypeResponse['default'], DateResponse['default']] = await Promise.all([
            getMarketCompetitiveFilter<'category'>(catGen),
            getMarketCompetitiveFilter<'entity_type'>(osGen),
            getMarketCompetitiveFilter<'date'>(date),
          ]);

          // 调整filter数据
          const modifyPlatformList = platformList.map((e) => {
            const value = e.entity_type;
            return { label: value.slice(0, 1).toLocaleUpperCase() + value.slice(1), value };
          });

          const modifyCategoryList = toOption(categoryList, 'category');

          const modifyDateList = toOption(dateList, 'date').map(({ label = '', value = '' }) => ({
            label: label.split('-').join('')
              .slice(0, 6),
            value,
          }));

          // 拿取图表所需要的数据
          const config = {
            market_type: 'country', region: [], country: [],
            entity_type: [modifyPlatformList[0].value], category: [modifyCategoryList[0].value],
            date: [modifyDateList[0].value],
          };
          const where = Object.keys(config).map(k => inStringCond(k, config[k as keyof typeof config]));

          const [countryList, conditionCountry, competitiveMap] = await Promise.all([
            getCountry(),
            getMarketCompetitiveCountry(COUNTRY_COMPETITIVE_METRIC),
            getMarketCompetitiveMap(where),
          ]);

          const modifyConditionCountry = countryMapModification(countryList, conditionCountry);
          const saveRegionInputList = Array.from(new Set(modifyConditionCountry.map(({ region_abbre = '' }) => region_abbre)));

          conditionObj = {
            ...INIT_CON_OBJ,
            regionInputList: saveRegionInputList,
            countryInputList: modifyConditionCountry.map(({ country_abbre = '' }) => country_abbre),
            dateInput: modifyDateList[0].value,
            categoryInput: modifyCategoryList[0].value,
            platformInput: modifyPlatformList[0].value,
          };

          payload.countryList = countryList;
          payload.conditionCountry = modifyConditionCountry;
          payload.saveRegionInputList = saveRegionInputList;
          payload.dateList = modifyDateList;
          payload.competitiveMap = mapModification(countryList, competitiveMap);
          payload.categoryList = modifyCategoryList;
          payload.platformList = modifyPlatformList;
          payload.initConditionObj = { ...conditionObj };
        } catch (error) {
          // Handle errors here
        } finally {
          hideLoading();
        }
      });
    };

    // get Filter Data
    const getFilterData = async (
      country: string[], date: string, region: string[],
      category: string, platform: string,
    ) => {
      showLoading();
      const { regionInputList, countryInputList } = payload.initConditionObj;

      const filterConfig = {
        market_type: 'country',
        region: region.length === regionInputList.length ? [] : region,
        country: country.length === countryInputList.length ? [] : country,
        date,
        category,
        entity_type: platform,
      };

      const where = Object.keys(filterConfig).map(k => inStringCond(k, filterConfig[k as keyof typeof filterConfig]));

      const competitiveMap = await getMarketCompetitiveMap(where);

      conditionObj.dateInput = date;
      conditionObj.countryInputList = country;
      conditionObj.regionInputList = region;
      conditionObj.categoryInput = category;
      conditionObj.platformInput = platform;
      payload.competitiveMap = mapModification(payload.countryList, competitiveMap);
      hideLoading();
    };

    return {
      init,
      isLoading,
      payload,
      conditionObj,
      getCountry,
      getFilterData,
      hideLoading,
      showLoading,
    };
  },
);
