export const COUNTRY_METRIC = {
  gen: {
    metricKeys: [{
      name: 'region', // -- 区域缩写
      as: 'region_abbre',
    }, {
      name: 'country', // -- 国家缩写
      as: 'country_abbre',
    }],
    where: [{
      name: 'market_type', // 固定传递
      in_list: ['country'],
      type: 1,
    }],
    group: ['region', 'country'],
    order: ['region', 'country'],
    pageSize: 500,
    pageNum: 0,
  },
};

export const osGen = {
  gen: {
    metricKeys: ['entity_type'], // -- 平台设备
    group: ['entity_type'],
    order: ['entity_type'],
    pageSize: 500,
    pageNum: 0,
  },
};

export const platformGen = {
  gen: {
    metricKeys: ['platform'], // -- 平台设备
    group: ['platform'],
    order: ['platform'],
    pageSize: 500,
    pageNum: 0,
  },
};

export const catGen = {
  gen: {
    metricKeys: ['category'],
    group: ['category'],
    order: ['category'],
    pageSize: 500,
    pageNum: 0,
  },
};

export const date = {
  gen: {
    // -- 日期
    metricKeys: ['date'],
    group: ['date'],
    order: [{ order: 'DESC', by: 'date' }],
    pageSize: 500,
    pageNum: 0,
  },
};
