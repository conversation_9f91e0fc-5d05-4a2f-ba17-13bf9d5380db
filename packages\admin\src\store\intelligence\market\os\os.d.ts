export const COUNTRY_OS_METRIC = {
  gen: {
    metricKeys: [{
      name: 'region', // -- 区域缩写
      as: 'region_abbre',
    }, {
      name: 'country', // -- 国家缩写
      as: 'country_abbre',
    }],
    where: [{
      name: 'market_type', // 固定传递
      in_list: ['country'],
      type: 1,
    }],
    group: ['region', 'country'],
    order: ['region', 'country'],
    pageSize: 500,
    pageNum: 0,
  },
};

export const BAR_OS_METRIC =    ['platform',
  {
    name: 'download',
    sum: true,
    as: 'download',
  }, {
    name: 'region',
    as: 'region_abbre',
  }];

export const INIT_CON_OBJ = {
  regionInputList: [],
  countryInputList: [],
  categoryInputList: [],
  platformInputList: [], // 默认页面的查询条件，需要暂存
  dateInputList: [],
};

export const TABLE_OS_METRIC =    [
  // attr
  'date',
  {
    name: 'region', // -- 区域缩写
    as: 'region_abbre',
  }, {
    name: 'country', // -- 国家缩写
    as: 'country_abbre',
  },
  'category', // -- 主品类
  'platform', // -- 平台设备
  // metric
  {
    name: 'download',
    sum: true,
    as: 'download', // -- 游戏月活用户数|游戏付费用户数
  }];
