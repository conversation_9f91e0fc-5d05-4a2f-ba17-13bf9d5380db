import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useWatchGameChange } from 'common/compose/request/game';
import { useLoading } from 'common/compose/loading';
import { reactive, ref } from 'vue';
// modal && type
import { catGen, date, platformGen } from '../const';
import { WhereModal } from '../common.d';
import { columnsConfig, inStringCond, toOption, countryMapModification } from '../common';
// service
import {
  CategoryResponse, DateResponse, GetCountryConditionRequestModal,
  GetCountryConditionResponseModal, GetCountryResponseModal, GetFilterRequestModal, MetricKeys, PlatformResponse,
} from 'common/service/intelligence/common/common.d';
import { getAllCountry } from 'common/service/intelligence/common/common';
import { getBar, getConfig, getFilter, getOSCountry, getOSTable } from 'common/service/intelligence/market/os/os';
import { BAR_OS_METRIC, COUNTRY_OS_METRIC, INIT_CON_OBJ, TABLE_OS_METRIC } from '@/store/intelligence/market/os/os.d';
import { GetOSBarResponseModal, GetOSTableResponseModal } from 'common/service/intelligence/market/os/os.d';
import { ITableCols } from 'common/components/table/type';

export const useIntelligenceMarketOSStore = defineStore(
  STORE_KEY.INTELLIGENCE.MARKET.OS,
  () => {
    const { isLoading, hideLoading, showLoading } = useLoading();
    const gameStore = useGlobalGameStore();
    const tableLoading = ref(false);
    const tableColumns = reactive<{ displayColumns: string[], columns: ITableCols[] }>({
      displayColumns: [],
      columns: [],
    });
    const payload = reactive<{
      countryList: GetCountryResponseModal['default'],
      conditionCountry: GetCountryResponseModal['default'],
      barData: GetOSBarResponseModal['default'],
      percentageBarData: GetOSBarResponseModal['default'],
      option_obj: {
        categoryList: any[],
        platformList: any[],
        dateList: any[]
      }, // 查询相关的列表
      initConditionObj: {
        regionInputList: string[];
        countryInputList: string[];
        categoryInputList: string[];
        dateInputList: string[];
        platformInputList: string[];
      },
      conditionObj: {
        regionInputList: string[];
        countryInputList: string[];
        categoryInputList: string[];
        dateInputList: string[];
        platformInputList: string[];
      },
    }>({
      countryList: [],
      conditionCountry: [],
      barData: [],
      percentageBarData: [],
      option_obj: {
        platformList: [],
        categoryList: [],
        dateList: [],
      },
      initConditionObj: {
        regionInputList: [],
        countryInputList: [],
        categoryInputList: [],
        dateInputList: [],
        platformInputList: [],
      },
      conditionObj: {
        regionInputList: [],
        countryInputList: [],
        categoryInputList: [],
        dateInputList: [],
        platformInputList: [],
      },
    });

    const table = reactive({
      records: [] as GetOSTableResponseModal['total_record'] |
      GetOSTableResponseModal['default'],
      pageInfo: {
        pageSize: 10,
        pageIndex: 1,
        total: 0,
      },
    });

    async function getTableConfig() {
      const data = await getConfig({ game: gameStore.gameCode });
      return data;
    }

    async function getCountry() {
      const data = await getAllCountry({ game: gameStore.gameCode });
      return data.default;
    }

    async function getMarketOSTable(metric: (string | {
      name: string;
      as: string;
      sum?: boolean;
    })[], where: WhereModal, page: {
      pageSize: number;
      pageIndex: number;
    }) {
      const { pageSize, pageIndex } = page;
      const gen = {
        metricKeys: metric,
        where,
        group: ['date', 'region', 'country', 'category', 'platform'],
        order: [{ order: 'DESC', by: 'date' }, { order: 'ASC', by: 'region' }, { order: 'ASC', by: 'country' }],
        pageSize,
        pageNum: pageIndex - 1,
      };
      const data = await getOSTable({ ext: { isGenTotalRecord: true, isGenTotalNum: true }, gen });
      return {
        totalRecord: data.total_record, // 第一条总数
        tableValue: data.default,
        pages: data.total_num ? data.total_num[0] : { total_num: 0, total_size: '0' },
      };
    }

    async function getMarketOSCountry(metric: GetCountryConditionRequestModal) {
      const data = await getOSCountry(metric);
      return data.default;
    }

    async function getMarketOSBarData(metric: MetricKeys, where: WhereModal) {
      const gen = {
        metricKeys: metric,
        where,
        group: ['region', 'platform'],
        order: ['region', 'platform'],
      };
      const data = await getBar({ gen });
      return data.default.filter(one => one.region_abbre !== 'GLOBAL_WO_CHINA' && one.region_abbre !== 'CHN');
    }

    async function getMarketOSFilter<T>(metric: GetFilterRequestModal) {
      const data = await getFilter<T>(metric);
      return data.default;
    }

    // init
    const init = async () => {
      useWatchGameChange(async () => {
        try {
          showLoading();
          // 拿取过滤选择器的数据
          const [categoryList, dateList, platformList, countryList, conditionCountry]:
          [CategoryResponse['default'], DateResponse['default'], PlatformResponse['default'],
            GetCountryResponseModal['default'], GetCountryConditionResponseModal['default']] = await Promise.all([
            getMarketOSFilter<'category'>(catGen),
            getMarketOSFilter<'date'>(date),
            getMarketOSFilter<'platform'>(platformGen),
            getCountry(),
            getMarketOSCountry(COUNTRY_OS_METRIC),
          ]);
          const modifyPlatformList = toOption(platformList, 'platform');
          const modifyCategoryList = toOption(categoryList, 'category');

          const obj: Record<string, boolean> = {}; // Define obj with proper typing

          const modifyDateList = toOption(dateList, 'date').map(({ label = '', value = '' }) => ({
            label: label.toString().split('-')
              .join('')
              .slice(0, 6),
            value: value.toString(),
          }))
            .reduce((pre: any[], cur: any) => { // Adjust the type of pre
              if (!obj[cur.label]) { // Update the property access to cur.label
                obj[cur.label] = true;
                pre.push(cur);
              }
              return pre;
            }, []);

          const modifyConditionCountry = countryMapModification(countryList, conditionCountry).filter(one => one.region_abbre !== 'GLOBAL_WO_CHINA' && one.region_abbre !== 'CHN');

          // 拿取柱状图数据
          const config = {
            market_type: 'country', region: [], country: [], platform: [],
            category: [], date: modifyDateList.map((one: { value: string; }) => one.value).slice(0, 12),
          };
          const where = Object.keys(config).map(k => inStringCond(k, config[k as keyof typeof config]));
          const barData = await getMarketOSBarData(BAR_OS_METRIC, where);

          const saveRegionInputList = Array.from(new Set(modifyConditionCountry.map(({ region_abbre = '' }) => region_abbre)));

          const conditionObj = {
            ...INIT_CON_OBJ,
            regionInputList: saveRegionInputList,
            countryInputList: modifyConditionCountry.map(({ country_abbre = '' }) => country_abbre),
            categoryInputList: modifyCategoryList.map(({ value }: { value: string }) => value),
            platformInputList: modifyPlatformList.map(({ value }: { value: string }) => value),
            dateInputList: modifyDateList.slice(0, 12).map(({ value }: { value: string }) => value),
          };

          payload.countryList = countryList;
          payload.option_obj = {
            platformList: modifyPlatformList,
            categoryList: modifyCategoryList,
            dateList: modifyDateList,
          };
          payload.conditionCountry = modifyConditionCountry;
          payload.barData = countryMapModification(countryList, barData);
          // eslint-disable-next-line max-len
          payload.percentageBarData = getPercentageModification(countryMapModification(countryList, barData));
          payload.initConditionObj = { ...conditionObj };
          payload.conditionObj = { ...conditionObj };

          await getTable(
            saveRegionInputList, conditionObj.categoryInputList, conditionObj.countryInputList,
            { pageSize: 10, pageIndex: 1 }, conditionObj.dateInputList, conditionObj.platformInputList,
          );
        } catch (error) {
          // Handle errors here
        } finally {
          hideLoading();
        }
      });
    };

    const getPercentageModification = (data: GetOSBarResponseModal['default']):
    GetOSBarResponseModal['default'] => {
      // Calculate total downloads for each region
      const totalDownloadsByRegion: Record<string, number> = {};
      data.forEach(({ download, region_abbre }) => {
        // eslint-disable-next-line max-len
        totalDownloadsByRegion[region_abbre] = (totalDownloadsByRegion[region_abbre] || 0) + parseInt(download, 10);
      });
      // Add percentage property to each object
      return data.map(({ download, region_abbre, ...rest }) => ({
        ...rest,
        region_abbre,
        download,
        percentage: ((parseInt(download, 10) / totalDownloadsByRegion[region_abbre]) * 100).toFixed(2),
      }));
    };

    // get Table Data
    const getTable = async (
      regionList: string[], categoryList: string[], countryList: string[],
      page: { pageSize: number, pageIndex: number }, dateList: string[], platformList: string[],
    ) => {
      tableLoading.value = true;
      // get table data
      const {
        regionInputList = [],
        countryInputList = [],
        categoryInputList = [],
        platformInputList = [],
        dateInputList = [],
      } = payload.initConditionObj;

      const { attrList = [], metricList = [] } = await getTableConfig();
      tableColumns.displayColumns = [
        ...attrList.map((list: { key: any; }) => list.key),
        ...metricList.map((list: { key: any; }) => list.key),
      ];
      tableColumns.columns = columnsConfig(attrList, metricList, ['platform']);

      const config = {
        market_type: 'country',
        region: regionInputList.length === regionList.length ? [] : regionList,
        country: countryInputList.length === countryList.length ? [] : countryList,
        platform: platformInputList.length === platformList.length ? [] : platformList,
        category: categoryInputList.length === categoryList.length ? [] : categoryList,
        date: dateList.length === 0 ? dateInputList : dateList,
      };
      const where = Object.keys(config).map(k => inStringCond(k, config[k as keyof typeof config]));
      const { tableValue, totalRecord, pages } = await getMarketOSTable(TABLE_OS_METRIC, where, page);
      // eslint-disable-next-line @typescript-eslint/naming-convention
      let addTotalNum;
      if (page.pageIndex === 1) {
        addTotalNum = totalRecord?.map(one => ({
          ...one,
          date: 'Total',
        }))?.concat(tableValue as any);
      } else {
        addTotalNum = tableValue;
      }
      if (addTotalNum && addTotalNum.length > 0) {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        table.pageInfo = { ...page, total: parseInt(pages?.total_size, 10) };
        table.records = countryMapModification(payload.countryList, addTotalNum as any).map(one => ({
          ...one,
          date: one.date.split('-').join('')
            .slice(0, 6),
        }));
      } else {
        table.records = addTotalNum;
      }
      tableLoading.value = false;
    };

    // get Filter Data
    const getFilterData = async (
      category: string[], date: string[],
      region: string[], platform: string[], country: string[],
    ) => {
      showLoading();
      const { regionInputList, countryInputList,
        categoryInputList, platformInputList, dateInputList } = payload.initConditionObj;

      const pageInfo = {
        pageSize: 10,
        pageIndex: 1,
        total: 0,
      };

      table.pageInfo = pageInfo;
      // 默认选择区域的第一个来生成国家的option
      const configFilter = {
        market_type: 'country',
        region: region.length === regionInputList.length ? [] : region,
        country: country.length === countryInputList.length ? [] : country,
        platform: platform.length === platformInputList.length ? [] : platform,
        category: category.length === categoryInputList.length ? [] : category,
        date: date.length === 0 ? dateInputList : date,
      };

      const where = Object.keys(configFilter).map(k => inStringCond(k, configFilter[k as keyof typeof configFilter]));

      const [barData, { tableValue, totalRecord, pages }] = await Promise.all([
        getMarketOSBarData(BAR_OS_METRIC, where),
        // eslint-disable-next-line max-len
        getMarketOSTable(TABLE_OS_METRIC, where, table.pageInfo),
      ]);

      const modifyTableValue = tableValue.map(one => ({
        ...one,
        date: one.date.split('-').join('')
          .slice(0, 6),
      }));

      const addTotalNum = pageInfo.pageIndex === 1
        ? totalRecord?.map(one => ({ ...one, date: 'Total' }))?.concat(modifyTableValue as any)
        : tableValue;

      const conditionObj = {
        ...INIT_CON_OBJ,
        regionInputList: region.length === 0 ? regionInputList : region,
        platformInputList: platform.length === 0 ? platformInputList : platform,
        countryInputList: country.length === 0 ? countryInputList : country,
        categoryInputList: category.length === 0 ? categoryInputList : category,
        dateInputList: date.length === 0 ? dateInputList : date,
      };
      payload.barData = countryMapModification(payload.countryList, barData);
      // eslint-disable-next-line max-len
      payload.percentageBarData = getPercentageModification(countryMapModification(payload.countryList, barData));
      if (addTotalNum && addTotalNum.length > 0) {
        table.records = countryMapModification(payload.countryList, addTotalNum as any).map(one => ({
          ...one,
          date: one.date.split('-').join('')
            .slice(0, 6),
        }));
        table.pageInfo = { ...table.pageInfo, total: parseInt(pages.total_size, 10) };
      } else {
        table.records = addTotalNum;
      }
      payload.conditionObj = { ...conditionObj };
      hideLoading();
    };

    return {
      init,
      isLoading,
      payload,
      table,
      tableColumns,
      tableLoading,
      getTable,
      getCountry,
      getFilterData,
      hideLoading,
      showLoading,
    };
  },
);
