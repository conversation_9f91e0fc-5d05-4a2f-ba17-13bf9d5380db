// const
export const BAR_DATA_METRIC = [{
  name: 'region', // -- 区域缩写
  as: 'region',
}, {
  name: 'total_population', // -- 总人口规模数
  sum: true,
  as: 'Population',
}, {
  name: 'total_online_population', // -- 总在线人口规模数
  sum: true,
  as: 'OnlinePopulation',
}, {
  name: 'total_players', // -- 总游戏用户规模数
  sum: true,
  as: 'Players',
}, {
  name: 'total_payers', // -- 总付费用户规模数
  sum: true,
  as: 'Payers',
}];

export const MAP_METRIC = [{
  name: 'region', // -- 区域缩写
  as: 'region_abbre',
}, {
  name: 'country', // -- 国家缩写
  as: 'country_abbre',
},
// players
{
  name: 'total_players', // -- 总游戏用户规模数
  sum: true,
  as: 'total_players',
}, {
  name: 'pc_players', // -- PC游戏用户规模数
  sum: true,
  as: 'pc_players',
}, {
  name: 'mobile_players', // -- Mobile游戏用户规模数
  sum: true,
  as: 'mobile_players',
}, {
  name: 'console_players', // -- Console游戏用户规模数
  sum: true,
  as: 'console_players',
},
// payers
{
  name: 'total_payers', // -- 总付费用户规模数
  sum: true,
  as: 'total_payers',
}, {
  name: 'pc_payers', // -- PC游戏付费用户规模数
  sum: true,
  as: 'pc_payers',
}, {
  name: 'mobile_payers', // -- Mobile游戏付费用户规模数
  sum: true,
  as: 'mobile_payers',
}, {
  name: 'console_payers', // -- Console游戏付费用户规模数
  sum: true,
  as: 'console_payers',
}];

export const DATE_METRIC = {
  // -- 日期
  metricKeys: ['date'],
  group: ['date'],
  order: [{ order: 'DESC', by: 'date' }],
  pageSize: 500,
  pageNum: 0,
};

export const TABLE_METRIC = [
  // attr
  'date',
  {
    name: 'region', // -- 区域缩写
    as: 'region_abbre',
  }, {
    name: 'country', // -- 国家缩写
    as: 'country_abbre',
  },
  // metric: pop
  {
    name: 'total_population', // -- 总人口规模数
    sum: true,
    as: 'total_population',
  }, {
    name: 'total_online_population', // -- 总在线人口规模数
    sum: true,
    as: 'total_online_population',
  },
  // metric: players
  {
    name: 'total_players', // -- 总游戏用户规模数
    sum: true,
    as: 'total_players',
  }, {
    name: 'pc_players', // -- PC游戏用户规模数
    sum: true,
    as: 'pc_players',
  }, {
    name: 'mobile_players', // -- Mobile游戏用户规模数
    sum: true,
    as: 'mobile_players',
  }, {
    name: 'console_players', // -- Console游戏用户规模数
    sum: true,
    as: 'console_players',
  },
  // metric: payers
  {
    name: 'total_payers', // -- 总付费用户规模数
    sum: true,
    as: 'total_payers',
  }, {
    name: 'pc_payers', // -- PC游戏付费用户规模数
    sum: true,
    as: 'pc_payers',
  }, {
    name: 'mobile_payers', // -- Mobile游戏付费用户规模数
    sum: true,
    as: 'mobile_payers',
  }, {
    name: 'console_payers', // -- Console游戏付费用户规模数
    sum: true,
    as: 'console_payers',
  }];

export const INIT_CON_OBJ = {
  game: '',
  regionInputList: [],
  countryInputList: [],
  dateInputList: '',
};

export const TABLE_DATA_CONFIG = { market_type: 'country', region: [], country: [], date: [] as string[] };
