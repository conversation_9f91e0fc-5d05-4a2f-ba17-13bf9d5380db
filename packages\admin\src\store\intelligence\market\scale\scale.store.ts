import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useWatchGameChange } from 'common/compose/request/game';
import { useLoading } from 'common/compose/loading';
import { reactive, ref } from 'vue';
// modal && type
import { BAR_DATA_METRIC, DATE_METRIC, INIT_CON_OBJ, MAP_METRIC, TABLE_DATA_CONFIG, TABLE_METRIC } from './scale.d';
import { ScaleWorldMap } from '@/views/intelligence/market/scale/modal/scale';
import { GetScaleBarResponseModal, GetScaleDataResponseModal } from 'common/service/intelligence/market/scale/scale.d';
import { GetCountryConditionRequestModal, GetCountryResponseModal } from 'common/service/intelligence/common/common.d';
import { MetricModal, WhereModal } from '../common.d';
import { ITableCols } from 'common/components/table/type';
// service
import { getBar, getConfig, getData, getFilter, getMap, getScaleCountry } from 'common/service/intelligence/market/scale/scale';
import { getAllCountry } from 'common/service/intelligence/common/common';
import { columnsConfig, countryMapModification, inStringCond, toOption } from '../common';
import { COUNTRY_METRIC } from '../const';

export const useIntelligenceMarketScaleStore = defineStore(
  STORE_KEY.INTELLIGENCE.MARKET.SCALE,
  () => {
    const { isLoading, hideLoading, showLoading } = useLoading();
    const gameStore = useGlobalGameStore();
    const tableLoading = ref(false);
    const payload = reactive<{
      countryList: GetCountryResponseModal['default'],
      conditionCountry: GetCountryResponseModal['default'],
      barData: { region: string, name: string, value: number }[],
      scaleMap: ScaleWorldMap[],
      saveRegionInputList: string[],
      dateList: {
        label: string;
        value: string;
      }[],
      initConditionObj: {
        game: string;
        regionInputList: string[];
        countryInputList: string[];
        dateInputList: string;
      },
      conditionObj: {
        game: string;
        regionInputList: string[];
        countryInputList: string[];
        dateInputList: string;
      },
    }>({
      countryList: [],
      conditionCountry: [],
      barData: [],
      scaleMap: [],
      saveRegionInputList: [],
      dateList: [],
      initConditionObj: {
        game: '',
        regionInputList: [],
        countryInputList: [],
        dateInputList: '',
      },
      conditionObj: {
        game: '',
        regionInputList: [],
        countryInputList: [],
        dateInputList: '',
      },
    });
    let conditionObj = reactive<{
      game: string;
      regionInputList: string[];
      countryInputList: string[];
      dateInputList: string;
    }>({
      game: '',
      regionInputList: [],
      countryInputList: [],
      dateInputList: '',
    });
    const table = reactive({
      records: [] as GetScaleDataResponseModal['total_record'] |
      GetScaleDataResponseModal['default'],
      pageInfo: {
        pageSize: 10,
        pageIndex: 1,
        total: 0,
      },
    });

    const tableColumns = reactive<{ displayColumns: string[], columns: ITableCols[] }>({
      displayColumns: [],
      columns: [],
    });

    async function getTableConfig() {
      const data = await getConfig({ game: gameStore.gameCode });
      return data;
    }

    async function getCountry() {
      const data = await getAllCountry({ game: gameStore.gameCode });
      return data.default;
    }

    async function getMarketScaleCountry(metric: GetCountryConditionRequestModal) {
      const data = await getScaleCountry(metric);
      return data.default;
    }

    async function getMarketScaleMap(metric: MetricModal, where: WhereModal) {
      const gen = {
        metricKeys: metric,
        where,
        group: ['region', 'country'],
        order: ['region', 'country'],
        pageSize: 500,
        pageNum: 0,
      };
      const data = await getMap({ gen });
      return data.default;
    }

    async function getMarketScaleBarData(metric: MetricModal, where: WhereModal) {
      const gen = {
        metricKeys: metric,
        where,
        group: ['region'],
        order: ['region'],
      };
      const data = await getBar({ gen });
      return data.default;
    }

    async function getMarketScaleDate() {
      const data = await getFilter<'date'>({ gen: DATE_METRIC });
      return data.default;
    }

    async function getMarketScaleTable(metric: (string | {
      name: string;
      as: string;
      sum?: boolean;
    })[], where: WhereModal, page: {
      pageSize: number;
      pageIndex: number;
    }) {
      const { pageSize, pageIndex } = page;
      const gen = {
        metricKeys: metric,
        where,
        group: ['date', 'region', 'country'],
        order: ['date', 'region', 'country'],
        pageSize,
        pageNum: pageIndex - 1,
      };
      const data = await getData({ gen, ext: { isGenTotalRecord: true, isGenTotalNum: true } });
      return {
        totalNum: data.total_record, // 第一条总数
        tableValue: data.default,
        pages: data.total_num[0],
      };
    }

    // init
    const init = async () => {
      useWatchGameChange(async () => {
        try {
          showLoading();

          // Assuming gameStore.gameCode is a ref or reactive property
          const { gameCode } = gameStore;

          const dateList = await getMarketScaleDate();
          TABLE_DATA_CONFIG.date.push(dateList[0].date.toString());
          const [countryList, conditionCountry, barData, scaleMap] = await Promise.all([
            getCountry(),
            getMarketScaleCountry(COUNTRY_METRIC),
            getMarketScaleBarData(
              BAR_DATA_METRIC,
              // eslint-disable-next-line max-len
              Object.keys(TABLE_DATA_CONFIG).map(k => inStringCond(k, TABLE_DATA_CONFIG[k as keyof typeof TABLE_DATA_CONFIG])),
            ),
            getMarketScaleMap(
              MAP_METRIC,
              // eslint-disable-next-line max-len
              Object.keys(TABLE_DATA_CONFIG).map(k => inStringCond(k, TABLE_DATA_CONFIG[k as keyof typeof TABLE_DATA_CONFIG])),
            ),
          ]);

          const modifyConditionCountry = countryMapModification(countryList, conditionCountry);
          const saveRegionInputList = Array.from(new Set(modifyConditionCountry.map(({ region_abbre = '' }) => region_abbre)));

          const modifyDateList = toOption(dateList, 'date');

          const modifyScaleMap = countryMapModification(countryList, scaleMap);

          conditionObj = {
            ...INIT_CON_OBJ,
            game: gameCode,
            regionInputList: saveRegionInputList,
            countryInputList: modifyConditionCountry.map(({ country_abbre = '' }) => country_abbre),
            dateInputList: modifyDateList[0].value,
          };
          const modifyBarData = setBarData(barData, countryList);

          payload.countryList = countryList;
          payload.conditionCountry = modifyConditionCountry;
          payload.barData = modifyBarData;
          payload.scaleMap = modifyScaleMap;
          payload.saveRegionInputList = saveRegionInputList;
          payload.dateList = modifyDateList;
          payload.initConditionObj = { ...conditionObj };
          payload.conditionObj = { ...conditionObj };
          await getTable(saveRegionInputList, conditionCountry, { pageIndex: 1, pageSize: 10 }, dateList);
        } catch (error) {
          // Handle errors here
        } finally {
          hideLoading();
        }
      });
    };

    // arrange to bar data
    const setBarData = (
      barData: GetScaleBarResponseModal['default'],
      countryList: GetCountryResponseModal['default'],
    ) => barData.flatMap((item) => {
      const region = countryList.find(country => country.region_abbre === item.region)?.region_en ?? item.region;
      return Object.entries(item)
        .filter(([key]) => key !== 'region')
        .map(([key, value]) => ({ region, name: key, value: parseInt(value, 10) }));
    });

    // get Table Data
    const getTable = async (saveRegionInputList: string[], conditionCountry: {
      region_abbre: string;
      country_abbre: string;
    }[], page: { pageSize: number, pageIndex: number }, initialDate?: {
      date: string;
    }[]) => {
      tableLoading.value = true;
      // get table data
      const {
        regionInputList = [],
        countryInputList = [],
        dateInputList = '',
      } = payload.conditionObj;
      const tableConfig = {
        market_type: 'country',
        region: regionInputList.length === saveRegionInputList.length ? [] : regionInputList,
        country: countryInputList.length === conditionCountry.length ? [] : countryInputList,
        date: dateInputList ? dateInputList : initialDate?.length ? initialDate[0].date.toString() : '',
      };

      const { attrList = [], metricList = [] } = await getTableConfig();

      const tableWhere = Object.keys(tableConfig).map(k => inStringCond(k, tableConfig[k as keyof typeof tableConfig]));
      const { tableValue, totalNum, pages } = await getMarketScaleTable(TABLE_METRIC, tableWhere, page);
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { total_size = '' } = pages;
      let addTotalNum;
      if (page.pageIndex === 1) {
        addTotalNum = totalNum?.map(one => ({
          ...one,
          date: 'Total',
        }))?.concat(tableValue as any);
      } else {
        addTotalNum = tableValue;
      }
      // eslint-disable-next-line @typescript-eslint/naming-convention
      table.pageInfo = { ...page, total: parseInt(total_size, 10) };
      table.records = countryMapModification(payload.countryList, addTotalNum as any);
      tableColumns.displayColumns = [
        ...attrList.map((list: { key: any; }) => list.key),
        ...metricList.map((list: { key: any; }) => list.key),
      ];
      tableColumns.columns = columnsConfig(attrList, metricList);
      tableLoading.value = false;
    };

    // get Filter Data
    const getFilterData = async (country: string[], date: string, region: string[]) => {
      showLoading();
      const { regionInputList, countryInputList } = payload.initConditionObj;
      const pageInfo = {
        pageSize: 10,
        pageIndex: 1,
        total: 0,
      };

      table.pageInfo = pageInfo;

      const filterConfig = {
        market_type: 'country',
        region: region.length === regionInputList.length ? [] : region,
        country: country.length === countryInputList.length ? [] : country,
        date: [date],
      };

      const where = Object.keys(filterConfig).map(k => inStringCond(k, filterConfig[k as keyof typeof filterConfig]));

      const [barData, scaleMap, { tableValue, totalNum, pages }] = await Promise.all([
        getMarketScaleBarData(BAR_DATA_METRIC, where),
        getMarketScaleMap(MAP_METRIC, where),
        getMarketScaleTable(TABLE_METRIC, where, table.pageInfo),
      ]);

      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { total_size = '' } = pages;

      const addTotalNum = pageInfo.pageIndex === 1
        ? totalNum?.map(one => ({ ...one, date: 'Total' }))?.concat(tableValue as any)
        : tableValue;

      table.records = countryMapModification(payload.countryList, addTotalNum as any);;
      // eslint-disable-next-line @typescript-eslint/naming-convention
      table.pageInfo = { ...table.pageInfo, total: parseInt(total_size, 10) };

      conditionObj.dateInputList = date;
      conditionObj.countryInputList = countryInputList;
      conditionObj.regionInputList = regionInputList;
      payload.barData = setBarData(barData, payload.countryList);
      payload.scaleMap = countryMapModification(payload.countryList, scaleMap);
      payload.conditionObj = { ...conditionObj };
      hideLoading();
    };

    return {
      init,
      isLoading,
      table,
      payload,
      tableLoading,
      tableColumns,
      conditionObj,
      getCountry,
      getFilterData,
      getTable,
      getMarketScaleCountry,
      hideLoading,
      showLoading,
    };
  },
);
