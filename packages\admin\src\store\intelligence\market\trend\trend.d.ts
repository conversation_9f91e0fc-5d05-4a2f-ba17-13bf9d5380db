export const LINE_BAR_METRIC = ['date',
  {
    name: 'mau',
    sum: true,
    as: 'MAU', // -- 游戏月活用户数|游戏付费用户数
  }, {
    name: 'download',
    sum: true,
    as: 'Downloads', // -- 游戏下载量
  }, {
    name: 'revenue',
    sum: true,
    as: 'Revenue', // -- 收入规模
  }, {
    name: 'arpu',
    sum: true,
    as: 'ARPU', // -- 用户人均付费能力
  }];

export const INIT_CON_OBJ = {
  game: '',
  regionInputList: [],
  countryInputList: [],
  categoryInputList: [],
  platformInputList: '', // 默认页面的查询条件，需要暂存
  dateInputList: [],
};

// const
export const osGen = {
  metricKeys: ['entity_type'], // -- 平台设备
  group: ['entity_type'],
  order: ['entity_type'],
  pageSize: 500,
  pageNum: 0,
};
export const catGen = {
  metricKeys: ['category'],
  group: ['category'],
  order: ['category'],
  pageSize: 500,
  pageNum: 0,
};
export const date = {
  // -- 日期
  metricKeys: ['date'],
  group: ['date'],
  order: [{ order: 'DESC', by: 'date' }],
  pageSize: 500,
  pageNum: 0,
};

export const TABLE_METRIC = [
  // attr
  'date',
  {
    name: 'region', // -- 区域缩写
    as: 'region_abbre',
  }, {
    name: 'country', // -- 国家缩写
    as: 'country_abbre',
  },
  'category', // -- 主品类
  'entity_type', // -- 平台设备
  // metric
  {
    name: 'mau',
    sum: true,
    as: 'mau', // -- 游戏月活用户数|游戏付费用户数
  }, {
    name: 'download',
    sum: true,
    as: 'download', // -- 游戏下载量
  }, {
    name: 'revenue',
    sum: true,
    as: 'revenue', // -- 收入规模
  }, {
    name: 'arpu',
    sum: true,
    as: 'arpu', // -- 用户人均付费能力
  }];
