import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useWatchGameChange } from 'common/compose/request/game';
import { useLoading } from 'common/compose/loading';
import { reactive, ref } from 'vue';
// modal && type
import { CountryListModal, WhereModal } from '../common.d';
import { INIT_CON_OBJ, LINE_BAR_METRIC, TABLE_METRIC, catGen, date, osGen } from './trend.d';
import { CategoryResponse, DateResponse, EntityTypeResponse, GetCountryConditionRequestModal, GetCountryResponseModal, GetFilterRequestModal, MetricKeys } from 'common/service/intelligence/common/common.d';
import { GetTrendDataResponseModal } from 'common/service/intelligence/market/trend/trend.d';
import { ITableCols } from 'common/components/table/type';
import { columnsConfig, inStringCond, toOption } from '../common';
// service
import { getAllCountry } from 'common/service/intelligence/common/common';
import { getTrendCountry, getFilter, getConfig, getData, getLineBar } from 'common/service/intelligence/market/trend/trend';
import { COUNTRY_METRIC } from '../const';

export const useIntelligenceMarketTrendStore = defineStore(
  STORE_KEY.INTELLIGENCE.MARKET.TREND,
  () => {
    const { isLoading, hideLoading, showLoading } = useLoading();
    const gameStore = useGlobalGameStore();
    const tableLoading = ref(false);
    const tableColumns = reactive<{ displayColumns: string[], columns: ITableCols[] }>({
      displayColumns: [],
      columns: [],
    });

    const payload = reactive<{
      countryList: GetCountryResponseModal['default'],
      conditionCountry: GetCountryResponseModal['default'],
      lineBarData: {
        label: string;
        value: number | string;
        date: string;
      }[],
      saveRegionInputList: string[],
      initConditionObj: {
        regionInputList: string[];
        countryInputList: string[];
        categoryInputList: string[];
        platformInputList: string;
        dateInputList: string[];
      },
      conditionObj: {
        regionInputList: string[];
        categoryInputList: string[];
        countryInputList: string[];
        platformInputList: string;
        dateInputList: string[];
      },
      option_obj: {
        categoryList: string[],
        platformList: { label: string; value: string; }[],
        dateList: string[],
        saveRegionInputList: any[],
        saveCountryInputList: any[],
      },
    }>({
      countryList: [],
      conditionCountry: [],
      lineBarData: [],
      saveRegionInputList: [],
      initConditionObj: {
        regionInputList: [],
        categoryInputList: [],
        countryInputList: [],
        platformInputList: '',
        dateInputList: [],
      },
      conditionObj: {
        regionInputList: [],
        countryInputList: [],
        platformInputList: '',
        categoryInputList: [],
        dateInputList: [],
      },
      option_obj: {
        categoryList: [],
        platformList: [],
        dateList: [],
        saveRegionInputList: [],
        saveCountryInputList: [],
      },
    });

    const table = reactive({
      records: [] as GetTrendDataResponseModal['total_record'] |
      GetTrendDataResponseModal['default'],
      pageInfo: {
        pageSize: 10,
        pageIndex: 1,
        total: 0,
      },
    });

    async function getTableConfig() {
      const data = await getConfig({ game: gameStore.gameCode });
      return data;
    }

    async function getCountry() {
      const data = await getAllCountry({ game: gameStore.gameCode });
      return data.default;
    }

    async function getMarketTrendCountry(metric: GetCountryConditionRequestModal) {
      const data = await getTrendCountry(metric);
      return data.default;
    }

    async function getMarketTrendLineBarData(metric: MetricKeys, where: WhereModal) {
      const gen = {
        metricKeys: metric,
        where,
        group: ['date'],
        order: ['date'],
        pageSize: 500,
        pageNum: 0,
      };
      const data = await getLineBar({ gen });
      return data.default;
    }

    async function getMarketTrendFilter<T>(metric: GetFilterRequestModal['gen']) {
      const data = await getFilter<T>({ gen: metric });
      return data.default;
    }

    async function getMarketTrendTable(metric: (string | {
      name: string;
      as: string;
      sum?: boolean;
    })[], where: WhereModal, page: {
      pageSize: number;
      pageIndex: number;
    }) {
      const { pageSize, pageIndex } = page;
      const gen = {
        metricKeys: metric,
        where,
        group: ['date', 'region', 'country', 'category', 'entity_type'],
        order: [{ order: 'DESC', by: 'date' }, { order: 'ASC', by: 'region' }, { order: 'ASC', by: 'country' }],
        pageSize,
        pageNum: pageIndex - 1,
      };
      const data = await getData({ gen, ext: { isGenTotalRecord: true, isGenTotalNum: true } });
      return {
        totalNum: data.total_record, // 第一条总数
        tableValue: data.default.map((one: { date: string; }) => ({
          ...one,
          date: one.date.split('-').join('')
            .slice(0, 6),
        })),
        pages: data.total_num[0],
      };
    }

    const configData = (data: any) => data.flatMap((entry: any) => {
      const { date } = entry;
      return Object.entries(entry).filter(([key]) => key !== 'date')
        .map(([label, value]: [any, any]) => ({
          date: date.split('-')[0] + date.split('-')[1],
          label,
          value: isNaN(parseFloat(value)) ? value : parseFloat(value),
        }));
    });

    const countryMapModification = (countryList: CountryListModal[], array: any[]) => {
      const countryMap: { [key: string]: string } = {};
      const regionMap: { [key: string]: string } = {};

      countryList.forEach((item) => {
        regionMap[item.region_abbre] = item.region_en;
        countryMap[item.country_abbre] = item.country_en;
      });
      return array.map((item, index) => ({
        ...item,
        index,
        region_en: regionMap[item.region_abbre],
        country_en: countryMap[item.country_abbre],
      }));
    };

    // init
    const init = async () => {
      useWatchGameChange(async () => {
        try {
          showLoading();
          const [categoryList, platformList, dateList]:
          [CategoryResponse['default'], EntityTypeResponse['default'], DateResponse['default']] = await Promise.all([
            getMarketTrendFilter<'category'>(catGen),
            getMarketTrendFilter<'entity_type'>(osGen),
            getMarketTrendFilter<'date'>(date),
          ]);
          const modifyPlatformList = platformList.map((e) => {
            const value = e.entity_type;
            if (value === 'pc') {
              return { label: value.toLocaleUpperCase(), value };
            }
            return { label: value.slice(0, 1).toLocaleUpperCase() + value.slice(1), value };
          });

          const modifyCategoryList = toOption(categoryList, 'category');
          const modifyDateList = toOption(dateList, 'date').map(({ label = '', value = '' }) => ({
            label: label.split('-').join('')
              .slice(0, 6),
            value,
          }));
          // 为了拿取linebar数据
          const date12 = modifyDateList.map((one: { value: any; }) => one.value).slice(0, 12);
          const config = {
            market_type: 'country', region: [], country: [], entity_type: [modifyPlatformList.find(item => item.value === 'mobile')?.value ?? modifyPlatformList[0].value], category: [], date: date12,
          };
          const where = Object.keys(config).map(k => inStringCond(k, config[k as keyof typeof config]));

          const [countryList, condition, lineAndBarData] = await Promise.all([
            getCountry(),
            getMarketTrendCountry(COUNTRY_METRIC),
            getMarketTrendLineBarData(LINE_BAR_METRIC, where),
          ]);
          const conditionCountry = countryMapModification(countryList, condition);

          const saveRegionInputList = Array.from(new Set(conditionCountry.map(({ region_abbre = '' }) => region_abbre)));

          const conditionObj = {
            ...INIT_CON_OBJ,
            regionInputList: saveRegionInputList,
            countryInputList: conditionCountry.map(({ country_abbre = '' }) => country_abbre),
            categoryInputList: getValueFrMap(modifyCategoryList),
            platformInputList: modifyPlatformList.find(item => item.value === 'mobile')?.value ?? modifyPlatformList[0].value,
            dateInputList: getValueFrMap(modifyDateList).slice(0, 12),
          };
          payload.option_obj = {
            categoryList: modifyCategoryList,
            platformList: modifyPlatformList,
            dateList: modifyDateList,
            saveRegionInputList,
            saveCountryInputList: conditionCountry,
          };
          payload.lineBarData = configData(lineAndBarData);
          payload.countryList = countryList;
          payload.conditionCountry = conditionCountry;
          payload.initConditionObj = { ...conditionObj };
          payload.conditionObj = { ...conditionObj };

          await getTable(
            saveRegionInputList, conditionObj.categoryInputList, conditionObj.countryInputList,
            { pageSize: 10, pageIndex: 1 }, conditionObj.dateInputList, modifyPlatformList.find(item => item.value === 'mobile')?.value ?? modifyPlatformList[0].value,
          );
        } catch (error) {
          // Handle errors here
        } finally {
          hideLoading();
        }
      });
    };

    // get Table Data
    const getTable = async (
      saveRegionInputList: string[], categoryList: string[], saveCountryInputList: string[],
      page: { pageSize: number, pageIndex: number }, dateList: string[], platform: string,
    ) => {
      tableLoading.value = true;
      // get table data
      const {
        regionInputList = [],
        countryInputList = [],
        categoryInputList = [],
      } = payload.initConditionObj;

      const { attrList = [], metricList = [] } = await getTableConfig();
      tableColumns.displayColumns = [...attrList.map((list: { key: any; }) => list.key),
        ...metricList.map((list: { key: any; }) => list.key)];
      tableColumns.columns = columnsConfig(attrList, metricList, ['arpu']);

      const config = {
        market_type: 'country',
        region: regionInputList.length === saveRegionInputList.length ? [] : regionInputList,
        country: countryInputList.length === saveCountryInputList.length ? [] : countryInputList,
        entity_type: platform,
        category: categoryInputList.length === categoryList.length ? [] : categoryInputList,
        date: dateList,
      };
      const where = Object.keys(config).map(k => inStringCond(k, config[k as keyof typeof config]));
      const { tableValue, totalNum, pages } = await getMarketTrendTable(TABLE_METRIC, where, page);
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { total_size = '' } = pages;
      let addTotalNum;
      if (page.pageIndex === 1) {
        addTotalNum = totalNum?.map(one => ({
          ...one,
          date: 'Total',
        }))?.concat(tableValue as any);
      } else {
        addTotalNum = tableValue;
      }
      // eslint-disable-next-line @typescript-eslint/naming-convention
      table.pageInfo = { ...page, total: parseInt(total_size, 10) };
      table.records = countryMapModification(payload.countryList, addTotalNum as any);
      tableLoading.value = false;
    };

    // get Filter Data
    const getFilterData = async (
      country: string[], date: string[],
      region: string[], platform: string, category: string[],
    ) => {
      showLoading();
      const { regionInputList, countryInputList, categoryInputList, dateInputList } = payload.initConditionObj;
      const pageInfo = {
        pageSize: 10,
        pageIndex: 1,
        total: 0,
      };

      table.pageInfo = pageInfo;

      const configFilter = {
        market_type: 'country',
        region: region.length === regionInputList.length ? [] : region,
        country: country.length === countryInputList.length ? [] : country,
        entity_type: platform,
        category: category.length === categoryInputList.length ? [] : category,
        date: date.length === 0 ? dateInputList : date,
      };

      const where = Object.keys(configFilter).map(k => inStringCond(k, configFilter[k as keyof typeof configFilter]));

      const [lineBarData, { tableValue, totalNum, pages }] = await Promise.all([
        getMarketTrendLineBarData(LINE_BAR_METRIC, where),
        getMarketTrendTable(TABLE_METRIC, where, table.pageInfo),
      ]);

      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { total_size = '' } = pages;

      const addTotalNum = pageInfo.pageIndex === 1
        ? totalNum?.map(one => ({ ...one, date: 'Total' }))?.concat(tableValue as any)
        : tableValue;

      table.records = countryMapModification(payload.countryList, addTotalNum as any);;
      // eslint-disable-next-line @typescript-eslint/naming-convention
      table.pageInfo = { ...table.pageInfo, total: parseInt(total_size, 10) };
      payload.lineBarData = configData(lineBarData);
      payload.conditionObj = {
        regionInputList: region.length === 0 ? regionInputList : region,
        platformInputList: platform,
        countryInputList: country.length === 0 ? countryInputList : country,
        categoryInputList: category.length === 0 ? categoryInputList : category,
        dateInputList: date.length === 0 ? dateInputList : date,
      };
      hideLoading();
    };

    /** 对于多选下拉列表如果选择了全部时可以转换为空数组传递到后台 **/
    const getValueFrMap = (arr = []) => arr.map(({ value }) => value);

    return {
      init,
      isLoading,
      payload,
      table,
      tableLoading,
      tableColumns,
      getTable,
      getFilterData,
      getCountry,
      hideLoading,
      showLoading,
    };
  },
);
