import { computed, reactive, ref, watch } from 'vue';
import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { Router, useRouter } from 'vue-router';
import { cloneDeep } from 'lodash-es';

// ---------------------------- Service ------------------------------
import { getCompetitorByGameCode, createGameId, searchGameByKeyword, getCompetitorConfig, getLtv, getAnalyzeLtv, saveVersion, createGroup, updateGroup, deleteGameById, getCompetitorByGameID, updateGameCompetitor, getAnalyzeLtv2 } from 'common/service/intelligence/prediction/prediction';

// ---------------------------- 参数 ------------------------------
import { worldCodeAlias as worldCode } from 'common/components/WorldMap/const';
import { InitialAllData, MODE_DOWNLOAD, OVERVIEW, ANALYZE } from '@/views/intelligence/prediction/const/const';

// ---------------------------- Type ------------------------------
import { ICustomViewItem } from 'common/components/NewViewTab/type';
import { CompetitorListGroupContentModal, Ltv, SaveGameVersionModal, CreateOrUpdateGroupModal, AllData, CompetitorExportListModal } from '@/views/intelligence/prediction/modal/prediction';

// ---------------------------- Util ------------------------------
import { useLoading } from 'common/compose/loading';


export const useIntelligencePredictionStore = defineStore(STORE_KEY.INTELLIGENCE.PREDICTION, () => {
  const { isLoading, hideLoading, showLoading } = useLoading();
  let router = useRouter();
  const allData = ref<AllData>(cloneDeep(InitialAllData)); // 全局数据
  const selectedMode = ref();
  const selectedDay = ref();
  const isDataLoading = ref(true);
  const userSearchSelectedStatus = ref<boolean>(false); // 搜索被点击的状态，用于记录空数据还是items被选完了
  const confirmResetCount = ref(0);
  const evaluationEditStatus = ref(false);

  // ---------------------------- Form ------------------------------
  // Const
  // 获取全局的国家选项
  const countryOptions = computed(() => getCountryOptions(allData.value.Market));
  const defaultCountryOptions = computed(() => handleMarketValue(allData.value.Market));

  // 从全局数据里提取区域及国家相关的数据，转换成下拉菜单所需的格式
  function getCountryOptions(data: [string, string][]) {
    const groupedData: { [key: string]: { label: string; value: string }[] } = {};

    data.forEach(([region, country]) => {
      if (!groupedData[region]) {
        groupedData[region] = [];
      }
      groupedData[region].push({
        label: getCountryFullName(country as keyof typeof worldCode),
        value: country,
      });
    });

    const transformedData = Object.keys(groupedData).map(region => ({
      label: region.replace('_', ' '),
      value: region,
      children: groupedData[region],
    }));

    return transformedData;
  }

  function handleMarketValue(data: [string, string][]): string[] {
    const res: string[] = [];
    data.forEach(([, country]) => {
      res.push(country);
    });
    return res;
  }

  // ---------------------------- Tab ------------------------------
  const tabList = ref<Array<ICustomViewItem>>([
    { value: OVERVIEW, label: OVERVIEW, game: '', param: {}, hidden: false, type: 'default' },
    { value: ANALYZE, label: ANALYZE, game: '', param: {}, hidden: false, type: 'default' },
  ]);
  const tabSelectId = ref<string>(tabList.value[0].value);
  const tabProps = reactive<Object>({
    modelValue: tabSelectId,
    list: tabList,
    showNum: 3,
    shareParams: {},
    hideSaveBtn: true,
    hideShareBtn: true,
    hideShareView: true,
    customIconList: [
      {
        iconName: 'setting',
        clickEvent: () => {
          router.push({
            path: '/intelligence/prediction/settings',
          });
        },
      },
    ],
    'onUpdate:modelValue': (newValue: typeof tabSelectId.value) => (tabSelectId.value = newValue),
  });

  // ---------------------------- API ------------------------------
  async function getCompetitorByCode(game: string) {
    showLoading();
    const data = await getCompetitorByGameCode({ game });
    return data;
  }

  async function getCompetitorById(id: number) {
    showLoading();
    const data = await getCompetitorByGameID({ id });
    hideLoading();
    return data;
  }

  async function createGameIDByGame(
    game: string,
    competitors: CompetitorListGroupContentModal[],
  ): Promise<any> {
    showLoading();
    const result = await createGameId({ game, competitors });
    hideLoading();
    return result;
  }

  async function updateGame(game_id: number, competitors: CompetitorExportListModal[]): Promise<any> {
    showLoading();
    const result = await updateGameCompetitor({ game_id, competitors });
    hideLoading();
    return result;
  }

  async function getCompetitorBySearch(search: string) {
    showLoading();
    const result = await searchGameByKeyword({ k: search, gameType: 'mobile' });
    hideLoading();
    return result;
  }

  async function getCompetitorConfigDetails() {
    showLoading();
    const result = await getCompetitorConfig();
    hideLoading();
    return result;
  }

  async function createOrUpdateGroup(data: CreateOrUpdateGroupModal, update: boolean) {
    if (!update) {
      const result = await createGroup(data);
      return result;
    }
    return await updateGroup(data);
  }

  async function saveGameVersion(data: SaveGameVersionModal) {
    showLoading();
    const result = await saveVersion(data);
    hideLoading();
    return result;
  }

  async function getLtvDetails(ltv: Ltv) {
    showLoading();
    const result = await getLtv(ltv);
    hideLoading();
    return result;
  }

  async function getAnalyzeLtvDetails(ltv: Ltv) {
    // showLoading();
    const result = await getAnalyzeLtv(ltv);
    // hideLoading();
    return result;
  }
  async function getAnalyzeLtvDetails2(ltv: Ltv) {
    // showLoading();
    const result = await getAnalyzeLtv2(ltv);
    // hideLoading();
    return result;
  }

  // ---------------------------- 公用函数 ------------------------------
  function getCountryFullName(code: keyof typeof worldCode): string {
    if (Object.prototype.hasOwnProperty.call(worldCode, code)) {
      return worldCode[code];
    }
    return '';
  }

  async function removeGameById(id: number) {
    showLoading();
    const result = await deleteGameById({ id });
    hideLoading();
    return result;
  }

  // 提取字符串中的纯数字
  const extractNumber = (str: any) => parseInt(str.replace(/\D/g, ''), 10);

  // 轴格式化处理
  function axisLabelFormat(value: any): any {
    return selectedMode.value === MODE_DOWNLOAD ? value.toLocaleString() : `$${value.toLocaleString()}`;
  }

  /**
   * 根据sortOrder来决定排序，默认逆序
   * asc=顺序；desc=逆序
   * @param arr
   * @param sortOrder
   * @returns
   */
  function sortByValue(arr: any[], sortOrder = 'desc') {
    const sortedArr = arr.slice(); // 创建一个副本，避免修改原始数组
    sortedArr.sort((a: { value: number; }, b: { value: number; }) => {
      if (sortOrder === 'asc') {
        return a.value - b.value;
      }
      return b.value - a.value;
    });
    return sortedArr;
  }
  // ---------------------------- 初始化 ------------------------------
  // 待解决， Analze页面会调用三次
  async function init() {
    // allData.value = cloneDeep(initialAllData);
  };

  async function clearData() {
    allData.value = cloneDeep(InitialAllData);
  };

  // ---------------------------- 监控 ------------------------------
  watch(() => tabSelectId.value, (newV) => {
    router.push({
      path: `/intelligence/prediction/${newV}`,
    });
  });

  const setRouter = (routerObj: Router) => {
    router = routerObj;
  };

  return {
    init,
    clearData,
    getCompetitorByCode,
    saveGameVersion,
    updateGame,
    getCompetitorBySearch,
    createGameIDByGame,
    createOrUpdateGroup,
    getCompetitorById,
    getCompetitorConfigDetails,
    getLtvDetails,
    getAnalyzeLtvDetails,
    getAnalyzeLtvDetails2,
    removeGameById,
    isLoading,
    hideLoading,
    showLoading,
    isDataLoading,
    allData,
    confirmResetCount,
    getCountryFullName,
    countryOptions,
    getCountryOptions,
    defaultCountryOptions,
    selectedMode,
    selectedDay,
    tabSelectId,
    tabProps,
    extractNumber,
    axisLabelFormat,
    sortByValue,
    userSearchSelectedStatus,
    evaluationEditStatus,
    router,
    setRouter,
  };
});
