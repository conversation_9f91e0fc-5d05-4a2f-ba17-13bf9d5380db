import { defineStore } from 'pinia';
import { STORE_KEY } from '@/config/config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useLoading } from 'common/compose/loading';
import { getDateFilterOption, getUserCountry, getUserPieData } from 'common/service/intelligence/user/user';
import { PIE_METRIC_KEYS, PIE_VARIABLE, USER_COUNTRY_PARAM, USER_FILTER_DATE, inStringCond } from '@/views/intelligence/user/const/const';
import { GetPieRequestModal } from 'common/service/intelligence/user/user.d';
import { useWatchGameChange } from 'common/compose/request/game';
import { ref } from 'vue';
import { useIntelligenceCommonStore } from '../common.store';
import { PieDataModal } from '@/views/intelligence/user/modal/user';
import { getAllCountry } from 'common/service/intelligence/common/common';


export const useIntelligenceUserStore = defineStore(STORE_KEY.INTELLIGENCE.USER, () => {
  const { isLoading, hideLoading, showLoading } = useLoading();
  const commonStore = useIntelligenceCommonStore();
  const pieData = ref<PieDataModal>([]);
  const countryList = ref<{ label: string; value: string; }[]>([]);
  const dateList = ref<{ label: string; value: string; }[]>([]);
  const gameStore = useGlobalGameStore();
  const verifyAdmin = ref<boolean>(false);


  async function getPieChartData(config: { in_list: string[], name: string, type: number }[]) {
    showLoading();
    const request: GetPieRequestModal<string | object> = {
      gen: {
        metricKeys: PIE_METRIC_KEYS,
        where: config,
      },
    };
    const data = await getUserPieData(request);
    hideLoading();
    return data;
  }

  async function getCountryData() {
    const data = await getUserCountry(USER_COUNTRY_PARAM);
    return data;
  }

  async function getCountry() {
    const game = gameStore.gameCode;
    const data = await getAllCountry({ game });
    return data;
  }

  async function getDateOptions() {
    const data = await getDateFilterOption({ gen: USER_FILTER_DATE });
    await commonStore.init();
    const verifyAdmin = commonStore.isAdmin;

    return {
      isAdmin: verifyAdmin,
      list: data.default,
    };
  }

  // 计算功能
  const toOption = (list: {
    date: string;
  }[]) => list.map(one => ({
    text: one.date,
    value: one.date,
  }));


  // ---------------------------- 初始化 ------------------------------
  async function init() {
    useWatchGameChange(async () => {
      try {
        showLoading();
        // 日期过滤器
        const { list, isAdmin = false } = await getDateOptions();
        dateList.value = toOption(list).map(({ text = '', value = '' }) => ({
          label: text.toString(),
          value: value.toString(),
        }));

        verifyAdmin.value = isAdmin;
        const allCountry = await getCountry();
        const countryMap: Record<string, string> = {};
        allCountry.default.forEach((item) => {
          countryMap[item.country_abbre] = item.country_en;
        });

        const countryData = await getCountryData();
        // 国家过滤器
        countryList.value = countryData.default.map(item => ({
          label: countryMap[item.country_abbre],
          value: item.country_abbre,
        }));

        countryList.value.push(countryList.value.shift() ?? { label: '', value: '' });
        const [date] = dateList.value;
        const config = {
          country: countryList.value[0].value,
          variable: PIE_VARIABLE,
          date: date.value,
        };

        const result = await getPieChartData(Object.entries(config).map(([key, value]) => inStringCond(key, value)));
        if (result) {
          result.default.forEach((data: { share: number; }) => {
            // eslint-disable-next-line no-param-reassign
            data.share = (data.share * 100).toFixed(2) as any;
          });
          pieData.value = result.default;
        }
      } catch (error) {
        pieData.value = [];
        // Handle errors here
      } finally {
        hideLoading();
      }
    });
  }


  return {
    init,
    verifyAdmin,
    pieData,
    dateList,
    countryList,
    isLoading,
    getCountryData,
    getDateOptions,
    getPieChartData,
    hideLoading,
    showLoading,
  };
});
