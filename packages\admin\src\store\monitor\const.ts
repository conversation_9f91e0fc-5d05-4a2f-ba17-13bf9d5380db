export const TYPE_LIST = [
  {
    iconName: 'control-svgrepo-com-cropped',
    value: 'rules',
    label: 'Metric Issues',
    num: 0,
  },
  {
    iconName: 'advertising-svgrepo-com-cropped',
    value: 'ad_issues',
    label: 'Ad Issues',
    num: 0,
  },
  {
    iconName: 'account-svgrepo-com-cropped',
    value: 'account',
    label: 'Account Issues',
    num: 0,
  },
  // {
  //   iconName: 'ai-svgrepo-com-cropped',
  //   value: 'ai_optimization',
  //   label: 'AI Optimization',
  //   num: 0,
  // },
];

export const SETTING_LIST = [
  {
    name: 'Account Issues',
    description: 'Account suspension',
    type: 'account',
    showViewMore: false,
  },
  {
    name: 'Ad Issues',
    description: 'Ad Issues from media channels',
    type: 'ad_issues',
    showViewMore: false,
  },
  {
    name: 'Rules',
    description: 'Rule notification',
    type: 'rules',
    showViewMore: true,
  },
  // {
  //   name: 'AI Optimization',
  //   description: 'Optimization suggestions',
  //   type: 'ai_optimization',
  //   showViewMore: true,
  // },
];

export const MediaMap: { [key: string]: string} = {
  google: 'Google',
  facebook: 'Facebook',
  tiktok: 'TikTok',
  twitter: 'Twitter',
  asa: 'Apple Search Ads',
};

export const TIMERANGEOPTIONS = [
  {
    label: 'Daily',
    value: '1',
  },
  {
    label: '3 Days',
    value: '3',
  },
];
export const REPORTPAGEID = {
  google: '0204',
  facebook: '0205',
  tiktok: '0206',
  twitter: '0207',
  snapchat: '0208',
  asa: '0209',
};
export const subTypeMapRuleType: { [key: string]: string } = {
  campaign: 'ad_issues',
  adgroup: 'ad_issues',
  ad: 'ad_issues',
  account: 'account',
  keyword: 'ad_issues',
  change: 'rules',
  target: 'rules',
  asset_name: 'ad_issues',
};
