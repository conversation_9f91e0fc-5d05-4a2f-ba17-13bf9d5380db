import { STORE_KEY } from '@/config/config';
import { defineStore, storeToRefs } from 'pinia';
import { ref, reactive, computed, watch } from 'vue';
import type { Notification, Unread } from './type';
import {
  getNotifications, getUnread, setUnreadMsgToRead, readAllNotificationMsg, getSeeClient, getMonitorSetting,
} from 'common/service/monitor/get';
import Cookies from 'js-cookie';
import { useRoute } from 'vue-router';
import { transTimeByZone } from 'common/utils/format';
import { ItableRow } from '@/views/trade/ads_management/type';
import { reportCommonClick } from '@/report';
import { REPORTPAGEID, subTypeMapRuleType } from './const';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useRouterStore } from '@/store/global/router.store';
import dayjs from 'dayjs';

export const useNotificationStore = defineStore(STORE_KEY.TD.MONITOR.NOTIFICATION, () => {
  const username = Cookies.get('aix-username') as string;
  const isShowMask = ref<boolean>(false);
  const isShowMessageBox = ref<boolean>(false);
  let sseClient: EventSource;
  const currentRoute = useRoute();
  const media = computed(() => (currentRoute?.meta?.media as string || '').toLowerCase());
  const { gameCode } = storeToRefs(useGlobalGameStore());
  const { allowRoutes } = storeToRefs(useRouterStore());


  startSse();

  const notParams = reactive({
    username,
    page_size: 20,
    type: 'rules',
    media: '',
    date_type: 'lastDate',
  });
  const unreadData = ref<Unread>({
    account: 0,
    ad_issues: 0,
    rules: 0,
    ai_optimization: 0,
  });
  const unreadHistoryData = ref<Unread>({
    account: 0,
    ad_issues: 0,
    rules: 0,
    ai_optimization: 0,
  });
  const unreadList = ref<Notification[]>([]);

  const notificationList = ref<Notification[]>([]);
  const allMetricList = ref<Notification[]>([]);
  const notLoading = ref(false);
  const isEnd = ref(false);
  const lastId = ref('');

  const historyNotificationList = ref<Notification[]>([]);
  const historyNotLoading = ref(false);
  const historyIsEnd = ref(false);
  const historyListId = ref('');
  // 展示detail
  const detailItem = ref<Notification | undefined>();
  const isNotOpenSetting = ref<boolean>(false);

  const isInGcpEnv = ref<boolean>(location.host.includes('aix.levelinfinite.com'));
  // const isInGcpEnv = ref<boolean>(location.host.includes('aix.levelinfinite.com')
  // || location.host.includes('local.aix.intlgame.com'));
  const isInDemoGame = computed(() => gameCode.value.includes('demo'));
  const isShowTotalMessageBox = computed(() => {
    // 通过路由菜单控制
    const isHasMonitorMenu = allowRoutes.value[gameCode.value]?.find(item => item === 'Trading Desk / Monitor / Setting');
    if (isHasMonitorMenu && currentRoute.path.includes('/trade/management') && !currentRoute.path.includes('/create')) {
      return true;
    }
    return false;
  });

  const isRedPointer = ref(false);

  // 获取全量当天数据
  async function getCurMetricAllData() {
    const res = await getNotifications({
      ...notParams,
      type: 'rules',
      date_type: 'lastDate',
      page_size: 99999,
    });
    allMetricList.value = res.list;
  }

  async function getNotificationList(lastIdVal = '') {
    notLoading.value = true;
    isNotOpenSetting.value = false;
    lastId.value = lastIdVal;
    notParams.media = media.value;
    const params = {
      ...notParams,
      last_id: lastIdVal,
      date_type: 'lastDate',
    };
    if (!lastIdVal) {
      isEnd.value = false;
    }
    if (isEnd.value) {
      notLoading.value = false;
      return;
    }
    const res = await getNotifications(params);
    notLoading.value = false;
    if (lastIdVal) {
      notificationList.value = [...notificationList.value, ...res.list];
    } else {
      notificationList.value = res.list || [];
    }
    notificationList.value = sortByDate(notificationList.value);
    if (!detailItem.value && notificationList.value.length) {
      if (isShowMessageBox.value) {
        setDetail(notificationList.value[0]);
      } else {
        [detailItem.value] = notificationList.value;
      }
    }
    if (params.date_type === 'lastDate' && notificationList.value.length === 0) {
      isAllSettingsNotOpen(params.media);
    }
    isEnd.value = res.is_end;
  }

  async function isAllSettingsNotOpen(media: string) {
    const { list: data }  = await getMonitorSetting(media);
    const isAllDisabled = data.every(item => !item.enable);
    isNotOpenSetting.value = isAllDisabled;
  }

  async function getHistoryNotificationList(lastId = '') {
    historyNotLoading.value = true;
    historyListId.value = lastId;
    notParams.media = media.value;
    const params = {
      ...notParams,
      last_id: lastId,
      date_type: 'history',
    };
    // 先写死
    if (!lastId) {
      historyIsEnd.value = false;
    }
    if (historyIsEnd.value) {
      historyNotLoading.value = false;
      return;
    }
    const res = await getNotifications(params);
    historyNotLoading.value = false;
    if (lastId) {
      historyNotificationList.value = [...historyNotificationList.value, ...res.list];
    } else {
      historyNotificationList.value = res.list || [];
    }
    historyNotificationList.value = sortByDate(historyNotificationList.value);
    if (historyNotificationList.value.length && notParams.date_type === 'history') {
      setDetail(historyNotificationList.value[0]);
    }

    historyIsEnd.value = res.is_end;
  }

  function sortByDate(list: Notification[]) {
    if (notParams.type !== 'rules') {
      return list;
    }
    // 通过date日期分组，然后根据spend字段降序
    const groupByDate: { [key: string]: Notification[]} = {};
    list.forEach((item) => {
      const { date } = item;
      if (!groupByDate[date]) {
        groupByDate[date] = [];
      }
      groupByDate[date].push(item);
    });
    const finalList: Notification[] = [];
    Object.values(groupByDate).forEach((listByDate: Notification[]) => {
      const list = listByDate.sort((a, b) => Number(b.ext.spend) - Number(a.ext.spend));
      finalList.push(...list);
    });
    return finalList;
  }

  async function getUnreadData(isInit?: boolean) {
    const res = await getUnread({
      username,
      media: media.value,
    });
    unreadHistoryData.value = {
      account: 0,
      ad_issues: 0,
      rules: 0,
      ai_optimization: 0,
    };
    unreadData.value = res.unread_type;
    unreadList.value = res.list;
    const targetItems = res.list.filter((item: { media: string}) => item.media === media.value) as Notification[];
    const now = dayjs(new Date()).format('YYYY-MM-DD');
    const isInGcpDemo = isInGcpEnv.value && isInDemoGame.value;
    let latestTime: string | null = null;
    if (isInGcpDemo) {
      targetItems.forEach((notificationItem) => {
        if (!latestTime) {
          latestTime = notificationItem.time;
        } else {
          const isAfter = dayjs(notificationItem.time).isAfter(latestTime);
          if (isAfter) {
            latestTime = notificationItem.time;
          }
        }
      });
      if (latestTime) {
        latestTime = `${(latestTime as string).substring(0, 10)} 00:00:00`;
      }
    };
    targetItems
      .filter((item) => {
        if (isInGcpDemo) {
          return dayjs(item.time).isBefore(latestTime);
        }
        return new Date(item.time).getTime() - new Date(`${now} 00:00:00`).getTime() < 0;
      })
      .forEach((item) => {
        unreadHistoryData.value[item.type] += 1;
      });

    if (isInit && targetItems.length > 0) {
      report({
        optid: `${REPORTPAGEID[media.value as keyof typeof REPORTPAGEID]}0301`,
      });
    }
  }

  watch(() => [media.value, gameCode.value], () => {
    if (!media.value || !isShowTotalMessageBox.value) {
      unreadList.value = [];
      return;
    };
    lastId.value = '';
    historyListId.value = '';
    detailItem.value = undefined;
    if (media.value === 'asa') notParams.type = 'rules';
    const { subtype } = currentRoute.query;
    const notificationType = subTypeMapRuleType[subtype as string];
    if (subtype && notificationType) {
      notParams.type = notificationType;
      notParams.date_type = 'lastDate';
      detailItem.value = undefined;
      setMessageBoxVisible(true);
    }
    getNotificationList();
    getHistoryNotificationList();
    getUnreadData(true);
    getCurMetricAllData();
  }, {
    immediate: true,
  });

  const changeNotType = (val: string) => {
    notificationList.value = [];
    historyNotificationList.value = [];
    notParams.type = val;
    notParams.date_type = 'lastDate';
    detailItem.value = undefined;
    getNotificationList();
    getHistoryNotificationList();
  };


  async function setDetail(item: Notification, dataType?: string) {
    // z展示detail的同时 要设置为已读
    detailItem.value = item;
    if (!item.is_read) {
      setRead(item, dataType);
    }
  }
  async function setRead(item: Notification, dataType?: string) {
    if (isInGcpEnv.value  && isInDemoGame.value) {
      const list = unreadList.value;
      const index = list.findIndex(i => i.notification_id === item.notification_id);
      list.splice(index, 1);
      unreadList.value = list;
      const currNum = unreadData.value[notParams.type] - 1;
      unreadData.value[notParams.type] = currNum > 0 ? currNum : 0;
      if (dataType === 'history') {
        const currHistoryNum = unreadHistoryData.value[item.type] - 1;
        unreadHistoryData.value[item.type] = currHistoryNum > 0 ? currHistoryNum : 0;
      }
      return;
    }
    await setUnreadMsgToRead({
      notification_id: item.notification_id,
      receiver: username,
    });
    // 重新获取当前消息列表和未读消息
    getUnreadData();
  }
  async function setAllReadMsg(type: string) {
    // z展示detail的同时 要设置为已读
    await readAllNotificationMsg(media.value, type);
    // 重新获取当前消息列表和未读消息
    getUnreadData();
  }
  // 关闭detail
  function closeDetail() {
    detailItem.value = undefined;
  }

  function setShowMask(val: boolean) {
    isShowMask.value = val;
  }
  function setMessageBoxVisible(val: boolean) {
    isShowMessageBox.value = val;
    if (val && detailItem.value) {
      setDetail(detailItem.value);
    }
  }

  function startSse() {
    return;
    if (sseClient) {
      sseClient.close();
    }
    sseClient = getSeeClient();
    // 接受实时消息
    sseClient.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (!data.newData) return;
      // 新消息类型和渠道与当前列表类型一致，加入到第1个
      if (data.newData.type === notParams.type && data.newData.media === media.value) {
        data.newData.local_time = transTimeByZone(data.newData.time, 'YYYY-MM-DD HH:mm:ss');
        notificationList.value = [data.newData].concat(notificationList.value);
        notificationList.value = sortByDate(notificationList.value);
      }
      getUnreadData();
    };
  }

  // 用当天的数据，判断campaign是否异常
  function isAbnormal(row: ItableRow) {
    return allMetricList.value.find(item => item.ext?.campaign_id === row.campaign_id);
  }

  function report(reportParams: any) {
    const pageId = REPORTPAGEID[media.value.toLocaleLowerCase() as keyof typeof REPORTPAGEID];
    const params = {
      ext10: notParams.type,
      ext11: pageId,
      ...reportParams,
    };
    reportCommonClick(`${pageId}${reportParams.optid || ''}`, (currentRoute?.name as string) || '', params);
  }

  return {
    notParams,
    isShowMask,
    isShowMessageBox,
    notificationList,
    allMetricList,
    historyNotificationList,
    detailItem,
    unreadData,
    unreadHistoryData,
    unreadList,
    notLoading,
    historyNotLoading,
    setDetail,
    closeDetail,
    setShowMask,
    setMessageBoxVisible,
    getNotificationList,
    getHistoryNotificationList,
    changeNotType,
    setAllReadMsg,
    isAbnormal,
    report,
    isNotOpenSetting,
    isRedPointer,
    isInGcpEnv,
    isShowTotalMessageBox,
  };
});

