import { STORE_KEY } from '@/config/config';
import { CampaignItem, type RuleItem } from './type';
import { defineStore } from 'pinia';
import { reactive, ref } from 'vue';
import { getMonitorRules, setMonitorRules, getMedias, getCampaignList } from 'common/service/monitor/get';
import { MessagePlugin } from 'tdesign-vue-next';
import { MediaMap } from '@/store/monitor/const';
import { isShowMetric } from './tool';


export const useRulesStore = defineStore(STORE_KEY.TD.MONITOR.RULES, () => {
  const params = reactive<{
    pageIndex: number,
    pageSize: number,
    campaign_name?: string | undefined,
    maxPage: number,
  }>({
    pageIndex: 0,
    pageSize: 10,
    campaign_name: undefined,
    maxPage: Number.POSITIVE_INFINITY,
  });
  const total = ref(0);

  const rules = ref<{name: string, list: RuleItem[]}[]>([]);
  const totalRuleIds = ref<number[]>([]);
  const rowSpan = ref<{[key: string]: number}>({});

  const curMedia = ref<string>('');
  const realMediaList = ref<string[]>([]);

  const initSelectObjectives = ref<CampaignItem[]>([]);
  const objectiveOptions = ref<CampaignItem[]>([]);
  const isLoadingObjetive = ref(false);
  const ruleEditStatus = ref<{[key: string]: boolean}>({});

  const setMedia = (media: string) => {
    curMedia.value = media;
    getRules();
  };

  const getMediaList = async () => {
    const games = await getMedias();
    realMediaList.value = games.filter(game => !!MediaMap[game]);
    curMedia.value = curMedia.value || realMediaList.value[0];
    getRules();
  };


  async function getRules() {
    rowSpan.value = {};
    totalRuleIds.value = [];
    initSelectObjectives.value = [];
    const data = await getMonitorRules({
      page_index: 1,
      page_size: 100,
      media: curMedia.value,
    });
    data.list = data.list.map(item => ({
      ...item,
      range: item.range ? Number(item.range) : item.range,
    })).filter(item => isShowMetric(item.index));

    const selectedCampaignIds = [];
    const groupList: {name: string, list: RuleItem[] }[] = [];
    const tempObj: {[key: string]: RuleItem[]} = {};
    for (const item of data.list) {
      if (!tempObj[item.name]) {
        tempObj[item.name] = [];
      }
      tempObj[item.name].push(item);
      if (item.object_campaign[0] !== 'all') {
        selectedCampaignIds.push(...item.object_campaign);
      }
      totalRuleIds.value.push(item.rule_status_id);
      item.target_val = item.target_val ? Number(item.target_val) : item.target_val;
      item.absRange = item.range !== null && item.range !== undefined ? Math.abs(parseFloat(`${item.range}`)) : item.range;
    }
    Object.keys(tempObj).forEach((key) => {
      groupList.push({
        name: key,
        list: tempObj[key],
      });
    });
    rules.value = groupList;
    total.value = +data.total;
    if (selectedCampaignIds.length) {
      const result = await getCampaigns({ campaign_ids: selectedCampaignIds }, false, false);
      if (result) {
        initSelectObjectives.value = result;
      }
    }
    if (!Object.values(ruleEditStatus.value).filter(item => item).length) {
      ruleEditStatus.value[groupList[0].name] = true;
    }
  }


  async function getCampaigns(
    inputParams: { campaign_ids?: string[]},
    needTotalNum: boolean,
    isNeedNoDataTip = true,
  ) {
    if (params.pageIndex > params.maxPage && isNeedNoDataTip) {
      MessagePlugin.closeAll();
      MessagePlugin.info('no more data!');
      return null;
    }
    if (isLoadingObjetive.value && needTotalNum) {
      return null;
    }
    if (needTotalNum) {
      isLoadingObjetive.value = true;
      params.pageIndex += 1;
    }
    const media = curMedia.value === 'asa' ? 'ASA' : MediaMap[curMedia.value];
    const { nums, list } = await getCampaignList({
      page_index: needTotalNum ? params.pageIndex : 1,
      page_size: needTotalNum ? params.pageSize : 1000,
      media,
      need_total_num: needTotalNum,
      campaign_name: params.campaign_name,
      campaign_ids: inputParams.campaign_ids,
    });
    isLoadingObjetive.value = false;
    const listTemp = list.map(item => ({
      ...item,
      label: item.campaign_name,
      value: item.campaign_id,
    }));
    if (needTotalNum) {
      params.maxPage = Math.ceil(nums / params.pageSize);
      objectiveOptions.value = [
        ...objectiveOptions?.value,
        ...listTemp,
      ];
    }
    return listTemp;
  };

  async function setRules(params: {
    rule_status_id: number[], enable?: Boolean, range?: number | null,
    time_range?: number, object_campaign?: string[], type?: string,
  }) {
    await setMonitorRules(params);
  }

  function reset() {
    rules.value = [];
    total.value = 0;
  }

  return {
    getMediaList,
    getRules,
    setRules,
    params,
    total,
    rules,
    curMedia,
    realMediaList,
    setMedia,
    rowSpan,
    reset,
    getCampaigns,
    initSelectObjectives,
    objectiveOptions,
    ruleEditStatus,
    totalRuleIds,
  };
});
