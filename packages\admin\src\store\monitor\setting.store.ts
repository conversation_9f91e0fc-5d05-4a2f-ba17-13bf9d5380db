import { STORE_KEY } from '@/config/config';
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { getMedias, getMonitorSetting, setMonitorSetting } from 'common/service/monitor/get';
import { SettingListItem, SettingStatusItem } from './type';
import { SETTING_LIST } from './const';
import { MessagePlugin } from 'tdesign-vue-next';
import { MediaMap } from '@/store/monitor/const';


export const useSettingStore = defineStore(STORE_KEY.TD.MONITOR.SETTING, () => {
  const settingList = ref<SettingListItem[]>([]);
  const curMedia = ref<string>('');
  const gameList = ref<string[]>([]);
  const isInitSetting = ref(false);
  const isInGcpEnv = ref<boolean>(location.host.includes('aix.levelinfinite.com'));


  const getMediaList = async () => {
    const games = await getMedias();
    gameList.value = games.filter(game => !!MediaMap[game]);
    setMedia(curMedia.value || gameList.value[0]);
  };

  const getSetting = async (media?: string) => {
    const { list: data, isInit } = await getMonitorSetting(media || curMedia.value);
    isInitSetting.value = isInit;
    settingList.value = SETTING_LIST.map((item) => {
      const targetList = data.filter(s => s.type === item.type);
      return {
        ...item,
        game_code: targetList[0].game_code,
        type: targetList[0].type,
        status: targetList.map(set => ({
          setting_status_id: set.setting_status_id,
          enable: set.enable,
          type: set.type,
        })),
      };
    });
    return settingList.value;
  };
  const updateSettings = async (
    enable: boolean,
    media: string,
    type: string,
    setting_status_ids: number[],
    is_init: boolean,
  ) => {
    await setMonitorSetting({
      setting_status_ids,
      type,
      media,
      enable,
      is_init,
    }).catch(err => err);
    MessagePlugin.success('Update success!');
  };

  const updateSetting = async (enable: boolean, statusItem: SettingStatusItem) => {
    console.log(enable, statusItem);
    return await setMonitorSetting({
      setting_status_ids: [statusItem.setting_status_id],
      type: statusItem.type,
      media: curMedia.value,
      enable,
      is_init: isInitSetting.value,
    }).catch(err => err);
  };

  const setMedia = async (media: string) => {
    curMedia.value = media;
    await getSetting();
  };

  return {
    gameList,
    settingList,
    curMedia,
    getMediaList,
    getSetting,
    setMedia,
    updateSetting,
    updateSettings,
    isInitSetting,
    isInGcpEnv,
  };
});
