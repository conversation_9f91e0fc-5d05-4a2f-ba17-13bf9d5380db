import { STORE_KEY } from '@/config/config';
import { defineStore } from 'pinia';
import { reactive, ref } from 'vue';
import { getMonitorTargets, setMonitorTargetVal } from 'common/service/monitor/get';
import type { TargetItem } from './type';
import { isShowMetric } from './tool';

export const useTargetsStore = defineStore(STORE_KEY.TD.MONITOR.TARGET, () => {
  const targetList = ref<TargetItem[]>([]);
  const params = reactive({
    pageIndex: 1,
    pageSize: 10,
  });

  async function getTargets() {
    const res = await getMonitorTargets({
      page_index: params.pageIndex,
      page_size: params.pageSize,
    });
    targetList.value = res.filter(item => isShowMetric(item.index));
  }

  async function setTargetVal(rule_id: number, target_val: string | null, type: string, apply_range: boolean) {
    return setMonitorTargetVal({ rule_id, target_val, type, apply_range });
  }

  return {
    targetList,
    getTargets,
    setTargetVal,
  };
});
