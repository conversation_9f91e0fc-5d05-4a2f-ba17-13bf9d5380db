import { useGlobalGameStore } from '@/store/global/game.store';
import { storeToRefs } from 'pinia';

export const isShowMetric = (indexKey: string) => {
  const { gameCfg, gameCode } = storeToRefs(useGlobalGameStore());
  const isInGcpEnv = location.host.includes('aix.levelinfinite.com');
  if (isInGcpEnv && gameCfg.value) {
    const { type } = gameCfg.value[gameCode.value];
    if (type === 'pc' && (indexKey.includes('Install') || indexKey.includes('CPI-A') || indexKey.includes('D1_Cohort_Register'))) {
      return false;
    }
  }
  return true;
};
