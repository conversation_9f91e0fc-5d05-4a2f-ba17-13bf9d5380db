export interface NotGetParams {
  username: string,
  page_size: number,
  type: string,
  date_type: string,
  last_id?: string
}

export interface UnreadGetParams {
  username: string
  media: string
}

export interface Notification {
  notification_id: string,
  game_code: string,
  title: string,
  media: string,
  content: string,
  reason: string,
  date: string,
  time: string,
  local_time: string,
  create_time: string,
  opt_id: string,
  is_read: boolean,
  type: string,
  ext: any,
}

export interface Unread {
  account: number,
  ad_issues: number,
  rules: number,
  ai_optimization: number,
  [key: string]: number,
}
export interface UnreadSetParams {
  notification_id: string,
  receiver: string,
}

export interface SettingItem {
  game_code: string,
  media: string,
  type: string,
  setting_id: number,
  setting_status_id: number,
  channel_type: string,
  enable: boolean,
}

interface SettingStatusItem {
  setting_status_id: number,
  enable: boolean,
  type: string,
}

export interface SettingListItem {
  name: string
  description: string,
  type: string,
  showViewMore: boolean,
  status: SettingStatusItem[],
}

export interface MonitorSettingParams {
  setting_status_ids: number[],
  enable: boolean,
  type: string,
  media: string,
  is_init?: boolean,
}

export interface RuleItem {
  rule_id: number,
  rule_status_id: number,
  media: string,
  is_percent: boolean,
  setting_status_id: string,
  name: string,
  status: boolean,
  range: string | number | null,
  receiver: string,
  enable: boolean,
  isEdit?: boolean,
  condition: string,
  desc: string,
  time_range: string,
  is_target: boolean,
  object_campaign: string[],
  target_val: number | null,
  type: string,
  absRange?: number | null,
  index: string,
}

export interface TargetItem {
  rule_id: number,
  name: string,
  game_code: string,
  index: string,
  range: string | null,
  target_val: string | null,
  type: string,
  condition: string
  is_percent: boolean,
  editable?: boolean,
}

export interface CampaignListParam {
  media: string,
  page_index: number,
  page_size: number,
  campaign_ids?: string[],
  campaign_name?: string,
  need_total_num: boolean,
}
export interface CampaignItem {
  campaign_name: string,
  campaign_id: string,
  aix_campaign_create_time: string,
  label?: string,
  value?: string,
}
