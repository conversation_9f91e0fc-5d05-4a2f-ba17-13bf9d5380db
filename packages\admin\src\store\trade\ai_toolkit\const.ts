export const LANGUAGE = [
  {
    lable: 'English',
    value: 'English',
  },
  {
    lable: 'Brazil Portuguese',
    value: 'Brazil Portuguese',
  },
  {
    lable: 'LATAM Spanish',
    value: 'LATAM Spanish',
  },
  {
    lable: 'Russian',
    value: 'Russian',
  },
  {
    lable: 'Turkish',
    value: 'Turkish',
  },
  {
    lable: 'German',
    value: 'German',
  },
  {
    lable: 'French',
    value: 'French',
  },
  {
    lable: 'Arabic',
    value: 'Arabic',
  },
  {
    lable: 'Indonesian',
    value: 'Indonesian',
  },
  {
    lable: 'Malay',
    value: 'Malay',
  },
  {
    lable: 'Thai',
    value: 'Thai',
  },
  {
    lable: 'Traditional Chinese',
    value: 'Traditional Chinese',
  },
  {
    lable: 'Vietnam',
    value: 'Vietnam',
  },
  {
    lable: 'Bengali',
    value: 'Bengali',
  },
];

export const THEME = [
  {
    label: '无',
    value: '无',
  },
  {
    label: 'aftermath',
    value: 'aftermath',
  },
  {
    label: 'alan walker',
    value: 'alan walker',
  },
  {
    label: 'a计划',
    value: 'a计划',
  },
  {
    label: 'baby shark',
    value: 'baby shark',
  },
  {
    label: 'blackpink',
    value: 'blackpink',
  },
  {
    label: 'eva',
    value: 'eva',
  },
  {
    label: 'gun',
    value: 'gun',
  },
  {
    label: 'ignition',
    value: 'ignition',
  },
  {
    label: 'line friend',
    value: 'line friend',
  },
  {
    label: 'livik',
    value: 'livik',
  },
  {
    label: 'nusa',
    value: 'nusa',
  },
  {
    label: '冰雪节',
    value: '冰雪节',
  },
  {
    label: '卡拉金',
    value: '卡拉金',
  },
  {
    label: '咒术回战',
    value: '咒术回战',
  },
  {
    label: '哥斯拉',
    value: '哥斯拉',
  },
  {
    label: '四周年',
    value: '四周年',
  },
  {
    label: '圣托里尼',
    value: '圣托里尼',
  },
  {
    label: '圣装',
    value: '圣装',
  },
  {
    label: '埃及',
    value: '埃及',
  },
  {
    label: '奖励',
    value: '奖励',
  },
  {
    label: '感染模式',
    value: '感染模式',
  },
  {
    label: '斋月',
    value: '斋月',
  },
  {
    label: '海岛',
    value: '海岛',
  },
  {
    label: '特斯拉',
    value: '特斯拉',
  },
  {
    label: '空中玩法',
    value: '空中玩法',
  },
  {
    label: '维寒迪',
    value: '维寒迪',
  },
  {
    label: '蜘蛛侠',
    value: '蜘蛛侠',
  },
  {
    label: '角色技能主题',
    value: '角色技能主题',
  },
  {
    label: '足球玩法',
    value: '足球玩法',
  },
  {
    label: '通用',
    value: '通用',
  },
  {
    label: '重火力',
    value: '重火力',
  },
];
