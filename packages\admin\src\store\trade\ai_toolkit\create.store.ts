import { STORE_KEY } from '@/config/config';
import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { SortInfo } from 'tdesign-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import type { CopyWriterForm, CopyWriteCopiesInner, CreateTextItem } from './type';
import { getCopyWrite as getCopyWriteService, createText } from 'common/service/td/ai_toolkit/smartCopywriter';
import { useLoading } from 'common/compose/loading';
import { v4 as uuidv4 } from 'uuid';
import { useId } from 'common/compose/useId';

export const useCreateTextStore = defineStore(STORE_KEY.TD.AI_TOOLKIT.SMART_COPYWRITER.CREATE_TEXT, () => {
  const { isLoading, hideLoading, showLoading } = useLoading();
  const { idList, setIds, clearIds } = useId([]);
  const tableLanguage = ref<string>(''); // 表格中的语言
  const tableSelectIdList = ref<string[]>([]);  // 表格选中的
  const sort = ref<SortInfo | undefined>(undefined); // 排序
  const tableData = ref<CopyWriteCopiesInner[]>([]); // 表格数据

  function setTableLanguage(val: string) {
    tableLanguage.value = val;
  }
  function setTableSelectIdList(val: string[]) {
    tableSelectIdList.value = val;
  }
  function setSort(val: SortInfo | undefined) {
    sort.value = val;
  }

  async function getCopyWrite(formData: CopyWriterForm) {
    showLoading();
    let isErr = false;
    try {
      const timeoutId = setTimeout(() => {
        if (!isErr) {
          MessagePlugin.warning({
            content: 'Texts are still generating, please wait a moment.',
            duration: 0,
          });
        }
      }, 8000);
      const res = await getCopyWriteService(formData);
      clearTimeout(timeoutId);
      MessagePlugin.closeAll();
      hideLoading();
      if (res.ret_code !== '0') {
        if (res.message.includes('mlflow')) {
          MessagePlugin.warning('Input error, please adjust the settings and try again.');
        } else {
          MessagePlugin.error(res.message || 'Generation failed due to timeout. Please try again.');
        }
        return;
      }
      tableData.value = res.data.results.map((item: any) => ({
        id: uuidv4(),
        copy: item,
        length: item.length,
        ...item,
      }));
      clearIds();
      setTableLanguage(formData.language);
    } catch (e: unknown) {
      console.log('e', e);
      isErr = true;
      MessagePlugin.closeAll();
      MessagePlugin.error('Generation failed due to timeout. Please try again.');
      hideLoading();
    }
  }

  async function create(textList: CreateTextItem[]) {
    return await createText(textList);
  }

  return {
    tableLanguage,
    tableData,
    setTableLanguage,
    sort,
    setSort,
    setTableSelectIdList,
    tableSelectIdList,
    isLoading,
    detailIdList: idList,
    toggleDetail: setIds,
    getCopyWrite,
    create,
  };
});
