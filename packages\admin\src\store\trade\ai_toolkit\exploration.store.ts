import { ref, reactive } from 'vue';
import { STORE_KEY } from '@/config/config';
import { defineStore } from 'pinia';
import type { OptionsItem } from 'common/components/Cascader';
import {
  getMediaCopies,
  getOptions,
  getMediaContents,
  getGameList,
  getChannelList,
} from 'common/service/td/ai_toolkit/smartCopywriter';
import { MediaCopyParams, MediaCopyItem, MediaContentItem } from './type';
import dayjs from 'dayjs';
import { FilterValue, MessagePlugin } from 'tdesign-vue-next';

export const useExploration = defineStore(STORE_KEY.TD.AI_TOOLKIT.SMART_COPYWRITER.EXPLORATION, () => {
  const curTab = ref('media_copies');
  const tabList = ref([
    {
      panelSlotName: 'Media Copies',
      labelIconSlotName: 'folder',
      label: 'Media Copies',
      value: 'media_copies',
      selectCount: 0,
    },
    {
      panelSlotName: 'Social Media Contents',
      labelIconSlotName: 'more-file',
      label: 'Social Media Contents',
      value: 'media_content',
      selectCount: 0,
    },
  ]);

  function getInitDate() {
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 1);
    const endDate = new Date();
    return [
      dayjs(startDate).format('YYYY-MM-DD'),
      dayjs(endDate).format('YYYY-MM-DD'),
    ];
  }

  const languageOptions = ref<OptionsItem[]>([]);
  const countryOptions = ref<OptionsItem[]>([]);
  const gameList = ref<OptionsItem[]>([]);
  const channelList = ref<OptionsItem[]>([]);
  const textList = ref<MediaCopyItem[]>([]);
  const contentList = ref<MediaContentItem[]>([]);
  const params = reactive<MediaCopyParams>({
    date_range: getInitDate(),
    country: [],
    keyword: '',
    language: [],
    page_num: 1,
    page_size: 10,
  });
  const orderList = ref<{
    by: string,
    order: 'DESC' | 'ASC',
  }[]>([]);
  const filterValue = ref<FilterValue>({
    game: [],
    channel: [],
  });
  const tableLoading = ref(false);
  const total = ref(0);
  const contentTableLoading = ref(false);
  const contentTotal = ref(0);

  function tabChange(val: string) {
    curTab.value = val;
    orderList.value = [];
    fetchData();
  }

  function onFilterChange(filters: FilterValue) {
    filterValue.value = filters;
    getContentData();
  }

  async function getFilterOptions() {
    const res = await getOptions();
    const data = res.asset_language.map(item => ({
      label: item.text,
      value: item.value,
    }));
    languageOptions.value = data;
    countryOptions.value = res.country_code;
  }

  async function getGames() {
    gameList.value = await getGameList();
    filterValue.value = {
      ...filterValue.value,
      game: [gameList.value[0].value],
    };
  }

  async function getChannels() {
    const list = await getChannelList();
    const channles = list.map(channel => ({
      label: channel,
      value: channel,
    }));
    channelList.value = channles;
  }

  function getLanguageName(val: string) {
    const target = languageOptions.value.find(item => item.value === val.toUpperCase());
    if (target) return target.label;
    return val || '-';
  }

  function getCountryName(val: string) {
    if (!val) return '-';
    let target: undefined | OptionsItem = undefined;
    countryOptions.value.forEach((item) => {
      item.children?.forEach((country) => {
        if (country.value === val.toUpperCase()) {
          target = country;
        }
      });
    });
    if (target) return (target as OptionsItem).label;
    return val || '-';
  }

  function sortChange(sortData: { sortBy: string, descending: boolean } | undefined) {
    if (sortData) {
      let by = sortData.sortBy;
      if (by === 'length') by = 'LENGTH(content)';
      orderList.value = [
        { by, order: sortData.descending ? 'DESC' : 'ASC' },
      ];
    } else {
      orderList.value = [];
    }
    fetchData();
  }

  function fetchData() {
    if (curTab.value === 'media_copies') getData();
    else getContentData();
  }

  async function getData() {
    tableLoading.value = true;
    const res = await getMediaCopies(params, orderList.value).catch((err) => {
      MessagePlugin.error(err.message);
      return {
        total: 0,
        list: [],
      };
    });
    tableLoading.value = false;
    textList.value = res.list;
    // 翻页请求，不设置total
    if (params.page_num === 1) {
      total.value = res.total;
    }
  }

  async function getAllData() {
    const res = await getMediaCopies({
      ...params,
      page_num: 1,
      page_size: undefined,
    }, orderList.value);
    return res.list;
  }

  async function getContentData() {
    contentTableLoading.value = true;
    let countryCodes: string[] = [];
    params.country.forEach(val => countryCodes = countryCodes.concat([val.toLowerCase(), val.toUpperCase()]));
    const paramsData = {
      ...params,
      language: params.language.map(item => item.toLowerCase()),
      start_time: params.date_range[0],
      end_time: params.date_range[1],
      country: countryCodes,
      date_range: undefined,
      order: orderList.value,
      games: filterValue.value.game,
      channel: filterValue.value.channel_name,
    };
    const res = await getMediaContents(paramsData);
    contentTableLoading.value = false;
    let { list } = res;
    list = list.map((item) => {
      const game = gameList.value.find(game => item.unified_id === game.value);
      return {
        ...item,
        game: game ? game.label : '-',
      };
    });
    contentList.value = list;
    contentTotal.value = res.total;
  }

  async function getAllContentData() {
    let countryCodes: string[] = [];
    params.country.forEach(val => countryCodes = countryCodes.concat([val.toLowerCase(), val.toUpperCase()]));
    const res = await getMediaContents({
      ...params,
      page_num: 1,
      page_size: undefined,
      language: params.language.map(item => item.toLowerCase()),
      country: countryCodes,
      start_time: params.date_range[0],
      end_time: params.date_range[1],
    });
    return res.list;
  }

  function changePage(pageNum: number, pageSize: number) {
    params.page_num = pageNum;
    params.page_size = pageSize;
    fetchData();
  }

  function onResetFilter() {
    params.keyword = '';
    params.language = [];
    params.date_range = getInitDate();
    params.country = [];
    fetchData();
  }

  return {
    curTab,
    tabList,
    params,
    filterValue,
    tableLoading,
    contentTableLoading,
    total,
    contentTotal,
    languageOptions,
    countryOptions,
    textList,
    contentList,
    gameList,
    channelList,
    getData,
    getContentData,
    getFilterOptions,
    getGames,
    getLanguageName,
    getCountryName,
    changePage,
    getAllData,
    getAllContentData,
    onResetFilter,
    sortChange,
    tabChange,
    onFilterChange,
    getChannels,
  };
});
