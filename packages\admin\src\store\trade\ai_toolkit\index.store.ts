import { STORE_KEY } from '@/config/config';
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { router } from '@/router';
import { useRoute } from 'vue-router';

export const useTradeAiToolkitStore = defineStore(STORE_KEY.TD.AI_TOOLKIT.SMART_COPYWRITER.INDEX, () => {
  const route = useRoute();
  const tabValue = ref<string>(route.query.type as string || 'create_text');

  async function setTabValue(val: string) {
    tabValue.value = val;
    await router.replace({
      query: { type: val },
    });
  }

  return {
    tabValue,
    setTabValue,
  };
});
