import { ref, reactive } from 'vue';
import { STORE_KEY } from '@/config/config';
import { defineStore } from 'pinia';
import { getText, deleteText, updateText } from 'common/service/td/ai_toolkit/smartCopywriter';
import { TextParams, TextListItem } from './type';
import dayjs from 'dayjs';

export const useSavedTextStore = defineStore(STORE_KEY.TD.AI_TOOLKIT.SMART_COPYWRITER.SAVED_TEXT, () => {
  const textList = ref<TextListItem[]>([]);
  const defaultRange = ref();
  const params = reactive<TextParams>({
    date_range: [],
    keyword: '',
    themes: [],
    language: '',
    page_num: 1,
    page_size: 10,
    order: [],
  });
  const tableLoading = ref(false);
  const total = ref(0);

  async function getData() {
    tableLoading.value = true;
    const res = await getText(params);
    const data = res.list.map(item => ({
      ...item,
      editable: false,
    }));
    tableLoading.value = false;
    textList.value = data;
    total.value = res.total;
  }

  async function getAllData() {
    const res = await getText({
      ...params,
      all: true,
    });
    return res.list;
  }

  function changePage(pageNum: number, pageSize: number) {
    params.page_num = pageNum;
    params.page_size = pageSize;
    getData();
  }

  function sortChange(sortData: { sortBy: string, descending: boolean } | undefined) {
    if (sortData) params.order = [{ by: sortData.sortBy, order: sortData.descending ? 'DESC' : 'ASC' }];
    else params.order = [];
    getData();
  }

  function deleteItem(ids: number[]) {
    return deleteText(ids);
  }

  function updateItem(item: TextListItem) {
    return updateText(item.id, item.text, item.length);
  }

  function onResetFilter() {
    params.keyword = '';
    params.themes = [];
    params.language = '';
    params.date_range = defaultRange.value;
    getData();
  }

  async function setDate() {
    const defaultStartDate = new Date();
    const defaultEndDate = new Date();
    defaultStartDate.setFullYear(defaultStartDate.getFullYear() - 1);
    params.date_range = [
      dayjs(defaultStartDate).format('YYYY-MM-DD'),
      dayjs(defaultEndDate).format('YYYY-MM-DD'),
    ];
    defaultRange.value = params.date_range;
    getData();
  }

  return {
    params,
    tableLoading,
    total,
    textList,
    setDate,
    getData,
    getAllData,
    changePage,
    deleteItem,
    updateItem,
    onResetFilter,
    sortChange,
  };
});
