export interface TextParams {
  date_range: string[],
  keyword: string,
  themes: string[],
  language: string,
  page_num: number,
  page_size: number,
  all?: boolean,
  order: { by: string, order: string } [],
}

export interface CopyItem {
  copy: string,
  copy_number: string,
  copy_en: string,
  copy_length: number,
  language?: string,
  reason: Reason,
}

export interface Reason {
  description: string,
  keywords: string[],
  'reference contents': {
    'historical copies': any[],
    'social media contents': {
      like_number: number,
      post_content: string,
      source: string,
    }[],
  },
  themes: string[],
}

export interface CopyWriterForm {
  length: string,
  quantity: string,
  language: string,
  keywords: string[],
  themes: string[],
}

export interface CopyWriteCopiesInner extends CopyItem {
  id: string,
}

export type TCopyWriterFormKey = 'length' | 'quantity' | 'language' | 'keywords' | 'themes';

export interface CreateTextItem {
  text: string,
  length: number,
  keyword: string,
  theme: string,
  language: string,
  detail: string, // json字符串
}

export interface TargetItem extends CreateTextItem {
  index: number,
  en: string,
  editable: boolean,
}

export interface TextListItem {
  id: number,
  game_code: string,
  en: string, // 英文翻译
  keyword: string,
  language: string,
  country_name_en: string,
  length: number,
  text: string,
  theme: string,
  editable?: boolean,
  detail: Reason,
}

export interface MediaCopyParams {
  date_range: string[],
  country: string[],
  keyword: '',
  language: string[],
  page_num: number,
  page_size?: number,
}

export interface MediaCopyItem {
  id: string,
  text: string,
  network: string,
  language: string,
  length: number,
  impressions: number,
  rank: number,
}

export interface MediaContentParams {
  start_time: string,
  end_time: string,
  keyword: '',
  language: string[],
  country: string[],
  page_num: number,
  page_size?: number,
  order?: { by: string, order: string }[]
}

export interface MediaContentItem {
  id: string,
  unified_id: string,
  content: string,
  channel_name: string,
  game: string,
  language: string,
  country: string,
  length: number,
  comment_time: string,
  tweets_like: number,
  tweets_retweet: number,
  tweets_reply: number,
  follower_number: number,
  tweets_view: number,
}
