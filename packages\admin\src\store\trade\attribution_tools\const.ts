// validate custom parameters
let errorParamsArray: { [x: string]: string }[] = []; // 存放错误参数的数组
let errorParamsName: string | null = null; // 错误参数的名称（如：params_0）
let isNeedValidate = false; // 判断是否需要验证
let errorParamsKey: string | null = null; // 错误参数的"Key"值（用户输入项）
/**
 *
 * @param params 用户当前输入项
 * @returns
 */
export const validatorUniqueKey = (params: { [x: string]: string }) => {
  // 固有的"Key"值
  const keyArray: string[] = [
    'network',
    'tracker_name',
    'destination_url',
    'suffix',
    'account',
    'campaign',
    'campaign_name',
    'ad_group',
    'ad_group_name',
    'ad',
    'ad_name',
    'channel',
    'keyword',
    'creative',
  ];
  let result = true;

  if (!params) {
    return { result: true };
  }
  for (const [key, value] of Object.entries(FORM_DATA) as any) {
    if (key.includes('params_') && !key.includes(params.name) && value.key) {
      keyArray.push(value.key); // 将正确的"Key"值存放到"KeyArray"数组
    }
  }

  try {
    keyArray.reverse().forEach((it) => {
      if (!result) {
        let existErrorParams = false;
        let existErrorParamsArray = false;

        if (!errorParamsArray.length) {
          // 首次出现输入错误参数"Key"时，不需要进行去重处理，直接将当前输入项存放到"errorParamsArray"数组
          errorParamsArray.push(params);
        } else {
          for (const [, value] of errorParamsArray.entries()) {
            // "错误参数的名称（如：params_0）"已存在"errorParamsArray"数组，则将"existErrorParams"变量赋值为true
            if (value.name === errorParamsName) {
              existErrorParams = true;
            }

            // 当前输入项的名称已存在"errorParamsArray"数组，且当前输入项的值也已存在"errorParamsArray"数组，将"existErrorParamsArray"变量赋值为true
            if (value.name === params.name && value.key === params.key) {
              existErrorParamsArray = true;
            }
          }

          // "existErrorParamsArray"变量为false，表示当前错误项不存在"errorParamsArray"数组，应将其添加到"errorParamsArray"数组
          if (!existErrorParamsArray) errorParamsArray.push(params);
        }

        // eslint-disable-next-line max-len
        // 当"errorParamsName"变量值是null 或 "existErrorParams"变量值是false时，将当前错误项的名称赋值给"errorParamsName"变量（用户首次出现错误参数，或之前错误参数被更正后，还存在其他错误参数，或重新输入其他错误参数）
        if (!errorParamsName || !existErrorParams) {
          errorParamsName = params.name;
        }

        throw new Error('Loop End');
      }
      // 如果当前输入项"Key"已经存在，或者key和value异或时，
      // 则result变量赋值为false，否则赋值为true
      result = it !== params.key && (!!((params.key && params.value) || (!params.key && !params.value)));
    });
  } catch (err) {}

  if (result) {
    if (errorParamsArray.length) {
      // 需要剔除的错误参数的索引
      let spliceErrorParamsIndex: number | null = null;
      // 优先需要处理的错误参数的索引
      let priorityErrorParamsIndex: number | null = null;

      errorParamsArray.forEach((it, idx) => {
        if (it.name === params.name) {
          // 更正错误（当前输入正确参数），将需要剔除的错误参数的索引赋值给"spliceErrorParamsIndex"变量
          isNeedValidate = false;
          spliceErrorParamsIndex = idx;
        } else {
          // 如果不是更正错误，则将错误参数"Key"等于"errorParamsKey"变量的项（索引）定义为需要优先处理的项
          if (priorityErrorParamsIndex === null && it.key === errorParamsKey) priorityErrorParamsIndex = idx;
        }
      });

      // 更正错误（当前输入正确参数）后，将其从"errorParamsArray"数组内剔除
      if (spliceErrorParamsIndex !== null) errorParamsArray.splice(spliceErrorParamsIndex, 1);

      // const realIndex = priorityErrorParamsIndex ? priorityErrorParamsIndex - 1 : 0;

      // 还存在错误项，将根据"realIndex"变量（索引）进行触发验证
      // if (errorParamsArray.length) {
      //   (errorParamsArray[realIndex].elem as unknown as HTMLInputElement).focus();
      //   (errorParamsArray[realIndex].elem as unknown as HTMLInputElement).blur();
      // }
    }
    return { result: true };
  }

  // 当已存在错误项，但用户在没有完成对错误项的更正就有添加新的错误项，此时不对新的错误项进行验证
  if (isNeedValidate && errorParamsName !== null && params.name !== errorParamsName) return { result: true };

  isNeedValidate = true;
  errorParamsKey = params.key;

  if (!((params.key && params.value) || (!params.key && !params.value))) return { result: false, message: 'Parameter or Value cannot be empty.', type: 'error' };

  return { result: false, message: 'Parameter already exists.', type: 'error' };
};

export const resetVariable = () => {
  errorParamsArray = [];
  errorParamsName = null;
  isNeedValidate = false;
  errorParamsKey = null;
};

export const FORM_DATA = {
  network: '',
  tracker_name: '',
  destination_url: '',
  suffix: '',
  account: '',
  campaign: '',
  campaign_name: '',
  ad_group: '',
  ad_group_name: '',
  ad: '',
  ad_name: '',
  channel: '',
  keyword: '',
  creative: '',
  params_0: {
    name: 'params_0',
    key: '',
    value: '',
  },
};

export const FORM_RULES: { [x: string]: { [x: string]: any }[] } = {
  network: [{ required: true, message: 'Media Source is required.', type: 'error', trigger: 'blur' }],
  params_0: [{ validator: validatorUniqueKey, trigger: 'blur' }],
};

export const BASIC_INFORMATION = [
  [
    {
      label: 'Media Source', // Media Source Name
      name: 'network',
      disabled: true,
      required: true,
      exist: true,
      tip: '',
    },
  ],
  [
    {
      label: 'Tracker Name',
      name: 'tracker_name',
      disabled: false,
      required: true,
      exist: true,
      tip: '',
    },
  ],
  [
    {
      label: 'Destination URL',
      name: 'destination_url',
      disabled: false,
      required: true,
      exist: true,
      tip: '',
    },
  ],
];

export const URL_PARAMETERS = [
  [
    {
      label: 'Account ID',
      name: 'account',
      disabled: false,
      required: false,
      exist: true,
      tip: '',
    },
  ],
  [
    {
      label: 'Campaign ID',
      name: 'campaign',
      disabled: true,
      required: true,
      exist: true,
      tip: '',
    },
    {
      label: 'Campaign Name',
      name: 'campaign_name',
      disabled: false,
      required: false,
      exist: true,
      tip: '',
    },
  ],
  [
    {
      label: 'Ad Group ID',
      name: 'ad_group',
      disabled: false,
      required: false,
      exist: true,
      tip: '',
    },
    {
      label: 'Ad Group Name',
      name: 'ad_group_name',
      disabled: false,
      required: false,
      exist: true,
      tip: '',
    },
  ],
  [
    {
      label: 'Ad ID',
      name: 'ad',
      disabled: false,
      required: false,
      exist: true,
      tip: '',
    },
    {
      label: 'Ad Name',
      name: 'ad_name',
      disabled: false,
      required: false,
      exist: true,
      tip: '',
    },
  ],
  [
    {
      label: 'Channel',
      name: 'channel',
      disabled: false,
      required: false,
      exist: true,
      tip: '',
    },
  ],
  [
    {
      label: 'Keyword',
      name: 'keyword',
      disabled: false,
      required: false,
      exist: true,
      tip: '',
    },
  ],
  [
    {
      label: 'Creative',
      name: 'creative',
      disabled: false,
      required: false,
      exist: true,
      tip: '',
    },
  ],
];

// pre channels, name enum
export enum PreChannelsType {
  Twitter = 'twitter',
  Google = 'google',
  TikTok = 'tiktok',
  Reddit = 'reddit',
  Facebook = 'facebook',
}

// set first letter to upper,  map
export const UpperMap: Record<string, string> = {
  facebook: 'Facebook',
  twitter: 'Twitter',
  google: 'Google',
  tiktok: 'Tiktok',
  reddit: 'Reddit',
};

export const UTMParams: Record<string, any> = {
  google: {
    utm_source: '',
    utm_medium: 'google',
    utm_campaign: '{campaignid}',
    utm_content: '{adgroupid}',
    utm_term: '{creative}',
  },
  facebook: {
    utm_source: '',
    utm_medium: 'facebook',
    utm_campaign: '{{campaign.name}}',
    utm_content: '{{adset.name}}',
    utm_term: '{{ad.name}}',
  },
  tiktok: {
    utm_source: '',
    utm_medium: 'tiktok',
    utm_campaign: '__CAMPAIGN_NAME__',
    utm_content: '__AID_NAME__',
    utm_term: '__CID_NAME__',
  },
  reddit: {
    utm_source: '',
    utm_medium: 'reddit',
    utm_campaign: '{{CAMPAIGN_ID}}',
    utm_content: '{{ADGROUP_NAME}}',
    utm_term: '{{AD_NAME}}',
  },
  twitter: {
    utm_source: '',
    utm_medium: 'twitter',
    utm_campaign: '_copy_campaign_name',
    utm_content: '_copy_ad_group_name',
    utm_term: '_copy_ad_name',
  },
  custom: {
    utm_source: '',
    utm_medium: '_copy_network',
    utm_campaign: '_copy_campaign_name',
    utm_content: '_copy_ad_group_name',
    utm_term: '_copy_ad_name',
  },
};

export enum Template {
  Details = 'Template Details',
  Edit = 'Template Edit',
}
