/* eslint-disable no-param-reassign */
import { computed, ref } from 'vue';
import { defineStore } from 'pinia';
import { FORM_DATA, FORM_RULES, BASIC_INFORMATION, URL_PARAMETERS, resetVariable } from './const';
import { IFormItemControl, IFormItem } from './type';

/**
 * "基础信息"或"查询字符串"的每一项
 *
 * @param state 默认状态
 * @param controlArray 需要改动状态的项
 * @returns 返回每项的状态
 */
function controlFunction(state: IFormItem[][], controlArray: IFormItemControl[]) {
  // 深拷贝，防止其默认状态被改动
  const copyState = JSON.parse(JSON.stringify(state));

  // 没有需要改动的项，直接使用默认状态
  if (!controlArray.length) return copyState;

  const result: IFormItem[][] = [];

  controlArray.forEach((item: IFormItemControl, index, c_self: IFormItemControl[]) => {
    copyState.forEach((arr: IFormItem[]) => {
      arr.forEach((it: IFormItem, idx: number, a_self: IFormItem[]) => {
        if (it.name === item.name) {
          it.disabled = item.disabled;
          it.required = item.required;
          it.exist = item.exist;
          it.tip = item.tip;
        }
        if (!it.exist) a_self.splice(idx, 1); // 判断此项是否应该存在，不存在则将此项剔除（如：google渠道存在"keyword"，而其他渠道不存在）
      });

      if (index === c_self.length - 1 && arr.length) result.push(arr);
    });
  });

  return result;
}

export const useTrackerStore = defineStore('tracker', () => {
  // Drawer组件标题
  const title = ref('Channels');
  // Form组件数据
  const data = ref<{ [x: string]: string | object }>(FORM_DATA);
  // Form组件规则
  const rules = ref<{ [x: string]: object[] }>(FORM_RULES);
  // 生成的URL
  const trackUrl = ref<string | null>(null);
  // 存储Track ID 或 Channel ID
  const uniqueId = ref<string | null>(null);

  // 需要改动的基础信息状态项
  const basicInformationControlArray = ref<IFormItemControl[]>([]);
  // 获取基础信息的响应式状态（用于动态响应<template>中的内容）
  const getBasicInformation = computed(() => controlFunction(BASIC_INFORMATION, basicInformationControlArray.value));
  // get current basic info
  const getCurBasicInfo = () => controlFunction(BASIC_INFORMATION, basicInformationControlArray.value);

  // 需要改动的“URL Parameters”状态项
  const urlParametersControlArray = ref<IFormItemControl[]>([]);
  // 获取“URL Parameters”的响应式状态（用于动态响应<template>中的内容）
  const getUrlParameters = computed(() => controlFunction(URL_PARAMETERS, urlParametersControlArray.value));
  // get current "URL Parameters"
  const getCurUrlParams = () => controlFunction(URL_PARAMETERS, urlParametersControlArray.value);

  // 设置Drawer组件标题
  const setTitle = async (val: string) => {
    title.value = val;
  };

  // 设置Form组件数据
  const setData = async ({
    network,
    tracker_name,
    destination_url,
    track_url,
    query_string,
    account,
  }: {
    network?: string;
    tracker_name?: string;
    destination_url?: string;
    suffix?: string;
    track_url?: string;
    query_string?: string;
    account?: string;
  }) => {
    let index = 0;
    if (track_url || query_string) {
      const params = track_url ? new URL(track_url).searchParams : new URLSearchParams(query_string);
      for (const [key, value] of params.entries()) {
        if (Object.hasOwnProperty.call(data.value, key)) data.value[key] = value; // update URL Parameters
        else {
          // update Custom Parameters
          const name = `params_${index}`;
          data.value[name] = {
            name,
            key,
            value,
          };
          data.value.suffix += `${key}=${value}&`;
          index += 1;
        }
      }
      // remove '&' at the suffix end
      data.value.suffix = (data.value.suffix as string)?.slice(0, (data.value.suffix as string).length - 1);
    } else {
      for (const [key, value] of Object.entries(data.value)) {
        if (typeof value === 'string') {
          data.value[key] = '';
        } else {
          if (key === `params_${index}`) {
            (data.value[key] as { key: string }).key = '';
            (data.value[key] as { value: string }).value = '';
          } else {
            delete data.value[key];
          }
        }
      }
    }

    // set basic info
    data.value.network = network ?? '';
    data.value.tracker_name = tracker_name ?? '';
    data.value.destination_url = destination_url ?? '';
    data.value.account = account ?? '';

    await setTrackUrl(track_url);
  };

  // 给trackUrl变量赋值
  const setTrackUrl = async (t?: string) => {
    trackUrl.value = t ?? null;
  };

  // 给uniqueId变量赋值
  const setUniqueId = async (id?: string) => {
    uniqueId.value = id ?? null;
  };

  const setBasicInformation = async (arr: {
    name: string;
    disabled?: boolean;
    required?: boolean;
    exist?: boolean;
    tip?: string;
  }[]) => arr.forEach(({ name, disabled = false, required = false, exist = true, tip = '' }) => {
    let set = false;
    for (let i = 0; i < basicInformationControlArray.value.length; i++) {
      if (name === basicInformationControlArray.value[i].name) {
        basicInformationControlArray.value[i] = {
          name,
          disabled,
          required,
          exist,
          tip,
        };
        set = true;
        break;
      }
    }
    if (!set) {
      basicInformationControlArray.value.push({
        name,
        disabled,
        required,
        exist,
        tip,
      });
    }
  });

  const setUrlParameters = async (arr: {
    name: string;
    disabled?: boolean;
    required?: boolean;
    exist?: boolean;
    tip?: string;
  }[]) => arr.forEach(({ name, disabled = false, required = false, exist = true, tip = '' }) => {
    let set = false;
    for (let i = 0; i < urlParametersControlArray.value.length; i++) {
      if (name === urlParametersControlArray.value[i].name) {
        urlParametersControlArray.value[i] = {
          name,
          disabled,
          required,
          exist,
          tip,
        };
        set = true;
        break;
      }
    }
    if (!set) {
      urlParametersControlArray.value.push({
        name,
        disabled,
        required,
        exist,
        tip,
      });
    }
  });

  // 设置Form组件规则
  const setRules = async (arr: { name: string; rule: object[] }[]) => {
    arr.forEach(({ name, rule }) => {
      rules.value[name] = rule;
    });
  };

  const fixedRules = Object.keys(FORM_RULES); // 固有的规则项
  // 清除操作
  const emptyArray = async () => {
    basicInformationControlArray.value = [];
    urlParametersControlArray.value = [];
    await setUniqueId();
    await setData({});

    for (const [key] of Object.entries(rules.value)) {
      if (!fixedRules.includes(key)) {
        delete rules.value[key];
      }
    }

    resetVariable();
  };

  return {
    title,
    data,
    rules,
    getUrlParameters,
    getCurUrlParams,
    getBasicInformation,
    getCurBasicInfo,
    trackUrl,
    uniqueId,
    setTitle,
    setData,
    setTrackUrl,
    setUniqueId,
    setBasicInformation,
    setUrlParameters,
    setRules,
    emptyArray,
  };
});
