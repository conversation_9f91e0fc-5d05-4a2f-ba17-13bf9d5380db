import { useGGTreeListData } from './google/google.store';
import { useTTTreeListData } from './tiktok/tiktok.store';
import { useFBTreeListData } from './facebook/facebook.store';
import { useTWTreeListData } from './twitter/twitter.store';
import {
  getAccountInfo, getCampaignNameNumber, updateCampaignNameNumber, getParamsById, createTreeList,
  getTemplates, addOrUpdateTemplate, deleteTemplate,
} from 'common/service/td/base';

export const storeConfig: { [key: string]: any} = {
  tiktok: useTTTreeListData,
  google: useGGTreeListData,
  facebook: useFBTreeListData,
  twitter: useTWTreeListData,
  default: useGGTreeListData,
};

export const basicRequestConfig: { [key: string]: any } = {
  base: {
    getCampaignNameNumber,
    updateCampaignNameNumber,
    getParamsById,
    createTreeList,
    getAccountInfo,
    getTemplates,
    addOrUpdateTemplate,
    deleteTemplate,
  },
  tiktok: {
    keyConfig: {
      inner_campaign_id: 'inner_campaign_id',
      media_campaign_id: 'campaign_id',
      inner_adgroup_id: 'inner_adgroup_id',
      media_adgroup_id: 'adgroup_id',
      inner_ad_id: 'inner_ad_id',
      media_ad_id: 'ad_id',
      status: {
        campaign: 'status',
        adgroup: 'status',
        ad: 'status',
      },
      levelName: {
        campaign: 'campaign_name',
        adgroup: 'adgroup_name',
        ad: 'ad_name',
      },
      inner_status: 'inner_status', // 子状态
      errorKey: 'remark',
    },
    statusDraftCompleted: 'INDRAFT_COMPLETED',
    statusDraftInCompleted: 'INDRAFT_INCOMPLETED',
    statusFailed: 'INDRAFT_FAIL',
    statusEnable: 'ENABLE',
    statusPublishing: 'INDRAFT_PUBLISHING',
    statusUploadFail: 'INNER_INDRAFT_ASSET_UPLOAD_FAIL_OR_CANCELED', // 子状态：素材上传失败或取消
    isInDraftStatus: (status: string) => (['INDRAFT', 'INDRAFT_FAIL', 'INDRAFT_COMPLETED', 'INDRAFT_INCOMPLETED'].includes(status)),
    canPublishInDraft: (status: string) => ['INDRAFT_COMPLETED', 'ENABLE', 'DISABLE', 'INDRAFT_FAIL'].includes(status),
  },
  google: {
    keyConfig: {
      inner_campaign_id: 'inner_campaign_id',
      media_campaign_id: 'campaign_resource_name',
      inner_adgroup_id: 'inner_ad_group_id',
      media_adgroup_id: 'ad_group_resource_name',
      inner_ad_id: 'inner_ad_id',
      media_ad_id: 'ad_resource_name',
      status: {
        campaign: 'campaign_status',
        adgroup: 'group_status',
        ad: 'ad_status',
      },
      levelName: {
        campaign: 'campaign_name',
        adgroup: 'ad_group_name',
        ad: 'ad_name',
      },
      inner_status: 'inner_status', // 子状态
      errorKey: 'memo',
    },
    statusDraftCompleted: 0,
    statusDraftInCompleted: 4,
    statusFailed: 6,
    statusEnable: 2,
    statusPaused: 1,
    statusPublishing: 5,
    statusUploadFail: 'INNER_INDRAFT_ASSET_UPLOAD_FAIL_OR_CANCELED', // 子状态：素材上传失败或取消
    isInDraftStatus: (status: number) => ([0, 6, 4].includes(status)),
    canPublishInDraft: (status: number) => [0, 1, 2, 6].includes(status),
  },
  facebook: {
    keyConfig: {
      inner_campaign_id: 'inner_campaign_id',
      media_campaign_id: 'media_campaign_id',
      inner_adgroup_id: 'inner_adset_id',
      media_adgroup_id: 'media_adset_id',
      inner_ad_id: 'inner_ad_id',
      media_ad_id: 'media_ad_id',
      status: {
        campaign: 'status',
        adgroup: 'status',
        ad: 'status',
      },
      levelName: {
        campaign: 'campaign_name',
        adgroup: 'name',
        ad: 'ad_name',
      },
      inner_status: 'inner_status', // 子状态
      errorKey: 'failed_reason',
    },
    statusDraftCompleted: 'DRAFT',
    statusDraftInCompleted: 'INCOMPLETED',
    statusFailed: 'PUBLISHFAILED',
    statusEnable: 'ACTIVE',
    statusPaused: 'PAUSED',
    statusPublishing: 'PUBLISHING',
    statusUploadFail: 'INNER_INDRAFT_ASSET_UPLOAD_FAIL_OR_CANCELED', // 子状态：素材上传失败或取消
    isInDraftStatus: (status: string) => (['DRAFT', 'INCOMPLETED', 'PUBLISHFAILED'].includes(status)),
    canPublishInDraft: (status: string) => ['DRAFT', 'ACTIVE', 'PAUSED', 'PUBLISHFAILED'].includes(status),
  },
  twitter: {
    keyConfig: {
      inner_campaign_id: 'inner_campaign_id',
      media_campaign_id: 'media_campaign_id',
      inner_adgroup_id: 'inner_ad_group_id',
      media_adgroup_id: 'media_ad_group_id',
      inner_ad_id: 'inner_ad_id',
      media_ad_id: 'media_ad_id',
      status: {
        campaign: 'status',
        adgroup: 'status',
        ad: 'status',
      },
      levelName: {
        campaign: 'name',
        adgroup: 'name',
        ad: 'name',
      },
      inner_status: 'inner_status', // 子状态
      errorKey: 'failed_reason',
    },
    statusDraftCompleted: 'DRAFT',
    statusDraftInCompleted: 'INCOMPLETED',
    statusFailed: 'PUBLISHFAILED',
    statusEnable: 'ACTIVE',
    statusPaused: 'PAUSED',
    statusPublishing: 'PUBLISHING',
    statusUploadFail: 'INNER_INDRAFT_ASSET_UPLOAD_FAIL_OR_CANCELED', // 子状态：素材上传失败或取消
    isInDraftStatus: (status: string) => (['INDRAFT', 'PUBLISHFAILED', 'DRAFT', 'INCOMPLETED'].includes(status)),
    canPublishInDraft: (status: string) => ['DRAFT', 'ACTIVE', 'PAUSED', 'PUBLISHFAILED'].includes(status),
  },
};
