import { useGlobalGameStore } from '@/store/global/game.store';
import { useCustomView } from 'common/compose/useCustomView';
import { MODULE, VIEW_TYPE } from './pivot/utils/const';
import { STORE_KEY } from '@/config/config';
import { defineStore, storeToRefs } from 'pinia';
import { ref, watch } from 'vue';
import { getDefaultColDialogCheckedList, getLowDefault } from './pivot/utils/base';
import { useRoute } from 'vue-router';
import type { ICustomViewItem, IFormData } from 'common/components/NewViewTab';
import type { IViewParam } from 'common/service/customView/type';
import { IColItem } from './pivot/type';
import { ADMAP } from '@/views/trade/ads_management/const';

const { getShare, getList, deleteView, updateView, addView } = useCustomView();

export const useTdCustomView = defineStore(STORE_KEY.TD.CUSTOM_VIEW, () => {
  const viewList = ref<any>([]);

  const currentRoute = useRoute();
  const { gameCode } = storeToRefs(useGlobalGameStore());
  const { isPcGame } = useGlobalGameStore();
  const curMedia = (currentRoute.meta.media || '') as string;

  const defaultView = ref({
    label: 'Default',
    type: 'default',
    param: {
      ...getLowDefault(),
      colDialogCheckedList: getDefaultColDialogCheckedList(gameCode.value, curMedia, ADMAP.CAMPAIGN, isPcGame()),
    },
    game: gameCode.value,
    media: curMedia,
    isPCGame: isPcGame(),
    value: `default_${gameCode.value}_${currentRoute.meta.media || ''}`,
  });

  watch(
    () => [currentRoute.meta.media, gameCode.value],
    ([media, game]) => {
      defaultView.value = {
        ...defaultView.value,
        game: game as string,
        value: `default_${game}_${media || ''}`,
      };
    },
    { deep: true },
  );

  // 分享视图
  const sharedView = ref<any>({});

  // 设置分享视图
  function setSharedView(shared: any) {
    sharedView.value = {
      ...shared,
      label: 'Shared View',
      value: shared._id,
    };
  }

  // 更新视图
  async function setViewList(media: string, game: string, list?: any) {
    if (list > 0) {
      viewList.value = list;
    } else {
      const res: any = await getCustomViews(
        {
          game: [game || '', 'allgame'],
          module: MODULE,
          type: VIEW_TYPE,
        },
        media,
        game,
      ); // 老接口获取视图
      viewList.value = res;
    }
  }

  function addCustomView(
    condition: any,
    formData: IFormData,
    visibleFilters: string[],
    colDialogCheckedList: IColItem[],
  ) {
    const gameInner = formData.viewType === 'all' ? 'allgame' : condition.game;
    return addView({
      name: formData.viewName,
      param: {
        condition: condition.cur,
        other: {
          adStructure: condition.adStructure,
        },
        visibleFilters,
        media: condition.media,
        game: gameInner,
        colDialogCheckedList,
      },
      system: MODULE,
      type: VIEW_TYPE,
    });
  }

  function updateCustomView(
    viewItem: ICustomViewItem,
    condition: any,
    formData: IFormData,
    visibleFilters: string[],
    colDialogCheckedList: IColItem[],
  ) {
    const gameInnier = formData.viewType === 'all' ? 'allgame' : viewItem.game;
    return updateView({
      id: viewItem.value,
      name: formData.viewName,
      param: {
        condition: condition.cur,
        other: {
          adStructure: condition.adStructure,
        },
        visibleFilters,
        media: viewItem?.param?.media,
        game: gameInnier,
        colDialogCheckedList,
      },
      system: MODULE,
      type: VIEW_TYPE,
    });
  }

  function deleteCustomView(viewItem: ICustomViewItem) {
    return deleteView({ id: viewItem.value });
  }

  /**
   * 获取并更新视图
   * @param {{ game: string; system: string }} params
   * @returns
   */
  async function getCustomViews(params: IViewParam, media: string, game: string | string[]) {
    const list = await getList(params);
    return list.filter((item: any) => item?.param?.media === media && (item?.game === game || item?.game === 'allgame'));
  }

  /**
   * 根据code获取视图
   */
  async function getShareViewById(id: string) {
    return await getShare(id);
  }

  /**
   * 分享视图
   */
  // function  shareCustomView(viewItem: ViewItem) {
  //   return addShare({ ...viewItem, system: MODULE });
  // }
  return {
    defaultView, // 默认视图
    sharedView, // 分享视图
    viewList, // 自定义视图
    setViewList,
    addCustomView,
    updateCustomView,
    deleteCustomView,
    getCustomViews,
    getShareViewById,
    setSharedView,
    // shareCustomView,
  };
});
