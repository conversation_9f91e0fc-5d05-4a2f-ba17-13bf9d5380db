import { defineStore, storeToRefs } from 'pinia';
import { ref } from 'vue';
import type {
  BasicCampaignNumberInfoType, BasicInfoType, CopyParams, LinkParam, TreeCurrent, TreeNode,
} from '@/views/trade/ads_creation/common/template/type';
import type { FBAdgroupType, FBCampaignType } from '@/views/trade/ads_creation/facebook/type';
import {
  copyAd, copyAdgroup, copyCampaign,
  createNewTreeNode, deleteDraft, getDetailedTargetingMatchApi, getTreeList, publishAdsApi,
  saveAdApi, saveAdgroupApi, saveCampaignApi,
} from 'common/service/td/facebook/tree';
import { STORE_KEY } from '@/config/config';
import { Level, MediaReqMap } from '@/views/trade/ads_creation/common/template/config';
import { useTreeListDataStore } from '../template.store';
import { MessagePlugin } from 'tdesign-vue-next';
import { levelMapName } from '../util';
import { isNodePublished, isNodeTemp } from '@/views/trade/ads_creation/common/template/utils-common';
import { is3A, isAppPromotion, isConversionApp } from '@/views/trade/ads_creation/facebook/utils';
import { cloneDeep, uniq } from 'lodash-es';
import { initDefaultAdData, initDefaultAdgroupData, initDefaultCampaignData } from 'common/service/td/facebook/utils';
import { generateTree } from 'common/service/td/utils';
import { useCommonParams } from '@/views/trade/ads_creation/common/template/compose/currentCompose';

export const useFBTreeListData = defineStore(STORE_KEY.TD.TEMPLATE.FACEBOOK, () => {
  // 基本账户信息
  let basicInfo: BasicInfoType;
  let basicCampaignNumberInfo: BasicCampaignNumberInfoType;
  // 增加公共options值保存
  const globalOptions: any = ref({});
  // 初始化表单的一些基础数据，后续供各个操作使用
  const basicInit = (
    basicInfoParams: BasicInfoType,
    basicCampaignNumberParams: BasicCampaignNumberInfoType,
  ) => {
    basicInfo = basicInfoParams;
    basicCampaignNumberInfo = basicCampaignNumberParams;
  };
  const { channelConfig } = storeToRefs(useTreeListDataStore());
  // 只初始化树的数据
  const init = async (LinkParams: LinkParam[]): Promise<TreeNode[]> => {
    // 获取当前账户信息
    if (basicInfo.operationType === 'add') {
      return [createNewTreeNode(Level.CampaignLevel, basicCampaignNumberInfo, basicInfo, 1, channelConfig.value)];
    }
    const treeList: TreeNode[] = await getTreeList({
      campaigns: LinkParams.map(item => ({
        account_id: item.account_id,
        inner_campaign_id: item.inner_campaign_id,
        media_campaign_id: item.media_campaign_id,
        game_code: basicInfo.gameCode,
      })),
    }, channelConfig.value, basicCampaignNumberInfo, globalOptions.value);
    return treeList;
  };

  const saveCampaignNode = async (campaignNode: TreeNode, important: boolean) => {
    // 这里保存campaign数据 个性数据都在这里处理
    const temp = isNodeTemp(Level.CampaignLevel, campaignNode.data, channelConfig.value);
    const published = isNodePublished(Level.CampaignLevel, campaignNode.data, channelConfig.value);
    const apiName = temp ? 'CreateCampaign' : 'UpdateCampaign';
    const ignoreKeys = ['campaign_name', 'open_budget', 'budget', 'budget_mode', 'adsets'];
    const { open_budget: openBudget } = campaignNode.data;

    const data: any = {
      ...campaignNode.data,
      name: campaignNode.data.campaign_name,
    };
    // 3A类型，不需要传bid_strategy
    if (is3A(campaignNode.data)) {
      data.bid_strategy = '';
    }
    if (openBudget) {
      const { budget_mode: mode } = data;
      data[mode === 'Daily' ? 'daily_budget' : 'lifetime_budget'] = String(data.budget);
    } else {
      data.daily_budget = '';
      data.lifetime_budget = '';
    }

    if (published) ignoreKeys.push('status'); // 已发布广告，不修改status
    ignoreKeys.forEach(key => delete data[key]);
    const result: any = await saveCampaignApi({
      campaign: {
        account_id: basicInfo.accountId,
        game_code: basicInfo.gameCode,
        ...data,
      },
    } as any, apiName);
    // 矫正字段
    result.media_campaign_id = result.campaign_id;
    console.log('facebook saveCampaign:', result);
    result.important = important;
    const innerCampaignKey = channelConfig.value.keyConfig.inner_campaign_id;
    const mediaCampaignKey = channelConfig.value.keyConfig.media_campaign_id;
    return {
      ...result,
      [innerCampaignKey]: result.campaign?.[innerCampaignKey],
      [mediaCampaignKey]: result.campaign?.[mediaCampaignKey],
    };
  };

  const saveAdgroupNode = async (adgroupNode: TreeNode, campaignNode: TreeNode) => {
    const { open_budget: openBudget } = campaignNode.data;
    const temp = isNodeTemp(Level.AdgroupLevel, adgroupNode.data, channelConfig.value);
    const published = isNodePublished(Level.AdgroupLevel, adgroupNode.data, channelConfig.value);
    const apiName = temp ? 'CreateAdset' : 'UpdateAdset';
    const ignoreKeys = ['ads', 'budget', 'budget_mode', 'adgroup_name_editable', 'reqManual'];

    const data = cloneDeep(adgroupNode.data);
    if (published) delete data.status; // 已发布广告，不修改status
    const {
      start_time: startTime,
      end_time: endTime,
      inner_adset_id: innerAdsetId,
      placement,
    } = data;
    delete data.audience.languageCodes;

    if (openBudget) {
      data.daily_budget = '';
      data.lifetime_budget = '';
    } else {
      const { budget_mode: mode } = data;
      data[mode === 'Daily' ? 'daily_budget' : 'lifetime_budget'] = String(data.budget);
    }

    if (data.placement === 'Automatic') {
      data.manual = {
        wireless_carrier: data.manual.wireless_carrier,
      };
    } else {
      if (!data.reqManual) {
        data.manual = {
          publisher_platforms: data.manual.publisher_platforms,
          device_platforms: data.manual.device_platforms,
          wireless_carrier: data.manual.wireless_carrier,
        };
      }
    }
    if (is3A(campaignNode.data) && placement === 'Automatic') {
      data.manual = {};
    }
    if (isAppPromotion(campaignNode.data)) {
      data.promoted_object = {
        ...data.promoted_object,
        pixel_id: '',
      };
    }
    if (isConversionApp(campaignNode.data, adgroupNode.data)) {
      data.promoted_object = {
        ...data.promoted_object,
        pixel_id: '',
        custom_event_str: '',
      };
    }
    data.audience.location = uniq(data.audience.location);

    ignoreKeys.forEach(key => delete data[key]);

    const params = {
      ...data,
      account_id: basicInfo.accountId,
      game_code: basicInfo.gameCode,
      start_time: `${new Date(startTime).getTime() / 1000}`,
      end_time: endTime ? `${new Date(endTime).getTime() / 1000}` : '',
      inner_adset_id: innerAdsetId.startsWith('td-temptd') ? '' : innerAdsetId,
    };
    const result: any = await saveAdgroupApi({ adset: params }, apiName);
    // 矫正字段
    if (result.adset) {
      result.media_adset_id = result.adset.media_adset_id;
      result.inner_adset_id = result.adset.inner_adset_id;
    }
    if (!result.inner_adset_id) {
      result.inner_adset_id = data.inner_adset_id;
    }
    console.log('saveAdgroupApi:', result);
    return result;
  };

  const saveAdNode = async (campaignNode: TreeNode, adgroupNode: TreeNode, adNode: TreeNode) => {
    const temp = isNodeTemp(Level.AdLevel, adNode.data, channelConfig.value);
    const published = isNodePublished(Level.AdLevel, adNode.data, channelConfig.value);
    const apiName = temp ? 'CreateAd' : 'UpdateAd';
    const data = cloneDeep(adNode.data);
    const ignoreKeys = ['ad_name', 'medias', 'medias_list', 'ad_name_editable'];
    if (published) ignoreKeys.push('status'); // 已发布广告，不修改status
    data.name = data.ad_name;
    ignoreKeys.forEach(key => delete data[key]);

    const result: any = await saveAdApi({
      ad: {
        ...data,
        account_id: basicInfo.accountId,
        game_code: basicInfo.gameCode,
      },
    }, apiName);

    // 矫正字段
    if (result.ad) {
      result.media_ad_id = result.ad.media_ad_id;
      result.inner_ad_id = result.ad.inner_ad_id;
    }
    if (!result.inner_ad_id) result.inner_ad_id = data.inner_ad_id;
    console.log('saveAdApi:', result);
    return result;
  };

  /**
   * 每个条件保存前做的一些前置校验
   */
  const saveCondition = (...param: any) => {
    const result = [true, true, true];
    const { statusPublishing } = channelConfig.value;
    Object.keys(levelMapName).forEach((_key, index) => {
      result[index] = param[index].status !== statusPublishing;
    });
    if (result.reduce((total, current) => total && !current, true)) {
      MessagePlugin.info('Ad is still publishing and can\'t be update');
    }
    return result;
  };

  /**
   * 更新目录树
   */
  const refreshTree = async () => {
    const { treeList, initTreeList } = storeToRefs(useTreeListDataStore());
    const accountCampaigns = (treeList.value as TreeNode[]).map((item) => {
      const { inner_campaign_id: icid, campaign_id: cid, account_id: aid } = item.data;
      return { inner_campaign_id: icid, media_campaign_id: cid, account_id: aid, game_code: basicInfo.gameCode };
    });
    const newTreeList: TreeNode[] = await getTreeList({
      campaigns: accountCampaigns,
    }, channelConfig.value, basicCampaignNumberInfo, globalOptions.value);
    // 设置新的树，更新当前层级数据
    treeList.value = newTreeList;
    initTreeList.value = cloneDeep(newTreeList);
  };

  // 判断是否能够新增或者复制节点
  function canAddOrCopy(current: TreeCurrent, level: Level) {
    const { is_dynamic: dynamic } = current.adgroupNode.data;
    if (level === Level.AdLevel && dynamic) {
      MessagePlugin.warning('Only one ad can be created when the adSet is dynamic');
      return false;
    }
    return true;
  }

  // 增加新节点
  const addNode = (current: TreeCurrent, level: Level, num: number) => {
    if (!canAddOrCopy(current, level)) return;
    return createNewTreeNode(level, basicCampaignNumberInfo, basicInfo, num, channelConfig.value, current);
  };

  // 复制节点
  const copyNode = async (data: CopyParams) => {
    const { game_code: gameCode, account_id: accountId } = useCommonParams();
    const { treeList, current, getTreeNode, addNode } = useTreeListDataStore();
    if (!canAddOrCopy(current, data.level)) return;

    const params = {
      account_id: accountId,
      game_code: gameCode,
      copy_type: data.copy_type === 1 ? 'THIS_LEVEL' : 'ALL_LEVEL',
    };
    const accountCampaigns = (treeList as TreeNode[]).map((item) => {
      const { inner_campaign_id: icid, campaign_id: cid, account_id: aid } = item.data;
      return { inner_campaign_id: icid, campaign_resource_name: cid, account_id: aid, game_code: gameCode };
    });
    let newId = '';
    if (data.level === Level.CampaignLevel) {
      const res = await copyCampaign({ ...params, inner_campaign_id: data.inner_id });
      if (res.result.error_code) return MessagePlugin.error(res.result.error_message);
      newId = res.inner_campaign_id;
      // 复制campaign，使用当前campaign层级的参数
      const { account_id: aid } = current.campaignNode.data;
      accountCampaigns.push({
        inner_campaign_id: newId, campaign_resource_name: '', account_id: aid, game_code: gameCode,
      });
    }
    if (data.level === Level.AdgroupLevel) {
      const res = await copyAdgroup({ ...params, inner_adset_id: data.inner_id });
      if (res.result.error_code) return MessagePlugin.error(res.result.error_message);
      newId = res.inner_adset_id;
    }
    if (data.level === Level.AdLevel) {
      const res = await copyAd({ ...params, inner_ad_id: data.inner_id });
      if (res.result.error_code) return MessagePlugin.error(res.result.error_message);
      newId = res.inner_ad_id;
    }

    // 重新获取目录树
    const newTreeList: TreeNode[] = await getTreeList({
      campaigns: accountCampaigns,
    }, channelConfig.value, basicCampaignNumberInfo);
    const newNode = getTreeNode(newTreeList, `${data.level}-${newId}`, channelConfig.value);
    addNode(data.level, newNode as TreeNode);
  };

  // 删除节点
  const deleteNode = async (treeId: string, level: Level) => {
    let type = 'campaign';
    if (level === Level.AdgroupLevel) type = 'adset';
    if (level === Level.AdLevel) type = 'ad';
    return deleteDraft({ inner_id: treeId, type });
  };

  // 初始化各个层级数据方法
  const getDefaultLevelNode = (level: Level, campaignData?: FBCampaignType, adgroupData?: FBAdgroupType) => {
    const initCampaignData = initDefaultCampaignData(basicCampaignNumberInfo, basicInfo.accountId);
    const initAdgroupData = initDefaultAdgroupData(basicCampaignNumberInfo, 1, campaignData || initCampaignData);
    const treeData = generateTree({
      level,
      initCampaignData,
      initAdgroupData,
      initAdData: initDefaultAdData(1, campaignData || initCampaignData, adgroupData || initAdgroupData),
      channelConfig: channelConfig.value,
    });
    return treeData;
  };

  const getDetailedTargetingMatch = async (searchWord: string) => {
    const result = await getDetailedTargetingMatchApi({
      game_code: basicInfo.gameCode,
      account_id: basicInfo.accountId,
      search_word: searchWord,
      media: MediaReqMap[basicInfo.media],
    });
    return (result as any).detailed_targetings.map((item: { name: string, id: string }) => ({
      ...item,
      label: item.name,
      value: item.name,
    }));
  };

  // 发布广告
  const publishAds = async (adNode: TreeNode) => {
    const result = await publishAdsApi({
      game_code: basicInfo.gameCode,
      account_id: basicInfo.accountId,
      inner_ad_id: adNode.data.inner_ad_id,
    });
    // 矫正字段
    console.log('publishAds:', result);
    return result;
  };

  return {
    basicInit, init, addNode, deleteNode, copyNode,
    saveAdNode, saveAdgroupNode, saveCampaignNode, saveCondition,
    refreshTree, globalOptions, getDefaultLevelNode, getDetailedTargetingMatch,
    publishAds,
  };
});
