/* eslint-disable no-param-reassign */
import { MessagePlugin } from 'tdesign-vue-next';
import { defineStore, storeToRefs } from 'pinia';
import { ref } from 'vue';
import type {
  BasicCampaignNumberInfoType, BasicInfoType, CopyParams, LinkParam, TreeCurrent, TreeNode,
} from '@/views/trade/ads_creation/common/template/type';
import type { CampaignType, OperatingSystemItem } from '@/views/trade/ads_creation/google/type';
import {
  copyAd, copyAdgroup, copyCampaign,
  createNewTreeNode, deleteAdDraft, deleteAdgroupDraft, deleteCampaignDraft, deleteTargetingTemplate,
  getTargetingTemplate, getTreeList, publishAdsApi,
  saveAdApi, saveAdgroupApi, saveCampaignApi, saveTargetingTemplate,
} from 'common/service/td/google/tree';
import { initDefaultAdData, initDefaultAdgroupData, initDefaultCampaignData } from 'common/service/td/google/utils';
import { generateTree } from 'common/service/td/utils';
import { STORE_KEY } from '@/config/config';
import { Level } from '@/views/trade/ads_creation/common/template/config';
import { isNodeTemp } from '@/views/trade/ads_creation/common/template/utils-common';
import { useCommonParams } from '@/views/trade/ads_creation/common/template/compose/currentCompose';
import { useTreeListDataStore } from '../template.store';
import { cloneDeep, uniq } from 'lodash-es';
import { DEVICESFINALOPTIONS } from '@/views/trade/ads_creation/google/const';
import { EBusEmit } from '@/views/trade/ads_creation/common/template/event';


export const useGGTreeListData = defineStore(STORE_KEY.TD.TEMPLATE.GOOGLE, () => {
  const { channelConfig } = storeToRefs(useTreeListDataStore());
  // 基本账户信息
  let basicInfo: BasicInfoType;
  let basicCampaignNumberInfo: BasicCampaignNumberInfoType;
  // 增加公共options值保存
  const globalOptions: any = ref({});
  // 初始化表单的一些基础数据，后续供各个操作使用
  const basicInit = (
    basicInfoParams: BasicInfoType,
    basicCampaignNumberParams: BasicCampaignNumberInfoType,
  ) => {
    basicInfo = basicInfoParams;
    basicCampaignNumberInfo = basicCampaignNumberParams;
  };
  // 只初始化树的数据
  const init = async (LinkParams: LinkParam[]): Promise<TreeNode[]> => {
    if (basicInfo.operationType === 'add') {
      return [createNewTreeNode(Level.CampaignLevel, basicCampaignNumberInfo, basicInfo, 1, channelConfig.value)];
    }
    const treeList: TreeNode[] = await getTreeList({
      campaigns: LinkParams.map((item) => {
        const params: {
          account_id: string, inner_campaign_id?: string, campaign_resource_name?: string,
        } = {
          account_id: item.account_id,
        };
        if (item.inner_campaign_id) params.inner_campaign_id = item.inner_campaign_id;
        if (item.media_campaign_id) params.campaign_resource_name = item.media_campaign_id;
        return params;
      }),
    }, channelConfig.value, basicCampaignNumberInfo);
    return treeList;
  };

  const saveCondition = (campaign: any, adgroup: any, ad: any) => {
    const result = [true, true, true];
    const { statusPublishing } = channelConfig.value;
    if (campaign.status === statusPublishing) {
      result[0] = false;
    }
    if (adgroup.status === statusPublishing) {
      result[1] = false;
    }
    if (ad.status === statusPublishing) {
      result[2] = false;
    }
    if (!result[0] && !result[1] && !result[2]) {
      MessagePlugin.info('Ad is still publishing and can\'t be update');
    }
    // 在这里增加对audience是否保存进行检测
    const { objective, type } = campaign;
    if (objective === 2 && [4, 5].includes(type)) {
      // performance max和discovery进行检测
      EBusEmit('checkIsAudienceSaved');
    }
    return result;
  };

  // 重新获取目录树
  const refreshTree = async () => {
    const { treeList, initTreeList } = storeToRefs(useTreeListDataStore());
    const accountCampaigns = (treeList.value as TreeNode[]).map((item) => {
      const { inner_campaign_id: icid, campaign_id: cid, account_id: aid } = item.data;
      return { inner_campaign_id: icid, campaign_resource_name: cid, account_id: aid };
    });
    const newTreeList: TreeNode[] = await getTreeList({
      campaigns: accountCampaigns,
    }, channelConfig.value, basicCampaignNumberInfo);
    // 设置新的树，更新当前层级数据
    treeList.value = newTreeList;
    initTreeList.value = cloneDeep(newTreeList);
  };

  // 这里保存campaign数据 个性数据都在这里处理
  const saveCampaignNode = async (campaignNode: TreeNode) => {
    const temp = isNodeTemp(Level.CampaignLevel, campaignNode.data, channelConfig.value);
    const apiName = temp ? 'SaveCampaignDraft' : 'UpdateCampaign';
    const {
      objective,
      type,
      sub_type: subType,
      inner_campaign_id: innerCampaignId,
      devices,
      track_install_volume: trackInstallVolume,
      maximum_cpc_bid_limit: maximumCpcBidLimit,
      os,
      target_cpa: targetCpa,
      target_cpi: targetCpi,
      target_cpr: targetCpr,
      target_impression_share: {
        impression_share_to_target: impressionShareToTarget,
        location,
      },
      target_roas: targetRoas,
      url_custom_parameters: urlCustomParameters,
      conversions,
      devicesRadio,
    } = campaignNode.data;

    let {
      operating_system_version_info: {
        android,
        ios,
      },
      focus_on: focusOn,
    } = campaignNode.data;

    const {
      operatingSystem: {
        android: initAndroidOptions,
        ios: initIosOptions,
      },
    } = globalOptions.value;

    // 处理focuson display和discovery 的 conversion 不能直接用10 需要判断一下
    if ([3, 5].includes(type) && focusOn === 10) {
      // conversions选择auto
      if ((conversions === 14 && targetCpa) || conversions === 15) {
        focusOn = conversions;
      }
    }

    // 手机版本字段处理
    const copeSys = (selectVersions: string[], type: string) => {
      const initSystemOptions: OperatingSystemItem[] = type === 'android' ? initAndroidOptions : initIosOptions;
      const [from, to] = selectVersions;
      if (to === 'current') {
        const min = initSystemOptions.find(item => from === item.version && item.greater)?.value;
        return [min];
      }
      return initSystemOptions.filter(item => Number(item.version) >= Number(from)
        && Number(item.version) <= Number(to) && !item.greater).map(item => item.value);
    };
    // 只有display类型才处理
    if (!(objective === 2 && type === 3) || devicesRadio) {
      android = [];
      ios = [];
    } else {
      android = os.includes(2) ? copeSys(android, 'android') : [];
      ios = os.includes(1) ? copeSys(ios, 'ios') : [];
    }

    const data = {
      ...campaignNode.data,
      inner_campaign_id: innerCampaignId.startsWith('td-temptd') ? '0' : innerCampaignId,
      budget: {
        budget_amount: Number(campaignNode.data.budget.budget_amount),
      },
      devices: DEVICESFINALOPTIONS.map((item) => {
        const newItem = { ...item };
        if (devices.includes(item.type) || devicesRadio) {
          newItem.bid_modifier = 1;
        }
        return newItem;
      }),
      track_install_volume: objective === 1 && type === 1 && subType === 1
        && trackInstallVolume ? [trackInstallVolume] : [],
      maximum_cpc_bid_limit: Number(maximumCpcBidLimit),
      operating_system_version_info: {
        android,
        ios,
      },
      focus_on: focusOn,
      target_cpa: Number(targetCpa),
      target_cpi: Number(targetCpi),
      target_cpr: Number(targetCpr),
      target_roas: Number((Number(targetRoas) * 1000).toFixed(0)),
      target_impression_share: {
        impression_share_to_target: Number(impressionShareToTarget),
        location,
      },
      url_custom_parameters: urlCustomParameters.filter((item: { key: string, value: string}) => item.key
        && item.value),
      location: uniq(campaignNode.data.location),
    };
    // 删除前端自用字段
    data.devicesRadio = undefined;
    data.os = undefined;
    data.conversions = undefined;

    const result: any = await saveCampaignApi({
      campaign: {
        account_id: basicInfo.accountId,
        ...data,
        game_code: basicInfo.gameCode,
      },
    }, apiName);
    // 矫正字段
    result.media_campaign_id = result.campaign_id;
    if (!result.inner_campaign_id) {
      result.inner_campaign_id = innerCampaignId;
    }
    console.log('saveCampaign:', result);
    return result;
  };

  const saveAdgroupNode = async (adgroupNode: TreeNode, campaignNode: TreeNode) => {
    const temp = isNodeTemp(Level.AdgroupLevel, adgroupNode.data, channelConfig.value);
    const apiName = temp ? 'SaveAdGroupDraft' : 'UpdateAdGroup';
    const ignoreKeys = ['adgroup_name_editable'];

    const data = cloneDeep(adgroupNode.data);
    ignoreKeys.forEach((k) => {
      delete data[k];
    });

    const {
      inner_ad_group_id: innerAdgroupId,
      keywords,
      target_cpa: targetCpa,
      cpc_bid: cpcBid,
      cpm_bid: cpmBid,
    } = data;
    const { type, objective } = campaignNode.data;
    const newKeywords = objective === 2 && type === 2 ? keywords.split(/\n|,/g).map((text: string) => ({ match_type: 4, text })) : [];

    const result: any = await saveAdgroupApi({
      account_id: basicInfo.accountId,
      ad_group: {
        ...adgroupNode.data,
        inner_ad_group_id: innerAdgroupId.startsWith('td-temptd') ? '0' : innerAdgroupId,
        keywords: newKeywords,
        target_cpa: Number(targetCpa),
        cpc_bid: Number(cpcBid),
        cpm_bid: Number(cpmBid),
      },
    }, apiName);
    // 矫正字段
    result.media_adgroup_id = result.adgroup_id;
    if (!result.inner_ad_group_id) {
      result.inner_ad_group_id = innerAdgroupId;
    }
    console.log('saveAdgroupApi:', result);
    return result;
  };
  const saveAdNode = async (campaignNode: TreeNode, adgroupNode: TreeNode, adNode: TreeNode) => {
    const temp = isNodeTemp(Level.AdLevel, adNode.data, channelConfig.value);
    const apiName = temp ? 'SaveAdGroupAdDraft' : 'UpdateAd';
    const params = cloneDeep(adNode.data);
    // 删除无用字段
    const ignoreKeys = [
      'medias', 'medias_images', 'medias_images_list', 'medias_images_list',
      'medias_logos', 'medias_logos_list', 'medias_videos', 'medias_videos_list'];
    ignoreKeys.forEach(key => delete params[key]);

    const { type, objective } = campaignNode.data;

    const {
      inner_ad_id: innerAdId,
      url_custom_parameters: urlCustomParameters,
      final_urls: finalUrls,
      final_mobile_urls: finalMobileUrls,
      shortDescription,
      description,
      medias,
    } = params;

    if (objective === 2) {
      if (type === 4) {
        description.unshift(shortDescription); // Performance max类型
      } else {
        params.long_headlines = [params.long_headline];
      }
    }
    params.medias = undefined;
    const result: any = await saveAdApi({
      account_id: basicInfo.accountId,
      ad: {
        ...params,
        ...medias,
        inner_ad_id: innerAdId.startsWith('td-temptd') ? '0' : innerAdId,
        final_urls: finalUrls ? [finalUrls] : [],
        final_mobile_urls: finalMobileUrls ? [finalMobileUrls] : [],
        url_custom_parameters: urlCustomParameters.filter((item: { key: string, value: string}) => item.key
        && item.value),
      },
    }, apiName);
    // 矫正字段
    result.media_ad_id = result.ad_id;
    if (!result.inner_ad_id) {
      result.inner_ad_id = innerAdId;
    }
    return result;
  };

  // 发布广告
  const publishAds = async (adNode: TreeNode) => {
    const result = await publishAdsApi({
      status: channelConfig.value.statusPaused || 1,
      inner_ad_id: adNode.data.inner_ad_id,
    });
    // 矫正字段
    console.log('publishAds:', result);
    return result;
  };

  // 判断是否能够新增或者复制节点
  function canAddOrCopy(current: TreeCurrent, level: Level) {
    const appType = current.campaignNode.data.objective;
    const subType = current.campaignNode.data.type;
    if (level === Level.AdLevel) {
      // app类型，website-performance max类型，只允许有1个ad
      if (appType === 1) {
        MessagePlugin.warning('Only one ad can be created when the objectives is App');
        return false;
      }
      if (appType === 2) {
        if (subType === 4) {
          MessagePlugin.warning('Only one ad can be created when the campaign type is Performance max');
          return false;
        }
      }
    }
    return true;
  }

  // 增加新节点
  const addNode = (current: TreeCurrent, level: Level, num: number) => {
    if (!canAddOrCopy(current, level)) return;
    return createNewTreeNode(level, basicCampaignNumberInfo, basicInfo, num, channelConfig.value, current);
  };

  // 复制节点
  const copyNode = async (data: CopyParams) => {
    const { treeList, current, getTreeNode, addNode } = useTreeListDataStore();

    if (!canAddOrCopy(current, data.level)) return;

    const params = {
      account_id: useCommonParams().account_id,
      copy_type: data.copy_type === 1 ? 'THIS_LEVEL' : 'ALL_LEVEL',
    };
    const accountCampaigns = (treeList as TreeNode[]).map((item) => {
      const { inner_campaign_id: icid, campaign_id: cid, account_id: aid } = item.data;
      return { inner_campaign_id: icid, campaign_resource_name: cid, account_id: aid };
    });
    let newId = '';
    if (data.level === Level.CampaignLevel) {
      const res = await copyCampaign({ ...params, inner_campaign_id: data.inner_id });
      if (res.result.error_code) return MessagePlugin.error(res.result.error_message);
      newId = res.inner_campaign_id;
      // 复制campaign，使用当前campaign层级的参数
      const { account_id: aid } = current.campaignNode.data;
      accountCampaigns.push({ inner_campaign_id: newId, campaign_resource_name: '', account_id: aid });
    }
    if (data.level === Level.AdgroupLevel) {
      const res = await copyAdgroup({ ...params, inner_ad_group_id: data.inner_id });
      if (res.result.error_code) return MessagePlugin.error(res.result.error_message);
      newId = res.inner_ad_group_id;
    }
    if (data.level === Level.AdLevel) {
      const res = await copyAd({ ...params, inner_ad_id: data.inner_id });
      if (res.result.error_code) return MessagePlugin.error(res.result.error_message);
      newId = res.inner_ad_id;
    }
    // 重新获取目录树
    const newTreeList: TreeNode[] = await getTreeList({
      campaigns: accountCampaigns,
    }, channelConfig.value, basicCampaignNumberInfo);
    const newNodeId = `${data.level}-${newId}`;
    const newNode = getTreeNode(newTreeList, newNodeId, channelConfig.value);
    addNode(data.level, newNode as TreeNode);
  };

  // 删除节点
  const deleteNode = async (treeId: string, level: number) => {
    if (level === Level.AdLevel) {
      return deleteAdDraft({ inner_ad_id: treeId });
    } if (level === Level.AdgroupLevel) {
      return deleteAdgroupDraft({ inner_ad_group_id: treeId });
    } if (level === Level.CampaignLevel) {
      return deleteCampaignDraft({ inner_campaign_id: treeId });
    }
  };

  // 初始化各个层级数据方法
  const getDefaultLevelNode = (level: Level, campaignData?: CampaignType) => {
    const initCampaignData = initDefaultCampaignData(basicCampaignNumberInfo, basicInfo.accountId);
    const treeData = generateTree({
      level,
      initCampaignData,
      initAdgroupData: initDefaultAdgroupData(basicCampaignNumberInfo, 1, campaignData || initCampaignData),
      initAdData: initDefaultAdData(1, campaignData || initCampaignData),
      channelConfig: channelConfig.value,
    });
    return treeData;
  };

  const saveTargetingTemp = (audience: any) => saveTargetingTemplate({
    ...useCommonParams(),
    audience,
  });
  const getTargetingTemp = () => getTargetingTemplate(useCommonParams());
  const deleteTargetingTemp = (templateIds: string[]) => deleteTargetingTemplate({
    ...useCommonParams(),
    template_ids: templateIds,
  });

  return {
    basicInit, init, addNode, deleteNode, copyNode,
    saveAdNode, saveAdgroupNode, saveCampaignNode, getDefaultLevelNode, refreshTree,
    publishAds, saveCondition, saveTargetingTemp, getTargetingTemp, deleteTargetingTemp, globalOptions,
  };
});
