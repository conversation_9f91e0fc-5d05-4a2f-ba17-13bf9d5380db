import { STORE_KEY } from '@/config/config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useGlobalMediaAccountStore } from '@/store/global/media-account.store';
import type { ViewItem } from '@/views/creative/dashboard/components/TabView';
import { ADMAP } from '@/views/trade/ads_management/const';
import { ItableRow } from '@/views/trade/ads_management/type';
import { isDraft } from '@/views/trade/ads_management/utils/base';
import { getConditionList } from '@/views/trade/ads_management/utils/filters';
import { useUrlSearchParams, watchDebounced } from '@vueuse/core';
import { useLoading } from 'common/compose/loading';
import { useId } from 'common/compose/useId';
import { deleteDraft, updateStatus } from 'common/service/td/pivot/edit';
import { getCardOrLine, getTable as getTableFrServer, getTotal as getTotalFrServer } from 'common/service/td/pivot/get';
import { cloneDeep, flatMap, uniq } from 'lodash-es';
import { defineStore, storeToRefs } from 'pinia';
import { ComputedRef, computed, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useTdCustomView } from '../customView.store';
import { IAnyOne, IColGroup, IColItem, IFilters, IFrontValue, ILineParam, IOptionItem, IStatusObj } from './type';
import {
  batchGetFrCentral,
  getLineParam,
  getPivotParam,
  getTableParam,
  initCardData,
  initColDialogAtDefault,
  initSetPivotCfg,
  initConditionCfg,
  isDiffT,
} from './utils/base';
import { getCardData } from './utils/card-data';
import {
  CONDITION_CFG,
  DATATYPE,
  DEFAULT_ATTR,
  DEFAULT_CONDITION,
  DEFAULT_METRIC,
  DRAFT_ROOT,
  INIT_CHECK,
  MODULE,
  PRE_CONVERSIONS_WHITE_GAME,
  PUBLISHED_ROOT,
  V2_MEDIA,
  genCols,
  getDefaultDates,
} from './utils/const';
import { initConditionByCustomView, setConditionByViewParams } from './utils/custom-view';
import { getLineData } from './utils/line-data';
import { getDefaultChecked, getNewChecked } from './utils/table-checked';
import { getDraftResParam, getPublishedResParam, getTableCols, getTableData } from './utils/table-data';
import { getDeleteParams, getDraftParam, getEditParams } from './utils/transform-adsvr';
import { getApiParam } from './utils/transform-pivotsvr';
import { useEnv } from 'common/compose/env';
import { MediaType } from '@/views/trade/ads_creation/common/template/type';

// 折线图loading的显示和隐藏
const {
  isLoading: isLoadingComponent,
  showLoading: showLoadingComponent,
  hideLoading: hideLoadingComponent,
} = useLoading(true);
const { isLoading: isLoadingTable, showLoading: showLoadingTable, hideLoading: hideLoadingTable } = useLoading();
const { isLoading: isLoadingLine, showLoading: showLoadingLine, hideLoading: hideLoadingLine } = useLoading();

export const useTradePivotStore = defineStore(STORE_KEY.TD.PIVOT, () => {
  const { accountObj, setAccountList } = useGlobalMediaAccountStore(); // 需从这里去media传递到service中
  const { getCustomViews, setViewList } = useTdCustomView();
  const { defaultView } = storeToRefs(useTdCustomView());

  const visibleFilterList = ref<string[]>(['account_id']);
  const filters = ref<IFilters>({
    fieldObj: CONDITION_CFG.filters.fieldObj,
    conditionList: CONDITION_CFG.filters.conditionList,
  });
  const currentRoute = useRoute();
  const gameStore = useGlobalGameStore();
  const condition = reactive<{
    id: string;
    game: string;
    media: ComputedRef<string>;
    adStructure: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD;
    default: IFrontValue; // 默认值
    cur: IFrontValue; // 当前值
  }>({
    id: defaultView.value.value,
    game: '', // 从gamestore中渠道
    media: computed<string>(() => (currentRoute.meta.media || '') as string), // 从route中获取
    adStructure: ADMAP.CAMPAIGN,
    default: cloneDeep(DEFAULT_CONDITION), // 分享视图进来的时候 reset 需回到此值
    cur: cloneDeep(DEFAULT_CONDITION), // 当前筛选器中对应的值
  }); // 当前使用的筛选条件
  const cardServerData = ref<any[]>(initCardData(genCols('', '', false))); // 顶部汇总数据,这里的指标会是activeMetric的源
  const isLineExpand = ref<boolean>(true);
  const metricOptions = ref<IOptionItem[]>([]); // 折线图中指标下拉列表
  const metric = ref<string>(DEFAULT_METRIC); // 折线图中指标选中值
  const attribute = ref<string>(DEFAULT_ATTR); // 折线图中指标选中值
  const dateList = ref<any[]>([]);
  const legendObjList = ref<IAnyOne[]>([]); // 折线图legend
  const lineServerData = ref<any>([]); // 固定不变
  const colDialogList = ref<IColGroup[]>([]); // 自定义列弹窗中所有列表 [{ groupName, list }]
  const colDialogCheckedList = ref<IColItem[]>([]); // 自定义列弹窗中选中的列表，是表格展示列的源
  const checkedObj = ref(INIT_CHECK); // 表格中选中的数据
  const draftRootChecked = ref<boolean[]>([]); // draft 分组头是否被选中
  const isExpandChart = ref<boolean>(true); // 是否展开图表
  const statusObj = ref<IStatusObj>(INIT_CHECK); // 当前层级中后代状态统计列表 [{"id","icon":"error"|"pending","text":""}}]

  const { setIds, isId: isHiddenGroupId, clearIds } = useId([]);
  // 筛选器包裹容器对应的配置和下拉列表
  const conditionList = computed(() => getConditionList({
    src: filters.value.conditionList,
    fieldObj: filters.value.fieldObj,
  }));
  // fb渠道下，并且游戏在PRE_CONVERSIONS_WHITE_GAME中时
  const { isFuncom } = useEnv();
  const isFbWhiteGame = computed(() => {
    const flag =      condition.media === 'Facebook' && (PRE_CONVERSIONS_WHITE_GAME.includes(gameStore.gameCode) || isFuncom.value);
    return flag;
  });
  const cardData = computed(() => {
    const res = getCardData(cardServerData.value, tableColumns.value, attribute.value, isDisabledConversions);
    return res;
  });
  // 用于前端展示的折线图数据，会根据lineServerData和activeMetric而变化。
  const lineData = computed(() => getLineData({
    data: lineServerData.value,
    date: condition.cur.date || getDefaultDates(1),
    columns: flatMap(colDialogList.value.map(item => item.list)),
    metric: metric.value,
    attribute: attribute.value,
    legendObjList: legendObjList.value,
    game: condition.game,
  }));

  // 表格中需展示的列，随着自定义列弹窗中的选中列表的变化而变化。
  const tableColumns = computed(() => getTableCols(colDialogCheckedList.value, condition.adStructure));
  const hasDraftRoot = ref<boolean>(false);
  const hasPublishedRoot = ref<boolean>(false);
  // tableData会因为checkedObj中的数据变化而变化
  const draftAllData = ref<any[]>([]); // 表格全量草稿
  const publishedTotalNum = reactive({ campaign: 0, adgroup: 0, ad: 0 }); // 表格已发布全量广告数
  const publishedCurData = ref<any[]>([]); // 表格当前页面已发布的数据
  const tableData = computed(() => getTableData({
    draftAllData: draftAllData.value,
    publishedTotalNum: publishedTotalNum[condition.adStructure],
    publishedCurData: publishedCurData.value,
    curChecked: (checkedObj.value as any)[condition.adStructure || ADMAP.CAMPAIGN],
    adStructure: condition.adStructure,
    columns: tableColumns.value,
    pageIndex: condition.cur.pageIndex || 1,
    pageSize: condition.cur.pageSize || 10,
    hasDraftRoot: hasDraftRoot.value,
    hasPublishedRoot: hasPublishedRoot.value,
    draftRootChecked: draftRootChecked.value,
  }));

  watchDebounced([() => gameStore.gameCode], async ([newGame]) => {
    if (V2_MEDIA.includes(condition.media.toLocaleLowerCase())) {
      if (newGame) await initReq(newGame, condition.media, gameStore.isPcGame());
    }
  });

  watchDebounced([() => condition.media], async ([newMedia]) => {
    if (newMedia && V2_MEDIA.includes(newMedia.toLocaleLowerCase())) {
      cardServerData.value = initCardData(genCols(gameStore.gameCode, newMedia, gameStore.isPcGame()));
      checkedObj.value = getDefaultChecked();
      const { optionObj, emitBatchGetCfg } = batchGetFrCentral(newMedia, gameStore.gameCode);
      filters.value.fieldObj = { ...optionObj.value, account_id: [] };
      await initConditionByCustomView('', gameStore.gameCode, newMedia);
      // await emitBatchGetCfg();
      const [viewListRes] = await Promise.all([
        getCustomViews(
          { game: [gameStore.gameCode, 'allgame'], module: MODULE, type: 'custom' },
          newMedia,
          gameStore.gameCode,
        ),
        emitBatchGetCfg(),
      ]);
      setViewList(newMedia, gameStore.gameCode, viewListRes);
      const { setAccountList } = useGlobalMediaAccountStore();
      await setAccountList({ media: newMedia as MediaType });
      const { accountObj } = useGlobalMediaAccountStore(); // 需从这里去media传递到service中
      filters.value.fieldObj = { ...optionObj.value, account_id: accountObj[newMedia.toLocaleLowerCase()] };
    }
  });

  async function init(): Promise<void> {
    const game = gameStore.gameCode || condition.game;
    if (V2_MEDIA.includes(condition.media.toLocaleLowerCase())) {
      if (game) await initReq(game, condition.media, gameStore.isPcGame());
    }
  }
  async function initReq(game: string, media: string, isPCGame = false): Promise<void> {
    initConditionCfg(game);
    /** -------------第一步、设置 自定义穿梭框中全部列和 表格列 -------------**/
    initColDialogAtDefault(game, media, isPCGame); // 设置默认情况下default视图中列和列穿梭框初始值；【会被下面的 initConditionByCustomView 覆盖】
    const urlParams = useUrlSearchParams<{ code: string }>('history');
    await initConditionByCustomView(urlParams.code, game, media); // 设置filter中的初始值 和 colDialogChecedList的值
    /** -------------第二步、拉取结果数据和下拉列表配置，拉取结果数据前后设置loading效果即可。 --------------**/
    const { optionObj, emitBatchGetCfg } = batchGetFrCentral(media, game);
    const [viewListRes] = await Promise.all([
      // 配置和数据拉取走并发，并用fetchWrapper包裹配置接口,以使用storage缓存,
      getCustomViews({ game: [game, 'allgame'], module: MODULE, type: 'custom' }, condition.media, condition.game),
      emitBatchGetCfg(),
      setAccountList({ media: media as MediaType }),
    ]);
    filters.value.fieldObj = { ...optionObj.value, account_id: accountObj[media.toLocaleLowerCase()] };
    // 先放到外部，首次请求才能够使用fieldObj（如果走并发，需要拆分getTotalFrServer，将请求和数据处理分开，改动较大）
    if (!sessionStorage.pivot_from) {
      // 如果是message_box触发的，则不进行初始化请求，mssage_box里面会调用setPivot，避免重复请求
      setPivot({ isInit: !urlParams.code });
    }
    setViewList(condition.media, condition.game, viewListRes);
  }
  async function setPivot({ newCondition, isInit }: { newCondition?: any; isInit?: boolean }) {
    if (newCondition) {
      condition.cur = cloneDeep(newCondition);
    }
    showLoadingComponent();
    initSetPivotCfg();
    const { published, linePublished, draft, hasLocationFilter, sonDraftStatus } = await getPivotParam(isInit);
    const { card, line, table } = await getTotalFrServer({
      cardParams: getApiParam({ ...published, type: DATATYPE.TOTAL }),
      lineParams: getApiParam({ ...linePublished, type: DATATYPE.DATE }),
      publishedParams: getApiParam({ ...published, type: DATATYPE.PIVOT, needExtra: true }),
      draftParams: getDraftParam(draft),
      adStructure: condition.adStructure,
      columns: tableColumns.value,
      textMap: { ...filters.value.fieldObj },
      media: condition.media as MediaType,
      game: condition.game,
    });
    cardServerData.value = card;
    lineServerData.value = line;
    publishedCurData.value = table.publishedCurData;
    setDraftPublished(table, condition.adStructure);
    // setStatusMap(table);
    // 这里判断 是否有分组头, 因为分组头收缩时会触发后台数据查询逻辑，故不能完全基于后台数据判断是否有分组头
    hasDraftRoot.value = hasLocationFilter || (table?.draftAllData && table?.draftAllData.length > 0);
    hasPublishedRoot.value = sonDraftStatus || (table?.publishedTotalNum && table?.publishedTotalNum > 0);
    if (hasLocationFilter) setIds(DRAFT_ROOT.id);
    setExpandChart(isDiffT(dateList.value));
    // 清除标记
    sessionStorage.pivot_from = '';
    hideLoadingComponent();
  }

  async function setTable({
    cur,
    noNeedDraft = isHiddenGroupId(DRAFT_ROOT.id),
    noNeedPublished = isHiddenGroupId(PUBLISHED_ROOT.id),
    from = '',
    noCache = 1, // 此方法默认都不使用缓存
  }: {
    cur?: IFrontValue;
    noNeedDraft?: boolean;
    noNeedPublished?: boolean;
    from?: string;
    noCache?: number;
  }) {
    showLoadingTable();
    const { published, draft, needTotalNum } = await getTableParam({
      cur,
      noNeedDraft,
      noNeedPublished,
      from,
    });
    const res = await getTableFrServer({
      publishedParams: getApiParam(published),
      draftParams: getDraftParam(draft),
      adStructure: condition.adStructure,
      columns: tableColumns.value,
      draftAllLength: from === 'changeTab' ? undefined : draftAllData.value.length,
      textMap: { ...filters.value.fieldObj },
      existAllDrafts: from === 'page' ? draftAllData.value : undefined,
      noCache,
    });
    publishedCurData.value = res.publishedCurData || [];
    setDraftPublished(res, condition.adStructure, needTotalNum);
    // setStatusMap(res);
    hideLoadingTable();
  }
  function setDraftPublished(res: any, adStructure: string, needTotalNum = true) {
    if (needTotalNum) {
      // 只有翻页的时候为false，需要保留原有state中 值
      (publishedTotalNum as any)[adStructure] = res?.publishedTotalNum || 0;
      draftAllData.value = res.draftAllData || [];
    }
  }
  function setChecked({ type, value, row }: { type: string; value: boolean; row: ItableRow }) {
    let serverData = [] as any;
    if (isDraft(row)) {
      const pageIndex = condition.cur.pageIndex || 1;
      const pageSize = condition.cur.pageSize || 10;
      if (type === 'group') draftRootChecked.value[pageIndex - 1] = value;
      serverData = tableData.value.draftAllData
        .slice(0, pageIndex * pageSize)
        .filter((v, index) => !!draftRootChecked.value[Math.floor(index / pageSize)])
        .map((param: any) => getDraftResParam(param));
    } else {
      serverData = tableData.value.publishedCurData.map((param: any) => getPublishedResParam(param));
    }
    // 需做一下去重
    checkedObj.value = getNewChecked({
      serverData,
      row,
      type,
      value,
      checkedObj: checkedObj.value,
      adStructure: condition.adStructure,
    });

    // 当newChecked中没有一行被勾选时，顶部折线图中的attribute需重置为total
    if ((checkedObj.value as any)[condition.adStructure].length === 0) {
      attribute.value = DEFAULT_ATTR;
      setLine({ metric: metric.value, attribute: attribute.value });
    }
  }
  // 非tableColumns中的指标时折线图需发起后台查询; 当condition中searchbox有值时，不需要先查前20条campaign;其余均需先查前20条campaign
  async function setLine(param: ILineParam, isShowChart = false) {
    showLoadingLine();
    const line = await getLineParam(param);
    const res = await getCardOrLine(getApiParam({ ...line, type: DATATYPE.DATE }), 'date');
    if (param.metric) metric.value = param.metric;
    if (param.attribute) attribute.value = param.attribute;
    lineServerData.value = res;
    setExpandChart(isShowChart ? true : isDiffT(dateList.value));
    hideLoadingLine();
  }
  // viewTab切换的同时，defaultValue也同时更新
  function setConditionId(viewItem: ViewItem) {
    setVisibleFilterList([]);
    condition.id = viewItem.value;
    // 判断一下是不是默认视图, 如果是的话直接用param赋值就行了
    if (viewItem.value === defaultView.value.value) {
      filters.value.conditionList = cloneDeep(CONDITION_CFG.filters.conditionList);
      condition.cur = cloneDeep(defaultView.value.param);
      initColDialogAtDefault(defaultView.value.game, defaultView.value.media, defaultView.value.isPCGame);
      setVisibleFilterList(['account_id']);
      return setPivot({ isInit: true });
    }
    setConditionByViewParams(viewItem.param);
    setPivot({});
  }

  // 批量删除草稿或者 修改已发布数据的状态
  async function batchEdit({
    type,
    list,
    media,
    adStructure,
  }: {
    type: string;
    list: any[];
    media: string;
    adStructure: string;
  }) {
    let res = [];
    const { game } = condition;
    showLoadingTable();
    if (type === 'delete') {
      const param = getDeleteParams({ type, list, game, media, adStructure });
      res = await deleteDraft(media, adStructure, param);
    } else if (['pause', 'enable'].includes(type)) {
      const newList = getEditParams({ type, list, game, media, adStructure });
      res = await updateStatus({ media, adStructure, list: newList });
    }
    hideLoadingTable();
    return res;
  }

  // 设置可见的筛选条件组件的key
  function setVisibleFilterList(val: string[]) {
    const conditionKeyList = CONDITION_CFG.filters.conditionList.map(item => item.ext.key);
    visibleFilterList.value = uniq(val).filter(item => conditionKeyList.includes(item));
  }

  function setExpandChart(val: boolean) {
    isExpandChart.value = val;
  }
  // 是否禁用conversions选项卡和metric选项
  function isDisabledConversions(key: string, attr: string) {
    return isFbWhiteGame.value && ['conversions', 'cpa', 'cvr'].includes(key) && attr === 'total';
  }
  return {
    setIds,
    isHiddenGroupId,
    clearIds,
    conditionList, // 筛选器包裹容器相关配置
    condition, // 当前筛选条件对应的值
    colDialogList, // 自定义穿梭框全部数据
    colDialogCheckedList, // 自定义穿梭框选中数据
    dateList,
    isLineExpand,
    metricOptions, // 折线图指标下拉列表
    metric, // 折线图指标当前选中值
    attribute, // 折线图attribute by当前选中值
    legendObjList, // 折线图legend配置
    cardServerData, // 滚动卡片server端数据
    cardData, // 滚动卡片数据
    lineData, // 折线数据
    draftRootChecked, // 每一页draft分组头是否被选中
    checkedObj, // 表格中选中的列
    statusObj, // 状态统计数据
    tableData, // 草稿数据，已发布的数据，当前表格展示的数据和条数
    tableColumns, // 表格列
    init, // 初始化方法，获取配置信息和数据
    setLine, // 查询后台接口，设置折线图数据
    setTable, // 查询后台接口，设置表格数据
    setPivot, // 查询后台接口，设置卡片，折线图，表格数据
    setChecked, // 设置表格勾选项数据
    setConditionId,
    isLoadingComponent, // 页面组件是否loading
    isLoadingTable, // 表格是否loading
    isLoadingLine, // 折线图是否loading
    batchEdit, // 批量编辑
    filters,
    visibleFilterList, // 可见的筛选条件的key
    setVisibleFilterList,
    isExpandChart, // 是否展开折线图
    setExpandChart, // 更改折线图的显示和隐藏,
    isDisabledConversions, // 是否禁用Conversions卡片
  };
});
