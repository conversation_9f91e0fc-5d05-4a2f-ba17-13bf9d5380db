import { ADMAP } from '@/views/trade/ads_management/const';
import type { ICondition, ISearchValue } from 'common/components/SearchBox/type';
import { ISort } from 'common/components/trade/ads-management/table/type';
import dayjs, { Dayjs } from 'dayjs';
import type { IAccountItem } from '@/store/global/media-account';
import type { StringKeyAnyValueObject } from 'common/types/report';
export interface IPivotStoreType {
  config: IConfig; // 配置信息
  conditionId: string; // 查询条件id
  condition: ICondition;
  activeMetric: string; // 顶部处于激活态的指标
  totalData: any[]; // 顶部汇总数据
  lineData: any[]; // 中间折线图
  tableData: {
    draftData: any[];
    publishedData: any[];
    checkedObj: ITotalAdObj;
  };
}

export interface IConfig {
  viewList: any[];
  filters: IFilterItem[]; // 筛选器相关配置，label标签，options下拉列表
}
export interface IFilterItem {
  lable: string;
  componentName?: string; // 筛选器使用的组件 select, multipleselect, datepicker
  options?: IOptionItem[];
  key: string; // 与condition字段中的key 一一对应
}

export interface IOptionItem {
  label: string;
  value: string | number;
}
export interface ITotalAdObj {
  campaign: any[];
  adgroup: any[];
  ad: any[];
}
export interface IOrderby {
  key: string;
  value: string;
  from: string; // eg. 'front':来自前端表格的设置,只有一行；  'backend'：基于后台需求的补充
}

export interface IDstTableItem {
  id: string;
  name: string;
  opt_status: boolean;
  deliverys_status: string;
  spend: number;
  impressions: number;
  cpm: number;
  data_fr: string;
}

export interface IFilters {
  fieldObj: {
    account_id?: IAccountItem[];
    delivery_status?: INameValue[];
    opt_status?: INameValue[];
    aix_campaign_type?: INameValue[];
    aix_locations?: INameValue[];
  };
  conditionList: any[];
}

export interface IConditionItem {
  key: string;
  operator?: string;
  value: string | number | (string | number | dayjs.Dayjs)[];
  from?: string;
}
export interface IAttrItem {
  select: string[];
  where?: ICondition[];
}
export interface IAttrObj {
  // 广告层级需满足从高到底
  campaign?: IAttrItem;
  adgroup?: IAttrItem;
  ad?: IAttrItem;
}
export interface IExtraObj {
  orderby?: IOrderby[];
  pageSize?: number;
  pageNum?: number;
  start?: number;
}

export interface IApiParam {
  game: string;
  media: string;
  adStructure: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD;
  metricKeys?: string[]; // 成效相关指标
  metricWhere?: IConditionItem[]; // 成效相关查询条件 {key, operator, value}
  attrObj?: IAttrObj;
  extraObj?: IExtraObj;
}
export interface IFrontValue extends StringKeyAnyValueObject {
  date?: Dayjs[];
  campaign_name?: string[];
  adgroup_name?: string[];
  ad_name?: string[];
  account_id?: string[];
  delivery_status?: string[];
  opt_status?: string[];
  aix_campaign_type?: string[];
  aix_locations?: string[];
  sort?: ISort | any;
  pageSize?: number;
  pageIndex?: number;
  [string]?: any[];
  search_box?: ISearchValue[]; // 用于做搜索框检索，转化成后台参数时需要剔除
}
export type IFormat = 'float' | 'percent' | 'money' | 'date' | 'int' | 'numShort' | 'dollar';
export type IFullFormat =
  | string
  | 'undefined'
  | 'date'
  | 'percent'
  | 'int'
  | 'numShort'
  | 'text'
  | 'float'
  | 'money'
  | 'floatToFixed'
  | 'moneyToFixed';
export interface IColItem {
  index?: number;
  colKey: string;
  titleStr?: string;
  title?: string | Function;
  width?: number;
  groupName?: string;
  hidden?: boolean;
  required?: boolean;
  default?: boolean;
  type?: string;
  format?: IFullFormat;
  cardFormat?: IFullFormat;
  prefix?: { [key: string]: string };
  sorter?: boolean;
  fixed?: string;
  tips?: string;
  ellipsisTitle?: object;
  ellipsis?: boolean;
  version?: number;
}
export interface IColGroup {
  groupName: string;
  list: IMetric[];
}
export interface IMetric {
  title: string;
  colKey: string;
}

export interface IMetricAttrs {
  metricKeys?: string[];
  attrObj?: IAttrObj;
}

export interface IExtra {
  sort?: ISort;
  orderby?: IOrderBy[];
  pageSize?: number;
  pageNum?: number;
}
export interface IBaseCondition {
  game: string;
  media: string;
  adStructure: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD;
  columns: IColItem[];
  attribute?: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD;
}

export interface IAPIParam {
  type: string;
  game: string;
  media: string;
  adStructure: string;
  needTotalNum?: boolean;
  metricKeys: string[];
  metricWhere: IWhere[];
  attrObj?: IAttrObj;
  extraObj?: IExtraObj; // 有attrObj时一定要有extraObj,否则后台validate-class校验失败。
  noNeed?: boolean; // 不需要查询已发布数据
  noCache?: number; // 不需要使用缓存
}

export interface IAttrObj {
  campaign?: IAttrItem;
  adgroup?: IAttrItem;
  ad?: IAttrItem;
}

export interface IExtraObj {
  orderby?: IOrderby[];
  pageNum?: number;
  pageSize?: number;
  start?: number;
}

export interface IWhere {
  key: string;
  operator?: string;
  value: (string | number)[] | string | number;
}

export interface IDraftParam {
  media: string;
  game_code: string;
  account_ids?: string[];
  filter?: object;
  __game?: string;
  advertise_type?: string;
  conditions?: IDraftConditionItem[];
  offset?: number;
  limit?: number;
  noNeed?: boolean; // 不需要草稿
}
export interface IDraftConditionItem {
  column: string;
  operator: stinger;
  value: string | string[];
}
export interface ITableServerData {
  publishedCurData?: any[];
  publishedTotalNum?: number;
  draftAllData?: any[];
}

export interface CommonConfigItem {
  label?: string;
  value?: string;
  source_content?: string;
  target_content?: string;
  children?: CommonConfigItem[];
  is_visible?: boolean;
  source_category?: string;
}

export interface IDeliveryStatusValueObj {
  [ADMAP.CAMPAIGN]?: string[];
  [ADMAP.ADGROUP]?: string[];
  [ADMAP.AD]?: string[];
}

export interface ITextMapParam {
  account_id?: IAccountItem[];
  delivery_status?: INameValue[];
  opt_status?: INameValue[];
  aix_campaign_type?: INameValue[];
  aix_locations?: INameValue[];
}

export interface INameValue {
  label: string;
  value: string;
  target?: string;
  tip?: '';
  children?: INameValue[];
}

export interface IDeleteGGParam {
  status: string;
  inner_campaign_id?: string;
  inner_ad_group_id?: string;
  inner_ad_id?: string;
}
export interface ILineParam {
  metric?: string;
  attribute?: string;
}
export interface IAnyOne {
  [x: string]: any;
}

export interface ILineData {
  yField: string;
  yFieldFormat: Function;
  data: any[];
  series?: undefined | any[];
}

export interface IStatusObj {
  campaign: IStatusItem[];
  adgroup: IStatusItem[];
  ad: IStatusItem[];
}
export interface IStatusItem {
  [key: string]: string;
  icon: string;
  text: string;
}

export interface IStatusRes {
  draft?: IStatusItem[];
  published?: IStatusItem[];
}
export interface IStatusResItem {
  status?: string;
  num?: string;
}

export interface HistoryParams {
  game_code: string;
  account_id?: string;
  campaign_resource_name?: string;
  adgroup_resource_name?: string;
  ad_resource_name?: string;
  campaign_id?: string;
  adgroup_id?: string;
  ad_id?: string;
  start_time: string;
  end_time: string;
  page: number;
  page_size: number;
}

export interface HistoryItem {
  id: string;
  objective: string;
  operator: string;
  change_date_time: string;
  campaign_resource_name: string;
  adgroup_resource_name: string;
  change_summary: string;
  changes: string[];
}
