import { useGlobalGameStore } from '@/store/global/game.store';
import { ADMAP } from '@/views/trade/ads_management/const';
import { isDraft } from '@/views/trade/ads_management/utils/base';
import { useFetchWrapper } from 'common/compose/request/request';
import { SERVER_MEDIA_MAP } from 'common/const/trade';
import { batchGetCfg, getCommonConfig, getConfig } from 'common/service/td/pivot/config';
import { getCardOrLine } from 'common/service/td/pivot/get';
import dayjs, { Dayjs } from 'dayjs';
import { flatMap, uniq } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { useRoute } from 'vue-router';
import { useTradePivotStore } from '../index.store';
import { IAnyOne, IColItem, IDeliveryStatusValueObj, ILineParam } from '../type';
import {
  DATATYPE,
  DEFAULT_ATTR,
  DEFAULT_METRIC,
  FATHER_TREE_AD_MAP,
  SON_TREE_AD_MAP,
  getDefaultValues,
  getDefaultDates,
  getCfgCols,
  DEFAULT_PAGE_SIZE,
  DEFAULT_PAGE_INDEX,
} from './const';
import { getLegendFrChecked, getLineParamWithDate, getTopParam } from './line-data';
import { getDefaultChecked } from './table-checked';
import {
  getColByEnvGame,
  getDefaultCols,
  getMediaIdsByFatherDevlieryStatus,
  getMediaIdsFrDraftApi,
  getMetricOpt,
  toGroup,
} from './table-data';
import { getApiParam } from './transform-pivotsvr';
import { useAuthStageStore } from '@/store/global/auth.store';
import type { TActionItem } from 'common/service/baseinfo';
import { useEnv } from 'common/compose/env';
import { MediaType } from '@/views/trade/ads_creation/common/template/type';

export function getGameMedia(): [string, string] {
  const gameStore = useGlobalGameStore();
  const game = gameStore.gameCode;
  const route = useRoute();
  const media = typeof route.meta.media === 'string' ? route.meta.media : '';
  return [game, media];
}
export function getDefaultDayNum(): number {
  const { getGameTimezone } = useGlobalGameStore();
  const timezone = getGameTimezone();
  // dayjs().get('h') < 18
  return timezone <= -8 ? 1 : 0;
}
export function getLowDefault() {
  const num = getDefaultDayNum();
  const defaultV = getDefaultValues(num);
  const { date } = defaultV;
  const res = JSON.parse(JSON.stringify(defaultV));
  return { ...res, date };
}

export function isDefaultDate(src: dayjs.Dayjs[]): boolean {
  if (!src) return false;
  const num = getDefaultDayNum();
  const dates = getDefaultDates(num);
  return !isDiffT([src[0], dates[0]]) && !isDiffT([src[1], dates[1]]);
}

export function getCommonKey({ game, media }: { game?: string; media?: string }) {
  const commonGame = game ? game : 'default';
  const commonMedia = media ? media.toLocaleLowerCase() : 'default';
  return `${commonGame}-td-${commonMedia}-default`;
}

export function initConditionCfg(game: string) {
  const { condition } = storeToRefs(useTradePivotStore());
  condition.value.game = game;
  const defaultV = getLowDefault();
  const { date } = defaultV;
  const conditionV = { ...JSON.parse(JSON.stringify(defaultV)), date };
  condition.value.default = conditionV;
  condition.value.cur = conditionV;
}
/**
 * @description 设置默认视图default 的列和列穿梭框初始值
 */
export function initColDialogAtDefault(game: string, media: string, isPCGame: boolean) {
  const { condition, metricOptions, colDialogList, colDialogCheckedList } = storeToRefs(useTradePivotStore());
  const metricsRes = getColByEnvGame(getCfgCols(game, media, isPCGame), game); // 测试环境v2版本均显示，正式环境需基于游戏做灰度。
  metricOptions.value = getMetricOpt(metricsRes as any);
  colDialogList.value = toGroup(metricsRes as any);
  colDialogCheckedList.value = getDefaultCols(metricsRes, condition.value.adStructure); // 从后台配置中取默认的列弹窗选中值
}
/**
 * @description 用于给 defaultView 的param赋初值，用在custom.view.store.ts的定义中。
 **/
export function getDefaultColDialogCheckedList(game: string, media: string, adStructure: ADMAP, isPCGame: boolean) {
  const metricsRes = getColByEnvGame(getCfgCols(game, media, isPCGame), game); // 测试环境v2版本均显示，正式环境需基于游戏做灰度。
  return getDefaultCols(metricsRes, adStructure); // 从后台配置中取默认的列弹窗选中值
}

export function initSetPivotCfg() {
  const { condition, checkedObj, metric, attribute, legendObjList } = storeToRefs(useTradePivotStore());
  const { clearIds } = useTradePivotStore();
  checkedObj.value = getDefaultChecked();
  clearIds();
  condition.value.cur.pageIndex = DEFAULT_PAGE_INDEX;
  condition.value.cur.pageSize = DEFAULT_PAGE_SIZE;
  metric.value = DEFAULT_METRIC;
  attribute.value = DEFAULT_ATTR;
  legendObjList.value = [];
}
/**
 * @description 表格初始化
 */
export function initCardData(columns: any) {
  return columns
    .filter((one: any) => one?.default && one?.type !== 'attr')
    .map((one: any) => {
      const param = {
        icon: '',
        title: one.title,
        key: one.colKey,
        indicatorValues: '-',
      };
      return param;
    });
}

/**
 * @description 通过 useFetchWrapper接口包一下请求，添加storage缓存
 * @export
 */
export function getStatusByFetchWrapper(media: string, adStructure: string, game: string) {
  const { data, emit } = useFetchWrapper<any, any>(
    getConfig,
    {
      key: getCommonKey({ media }),
      type: `aix_${adStructure}_delivery_status`,
      where: { game_code: game },
    },
    {
      storage: 'getConfig', // 先用local storage 同步更新
    },
  );
  return { data, emit };
}

/**
 * @description 通过 useFetchWrapper接口包一下请求，添加storage缓存
 * @export
 */
export function getByFetchWrapper(media: string, game: string) {
  const optionList: any[] = [];
  const emitList: any[] = [];
  ['aix_campaign_delivery_status', 'aix_campaign_type', 'aix_locations'].forEach((type) => {
    const { data, emit } = useFetchWrapper<any, any>(
      getConfig,
      {
        key: getCommonKey({ media: type === 'aix_locations' ? 'default' : media }),
        type,
        where: { game_code: game },
      },
      {
        storage: 'getConfig', // 先用local storage 同步更新
      },
    );
    optionList.push(data);
    emitList.push(emit);
  });
  return { optionList, emitList };
}
/**
 * @description 批量请求配置信息
 * @export
 */
export function batchGetByFetchWrapper(
  media: string,
  game: string,
): {
    optionList: any;
    emitBatchGetCfg: Function;
  } {
  const paramList = ['aix_campaign_delivery_status', 'aix_locations'].map((type) => {
    const params = {
      key: getCommonKey({ media: type === 'aix_locations' ? 'default' : media }),
      type,
      where: { game_code: game },
    };
    return params;
  });
  const { data: optionList, emit: emitBatchGetCfg } = useFetchWrapper<any, any>(
    batchGetCfg,
    { paramList },
    {
      storage: 'batchGetCfg', // 先用local storage 同步更新
    },
  );
  return { optionList, emitBatchGetCfg };
}

/**
 * // https://aix-doc.pages.woa.com/hooks/fetch/
 * @description 走后台david的公共配置服务
 * @export
 * @param {string} media
 * @param {string} game
 * @returns {{
 *     optionList: any;
 *     emitBatchGetCfg: Function;
 *   }}
 */
export function batchGetFrCentral(
  media: string,
  game: string,
): {
    optionObj: any;
    emitBatchGetCfg: Function;
  } {
  const { data: optionObj, emit: emitBatchGetCfg } = useFetchWrapper<any, any>(
    getCommonConfig,
    { media: (SERVER_MEDIA_MAP as any)[media].attr_server, game },
    {
      storage: 'getCommonConfig', // 先用local storage 同步更新
    },
  );
  return { optionObj, emitBatchGetCfg };
}

export const unique = (arr: Array<any>, adStructure: string) => {
  const res = new Map();
  if (!arr || arr.length === 0) return [];
  return arr.filter((item) => {
    const val = isDraft(item) ? `inner_${adStructure}_id` : `${adStructure}_id`;
    return !res.has(item[val]) && res.set(item[val], 1);
  });
};

/**
 * @description 基于筛选器勾选值；先过滤出草稿 或非草稿的数据；再按照广告层级切分
 * @returns {IDeliveryStatusValueObj}
 */
export const getDeliveryStatusObj = ({
  list,
  isDraft = false,
}: {
  list: string[];
  media: string;
  isDraft?: boolean;
}): IDeliveryStatusValueObj => {
  const obj: any = {};
  // eslint-disable-next-line no-param-reassign
  list = generateFilter(list);
  if (!list || list.length === 0) return obj;
  const filterList = list.filter((v) => {
    const [, , type = ''] = v.split('|');
    return isDraft ? type === 'draft' : type !== 'draft';
  });
  if (filterList.length === 0) return obj;
  [ADMAP.CAMPAIGN, ADMAP.ADGROUP, ADMAP.AD].forEach((adStructure) => {
    const filters = filterList
      .filter((v) => {
        const [ad = ''] = v.split('|');
        return adStructure === ad;
      })
      .map((v) => {
        const [, value = ''] = v.split('|');
        return value;
      });
    if (filters && filters.length > 0) {
      obj[adStructure] = filters;
    }
  });
  return obj;
};

function generateFilter(list: string[]) {
  const newList: string[] = [];
  list.forEach((item) => {
    const data = item.split(',');
    data.forEach(str => newList.push(str));
  });
  return newList;
}

/**
 * @description 查询adStructure层级数据时，筛选器delivery status中有后代草稿状态被勾选
 * @export
 * @param {*} list
 * @param {string} media
 * @param {(ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD)} adStructure
 * @returns {boolean}
 */
export function hasSonDraftStatus(list: any, adStructure: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD): boolean {
  if (!list || list.length === 0 || adStructure === ADMAP.AD) return false;
  return list.some((v: string) => {
    const [ad, , type = ''] = v.split('|');
    return (SON_TREE_AD_MAP as any)[adStructure].includes(ad) && type === 'draft';
  });
}
export function hasSelfDraftStatus(list: any, adStructure: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD): boolean {
  if (!list || list.length === 0) return false;
  return list.some((v: string) => {
    const [ad, , type = ''] = v.split('|');
    return ad === adStructure && type === 'draft';
  });
}
export function hasSelfSonDraftStatus(list: any, adStructure: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD): boolean {
  return hasSonDraftStatus(list, adStructure) || hasSelfDraftStatus(list, adStructure);
}
/**
 * @description 查询adStructure层级数据时，筛选器delivery status中有祖先已发布状态被勾选
 * @export
 * @param {*} list
 * @param {string} media
 * @param {(ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD)} adStructure
 * @returns {boolean}
 */
export function hasFatherPublishedStatus(list: any, adStructure: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD) {
  if (!list || list.length === 0 || adStructure === ADMAP.CAMPAIGN) return false;
  return list.some((v: string) => {
    const [ad, , type = ''] = v.split('|');
    return (FATHER_TREE_AD_MAP as any)[adStructure].includes(ad) && type !== 'draft';
  });
}
export function hasSelfPublishedStatus(list: any, adStructure: ADMAP) {
  if (!list || list.length === 0) return false;
  return list.some((v: string) => {
    const [ad, , type = ''] = v.split('|');
    return ad === adStructure && type !== 'draft';
  });
}
export function hasSelfFatherPublishedStatus(list: any, adStructure: ADMAP) {
  return hasSelfPublishedStatus(list, adStructure) || hasFatherPublishedStatus(list, adStructure);
}
/**
 * @desc str：可输入数字，字符串或者数组
 * isNumNeed = true时：
 * 输入：123 * 输出：'123'
 * 输入：[123,456] * 输出：'123','456'
 * -----------------------------------
 * isNumNeed = false时
 * 输入：123 * 输出：123
 * 输入：[123,456] * 输出：123,456
 */
export const addQuotation = ({ str, isNumNeed = false }: { str: string | string[]; isNumNeed?: boolean }) => {
  if (!str) return '';
  const arr = genArr(str);
  return arr.map(v => (isNumNeed || isNaN(Number(v)) ? `'${v}'` : v)).join(','); // '123', '345'
};
export const genArr = (src: any) => (Array.isArray(src) ? src.filter(x => x) : [src].filter(x => x));

/**
 * @description
 * 输入： dayjs类型['20230401','20230403']
 * 输出: string类型['2022-04-01', '2022-04-02', '2022-04-03']
 */
export function getDateArr([start, end]: [start: Dayjs, end: Dayjs]): string[] {
  const res = [];
  const days = dayjs(end).diff(dayjs(start), 'days');
  for (let i = 0; i <= days; i++) {
    res.push(dayjs(start).add(i, 'day')
      .format('YYYY-MM-DD'));
  }
  return res;
}

/**
 * @description 顶部seachbox是否有值，表格中checkbox是否有勾选；用于折线图分层级展示时是否取top20
 * @export
 * @returns
 */
export function hasCheckedOrSearchBox() {
  const { condition, checkedObj } = storeToRefs(useTradePivotStore());
  const { adStructure } = condition.value;
  if (checkedObj && (checkedObj.value as any)[adStructure].length > 0) {
    return true;
  }
  const searchBox = condition.value.cur.search_box;
  return searchBox?.some(({ condition }) => condition?.length > 0);
}

/**
 * @description 基于后代草稿状态查当前已发布
 */
export async function getPublishedParam(common: any, sonDraftStatus: boolean) {
  const publishedParam = { ...common };
  const { condition: conditionRef } = storeToRefs(useTradePivotStore());
  const condition = conditionRef.value;
  // 假如condition.cur中delivery_status中有子层级的草稿状态勾选时，需要基于此得到相应的已发布id
  if (sonDraftStatus) {
    const ids = await getMediaIdsFrDraftApi();
    if (ids && ids.length > 0) {
      (publishedParam as any).extraCondition = { [`${condition.adStructure}_id`]: ids };
    }
  }
  return publishedParam;
}

/**
 * @description 基于祖先已发布状态查当前草稿
 */
export async function getBaseDraftParam(common: any, hasLocationFilter: boolean) {
  const { condition: conditionRef } = storeToRefs(useTradePivotStore());
  const condition = conditionRef.value;
  const fatherPublishedStatus = hasFatherPublishedStatus(condition.cur.delivery_status, condition.adStructure);
  let extraCondition = {};
  const noNeed = hasLocationFilter;
  if (!noNeed && fatherPublishedStatus) {
    const idObjList = await getMediaIdsByFatherDevlieryStatus();
    if (idObjList && idObjList.length > 0) {
      extraCondition = idObjList;
    }
  }
  return { ...common, noNeed, extraCondition };
}
export const isDiffT = (date: dayjs.Dayjs[]) => date.length > 1 && dayjs(date[0]).format('YYYY-MM-DD') !== dayjs(date[1]).format('YYYY-MM-DD');

export async function getPivotParam(isInit?: boolean) {
  const { condition: conditionRef, attribute, tableColumns, dateList } = storeToRefs(useTradePivotStore());
  const condition = conditionRef.value;
  const { adStructure } = condition;
  const common = {
    baseCondition: {
      game: condition.game,
      media: condition.media,
      adStructure,
      attribute: attribute.value as ADMAP,
      columns: tableColumns.value,
    },
    condition: condition.cur,
  };
  if (isInit) {
    const linePublished = getLineParamWithDate(common, true);
    dateList.value = getDateArr(linePublished.condition.date);
    return { published: common, linePublished, draft: common, sonDraftStatus: false, hasLocationFilter: false };
  }
  // 假如condition.cur中delivery_status中有子层级的草稿状态勾选时，需要基于此得到相应的已发布id
  const sonDraftStatus = hasSonDraftStatus(condition.cur.delivery_status, adStructure);
  const published = await getPublishedParam(common, sonDraftStatus);
  dateList.value = getDateArr(published.condition.date);
  const hasLocationFilter = condition.cur?.aix_locations && condition.cur?.aix_locations?.length > 0;
  // 假如condition.cur中delivery_status中有祖先层级 已发布状态勾选时，需要基于此得到相应的草稿
  const draft = await getBaseDraftParam(common, hasLocationFilter || false);
  return { published, linePublished: published, draft, sonDraftStatus, hasLocationFilter };
}

export async function getTableParam({
  cur,
  noNeedPublished,
  noNeedDraft,
  from,
}: {
  cur: any;
  noNeedPublished: boolean;
  noNeedDraft: boolean;
  from?: string;
}) {
  const { condition: conditionRef, tableColumns, checkedObj } = storeToRefs(useTradePivotStore());
  const condition = conditionRef.value;
  const curCondition = cur ? cur : condition.cur;
  const { game, media, adStructure } = condition;
  // checkedObj 勾选父层级查询子层级用, clickedObj 点击父层级名查子层级用
  const commonParams = {
    baseCondition: { game, media, adStructure, columns: tableColumns.value },
    condition: curCondition,
    checkedObj: checkedObj.value,
  };
  const needTotalNum = from !== 'page';
  const published = {
    ...commonParams,
    type: DATATYPE.PIVOT,
    needExtra: true,
    noNeed: noNeedPublished,
    needTotalNum,
  };
  // 假如condition.cur中delivery_status中有子层级的草稿状态勾选时，需要基于此得到相应的已发布id
  if (hasSonDraftStatus(curCondition.delivery_status, adStructure)) {
    const ids = await getMediaIdsFrDraftApi();
    if (ids && ids.length > 0) {
      (published as any).extraCondition = { [`${adStructure}_id`]: ids };
    }
  }
  const draft = { ...commonParams, noNeed: noNeedDraft };
  // 假如condition.cur中delivery_status中有祖先层级的已发布状态勾选时，需要基于此得到相应的草稿
  if (hasFatherPublishedStatus(curCondition.delivery_status, adStructure)) {
    const idObjList = await getMediaIdsByFatherDevlieryStatus();
    if (idObjList && idObjList.length > 0) {
      (draft as any).extraCondition = idObjList;
    }
  }
  return { published, draft, needTotalNum };
}

export async function getLineParam(param: ILineParam) {
  const {
    colDialogList,
    metric,
    condition: conditionRef,
    tableColumns,
    checkedObj,
    dateList,
    legendObjList,
  } = storeToRefs(useTradePivotStore());
  const condition = conditionRef.value;
  const totalColList = flatMap(colDialogList.value.map(item => item.list));
  const colItem = totalColList.find(({ colKey = '' }) => colKey === metric.value) as IColItem;
  const baseCondition = {
    game: condition.game,
    media: condition.media,
    attribute: param.attribute as ADMAP,
    adStructure: param.attribute === 'total' ? ADMAP.CAMPAIGN : (param.attribute as ADMAP),
    columns: tableColumns.value.concat([colItem]),
  };
  let common = { baseCondition, condition: condition.cur, checkedObj: checkedObj.value };
  const datesInCon = condition.cur.date as dayjs.Dayjs[];
  const isInit = isDefaultDate(datesInCon) && dateList.value.length !== uniq(datesInCon).length;
  if (isInit) {
    common = getLineParamWithDate(common, isInit);
  }
  legendObjList.value = getLegendFrChecked();
  if (!hasCheckedOrSearchBox() && param.attribute !== 'total') {
    // 查相应层级media_id; condition中有attr相关筛选时，attrObj才有值
    const topParam = getTopParam({ baseCondition, param, colItem, condition, isInit });
    const res = await getCardOrLine(getApiParam({ ...topParam, type: DATATYPE.TOTAL, needExtra: true }), '');
    if ('extraCondition' in common && !(`${param.attribute}_id` in (common as any).extraCondition)) {
      (common as any).extraCondition = {
        [`${param.attribute}_id`]: res.map((one: IAnyOne) => one[`${param.attribute}_id`]).filter((x: any) => x),
      };
    }
    legendObjList.value = res;
  }
  return common;
}

export function getValidAccount(gameCode: string, media: MediaType | '') {
  const { baseInfo } = storeToRefs(useAuthStageStore());
  if (!baseInfo.value) return [];

  let validAccounts: string[] = [];
  const right = baseInfo.value.actions?.['Trading Desk']?.find((item: TActionItem) => item.dimension?.game === gameCode && !!item.dimension?.accounts);
  if (right?.dimension?.accounts) {
    validAccounts = right?.dimension?.accounts[media] || [];
  }
  return validAccounts;
}

/**
 * @description gcp上部署的demo游戏；（1）默认日期********-******** （2）先暂时屏蔽草稿接口
 */
export const isDemoInGCP = () => {
  const { isDemoGame } = useGlobalGameStore();
  const { isFuncom } = useEnv();
  return isDemoGame() && isFuncom.value;
};
