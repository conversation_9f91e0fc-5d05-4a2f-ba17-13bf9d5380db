import { toRaw, unref } from 'vue';
import { cloneDeep } from 'lodash-es';

export function getCardData(
  serverCard: any[],
  colList: any[],
  attribute: string,
  isDisabledConversions: (key: string, attr: string) => boolean,
) {
  const cardList: any[] = [];
  colList.forEach((col) => {
    const cardItem = cloneDeep(toRaw(unref(serverCard))).find(item => item.key === col.colKey);
    if (cardItem) {
      // 在fb渠道下，并且当前游戏在白名单中 当attribute为total时， conversions这个卡片要禁用掉
      const newCardItem = {
        ...cardItem,
        disabled: isDisabledConversions(cardItem.key, attribute),
      };
      if (['installs', 'offline_install'].includes(col.colKey) || newCardItem.tips) {
        cardList.push({
          ...newCardItem,
          cardTips: {
            isShow: true,
            tips: cardItem.tips || col.tips,
          },
        });
      } else {
        cardList.push({
          ...newCardItem,
          cardTips: {
            isShow: true,
            tips: cardItem.tips || col.tips,
          },
        });
      }
    }
  });
  return cardList;
}
