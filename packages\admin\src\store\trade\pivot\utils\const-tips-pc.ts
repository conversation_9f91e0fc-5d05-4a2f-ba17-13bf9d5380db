export const PC_TIPS = {
  installs: 'The installs from the media channels which support self-attribution.',
  impressions: 'The number of times your ad is shown on screens.',
  clicks: 'The number of times your ad has been clicked.',
  spend: 'The amount of money spent on ads in your selected period.',
  in_app_action: 'The number of times that someone clicked your ad and performed one of the conversions tracked in your app.',
  conversions: 'The number of times your ad achieved an outcome(conversion), based on the objective and settings you selected.',
  ipm: 'Install per mille impressions. The average amount of installs per 1,000 impressions of your ad.',
  ctr: 'Click through rate. The ratio showing how often people who see your ad end up clicking it. Clicks/Impressions*100%',
  cvr: 'Conversion rate. The ratio shows how often, on average, an ad click leads to a conversion. Conversions / Clicks *100%',
  cpc: 'Cost per click. The average amount of money you\'ve spent on a click. Cost/Clicks',
  cpm: 'Cost per mille impressions. The average amount of money you\'ve spent per 1,000 impressions of your ad.',
  cpi: 'Cost per install from the media channels which support self-attribution. Cost / Install(Channel).',
  cpa: 'Cost per action. The average amount of money you\'ve spent on a conversion action. Cost/Conversion',
  offline_install: 'The number of new User IDs in your selected period. Definition of install: The User ID who opens the game for the first time.',
  offline_cpi: 'Cost per install. Cost / Install.',

  offline_cohort_register: 'The cumulative number of registered accounts by devices who installed the game in your selected period.',
  offline_cohort_register_rate: 'The cumulative number of registered accounts per device by devices who installed the game in your selected period.',
  offline_retention: 'The number of new User IDs in your selected period who have launched the game on Day X.',
  offline_retention_rate: 'D(x) Retention / Install',
  offline_cohort_revenue: 'The amount of revenue up to Day X by new User IDs in your selected period',
  offline_roas: 'D(x) Revenue / Cost',
  offline_cohort_payer: 'The number of new User IDs in your selected period who have completed a purchase up to Day X.',
  offline_cohort_payer_rate: 'D(x) Payer / Install',
  offline_purchase_rate: 'The percentage of devices who installed the game in your selected period and completed the payment up to the latest day.',
  offline_arppu: 'The cumulative amount of revenue per paying device by devices who installed the game in your selected period.',
  offline_ltv: 'D(x) Revenue / Install',

  offline_d1_cohort_register: 'The cumulative number of registered accounts by devices who installed the game in your selected period up to Day 1 .',
  offline_d1_cohort_register_rate: 'The cumulative number of registered accounts per device by devices up to Day 1 who installed the game in your selected period up to Day 1.',
  offline_d1_cohort_revenue: 'The amount of revenue up to Day 1 by new User IDs in your selected period',
  offline_d1_roas: 'D(1) Revenue / Cost',
  offline_d1_cohort_payer: 'The number of new User IDs in your selected period who have completed a purchase up to Day 1.',
  offline_d1_cohort_payer_rate: 'D(1) Payer / Install',
  offline_d1_purchase_rate: 'The percentage of devices who installed the game in your selected period and completed the payment up to Day 1.',
  offline_d1_arppu: 'The cumulative amount of revenue per paying device by devices who installed the game in your selected period up to Day 1.',
  offline_d1_ltv: 'D(1) Revenue / Install',

  offline_d2_cohort_register: 'The cumulative number of registered accounts by devices who installed the game in your selected period up to Day 2 .',
  offline_d2_cohort_register_rate: 'The cumulative number of registered accounts per device by devices up to Day 2 who installed the game in your selected period up to Day 2.',
  offline_d2_retention: 'The number of new User IDs in your selected period who have launched the game on Day 2.',
  offline_d2_retention_rate: 'D(2) Retention / Install',
  offline_d2_cohort_revenue: 'The amount of revenue up to Day 2 by new User IDs in your selected period',
  offline_d2_roas: 'D(2) Revenue / Cost',
  offline_d2_cohort_payer: 'The number of new User IDs in your selected period who have completed a purchase up to Day 2.',
  offline_d2_cohort_payer_rate: 'D(2) Payer / Install',
  offline_d2_purchase_rate: 'The percentage of devices who installed the game in your selected period and completed the payment up to Day 2.',
  offline_d2_arppu: 'The cumulative amount of revenue per paying device by devices who installed the game in your selected period up to Day 2.',
  offline_d2_ltv: 'D(2) Revenue / Install',

  offline_d3_cohort_register: 'The cumulative number of registered accounts by devices who installed the game in your selected period up to Day 3 .',
  offline_d3_cohort_register_rate: 'The cumulative number of registered accounts per device by devices up to Day 3 who installed the game in your selected period up to Day 3.',
  offline_d3_retention: 'The number of new User IDs in your selected period who have launched the game on Day 3.',
  offline_d3_retention_rate: 'D(3) Retention / Install',
  offline_d3_cohort_revenue: 'The amount of revenue up to Day 3 by new User IDs in your selected period',
  offline_d3_roas: 'D(3) Revenue / Cost',
  offline_d3_cohort_payer: 'The number of new User IDs in your selected period who have completed a purchase up to Day 3.',
  offline_d3_cohort_payer_rate: 'D(3) Payer / Install',
  offline_d3_purchase_rate: 'The percentage of devices who installed the game in your selected period and completed the payment up to Day 3.',
  offline_d3_arppu: 'The cumulative amount of revenue per paying device by devices who installed the game in your selected period up to Day 3.',
  offline_d3_ltv: 'D(3) Revenue / Install',

  offline_d7_cohort_register: 'The cumulative number of registered accounts by devices who installed the game in your selected period up to Day 7 .',
  offline_d7_cohort_register_rate: 'The cumulative number of registered accounts per device by devices up to Day 7 who installed the game in your selected period up to Day 7.',
  offline_d7_retention: 'The number of new User IDs in your selected period who have launched the game on Day 7.',
  offline_d7_retention_rate: 'D(7) Retention / Install',
  offline_d7_cohort_revenue: 'The amount of revenue up to Day 7 by new User IDs in your selected period',
  offline_d7_roas: 'D(7) Revenue / Cost',
  offline_d7_cohort_payer: 'The number of new User IDs in your selected period who have completed a purchase up to Day 7.',
  offline_d7_cohort_payer_rate: 'D(7) Payer / Install',
  offline_d7_purchase_rate: 'The percentage of devices who installed the game in your selected period and completed the payment up to Day 7.',
  offline_d7_arppu: 'The cumulative amount of revenue per paying device by devices who installed the game in your selected period up to Day 7.',
  offline_d7_ltv: 'D(7) Revenue / Install',

  offline_d14_cohort_register: 'The cumulative number of registered accounts by devices who installed the game in your selected period up to Day 14 .',
  offline_d14_cohort_register_rate: 'The cumulative number of registered accounts per device by devices up to Day 14 who installed the game in your selected period up to Day 14.',
  offline_d14_retention: 'The number of new User IDs in your selected period who have launched the game on Day 14.',
  offline_d14_retention_rate: 'D(14) Retention / Install',
  offline_d14_cohort_revenue: 'The amount of revenue up to Day 14 by new User IDs in your selected period',
  offline_d14_roas: 'D(14) Revenue / Cost',
  offline_d14_cohort_payer: 'The number of new User IDs in your selected period who have completed a purchase up to Day 14.',
  offline_d14_cohort_payer_rate: 'D(14) Payer / Install',
  offline_d14_purchase_rate: 'The percentage of devices who installed the game in your selected period and completed the payment up to Day 14.',
  offline_d14_arppu: 'The cumulative amount of revenue per paying device by devices who installed the game in your selected period up to Day 14.',
  offline_d14_ltv: 'D(14) Revenue / Install',

  video_views: 'The number of views your video has been watched.',
  cpv: 'Cost per view. The average amount of money you\'ve spent on each measured view.',
};
