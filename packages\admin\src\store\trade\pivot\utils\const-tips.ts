import { PC_TIPS } from './const-tips-pc';

export const MOBILE_TIPS = {
  installs: 'The number of times that someone clicked your ad and opens your app for the first time after installing it.',
  impressions: 'The number of times your ad is shown on screens.',
  clicks: 'The number of times your ad has been clicked.',
  spend: 'The amount of money spent on your ad.',
  in_app_action: 'The number of times that someone clicked your ad and performed one of the conversions tracked in your app.',
  conversions: 'The number of times your ad achieved an outcome(conversion), based on the objective and settings you selected.',
  ipm: 'Install per mille impressions. The average amount of installs per 1,000 impressions of your ad.',
  ctr: 'Click through rate. The ratio showing how often people who see your ad end up clicking it.',
  cvr: 'Conversion rate. The ratio shows how often, on average, an ad click leads to a conversion.',
  cpc: 'Cost per click. The average amount of money you\'ve spent on a click.',
  cpm: 'Cost per mille impressions. The average amount of money you\'ve spent per 1,000 impressions of your ad.',
  cpi: 'Cost per click. The average amount of money you\'ve spent on an install.',
  cpa: 'Cost per action. The average amount of money you\'ve spent on a conversion action.',
  offline_install: 'The number of times that someone clicked your ad and opens your app for the first time after installing it.(The data is from third-party measurement partners)',
  offline_cpi: 'Cost per install. The average amount of money you\'ve spent on an install.(caculated based on Installs-A)',

  offline_cohort_register: 'The cumulative number of registered accounts by devices who installed the game in your selected period.',
  offline_cohort_register_rate: 'The cumulative number of registered accounts per device by devices who installed the game in your selected period.',
  offline_retention: 'The number of devices who installed the game in your selected period and logged in to the game on the latest day.',
  offline_retention_rate: 'The percentage of devices who installed the game in your selected period and logged in to the game on the latest day.',
  offline_cohort_revenue: 'The cumulative amount of revenue by devices who installed the game in your selected period.',
  offline_roas: 'The return on advertising spend by devices who installed the game in your selected period.',
  offline_cohort_payer: 'The number of devices that installed the game in the selected time period and completed the payment up to the latest day.',
  offline_purchase_rate: 'The percentage of devices who installed the game in your selected period and completed the payment up to the latest day.',
  offline_arppu: 'The cumulative amount of revenue per paying device by devices who installed the game in your selected period.',
  offline_ltv: 'The cumulative amount of revenue per device by devices who installed the game in your selected period.',

  offline_d1_cohort_register: 'The cumulative number of registered accounts up to the day 1 (D1) by devices who installed the game in your selected period.',
  offline_d1_cohort_register_rate: 'The cumulative number of registered accounts per device up to the day 1 (D1) by devices who installed the game in your selected period.',
  offline_d1_cohort_revenue: 'The cumulative amount of revenue up to the day 1 (D1) by devices who installed the game in your selected period. The installation date is D1.',
  offline_d1_roas: 'The return on advertising spend up to the day 1 (D1) by devices who installed the game in your selected period. The installation date is D1.',
  offline_d1_cohort_payer: 'The number of devices who installed the game in your selected period and completed a purchase up to the day 1 (D1). The installation date is D1.',
  offline_d1_purchase_rate: 'The percentage of devices who installed the game in your selected period and completed a purchase up to the day 1 (D1). The installation date is D1.',
  offline_d1_arppu: 'The cumulative amount of revenue per paying device up to the day 1 (D1) by devices who installed the game in your selected period. The installation date is D1.',
  offline_d1_ltv: 'The cumulative amount of revenue per device up to the day 1 (D1) by devices who installed the game in your selected period. The installation date is D1.',

  offline_d2_cohort_register: 'The cumulative number of registered accounts up to the day 2 (D2) by devices who installed the game in your selected period.',
  offline_d2_cohort_register_rate: 'The cumulative number of registered accounts per device up to the day 2 (D2) by devices who installed the game in your selected period.',
  offline_d2_retention: 'The number of devices who installed the game in your selected period and logged in to the game on day 2 (D2). The installation date is D1.',
  offline_d2_retention_rate: 'The percentage of devices who installed the game in your selected period and logged in to the game on day 2 (D2). The installation date is D1.',
  offline_d2_cohort_revenue: 'The cumulative amount of revenue up to the day 2 (D2) by devices who installed the game in your selected period. The installation date is D1.',
  offline_d2_roas: 'The return on advertising spend up to the day 2 (D2) by devices who installed the game in your selected period. The installation date is D1.',
  offline_d2_cohort_payer: 'The number of devices who installed the game in your selected period and completed a purchase up to the day 2 (D2). The installation date is D1.',
  offline_d2_purchase_rate: 'The percentage of devices who installed the game in your selected period and completed a purchase up to the day 2 (D2). The installation date is D1.',
  offline_d2_arppu: 'The cumulative amount of revenue per paying device up to the day 2 (D2) by devices who installed the game in your selected period. The installation date is D1.',
  offline_d2_ltv: 'The cumulative amount of revenue per device up to the day 2 (D2) by devices who installed the game in your selected period. The installation date is D1.',

  offline_d3_cohort_register: 'The cumulative number of registered accounts up to the day 3 (D3) by devices who installed the game in your selected period.',
  offline_d3_cohort_register_rate: 'The cumulative number of registered accounts per device up to the day 3 (D3) by devices who installed the game in your selected period.',
  offline_d3_retention: 'The number of devices who installed the game in your selected period and logged in to the game on day 3 (D3). The installation date is D1.',
  offline_d3_retention_rate: 'The percentage of devices who installed the game in your selected period and logged in to the game on day 3 (D3). The installation date is D1.',
  offline_d3_cohort_revenue: 'The cumulative amount of revenue up to the day 3 (D3) by devices who installed the game in your selected period. The installation date is D1.',
  offline_d3_roas: 'The return on advertising spend up to the day 3 (D3) by devices who installed the game in your selected period. The installation date is D1.',
  offline_d3_cohort_payer: 'The number of devices who installed the game in your selected period and completed a purchase up to the day 3 (D3). The installation date is D1.',
  offline_d3_purchase_rate: 'The percentage of devices who installed the game in your selected period and completed a purchase up to the day 3 (D3). The installation date is D1.',
  offline_d3_arppu: 'The cumulative amount of revenue per paying device up to the day 3 (D3) by devices who installed the game in your selected period. The installation date is D1.',
  offline_d3_ltv: 'The cumulative amount of revenue per device up to the day 3 (D3) by devices who installed the game in your selected period. The installation date is D1.',

  offline_d7_cohort_register: 'The cumulative number of registered accounts up to the day 7 (D7) by devices who installed the game in your selected period.',
  offline_d7_cohort_register_rate: 'The cumulative number of registered accounts per device up to the day 7 (D7) by devices who installed the game in your selected period.',
  offline_d7_retention: 'The number of devices who installed the game in your selected period and logged in to the game on day 7 (D7). The installation date is D1.',
  offline_d7_retention_rate: 'The percentage of devices who installed the game in your selected period and logged in to the game on day 7 (D7). The installation date is D1.',
  offline_d7_cohort_revenue: 'The cumulative amount of revenue up to the day 7 (D7) by devices who installed the game in your selected period. The installation date is D1.',
  offline_d7_roas: 'The return on advertising spend up to the day 7 (D7) by devices who installed the game in your selected period. The installation date is D1.',
  offline_d7_cohort_payer: 'The number of devices who installed the game in your selected period and completed a purchase up to the day 7 (D7). The installation date is D1.',
  offline_d7_purchase_rate: 'The percentage of devices who installed the game in your selected period and completed a purchase up to the day 7 (D7). The installation date is D1.',
  offline_d7_arppu: 'The cumulative amount of revenue per paying device up to the day 7 (D7) by devices who installed the game in your selected period. The installation date is D1.',
  offline_d7_ltv: 'The cumulative amount of revenue per device up to the day 7 (D7) by devices who installed the game in your selected period. The installation date is D1.',

  offline_d14_cohort_register: 'The cumulative number of registered accounts up to the day 14 (D14) by devices who installed the game in your selected period.',
  offline_d14_cohort_register_rate: 'The cumulative number of registered accounts per device up to the day 14 (D14) by devices who installed the game in your selected period.',
  offline_d14_retention: 'The number of devices who installed the game in your selected period and logged in to the game on day 14 (D14). The installation date is D1.',
  offline_d14_retention_rate: 'The percentage of devices who installed the game in your selected period and logged in to the game on day 14 (D14). The installation date is D1.',
  offline_d14_cohort_revenue: 'The cumulative amount of revenue up to the day 14 (D14) by devices who installed the game in your selected period. The installation date is D1.',
  offline_d14_roas: 'The return on advertising spend up to the day 14 (D14) by devices who installed the game in your selected period. The installation date is D1.',
  offline_d14_cohort_payer: 'The number of devices who installed the game in your selected period and completed a purchase up to the day 14 (D14). The installation date is D1.',
  offline_d14_purchase_rate: 'The percentage of devices who installed the game in your selected period and completed a purchase up to the day 14 (D14). The installation date is D1.',
  offline_d14_arppu: 'The cumulative amount of revenue per paying device up to the day 14 (D14) by devices who installed the game in your selected period. The installation date is D1.',
  offline_d14_ltv: 'The cumulative amount of revenue per device up to the day 14 (D14) by devices who installed the game in your selected period. The installation date is D1.',
};
export const TIPS_OBJ = {
  default: MOBILE_TIPS,
  pc: PC_TIPS,
};
