import { ADMAP } from '@/views/trade/ads_management/const';
import { isDemoInGCP } from '@/store/trade/pivot/utils/base';
import dayjs from 'dayjs';
import { capitalize } from 'lodash-es';
import { IColItem } from '../type';
import { TIPS_OBJ } from './const-tips';
import { isPreWithConversions } from './table-data';
import { useEnv } from 'common/compose/env';
import { storeToRefs } from 'pinia';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useRouterStore } from '@/store/global/router.store';
import { isViewsDefaultGame } from '@/views/trade/ads_management/utils/base';
import { DOLLAR_PREFIX } from 'common/utils/format';

const { isProduction, isExp } = useEnv();

export const V2_MEDIA = ['facebook', 'google', 'tiktok', 'twitter', 'asa', 'reddit'];
export const UTC_E8_GAMES = ['pubgm'];
export const DEFAULT_VIEW_ID = 'default-td_pivot-default-default';
export const MODULE = 'td_pivot';
export const VIEW_TYPE = 'custom';
export const PRE_CONVERSIONS_WHITE_GAME = ['nikke']; // 前链路对接新的数据源
export const PRE_CONVERSIONS_WHITE_MEDIA = ['Facebook', 'Google', 'TikTok', 'Twitter']; // 前链路对接新的数据源
const OFFLINE_WHITE_GAME_INTL = [
  'aix_test',
  'nikke_test',
  'nikke',
  'hok_prod',
  'trssj',
  'tdm_sea',
  'cosjp',
  'cosna',
  'cosgame',
  'uamo',
  'dhcnchw',
  'nba2',
  'dragonn2_gl',
  'dragonnest2',
  'dragonn2_kr',
  'toweroffantacy',
  'nikke_hmt',
  'xssmaust_global',
  'komorilife_tha',
  'aoem',
  'sop',
];
const OFFLINE_WHITE_GAME_GCP = ['demo', 'demo_mobile', 'p2'];
export const OFFLINE_WHITE_GAME = OFFLINE_WHITE_GAME_INTL.concat(OFFLINE_WHITE_GAME_GCP);
export enum DATACON {
  GROUP_DRAFT = 'group_draft',
  GROUP_PUBLISHED = 'group_published',
  LOCAL = 'local',
  MEDIA = 'media',
}

export const DRAFT_ROOT = {
  id: DATACON.GROUP_DRAFT,
  layer_id: 0,
  data_fr: DATACON.LOCAL,
};

export const PUBLISHED_ROOT = {
  id: DATACON.GROUP_PUBLISHED,
  layer_id: 0,
  data_fr: DATACON.MEDIA,
};
export const TOP_NUM = 20;
export const RT_NUM = 20;
export const DEFAULT_PAGE_INDEX = 1;
export const DEFAULT_PAGE_SIZE = 10;
const SEARCH_BOX_TIPS = 'For bulk searches, please use lines to separate keywords.';
export const DEFAULT_CONDITION = {
  sort: { sortBy: 'spend', descending: true, isInit: true }, // 需要将deleted状态数据排在最后面
  pageSize: DEFAULT_PAGE_SIZE,
  pageIndex: DEFAULT_PAGE_INDEX, // 以上几项与筛选器无关
  date: [dayjs(), dayjs()],
  // campaign_name: [],
  // adgroup_name: [],
  // ad_name: [],
  account_id: [],
  delivery_status: [],
  opt_status: [],
  aix_campaign_type: [],
  aix_locations: [],
  search_box: [
    {
      text: 'Campaign Name',
      field: 'campaign_name', // 与condition字段中的key 一一对应
      condition: [],
      searchType: 1,
      tips: SEARCH_BOX_TIPS,
    },
    {
      text: 'Adgroup Name',
      field: 'adgroup_name', // 与condition字段中的key 一一对应
      condition: [],
      searchType: 1,
      tips: SEARCH_BOX_TIPS,
    },
    {
      text: 'Ad Name',
      field: 'ad_name', // 与condition字段中的key 一一对应
      condition: [],
      searchType: 1,
      tips: SEARCH_BOX_TIPS,
    },
    {
      text: 'Campaign ID',
      field: 'campaign_id', // 与condition字段中的key 一一对应
      condition: [],
      searchType: 1,
      tips: SEARCH_BOX_TIPS,
    },
  ],
};
export const getDefaultDates = (num = 0) => {
  if (isDemoInGCP()) {
    return [dayjs('********'), dayjs('********')];
  }
  const { gameCode } = storeToRefs(useGlobalGameStore());
  const { route } = useRouterStore();
  if (gameCode.value === 'demo' && ['Google', 'Facebook'].includes(route.meta.media as string)) {
    return [dayjs('2022-12-12'), dayjs('2022-12-28')];
  }
  return num === 0 ? [dayjs(), dayjs()] : [dayjs().subtract(num, 'day'), dayjs().subtract(num, 'day')];
};
export const getDefaultValues = (num = 0) => ({
  sort: { sortBy: 'spend', descending: true, isInit: true },
  pageSize: DEFAULT_PAGE_SIZE,
  pageIndex: DEFAULT_PAGE_INDEX, // 以上几项与筛选器无关
  date: getDefaultDates(num),
  // campaign_name: [],
  // adgroup_name: [],
  // ad_name: [],
  account_id: [],
  delivery_status: [],
  opt_status: [],
  aix_campaign_type: [],
  aix_locations: [],
  search_box: [
    {
      text: 'Campaign Name',
      field: 'campaign_name', // 与condition字段中的key 一一对应
      condition: [],
      searchType: 1,
      tips: SEARCH_BOX_TIPS,
    },
    {
      text: 'Adgroup Name',
      field: 'adgroup_name', // 与condition字段中的key 一一对应
      condition: [],
      searchType: 1,
      tips: SEARCH_BOX_TIPS,
    },
    {
      text: 'Ad Name',
      field: 'ad_name', // 与condition字段中的key 一一对应
      condition: [],
      searchType: 1,
      tips: SEARCH_BOX_TIPS,
    },
    {
      text: 'Campaign ID',
      field: 'campaign_id', // 与condition字段中的key 一一对应
      condition: [],
      searchType: 1,
      tips: SEARCH_BOX_TIPS,
    },
  ],
});
export const DEFAULT_METRIC = 'spend';
export const DEFAULT_ATTR = 'total';

// 表格中选中的数据 和 状态初始值
export const INIT_CHECK = {
  campaign: [],
  adgroup: [],
  ad: [],
};
// 点击父层级名称穿梭到子层级时用到
export const INIT_CLICK = {
  campaign: null,
  adgroup: null,
  ad: null,
};

export enum DATATYPE {
  TOTAL = 'total',
  DATE = 'date',
  PIVOT = 'pivot',
}

export const ATTR_SELECT = {
  default: {
    campaign: ['account_id', 'campaign_id'],
    adgroup: ['account_id', 'campaign_id', 'adgroup_id'],
    ad: ['account_id', 'campaign_id', 'adgroup_id', 'ad_id'],
  },
  pivot: {
    default: {
      campaign: [
        'campaign_id',
        'campaign_name',
        'account_id',
        'aix_campaign_type',
        'aix_campaign_status',
        'aix_campaign_delivery_status',
        'aix_daily_budget', // facebook, tiktok, gg, asa：均在campaign层有此字段
        'aix_campaign_create_time',
        'create_time',
        'update_time',
      ],
      adgroup: [
        'campaign_id',
        'campaign_name',
        'adgroup_id',
        'adgroup_name',
        'account_id',
        'aix_adgroup_status',
        'aix_adgroup_delivery_status',
        'aix_daily_budget', // facebook, tiktok：均在adgroup层有budget和bid
        'aix_bid_amount',
        'aix_adgroup_create_time',
        'create_time',
        'update_time',
      ],
      ad: [
        'campaign_id',
        'campaign_name',
        'adgroup_id',
        'adgroup_name',
        'ad_id',
        'ad_name',
        'account_id',
        'aix_ad_status',
        'aix_ad_delivery_status',
        'aix_ad_create_time',
        'create_time',
        'update_time',
      ],
    },
    Google: {
      campaign: [
        'campaign_id',
        'campaign_name',
        'account_id',
        'aix_campaign_type',
        'aix_campaign_status',
        'aix_campaign_delivery_status',
        'aix_delivery_status_reason',
        'aix_daily_budget', // gg均在campaign层有budget, bid；adgroup层无。
        'aix_bid_amount',
        'aix_campaign_create_time',
        'aix_schedule_start_time',
        'aix_schedule_end_time',
        'create_time',
        'update_time',
      ],
      adgroup: [
        'campaign_id',
        'campaign_name',
        'adgroup_id',
        'adgroup_name',
        'account_id',
        'aix_adgroup_status',
        'aix_adgroup_delivery_status',
        'aix_delivery_status_reason',
        'aix_adgroup_create_time',
        'create_time',
        'update_time',
      ],
      ad: [
        'campaign_id',
        'campaign_name',
        'adgroup_id',
        'adgroup_name',
        'ad_id',
        'ad_name',
        'account_id',
        'aix_ad_status',
        'aix_ad_delivery_status',
        'aix_delivery_status_reason',
        'aix_ad_create_time',
        'create_time',
        'update_time',
      ],
    },
    Facebook: {
      campaign: [
        'campaign_id',
        'campaign_name',
        'account_id',
        'aix_campaign_type',
        'aix_campaign_status',
        'aix_campaign_delivery_status',
        'aix_delivery_status_reason',
        'aix_daily_budget',
        'aix_campaign_create_time',
        'aix_optimization_events',
        'create_time',
        'update_time',
      ],
      adgroup: [
        'campaign_id',
        'campaign_name',
        'adgroup_id',
        'adgroup_name',
        'account_id',
        'aix_adgroup_status',
        'aix_adgroup_delivery_status',
        'aix_delivery_status_reason',
        'aix_daily_budget', // facebook, tiktok：均在adgroup层有budget和bid
        'aix_bid_amount',
        'aix_adgroup_create_time',
        'is_dynamic',
        'aix_optimization_goal',
        'aix_event_type',
        'aix_event_name',
        'aix_schedule_start_time',
        'aix_schedule_end_time',
        'create_time',
        'update_time',
      ],
      ad: [
        'campaign_id',
        'campaign_name',
        'adgroup_id',
        'adgroup_name',
        'ad_id',
        'ad_name',
        'account_id',
        'aix_ad_status',
        'aix_ad_delivery_status',
        'aix_delivery_status_reason',
        'aix_ad_create_time',
        'is_dynamic',
        'create_time',
        'update_time',
      ],
    },
    TikTok: {
      campaign: [
        'campaign_id',
        'campaign_name',
        'account_id',
        'aix_campaign_type',
        'aix_campaign_status',
        'aix_campaign_delivery_status',
        'aix_delivery_status_reason',
        'aix_daily_budget', // facebook, tiktok, gg, asa：均在campaign层有此字段
        'aix_campaign_create_time',
        'create_time',
        'update_time',
      ],
      adgroup: [
        'campaign_id',
        'campaign_name',
        'adgroup_id',
        'adgroup_name',
        'account_id',
        'aix_adgroup_status',
        'aix_adgroup_delivery_status',
        'aix_delivery_status_reason',
        'aix_daily_budget', // facebook, tiktok：均在adgroup层有budget和bid
        'aix_bid_amount',
        'aix_adgroup_create_time',
        'creative_material_mode',
        'aix_schedule_start_time',
        'aix_schedule_end_time',
        'create_time',
        'update_time',
      ],
      ad: [
        'campaign_id',
        'campaign_name',
        'adgroup_id',
        'adgroup_name',
        'ad_id',
        'ad_name',
        'account_id',
        'aix_ad_status',
        'aix_ad_delivery_status',
        'aix_delivery_status_reason',
        'aix_ad_create_time',
        'is_aco',
        'create_time',
        'update_time',
      ],
    },
    Twitter: {
      campaign: [
        'campaign_id',
        'campaign_name',
        'account_id',
        'aix_campaign_type',
        'aix_campaign_status',
        'aix_campaign_delivery_status',
        'aix_delivery_status_reason',
        'aix_daily_budget',
        'aix_campaign_create_time',
        'create_time',
        'update_time',
      ],
      adgroup: [
        'campaign_id',
        'campaign_name',
        'adgroup_id',
        'adgroup_name',
        'account_id',
        'aix_adgroup_status',
        'aix_adgroup_delivery_status',
        'aix_delivery_status_reason',
        'aix_daily_budget',
        'aix_bid_amount',
        'aix_adgroup_create_time',
        'aix_schedule_start_time',
        'aix_schedule_end_time',
        'create_time',
        'update_time',
      ],
      ad: [
        'campaign_id',
        'campaign_name',
        'adgroup_id',
        'adgroup_name',
        'ad_id',
        'ad_name',
        'account_id',
        'aix_ad_status',
        'aix_ad_delivery_status',
        'aix_delivery_status_reason',
        'aix_ad_create_time',
        'create_time',
        'update_time',
      ],
    },
    asa: {
      adgroup: [
        'campaign_id',
        'campaign_name',
        'adgroup_id',
        'adgroup_name',
        'account_id',
        'aix_adgroup_status',
        'aix_adgroup_delivery_status',
        'aix_adgroup_create_time',
        'aix_bid_amount',
        'create_time',
        'update_time',
      ],
    },
    Reddit: {
      campaign: [
        'campaign_id',
        'campaign_name',
        'account_id',
        'aix_campaign_type',
        'aix_campaign_status',
        'aix_campaign_delivery_status',
        'aix_campaign_create_time',
        'create_time',
        'update_time',
      ],
      adgroup: [
        'campaign_id',
        'campaign_name',
        'adgroup_id',
        'adgroup_name',
        'account_id',
        'aix_adgroup_status',
        'aix_adgroup_delivery_status',
        'aix_bid_amount',
        'aix_adgroup_create_time',
        'create_time',
        'update_time',
      ],
    },
    // 查询adgroup层或ad层数据时，需要使用到campaign层的campaign_type
    campaign_by_children: ['campaign_id', 'account_id', 'aix_campaign_type'],
  },
  // date: {
  //   campaign: ['campaign_id', 'campaign_name', 'account_id', 'aix_optimization_events'],
  //   adgroup: ['campaign_id', 'campaign_name', 'adgroup_id', 'adgroup_name', 'account_id',
  //     'aix_optimization_goal', 'aix_event_type'],
  //   ad: ['campaign_id', 'campaign_name', 'adgroup_id', 'adgroup_name', 'ad_id', 'ad_name', 'account_id'],
  // },
  date: {
    Facebook: {
      campaign: ['campaign_id', 'campaign_name', 'account_id', 'aix_optimization_events'],
      adgroup: [
        'campaign_id',
        'campaign_name',
        'adgroup_id',
        'adgroup_name',
        'account_id',
        'aix_optimization_goal',
        'aix_event_type',
        'aix_event_name',
      ],
      ad: ['campaign_id', 'campaign_name', 'adgroup_id', 'adgroup_name', 'ad_id', 'ad_name', 'account_id'],
    },
    default: {
      campaign: ['campaign_id', 'campaign_name', 'account_id'],
      adgroup: ['campaign_id', 'campaign_name', 'adgroup_id', 'adgroup_name', 'account_id'],
      ad: ['campaign_id', 'campaign_name', 'adgroup_id', 'adgroup_name', 'ad_id', 'ad_name', 'account_id'],
    },
  },
  fr_metric_table: {
    campaign: ['account_id', 'media_campaign_id'],
    adgroup: ['account_id', 'media_adset_id'],
    ad: ['account_id', 'media_ad_id'],
  },
  optimization: {
    campaign: ['aix_optimization_events'],
    adgroup: ['aix_optimization_goal', 'aix_event_type', 'aix_event_name'],
  },
  total: {
    Facebook: {
      campaign: ['campaign_id', 'campaign_name', 'account_id', 'aix_optimization_events'],
      adgroup: [
        'campaign_id',
        'campaign_name',
        'adgroup_id',
        'adgroup_name',
        'account_id',
        'aix_optimization_goal',
        'aix_event_type',
        'aix_event_name',
      ],
      ad: ['campaign_id', 'campaign_name', 'adgroup_id', 'adgroup_name', 'ad_id', 'ad_name', 'account_id'],
    },
  },
};
/**
 * @description 枚举值映射
 * campaign层 aix_optimization_events [key1,key3]
 * adgroup层 (aix_optimization_goal|aix_event_type|aix_event_name
 * conversions_map {key1:value1, key2:value2, key3:value3}
 * 第一步基于 aix_optimization_events 过滤conversions_map，筛选出对应的key。
 * 第二步基于 OPTIMIZATION_MAP 将对应的key转换成text，用于展示。
 */
export const OPTIMIZATION_MAP = {
  mobile: [
    {
      key: 'app_custom_event.fb_mobile_purchase',
      text: 'Mobile app purchases',
      attr_value: 'OFFSITE_CONVERSIONS|standard|PURCHASE',
    },
    {
      key: 'app_custom_event.fb_mobile_complete_registration',
      text: 'Mobile app registrations completed',
      attr_value: 'OFFSITE_CONVERSIONS|standard|COMPLETE_REGISTRATION',
    },
    {
      key: 'app custom event.other',
      text: 'Mobile app custom events',
      // attr_value: 'OFFSITE_CONVERSIONS|custom',
      attr_value: /^OFFSITE_CONVERSIONS\|custom(\s|\S)*$/,
    },
    {
      key: 'mobile_app_install',
      text: 'Mobile app installs',
      attr_value: 'APP_INSTALLS',
    },
  ],
  pc: [
    {
      key: 'app_custom_event.fb_mobile_purchase',
      text: 'App purchases',
      attr_value: 'OFFSITE_CONVERSIONS|standard|PURCHASE',
    },
    {
      key: 'app_custom_event.fb_mobile_complete_registration',
      text: 'Registrations completed',
      attr_value: 'OFFSITE_CONVERSIONS|standard|COMPLETE_REGISTRATION',
    },
    {
      key: 'app custom event.other',
      text: 'Custom events',
      // attr_value: 'OFFSITE_CONVERSIONS|custom',
      attr_value: /^OFFSITE_CONVERSIONS\|custom(\s|\S)*$/,
    },
    {
      key: 'mobile_app_install',
      text: 'App installs',
      attr_value: 'APP_INSTALLS',
    },
  ],
};

// 表格中固定的且不在穿梭框中的列
export const FIXED_COLS = [
  { colKey: 'checkbox_col', title: '', width: 75, index: 0, fixed: 'left' },
  {
    colKey: 'name',
    title: 'Name',
    titleStr: 'Name',
    group: 'Detail',
    width: 540,
    ellipsis: true,
    required: true,
    default: true,
    index: 1,
    fixed: 'left',
  },
];

export const COLS = {
  BASE: ['game_code', 'media'],
  COUNTRY: 'aix_locations',
  NOMETRIC: ['checkbox_col', 'campaign_name', 'adgroup_name', 'ad_name'],
  STATUS: ['opt_status'],
  NAMES: ['campaign_name', 'adgroup_name', 'ad_name', 'campaign_id'],
  FILTERS: [
    'date',
    'campaign_name',
    'adgroup_name',
    'ad_name',
    'campaign_id',
    'account_id',
    'opt_status',
    'delivery_status', // 对应于筛选器condition中的key，转到后台参数时1to3
    'aix_campaign_type',
    'aix_locations',
  ],
  DRAFT_FILTERS: [
    'campaign_name',
    'adgroup_name',
    'ad_name',
    'campaign_id',
    'account_id',
    'delivery_status', // 对应于筛选器condition中的key，转到后台参数时1to3
    'aix_campaign_type',
    // 'aix_locations',
  ],
  FILTERS_FOR_SVR: [
    'date',
    'campaign_name',
    'adgroup_name',
    'ad_name',
    'campaign_id',
    'account_id',
    'opt_status',
    'aix_campaign_delivery_status',
    'aix_adgroup_delivery_status',
    'aix_ad_delivery_status',
    'aix_campaign_type',
    'aix_locations',
  ],
  CONVERSIONS_V2: ['conversions_map'],
};
export const NAME_INDEX = {
  [ADMAP.CAMPAIGN]: 0,
  [ADMAP.ADGROUP]: 2,
  [ADMAP.AD]: 4,
};
export const NAME_ID_COLS = [
  {
    colKey: 'campaign_name',
    title: 'Campaign Name',
    titleStr: 'Campaign Name',
    width: 540,
    groupName: 'Details',
    type: 'attr',
    ellipsis: true,
  },
  {
    colKey: 'campaign_id',
    title: 'Campaign ID',
    titleStr: 'Campaign ID',
    width: 200,
    groupName: 'Details',
    type: 'attr',
  },
  {
    colKey: 'adgroup_name',
    title: 'Ad group Name',
    titleStr: 'Ad group Name',
    width: 540,
    groupName: 'Details',
    type: 'attr',
    ellipsis: true,
  },
  {
    colKey: 'adgroup_id',
    title: 'Ad group ID',
    titleStr: 'Ad group ID',
    width: 200,
    groupName: 'Details',
    type: 'attr',
  },
  {
    colKey: 'ad_name',
    title: 'Ad Name',
    titleStr: 'Ad Name',
    width: 540,
    groupName: 'Details',
    type: 'attr',
    ellipsis: true,
  },
  {
    colKey: 'ad_id',
    title: 'Ad ID',
    titleStr: 'Ad ID',
    width: 200,
    groupName: 'Details',
    type: 'attr',
  },
];
const NOT_IN_PC_COLS = [
  'installs',
  'in_app_action',
  'ipm',
  'cpi',
  'offline_cohort_register',
  'offline_d1_cohort_register',
  'offline_d2_cohort_register',
  'offline_d3_cohort_register',
  'offline_d7_cohort_register',
  'offline_d14_cohort_register',
  'offline_cohort_register_rate',
  'offline_d1_cohort_register_rate',
  'offline_d2_cohort_register_rate',
  'offline_d3_cohort_register_rate',
  'offline_d7_cohort_register_rate',
  'offline_d14_cohort_register_rate',
  'offline_arppu',
  'offline_d1_arppu',
  'offline_d2_arppu',
  'offline_d3_arppu',
  'offline_d7_arppu',
  'offline_d14_arppu',
];
// 自定义穿梭列左边
export const genCols = (game: string, media: string, isPCGame: boolean) => {
  const TIPS = TIPS_OBJ[isPCGame ? 'pc' : 'default'];
  let initList: IColItem[] = [
    {
      colKey: 'opt_status',
      title: 'On/Off',
      titleStr: 'On/Off',
      width: 120,
      groupName: 'Details',
      required: true,
      default: true,
      type: 'attr',
      fixed: 'left',
    },
    {
      colKey: 'delivery_status',
      title: 'Delivery Status',
      titleStr: 'Delivery Status',
      width: 180,
      groupName: 'Details',
      sorter: true,
      default: true,
      type: 'attr',
    },
    {
      colKey: 'aix_daily_budget',
      // title: 'Budget($)',
      // titleStr: 'Budget($)',
      title: 'Budget',
      titleStr: 'Budget',
      width: 120,
      format: 'dollar',
      prefix: DOLLAR_PREFIX,
      groupName: 'Details',
      required: true,
      default: true,
      type: 'attr',
    },
    {
      colKey: 'aix_bid_amount',
      // title: 'Bid($)',
      // titleStr: 'Bid($)',
      title: 'Bid',
      titleStr: 'Bid',
      width: 120,
      format: 'dollar',
      prefix: DOLLAR_PREFIX,
      groupName: 'Details',
      default: !(isProduction || isExp),
      type: 'attr',
    },
    {
      colKey: 'aix_campaign_type',
      title: 'Objectives',
      titleStr: 'Objectives',
      width: 220,
      ellipsis: true,
      groupName: 'Details',
      default: !(isProduction || isExp),
      type: 'attr',
    },
    ...NAME_ID_COLS,
    {
      colKey: 'spend',
      title: 'Cost',
      titleStr: 'Cost',
      width: 120,
      sorter: true,
      format: 'dollar',
      prefix: DOLLAR_PREFIX,
      groupName: 'Conversion Metrics',
      required: true,
      default: true,
      tips: TIPS.spend,
    },
    {
      colKey: 'impressions',
      title: 'Impressions',
      titleStr: 'Impressions',
      width: 120,
      sorter: true,
      format: 'money',
      cardFormat: 'numShort',
      groupName: 'Conversion Metrics',
      default: true,
      tips: TIPS.impressions,
    },
    {
      colKey: 'clicks',
      title: 'Clicks',
      titleStr: 'Clicks',
      width: 120,
      sorter: true,
      format: 'money',
      cardFormat: 'numShort',
      groupName: 'Conversion Metrics',
      default: true,
      tips: TIPS.clicks,
    },
    {
      colKey: 'aix_create_time',
      title: 'Create Date',
      titleStr: 'Create Date',
      width: 120,
      sorter: true,
      groupName: 'Details',
      default: false,
      type: 'attr',
    },
    {
      colKey: 'aix_schedule_end_time',
      title: 'End Date',
      titleStr: 'End Date',
      width: 120,
      sorter: true,
      groupName: 'Details',
      default: false,
      type: 'attr',
    },
  ];
  if (!isProduction) {
    const objectivesItem = initList.splice(4, 1);
    initList = [...initList.slice(0, 1), ...objectivesItem, ...initList.slice(1)];
  }
  const listFloat1 = ['ctr', 'cpc', 'cpm'].map((colKey) => {
    const item = {
      colKey,
      // title: `${colKey.toUpperCase()}${['cpc', 'cpm'].includes(colKey) ? '($)' : ''}`,
      // titleStr: `${colKey.toUpperCase()}${['cpc', 'cpm'].includes(colKey) ? '($)' : ''}`,
      title: `${colKey.toUpperCase()}`,
      titleStr: `${colKey.toUpperCase()}`,
      width: 120,
      sorter: true,
      format: ['cpc', 'cpm'].includes(colKey) ? 'dollar' : 'percent',
      prefix: DOLLAR_PREFIX,
      groupName: 'Conversion Metrics',
      tips: (TIPS as any)[colKey],
      default: colKey === 'cpc',
    };
    return item;
  });
  const listInt1 = ['conversions', 'installs', 'in_app_action'].map((colKey) => {
    const titleMap = {
      in_app_action: 'In App Event',
      installs: 'Installs(Channel)',
    };
    const title = colKey in titleMap ? (titleMap as any)[colKey] : capitalize(colKey);
    const item: any = {
      colKey,
      title,
      titleStr: title,
      width: colKey === 'in_app_action' ? 180 : 120,
      sorter: true,
      format: 'money',
      cardFormat: 'numShort',
      groupName: 'Conversion Metrics',
      tips: (TIPS as any)[colKey],
    };
    if (colKey === 'conversions' && isPreWithConversions({ cols: ['conversions'], game, media })) {
      item.width = 320;
      if (media === 'Facebook') {
        item.sorter = undefined;
      }
    }
    return item;
  });
  const listFloat2 = ['ipm', 'cpi', 'cpa', 'cvr'].map((colKey) => {
    let format = 'float';
    let prefix = {};
    let title = colKey.toUpperCase();
    let titleStr = colKey.toUpperCase();
    if (['cpa', 'cpi'].includes(colKey)) {
      format = 'dollar';
      prefix = DOLLAR_PREFIX;
      title = colKey === 'cpi' ? 'CPI(Channel)' : `${colKey.toUpperCase()}`;
      titleStr = title;
    }
    if (colKey === 'cvr') {
      format = 'percent';
    }
    const item: any = {
      colKey,
      title,
      titleStr,
      width: 120,
      sorter: true,
      format,
      prefix,
      groupName: 'Conversion Metrics',
      tips: (TIPS as any)[colKey],
    };
    if (
      ['cpa', 'cvr'].includes(colKey)
      && isPreWithConversions({ cols: ['conversions'], game, media })
      && media === 'Facebook'
    ) {
      item.sorter = undefined;
    }
    // if (colKey === 'cpi') item.default = true;
    return item;
  });
  const listViews: any = isViewsDefaultGame(game)
    ? [
      {
        colKey: 'video_views',
        title: 'Views',
        titleStr: 'Views',
        width: 120,
        sorter: true,
        format: 'money',
        cardFormat: 'numShort',
        groupName: 'Conversion Metrics',
        tips: (TIPS as any).video_views,
        default: true,
      },
      {
        colKey: 'cpv',
        title: 'CPV',
        titleStr: 'CPV',
        width: 120,
        sorter: true,
        format: 'dollar',
        prefix: DOLLAR_PREFIX,
        groupName: 'Conversion Metrics',
        tips: (TIPS as any).cpv,
        default: true,
      },
    ]
    : [];
  const listInt2 = [
    {
      colKey: 'offline_install',
      title: 'Installs',
      titleStr: 'Installs',
      width: 120,
      sorter: true,
      format: 'money',
      cardFormat: 'numShort',
      groupName: 'Attributed Metrics',
      tips: (TIPS as any).offline_install,
      version: 2,
    },
    {
      colKey: 'offline_cpi',
      title: 'CPI',
      titleStr: 'CPI',
      width: 120,
      sorter: true,
      format: 'dollar',
      prefix: DOLLAR_PREFIX,
      groupName: 'Attributed Metrics',
      tips: (TIPS as any).offline_cpi,
      version: 2,
    },
  ];
  const listNum1 = ['', 1, 2, 3, 7, 14].map((num) => {
    const colKey = num ? `offline_d${num}_cohort_register` : 'offline_cohort_register';
    const title = `D${num ? num : '(x)'} Register`;
    const item = {
      colKey,
      title,
      titleStr: title,
      width: 180,
      sorter: true,
      format: 'int',
      cardFormat: 'numShort',
      groupName: 'D(x) Register',
      tips: (TIPS as any)[colKey],
      version: ['', 1].includes(num) ? 2 : undefined,
    };
    return item;
  });
  const listNum2 = ['', 1, 2, 3, 7, 14].map((num) => {
    const colKey = num ? `offline_d${num}_cohort_register_rate` : 'offline_cohort_register_rate';
    const title = `D${num ? num : '(x)'} Register %`;
    const item = {
      colKey,
      title,
      titleStr: title,
      width: 180,
      format: 'percent',
      groupName: 'D(x) Register %',
      tips: (TIPS as any)[colKey],
      version: ['', 1].includes(num) ? 2 : undefined,
    };
    return item;
  });

  const listNum3 = ['', 2, 3, 7, 14].map((num) => {
    const colKey = num ? `offline_d${num}_retention` : 'offline_retention';
    const title = `D${num ? num : '(x)'} Retention`;
    const item = {
      colKey,
      title,
      titleStr: title,
      width: 180,
      sorter: true,
      format: 'int',
      cardFormat: 'numShort',
      groupName: 'D(x) Retention',
      tips: (TIPS as any)[colKey],
      version: ['', 1].includes(num) ? 2 : undefined,
    };
    return item;
  });
  const listNum4 = ['', 2, 3, 7, 14].map((num) => {
    const colKey = num ? `offline_d${num}_retention_rate` : 'offline_retention_rate';
    const title = `D${num ? num : '(x)'} Retention %`;
    const item = {
      colKey,
      title,
      titleStr: title,
      width: 180,
      format: 'percent',
      groupName: 'D(x) Retention %',
      tips: (TIPS as any)[colKey],
      version: ['', 1].includes(num) ? 2 : undefined,
    };
    return item;
  });

  const listNum5 = ['', 1, 2, 3, 7, 14].map((num) => {
    const colKey = num ? `offline_d${num}_cohort_revenue` : 'offline_cohort_revenue';
    const title = `D${num ? num : '(x)'} Revenue`;
    const item = {
      colKey,
      title,
      titleStr: title,
      width: 180,
      sorter: true,
      format: 'dollar',
      prefix: DOLLAR_PREFIX,
      cardFormat: 'numShort',
      groupName: 'D(x) Revenue',
      tips: (TIPS as any)[colKey],
      version: ['', 1].includes(num) ? 2 : undefined,
    };
    return item;
  });
  const listNum6 = ['', 1, 2, 3, 7, 14].map((num) => {
    const colKey = num ? `offline_d${num}_roas` : 'offline_roas';
    const title = `D${num ? num : '(x)'} ROAS`;
    const item = {
      colKey,
      title,
      titleStr: title,
      width: 180,
      format: 'percent',
      groupName: 'D(x) ROAS',
      tips: (TIPS as any)[colKey],
      version: ['', 1].includes(num) ? 2 : undefined,
    };
    return item;
  });
  // Cohort Payer ,%Purchase Rate, ARPPU, LTV
  const listNum7 = ['', 1, 2, 3, 7, 14].map((num) => {
    const colKey = num ? `offline_d${num}_cohort_payer` : 'offline_cohort_payer';
    const title = `D${num ? num : '(x)'} Payer`;
    const item = {
      colKey,
      title,
      titleStr: title,
      width: 180,
      sorter: true,
      format: 'int',
      cardFormat: 'numShort',
      groupName: 'D(x) Payer',
      tips: (TIPS as any)[colKey],
      version: 2,
    };
    return item;
  });
  const listNum8 = ['', 1, 2, 3, 7, 14].map((num) => {
    const colKey = num ? `offline_d${num}_purchase_rate` : 'offline_purchase_rate';
    const title = `D${num ? num : '(x)'} Payer %`;
    const item = {
      colKey,
      title,
      titleStr: title,
      width: 180,
      format: 'percent',
      groupName: 'D(x) Payer %',
      tips: (TIPS as any)[colKey],
      version: 2,
    };
    return item;
  });
  const listNum9 = ['', 1, 2, 3, 7, 14].map((num) => {
    const colKey = num ? `offline_d${num}_arppu` : 'offline_arppu';
    const title = `D${num ? num : '(x)'} ARPPU`;
    const item = {
      colKey,
      title,
      titleStr: title,
      width: 180,
      format: 'float',
      groupName: 'D(x) ARPPU',
      tips: (TIPS as any)[colKey],
      version: 2,
    };
    return item;
  });
  const listNum10 = ['', 1, 2, 3, 7, 14].map((num) => {
    const colKey = num ? `offline_d${num}_ltv` : 'offline_ltv';
    const title = num ? `D${num} LTV` : 'LTV';
    const item = {
      colKey,
      title,
      titleStr: title,
      width: 180,
      format: 'dollar',
      prefix: DOLLAR_PREFIX,
      groupName: 'D(x) LTV',
      tips: (TIPS as any)[colKey],
      version: 2,
    };
    return item;
  });
  const res = initList
    .concat(listFloat1)
    .concat(listInt1)
    .concat(listFloat2)
    .concat(listViews)
    .concat(listInt2)
    .concat(listNum1)
    .concat(listNum2)
    .concat(listNum3)
    .concat(listNum4)
    .concat(listNum5)
    .concat(listNum10)
    .concat(listNum6)
    .concat(listNum7)
    .concat(listNum8)
    .concat(listNum9);
  if (!isPCGame) return res;
  return res.filter(({ colKey }) => !NOT_IN_PC_COLS.includes(colKey));
};
export const NAME_ID_COL_MAP = {
  [ADMAP.CAMPAIGN]: [NAME_ID_COLS[1], NAME_ID_COLS[0]],
  [ADMAP.ADGROUP]: [NAME_ID_COLS[3], NAME_ID_COLS[2], NAME_ID_COLS[1], NAME_ID_COLS[0]],
  [ADMAP.AD]: [NAME_ID_COLS[5], NAME_ID_COLS[4], NAME_ID_COLS[3], NAME_ID_COLS[2], NAME_ID_COLS[1], NAME_ID_COLS[0]],
};
export const CONDITION_CFG = {
  filters: {
    fieldObj: {
      account_id: [],
      delivery_status: [],
      opt_status: [
        // 统一枚举值
        // { label: 'Enabled', value: 'Enabled' },
        // { label: 'Paused', value: 'Paused' },
        // 以下三项全部映射为Paused
        // { label: 'Publishing', value: 'Publishing' },
        // { label: 'Updating', value: 'Updating' },
        // { label: 'Deleted', value: 'Deleted' },
      ],
      aix_campaign_type: [],
      aix_locations: [],
    },
    conditionList: [
      {
        name: 'DateRangePicker',
        ext: {
          key: 'date',
          label: 'Date',
          isAllowClose: false,
          isHide: false,
          rules: [],
        },
        props: {
          maxDate: dayjs().format('YYYYMMDD'),
          date: [dayjs().subtract(1, 'day'), dayjs().subtract(1, 'day')],
          presets: {
            Today: [dayjs().toDate(), dayjs().toDate()],
            'Last Day': [dayjs().subtract(1, 'day')
              .toDate(), dayjs().subtract(1, 'day')
              .toDate()],
            'Last 7 days': [dayjs().subtract(6, 'day')
              .toDate(), dayjs().toDate()],
            'Last 14 days': [dayjs().subtract(13, 'day')
              .toDate(), dayjs().toDate()],
            'Last 30 days': [dayjs().subtract(29, 'day')
              .toDate(), dayjs().toDate()],
            'This month': [dayjs().startOf('month')
              .toDate(), dayjs().toDate()],
            'Last month': [
              dayjs().add(-1, 'month')
                .startOf('month')
                .toDate(),
              dayjs().add(-1, 'month')
                .date(30)
                .toDate(),
            ],
            'Year to date': [dayjs().startOf('year')
              .toDate(), dayjs().toDate()],
          },
        },
      },
      {
        name: 'SearchBox',
        props: {
          // fieldList: [],
          placeholder: 'Search',
          enterToUpdate: true,
        },
        ext: {
          key: 'search_box',
          label: 'Search', // 包裹容器下拉列表中展示名称
          isAllowClose: false,
          isHide: false,
          rules: [],
        },
      },
      {
        name: 'MultipleSelect',
        props: {
          list: [],
          title: 'Accounts',
          multiple: true,
          button: (textArr: string[] | string) => {
            if (textArr.length === 0) {
              return 'All';
            }
            if (!Array.isArray(textArr)) return textArr;
            return textArr.length > 1 ? textArr.length : textArr.join(',');
          },
        },
        ext: {
          key: 'account_id',
          label: 'Accounts', // 包裹容器下拉列表中展示名称
          isAllowClose: true,
          // isHide: true,
        },
      },
      {
        name: 'InputCascader',
        props: {
          labels: ['Status', ''],
          options: [],
          title: 'Delivery Status',
          outReset: true,
        },
        ext: {
          key: 'delivery_status',
          label: 'Delivery Status',
          isAllowClose: true,
          isHide: true,
        },
      },
      {
        name: 'MultipleSelect',
        props: {
          list: [],
          title: 'On/Off',
          multiple: true,
          button: (textArr: string[] | string) => {
            if (textArr.length === 0) {
              return 'All';
            }
            if (!Array.isArray(textArr)) return textArr;
            return textArr.length > 1 ? textArr.length : textArr.join(',');
          },
        },
        ext: {
          key: 'opt_status',
          label: 'On/Off',
          isAllowClose: true,
          isHide: true,
        },
      },
      {
        name: 'MultipleSelect',
        props: {
          list: [],
          title: 'Campaign Objectives',
          multiple: true,
          button: (textArr: string[] | string) => {
            if (textArr.length === 0) {
              return 'All';
            }
            if (!Array.isArray(textArr)) return textArr;
            return textArr.length > 1 ? textArr.length : textArr.join(',');
          },
        },
        ext: {
          key: 'aix_campaign_type', // 对应store的front_value中的key
          label: 'Campaign Objectives',
          isAllowClose: true,
          isHide: true,
        },
      },
      // {
      //   name: 'InputCascader',
      //   props: {
      //     labels: ['Region', 'Country/Market'],
      //     options: [],
      //     title: 'Country/Market',
      //     ref: 'locationRef',
      //     outReset: true,
      //   },
      //   ext: {
      //     key: 'aix_locations', // 对应store的front_value中的key
      //     label: 'Country/Market',
      //     isAllowClose: true,
      //     isHide: true,
      //   },
      // },
    ],
  },
};
export const getCfgCols = (game: string, media: string, isPCGame: boolean) => genCols(game, media, isPCGame);
export const DRAFT_STAUS_LIST = [
  { label: 'Completed', value: 'Completed' },
  { label: 'Incompleted', value: 'Incompleted' },
  { label: 'Publishing', value: 'Publishing' },
  { label: 'Failed', value: 'Failed' },
];
export const OPT_STATUS = {
  Google: {
    pause: 1,
    enable: 2,
    delete: 3,
  },
  Facebook: {
    pause: 'PAUSED',
    enable: 'ACTIVE',
    delete: 'DELETED',
  },
  TikTok: {
    pause: 'DISABLE',
    enable: 'ENABLE',
  },
  Twitter: {
    pause: 'PAUSED',
    enable: 'ACTIVE',
    delete: 'DELETED',
  },
};

export const DRAFT_STATUS = {
  // https://ptc.coding.intlgame.com/p/aix-backend/d/common/git/tree/master/constant/tiktok/tiktok.go
  TikTok: {
    Completed: 'INDRAFT_COMPLETED',
    Incompleted: 'INDRAFT_INCOMPLETED',
    Publishing: 'INDRAFT_PUBLISHING',
    Failed: 'INDRAFT_FAIL',
  },
  Google: {
    Completed: 'STATUS_DRAFT',
    Incompleted: 'STATUS_INCOMPLETE',
    Publishing: 'STATUS_PUBLISHING',
    Failed: 'STATUS_PUBLISH_FAILED',
  },
  Facebook: {
    Completed: 'DRAFT',
    Incompleted: 'INCOMPLETED',
    Publishing: 'PUBLISHING',
    Failed: 'PUBLISHFAILED',
  },
  Twitter: {
    Completed: 'DRAFT',
    Incompleted: 'INCOMPLETED',
    Publishing: 'PUBLISHING',
    Failed: 'PUBLISHFAILED',
  },
  asa: {},
  Reddit: {},
};

export const OPT_STATUS_MAP = {
  // https://ptc.coding.intlgame.com/p/aix-backend/d/common/git/tree/master/constant/tiktok/tiktok.go
  TikTok: {
    Paused: 'DISABLE',
    Enabled: 'ENABLE',
    Deleted: 'DELETED',
  },
  Google: {
    Paused: 'PAUSED',
    Enabled: 'ENABLED',
    Removed: 'REMOVED',
  },
  Facebook: {
    Paused: 'Paused',
    Enabled: 'Enabled',
    Deleted: 'Deleted',
  },
  Twitter: {
    Paused: 'PAUSED',
    Enabled: 'ACTIVE',
    Deleted: 'DELETED',
  },
  asa: {
    Paused: 'PAUSED',
    Enabled: 'ENABLED',
    Deleted: 'DELETED',
  },
};

export const TREE_AD_MAP = {
  campaign: ['campaign', 'adgroup', 'ad'],
  adgroup: ['adgroup', 'ad'],
  ad: ['ad'],
};

export const SON_TREE_AD_MAP = {
  campaign: ['adgroup', 'ad'],
  adgroup: ['ad'],
};

export const FATHER_TREE_AD_MAP = {
  adgroup: ['campaign'],
  ad: ['campaign', 'adgroup'],
};

export const CAMPAIGN_DELIVERY_STATUS = {
  TikTok: [
    {
      value: 'STATUS_DELETE',
      name: 'Deleted',
    },
    {
      value: 'STATUS_DELIVERY_OK',
      name: 'Active',
    },
    {
      value: 'STATUS_DISABLE',
      name: 'Inactive',
    },
    {
      value: 'STATUS_NOT_DELIVERY',
      name: 'Not delivering',
    },
  ],
  Facebook: [
    {
      value: 'DELETED',
      name: 'Deleted',
    },
    {
      value: 'ARCHIVED',
      name: 'Deleted',
    },
    {
      value: 'IN_PROCESS',
      name: 'Processing',
    },
    {
      value: 'WITH_ISSUES',
      name: 'Errors',
    },
    {
      value: 'ACTIVE',
      name: 'Active',
    },
    {
      value: 'PAUSED',
      name: 'Off',
    },
  ],
  Google: [
    {
      value: 'ELIGIBLE',
      name: 'Eligible',
    },
    {
      value: 'LIMITED',
      name: 'Eligible(limited)',
    },
    {
      value: 'ENDED',
      name: 'Ended',
    },
    {
      value: 'LEARNING',
      name: 'Learning',
    },
    {
      value: 'NOT_ELIGIBLE',
      name: 'Not eligible',
    },
    {
      value: 'PAUSED',
      name: 'Paused',
    },
    {
      value: 'PENDING',
      name: 'Pending',
    },
    {
      value: 'REMOVED',
      name: 'Removed',
    },
  ],
  Twitter: [
    {
      value: 'PAUSED',
      name: 'Paused',
    },
    {
      value: 'UNKNOWN',
      name: 'Incomplete',
    },
    {
      value: 'DRAFT',
      name: 'Draft',
    },
    {
      value: 'PAUSED',
      name: 'Paused',
    },
    {
      value: 'HALTED',
      name: 'Halted',
    },
    {
      value: 'RUNNING',
      name: 'Running',
    },
    {
      value: 'EXPIRED',
      name: 'Expired',
    },
    {
      value: 'SCHEDULED',
      name: 'Scheduled',
    },
    {
      value: 'DELETED',
      name: 'Deleted',
    },
  ],
  asa: [
    {
      value: 'DELETED',
      name: 'Deleted',
    },
    {
      value: 'ON_HOLD',
      name: 'On hold',
    },
    {
      value: 'PAUSED',
      name: 'Paused',
    },
    {
      value: 'RUNNING',
      name: 'Running',
    },
  ],
};

export const AD_MAP = {
  Google: {
    campaign: 'campaign',
    adgroup: 'ad_group',
    ad: 'ad_group_ad',
  },
  Facebook: {
    campaign: 'campaign',
    adgroup: 'adset',
    ad: 'ad',
  },
  Twitter: {
    campaign: 'campaign',
    adgroup: 'ad_group',
    ad: 'ad',
  },
  ASA: {
    campaign: 'campaign',
    adgroup: 'ad_group',
    ad: 'ad',
  },
};

export const IN_OR_IN = {
  default: ['OR_IN', 'IN'],
  Google: ['or_in', 'in'],
};

export const ADMAPS = [ADMAP.CAMPAIGN, ADMAP.ADGROUP, ADMAP.AD];
