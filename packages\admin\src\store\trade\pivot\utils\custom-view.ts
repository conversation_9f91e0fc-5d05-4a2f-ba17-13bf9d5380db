import { useTradePivotStore } from '@/store/trade/pivot/index.store';
import { ADMAP } from '@/views/trade/ads_management/const';
import { cloneDeep, has } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { useTdCustomView } from '../../customView.store';
import { MODULE } from './const';

export async function initConditionByCustomView(code: string, game: string, media: string) {
  const { defaultView } = storeToRefs(useTdCustomView());
  const { getShareViewById, setSharedView } = useTdCustomView();
  const { condition: conditionRef } = storeToRefs(useTradePivotStore());
  const condition = conditionRef.value;
  // 先清空分享视图
  setSharedView({});
  condition.adStructure = ADMAP.CAMPAIGN;
  // 假如url参数中有code,需基于code修改cur
  if (code) {
    const shareView = await getShareViewById(code);
    const { param = {}, system = '' } = shareView;
    if (Object.keys(param).length > 0 && system === MODULE) {
      // 1.将分享的视图保存起来，便于和默认视图做区分
      setSharedView({ ...shareView });
      // 2.切换到分享的视图
      condition.id = (shareView as any)._id;
      // 3.设置筛选条件
      setConditionByViewParams(param);
    }
  } else {
    const defaultViewValue = `default_${game}_${media}`;
    if (condition.id !== defaultViewValue) {
      condition.id = defaultViewValue;
      console.log('defaultview param');
      console.log(defaultView.value.param);
      setConditionByViewParams({
        ...defaultView.value.param,
        visibleFilters: ['account_id'],
      });
    }
  }
}

export function setConditionByViewParams(param: any, isSetDefaultCondition = false) {
  const { condition: conditionRef, filters, colDialogCheckedList: colRef } = storeToRefs(useTradePivotStore());
  const { setVisibleFilterList } = useTradePivotStore();
  const {
    condition: viewCondition = {},
    other = {},
    visibleFilters = [],
    colDialogCheckedList: vColList = [],
  } = param || {};
  const { adStructure = '' } = other;
  const condition = conditionRef.value;
  if (adStructure && [ADMAP.AD, ADMAP.ADGROUP, ADMAP.CAMPAIGN].includes(adStructure)) {
    condition.adStructure = adStructure;
  }
  // 设置筛选条件
  Object.keys(viewCondition).forEach((item) => {
    if (has(condition.cur, item)) {
      condition.cur[item] = cloneDeep(viewCondition)[item];
      if (isSetDefaultCondition) {
        condition.default[item] = cloneDeep(viewCondition)[item];
      }
    }
  });
  // 设置筛选条件的显示
  filters.value.conditionList = cloneDeep(filters.value.conditionList).map((item: any) => ({
    ...item,
    ext: {
      ...item.ext,
      isHide: !item.ext.isAllowClose ? false : !visibleFilters.includes(item.ext.key),
    },
  }));
  setVisibleFilterList(visibleFilters);
  // 设置初始展示指标
  if (vColList?.length > 0) {
    colRef.value = vColList;
  }
}
