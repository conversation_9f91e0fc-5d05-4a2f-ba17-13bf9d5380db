import { ADMAP } from '@/views/trade/ads_management/const';
import { isDraft } from '@/views/trade/ads_management/utils/base';
import { useRenderFormat } from 'common/compose/table/render-format';
import dayjs, { Dayjs } from 'dayjs';
import { storeToRefs } from 'pinia';
import { isPlainObject } from 'lodash-es';
import { useTradePivotStore } from '../index.store';
import type { IAnyOne, IColItem, IFormat, ILineData } from '../type';
import { TOP_NUM } from './const';
import { transByFormat } from 'common/service/td/pivot/get';
import { useGlobalGameStore } from '@/store/global/game.store';

export const getLineData = ({
  data,
  metric = 'spend',
  attribute = 'total',
  columns,
  legendObjList = [],
  game = '',
}: {
  data: any;
  date: Dayjs[];
  metric?: string;
  attribute?: string;
  columns: IColItem[];
  legendObjList: IAnyOne[];
  game: string;
}): ILineData => {
  const { dateList: dateListRef } = storeToRefs(useTradePivotStore());
  const dateList = dateListRef.value;
  if (data.length === 0) return {
    yField: metric,
    yFieldFormat: (v: string | number) => v,
    data: dateList.map((day) => {
      const param = {
        groupName: columns.find(({ colKey }) => colKey === metric)?.title,
        day: useRenderFormat({ format: 'date', value: day, opt: 'YYYY-MM-DD' }),
        [metric]: 0,
      };
      return param;
    }),
  };
  const res: any[] = [];
  let series = undefined;
  if (attribute === 'total') {
    dateList.forEach((day) => {
      const param = {
        groupName: columns.find(({ colKey }) => colKey === metric)?.title,
        day: useRenderFormat({ format: 'date', value: day, opt: 'YYYY-MM-DD' }),
      };
      // const curDayItem = data.find((one: { date: { value: string } }) => one.date.value === day);
      const curDayItem = data.find((dataItem: { date: { value: string } | string }) => {
        if (isPlainObject(dataItem.date)) {
          return (dataItem.date as { value: string })?.value === day;
        }
        return (dataItem.date as string) === day;
      });
      (param as any)[metric] =        curDayItem && (curDayItem as any)[metric] >= 0
        ? useRenderFormat({ format: 'float', value: (curDayItem as any)[metric], opt: 3 })
        : null;
      res.push(param);
    });
  } else {
    const legendObj = {}; // {'id':'name'}
    const seriesObj: any = {}; // {'id_2122':{data:[null, 123,], name:''}}
    data.forEach((one: any) => {
      const idValue = one[`${attribute}_id`];
      const dateIndex = dateList.indexOf(one.date);
      if (idValue && one[`${attribute}_name`]) {
        (legendObj as any)[idValue] = one[`${attribute}_name`];
      }
      if (!(idValue in seriesObj)) {
        (seriesObj as any)[idValue] = new Array(dateList.length).fill(null);
      }
      (seriesObj as any)[idValue][dateIndex] =        one[metric] >= 0 ? useRenderFormat({ format: 'float', value: one[metric], opt: 3 }) : null;
    });
    const fillLegend = legendObjList.filter((legend) => {
      const isIN = data.some((one: any) => one[`${attribute}_name`] === legend[`${attribute}_name`]
          && one[`${attribute}_id`] === legend[`${attribute}_id`]);
      return !isIN;
    });
    const fillDateArr = dateList.filter(day => !data.some(({ date }: { date: string }) => date === day));
    if (fillLegend.length > 0 || fillDateArr.length > 0) {
      const desLegend = fillLegend.length > 0 ? fillLegend : [data[0]];
      const desDateArr = fillDateArr.length > 0 ? fillDateArr : [dateList[0]];
      desDateArr.forEach((day, i) => {
        const dateIndex = dateList.indexOf(day);
        desLegend.forEach((legend) => {
          const idValue = legend[`${attribute}_id`];
          if (i === 0 && legend[`${attribute}_name`]) {
            (legendObj as any)[idValue] = legend[`${attribute}_name`];
          }
          if (!(idValue in seriesObj)) {
            (seriesObj as any)[idValue] = new Array(dateList.length).fill(0);
          }
          (seriesObj as any)[idValue][dateIndex] = 0;
        });
      });
    }
    if (Object.keys(seriesObj).length > 0) {
      series = Object.keys(seriesObj)
        .filter(k => (legendObj as any)[k])
        .map(k => ({ ...LINE_SERIES, name: (legendObj as any)[k], data: seriesObj[k] }));
    }
    dateList.forEach((date) => {
      Object.keys(legendObj).forEach((idValue) => {
        if ((legendObj as any)[idValue]) {
          res.push({
            day: useRenderFormat({ format: 'date', value: date, opt: 'YYYY-MM-DD' }),
            groupName: (legendObj as any)[idValue],
          });
        }
      });
    });
  }
  const colItem = columns.find(({ colKey }) => colKey === metric);
  const metricFormat = (colItem?.cardFormat || colItem?.format || 'numShort') as IFormat;
  return {
    yField: metric,
    yFieldFormat: (v: string) => {
      if (metricFormat === 'numShort' && Number(v) < 1) {
        return v;
      }
      const desV = transByFormat(v, metricFormat, colItem?.prefix, game);
      return desV;
    },
    data: res,
    series,
  };
};

export function getLegendFrChecked() {
  const { condition, checkedObj } = storeToRefs(useTradePivotStore());
  const { adStructure } = condition.value;
  if (checkedObj && (checkedObj.value as any)[adStructure].some((one: any) => !isDraft(one))) {
    return (checkedObj.value as any)[adStructure].filter((one: any) => !isDraft(one));
  }
  return [];
}

export const LINE_SERIES = {
  type: 'line',
  smooth: true, // 平滑曲线
  symbol: 'circle', // 实心拐点
  symbolSize: 10,
  showSymbol: false, // 鼠标悬浮时才显示拐点
  lineStyle: {
    // 线条样式
    width: 2,
  },
  itemStyle: {
    borderColor: 'var(--aix-border-color-white-primary)',
    borderWidth: 2,
  },
};

export function getLineParamWithDate(src: any, isInit = false) {
  const param = JSON.parse(JSON.stringify(src));
  const { isDemoGame } = useGlobalGameStore();
  if (isInit && !isDemoGame()) {
    const dateArr = param.condition.date;
    dateArr[0] = dayjs(dateArr[0]).subtract(6, 'day');
  }
  return param;
}

export function getTopParam({
  baseCondition,
  param,
  colItem,
  condition,
  isInit,
}: {
  baseCondition: {
    game: string;
    media: string;
    attribute: ADMAP;
    adStructure: ADMAP;
    columns: any;
  };
  param: any;
  colItem: any;
  condition: any;
  isInit: boolean;
}) {
  const res = {
    baseCondition: { ...baseCondition, adStructure: param.attribute as ADMAP, columns: [colItem] },
    condition: {
      ...condition.cur,
      sort: { sortBy: colItem.colKey, descending: true },
      pageSize: TOP_NUM,
      pageIndex: 1,
    },
  };
  if (isInit) {
    return getLineParamWithDate(res, isInit);
  }
  return res;
}
