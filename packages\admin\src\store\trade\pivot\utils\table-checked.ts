import { ADMAP } from '@/views/trade/ads_management/const';
import { isDraft } from '@/views/trade/ads_management/utils/base';
import { unique } from './base';
/**
 * @description checkedObj字段恢复成默认值
 * @returns
 */
export function getDefaultChecked() {
  const newObj: any = {};
  newObj.campaign = [];
  newObj.adgroup = [];
  newObj.ad = [];
  return newObj;
}

/**
 * @description clickedObj字段恢复成默认值
 * @returns
 */
export function getDefaultClicked() {
  const newObj: any = {};
  newObj.campaign = null;
  newObj.adgroup = null;
  newObj.ad = null;
  return newObj;
}

/**
 * @description 表格中全选或者勾选一条时 checkedObj调整
 * @export
 * @param {*} { serverData, row, type, value, checkedObj, adStructure }
 * @returns
 */
export function getNewChecked({
  serverData,
  row,
  type,
  value,
  checkedObj,
  adStructure,
}: {
  serverData: any;
  row: any;
  type: string;
  value: boolean;
  checkedObj: any;
  adStructure: string;
}) {
  const newChecked = JSON.parse(JSON.stringify(checkedObj));
  const curChecked = newChecked[adStructure];
  switch (type) {
    case 'group':
      // 先清空原来勾选的子集元素
      (newChecked as any)[adStructure] = curChecked.filter(({ data_fr = '', id = '', parent_id = '' }) => {
        if (row.data_fr === data_fr) {
          return !(row.id === parent_id || row.id === id);
        }
        return row.data_fr !== data_fr;
      });
      if (value) {
        // 假如checkbox是勾选的，则其下面子集元素都需勾选
        (newChecked as any)[adStructure] = (newChecked as any)[adStructure].concat(serverData);
      }
      break;
    case 'one':
      // value 为true时 需要补充, false时需剔除
      if (value) {
        (newChecked as any)[adStructure].push(row);
        break;
      } else {
        (newChecked as any)[adStructure] = curChecked.filter((cur: any) => {
          const val = isDraft(row) ? `inner_${adStructure}_id` : `${adStructure}_id`;
          const res = (row.data_fr === cur.data_fr && row[val] !== cur[val]) || row.data_fr !== cur.data_fr;
          return res;
        });
        break;
      }
  }
  [ADMAP.CAMPAIGN, ADMAP.ADGROUP, ADMAP.AD].forEach((ad) => {
    unique((newChecked as any)[ad], ad);
  });
  return newChecked;
}
