/** For数据渲染过程中 涉及到的逻辑, 用于获取表格数据 **/
import { ADMAP } from '@/views/trade/ads_management/const';
import { isDraft, isDraftRoot } from '@/views/trade/ads_management/utils/base';
import { getResourceName } from '@/views/trade/ads_management/utils/table/data-google';
import { useEnv } from 'common/compose/env';
import { getCardOrLine } from 'common/service/td/pivot/get';
import { getStatusStatisticData } from 'common/service/td/pivot/get-attrdata';
import { getMediaIdByDraftSon } from 'common/service/td/pivot/get-draft';
import { uniqueId } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { capitalize } from 'vue';
import { useTradePivotStore } from '../index.store';
import { IAnyOne, IColGroup, IColItem, IOptionItem } from '../type';
import {
  DATACON,
  DATATYPE,
  DRAFT_ROOT,
  FATHER_TREE_AD_MAP,
  FIXED_COLS,
  OFFLINE_WHITE_GAME,
  PRE_CONVERSIONS_WHITE_GAME,
  PRE_CONVERSIONS_WHITE_MEDIA,
  PUBLISHED_ROOT,
  SON_TREE_AD_MAP,
} from './const';
import { getDraftParam, getStatusStatisticParam } from './transform-adsvr';
import { getApiParam } from './transform-pivotsvr';
import { useGlobalGameStore } from '@/store/global/game.store';

/**
 * @description 用于生成普通表格用的数据
 * curChecked 参数 是tableData中checkbox勾选与否的源；会因为store中curChecked的变化而变化
 * 若当前页面中只有草稿数据，则使用 allDraftData.slice((pageIndex -1) * pageSize, pageIndex + pageSize);
 * 若当前页面中既有草稿，又有published数据，则用 curDraftData.concat(publishedData).slice();
 * 若当前页面中只有 published 数据，则用 publishedData
 */
export function getTableData({
  draftAllData,
  publishedTotalNum,
  publishedCurData,
  curChecked,
  adStructure,
  columns,
  pageIndex,
  pageSize,
  hasDraftRoot,
  hasPublishedRoot,
  draftRootChecked,
}: {
  draftAllData: any[];
  publishedTotalNum: number | string;
  publishedCurData: any[];
  curChecked: any[];
  adStructure: string;
  columns: any[];
  pageIndex: number;
  pageSize: number;
  hasDraftRoot: boolean;
  hasPublishedRoot: boolean;
  draftRootChecked: boolean[];
}): { draftAllData: any[]; publishedCurData: any[]; data: any[]; num: number } {
  // console.log('----------- 计算属性 getTableData start：---------- ');
  // console.log('publishedCurData:', publishedCurData);
  // console.log('publishedTotalNum:', publishedTotalNum);
  // console.log('draftAllData:', draftAllData);
  // console.log('pageIndex:', pageIndex);
  // 获取当前页面中的草稿和已发布数据
  const {
    draftData = [],
    publishedData = [],
    curHasDraftRoot,
    curHasPublishedRoot,
  } = getCurPageData({
    draftAllData,
    publishedCurData,
    pageIndex,
    pageSize,
  });
  let res = [] as any[];
  if (curHasDraftRoot || (hasDraftRoot && pageIndex === 1)) {
    const draftNum =      curChecked && curChecked.length > 0
      ? curChecked.filter((src: any) => isDraft(src) && !isDraftRoot(src)).length
      : 0;
    const dSelectedText = draftNum > 0 ? `${draftNum} selected` : '';
    res[0] = {
      ...DRAFT_ROOT,
      ROWKEY: uniqueId(),
      [`${adStructure}_name`]: `Drafts ${dSelectedText}`,
      checked: !!draftRootChecked[pageIndex - 1],
    };
    const draftArr = draftData.map((one: any) => {
      const param = getDraftResParam({
        checked: curChecked?.some((src: any) => isDraft(src) && src[`inner_${adStructure}_id`] === one[`inner_${adStructure}_id`]),
      }) as any;
      columns.forEach(({ colKey = '' }) => {
        (param as any)[colKey] = '-';
      });
      return { ...param, ...one };
    });
    res = res.concat(draftArr);
  }
  // 非草稿页才有published分组头 draftData/pageSize
  if (curHasPublishedRoot || (hasPublishedRoot && pageIndex > draftAllData.length / pageSize)) {
    const publishedNum = curChecked.filter((src: any) => !isDraft(src)).length;
    const pSelectedText = publishedNum > 0 ? `${publishedNum} selected` : '';
    const arr = [
      {
        ...PUBLISHED_ROOT,
        ROWKEY: uniqueId(),
        [`${adStructure}_name`]: `Published ${adStructure}s ${pSelectedText}`,
        checked: curChecked && publishedNum > 0 && publishedNum === publishedData.length,
      },
    ];
    const publishedArr = publishedData.map((one: any) => {
      const newItem = getPublishedResParam({
        ...one,
        checked: curChecked?.some((src: any) => !isDraft(src) && src[`${adStructure}_id`] === one[`${adStructure}_id`]),
      });
      return newItem;
    });
    res = res.concat(arr).concat(publishedArr);
  }
  console.log('------ table data in td pivot ------------:', res);
  return {
    draftAllData, // 草稿状态的表格
    publishedCurData, // 当前拉取回来已发布的数据
    data: res,
    num: draftAllData.length + parseInt(publishedTotalNum.toString(), 10), // 总的已发布的数据条数
  };
}
/**
 * @description 获取当前页的数据，可能有草稿，或已发布
 * @param {{
 *   draftAllData: any[];
 *   publishedCurData: any[];
 *   condition: any;
 * }} {
 *   draftAllData,
 *   publishedCurData,
 *   condition,
 * }
 * @returns
 */
function getCurPageData({
  draftAllData,
  publishedCurData,
  pageIndex = 1,
  pageSize = 20,
}: {
  draftAllData: any[];
  publishedCurData: any[];
  pageIndex: number;
  pageSize: number;
}): { draftData?: any[]; publishedData?: any[]; curHasDraftRoot: boolean; curHasPublishedRoot: boolean } {
  const draftAllNum = draftAllData.length;
  const prevTotalPageNum = pageSize * (pageIndex - 1); // 截止前一页总条数
  // diffCount > 0: 还有草稿待展示; diffCount <=0: 前一页中有部分已发布数据
  const diffCount = draftAllNum - prevTotalPageNum; // 总草稿数 和 截止前一页总条数 之间的差值;
  // 需要拉取分页的数据来补全当前页
  if (diffCount < pageSize) {
    // ===============全是已发布=================
    if (diffCount <= 0) {
      const publishedData = publishedCurData.slice(0, pageSize);
      return {
        publishedData,
        curHasDraftRoot: false,
        curHasPublishedRoot: publishedData.length > 0,
      };
    }
    //  ===============部分草稿和部分已发布===========
    const draftData = draftAllData.slice((pageIndex - 1) * pageSize, (pageIndex - 1) * pageSize + diffCount);
    const publishedData = publishedCurData.slice(0, pageSize - diffCount);
    return {
      draftData,
      publishedData,
      curHasDraftRoot: draftData.length > 0,
      curHasPublishedRoot: publishedData.length > 0,
    };
  }
  const draftData = draftAllData.slice((pageIndex - 1) * pageSize, pageIndex * pageSize);
  return {
    draftData,
    curHasDraftRoot: draftData.length > 0,
    curHasPublishedRoot: false,
  };
}
export const getDraftResParam = (param: any) => ({
  ...param,
  layer_id: 1,
  data_fr: DATACON.LOCAL,
  parent_id: DATACON.GROUP_DRAFT,
});
export const getPublishedResParam = (param: any) => ({
  ...param,
  layer_id: 1,
  data_fr: DATACON.MEDIA,
  parent_id: 'group_published',
});

export function getColByEnvGame(src: IColItem[], game: string) {
  const { isPrerelease, isProduction, isExp, isFuncom } = useEnv();
  const isPro = isPrerelease || isProduction || isExp;
  if (isFuncom.value) return src;
  return isPro && !OFFLINE_WHITE_GAME.includes(game) ? src.filter(({ version = '' }) => version !== 2) : src;
}

/**
 * @description 白名单游戏走新逻辑, 白名单渠道走新逻辑
 * type为pivot; 查询指标中包含coversions; 测试环境所有游戏+正式环境白名单游戏 需补充 conversions_map 字段。
 */
export function isPreWithConversions({ cols, game, media }: { cols: string[]; game?: string, media?: string }) {
  let desGame = game || '';
  if (!desGame) {
    // 从global store中取game
    const gameStore = useGlobalGameStore() || {};
    desGame = gameStore?.gameCode || '';
  }
  const { isFuncom } = useEnv();
  // gcp上所有的游戏都在白名单中；intlgame上常量中配置的游戏才在白名单中。
  const isWhiteGame = PRE_CONVERSIONS_WHITE_GAME.includes(desGame) || (isFuncom.value && media !== 'TikTok');
  const isWhiteMedia = PRE_CONVERSIONS_WHITE_MEDIA.includes(media!);
  const hasConv = cols.includes('conversions');
  return isWhiteGame && isWhiteMedia && hasConv;
}

export function getMetricOpt(col: any[]): IOptionItem[] {
  return col.filter(({ type = '' }) => type !== 'attr').map(one => ({ label: one.title, value: one.colKey }));
}
/**
 * @description 初始化时展示列;用于表格列展示和自定义穿梭框右边
 * 这里是初始化时的方法，是起点。
 */
export function getDefaultCols(totalCols: any[], adStructure: string) {
  const list = [...totalCols.filter((one: any) => one?.default)];
  const fixedNameCol = FIXED_COLS[1];
  fixedNameCol.title = `${capitalize(adStructure)} Name`;
  fixedNameCol.titleStr = `${capitalize(adStructure)} Name`;
  fixedNameCol.colKey = `${adStructure}_name`;
  list.splice(fixedNameCol.index, 0, fixedNameCol);
  return list;
}
/**
 * @description 基于自定义列弹窗中的选中值生成表格的展示列，需将fixed_columns中的列补充进去
 * 补充了name展示后的列需要调整。
 * 最高层级只保留当前层级的名称，若有高层级id勾选则需要展示;
 * 作用于列时，adStructure_name 需要补充fixed:true,  ellipsis: true, 两个属性
 * @export
 * @param {*} checked
 */
export function getTableCols(checkedList: IColItem[], adStructure: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD) {
  let colList = JSON.parse(JSON.stringify(checkedList));
  if (adStructure === ADMAP.CAMPAIGN) {
    colList = colList.filter(({ colKey }: { colKey: string }) => !['adgroup_name', 'adgroup_id', 'ad_name', 'ad_id'].includes(colKey));
  } else if (adStructure === ADMAP.ADGROUP) {
    colList = colList.filter(({ colKey }: { colKey: string }) => !['ad_name', 'ad_id'].includes(colKey));
  }
  const fixedNameCol = colList[1];
  colList[1] = { ...FIXED_COLS[1], ...fixedNameCol }; // 补充fixed:true,  ellipsis: true, 两个属性
  colList.splice(FIXED_COLS[0].index, 0, FIXED_COLS[0]);
  return colList;
}
/**
 * @description for 将后台数据成自定义弹窗列中需要的原始数据
 * src:[{colKey, title, groupName}]
 * dst:[{groupName, list:[{text, value}]}]
 */
export function toGroup(src: any[]): IColGroup[] {
  const dstObj = {} as any;
  src.forEach((one) => {
    const groupValue = one.groupName;
    if (groupValue in dstObj) {
      dstObj[groupValue].push(one);
    } else {
      dstObj[groupValue] = [one];
    }
  });
  return Object.keys(dstObj).map(v => ({ groupName: v, list: dstObj[v] }));
}

/**
 * @description 用于生成t-enhanced-table表格用的数据
 * @export
 * @param {{
 *   draftData: any[];
 *   publishedData: any[];
 *   curChecked: any[];
 * }} {
 *   draftData,
 *   publishedData,
 *   curChecked,
 * }
 * @returns {any[]}
 */
export function composeTreeTableData({
  draftData,
  publishedData,
  curChecked,
}: {
  draftData: any[];
  publishedData: any[];
  curChecked: any[];
}): any[] {
  let res = [];
  if (draftData && draftData.length > 0) {
    res[0] = {
      id: 'DRAFTS_GROUP',
      groupName: 'Drafts',
      status: 'draft',
      children: draftData.map((one: { id: string }) => ({
        ...one,
        checked: curChecked.find((src: any) => isDraft(src) && src.id === one.id),
      })),
      checked: curChecked.filter((src: any) => isDraft(src)).length === draftData.length,
      layerId: 0,
      data_fr: 'local',
    };
  }
  if (publishedData && publishedData.length > 0) {
    const arr = [
      {
        id: 'PUBLISHED_GROUP',
        groupName: 'Published campaings',
        status: 'published',
        children: publishedData.map((one: { id: any }) => ({
          ...one,
          checked: curChecked.find((src: any) => !isDraft(src) && src.id === one.id),
        })),
        checked: curChecked.filter((src: any) => !isDraft(src)).length === draftData.length,
        layerId: 0,
        data_fr: 'media',
      },
    ];
    res = res.concat(arr);
  }
  return res;
}

/**
 * @description 基于子层级draft delivery status数据查询父层级media_id
 */
export async function getMediaIdsFrDraftApi() {
  const { condition: conRef, tableColumns } = storeToRefs(useTradePivotStore());
  const condition = conRef.value;
  const sonDrafts = condition.cur.delivery_status?.filter((one: string) => (SON_TREE_AD_MAP as any)[condition.adStructure].includes(one.split('|')[0]));
  const sonAds = Array.from(new Set(sonDrafts?.map(one => one.split('|')[0])));
  let ids: any[] = [];
  if (sonAds && sonAds.length > 0) {
    for (const sonAd of sonAds) {
      const draftParams = getDraftParam({
        baseCondition: {
          game: condition.game,
          media: condition.media,
          adStructure: sonAd as ADMAP,
          columns: tableColumns.value,
        },
        condition: {
          delivery_status: sonDrafts,
        },
      });
      const idsRes = await getMediaIdByDraftSon(draftParams, sonAd, condition.adStructure);
      ids = ids.concat(idsRes);
    }
  }
  ids = Array.from(new Set(ids.filter(x => x))); // 去空去重
  return ids;
}

/**
 * @description 基于祖先层级published delivery status数据查adStructure层的media_id；用于过滤草稿
 */
export async function getMediaIdsByFatherDevlieryStatus(): Promise<{ ad: string; value: any }[]> {
  const { condition: conditionRef, tableColumns } = storeToRefs(useTradePivotStore());
  const condition = conditionRef.value;
  const deliveryObj = {};
  condition.cur.delivery_status
    ?.filter((v: any) => {
      const [ad, , type] = v.split('|');
      return (FATHER_TREE_AD_MAP as any)[condition.adStructure].includes(ad) && type !== 'draft';
    })
    .forEach((v: any) => {
      const [ad, value] = v.split('|');
      if (ad in deliveryObj) {
        (deliveryObj as any)[ad].push(value);
      } else {
        (deliveryObj as any)[ad] = [value];
      }
    });
  const data = await Promise.all(Object.keys(deliveryObj).map((ad) => {
    const value = (deliveryObj as any)[ad];
    const baseCondition = {
      game: condition.game,
      media: condition.media,
      adStructure: ad as ADMAP,
      attribute: ad as ADMAP,
      columns: tableColumns.value,
    };
    const topParam = {
      baseCondition,
      condition: { [`aix_${ad}_delivery_status`]: value },
    };
    return getCardOrLine(getApiParam({ ...topParam, type: DATATYPE.TOTAL }), '');
  }));
  const res = Object.keys(deliveryObj)
    .map((ad: any, i) => {
      const ids = data[i]
        .map((one: IAnyOne) => {
          if (condition.media === 'Google') return getResourceName(one, ad);
          return one[`${ad}_id`];
        })
        .filter((x: any) => x);
      return { ad, value: ids };
    })
    .filter(({ value = [] }) => value.length > 0); // value为空的去过滤掉
  return res;
}

/**
 * @description 基于后台查询的table数据 查对应的数据状态
 * @export
 * @param {{ draftAllData: any; publishedCurData: any; }} table
 */
export async function setStatusMap(table: { draftAllData?: any; publishedCurData?: any }) {
  const { condition: conditionRef, statusObj } = storeToRefs(useTradePivotStore());
  const condition = conditionRef.value;
  const { draftAllData = [], publishedCurData = [] } = table || {};
  const params = getStatusStatisticParam({
    game: condition.game,
    media: condition.media,
    adStructure: condition.adStructure,
    src: { draft: draftAllData, published: publishedCurData },
  });
  const data = await getStatusStatisticData(condition.media, condition.adStructure, params);
  statusObj.value[condition.adStructure] = data;
}
