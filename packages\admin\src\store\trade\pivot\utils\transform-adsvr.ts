import { ADMAP } from '@/views/trade/ads_management/const';
import { isDraft } from '@/views/trade/ads_management/utils/base';
import { genRourceNameList, getResourceName } from '@/views/trade/ads_management/utils/table/data-google';
import { MEDIA as GMEDIA } from 'common/const/globalType';
import { SERVER_MEDIA_MAP } from 'common/const/trade';
import { IBaseCondition, IDraftParam, IFrontValue } from '../type';
import { addQuotation, getDeliveryStatusObj } from './base';
import { AD_MAP, COLS, OPT_STATUS, TREE_AD_MAP } from './const';
import { getSearchBoxValue } from './transform-pivotsvr';
import { genCommonKey } from 'common/service/td/pivot/get-draft';
import { isEmpty, uniq } from 'lodash-es';

/**
 * @description 生成调用后台删除草稿接口用的参数
 */
export function getDeleteParams({
  list,
  game,
  media,
  adStructure,
}: {
  type: string;
  list: any[];
  game: string;
  media: string;
  adStructure: string;
}) {
  const adInParam = getAdInMediaSvr(adStructure, media);
  switch (media.toLocaleLowerCase()) {
    case GMEDIA.TIKTOK:
      return {
        game_code: game,
        media,
        [`account_draft_${adStructure}s`]: list.map(one => ({
          account_id: one.account_id,
          [`inner_${adStructure}_id`]: one[`inner_${adStructure}_id`],
        })),
      };
    case GMEDIA.FACEBOOK:
      return {
        game_code: game,
        advertise_type: adStructure === ADMAP.ADGROUP ? 'adset' : adStructure,
        inner_ids: list.map(one => one[`inner_${adStructure}_id`]).filter(x => x),
      };
    case GMEDIA.GOOGLE:
      return {
        key: `inner_${adInParam}_id`,
        [`inner_${adInParam}_id`]: list.map(one => one[`inner_${adStructure}_id`]).filter(x => x),
      };

    case GMEDIA.TWITTER:
      return {
        advertise_type: adStructure === ADMAP.ADGROUP ? 'ad_group' : adStructure,
        inner_ids: list.map(one => one[`inner_${adStructure}_id`]).filter(x => x),
      };
    default:
      return [];
  }
}

/**
 * @description 生成调用后台批量修改状态接口用的参数
 */
export function getEditParams({
  type,
  list,
  game,
  media,
  adStructure,
}: {
  type: string;
  list: any[];
  game: string;
  media: string;
  adStructure: string;
}) {
  const accountIds = Array.from(new Set(list.map(one => one.account_id))).filter(x => x);
  if (!accountIds || accountIds.length === 0) return;
  switch (media.toLowerCase()) {
    case GMEDIA.TIKTOK:
      return {
        [`account_media_${adStructure}s`]: list.map(item => ({
          account_id: item.account_id,
          [`${adStructure}_id`]: item[`${adStructure}_id`],
        })),
        media: (SERVER_MEDIA_MAP as any)[media].attr_server,
        game_code: game,
        target_status: (OPT_STATUS as any)[media][type],
      };
    case GMEDIA.FACEBOOK:
      return {
        game_code: game,
        [`${getAdInMediaSvr(adStructure, media)}_info`]: list.map((one) => {
          const adInParam = getAdInMediaSvr(adStructure, media);
          const param = {
            status: (OPT_STATUS as any)[media][type],
            level: adInParam,
            account_id: one.account_id,
            [`inner_${adInParam}_id`]: '',
            [`media_${adInParam}_id`]: one[`${adStructure}_id`],
          };
          if (adStructure === ADMAP.ADGROUP) param.media_campaign_id = one.campaign_id;
          if (adStructure === ADMAP.AD) param.media_ad_set_id = one.adgroup_id;
          return param;
        }),
      };
    case GMEDIA.GOOGLE:
      return {
        [`${getAdInMediaSvr(adStructure, media)}s`]: list.map((one) => {
          const adInParam = getAdInMediaSvr(adStructure, media);
          const param = {
            [`${adStructure === ADMAP.ADGROUP ? 'group' : adStructure}_status`]: (OPT_STATUS as any)[media][type],
            [`inner_${adInParam}_id`]: 0,
            [`${adInParam}_resource_name`]: getResourceName(one, adStructure),
            account_id: one.account_id,
          };
          return param;
        }),
      };
    case GMEDIA.TWITTER:
      return {
        gameCode: game,
        advertise_type: adStructure === ADMAP.ADGROUP ? 'ad_group' : adStructure,
        status: list.map((one) => {
          const param = {
            status: (OPT_STATUS as any)[media][type],
            account_id: one.account_id,
            inner_id: '',
            media_id: one[`${adStructure}_id`],
          };
          return param;
        }),
      };
    default:
      return [];
  }
}

/**
 * @description 生成调用后台删除草稿接口用的参数
 */
export function getDraftParam({
  baseCondition,
  condition,
  checkedObj,
  noNeed = false,
  extraCondition = [],
}: {
  baseCondition: IBaseCondition;
  condition: IFrontValue;
  checkedObj?: any;
  noNeed?: boolean;
  extraCondition?: any;
}): IDraftParam {
  const { game = '', media: frontMedia = '', adStructure } = baseCondition;
  const hasOptStaus = condition.opt_status && condition.opt_status?.length > 0; // 当勾选了opt_status时不需要查草稿
  // 筛选器中当前层级或子层级delivery_status均为已发布时，不需要查草稿
  let allPublishedStatus = false;
  if (
    condition.delivery_status?.some((v) => {
      const [ad = ''] = v.split('|');
      return TREE_AD_MAP[adStructure].includes(ad);
    })
  ) {
    const hasDraftStatus = condition.delivery_status
      .filter((v) => {
        const [ad = ''] = v.split('|');
        return TREE_AD_MAP[adStructure].includes(ad);
      })
      .some((v) => {
        const [, , type = ''] = v.split('|');
        return type === 'draft';
      });
    allPublishedStatus = !hasDraftStatus;
  }
  // campaign_id查询的时候也不需要查草稿
  let hasCampaignIdSeach = false;
  if (!isEmpty(condition?.search_box?.[3]?.condition)) {
    hasCampaignIdSeach = true;
  }
  // 筛选器 aix_locations字段不为空时，不需要查草稿;但是点击收缩放开草稿时需要查草稿
  const noNeedDraft = noNeed || hasOptStaus || allPublishedStatus || hasCampaignIdSeach;
  if (noNeedDraft) {
    return {
      game_code: game,
      media: frontMedia,
      noNeed: noNeedDraft,
    };
  }
  const { date, sort, pageIndex, pageSize, ...attrCon } = condition; // condition 中传入了search_box字段用于做检索
  attrCon.campaign_name = getSearchBoxValue('campaign_name', condition.search_box);
  attrCon.adgroup_name = getSearchBoxValue('adgroup_name', condition.search_box);
  attrCon.ad_name = getSearchBoxValue('ad_name', condition.search_box);
  const filterAttrCon: IFrontValue = {};
  Object.keys(attrCon)
    .filter((k) => {
      // 过滤掉了search_box
      const isTrue = COLS.DRAFT_FILTERS.includes(k) && (attrCon as any)[k] && (attrCon as any)[k].length > 0;
      return isTrue;
    })
    .forEach(k => ((filterAttrCon as any)[k] = Array.from(new Set((attrCon as any)[k]))));
  switch (frontMedia) {
    case 'TikTok':
      return getTikTokParam({ attrCon: filterAttrCon, game, checkedObj, adStructure, extraCondition });
    case 'Facebook':
      return {
        media: SERVER_MEDIA_MAP.Facebook.attr_server,
        game_code: game,
        conditions: getCommonConditions({
          game,
          media: frontMedia,
          condition: filterAttrCon,
          checkedObj,
          adStructure,
          extraCondition,
        }),
        offset: 0,
        limit: 20,
        advertise_type: adStructure === ADMAP.ADGROUP ? 'adset' : adStructure,
      };
    case 'Google':
      return {
        media: SERVER_MEDIA_MAP.Google.attr_server,
        game_code: game,
        conditions: getCommonConditions({
          game,
          media: frontMedia,
          condition: filterAttrCon,
          checkedObj,
          adStructure,
          extraCondition,
        }),
        offset: 0,
        limit: 20,
        advertise_type:
          adStructure === ADMAP.ADGROUP ? 'ad_group' : adStructure === ADMAP.AD ? 'ad_group_ad' : adStructure,
      };
    case 'Twitter':
      return {
        media: SERVER_MEDIA_MAP.Twitter.attr_server,
        game_code: game,
        conditions: getCommonConditions({
          game,
          media: frontMedia,
          condition: filterAttrCon,
          checkedObj,
          adStructure,
          extraCondition,
        }),
        offset: 0,
        limit: 20,
        advertise_type: adStructure === ADMAP.ADGROUP ? 'ad_group' : adStructure,
      };
    default:
      return {
        media: frontMedia,
        game_code: game,
      };
  }
}
/**
 * https://yapi.intlgame.com/project/1187/interface/api/67689
 */
export function getTikTokParam({
  attrCon,
  game,
  checkedObj,
  adStructure,
  extraCondition,
}: {
  attrCon: IFrontValue;
  game: string;
  checkedObj: any;
  adStructure: string;
  extraCondition: any[];
}): IDraftParam {
  const media = SERVER_MEDIA_MAP.TikTok.attr_server;
  const filter: any = {};
  Object.keys(attrCon).forEach((key) => {
    if (key === 'aix_locations') {
      filter.region_codes = attrCon[key];
    } else if (key === 'aix_campaign_type') {
      filter.campaign_types = attrCon[key];
    } else if (key === 'delivery_status') {
      const hasDraft = attrCon[key]?.some((v: string) => {
        const [, , type = ''] = v.split('|');
        return type === 'draft';
      });
      if (hasDraft) {
        const deliveryStatusObj = getDeliveryStatusObj({ list: attrCon[key] || [], media: 'TikTok', isDraft: true });
        if (JSON.stringify(deliveryStatusObj) !== '{}') {
          Object.keys(deliveryStatusObj).forEach((ad) => {
            filter[`${ad}_status_list`] = (deliveryStatusObj as any)[ad];
          });
        }
      }
    } else {
      filter[COLS.NAMES.includes(key) ? `${key}s` : key] = (attrCon as any)[key];
    }
  });
  const fatherAd = getCommonFatherAd(checkedObj, adStructure);
  if (!fatherAd && (!extraCondition || extraCondition.length === 0)) return {
    media,
    game_code: game,
    __game: game,
    account_ids: attrCon.account_id || [],
    filter,
  };
  // checkbox 勾选框中 祖先层级若有勾选的，需传入此筛选条件
  if ([ADMAP.CAMPAIGN, ADMAP.ADGROUP].includes(fatherAd as ADMAP)) {
    const newFilter = genTTNewFilter({ checkedObj, adStructure: fatherAd });
    Object.keys(newFilter)
      .filter(k => newFilter[k])
      .forEach((k) => {
        filter[k] = newFilter[k];
      });
  }
  if (extraCondition && extraCondition?.length > 0) {
    extraCondition.forEach(({ ad, value = [] }) => {
      if (`${ad}_ids` in filter) {
        // 需要与checked中父层级的 取交集。
        filter[`${ad}_ids`] = filter[`${ad}_ids`].filter((one: any) => value.includes(one));
      } else {
        filter[`${ad}_ids`] = value;
      }
    });
  }
  return {
    media,
    game_code: game,
    __game: game,
    account_ids: attrCon.account_id || [],
    filter,
  };
}

export function getCommonConditions({
  game,
  media,
  condition,
  checkedObj,
  adStructure,
  extraCondition = [],
}: {
  game: string;
  media: string;
  condition: any;
  checkedObj: any;
  adStructure: string;
  extraCondition: any;
}) {
  let params = [{ column: 'game_code', operator: '=', value: [game] }];
  const nameConList = getParamsByAdName({ media, condition });
  params = params.concat(nameConList);
  const ifThenPush = (key: any) => {
    if (condition[key] && condition[key].length > 0) {
      const desKey = media === 'Facebook' && key === 'aix_campaign_type' ? 'objective' : key;
      const value =        media === 'Google'
        ? addQuotation({ str: condition[key], isNumNeed: true }).split(',')
        : condition[key].join(',').split(',');
      params.push({
        column: desKey,
        operator: 'IN',
        value,
      });
    }
  };
  ['account_id', 'aix_locations', 'aix_campaign_type'].forEach(key => ifThenPush(key));
  if (condition.delivery_status && condition.delivery_status.length > 0) {
    const deliveryStatusObj = getDeliveryStatusObj({
      list: condition.delivery_status || [],
      media,
      isDraft: true,
    });
    if (JSON.stringify(deliveryStatusObj) !== '{}') {
      Object.keys(deliveryStatusObj).forEach((ad) => {
        const value = (deliveryStatusObj as any)[ad];
        const column = genCommonKey({ media, adStructure: ad, suffix: 'status' });
        params.push({
          column,
          operator: 'IN',
          value: media === 'Google' ? addQuotation({ str: value, isNumNeed: true }).split(',') : value,
        });
      });
    }
  }
  // checkbox中有父层级勾选时
  const fatherAd = getCommonFatherAd(checkedObj, adStructure);
  /** extraCondition 中不在 checkedObj中的层级，需要 补充过滤 **/
  if (extraCondition && extraCondition.length > 0) {
    let othersExtra = fatherAd ? extraCondition.find((one: { ad: string }) => one.ad !== fatherAd) : extraCondition;
    if (othersExtra) {
      if (!(othersExtra instanceof Array)) othersExtra = [othersExtra];
      const others = othersExtra?.filter(({ ad }: { ad: string }) => ad !== fatherAd);
      others.forEach(({ ad, value }: { ad: string; value: any[] }) => {
        const inStr = media === 'Google' ? 'in' : 'IN';
        let desValue = value;
        if (media === 'Google') desValue = addQuotation({ str: desValue, isNumNeed: true }).split(',');
        const adInParams = (AD_MAP as any)[media][ad];
        const column =          media === 'Google'
          ? `${adInParams}_resource_name`
          : media === 'Twitter'
            ? `media_${adInParams}_id`
            : `${adInParams}_id`;
        const othersParam = { column, operator: inStr, value: desValue };
        params.push(othersParam);
      });
    }
  }
  if (!fatherAd) return params;
  /** checked 父层级的数据 需要 以 extraCondition中的相应层级为基础 做过滤 **/
  let fatherChecked = checkedObj[fatherAd];
  const fatherExtra = extraCondition.length > 0 && extraCondition.find((one: { ad: string }) => one.ad === fatherAd);
  if (fatherExtra) {
    fatherChecked = fatherChecked.filter((one: any) => fatherExtra?.value.includes(one[`${fatherAd}_id`]));
  }
  const checkedParams: any[] = getCommonChecked(fatherChecked, fatherAd, media);
  return params.concat(checkedParams);
}

/**
 * @description For FB, GG, TW
 * @export
 * @param {*} checkedObj
 * @param {string} adStructure
 * @returns {string}
 */
export function getCommonFatherAd(checkedObj: any, adStructure: string): string {
  if (checkedObj === undefined || adStructure === ADMAP.CAMPAIGN) return '';
  if (adStructure === ADMAP.ADGROUP && checkedObj[ADMAP.CAMPAIGN].length > 0) {
    return ADMAP.CAMPAIGN;
  }
  if (adStructure === ADMAP.AD) {
    let fatherAd = '';
    if (checkedObj[ADMAP.CAMPAIGN].length > 0) {
      fatherAd = ADMAP.CAMPAIGN;
    }
    if (checkedObj[ADMAP.ADGROUP].length > 0) {
      fatherAd = ADMAP.ADGROUP;
    }
    return fatherAd;
  }
  return '';
}
export function getCommonChecked(checkList: any[], ad: string, media: string) {
  const adInParams = (AD_MAP as any)[media][ad];
  if (!checkList || checkList.length === 0) return [];
  const draftList = checkList.filter(row => isDraft(row));
  const publishedList = checkList.filter(row => !isDraft(row));
  const hasDraft = draftList.length > 0;
  const hasPublished = publishedList.length > 0;
  const orInStr = media === 'Google' ? 'or_in' : 'OR_IN';
  const inStr = media === 'Google' ? 'in' : 'IN';
  const operator = hasDraft && hasPublished ? orInStr : !hasDraft && !hasPublished ? '' : inStr;
  if (!operator) return [];
  const value: any[][] = [];
  const columnArr: string[] = [];
  if (hasDraft) {
    value.push(draftList.map(one => one[`inner_${ad}_id`]));
    columnArr.push(`inner_${adInParams}_id`);
  }
  if (hasPublished) {
    if (media === 'Google') {
      value.push(genRourceNameList(publishedList, ad));
      columnArr.push(`${adInParams}_resource_name`);
    } else if (media === 'Twitter') {
      value.push(publishedList.map(one => one[`${ad}_id`]));
      columnArr.push(`media_${adInParams}_id`);
    } else {
      value.push(publishedList.map(one => one[`${ad}_id`]));
      columnArr.push(`${adInParams}_id`);
    }
  }
  let desValue = value.length === 1 ? value[0] : value;
  if (media === 'Google') desValue = desValue.map(one => addQuotation({ str: one }));
  return [{ operator, value: desValue, column: columnArr.join(',') }];
}
export function getParamsByAdName({ media, condition }: { media: string; condition: any }) {
  if (Object.keys(condition).every(name => !condition[name] || condition[name].length === 0)) return [];
  const list: any[] = [];
  const names = ['campaign_name', 'adgroup_name', 'ad_name'];
  Object.keys(condition)
    .filter(name => names.includes(name) && condition[name] && condition[name].length > 0)
    .forEach((name) => {
      condition[name]
        .filter((x: string) => x)
        .forEach((v: string) => {
          const column = genCommonKey({ media, adStructure: name.split('_')[0], suffix: 'name' });
          list.push({ column, operator: 'like', value: [`%${v}%`] });
        });
    });
  return list;
}
export function genTTNewFilter({
  checkedObj,
  adStructure,
  isTrue = undefined,
}: {
  checkedObj: any;
  adStructure: string;
  isTrue?: boolean;
}) {
  const isTransTrue = isTrue ? isTrue : (checkedObj[adStructure] || []).length > 0;
  if (!isTransTrue) return {};
  const filter: any = {};
  const draftList = checkedObj[adStructure].filter((row: any) => isDraft(row));
  const publishedList = checkedObj[adStructure].filter((row: any) => !isDraft(row));
  if (draftList.length > 0) {
    const id = `inner_${adStructure}_id`;
    filter[`${id}s`] = draftList.map((row: any) => row[id]).filter((x: any) => x);
  }
  if (publishedList.length > 0) {
    const id = `${adStructure}_id`;
    filter[`${id}s`] = publishedList.map((row: any) => row[id]).filter((x: any) => x);
  }
  return filter;
}
/**
 * @description 不同渠道的adgroup层传递到渠道svr时使用的层级字段参数值需做转换
 */
export function getAdInMediaSvr(adStructure: string, media: string) {
  const transedAd = media === 'Facebook' ? 'ad_set' : ['Twitter', 'Google'].includes(media) ? 'ad_group' : adStructure;
  return adStructure === ADMAP.ADGROUP ? transedAd : adStructure;
}

/**
 * @description 查询后代状态统计信息需要的参数
 * @export
 * @param {{media: string, src: any[]}} { media, src }
 */
export function getStatusStatisticParam({
  game,
  media,
  adStructure,
  src,
}: {
  game: string;
  media: string;
  adStructure: ADMAP;
  src: { draft: any[]; published: any[] };
}) {
  const { draft = [], published = [] } = src;
  if (draft.length === 0 && published.length === 0) return;
  const param = {} as any;
  if (media === 'Google') {
    const dstAd = adStructure === ADMAP.ADGROUP ? 'ad_group' : adStructure;
    if (draft.length > 0) {
      param[`inner_${dstAd}_id`] = uniq(draft.map(one => one[`inner_${adStructure}_id`]).filter(x => x));
    }
    if (published.length > 0) {
      param[`${dstAd}_resource_name`] = uniq(published.map(one => getResourceName(one, adStructure)).filter(x => x));
    }
    return param;
  }
  if (media === 'Facebook' || media === 'Twitter') {
    if (draft.length > 0) {
      param.inner_ids = uniq(draft.map(one => one[`inner_${adStructure}_id`]).filter(x => x));
    }
    if (published.length > 0) {
      param.media_ids = uniq(published.map(one => one[`${adStructure}_id`]).filter(x => x));
    }
    return param;
  }
  if (media === 'TikTok') {
    param.game_code = game;
    param.media = media;
    param[`account_${adStructure}s`] = draft
      .map(one => ({
        account_id: one.account_id,
        [`inner_${adStructure}_id`]: one[`inner_${adStructure}_id`],
      }))
      .concat(published.map(one => ({
        account_id: one.account_id,
        [`${adStructure}_id`]: one[`${adStructure}_id`],
      })));
    return param;
  }
}
