import { ADMAP } from '@/views/trade/ads_management/const';
import { isDraft, isFatherOrSelf } from '@/views/trade/ads_management/utils/base';
import type { ISearchValue } from 'common/components/SearchBox/type';
import { ISort } from 'common/components/trade/ads-management/table/type';
import { SERVER_MEDIA_MAP } from 'common/const/trade';
import dayjs from 'dayjs';
import { cloneDeep, isArray } from 'lodash-es';
import { IAPIParam, IAttrObj, IBaseCondition, IColItem, IConditionItem, IExtra, IFrontValue, IOrderby } from '../type';
import { getDeliveryStatusObj, getValidAccount } from './base';
import { ADMAPS, ATTR_SELECT, COLS, DATATYPE } from './const';
import { isPreWithConversions } from './table-data';
import { MediaType } from '@/views/trade/ads_creation/common/template/type';

/**
 * @description 生成后台查询已发布数据时用的参数
 */
export function getApiParam({
  baseCondition,
  condition,
  extraCondition,
  checkedObj,
  type,
  needExtra = false,
  noNeed = false,
  needTotalNum = true,
}: {
  baseCondition: IBaseCondition;
  condition: IFrontValue;
  extraCondition?: { [key: string]: string[] };
  checkedObj?: any;
  type: DATATYPE;
  needExtra?: boolean;
  noNeed?: boolean;
  needTotalNum?: boolean;
}): IAPIParam {
  const { game = '', media: frontMedia = '', adStructure, attribute, columns } = baseCondition;
  const adForSvr = getAdForSvr({ type, attribute, adStructure });
  // 父层级勾选项全是草稿; delivery_status 只勾选了草稿相关的状态时不查询已发布数据
  const allDraftStatus =    condition.delivery_status
    && condition.delivery_status.length > 0
    && condition.delivery_status?.every((v) => {
      const [ad, type = ''] = v.split('|');
      return isFatherOrSelf({ father: ad as ADMAP, son: adStructure }) && type === 'draft';
    });
  const noNeedPublished = noNeed || isAllDraftFather(checkedObj, adStructure) || allDraftStatus;
  if (noNeedPublished) {
    return {
      type,
      game,
      media: frontMedia,
      adStructure: adForSvr,
      metricKeys: [],
      metricWhere: [],
      noNeed: noNeedPublished, // 不需要查询已发布数据
    };
  }
  const { date, sort, pageIndex, pageSize, ...attrCon } = condition; // condition 中传入了search_box字段用于做检索
  attrCon.campaign_name = getSearchBoxValue('campaign_name', condition.search_box);
  attrCon.adgroup_name = getSearchBoxValue('adgroup_name', condition.search_box);
  attrCon.ad_name = getSearchBoxValue('ad_name', condition.search_box);
  attrCon.campaign_id = getSearchBoxValue('campaign_id', condition.search_box);
  const media = (SERVER_MEDIA_MAP as any)[frontMedia]?.pivot_server || frontMedia;
  const res: any = {
    type,
    game,
    media,
    adStructure: adForSvr,
    metricKeys: getMetricKeys({ columns, adStructure, game, type, media }),
    // 假如type为date或total时且无attrObj时需在这里使用成效表中分组字段
    metricWhere: getMetricWhere({ type, date: date || [], media, game }),
    extraObj: {}, // 参数中若出现了attrObj,则一定要有extraObj，否则后台class-validator校验会报错。
    noNeed,
    needTotalNum, // 只有翻页的时候才不需要num
  };
  const filterAttrCon = genExpandAttrCon({ attrCon, media });
  // 注意：一定要有attrObj，属性表join 成效表 比 只遍历成效表 性能更好。（2S->80ms）
  const attrObj = getAttrObj({
    game,
    media,
    adStructure,
    attribute,
    attrCon: filterAttrCon,
    type,
    checkedObj,
    extraCondition: extraCondition || {},
  });
  if (JSON.stringify(attrObj) !== '{}') {
    res.attrObj = attrObj;
  }
  if (needExtra) {
    const extraObj: IExtra = {};
    [
      { frontKey: 'sort', serverKey: 'orderby' },
      { frontKey: 'pageIndex', serverKey: 'pageNum' },
      { frontKey: 'pageSize', serverKey: 'pageSize' },
    ]
      .filter(({ frontKey }) => (condition as any)[frontKey])
      .forEach(({ frontKey, serverKey }) => {
        const v = (condition as any)[frontKey];
        (extraObj as any)[serverKey] = frontKey === 'sort' ? sortToOrderby(v, type, adStructure) : v;
      });
    res.extraObj = extraObj;
  }
  return res;
}

/**
 * @description 生成后台查询参数adStructure的值
 */
export function getAdForSvr({
  type,
  attribute,
  adStructure,
}: {
  type: string;
  attribute?: ADMAP;
  adStructure: ADMAP;
}): ADMAP {
  if (type === DATATYPE.PIVOT) return adStructure;
  return attribute || ADMAP.CAMPAIGN;
}
/**
 * @description 前端condition中的key，转换成后台查询用的key，
 * 若有delivery_status时，需转换成campaign_delivery_status, adgroup_delivery_status, ad_delivery_status
 * @export
 * @param {{ media: string; attrCon: any }} { media, attrCon }
 * @returns
 */
export function genExpandAttrCon({ media, attrCon }: { media: string; attrCon: any }) {
  const filterAttrCon: any = {};
  Object.keys(attrCon)
    .filter((k) => {
      const vList = (attrCon as any)[k];
      let isTrue = COLS.FILTERS.includes(k) && vList && vList.length > 0;
      if (k.includes('delivery_status')) {
        // 任意层级有已发布数据
        const hasAndyPublished = vList.some((v: any) => {
          const [, , type = ''] = v.split('|');
          return type !== 'draft';
        });
        isTrue = hasAndyPublished;
      }
      return isTrue;
    })
    .forEach((k) => {
      if (k === 'delivery_status') {
        // 前端一个delivery_status转换成三个层级的delivery_status
        const deliveryStatusObj = getDeliveryStatusObj({ list: (attrCon as any)[k], media });
        if (JSON.stringify(getDeliveryStatusObj) !== '{}') {
          Object.keys(deliveryStatusObj).forEach((ad) => {
            (filterAttrCon as any)[`aix_${ad}_delivery_status`] = (deliveryStatusObj as any)[ad];
          });
        }
      } else {
        (filterAttrCon as any)[k] = Array.from(new Set((attrCon as any)[k]));
      }
    });
  return filterAttrCon;
}
/**
 * @description 生成后台查询的效果指标列表
 * 1、checkbox_col和name 字段均需要剔除，页面上展示的name字段取自属性表
 * 2、opt_status 和 delivery_status 两个字段需补充aix_${层级}_
 * 参考 《属性结构文档》
 * https://doc.weixin.qq.com/sheet/e3_ALUASwbdAFwG4SfbOEaR1m0JQ9q96?scode=AJEAIQdfAAoGTmU2a0ALUASwbdAFw&tab=BB08J2
 * @0609
 * coversion拆细需求，v2版本的游戏 当查询conversions时需使用 conversions_detailed_info 和 conversion_total_info
 * 且只有表格中需要补充，顶部卡片和折线图中不需要。
 */
export function getMetricKeys({
  columns,
  adStructure,
  game,
  media,
}: {
  columns: IColItem[];
  adStructure: string;
  game: string;
  type: DATATYPE;
  media: string;
}): string[] {
  let cols = columns
    .filter(({ type = 'metric', colKey }) => type === 'metric' && !COLS.NOMETRIC.includes(colKey))
    .map(({ colKey = '' }) => {
      if (colKey === 'opt_status') return `aix_${adStructure}_status`;
      return colKey;
    })
    .filter(x => x);
  // type为pivot; 查询指标中包含coversions; 测试环境所有游戏+正式环境白名单游戏 需补充 conversions_map 字段。
  if (isPreWithConversions({ cols, game, media })) {
    cols = cols.concat(['conversions_map']);
    if (media.toLocaleLowerCase() === 'facebook') {
      cols = cols.concat(((ATTR_SELECT.optimization as any)[adStructure]) || []);
    }
  }
  if (![ADMAP.CAMPAIGN, ADMAP.ADGROUP, ADMAP.AD].includes(adStructure as ADMAP)) return cols;
  return (ATTR_SELECT.fr_metric_table as any)[adStructure].concat(cols);
}

/**
 *
 * @description 生成后台查询效果指标需要的条件，若是直线图，最少要查一周的数据
 */
export function getMetricWhere({
  type,
  date,
  ...param
}: {
  type: string;
  date: dayjs.Dayjs[];
  media: string;
  game: string;
}) {
  const newData = cloneDeep(date);
  const dateList: IConditionItem[] = [
    {
      key: 'date',
      operator: '>=',
      value: dayjs(newData[0]).format('YYYY-MM-DD'),
    },
    {
      key: 'date',
      operator: '<=',
      value: dayjs(newData[1]).format('YYYY-MM-DD'),
    },
  ];
  const mediaGameWhere = [
    { serverKey: 'media', frontKey: 'media' },
    { serverKey: 'game_code', frontKey: 'game' },
  ]
    .map(({ serverKey, frontKey }) => {
      const res = filterToWhere({ key: serverKey as string, value: (param as any)[frontKey] });
      return res;
    })
    .filter(x => x);
  return dateList.concat(mediaGameWhere);
}
/**
 * @desc 表格中的展示列需区分是metrics还是attributes
 * !!! attrObj 属性逻辑：
 * 1、表格中 campaign层的aix_campaign_type是必须的字段，因此adStructure=adgroup或者ad时，也需要campaign层。
 * 2、卡片或者折线图 只需要campaign层，当搜索条件中有 adgroup_name 或者 ad_name 时才需要相应层级
 * 3、顶部筛选条件中的状态 opt_status, delivery_status 卡片或折线图中为campaign层的，表格中为adStructure层级对应的
 * ------------
 * add at0802 isPreWithConversions时FB campaign层需补充aix_optimization_events ，
 * adgroup层需补充aix_optimization_goal，aix_event_type，aix_event_name
 * @return
 */
export function getAttrObj({
  game,
  media,
  adStructure,
  attribute,
  attrCon,
  type,
  checkedObj,
  extraCondition = {},
}: {
  game: string;
  media: string;
  adStructure: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD;
  attribute?: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD;
  attrCon: IFrontValue;
  type: string;
  checkedObj: any;
  extraCondition: { [key: string]: string[] };
}): IAttrObj {
  let attrObj: IAttrObj = {};
  attrObj = getInitAttr({ type, game, media, adStructure, attribute, srcAttrObj: attrObj, checkedObj, extraCondition });
  if (attrCon.account_id) {
    if (attrObj[adStructure]?.where) {
      let where = attrObj[adStructure]?.where || [];
      where = where.filter(item => item.key !== 'account_id');
      attrObj[adStructure]!.where = where;
    }
  }
  // 当type为total或者date，attrCon为{}时，说明无属性表相关的筛选，attrObj为{}
  if (!hasAttrCon(attrCon)) return attrObj;
  Object.keys(attrCon).forEach((key) => {
    const attrAdStructure = getAttrAdStructure(type, key, media, adStructure); // filter对应的层级
    const isAdInPivot = type === DATATYPE.PIVOT && adStructure === attrAdStructure;
    // 基于ad/adgroup层的筛选查campaign时, adgroup或者ad层只需要 campaign_id
    // 基于ad层的筛选查adgroup时，ad层只需要adroup_id 即由adStructure决定
    // 基于campaign，adgroup层查adgroup时(父层级或自身) campaign层使用filter中层级决定
    const selAd = isFatherOrSelf({ father: attrAdStructure, son: adStructure }) ? attrAdStructure : adStructure;
    const select = isAdInPivot
      ? (ATTR_SELECT.pivot as any)[genMediaKeyInAttr(media, adStructure)][adStructure]
      : ATTR_SELECT.default[selAd];
    console.log('select', select);
    attrObj = getAttrItem({
      condition: attrCon,
      key,
      attrObj,
      adStructure: attrAdStructure,
      select,
    });
  });
  return attrObj;
}

export function genMediaKeyInAttr(media: string, adStructure: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD) {
  return (media === 'asa' && [ADMAP.ADGROUP].includes(adStructure))
  || (media === 'Reddit' && [ADMAP.CAMPAIGN, ADMAP.ADGROUP].includes(adStructure))
  || media === 'TikTok'
  || media === 'Google'
  || media === 'Facebook'
  || media === 'Twitter'
    ? media
    : 'default';
}

/**
 * @description 获取初始化时的attrObj字段
 * 逻辑：查campaign层级时，select 需取自 ATTR_SELECT中的pivot；
 * 在查adgroup层级时，需使用到campaign层级的数据吗？？需要用到 aix_campaign_type, account_id
 * attrObj中的层级排列需按照先adStructure层级，再其他的顺序。
 * @export
 */
export function getInitAttr({
  type,
  game,
  media,
  adStructure,
  attribute,
  srcAttrObj,
  checkedObj,
  extraCondition,
}: {
  type: string;
  game: string;
  media: string;
  adStructure: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD;
  attribute?: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD;
  srcAttrObj?: any;
  checkedObj?: any;
  extraCondition?: { [key: string]: string[] };
}) {
  let attrObj = JSON.parse(JSON.stringify(srcAttrObj));
  const where = getInitWhere({ type, game, media, extraCondition, adStructure, attribute });
  if (type === DATATYPE.PIVOT) {
    attrObj[adStructure] = {
      select: (ATTR_SELECT.pivot as any)[genMediaKeyInAttr(media, adStructure)][adStructure],
      where,
    };
    if ([ADMAP.ADGROUP, ADMAP.AD].includes(adStructure)) {
      // 查询adgroup层或ad层时，需补充campaign层,需要其中的aix_campaign_type, 且放在最高层
      attrObj[ADMAP.CAMPAIGN] = {
        select: ATTR_SELECT.pivot.campaign_by_children,
        where,
      };
      const hasAdgroupFather = checkedObj?.[ADMAP.ADGROUP] && checkedObj[ADMAP.ADGROUP].length > 0;
      if (adStructure === ADMAP.ADGROUP || (adStructure === ADMAP.AD && !hasAdgroupFather)) {
        attrObj = getAttrObjByChecked({
          // 会判断checkedObj中是否有相应层级
          checkedObj,
          media,
          adStructure: ADMAP.CAMPAIGN,
          idKey: 'campaign_id',
          attrObj,
          select: ATTR_SELECT.pivot.campaign_by_children,
        });
      } else {
        if (adStructure === ADMAP.AD) {
          attrObj = getAttrObjByChecked({
            checkedObj,
            media,
            adStructure: ADMAP.ADGROUP,
            idKey: 'adgroup_id',
            attrObj,
            select: ATTR_SELECT.default[ADMAP.ADGROUP],
          });
        }
      }
    }
  } else {
    // 汇总或分天数据时
    const toSelAd = attribute && ADMAPS.includes(attribute) ? attribute : ADMAP.CAMPAIGN;
    const selectList = (media === 'Facebook' ? ATTR_SELECT.date.Facebook : ATTR_SELECT.date.default)[toSelAd];
    attrObj[toSelAd] = {
      select: type === DATATYPE.TOTAL && media === 'Facebook'
        ? ATTR_SELECT.total.Facebook[toSelAd]
        // : ATTR_SELECT.date[toSelAd],
        : selectList,
      where,
    };
    if (checkedObj?.[adStructure]) {
      const adKey = isFatherOrSelf({ father: adStructure, son: toSelAd }) ? adStructure : toSelAd;
      const select = adStructure === toSelAd ? selectList : ATTR_SELECT.default[adKey];
      attrObj = getAttrObjByChecked({
        checkedObj,
        media,
        adStructure,
        idKey: `${adStructure}_id`,
        attrObj,
        select,
      });
    }
  }
  return attrObj;
}

function getInitWhere({
  type,
  game,
  media,
  extraCondition,
  adStructure,
  attribute,
}: {
  type: string;
  game: string;
  media: string;
  extraCondition?: { [key: string]: string[] };
  adStructure: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD;
  attribute?: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD;
}) {
  const validAccounts = getValidAccount(game, media as MediaType);
  const where = [{ key: 'game_code', operator: '=', value: game }];
  if (validAccounts.length > 0) {
    where.push({ key: 'account_id', operator: 'in', value: validAccounts as any });
  }
  const pushWhere = (key: any) => {
    if (key) {
      const keyId = `${key}_id`;
      const validKey = [ADMAP.CAMPAIGN, ADMAP.ADGROUP, ADMAP.AD].includes(key);
      const hasExtra = JSON.stringify(extraCondition) !== '{}'
        && (extraCondition as any)[keyId]
        && (extraCondition as any)[keyId].length > 0;
      if (validKey && hasExtra) {
        where.push({ key: keyId, operator: 'in', value: (extraCondition as any)[keyId] });
      }
    }
  };
  pushWhere(adStructure);
  if (type !== DATATYPE.PIVOT) {
    pushWhere(attribute);
  }
  return where;
}
/**
 *@description 生成attr属性相关的select和where组合
 * @returns
 */
function getAttrItem({
  condition,
  key,
  attrObj,
  adStructure,
  select,
}: {
  condition: IFrontValue | any;
  key: string;
  attrObj: IAttrObj | any;
  adStructure: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD;
  select: string[];
}): IAttrObj {
  const newObj = JSON.parse(JSON.stringify(attrObj));
  if (condition[key] && condition[key].length > 0) {
    if (newObj[adStructure]) {
      newObj[adStructure].where?.push(filterToWhere({ key, value: condition[key], adStructure }));
    } else {
      newObj[adStructure] = {
        select,
        where: [filterToWhere({ key, value: condition[key], adStructure })],
      };
    }
  }
  return newObj;
}
/**
 * @description 在card或者折线图中使用到的属性相关的层级
 * @param:
 * type: total, date, pivot; 全部，折线图，表格
 * key: filters中使用到的字段，campaign_name, adgroup_name, ad_name, opt_status, delivery_status, aix_locations等
 * adStructure: 当前选中的层级
 * ----------------------------------------
 * 对于opt_status, delivery_status字段，当前选中的是哪个层级，就基于哪个层级的opt_status, delivery_status查询 汇总，分天和表格数据
 */
function getAttrAdStructure(
  type: string,
  key: string,
  media: string,
  adStructure: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD,
): ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD {
  if (['aix_ad_delivery_status', 'ad_name'].includes(key)) return ADMAP.AD;
  if (['aix_adgroup_delivery_status', 'adgroup_name'].includes(key) || key === COLS.COUNTRY) return ADMAP.ADGROUP;
  if (COLS.STATUS.includes(key)) return adStructure;
  return ADMAP.CAMPAIGN;
}

/**
 * @description 查询adgroup或ad层表格数据时，若祖先层级表格中有被勾选，需将其作为过滤条件使用
 * @param {*} { checkedObj, adStructure, idKey, attrObj }
 * @returns
 */
function getAttrObjByChecked({
  checkedObj,
  adStructure,
  idKey,
  attrObj,
  select,
}: {
  checkedObj: any;
  media: string;
  adStructure: ADMAP.CAMPAIGN | ADMAP.ADGROUP | ADMAP.AD;
  idKey: string;
  attrObj: IAttrObj | any;
  select: string[];
}) {
  let newAttrObj = JSON.parse(JSON.stringify(attrObj));
  if (!checkedObj) return newAttrObj;
  if (adStructure in checkedObj && checkedObj[adStructure].length > 0) {
    const value = checkedObj[adStructure].filter((x: any) => !isDraft(x)).map((one: any) => one[idKey]);
    newAttrObj = getAttrItem({
      condition: { [idKey]: value },
      key: idKey,
      attrObj,
      adStructure,
      select,
    });
  }
  return newAttrObj;
}

/**
 * @description 前端筛选器中的值转换成后台查询用的参数
 */
export function filterToWhere({
  key,
  value: srcV,
  adStructure,
}: {
  key: string;
  value: (number | string)[];
  adStructure?: string;
  media?: string;
}) {
  let value = srcV;
  if (Array.isArray(srcV)) {
    value = srcV.join(',').split(',');
  }
  if (COLS.COUNTRY === key) {
    return { key, operator: 'has_any', value };
  }
  if (COLS.NAMES.includes(key)) {
    return { key, operator: 'like_or', value: value.map(v => `%${v}%`) };
  }
  if (key === 'opt_status') {
    return { key: `aix_${adStructure}_status`, operator: 'in', value };
  }
  if (COLS.BASE.includes(key)) {
    return { key, operator: '=', value };
  }
  return { key, operator: 'in', value };
}

export function ordersToSort(orderby: IOrderby[]) {
  if (!orderby || orderby.length === 0) return { sort: 'spend', descending: true };
  return orderby
    .filter(({ from = 'front' }) => from === 'front')
    .map(({ key, value = '' }) => ({ sortBy: key, descending: value === 'desc' }))[0];
}

/**
 * @description pivot页面中排序除了metric之外，需补上一个创建时间,以保证metric相同下campaign等不会随机展示
 */
export function sortToOrderby(sort: ISort, type: string, adStructure: string): IOrderby[] {
  const { sortBy, descending, isInit = false } = sort;
  const key = sortBy === 'delivery_status' ? `aix_${adStructure}_${sortBy}` : sortBy;
  let orderbyList = [{ key, value: descending ? 'desc' : 'asc', from: 'front' }];
  if (type === DATATYPE.PIVOT) {
    if (isInit) {
      orderbyList = [{ key: `aix_${adStructure}_delivery_status_d`, value: 'desc', from: 'backend' }, ...orderbyList];
    }
    orderbyList.push({ key: `aix_${adStructure}_create_time`, value: 'desc', from: 'backend' });
    orderbyList.push({ key: `${adStructure}_id`, value: 'desc', from: 'backend' });
  }
  return orderbyList;
}

/**
 * @description 从前端searchBox中解析出campaign_name, adgroup_name,ad_name等值
 */
export function getSearchBoxValue(field: string, searchBox?: ISearchValue[]): string[] {
  if (!isArray(searchBox)) {
    return [];
  }
  let { condition = [] } = searchBox.find(item => item.field === field) ?? {};
  condition = condition.filter(val => !!val); // 过滤掉空值
  return condition;
}

/**
 * @description 若父层级勾选项均是草稿时，noNeed=true,表示不需要查询已发布数据
 */
function isAllDraftFather(checkedObj: any, adStructure: string): boolean {
  if (!checkedObj) return false;
  const allCampaign =    ADMAP.CAMPAIGN in checkedObj
    && checkedObj[ADMAP.CAMPAIGN].length > 0
    && checkedObj[ADMAP.CAMPAIGN].every((row: any) => isDraft(row));
  if (adStructure === ADMAP.ADGROUP || (adStructure === ADMAP.AD && allCampaign)) {
    return allCampaign;
  }
  if (adStructure === ADMAP.AD) {
    if (!(ADMAP.ADGROUP in checkedObj)) return false;
    const allAdgroup =      ADMAP.ADGROUP in checkedObj
      && checkedObj[ADMAP.ADGROUP].length > 0
      && checkedObj[ADMAP.ADGROUP].every((row: any) => isDraft(row));
    return allAdgroup;
  }
  return false;
}

export function getNewCon({ noNeedSort = false, condition }: { noNeedSort: boolean; condition: any }) {
  if (noNeedSort) {
    return { ...condition.cur, sort: undefined, pageSize: undefined, pageIndex: undefined };
  }
  return condition.cur;
}

export function hasAttrCon(attrCon: any) {
  const res = Object.keys(attrCon)
    .filter(key => COLS.FILTERS_FOR_SVR.includes(key))
    .some(key => attrCon[key].length > 0);
  return res;
}
