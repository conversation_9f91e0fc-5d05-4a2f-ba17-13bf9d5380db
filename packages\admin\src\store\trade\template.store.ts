/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/naming-convention */
import { reactive, ref, watch, watchEffect } from 'vue';
import type { MEDIA } from '@@/index';
import { get } from '@vueuse/core';
import { defineStore, storeToRefs } from 'pinia';
import dayjs from 'dayjs';
import type {
  BasicInfoType, TargetingTemplateItem, TreeCurrent, TreeNode,
} from '@/views/trade/ads_creation/common/template/type';
import type { SubChannel } from '@/views/trade/ads_creation/common/template/config';
import { Level, MediaReqMap } from '@/views/trade/ads_creation/common/template/config';
import type { Router } from 'vue-router';
import {
  execNode, getTreeNode, isNodePublished, isNodeTemp,
} from '@/views/trade/ads_creation/common/template/utils-common';
import { cloneDeep } from 'lodash-es';
import { MessagePlugin, StatusEnum } from 'tdesign-vue-next';
import { filterCompleteDraftNode, getKeysByConfig, modifyTreeNodeId, routerGo, setInfoIntoCache } from './util';
import { STORE_KEY } from '@/config/config';
import { basicRequestConfig, storeConfig } from './config';
import { getQueryString } from 'common/utils/url';
import { EBusEmit } from '@/views/trade/ads_creation/common/template/event';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useNode } from './template/operate';

export const useTreeListDataStore = defineStore(STORE_KEY.TD.TEMPLATE.NAME, () => {
  const channelType = ref<MEDIA>();
  const { gameCode } = storeToRefs(useGlobalGameStore());

  const current = reactive<TreeCurrent>({
    level: 0,
    id: '',
    campaignNode: undefined,
    adgroupNode: undefined,
    adNode: undefined,
  });
  const basicInfo: BasicInfoType = reactive({
    operationType: '',
    gameCode,
    media: '',
    accountList: [],
    accountId: getQueryString('account_id') || '',
    cacheStatus: getQueryString('cache_status') || '',
    isInitFinish: false, // 是否初始化完成
  });

  // campaign的account_id切换，需要同步更新basicInfo
  watchEffect(() => {
    if (current.campaignNode?.data.account_id) basicInfo.accountId = current.campaignNode?.data.account_id;
  });

  // 定义两个变量，以后各个操作都要用到
  const store = ref();
  const channelConfig = ref<SubChannel>(basicRequestConfig[channelType.value as MEDIA]);
  const router = ref();

  const campaignNumberInfo = ref();
  // useSessionStorage，初始化写入到sessionStorage,后续刷新，如果sessionStorage有值，则自动获取初始化。
  const treeList = setInfoIntoCache([], 'cacheRoot');
  const initTreeList = setInfoIntoCache([], 'initCacheRoot');

  const publishStatusInfo = reactive<{
    isShow: boolean,
    publishError: number[],
    publishingNumber: number,
    finishAdsNumber: number,
    publishAdsNumber: number,
    percentage: number,
    publishStatus: StatusEnum,
  }>({
    isShow: false,
    publishError: [0, 0, 0],
    publishingNumber: 0,
    finishAdsNumber: 0,
    publishAdsNumber: 0,
    percentage: 0,
    publishStatus: 'active',
  });
  // 如果没有cacheStatus标识，则清空
  if (!basicInfo.cacheStatus) {
    treeList.value = [];
    initTreeList.value = [];
  }

  // 监听当前ad node变化，同步修改url参数
  watch(() => current.adNode, () => {
    if (!current.campaignNode) return;
    const campaignData = current.campaignNode.data;
    const adgroupData = current.adgroupNode.data;
    const adData = current.adNode.data;
    const config = channelConfig.value.keyConfig;
    routerGo(get(router), {
      account_id: campaignData.account_id,
      inner_campaign_id: campaignData[config.inner_campaign_id],
      inner_adgroup_id: adgroupData[config.inner_adgroup_id],
      inner_ad_id: adData[config.inner_ad_id],
    }, [], 'replace');
  });

  // 根据当前参数更新campaign number
  const updateCampaignNumber = async () => {
    const { _id: id, number } = campaignNumberInfo.value;
    await (basicRequestConfig.base as any)
      .updateCampaignNameNumber({ _id: id, number });
    campaignNumberInfo.value.number += 1;
    get(store).basicInit(basicInfo, get(campaignNumberInfo));
  };
  const getCampaignNumber = async () => {
    campaignNumberInfo.value = await (basicRequestConfig.base as any)
      .getCampaignNameNumber({
        account_id: basicInfo.accountId,
        // campaign_type: '-',
        media: basicInfo.media,
        __game: basicInfo.gameCode,
        date: dayjs().format('YYMMDD'),
      });
    get(store).basicInit(basicInfo, get(campaignNumberInfo));
  };

  const init = async (initRouter: Router): Promise<boolean> => {
    execNode(treeList.value, (node: TreeNode) => node.oldId = ''); // 初始化移除oldId，解决tree定位问题
    router.value = initRouter;

    // 每次init，重新设置状态
    basicInfo.operationType = getQueryString('operation_type') || 'add';
    basicInfo.cacheStatus = getQueryString('cache_status') || '';
    const noCache = !!basicInfo.cacheStatus;
    const newLevel = getQueryString('new_level') || '';

    // 请求两个基本值, 获取当前账户信息
    const accountInfo = await basicRequestConfig.base.getAccountInfo({
      game_code: basicInfo.gameCode,
      media: MediaReqMap[basicInfo.media],
    });
    basicInfo.accountList = accountInfo.accounts || [];
    if (basicInfo.operationType === 'add') {
      basicInfo.accountId = basicInfo.accountList[0].account_id;
    }
    // 获取当前账户的campaignNumber
    await getCampaignNumber();
    if (!basicInfo.cacheStatus) {
      if (basicInfo.operationType !== 'add') {
        // 属于编辑类型要处理一下参数通过id 拿到具体的参数
        const paramsDatas = await basicRequestConfig.base.getParamsById(getQueryString('code'), basicInfo.gameCode);
        console.log('链接上参数解析结果：', paramsDatas);
        treeList.value = await get(store).init(paramsDatas);
        const {
          inner_campaign_id, media_campaign_id, inner_adgroup_id,
          media_adgroup_id, inner_ad_id, media_ad_id, account_id,
        } = paramsDatas[0];
        basicInfo.accountId = account_id; // 需要从url参数中获取account_id
        await routerGo(get(router), {
          inner_campaign_id, media_campaign_id, inner_adgroup_id,
          media_adgroup_id, inner_ad_id, media_ad_id, account_id,
        }, ['code', 'new_level'], 'replace');
      } else {
        treeList.value = await get(store).init();
      }
      initTreeList.value = cloneDeep(treeList.value); // 同步一份到缓存
      basicInfo.cacheStatus = 'session_storage';
      await routerGo(get(router), { cache_status: 'session_storage' }, [], 'replace');
    }
    if (treeList.value.length === 0) {
      return false;
    }

    let currentNode;
    let adgroupNode;
    let adNode;
    // 操作为add，且没有缓存的场景下，全部使用树的第0个节点
    if (basicInfo.operationType === 'add' && noCache) {
      [currentNode] = treeList.value;
      [adgroupNode] = currentNode.children;
      [adNode] = adgroupNode.children;
    } else {
      const campaignId = getQueryString('inner_campaign_id')
        ? `0-${getQueryString('inner_campaign_id')}` : getQueryString('media_campaign_id');
      // 通过nodeId获取或者media_id获取，campaign层一定会有数据
      currentNode = getTreeNode(treeList.value, campaignId, channelConfig.value) as TreeNode;

      // 从url获取adgroup参数，没有则从currentNode中获取第0个
      const adgroupId = getQueryString('inner_adgroup_id')
        ? `1-${getQueryString('inner_adgroup_id')}` : getQueryString('media_adgroup_id');
      if (adgroupId) adgroupNode = getTreeNode(treeList.value, adgroupId, channelConfig.value);
      else adgroupNode = currentNode.children![0] as TreeNode;

      // 从url获取ad参数，没有则从adgroupNode中获取第0个
      const adId = getQueryString('inner_ad_id')
        ? `2-${getQueryString('inner_ad_id')}` : getQueryString('media_ad_id');
      if (adId) adNode = getTreeNode(treeList.value, adId, channelConfig.value);
      else adNode = adgroupNode!.children![0] as TreeNode;
    }
    Object.assign(current, {
      level: 0, id: currentNode.id, campaignNode: currentNode, adgroupNode, adNode,
    });
    basicInfo.isInitFinish = true;
    EBusEmit('initNavTree', treeList.value);
    console.log('treeList', treeList.value);

    // 从pivot页面新增节点跳转的场景，非临时几点才需要添加
    if (newLevel === 'adgroup' && !isNodeTemp(Level.AdgroupLevel, adgroupNode.data, channelConfig.value)) {
      addNode(Level.AdgroupLevel);
    }
    if (newLevel === 'ad' && !isNodeTemp(Level.AdLevel, adNode.data, channelConfig.value)) {
      addNode(Level.AdLevel);
    }
    return true;
  };

  // 清楚树结构数据，同步清除sessionStorage
  const clear = () => {
    treeList.value = [];
    initTreeList.value = [];
  };

  const changeChannelType = (newChannelType: MEDIA) => {
    basicInfo.media = newChannelType;
    channelType.value = newChannelType;
    channelConfig.value = basicRequestConfig[newChannelType];
    store.value = (storeConfig[newChannelType] || storeConfig.default)();
    return channelConfig.value;
  };

  // 修改当前状态 用于树节点切换 tab切换
  const updateCurrent = (id: string) => {
    const newNode = getTreeNode(treeList.value, id, channelConfig.value);
    if (!newNode) return;

    // 通过当前层级，插件其父层级节点
    let { campaignNode, adgroupNode, adNode } = current;
    if (newNode.level === Level.CampaignLevel) {
      campaignNode = newNode;
      [adgroupNode] = newNode.children as [TreeNode];
      [adNode] = adgroupNode.children as [TreeNode];
    } else if (newNode.level === Level.AdgroupLevel) {
      adgroupNode = newNode;
      campaignNode = adgroupNode.parentNode as TreeNode;
      [adNode] = newNode.children as [TreeNode];
    } else if (newNode.level === Level.AdLevel) {
      adgroupNode = newNode.parentNode as TreeNode;
      campaignNode = adgroupNode.parentNode as TreeNode;
      adNode = newNode;
    }

    Object.assign(current, {
      level: newNode.level,
      id: newNode.id,
      campaignNode,
      adgroupNode,
      adNode,
    });
  };

  const { addNode, copyNode, deleteNode, saveCampaign, saveAdgroupNode, saveAdNode, initTreeFromLevel } = useNode({
    current, treeList, initTreeList, store, channelConfig, basicInfo, router, campaignNumberInfo,
    updateCurrent, updateCampaignNumber,
  });


  // eslint-disable-next-line max-len
  const saveCondition = (campaignNode: TreeNode, adgroupNode: TreeNode, adNode: TreeNode, important: boolean, needTip: boolean) => {
    // 增加自定义保存校验条件
    const canSave: boolean[] = get(store).saveCondition(
      campaignNode.data, adgroupNode.data,
      adNode.data, important, needTip,
    );
    // const validatorPass = [true, true, true];
    // if (campaignNode.data[mediaCampaignKey] && campaignNode.status === -1) {
    //   validatorPass[0] = false;
    // }
    // if (adgroupNode.data[mediaAdgroupKey] && adgroupNode.status === -1) {
    //   validatorPass[1] = false;
    // }
    // if (adNode.data[mediaAdKey] && adNode.status === -1) {
    //   validatorPass[2] = false;
    // }
    // if (validatorPass.includes(false)) {
    //   MessagePlugin.error('There are items in the form that fail verification');
    // }
    return { canSave };
  };

  // 保存草稿状态下的节点
  const saveNodes = async (
    important = false,
    importantLevel = Level.CampaignLevel,
    needTip = true, // 保存提示
    needNoneTip = true, // 没有变更项提示
  ) => {
    const { campaignNode, adgroupNode, adNode } = current;  // 获取当前节点的数据内容
    const {
      innerCampaignKey, mediaCampaignKey, statusKey,
      innerAdgroupKey, mediaAdgroupKey, innerAdKey, mediaAdKey,
    } = getKeysByConfig(channelConfig.value);
    // 这里增加保存的前置条件
    const { canSave } = saveCondition(
      campaignNode, adgroupNode, adNode,
      important, needTip,
    );
    if (canSave.every(item => !item)) {
      return false;
    }

    const onePublished = isNodePublished(Level.CampaignLevel, campaignNode.data, channelConfig.value)
      || isNodePublished(Level.AdgroupLevel, adgroupNode.data, channelConfig.value)
      || isNodePublished(Level.AdLevel, adNode.data, channelConfig.value);

    let initCampaignNode = getTreeNode(initTreeList.value, campaignNode?.id || '', channelConfig.value);
    const isTreeNodeChanges = [false, false, false];
    const successMsg: string[] = [];
    if (canSave[0]) {
      const flag = await saveCampaign(
        initCampaignNode, campaignNode, isTreeNodeChanges, innerCampaignKey,
        mediaCampaignKey, successMsg, statusKey.campaign, important && importantLevel === Level.AdgroupLevel,
      );
      if (!flag) return false;
    }
    let initAdgroupNode = getTreeNode(initTreeList.value, adgroupNode?.id || '', channelConfig.value);
    initCampaignNode = getTreeNode(initTreeList.value, campaignNode?.id || '', channelConfig.value);  // 重新获取1次
    if (canSave[1]) {
      const flagAdgroup = await saveAdgroupNode(
        campaignNode, initCampaignNode, initAdgroupNode, adgroupNode,
        isTreeNodeChanges, mediaCampaignKey, innerCampaignKey,
        mediaAdgroupKey, innerAdgroupKey, successMsg, statusKey.adgroup, important && importantLevel === Level.AdLevel,
      );
      if (!flagAdgroup) return false;
    }
    initAdgroupNode = getTreeNode(initTreeList.value, adgroupNode?.id || '', channelConfig.value); // 重新获取1次
    const initAdNode = getTreeNode(initTreeList.value, adNode?.id || '', channelConfig.value);
    if (canSave[2]) {
      const flagAd = await saveAdNode(
        campaignNode, initAdNode, adNode, initAdgroupNode, adgroupNode, isTreeNodeChanges,
        mediaCampaignKey, innerCampaignKey, mediaAdgroupKey, innerAdgroupKey,
        mediaAdKey, innerAdKey, successMsg, statusKey.ad, canSave[2],
      );
      if (!flagAd) return false;
    }
    saveTip(isTreeNodeChanges, successMsg, onePublished, needTip, needNoneTip);
    return true;
  };

  // 保存完成后的提示
  const saveTip = (
    isTreeNodeChanges: boolean[],
    successMsg: string[],
    published = false,
    needTip = false,
    needNoneTip = false,
  ) => {
    if (!isTreeNodeChanges[0] && !isTreeNodeChanges[1] && !isTreeNodeChanges[2]) {
      // 没有变更项提示
      needNoneTip && MessagePlugin.info('There is no content change');
    } else if (successMsg.length > 0) {
      // 保存成功提示
      needTip && MessagePlugin.success(`${published ? 'Update' : 'Save'} ${successMsg.join('/')} Success!`);
    }
  };

  // 这里获取一下能发布的节点
  const getCanPublishTreeNumber = () => filterCompleteDraftNode(treeList.value, channelConfig.value);

  // 发布节点
  const publishAds = async () => {
    // 模板这里只提供整棵树的信息
    // 如果没有能发布节点直接返回
    const publishTreeList = getCanPublishTreeNumber();
    if (!publishTreeList.length) {
      console.log('没有可以发布的节点');
      return false;
    }
    // 复位状态
    publishStatusInfo.publishError = [0, 0, 0];
    publishStatusInfo.publishingNumber = 0;
    publishStatusInfo.publishAdsNumber = 0;
    publishStatusInfo.finishAdsNumber = 0;
    publishStatusInfo.percentage = 0;
    publishStatusInfo.publishStatus = 'active';
    publishStatusInfo.isShow = true;

    // 定义一个临时变量用于记录错误的数量
    let publishErrorNumber = 0;
    publishStatusInfo.publishAdsNumber = publishTreeList.length;
    // 如果是1， 则要开启一个0 - 100的倒计时
    let publishInterval: any = undefined;
    if (publishStatusInfo.publishAdsNumber === 1) {
      publishInterval = setInterval(() => {
        if (publishStatusInfo.percentage < 90) {
          publishStatusInfo.percentage += 10;
        }
        if (publishStatusInfo.finishAdsNumber === 1) {
          clearInterval(publishInterval);
        }
      }, 1000);
    }
    // 下面开始循环发送发布请求
    for (const treeNode of publishTreeList) {
      const reqRes = await get(store).publishAds(treeNode);
      const {
        result: { error_code: errorCode, error_message: errorMsg },
      } = reqRes as any;
      const {
        inner_ad_id: innerAdId, inner_adgroup_id: innerAdgroupId, inner_campaign_id: innerCampaignId,
      } = channelConfig.value.keyConfig;
      reqRes[innerAdId] = treeNode.data[innerAdId];
      reqRes[innerAdgroupId] = treeNode.parentNode?.data?.[innerAdgroupId];
      reqRes[innerCampaignId] = treeNode.parentNode?.parentNode?.data?.[innerCampaignId];
      publishStatusInfo.finishAdsNumber += 1;
      // 1.发布失败情况
      if (![0, 8000023, 1003002].includes(errorCode)) {
        publishErrorNumber += 1;
        modifyTreeNodeId(
          channelConfig.value, treeNode, reqRes, current.adNode, get(router),
          false, errorMsg, publishStatusInfo.publishError, get(store).copeErrorMsg,
        );
      } else if ([8000023, 1003002].includes(errorCode)) {
        // 延迟发布情况
        publishStatusInfo.publishingNumber += 1;
        modifyTreeNodeId(
          channelConfig.value, treeNode, reqRes,
          current.adNode, get(router), true,
        );
      } else {
        // 发布成功情况
        modifyTreeNodeId(channelConfig.value, treeNode, reqRes, current.adNode, get(router));
      }
      if (publishStatusInfo.publishAdsNumber !== 1) {
        publishStatusInfo.percentage = parseInt(`${(publishStatusInfo.finishAdsNumber / publishStatusInfo.publishAdsNumber) * 100}`, 10);
      } else {
        publishStatusInfo.percentage = 100;
      }
    }
    // 全部发出请求后, 设置进度条的状态
    publishStatusInfo.publishStatus = 'success';
    if (publishStatusInfo.publishingNumber) {
      publishStatusInfo.publishStatus = 'active';
    }
    if (publishErrorNumber) {
      publishStatusInfo.publishStatus = 'error';
    }
  };

  // 清除后台错误
  const clearBackend = () => {
    execNode(treeList.value, (node: TreeNode) => {
      node.data[channelConfig.value.keyConfig.errorKey] = '';
    });
  };

  // 重新同步初始化缓存树
  const syncInitTree = () => {
    initTreeList.value = cloneDeep(treeList.value);
  };
  // 模板相关的三个接口 begin
  const getTemplateList = async () => await basicRequestConfig.base
    .getTemplates(basicInfo.gameCode, MediaReqMap[basicInfo.media]);
  const addOrUpdateTemplate = async (templateItem: TargetingTemplateItem) => await basicRequestConfig.base
    .addOrUpdateTemplate(basicInfo.gameCode, MediaReqMap[basicInfo.media], 'TARGETING', templateItem);
  const deleteTemplate = async (templateIds: string[]) => await basicRequestConfig.base.deleteTemplate(basicInfo.gameCode, MediaReqMap[basicInfo.media], 'TARGETING', templateIds);
  // 模板相关的三个接口 end

  return {
    basicInfo, channelType, treeList, initTreeList, current, publishStatusInfo, channelConfig,
    init, clear, addNode, copyNode, deleteNode, getTreeNode, updateCurrent, changeChannelType,
    saveNodes, publishAds, getCanPublishTreeNumber, clearBackend, initTreeFromLevel, syncInitTree,
    getTemplateList, addOrUpdateTemplate, deleteTemplate,
  };
});
