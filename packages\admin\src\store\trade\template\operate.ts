/* eslint-disable no-param-reassign */
import { cloneDeep } from 'lodash-es';
import { Level } from '@/views/trade/ads_creation/common/template/config';
import { CopyParams, TreeNode } from '@/views/trade/ads_creation/common/template/type';
import { EBusEmit } from '@/views/trade/ads_creation/common/template/event';
import { get } from '@vueuse/core';
import {
  compareNodeData,
  isNodeDraft,
  isNodePublished,
  isNodeTemp,
} from '@/views/trade/ads_creation/common/template/utils-common';
import { changeChildrenStatus, routerGo, setSomeNodeInCache } from '@/store/trade/util';
import { MessagePlugin } from 'tdesign-vue-next';

export function useNode(params: any) {
  const {
    current, treeList, initTreeList, store, channelConfig, router,
    updateCurrent, updateCampaignNumber,
  } = params;

  /**
   * 增加新节点
   * @params
   * level：层级
   * targetNode: 目标节点
   */
  const addNode = async (level: Level, targetNode?: TreeNode) => {
    let newNode;
    if (targetNode) {
      newNode = targetNode; // 复制节点的场景
    } else {
      // 先计算新增节点的number
      let num = 0;
      if (level === Level.CampaignLevel) {
        // 直接使用campaignNumberInfo.number
        await updateCampaignNumber();
      } else if (level === Level.AdgroupLevel) {
        num = current.campaignNode.children.length + 1;
      } else if (level === Level.AdLevel) {
        num = current.adgroupNode.children.length + 1;
      }
      newNode = get(store).addNode(params.current, level, num);
      if (!newNode) return;
    }
    if (level === Level.CampaignLevel) {
      get(params.treeList).push(newNode);
    } else if (level === Level.AdgroupLevel) {
      newNode.parentNode = params.current.campaignNode;
      current.campaignNode.children.push(newNode);
    } else if (level === Level.AdLevel) {
      newNode.parentNode = params.current.adgroupNode;
      current.adgroupNode.children.push(newNode);
    }
    initTreeList.value = cloneDeep(get(treeList)); // 同步initTree
    updateCurrent((newNode as TreeNode).id); // 切换当前节点为新的节点
    EBusEmit('treeUpdate', { type: 'add', node: newNode });
  };

  // 复制节点
  const copyNode = async (data: CopyParams) => {
    await get(params.store).copyNode(data);
  };

  /**
   * 删除节点
   * 判断当前的adgroup层级的ad数量：
   * if ad > 1：
   *   删除当前ad，切换到上一个ad
   * if ad = 1：
   *   判断当前adgroup是否发布：
   *   if adgroup.published:
   *     删除ad，补充一个空白的ad
   *   else:
   *     判断当前campaign层级的adgroup数量
   *     if adgroupNum > 1：
   *       删除当前adgroup，切换到上一个adgroup
   *     if adgroupNum = 1：
   *       判断当前campaign是否发布
   *       if campagin.published:
   *         删除adgroup，补充空白adgroup
   *       else:
   *         判断当前campaign层级的数量
   *         if tree.length > 1：
   *           删除campaign后，切换到上一个campaign
   *         else:
   *           删除campaign后，回到pivot
   */
  const deleteNode = async (): Promise<{ error_code: number, error_message: string, go_back: boolean }> => {
    const adLen = current.adgroupNode.children.length; // 当前的adgroup层级的ad数量
    let result = { error_code: 0, error_message: '', go_back: false }; // 删除结果回传
    if (adLen > 1) {
      const adData = current.adNode.data;
      const adIndex = current.adgroupNode.children.findIndex((item: TreeNode) => item.id === current.adNode.id);
      if (isNodeDraft(Level.AdLevel, adData, get(channelConfig))) { // 草稿节点调用后台删除接口
        result = (await get(store).deleteNode(adData.inner_ad_id, Level.AdLevel)).result;
      }
      if (result.error_code) return result;
      current.adgroupNode.children.splice(adIndex, 1); // 删除ad节点
      const nextIndex = adIndex - 1 < 0 ? 0 : adIndex - 1; // 下一个节点index
      const nextNode = current.adgroupNode.children[nextIndex]; // 下一个节点
      EBusEmit('treeUpdate', { type: 'delete', node: current.adNode });
      updateCurrent(nextNode.id);
    } else {
      const adgroupPublished = isNodePublished(Level.AdgroupLevel, current.adgroupNode.data, get(channelConfig));
      if (adgroupPublished) { // 删除ad，补充一个空白的ad
        const adData = current.adNode.data;
        if (isNodeDraft(Level.AdLevel, adData, get(channelConfig))) { // 草稿节点调用后台删除接口
          result = (await get(store).deleteNode(adData.inner_ad_id, Level.AdLevel)).result;
        }
        if (result.error_code) return result;
        current.adgroupNode.children.splice(0, 1); // 删除ad节点
        const newId = completeTreeList(); // 补全treeList
        updateCurrent(newId);
      } else {
        const adgroupLen = current.campaignNode.children.length; // 当前campaign层级的adgroup数量
        if (adgroupLen > 1) {
          const adgroupData = current.adgroupNode.data;
          if (isNodeDraft(Level.AdgroupLevel, adgroupData, get(channelConfig))) { // 调用后台删除adgroup层接口
            const innerAdgroup = adgroupData.inner_adgroup_id
              || adgroupData.inner_ad_group_id
              || adgroupData.inner_adset_id;
            result = (await get(store).deleteNode(innerAdgroup, Level.AdgroupLevel)).result;
          }
          const adgroupIndex = current.campaignNode.children
            .findIndex((item: TreeNode) => item.id === current.adgroupNode.id);
          current.campaignNode.children.splice(adgroupIndex, 1); // 删除adgroup节点
          const nextIndex = adgroupIndex - 1 < 0 ? 0 : adgroupIndex - 1; // 下一个节点index
          const nextNode = current.campaignNode.children[nextIndex]; // 下一个节点
          EBusEmit('treeUpdate', { type: 'delete', node: current.adgroupNode });
          updateCurrent(nextNode.id);
        } else {
          const campaignData = current.campaignNode.data;
          const campaignPublished = isNodePublished(Level.CampaignLevel, campaignData, get(channelConfig));
          if (campaignPublished) {
            const adgroupData = current.adgroupNode.data;
            if (isNodeDraft(Level.AdgroupLevel, adgroupData, get(channelConfig))) { // 调用后台删除adgroup层接口
              const innerAdgroup = adgroupData.inner_adgroup_id
                || adgroupData.inner_ad_group_id
                || adgroupData.inner_adset_id;
              result = (await get(store).deleteNode(innerAdgroup, Level.AdgroupLevel)).result;
            }
            if (result.error_code) return result;
            current.campaignNode.children.splice(0, 1); // 删除adgroup节点
            const newId = completeTreeList(); // 补全treeList
            updateCurrent(newId);
          } else {
            if (isNodeDraft(Level.CampaignLevel, campaignData, get(channelConfig))) { // 调用后台删除campaign层接口
              result = (await get(store).deleteNode(campaignData.inner_campaign_id, Level.CampaignLevel)).result;
            }
            const campaignIndex = treeList.value.findIndex((item: TreeNode) => item.id === current.campaignNode.id);
            treeList.value.splice(campaignIndex, 1); // 删除campaign节点
            if (treeList.value.length > 0) {
              const nextIndex = campaignIndex - 1 < 0 ? 0 : campaignIndex - 1; // 下一个节点index
              const nextNode = treeList.value[nextIndex]; // 下一个节点
              EBusEmit('treeUpdate', { type: 'delete', node: current.campaignNode });
              updateCurrent(nextNode.id);
            } else {
              result.go_back = true; // 删除最后一个节点，需要跳转到pivot页面
            }
          }
        }
      }
    }
    initTreeList.value = cloneDeep(treeList.value);
    return result;
  };

  // 保存campaign节点
  const saveCampaign = async (
    initCampaignNode: TreeNode | null,
    campaignNode: TreeNode,
    isTreeNodeChanges: boolean[],
    innerCampaignKey: string,
    mediaCampaignKey: string,
    successMsg: string[],
    statusKey: string,
    important: boolean, // 是否有关键字段变更
  ) => {
    // 状态值这块还要处理 根据校验处理
    if (channelConfig.value.isInDraftStatus(campaignNode.data[statusKey])) {
      changeChildrenStatus(campaignNode, channelConfig.value);
      campaignNode.data[statusKey] = campaignNode.status === 1
        ? channelConfig.value.statusDraftCompleted : channelConfig.value.statusDraftInCompleted;
    }
    const published = isNodePublished(Level.CampaignLevel, campaignNode.data, channelConfig.value);

    // 比较不同或者临时状态，都保存
    if (!compareNodeData(Level.CampaignLevel, campaignNode.data, initCampaignNode?.data)
      || isNodeTemp(Level.CampaignLevel, campaignNode.data, channelConfig.value)) {
      isTreeNodeChanges[0] = true;
      const result = await get(store).saveCampaignNode(campaignNode, important);
      if (result.result.error_code) {
        MessagePlugin.error(result.result.error_message);
        return false;
      }
      // 发布后的，补上mediaId、inner_id
      if (published) {
        result[mediaCampaignKey] = campaignNode.data[mediaCampaignKey];
        result[innerCampaignKey] = campaignNode.data[innerCampaignKey];
      }
      const { [innerCampaignKey]: innerCampaignId, [mediaCampaignKey]: mediaCampaignId } = result as any;
      if (innerCampaignId || mediaCampaignId) {
        const isTempNode = isNodeTemp(Level.CampaignLevel, campaignNode.data, channelConfig.value);
        if (isTempNode) {
          await updateCampaignNumber();  // 更新campaignNumber
        }
        campaignNode.data[mediaCampaignKey] = mediaCampaignId;
        campaignNode.data[innerCampaignKey] = innerCampaignId;
        campaignNode.data[innerCampaignKey] = innerCampaignId;
        isTempNode && (campaignNode.oldId = campaignNode.id);
        // 保存草稿才更新为inner_id，发布后的不需要更新
        if (innerCampaignId) campaignNode.id = `0-${innerCampaignId}`;

        // 重要字段变更，增加保存成功提示，不进行后续逻辑
        if (important) {
          await get(store).refreshTree();
          updateCurrent(campaignNode.id);
          MessagePlugin.success('save Campaign Success!');
          return false;
        }

        routerGo(get(router), {
          inner_campaign_id: innerCampaignId, media_campaign_id: mediaCampaignId,
        }, [], 'replace');
        successMsg.push('Campaign');
        // 保存完成后保存一份到缓存中
        if (initCampaignNode) {
          initCampaignNode.id = campaignNode.id;
          initCampaignNode.data = cloneDeep(campaignNode.data);
        } else {
          const cloneCampaignNode = cloneDeep(campaignNode);
          cloneCampaignNode.children = [];
          initTreeList.value.push(cloneCampaignNode);
        }
        return true;
      }
      return false;
    }
    return true;
  };

  // 保存adgroup节点
  const saveAdgroupNode = async (
    campaignNode: TreeNode,
    initCampaignNode: TreeNode | null,
    initAdgroupNode: TreeNode | null, adgroupNode: TreeNode,
    isTreeNodeChanges: boolean[],
    mediaCampaignKey: string,
    innerCampaignKey: string,
    mediaAdgroupKey: string,
    innerAdgroupKey: string,
    successMsg: string[],
    statusKey: string,
    important: boolean, // 是否有关键字段变更
  ) => {
    if (channelConfig.value.isInDraftStatus(adgroupNode.data[statusKey])) {
      changeChildrenStatus(adgroupNode, channelConfig.value);
      adgroupNode.data[statusKey] = adgroupNode.status === 1
        ? channelConfig.value.statusDraftCompleted : channelConfig.value.statusDraftInCompleted;
    }
    const isTempNode = isNodeTemp(Level.AdgroupLevel, adgroupNode.data, channelConfig.value);
    const published = isNodePublished(Level.AdgroupLevel, adgroupNode.data, channelConfig.value);

    // 比较不同或者临时状态，都保存
    if (!compareNodeData(Level.AdgroupLevel, adgroupNode.data, initAdgroupNode?.data) || isTempNode) {
      isTreeNodeChanges[1] = true;
      adgroupNode.data[mediaCampaignKey] = campaignNode.data[mediaCampaignKey];
      adgroupNode.data[innerCampaignKey] = campaignNode.data[innerCampaignKey];
      const result = await get(store).saveAdgroupNode(cloneDeep(adgroupNode), campaignNode, important);
      if (result.result.error_code) {
        MessagePlugin.error(result.result.error_message);
        return false;
      }
      // 发布后的，补上mediaId、inner_id
      if (published) {
        result[mediaAdgroupKey] = adgroupNode.data[mediaAdgroupKey];
        result[innerAdgroupKey] = adgroupNode.data[innerAdgroupKey];
      }
      const { [innerAdgroupKey]: innerAdgroupId, [mediaAdgroupKey]: mediaAdgroupId } = result as any;
      if (innerAdgroupId || mediaAdgroupId) {
        adgroupNode.data[innerAdgroupKey] = innerAdgroupId;
        adgroupNode.data[mediaAdgroupKey] = mediaAdgroupId;
        // 只有临时节点才会变更
        isTempNode && (adgroupNode.oldId = adgroupNode.id);
        // 保存草稿才更新为inner_id，发布后的不需要更新
        if (innerAdgroupId) adgroupNode.id = `1-${innerAdgroupId}`;

        // 重要字段变更，增加保存成功提示，不进行后续逻辑
        if (important) {
          await get(store).refreshTree();
          updateCurrent(adgroupNode.id);
          MessagePlugin.success('save Adgroup Success!');
          return false;
        }

        routerGo(get(router), {
          inner_adgroup_id: innerAdgroupId, media_adgroup_id: mediaAdgroupId,
        }, [], 'replace');
        successMsg.push('Adgroup');
        // 保存完成后保存一份到缓存中
        if (initAdgroupNode) {
          initAdgroupNode.id = adgroupNode.id;
          initAdgroupNode.data = cloneDeep(adgroupNode.data);
        } else {
          initCampaignNode && (setSomeNodeInCache(adgroupNode, initCampaignNode, isTempNode));
        }
      } else {
        return false;
      }
    }
    return true;
  };

  // 保存ad节点
  const saveAdNode = async (
    campaignNode: TreeNode,
    initAdNode: TreeNode | null,
    adNode: TreeNode,
    initAdgroupNode: TreeNode | null, adgroupNode: TreeNode,
    isTreeNodeChanges: boolean[],
    mediaCampaignKey: string,
    innerCampaignKey: string,
    mediaAdgroupKey: string,
    innerAdgroupKey: string,
    mediaAdKey: string,
    innerAdKey: string,
    successMsg: string[],
    statusKey: string,
    canSave: boolean,
  ) => {
    if (!canSave) {
      return false;
    }
    if (channelConfig.value.isInDraftStatus(adNode.data[statusKey])) {
      changeChildrenStatus(adNode, channelConfig.value);
      adNode.data[statusKey] = adNode.status === 1
        ? channelConfig.value.statusDraftCompleted : channelConfig.value.statusDraftInCompleted;
    }
    const isTempNode = isNodeTemp(Level.AdLevel, adNode.data, channelConfig.value);
    const published = isNodePublished(Level.AdLevel, adNode.data, channelConfig.value);

    // 比较不同或者临时状态，都保存
    if (!compareNodeData(Level.AdLevel, adNode.data, initAdNode?.data) || isTempNode) {
      isTreeNodeChanges[2] = true;
      adNode.data[mediaCampaignKey] = campaignNode.data[mediaCampaignKey];
      adNode.data[innerCampaignKey] = campaignNode.data[innerCampaignKey];
      adNode.data[mediaAdgroupKey] = adgroupNode.data[mediaAdgroupKey];
      adNode.data[innerAdgroupKey] = adgroupNode.data[innerAdgroupKey];
      const result = await get(store).saveAdNode(campaignNode, adgroupNode, adNode);
      if (!result) return false;
      if (result.result.error_code) {
        MessagePlugin.error(result.result.error_message);
        return false;
      }
      // 发布后的，补上mediaId、inner_id
      if (published) {
        result[mediaAdKey] = adNode.data[mediaAdKey];
        result[innerAdKey] = adNode.data[innerAdKey];
      }
      const { [innerAdKey]: innerAdId, [mediaAdKey]: mediaAdId } = result as any;
      if (innerAdId || mediaAdId) {
        adNode.data[innerAdKey] = innerAdId;
        adNode.data[mediaAdKey] = mediaAdId;
        isTempNode && (adNode.oldId = adNode.id);
        // 保存草稿才更新为inner_id，发布后的不需要更新
        adNode.id = `2-${innerAdId}`;
        routerGo(get(router), { inner_ad_id: innerAdId, media_ad_id: mediaAdId }, [], 'replace');
        successMsg.push('Ad');
        // 保存完成后保存一份到缓存中
        if (initAdNode) {
          initAdNode.id = adNode.id;
          initAdNode.data = cloneDeep(adNode.data);
        } else {
          initAdgroupNode && (setSomeNodeInCache(adNode, initAdgroupNode, isTempNode));
        }
        return true;
      }
      MessagePlugin.error((result as any)?.result?.error_message);
      return false;
    }
    return true;
  };

  // 补全treeList数据，返回新增层级的inner_ad_id
  const completeTreeList = (): string => {
    let newId = '';
    const newTree: TreeNode[] = [];
    treeList.value.forEach((campaignNode: TreeNode) => {
      if (campaignNode.children?.length === 0) {
        const defaultNode = get(store).getDefaultLevelNode(Level.AdgroupLevel, campaignNode.data);
        defaultNode.parentNode = campaignNode;
        newId = defaultNode.children[0].id;
        campaignNode.children.push(defaultNode);
      } else {
        campaignNode.children?.forEach((adgroupNode) => {
          if (adgroupNode.children?.length === 0) {
            const defaultNode = get(store).getDefaultLevelNode(Level.AdLevel, campaignNode.data, adgroupNode.data);
            defaultNode.parentNode = adgroupNode;
            newId = defaultNode.id;
            adgroupNode.children.push(defaultNode);
          }
        });
      }
      newTree.push(campaignNode);
    });
    treeList.value = newTree;
    initTreeList.value = cloneDeep(treeList.value);
    return newId;
  };

  // 从某一层级开始初始化目录树
  const initTreeFromLevel = (level: Level) => {
    if (level === Level.AdgroupLevel) {
      current.campaignNode.children = [];
    }
    if (level === Level.AdLevel) {
      current.adgroupNode.children = [];
    }
    const newId = completeTreeList();
    updateCurrent(newId);
  };

  return {
    addNode,
    copyNode,
    deleteNode,
    saveCampaign,
    saveAdgroupNode,
    saveAdNode,
    initTreeFromLevel,
  };
}
