/* eslint-disable no-param-reassign */
import { MessagePlugin } from 'tdesign-vue-next';
import { defineStore, storeToRefs } from 'pinia';
import type {
  BasicCampaignNumberInfoType, BasicInfoType, CopyParams, LinkParam, TreeCurrent, TreeNode,
} from '@/views/trade/ads_creation/common/template/type';
import {
  copyAd, copyAdgroup, copyCampaign, createNewTreeNode, deleteAdDraft, deleteAdgroupDraft, deleteCampaignDraft,
  getTreeList, publishAdsApi, saveAdApi, saveAdgroupApi, saveCampaignApi,
} from 'common/service/td/tiktok/tree';
import { initDefaultAdData, initDefaultAdgroupData, initDefaultCampaignData } from 'common/service/td/tiktok/utils';
import { generateTree } from 'common/service/td/utils';
import { STORE_KEY } from '@/config/config';
import { Level, MediaReqMap } from '@/views/trade/ads_creation/common/template/config';
import { useCommonParams } from '@/views/trade/ads_creation/common/template/compose/currentCompose';
import { useTreeListDataStore } from '../template.store';
import { CampaignType, AdgroupType, AdType } from '@/views/trade/ads_creation/tiktok/type';
import { cloneDeep, uniq } from 'lodash-es';

export const useTTTreeListData = defineStore(STORE_KEY.TD.TEMPLATE.TIKTOK, () => {
  const { channelConfig } = useTreeListDataStore();
  // 基本账户信息
  let basicInfo: BasicInfoType;
  let basicCampaignNumberInfo: BasicCampaignNumberInfoType;
  // 初始化表单的一些基础数据，后续供各个操作使用
  const basicInit = (
    basicInfoParams: BasicInfoType,
    basicCampaignNumberParams: BasicCampaignNumberInfoType,
  ) => {
    basicInfo = basicInfoParams;
    basicCampaignNumberInfo = basicCampaignNumberParams;
  };
  // 只初始化树的数据
  const init = async (LinkParams: LinkParam[]): Promise<TreeNode[]> => {
    if (basicInfo.operationType === 'add') {
      return [createNewTreeNode(Level.CampaignLevel, basicCampaignNumberInfo, basicInfo, 1, channelConfig)];
    }
    const treeList: TreeNode[] = await getTreeList({
      account_campaigns: LinkParams.map(({
        inner_campaign_id,
        media_campaign_id,
        account_id }) => ({ inner_campaign_id, campaign_id: media_campaign_id, account_id })),
      game_code: basicInfo.gameCode,
      media: MediaReqMap[basicInfo.media],
    }, channelConfig, basicCampaignNumberInfo);
    return treeList;
  };

  const saveCondition = (campaign: any, adgroup: any, ad: any) => {
    const result = [true, true, true];
    const isAcoAd = adgroup.creative_material_mode === 'DYNAMIC' && adgroup.ad_id;
    if (campaign.status === 'INDRAFT_PUBLISHING' || isAcoAd) {
      result[0] = false;
    }
    if (adgroup.status === 'INDRAFT_PUBLISHING' || isAcoAd) {
      result[1] = false;
    }
    if (ad.status === 'INDRAFT_PUBLISHING' || isAcoAd) {
      result[2] = false;
    }
    if (isAcoAd) {
      MessagePlugin.info('Aco ad can\'t be update');
    } else if (!result[0] && !result[1] && !result[2]) {
      MessagePlugin.info('Ad is still publishing and can\'t be update');
    }
    return result;
  };

  // 重新获取目录树
  const refreshTree = async () => {
    const { treeList, initTreeList } = storeToRefs(useTreeListDataStore());
    const accountCampaigns = (treeList.value as TreeNode[]).map((item) => {
      const { inner_campaign_id: icid, campaign_id: cid, account_id: aid } = item.data;
      return { inner_campaign_id: icid, campaign_id: cid, account_id: aid };
    });
    const newTreeList: TreeNode[] = await getTreeList({
      account_campaigns: accountCampaigns,
      game_code: basicInfo.gameCode,
      media: MediaReqMap[basicInfo.media],
    }, channelConfig, basicCampaignNumberInfo);
    // 设置新的树，更新当前层级数据
    treeList.value = newTreeList;
    initTreeList.value = cloneDeep(newTreeList);
  };

  const saveCampaignNode = async (campaignNode: TreeNode, important: boolean) => {
    // 这里保存campaign数据 个性数据都在这里处理
    const apiName = campaignNode.data.inner_campaign_id.startsWith('td-temp') ? 'create_draft_campaign' : 'update_campaign';

    const result: any = await saveCampaignApi({
      account_id: basicInfo.accountId,
      game_code: basicInfo.gameCode,
      media: MediaReqMap[basicInfo.media],
      __game: basicInfo.gameCode,
      campaign: {
        ...campaignNode.data,
        budget: !campaignNode.data.budget ? undefined : Number(campaignNode.data.budget),
        budget_mode: !campaignNode.data.budget ? undefined : campaignNode.data.budget_mode,
      },
    }, apiName);
    // 矫正字段
    result.media_campaign_id = (result as any).campaign_id;
    console.log('saveCampaign:', result);

    result.important = important;
    return result;
  };
  const saveAdgroupNode = async (adgroupNode: TreeNode, campaignNode: TreeNode, important: boolean) => {
    // 这里保存adgroup数据
    const apiName = adgroupNode.data.inner_adgroup_id.startsWith('td-temp') ? 'create_draft_adgroup' : 'update_adgroup';
    const ignoreKeys = ['ad_name_editable'];

    //  转换数据
    const data = cloneDeep(adgroupNode.data);
    if (!data.module_budget_schedule.budget) {
      data.module_budget_schedule.budget = undefined;
    } else {
      data.module_budget_schedule.budget = parseFloat(data.module_budget_schedule.budget);
    }
    if (!data.module_biding_optimization.secondary_optimization_event) {
      data.module_biding_optimization.secondary_optimization_event = undefined;
    }
    data.module_targeting.device.network_types = data.module_targeting.device.network_types.filter((item: string) => !['', 'NONE'].includes(item));
    const { interests_behavior: interestsBehavior } = data.module_targeting;
    interestsBehavior.behaviors = interestsBehavior.behaviors.filter((item: {
      action_category_ids: string[], video_user_actions: string[]
    }) => item.action_category_ids.length > 0 && item.video_user_actions.length > 0);
    data.module_targeting.demographics.location_ids = uniq(data.module_targeting.demographics.location_ids);

    ignoreKeys.forEach((k) => {
      delete data[k];
    });

    const result: any = await saveAdgroupApi({
      account_id: basicInfo.accountId,
      game_code: basicInfo.gameCode,
      media: MediaReqMap[basicInfo.media],
      __game: basicInfo.gameCode,
      adgroup: data,
    }, apiName);
    // 矫正字段
    result.media_adgroup_id = (result as any).adgroup_id;
    console.log('saveAdgroupApi:', result);

    result.important = important;
    return result;
  };
  const saveAdNode = async (campaignNode: TreeNode, adgroupNode: TreeNode, adNode: TreeNode) => {
    // 这里保存ad数据
    let apiName = adNode.data.inner_ad_id.startsWith('td-temp') ? 'create_draft_ad' : 'update_ad';
    const ignoreKeys = ['ad_name_editable'];

    const { creative_material_mode: mode } = adgroupNode.data;
    const params: { aco_ad?: AdType, inner_adgroup_id?: string, ad?: AdType } = {};
    if (mode === 'DYNAMIC') {
      apiName = apiName === 'create_draft_ad' ? 'create_draft_aco_ad' : 'update_aco_ad';
      params.aco_ad = cloneDeep(adNode.data);
      params.inner_adgroup_id = adNode.data.inner_adgroup_id;
    } else {
      params.ad = cloneDeep(adNode.data);
    }
    ignoreKeys.forEach((k) => {
      if (params.ad) delete (params.ad as any)[k];
      if (params.aco_ad) delete (params.aco_ad as any)[k];
    });

    const result: any = await saveAdApi({
      account_id: basicInfo.accountId,
      game_code: basicInfo.gameCode,
      media: MediaReqMap[basicInfo.media],
      __game: basicInfo.gameCode,
      ...params,
    }, apiName);
    if (mode !== 'DYNAMIC') {
      sessionStorage.setItem('lastAdName', (params.ad as AdType).ad_name); // 保存ad，缓存ad_name
    }
    // 矫正字段
    result.media_ad_id = (result as any).ad_id;
    return result;
  };

  // 发布广告
  const publishAds = async (adNode: TreeNode) => {
    const result = await publishAdsApi({
      account_id: basicInfo.accountId,
      game_code: basicInfo.gameCode,
      media: MediaReqMap[basicInfo.media],
      __game: basicInfo.gameCode,
      inner_ad_id: adNode.data.inner_ad_id,
    });
    // 矫正字段
    console.log('publishAds:', result);
    return result;
  };

  // 增加新节点
  const addNode = (current: TreeCurrent, level: Level, num: number) => {
    const creative = current.adgroupNode.data.creative_material_mode === 'DYNAMIC';
    // aco_ad，只允许有1个ad
    if (level === Level.AdLevel && creative) {
      MessagePlugin.warning('at most one ad');
      return undefined;
    }
    return createNewTreeNode(level, basicCampaignNumberInfo, basicInfo, num, channelConfig);
  };

  // 复制节点
  const copyNode = async (data: CopyParams) => {
    const { treeList, current, getTreeNode, addNode } = useTreeListDataStore();
    const params = {
      ...useCommonParams(),
      copy_type: data.copy_type === 1 ? 'THIS_LEVEL' : 'ALL_LEVEL',
    };
    console.log('params', params);
    const accountCampaigns = (treeList as TreeNode[]).map((item) => {
      const { inner_campaign_id: icid, campaign_id: cid, account_id: aid } = item.data;
      return { inner_campaign_id: icid, campaign_id: cid, account_id: aid };
    });
    let newId = '';
    if (data.level === Level.CampaignLevel) {
      const res = await copyCampaign({ ...params, inner_campaign_id: data.inner_id });
      newId = res.inner_campaign_id;
      // 复制campaign，使用当前campaign层级的参数
      const { account_id: aid } = current.campaignNode.data;
      accountCampaigns.push({ inner_campaign_id: newId, campaign_id: '', account_id: aid });
    }
    if (data.level === Level.AdgroupLevel) {
      const res = await copyAdgroup({ ...params, inner_adgroup_id: data.inner_id });
      newId = res.inner_adgroup_id;
    }
    if (data.level === Level.AdLevel) {
      // 创意广告类型，不允许复制
      if (current.adgroupNode.data.creative_material_mode === 'DYNAMIC') {
        MessagePlugin.warning('At most one ad');
        return;
      }
      const res = await copyAd({ ...params, inner_ad_id: data.inner_id });
      newId = res.inner_ad_id;
    }
    // 重新获取目录树
    const newTreeList: TreeNode[] = await getTreeList({
      account_campaigns: accountCampaigns,
      game_code: basicInfo.gameCode,
      media: MediaReqMap[basicInfo.media],
    }, channelConfig, basicCampaignNumberInfo);
    const newNodeId = `${data.level}-${newId}`;
    const newNode = getTreeNode(newTreeList, newNodeId, channelConfig);
    addNode(data.level, newNode as TreeNode);
  };

  // 查找节点
  const getTreeNode = (treeId: string, level: number) => {
    console.log(treeId, level);
  };

  // 删除节点
  const deleteNode = async (treeId: string, level: number) => {
    const commonParams = { game_code: basicInfo.gameCode, media: basicInfo.media };
    if (level === Level.AdLevel) {
      return deleteAdDraft({
        ...commonParams,
        account_draft_ads: [{ account_id: basicInfo.accountId, inner_ad_id: treeId }],
      });
    } if (level === Level.AdgroupLevel) {
      return deleteAdgroupDraft({
        ...commonParams,
        account_draft_adgroups: [{ account_id: basicInfo.accountId, inner_adgroup_id: treeId }],
      });
    } if (level === Level.CampaignLevel) {
      return deleteCampaignDraft({
        ...commonParams,
        account_draft_campaigns: [{ account_id: basicInfo.accountId, inner_campaign_id: treeId }],
      });
    }
  };

  // 初始化各个层级数据方法
  const getDefaultLevelNode = (level: Level, campaignData?: CampaignType, adgroupData?: AdgroupType) => {
    const initCampaignData = initDefaultCampaignData(basicCampaignNumberInfo, basicInfo.accountId);
    const initAdgroupData = initDefaultAdgroupData(basicCampaignNumberInfo, 1, campaignData || initCampaignData);
    return generateTree({
      level,
      initCampaignData,
      initAdgroupData,
      initAdData: initDefaultAdData(1, initCampaignData, adgroupData as AdgroupType || initAdgroupData),
      channelConfig,
    });
  };


  return {
    basicInit, init, addNode, getTreeNode, deleteNode, copyNode,
    saveAdNode, saveAdgroupNode, saveCampaignNode, getDefaultLevelNode, refreshTree,
    publishAds, saveCondition,
  };
});
