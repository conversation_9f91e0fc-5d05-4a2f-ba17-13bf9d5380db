/* eslint-disable no-param-reassign */
import { MessagePlugin } from 'tdesign-vue-next';
import { defineStore, storeToRefs } from 'pinia';
import { ref } from 'vue';
import type {
  BasicCampaignNumberInfoType, BasicInfoType, CopyParams, LinkParam, TreeCurrent, TreeNode,
} from '@/views/trade/ads_creation/common/template/type';
import {
  copyAd, copyAdgroup, copyCampaign, createNewTreeNode, deleteAdDraft, deleteAdgroupDraft, deleteCampaignDraft,
  getTreeList, publishAdsApi, saveAdApi, saveAdgroupApi, saveCampaignApi,
} from 'common/service/td/twitter/tree';
import { getCustomAudienceListApi, getOsVersionApi, getInterestsApi, getFollowersApi, getUsersApi } from 'common/service/td/twitter/options';
import { initDefaultAdData, initDefaultAdgroupData, initDefaultCampaignData } from 'common/service/td/twitter/utils';
import { generateTree } from 'common/service/td/utils';
import { STORE_KEY } from '@/config/config';
import { Level, MediaReqMap } from '@/views/trade/ads_creation/common/template/config';
import { useCommonParams } from '@/views/trade/ads_creation/common/template/compose/currentCompose';
import { useTreeListDataStore } from '../template.store';
import { CampaignType, AdgroupType } from '@/views/trade/ads_creation/twitter/type';
import { cloneDeep } from 'lodash-es';

export const useTWTreeListData = defineStore(STORE_KEY.TD.TEMPLATE.TWITTER, () => {
  const { channelConfig } = storeToRefs(useTreeListDataStore());
  // 基本账户信息
  let basicInfo: BasicInfoType;
  let basicCampaignNumberInfo: BasicCampaignNumberInfoType;
  // 增加公共options值保存
  const globalOptions: any = ref({});
  // 初始化表单的一些基础数据，后续供各个操作使用
  const basicInit = (
    basicInfoParams: BasicInfoType,
    basicCampaignNumberParams: BasicCampaignNumberInfoType,
  ) => {
    basicInfo = basicInfoParams;
    basicCampaignNumberInfo = basicCampaignNumberParams;
  };
  // 只初始化树的数据
  const init = async (LinkParams: LinkParam[]): Promise<TreeNode[]> => {
    if (basicInfo.operationType === 'add') {
      return [createNewTreeNode(Level.CampaignLevel, basicCampaignNumberInfo, basicInfo, 1, channelConfig.value)];
    }
    const treeList: TreeNode[] = await getTreeList({
      account_campaigns: LinkParams.map(({
        inner_campaign_id,
        media_campaign_id,
        account_id }) => ({ inner_campaign_id, campaign_id: media_campaign_id, account_id, media_campaign_id })),
      game_code: basicInfo.gameCode,
      media: MediaReqMap[basicInfo.media],
    }, channelConfig.value, basicCampaignNumberInfo);
    return treeList;
  };

  const saveCondition = (campaign: any, adgroup: any, ad: any) => {
    const result = [true, true, true];
    if (campaign.status === 'PUBLISHING') {
      result[0] = false;
    }
    if (adgroup.status === 'PUBLISHING') {
      result[1] = false;
    }
    if (ad.status === 'PUBLISHING') {
      result[2] = false;
    }
    if (!result[0] && !result[1] && !result[2]) {
      MessagePlugin.info('Ad is still publishing and can\'t be update');
    }
    return result;
  };

  // 重新获取目录树
  const refreshTree = async () => {
    const { treeList, initTreeList } = storeToRefs(useTreeListDataStore());
    const accountCampaigns = (treeList.value as TreeNode[]).map((item) => {
      const { inner_campaign_id: icid, campaign_id: cid, account_id: aid } = item.data;
      return { inner_campaign_id: icid, campaign_id: cid, account_id: aid, media_campaign_id: cid };
    });
    const newTreeList: TreeNode[] = await getTreeList({
      account_campaigns: accountCampaigns,
      game_code: basicInfo.gameCode,
      media: MediaReqMap[basicInfo.media],
    }, channelConfig.value, basicCampaignNumberInfo);
    // 设置新的树，更新当前层级数据
    treeList.value = newTreeList;
    initTreeList.value = cloneDeep(newTreeList);
  };

  const saveCampaignNode = async (campaignNode: TreeNode, important: boolean) => {
    // 这里保存campaign数据 个性数据都在这里处理
    const apiName = campaignNode.data.inner_campaign_id.startsWith('td-temp') ? 'create_campaign' : 'update_campaign';

    const result: any = await saveCampaignApi({
      campaign: {
        ...campaignNode.data,
        daily_budget_amount: campaignNode.data.budget_optimization === 'LINE_ITEM' ? '' : `${campaignNode.data.daily_budget_amount || ''}`,
        standard_delivery: !(campaignNode.data.budget_optimization === 'LINE_ITEM'),
        total_budget_amount: `${campaignNode.data.total_budget_amount || ''}`,
        account_id: campaignNode.data.account_id,
        game_code: basicInfo.gameCode,
        __game: basicInfo.gameCode,
      },
    }, apiName);
    // 矫正字段
    if (result.result.error_code === 0) {
      const { media_campaign_id: mediaId, inner_campaign_id: innerId } = result.campaign;
      result.media_campaign_id = mediaId;
      result.inner_campaign_id = innerId;
    }
    console.log('saveCampaign:', result);

    result.important = important;
    return result;
  };
  const saveAdgroupNode = async (adgroupNode: TreeNode, campaignNode: TreeNode, important: boolean) => {
    // 这里保存adgroup数据
    const apiName = adgroupNode.data.inner_ad_group_id.startsWith('td-temp') ? 'create_ad_group' : 'update_ad_group';
    const ignoreKeys = ['app_id', 'adgroup_name_editable'];
    const data = {
      ...adgroupNode.data,
    };
    const {
      audience_platform: audiencePlatform,
      demographics: {
        age_range: ageRange,
      },
    } = adgroupNode.data;

    if (!audiencePlatform.is_open) {
      data.audience_platform = {
        format: [],
        ad_categories: [],
        advertiser_domain: '',
        is_open: false,
      };
    }
    if (!ageRange) {
      data.demographics.min_age = 0;
      data.demographics.max_age = 0;
    }
    if (campaignNode.data.budget_optimization === 'CAMPAIGN') {
      data.budget_schedule.total_budget_amount = '';
      data.budget_schedule.daily_budget_amount = '';
    }
    data.targeting_features.key_words_exclude = data.targeting_features.key_words_exclude.split(/,|\n/g);
    data.targeting_features.key_words_include = data.targeting_features.key_words_include.split(/,|\n/g);
    data.delivery.bid_amount = `${data.delivery.bid_amount}`;
    data.budget_schedule.daily_budget_amount = `${data.budget_schedule.daily_budget_amount}`;
    data.budget_schedule.total_budget_amount = `${data.budget_schedule.total_budget_amount}`;

    data.budget_schedule.start_time = `${`${data.budget_schedule.start_time}:00`.split(' ').join('T')}Z`;
    if (data.budget_schedule.end_time) {
      data.budget_schedule.end_time = `${`${data.budget_schedule.end_time}:00`.split(' ').join('T')}Z`;
    }

    ignoreKeys.forEach((k) => {
      delete data[k];
    });

    const result: any = await saveAdgroupApi({
      ad_group: {
        account_id: basicInfo.accountId,
        game_code: basicInfo.gameCode,
        media: MediaReqMap[basicInfo.media],
        __game: basicInfo.gameCode,
        ...data,
      },
    }, apiName);
    // 矫正字段
    if (result.result.error_code === 0) {
      const { inner_ad_group_id: innerId, media_ad_group_id: mediaId } = result.ad_group;
      result.media_ad_group_id = mediaId;
      result.inner_ad_group_id = innerId;
    }

    console.log('saveAdgroupApi:', result);

    result.important = important;
    return result;
  };
  const saveAdNode = async (campaignNode: TreeNode, adgroupNode: TreeNode, adNode: TreeNode) => {
    // 这里保存ad数据
    const apiName = adNode.data.inner_ad_id.startsWith('td-temp') ? 'create_ad' : 'update_ad';
    const data = cloneDeep(adNode.data);
    const ignoreKeys = ['ad_name_editable'];

    data.objective = campaignNode.data.objective;
    if (!data.tweet.user.name) {
      data.tweet.user = globalOptions.value.promotableUsers;
    }
    data.tweet.destination.ios_app_store_identifier = adgroupNode.data.ios_app_store_identifier;
    data.tweet.destination.googleplay_app_id = adgroupNode.data.android_app_store_identifier;
    data.tweet.media_datas_list = undefined;

    ignoreKeys.forEach((k) => {
      delete data[k];
    });

    const result: any = await saveAdApi({
      ad: {
        ...data,
        account_id: basicInfo.accountId,
        game_code: basicInfo.gameCode,
      },
    }, apiName);
    if (result.result.error_code === 0) {
      // 矫正字段
      result.media_ad_id = (result as any).ad.media_ad_id;
      result.inner_ad_id = (result as any).ad.inner_ad_id;
    }

    return result;
  };

  // 发布广告
  const publishAds = async (adNode: TreeNode) => {
    const result = await publishAdsApi({
      account_id: basicInfo.accountId,
      game_code: basicInfo.gameCode,
      media: MediaReqMap[basicInfo.media],
      __game: basicInfo.gameCode,
      inner_ad_id: adNode.data.inner_ad_id,
    });
    // 矫正字段
    console.log('publishAds:', result);
    return result;
  };

  // 判断是否能够新增或者复制节点
  function canAddOrCopy() {
    return true;
  }

  // 增加新节点
  const addNode = (current: TreeCurrent, level: Level, num: number) => {
    if (!canAddOrCopy()) return;
    return createNewTreeNode(level, basicCampaignNumberInfo, basicInfo, num, channelConfig.value, current);
  };

  // 复制节点
  const copyNode = async (data: CopyParams) => {
    const { treeList, current, getTreeNode, addNode } = useTreeListDataStore();
    const params = {
      ...useCommonParams(),
      copy_type: data.copy_type === 1 ? 'THIS_LEVEL' : 'ALL_LEVEL',
    };
    console.log('params', params);
    const accountCampaigns = (treeList as TreeNode[]).map((item) => {
      const { inner_campaign_id: icid, campaign_id: cid, account_id: aid } = item.data;
      return { inner_campaign_id: icid, campaign_id: cid, account_id: aid, media_campaign_id: cid };
    });
    let newId = '';
    if (data.level === Level.CampaignLevel) {
      const res = await copyCampaign({ ...params, inner_campaign_id: data.inner_id });
      if (res.result.error_code) return MessagePlugin.error(res.result.error_message);
      newId = res.inner_campaign_id;
      // 复制campaign，使用当前campaign层级的参数
      const { account_id: aid } = current.campaignNode.data;
      accountCampaigns.push({ inner_campaign_id: newId, campaign_id: '', account_id: aid, media_campaign_id: '' });
    }
    if (data.level === Level.AdgroupLevel) {
      const res = await copyAdgroup({ ...params, inner_adgroup_id: data.inner_id });
      if (res.result.error_code) return MessagePlugin.error(res.result.error_message);
      newId = res.inner_adgroup_id;
    }
    if (data.level === Level.AdLevel) {
      // 创意广告类型，不允许复制
      if (current.adgroupNode.data.creative_material_mode === 'DYNAMIC') {
        MessagePlugin.warning('At most one ad');
        return;
      }
      const res = await copyAd({ ...params, inner_ad_id: data.inner_id });
      if (res.result.error_code) return MessagePlugin.error(res.result.error_message);
      newId = res.inner_ad_id;
    }
    // 重新获取目录树
    const newTreeList: TreeNode[] = await getTreeList({
      account_campaigns: accountCampaigns,
      game_code: basicInfo.gameCode,
      media: MediaReqMap[basicInfo.media],
    }, channelConfig.value, basicCampaignNumberInfo);
    const newNodeId = `${data.level}-${newId}`;
    const newNode = getTreeNode(newTreeList, newNodeId, channelConfig.value);
    addNode(data.level, newNode as TreeNode);
  };

  // 查找节点
  const getTreeNode = (treeId: string, level: number) => {
    console.log(treeId, level);
  };

  // 删除节点
  const deleteNode = async (treeId: string, level: number) => {
    if (level === Level.AdLevel) {
      return deleteAdDraft({
        advertise_type: 'ad',
        inner_ids: [treeId],
      });
    } if (level === Level.AdgroupLevel) {
      return deleteAdgroupDraft({
        advertise_type: 'ad_group',
        inner_ids: [treeId],
      });
    } if (level === Level.CampaignLevel) {
      return deleteCampaignDraft({
        advertise_type: 'campaign',
        inner_ids: [treeId],
      });
    }
  };

  // 初始化各个层级数据方法
  const getDefaultLevelNode = (level: Level, campaignData?: CampaignType, adgroupData?: AdgroupType) => {
    const initCampaignData = initDefaultCampaignData(basicCampaignNumberInfo, basicInfo.accountId);
    const initAdgroupData = initDefaultAdgroupData(basicCampaignNumberInfo, 1, campaignData || initCampaignData);
    return generateTree({
      level,
      initCampaignData,
      initAdgroupData,
      initAdData: initDefaultAdData(1, initCampaignData, adgroupData as AdgroupType || initAdgroupData),
      channelConfig: channelConfig.value,
    });
  };

  const getInterests = (query = '') => getInterestsApi({ ...useCommonParams(), query });
  const getCustomAudienceList = (query = '') => getCustomAudienceListApi({ ...useCommonParams(), query });
  const getOsVersionList = () => getOsVersionApi(useCommonParams());
  const getFollowers = (query = '') => getFollowersApi({ ...useCommonParams(), query });
  const getUsers = (query: string[]) => getUsersApi({ ...useCommonParams(), ids: query });

  return {
    basicInit, init, addNode, getTreeNode, deleteNode, copyNode,
    saveAdNode, saveAdgroupNode, saveCampaignNode, getDefaultLevelNode, refreshTree,
    publishAds, saveCondition, getInterests, getCustomAudienceList, getOsVersionList,
    getFollowers, getUsers, globalOptions,
  };
});
