/* eslint-disable no-param-reassign */
import { useSessionStorage, get } from '@vueuse/core';
import type { Router } from 'vue-router';
import { cloneDeep } from 'lodash-es';

import type { TreeNode } from '@/views/trade/ads_creation/common/template/type';
import type { SubChannel } from '@/views/trade/ads_creation/common/template/config';

export const levelMapName: { [key: string]: string} = {
  0: 'campaign',
  1: 'adgroup',
  2: 'ad',
};
const nameMapLevel: { [key: string]: number} = {
  campaign: 0,
  adgroup: 1,
  ad: 2,
};

/**
 * 写入缓存
 */
const sessionInCache: { [key: string]: any} = {
  cacheRoot: null,
  initCacheRoot: null,
};
export const setInfoIntoCache = (
  treeNodeList: TreeNode[],
  key: string,
) => {
  const sessionStorage = useSessionStorage(key, treeNodeList, {
    serializer: {
      read: (v: any) => {
        if (sessionInCache[key] && sessionInCache[key].length > 0) {
          return sessionInCache[key];
        }
        let result = null;
        if (v) {
          result = JSON.parse(v);
          addParentNode(result);
        }
        return result;
      },
      write: (v: any) => {
        sessionInCache[key] = v;
        console.log('session storage cache', v);
        return JSON.stringify(v, (key, value) => {
          if (key === 'parentNode') {
            return undefined;
          }
          return value;
        });
      },
    },
  });
  return sessionStorage;
};
/**
 * 每个节点挂上parentNode
 */
const addParentNode = (treeLists: TreeNode[]) => {
  const displayTree = (List: TreeNode[], parentNode: TreeNode | null) => {
    List.forEach((treeNode) => {
      if (parentNode) {
        // eslint-disable-next-line no-param-reassign
        treeNode.parentNode = parentNode;
      }
      if (treeNode.children && treeNode.children?.length > 0) {
        displayTree(treeNode.children, treeNode);
      }
    });
  };
  displayTree(treeLists, null);
};
/**
 * router跳转
 * @param router 路由实例
 * @param params 要增加或者替换的值
 * @param deleteKeys 要删除的key
 * @param type 路由replace 或者 push
 */
export const routerGo = async (router: Router, params: object, deleteKeys: string[], type: 'replace' | 'push') => {
  const { path, query } = get(router.currentRoute);
  const newParam = {
    ...query,
    ...params,
  };
  const queryStr = Object.entries(newParam).filter(([key, value]) => !deleteKeys.includes(key) && value !== '' && value !== undefined)
    .map(([key, value]) => `${key}=${value}`)
    .join('&');
  await router[type](`${path}?${queryStr}`);
};
/**
 * 对比表单数据和缓存中数据是否一致
 * @param treeNode
 * @param newTreeNode
 * @returns boolean
 */
export const compareData = (treeNode: TreeNode | null, newTreeNode: TreeNode | null) => {
  if (!treeNode || !newTreeNode) {
    console.log('treeNode or newTreeNode is undefined');
    return true;
  }
  if (JSON.stringify(treeNode.data) !== JSON.stringify(newTreeNode.data)) {
    return true;
  }
  return false;
};
/**
 * 部分写入缓存
 */
export const setSomeNodeInCache = (currentNode: TreeNode, parentNode: TreeNode, isTempNode: boolean) => {
  if (parentNode && !parentNode?.children) {
    // eslint-disable-next-line no-param-reassign
    parentNode.children = [];
  }
  const cloneCurrentNode = cloneDeep(currentNode);
  // 要把下一级干掉
  if (isTempNode) {
    cloneCurrentNode.children = [];
  }
  cloneCurrentNode.parentNode = parentNode;
  parentNode?.children?.push(cloneCurrentNode);
};

export const getKeysByConfig = (channelConfig: SubChannel) => {
  const {
    inner_campaign_id: innerCampaignKey,
    media_campaign_id: mediaCampaignKey,
    inner_adgroup_id: innerAdgroupKey,
    media_adgroup_id: mediaAdgroupKey,
    inner_ad_id: innerAdKey,
    media_ad_id: mediaAdKey,
    status,
    errorKey,
  } = channelConfig.keyConfig;
  return {
    innerCampaignKey,
    mediaCampaignKey,
    innerAdgroupKey,
    mediaAdgroupKey,
    innerAdKey,
    mediaAdKey,
    statusKey: status,
    errorKey,
  };
};

export const filterCompleteDraftNode = (treeListNode: TreeNode[], channelConfig: SubChannel) => {
  const result: TreeNode[] = [];
  if (!channelConfig) {
    console.log('channelConfig undefined');
    return result;
  }
  const statusName = channelConfig.keyConfig.status.ad;
  const statusCompleted = channelConfig.statusDraftCompleted;
  const { statusFailed } = channelConfig;
  const campaignStatusName = channelConfig.keyConfig.status.campaign;
  const adgroupStatusName = channelConfig.keyConfig.status.adgroup;
  const findCompleteDraftNode = (listNodes: TreeNode[]) => {
    // 这里ad节点必须是草稿完整，父节点可以是发布过或者未发布过的完整草稿
    listNodes.forEach((node) => {
      if (node.level === 2
          && [statusCompleted, statusFailed].includes(node.data[statusName])
          && channelConfig.canPublishInDraft(node.parentNode?.data?.[adgroupStatusName])
          && channelConfig.canPublishInDraft(node.parentNode?.parentNode?.data?.[campaignStatusName])
      ) {
        result.push(node);
      }
      if (node.children) {
        findCompleteDraftNode(node.children);
      }
    });
  };
  findCompleteDraftNode(treeListNode);
  return result;
};
const setErrorMsg = (
  node: TreeNode, errorKey: string,
  errMsg: string | undefined, failedLevel: string,
  statusKey: any,
  statusFailed: string,
) => {
  const faileLevelNumber = nameMapLevel[failedLevel];
  let tempNode: TreeNode | null = node;
  while (true) {
    if (!tempNode) {
      break;
    }
    if (tempNode.level === faileLevelNumber) {
      tempNode.data[errorKey] = errMsg;
      tempNode.data[statusKey[levelMapName[tempNode.level]]] = statusFailed;
      break;
    }
    if (tempNode.level > faileLevelNumber) {
      tempNode = tempNode.parentNode;
    }
  };
};
export const modifyTreeNodeId = (
  channelConfig: SubChannel,
  node: TreeNode,
  reqRes: any,
  currentAdNode: TreeNode,
  router: Router,
  isPublishing?: boolean,
  errMsg?: string,
  publishError?: number[],
  copeErrorMsg?: Function,
): boolean => {
  const {
    innerCampaignKey, mediaCampaignKey,  innerAdgroupKey, mediaAdgroupKey, innerAdKey, mediaAdKey, statusKey, errorKey,
  } = getKeysByConfig(channelConfig);
  const { statusFailed, statusPublishing } = channelConfig;
  const {
    [innerCampaignKey]: innerCampaignId,
    [mediaCampaignKey]: mediaCampaignId,
    [innerAdgroupKey]: innerAdgroupId,
    [mediaAdgroupKey]: mediaAdgroupId,
    [innerAdKey]: innerAdId,
    [mediaAdKey]: mediaAdId,
    failed_level: failedLevel, // gg特有的
  } = reqRes as any;
  const levelMapIdKey: { [key: string]: string[]} = {
    0: [mediaCampaignKey, innerCampaignKey],
    1: [mediaAdgroupKey, innerAdgroupKey],
    2: [mediaAdKey, innerAdKey],
  };
  const levelMapId: { [key: string]: string[]} = {
    0: [mediaCampaignId, innerCampaignId],
    1: [mediaAdgroupId, innerAdgroupId],
    2: [mediaAdId, innerAdId],
  };
  // 处理发布中的节点状态
  if (isPublishing) {
    node.data[statusKey[levelMapName[node.level]]] = statusPublishing;
    node.parentNode && (node.parentNode.data[statusKey[levelMapName[node.level - 1]]] = statusPublishing);
    node.parentNode?.parentNode
      && (node.parentNode.parentNode.data[statusKey[levelMapName[node.level - 2]]] = statusPublishing);
    return false;
  }
  // 处理错误发布状态
  const mediaId = levelMapId[node.level][0];
  let isRecordMsg = false;
  if (node.parentNode) {
    isRecordMsg = modifyTreeNodeId(
      channelConfig, node.parentNode, reqRes,
      currentAdNode, router, isPublishing,
      errMsg, publishError, copeErrorMsg,
    );
  }
  node.data[statusKey[levelMapName[node.level]]] = statusFailed;
  // 记录过，不在处理下一级
  if (isRecordMsg) {
    return true;
  }
  if (!mediaId) {
    if (failedLevel) {
      setErrorMsg(node, errorKey, errMsg, failedLevel, statusKey, statusFailed);
      return true;
    }
    node.data[errorKey] = errMsg;
    publishError && (publishError[node.level] += 1);
    return true;
  }
  // 发布成功的时候 把错误信息清空了
  node.data[errorKey] = '';
  // 处理成功发布的节点状态
  // node.id = `${node.level}-${mediaId}`;
  changeSomeParams(node, channelConfig, levelMapIdKey, levelMapId, mediaId);
  if (node === currentAdNode || node?.parentNode === currentAdNode || node?.parentNode?.parentNode === currentAdNode) {
    routerGo(router, {
      inner_campaign_id: innerCampaignId,
      media_campaign_id: mediaCampaignId,
      inner_adgroup_id: innerAdgroupId,
      media_adgroup_id: mediaAdgroupId,
      inner_ad_id: innerAdId,
      media_ad_id: mediaAdId,
    }, [], 'replace');
  }
  return isRecordMsg;
};

const changeSomeParams = (
  node: TreeNode,
  channelConfig: SubChannel,
  levelMapIdKey: any,
  levelMapId: any,
  mediaId: string,
) => {
  const { statusEnable } = channelConfig;
  const statusKey = channelConfig.keyConfig.status;
  node.data[levelMapIdKey[node.level][0]] = mediaId;
  node.data[levelMapIdKey[node.level][1]] = levelMapId[node.level][1] || '';
  node.data[statusKey[levelMapName[node.level]]] = statusEnable;
  if (node.level > 0) {
    // 如果是adgroup 或者ad层级，有些渠道需要设置campaign的innerid和mediaId数据
    node.data[levelMapIdKey[`${node.level - 1}`][0]] = levelMapId[`${node.level - 1}`][0] || '';
    node.data[levelMapIdKey[`${node.level - 1}`][1]] = levelMapId[`${node.level - 1}`][1] || '';
  }
  if (node.level > 1) {
    // 如果ad层级，有些渠道需要设置campaign的innerid和mediaId数据
    node.data[levelMapIdKey[`${node.level - 2}`][0]] = levelMapId[`${node.level - 2}`][0] || '';
    node.data[levelMapIdKey[`${node.level - 2}`][1]] = levelMapId[`${node.level - 2}`][1] || '';
  }
};

export const changeChildrenStatus = (node: TreeNode, channelConfig: SubChannel) => {
  const { statusFailed } = channelConfig;
  const {
    statusKey,
    errorKey,
  } = getKeysByConfig(channelConfig);
  if (node.data[statusKey[levelMapName[node.level]]] === statusFailed) {
    node.data[errorKey] = '';
    if (node.children?.length) {
      node.children.forEach((item) => {
        item.data[statusKey[levelMapName[node.level]]] = item.status === 1
          ? channelConfig.statusDraftCompleted : channelConfig.statusDraftInCompleted;
        // 这里还要清空错误信息remark (tiktok)
        item.data[errorKey] = '';
        if (item.children?.length) {
          item.children.forEach((subNode: TreeNode) => {
            subNode.data[statusKey[levelMapName[subNode.level]]] = subNode.status === 1
              ? channelConfig.statusDraftCompleted : channelConfig.statusDraftInCompleted;
            subNode.data[errorKey] = '';
          });
        }
      });
    }
  };
};
