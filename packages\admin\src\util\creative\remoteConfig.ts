import { useMemory, type MemoryInfo } from '@vueuse/core';
import { Ref } from 'vue';
type PerformanceMemory = Performance & {
  memory: MemoryInfo
};

export enum CacheMapMode {
  // 浏览器内存检测模式，使用performance.memory,但是有兼容性问题，火狐等一些浏览器不支持
  // 此时将退化到Byte模式，预估100MB
  'Memory' = 'memory',

  // 行模式，存储最大容量为N
  // 'Row' = 'row',

  // 字节模式，存储最大容量为N,使用JSON.Stringify存储字符串,根据value长度,估计使用的内存大小,并不精准
  // 而且JSON.Stringify序列化对象时会忽略undefined等其他问题
  // 请确保被序列化的对象符合JSON.Stringify规范
  'Byte' = 'byte',
}

/**
 * 遵循lru策略缓存map
 * 使用map默认set排序，get时将该记录重新插入，达到lru效果
 */

export class BaseCacheMap<T> {
  public map = new Map<string, T>();
  protected maxCapacity: number;
  protected curUsage = 0;

  constructor(maxCapacity: number) {
    this.maxCapacity = maxCapacity;
  }

  get(key: string) {
    const value = this.map.get(key);
    if (value) {
      // 移动到底部
      this.reInsert(key, value);
    }
    return value;
  }

  reInsert(key: string, value: T) {
    this.map.delete(key);
    this.map.set(key, value);
  }

  deleteFirst() {
    return this.map.delete(this.map.keys().next().value);
  }

  fuzzyDelete(fuzzyKey: string) {
    for (const key of this.map.keys()) {
      if (key.includes(fuzzyKey)) {
        this.map.delete(key);
      }
    }
  }
}

export class MemoryCacheMapImp<T> extends BaseCacheMap<T> {
  private memory: Ref<MemoryInfo>;

  // maxCapacity为百分比,1-100
  constructor(percent: number) {
    const {  memory } = useMemory();
    // 定时器轮询的，第一次没有值
    memory.value = (performance as PerformanceMemory).memory;

    const maxCapacity = Math.floor(memory.value.usedJSHeapSize * percent);
    super(maxCapacity);
    this.memory = memory as Ref<MemoryInfo>;
  }

  set(key: string, value: T): void {
    const curUsage = this.memory.value.usedJSHeapSize;
    if (curUsage > this.maxCapacity) {
      this.deleteFirst();
      return this.set(key, value);
    }

    this.map.set(key, value);
  }
}

export class ByteCacheMapImp<T> extends BaseCacheMap<string> {
  set(key: string, value: T): void {
    const newValue = JSON.stringify({ value });
    const curUsage = this.curUsage + this.getByteByString(newValue);

    if (curUsage > this.maxCapacity) {
      this.deleteFirst();
      return this.set(key, value);
    }
    this.map.set(key, newValue);
    this.curUsage = curUsage;
  }

  get(key: string) {
    return JSON.parse(this.map.get(key) ?? '{}').value;
  }

  deleteFirst(): boolean {
    const firstKey = this.map.keys().next().value;
    return this.delete(firstKey);
  }

  getByteByString(string: string) {
    return string.length * 2;
  }

  delete(key: string) {
    const value = this.map.get(key);
    this.curUsage -= this.getByteByString(value ?? '');
    return this.map.delete(key);
  }

  fuzzyDelete(fuzzyKey: string): void {
    for (const key of this.map.keys()) {
      if (key.includes(fuzzyKey)) {
        this.delete(key);
      }
    }
  }
}

interface MemoryOptions {
  percent: number;
}
interface ByteOptions {
  byte: number;
}

export class CacheMap<T extends CacheMapMode, F> {
  public instance: MemoryCacheMapImp<F> | ByteCacheMapImp<F>;
  constructor(mode: T, options: T extends CacheMapMode.Memory ? MemoryOptions : ByteOptions) {
    if (mode === CacheMapMode.Byte) {
      this.instance = new ByteCacheMapImp((options as ByteOptions).byte);
    }
    const { isSupported } = useMemory({
      immediate: true,
    });

    if (isSupported.value) {
      this.instance = new MemoryCacheMapImp<F>((options as MemoryOptions).percent);
    }
    // 200M
    this.instance = new ByteCacheMapImp<F>(200 * 1024 * 1024);
  }
}
