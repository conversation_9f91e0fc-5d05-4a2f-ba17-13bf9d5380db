
// replaceUrl.ts
import { ObjectDirective } from 'vue';

interface Binding {
  value: string;
}

const replaceUrl: ObjectDirective<HTMLElement, Binding> = {
  beforeMount(el, binding) {
    replaceElementUrl(el, binding.value);
  },
  updated(el, binding) {
    if (binding.oldValue !== binding.value) {
      replaceElementUrl(el, binding.value);
    }
  },
};

function replaceElementUrl(el: HTMLElement, url: unknown) {
  if (typeof url === 'string') {
    const originalUrl: string = url;
    const replacedUrl = replaceAiUrl(originalUrl);
    if (el.tagName === 'A') {
      el.setAttribute('href', replacedUrl);
    } else if (el.tagName === 'IMG') {
      el.setAttribute('src', replacedUrl);
    }
  }
}

export const replaceAiUrl = (originUrl = '') => originUrl.replace(
  'https://ua-sg-1300342648.cos.ap-singapore.myqcloud.com/',
  'https://ua-cdn.intlgame.com/',
).replace('https://file.aix.intlgame.com/', 'https://ua-cdn.intlgame.com/')
  .replace('http://aix-static-1300342648.cos.ap-singapore.myqcloud.com/', 'https://static.aix.intlgame.com/');

export default replaceUrl;
