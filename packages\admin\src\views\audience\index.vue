<template>
  <div>
    <!-- campaign list  -->
    <router-view v-slot="{ Component }">
      <template v-if="Component">
        <suspense>
          <!-- main content -->
          <component :is="Component" />

          <!-- loading state -->
          <template #fallback>
            <FullLoading />
          </template>
        </suspense>
      </template>
    </router-view>
  </div>
</template>
<script setup lang='ts'>
import FullLoading from 'common/components/FullLoading';
</script>
