<template>
  <CommonView
    :hide-right="true"
    :store="store"
    class="h-full overflow-hidden audience-overview"
  >
    <template #views>
      <div class="mb-[16px]">
        <t-button
          :loading="store.isLoading"
          @click="gotoForm('add', {})"
        >
          <template #icon>
            <SvgIcon
              name="plus"
              size="12px"
              color="var(--aix-text-color-white-primary)"
              class="mr-[8px]"
            />
          </template>
          Create
        </t-button>
      </div>
      <div
        v-if="store.isLoading"
        class="h-full"
      >
        <FullLoading
          class="relative  rounded-large"
        />
      </div>
      <template v-else>
        <!-- 不是demo游戏 -->
        <Tabs
          v-if="!isDemoGame()"
          :tab-list="store.tabList"
          :is-loading="store.isLoading"
          :filter-option="(store.filterOptions as any)"
          @tab-change="onTabChange"
        />
        <!-- 是demo游戏 -->
        <TabItem
          v-else
          :data="store.demoGameTableData"
        />
      </template>
      <BaseDialog
        ref="dialogRef"
        confirm-text="Run"
        cancel-text="Cancel"
        width="530px"
        @confirm="onConfrim"
        @close="reserForm"
      >
        <template #title>
          <div class="flex items-center gap-x-[8px]">
            <Text
              size="l"
              content="Run at now"
            />
            <t-tooltip theme="light">
              <template #content>
                <div>
                  <Text
                    content="Only upload tasks are supported."
                    size="small"
                  />
                </div>
              </template>
              <div class="cursor-pointer">
                <IconFont
                  name="info-circle"
                  size="14px"
                />
              </div>
            </t-tooltip>
          </div>
        </template>
        <t-loading :loading="store.isDialogLoading">
          <t-form
            ref="formRef"
            :data="formData"
            :rules="FORM_RULES"
            :required-mark="false"
            label-width="120px"
          >
            <t-form-item
              label="Audience name"
              name="audienceName"
            >
              <t-input
                disabled
                placeholder=""
                :value="formData.audienceName"
              />
            </t-form-item>
            <t-form-item name="period">
              <template #label>
                <div class="flex items-center gap-x-[8px] h-[32px] leading-[32px]">
                  <Text content="Select period " />
                  <t-tooltip theme="light">
                    <template #content>
                      <div>
                        <Text
                          :content="[
                            'Data flag field, and do not display flags',
                            'for successful uploads and uploads in progress.'
                          ].join(' ')"
                          size="small"
                        />
                      </div>
                    </template>
                    <div class="cursor-pointer leading-[22px] flex items-center">
                      <IconFont
                        name="info-circle"
                        size="14px"
                      />
                    </div>
                  </t-tooltip>
                </div>
              </template>
              <t-select
                v-model="formData.period"
                placeholder="Please select"
                multiple
              >
                <t-option
                  label="All"
                  :check-all="true"
                />
                <t-option
                  v-for="item in store.periodList"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </t-select>
            </t-form-item>
          </t-form>
        </t-loading>
      </BaseDialog>
    </template>
  </CommonView>
</template>
<script lang="ts" setup>
import { useGoto } from '@/router/goto';
import { useAixAudienceOverviewStore } from '@/store/audience/overview/index.store';
import { useGlobalGameStore } from '@/store/global/game.store';
import BaseDialog from 'common/components/Dialog/Base';
import CommonView from 'common/components/Layout/CommonView.vue';
import Text from 'common/components/Text';
import type { IAudienceTable } from 'common/service/audience/overview/type';
import { storeToRefs } from 'pinia';
import { IconFont } from 'tdesign-icons-vue-next';
import { provide, reactive, ref, watch } from 'vue';
import Tabs from './components/Tabs.vue';
import { FORM_RULES } from './const';
import SvgIcon from 'common/components/SvgIcon';
import TabItem  from '@/views/audience/overview/components/TabItem.vue';
import FullLoading from 'common/components/FullLoading';

const store = useAixAudienceOverviewStore();
const { gotoAudienceForm } = useGoto();
const { gameCode } = storeToRefs(useGlobalGameStore());
const { isDemoGame } = useGlobalGameStore();
provide('showDialog', showDialog);
provide('gotoForm', gotoForm);

const dialogRef = ref();
const formRef = ref();
const formData = reactive({
  audienceName: '',
  audienceId: '',
  period: [],
});

async function onConfrim() {
  const valid = await formRef.value.validate();
  if (valid === true) {
    store.restartTask(formData.audienceId, formData.audienceName, formData.period);
    reserForm();
    dialogRef.value.hide();
  }
}

function reserForm() {
  formRef.value.reset({ type: 'initial' });
  formRef.value.clearValidate();
}

function showDialog(row: IAudienceTable) {
  formData.audienceName = row.name;
  formData.audienceId = row.id;
  dialogRef.value.show();
  store.getRestartPeriod(row);
}

function gotoForm(type: 'add' | 'show', detail: {} | IAudienceTable) {
  store.setOperationTypeState(type);
  store.setAudienceTableRowState(detail);
  gotoAudienceForm();
}

function onTabChange(val: string) {
  store.getDataByType(val);
}

watch(
  () => gameCode.value,
  () => {
    store.init();
  },
  { deep: true },
);
</script>
<style lang="scss" scoped>
.audience-overview {
  :deep(.isolate) {
    overflow: hidden;
  }
}
</style>
