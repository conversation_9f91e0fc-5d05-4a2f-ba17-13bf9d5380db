<template>
  <DataContainer
    :class="{
      'rounded-[12px]': isDemoGame(),
      'h-full': !isDemoGame()
    }"
    class="gap-y-[8px]"
    :data="filterTableData"
    :total="filterTableData.length"
    :page-size="pageSize"
    :default-page="pageIndex"
    :loading="props.isLoading"
    @on-page-change="onPageChange"
  >
    <template #attributeSlot>
      <div class="mt-[24px]">
        <t-input
          v-model="keyWords"
          placeholder="Enter to search"
          clearable
        >
          <template #prefix-icon>
            <SvgIcon name="search" size="14px" />
          </template>
        </t-input>
      </div>
    </template>
    <template #actionSlot>
      <div />
    </template>
    <Table
      class="h-full"
      :columns="columnsInner"
      :sort="sortInfo"
      :filter-value="defaultFilter"
      hide-sort-tips
      :multiple-sort="false"
      :max-height="`calc(100vh - 324px)`"
      row-key="id"
      @sort-change="onSortChange"
      @filter-change="onFilterChange"
    />
  </DataContainer>
</template>
<script lang="ts" setup>
import DataContainer from 'common/components/Layout/DataContainer.vue';
import { PropType, ref, computed, watch } from 'vue';
import type { IAudienceTable, IAudienceFilterOption } from 'common/service/audience/overview/type';
import type { PageInfo as IPageInfo, SortInfo as ISortInfo, FilterValue as TFilterValue } from 'tdesign-vue-next';
import { useAixAudienceOverviewStore } from '@/store/audience/overview/index.store';
import { storeToRefs } from 'pinia';
import Table from 'common/components/table';
import SvgIcon from 'common/components/SvgIcon';
import { getColumns } from './columns/Index';
import { pick, isString, orderBy, isEqual, omit, cloneDeep } from 'lodash-es';
import { useGlobalGameStore } from '@/store/global/game.store';


const props = defineProps({
  data: {
    type: Array as PropType<IAudienceTable[]>,
    default: () => [],
  },
  isLoading: {
    type: Boolean,
    default: () => false,
  },
});
const { isDemoGame } = useGlobalGameStore();
const { columns, filterOptions, demoGameBlackAudienceIdList } = storeToRefs(useAixAudienceOverviewStore());
const pageIndex = ref<number>(1);
const pageSize = ref<number>(20);

const keyWords = ref<string>('');

const sortInfo = ref<ISortInfo>({ descending: true, sortBy: 'created_time' });

// const sortData = computed(() => setSortData(props.data, sortInfo.value));
const sortData = computed(() => setSortData(
  (
    isDemoGame() ? cloneDeep(props.data).filter(item => !demoGameBlackAudienceIdList.value.includes(item.id))
      : props.data
  ), sortInfo.value,
));

const filterTableData = computed(() => sortData.value.filter(item => (
  filterData(keyWords.value.toLocaleLowerCase(), item, filterColKey.value))));

// 表格筛选的列
const filterColKey = ref<TFilterValue>({});
// 默认筛选条件
const  defaultFilter = ref<TFilterValue>({
  type_text: '',
  os: '',
  media: '',
  createby_text: '',
  status: '',
  tag: '',
});

// 分页
function onPageChange(current: number, pageInfo: IPageInfo) {
  pageIndex.value = current;
  pageSize.value = pageInfo.pageSize;
}

const columnsInner = computed(() => getColumns(columns.value, filterOptions.value as IAudienceFilterOption));
// 处理排序的逻辑
function setSortData(data: IAudienceTable[], sort: ISortInfo) {
  if (!sort) {
    return data;
  }
  return orderBy(data, [sort.sortBy], [sort.descending ? 'desc' : 'asc']);
}

// 多字段搜索
function filterData(val: string, row: IAudienceTable, filterCol: TFilterValue)  {
  /**
   * 接口返回的table里面有很多个字段
   * 但是页面表格只是显示其中的一部分
   * 所以，拿columns和table中的每一行取交集，并拿对象中的value，得到一个数组
   */
  const rowValueList = Object.values(pick(row, columns.value.map(item => item.key)));
  // 搜索框的内容匹配到了多少个 字段
  const matchedRowValueByKeyWords =  rowValueList.filter(item => (isString(item) ? item : '').toLocaleLowerCase().includes(val));
  // 参与筛选的的字段有哪些 tag字段排除掉，需要单独处理
  const filterKeyList = Object.keys(omit(filterCol, ['tag']));
  // 表头没有筛选条件的情况下
  if (filterKeyList.length === 0 && !filterCol.tag) {
    return matchedRowValueByKeyWords.length > 0;
  }

  //  table中的字段和要筛选的字段求交集，拿到值
  const filterRowValueList = Object.values(pick(row, filterKeyList));
  const filterValueList = Object.values(omit(filterCol, ['tag']));
  if (filterCol.tag) {
    return isEqual(filterRowValueList, filterValueList) && (row as any)[filterCol.tag]
     && matchedRowValueByKeyWords.length > 0;
  }
  return isEqual(filterRowValueList, filterValueList) && matchedRowValueByKeyWords.length > 0;
}

// 排序的变化
function onSortChange(sort: ISortInfo) {
  sortInfo.value = sort;
}

function onFilterChange(filterValue: TFilterValue) {
  // 值为空的 排除的掉 =all的时候 值也是为空, 空的放进去没意义，因为空的表示不筛选
  filterColKey.value = {};
  Object.keys(filterValue).forEach((key) => {
    if (filterValue[key]) {
      filterColKey.value[key] = filterValue[key];
    }
  });
}

watch(() => filterColKey.value, (val) => {
  console.log('filterColKey', val);
});

</script>

<style lang="scss" scoped>
:deep(.t-input-adornment__prepend) {
  @apply bg-white-primary;
  border-color: --td-border-level-2-color;
  border-top-width: 1px;
  border-left-width: 1px;
  border-bottom-width: 1px;
}

:deep(.t-table__first-full-row) {
  display: none;
}
:deep(.t-table__content) {
  height: 100%;
}
</style>

