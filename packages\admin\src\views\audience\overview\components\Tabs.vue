<template>
  <t-tabs
    v-model="tabValue"
    theme="card"
    :disabled="props.isLoading"
    class=" h-auto max-h-full  overflow-y-hidden flex flex-col rounded-large"
    @change="onChange"
  >
    <t-tab-panel
      v-for="item in props.tabList"
      :key="item.id"
      class="h-full"
      :destroy-on-hide="true"
      :value="item.id"
    >
      <template #label>
        <Text :content="item.label" />
      </template>
      <TabItem
        :data="item.tableData"
        :is-loading="props.isLoading"
      />
    </t-tab-panel>
  </t-tabs>
</template>
<script lang="ts" setup>
import type { ITbas } from '@/store/audience/overview/type';
import { PropType, ref, watch } from 'vue';
import TabItem from './TabItem.vue';
import Text from 'common/components/Text';

const emit = defineEmits(['tabChange']);
const props = defineProps({
  tabList: {
    type: Array as PropType<ITbas[]>,
    default: () => [],
  },
  isLoading: {
    type: Boolean,
    default: () => false,
  },
  // filterOption: {
  //   type: Object as PropType<IFilterOption>,
  //   required: true,
  // },
});

const tabValue = ref<string>('');

watch(() => props.tabList, (val) => {
  if (val.length > 0 && (!tabValue.value || !val.some(item => item.id === tabValue.value))) {
    tabValue.value = val[0].id;
  }
}, { deep: true, immediate: true });

function onChange(val: string) {
  emit('tabChange', val);
}
</script>

<style lang="scss" scoped>
:deep(.t-tabs) {
  @apply rounded-large;
}
:deep(.t-tabs__nav-item) {
  border-top-right-radius: 12px;
  border-top-left-radius: 12px;
  border-bottom: none;
}
:deep(.t-tabs__nav--card.t-tabs__nav-item:not(:first-of-type)) {
  border-left: none;
}
:deep(.t-tabs__nav--card.t-tabs__nav-item:last-of-type) {
  border-right: none;
}
:deep(.t-tabs__nav-wrap) {
  column-gap: 8px;
}
</style>
