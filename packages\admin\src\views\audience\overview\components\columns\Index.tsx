import { useEnv } from 'common/compose/env';
import type { IAudienceFilterOption, IAudienceTable, IColumns } from 'common/service/audience/overview/type';
import Tag from 'tdesign-vue-next/es/tag';
import { FIX_COLUMNS, SORT_COLUMNS } from '../../const';
import { formatCell, formatTitle, setColumnsFilterList } from './format';
import { renderOperation } from './operation';
import { useGlobalGameStore } from '@/store/global/game.store';

export const getColumns = (colList: IColumns[], filterOption: IAudienceFilterOption) => {
  // const { updateAudience } = useAixAudienceOverviewStore();
  const { isPcGame } = useGlobalGameStore();
  const {
    tagOpt = [],
    typeOpt = [],
    platformOpt = [],
    mediaOpt = [],
    createdByOpt = [],
    statusOpt = [],
  } = filterOption;

  const { isFuncom } = useEnv();
  let platformFilterOpt = platformOpt.filter(item => ['Android', 'iOS'].includes(item.value));
  if (isPcGame()) {
    platformFilterOpt = [{ text: 'Web', value: 'Web' }];
  }
  // 带有筛选的列
  const filterColumns = [
    {
      colKey: 'type_text',
      filter: {
        type: 'single',
        list: setColumnsFilterList(typeOpt),
      },
    },
    {
      colKey: 'os',
      filter: {
        type: 'single',
        list: setColumnsFilterList(platformFilterOpt),
      },
    },
    {
      colKey: 'media',
      filter: {
        type: 'single',
        list: setColumnsFilterList(mediaOpt),
      },
    },
    {
      colKey: 'createby_text',
      filter: {
        type: 'single',
        list: setColumnsFilterList(createdByOpt),
      },
    },
    {
      colKey: 'status',
      filter: {
        type: 'single',
        list: setColumnsFilterList(statusOpt),
      },
    },
  ];

  // 可编辑的列
  // const editColumns = [
  //   {
  //     colKey: 'remark',
  //     edit: {
  //       component: Input,
  //       props: {
  //         clearable: true,
  //         autofocus: true,
  //       },
  //       validateTrigger: 'change',
  //       // on: (editContext) => ({
  //       //   // onBlur: () => {
  //       //   //   console.log('失去焦点', editContext);
  //       //   // },
  //       //   onEnter: (ctx) => {
  //       //     ctx?.e?.preventDefault();
  //       //     console.log('onEnter', ctx);
  //       //   },
  //       // }),
  //       // abortEditOnEvent: ['onEnter'],
  //       rules: [
  //         // { required: true, message: '不能为空' },
  //       ],
  //       onEdited: (context: any) => {
  //         // console.log('context', context);
  //         updateAudience({
  //           audience_id: (context.newRowData as IAudienceTable).id,
  //           remark: context.newRowData.remark,
  //         });
  //       },
  //       defaultEditable: false,
  //     },
  //   },
  // ];

  // 动态列，从接口拿的
  const dynamicColumns = colList.map(item => ({
    colKey: item.key,
    title: () => formatTitle(item),
    width: item.width || 150,
    ellipsis: true,
    cell: (h: any, { row }: any) => formatCell(row, item),
    fixed: FIX_COLUMNS.find(col => col.colKey === item.key)?.fixed,
    sorter: SORT_COLUMNS.includes(item.key),
    sortType: 'all',
    filter: filterColumns.find(col => col.colKey === item.key)?.filter,
    // edit: editColumns.find(col => col.colKey === item.key)?.edit,
  }));

  // 右边固定 的三列
  const staticColumnsByEnv = isFuncom.value
    ? []
    : [
      {
        colKey: 'tag',
        title: 'Key Tag',
        cell: (h: any, { row }: any) => {
          const tagList = tagOpt.filter(({ value = '' }) => row[value]);
          return tagList && tagList.length > 0 ? (
            tagList.map(({ text = '' }, index) => <Tag key={index}>{text}</Tag>)
          ) : (
              <div>-</div>
          );
        },
        filter: {
          type: 'single',
          list: setColumnsFilterList(tagOpt),
        },
        width: 255,
      },
    ];
  const staticColumns: any[] = [
    // {
    //   colKey: 'block_alarms',
    //   title: 'Block Alarm Msg',
    //   cell: (h: any, { row }: any) => (row?.block_alarms ? 'True' : 'False'),
    //   width: 140,
    // },
    {
      colKey: 'operation',
      title: 'Operation',
      fixed: 'right',
      width: 160,
      cell: (h: any, { row }: { row: IAudienceTable }) => renderOperation(row),
    },
  ];
  // 拼接在一起 返回
  return [...dynamicColumns, ...staticColumnsByEnv, ...staticColumns];
};
