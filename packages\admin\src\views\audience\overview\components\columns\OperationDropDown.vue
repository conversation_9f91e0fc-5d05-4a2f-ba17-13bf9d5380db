<template>
  <div>
    <t-dropdown
      v-if="isShowDropDown"
      :hide-after-item-click="false"
      trigger="click"
    >
      <div class="cursor-pointer"><IconFont class="text-brand" name="chevron-down" /></div>
      <t-dropdown-menu>
        <t-dropdown-item
          value="delete"
        >
          <t-popconfirm
            @confirm="deleteConfirm"
            @cancel="hideDropDownMenu"
          >
            <template #content>
              <div>
                <Text :content="deleteInfo.title" />
                <div><Text size="small" :content="deleteInfo.message" /></div>
              </div>
            </template>
            <Text class="inline-block w-full" content="Delete" />
          </t-popconfirm>
        </t-dropdown-item>
        <!-- <t-dropdown-item value="copy">
          <Text
            class="inline-block w-full"
            content="Copy"
            @click="copy"
          />
        </t-dropdown-item> -->
        <!-- demo 游戏下不显示 -->
        <!-- <t-dropdown-item
          v-if="!isDemoGame()"
          value="run_at_now"
        >
          <Text class="inline-block w-full" content="Run at now" @click="runAtNow" />
        </t-dropdown-item> -->
      </t-dropdown-menu>
    </t-dropdown>
  </div>
</template>
<script lang="ts" setup>
import Text from 'common/components/Text';
import { IconFont } from 'tdesign-icons-vue-next';
import { PropType, computed, ref, nextTick } from 'vue';
import type { IAudienceTable } from 'common/service/audience/overview/type';
import { DELETE_AUDEINCE_INFO } from '../../const';
import { useAixAudienceOverviewStore } from '@/store/audience/overview/index.store';
// import type { TShowDialogFun } from '../../type';
import { useGlobalGameStore } from '@/store/global/game.store';

const props = defineProps({
  row: {
    type: Object as PropType<IAudienceTable>,
    required: true,
  },
});

const { isDemoGame } = useGlobalGameStore();
// const gotoForm = inject('gotoForm') as Function;

const isShowDropDown = ref<boolean>(true);
const deleteInfo = computed(() => getDeleteInfo(props.row));
const { deleteAudience, deleteAudienceByDemoGame } = useAixAudienceOverviewStore();

// const injectShowDialog = inject('showDialog') as TShowDialogFun;

function getDeleteInfo(row: IAudienceTable) {
  let info = DELETE_AUDEINCE_INFO.default;
  if ((DELETE_AUDEINCE_INFO as any)[`${row.media}|${row.type}`]) {
    info = DELETE_AUDEINCE_INFO['Facebook|event'];
  }
  return info;
}

function hideDropDownMenu() {
  isShowDropDown.value = false;
  nextTick(() => {
    isShowDropDown.value = true;
  });
}

function deleteConfirm() {
  // 如果是demo游戏，假删除，不用调接口
  if (isDemoGame()) {
    deleteAudienceByDemoGame(props.row.id);
  } else {
    deleteAudience(props.row);
  }
  hideDropDownMenu();
}

// function runAtNow() {
//   injectShowDialog(props.row);
//   hideDropDownMenu();
// }

// function copy() {
//   hideDropDownMenu();
//   gotoForm('add', props.row);
// }
</script>
<style lang="scss">
</style>
