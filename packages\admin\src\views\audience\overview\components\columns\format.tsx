import type { IColumns, IFilterOptionItem } from 'common/service/audience/overview/type';
import dayjs from 'dayjs';
import { isString } from 'lodash-es';
import { formatMoney } from 'common/utils/format';
import Tooltip from 'tdesign-vue-next/es/tooltip';
import { IconFont } from 'tdesign-icons-vue-next';
import Text from 'common/components/Text';

// 格式化单元格
export function formatCell(row: any, col: IColumns) {
  if (col.formatter) {
    const [type = '', format = ''] = col.formatter.split('|');
    return type === 'date' ? dayjs(row[col.key]).format(format) : row[col.key];
  }
  if (col.format && col.format === 'number') {
    const isRange = isString(row[col.key]) && (row[col.key] as string).includes('-');
    if (isRange) {
      const size = row[col.key].split('-');
      return size.map((one: string) => formatMoney(one)).join('-');
    }
    if (row[col.key] === 'N/A') {
      return row[col.key];
    }
    return formatMoney(row[col.key] as number);
  }
  return row[col.key];
}

// 格式化表头
export function formatTitle(col: IColumns) {
  if (col.tips && isString(col.tips)) {
    return (
      <div class={'flex items-center gap-x-[8px]'}>
        {  col.title }
        <Tooltip theme="light" placement="top">
          {{
            default: () => (
              <div class={'cursor-pointer'}>
                <IconFont name="info-circle" size='14px' />
              </div>
            ),
            content: () => {
              const tipList = col.tips?.split('\n');
              return (<div>
                {tipList?.map((item, index) => (<div class={{ 'mt-[16px]': (index + 1) === tipList.length }}>
                  <Text content={item} size="small" />
                </div>))}
              </div>);
            },
          }}
        </Tooltip>
      </div>
    );
  }
  return col.title;
}


// 设置列 筛选
export function setColumnsFilterList(list: IFilterOptionItem[], isShowAllOption = true) {
  const filterList = list.map(item => ({
    label: item.text,
    value: item.value,
  }));
  if (isShowAllOption) {
    return [
      {
        label: 'ALL',
        value: '',
      },
      ...filterList,
    ];
  }
  return filterList;
}
