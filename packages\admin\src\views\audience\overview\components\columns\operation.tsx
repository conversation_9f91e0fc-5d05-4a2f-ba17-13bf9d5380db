import { computed, inject } from 'vue';
import Popconfirm from 'tdesign-vue-next/es/popconfirm';
import { AUDEINCE_INFO } from '../../const';
import type { IAudienceTable } from 'common/service/audience/overview/type';
import Text from 'common/components/Text';
import OperationDropDown from './OperationDropDown.vue';
import { useAixAudienceOverviewStore } from '@/store/audience/overview//index.store';
import { storeToRefs } from 'pinia';
import { useGoto } from '@/router/goto';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useRouterStore } from '@/store/global/router.store';

export function renderOperation(row: IAudienceTable) {
  const { gotoAudienceLog } = useGoto();
  const { isDemoGame } = useGlobalGameStore();
  const { updateStatus } = useAixAudienceOverviewStore();
  const { gameCode } = storeToRefs(useGlobalGameStore());
  const { allowRoutes } = storeToRefs(useRouterStore());
  const isShowLogText = computed(() => allowRoutes.value[gameCode.value].includes('Audience & Event / Manage Audience / Audience Log'));
  const gotoForm = inject('gotoForm') as Function;
  return (
    <div class={'flex items-center gap-x-[8px]'}>
      <Text
        class={'cursor-pointer'}
        color={'var(--aix-text-color-brand)'}
        content="Edit"
        onClick={() => gotoForm('show', row)}
      />
      {!isDemoGame() && isShowLogText.value && (
        <Text
          class={'cursor-pointer'}
          color={'var(--aix-text-color-brand)'}
          content="Log"
          onClick={() => gotoAudienceLog({ id: row.id })}
        />
      )}
      {
        // 不是demo游戏，并且是有权限的状态下，才显示（开启/暂停）的按钮
        !isDemoGame() && (
          <>
            <Popconfirm onConfirm={() => updateStatus(row) as any}>
              {{
                default: () => (
                  <Text
                    class={'cursor-pointer'}
                    color={'var(--aix-text-color-brand)'}
                    content={['suspend', 'Paused'].includes(row.status) ? 'Unpause' : 'Pause'}
                  />
                ),
                content: () => {
                  const { title = '', message = '' } = (AUDEINCE_INFO as any)[
                    row.status !== 'Paused' ? 'default' : 'suspend'
                  ];
                  return (
                    <div>
                      <Text content={title}></Text>
                      <div>
                        <Text
                          size="small"
                          content={message}
                        ></Text>
                      </div>
                    </div>
                  );
                },
              }}
            </Popconfirm>
          </>
        )
      }
      <OperationDropDown row={row} />
    </div>
  );
}
