
export const FIX_COLUMNS = [
  {
    colKey: 'name',
    fixed: 'left',
  },
];

// 可排序的列
export const SORT_COLUMNS = ['name', 'created_time'];

// audeince 删除提示信息
export const DELETE_AUDEINCE_INFO = {
  default: {
    title: 'Delete this audience/event?',
    message:
      'After deletion, the corresponding audience/event on the ad channels will also be deleted, and this action cannot be undone.',
  },
  'Facebook|event': {
    title: 'Deletion Alert',
    message: [
      'Facebook does not support event deletion via API.',
      'This event will only be deleted from the AiX audience list.',
      'The data in Facebook will not change, but it will no longer be updated.',
    ].join(' '),
  },
};

// 暂停/开启audeince 提示信息(暂停时需要开启，默认为开启时需要暂停)
export const AUDEINCE_INFO = {
  default: { // 暂停该audience/event
    title: 'Pause this audience/event?',
    message: 'Upon pausing, AiX will stop updating this audience/event to the ad channels.',
  },
  suspend: { // 开启该audience/event
    title: 'Unpause this audience/event?',
    message:
      'After unpausing, AiX will keep updating this audience/event to the ad channels ',
  },
};

// 表单验证规则
export const FORM_RULES = {
  period: [
    { required: true, message: 'please select at least one period', trigger: 'blur' },
    { required: true, message: 'please select at least one period', trigger: 'change' },
  ],
};

/**
 * 如果要更改
 * 1.0项目中 src\pages\audience\overview\utils.js 里面的方法 也要跟着一起改
 */
export const AIX_AUDIENCE_FROM_STATE_V2_GOTO_V1 = 'AIX_AUDIENCE_FROM_STATE_V2_GOTO_V1';

