<template>
  <div class="bg-white-primary rounded-large p-[16px] min-w-min">
    <t-form
      ref="goalFormRef"
      label-width="180px"
      :data="formData"
      :rules="FORM_RULES"
      :disabled="!isAdd"
      label-align="left"
    >
      <t-form-item
        label="Optimization target"
        required-mark
        name="target"
      >
        <t-radio-group
          :model-value="formData.target"
          @update:model-value="(val: string) => setTarget(val)"
        >
          <t-radio
            v-for="item in OPTIMIZATION_TARGET_LIST"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </t-radio>
        </t-radio-group>
      </t-form-item>
      <t-form-item
        v-if="formData.target === 'new_install'"
        :required-mark="false"
        name="newTarget"
      >
        <t-select
          :model-value="formData.newTarget"
          class="w-[440px]"
          :options="newInstallList"
          @update:model-value="(val: string) => setNewTarget(val)"
        />
      </t-form-item>
      <t-form-item
        v-if="formData.target === 're_attribution'"
        :required-mark="false"
        name="reTarget"
      >
        <t-select
          :model-value="formData.reTarget"
          class="w-[440px]"
          :options="reList"
          @update:model-value="(val: string) => setReTarget(val)"
        />
      </t-form-item>
    </t-form>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { storeToRefs } from 'pinia';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { FORM_RULES, OPTIMIZATION_TARGET_LIST } from './const';

const { newInstallList, reList, formData, isAdd } = storeToRefs(useAixAudienceOverviewFormStore());
const { setTarget, setNewTarget, setReTarget } = useAixAudienceOverviewFormUpdateStore();

const goalFormRef = ref();

defineExpose({
  validate: () => goalFormRef?.value?.validate(),
});

</script>
<style lang="scss" scoped>
</style>
