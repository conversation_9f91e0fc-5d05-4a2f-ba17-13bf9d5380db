<template>
  <CommonView
    hide-right
    class="pl-0 pr-0 pb-0 h-full"
    :store="store"
  >
    <template #title>
      <div class="pl-[24px] flex items-center">
        <SvgIcon
          name="arrow2"
          class="hover:fill-brand cursor-pointer mr-[8px]"
          size="14px"
          @click="goBack"
        />
        <h1 class="text-2xl leading-[32px] font-[600] truncate">
          {{ store.isAdd ? 'New Audience' : 'Audience Details' }}
        </h1>
      </div>
    </template>
    <template #views>
      <div class="h-full flex flex-col justify-between overflow-hidden">
        <div class="px-[24px] pb-[24px] h-full overflow-auto">
          <div
            v-if="store.isPageLoading"
            class="h-[600px] rounded-extraLarge overflow-hidden relative"
          >
            <loading />
          </div>
          <template v-else>
            <t-loading :loading="store.isReqLoading">
              <!-- <Goal
                v-if="!isDemoGame()"
                ref="goalRef"
              /> -->
              <Recommend
                ref="recommendRef"
              />
            </t-loading>
          </template>
        </div>
        <SendTypeDialog
          ref="sendTypeDialogRef"
          @confirm="() => submit()"
        />
        <footer
          v-if="isShowFooter"
          class="sticky bottom-0 left-0 top-0 right-0 bg-white-primary
            w-full py-[16px] px-[32px] border-white-primary border-solid border-t-[1px] flex justify-end z-10"
        >
          <t-button
            :class="{ invisible: store.isPageLoading || store.isPageLoading }"
            :disabled="store.isPageLoading || store.isPageLoading"
            :loading="store.isReqLoading || store.isPageLoading"
            @click="() => submit()"
          >
            {{ store.isAdd ? 'Add' : 'Save' }}
          </t-button>
        </footer>
      </div>
    </template>
  </CommonView>
</template>

<script lang="ts" setup>
import { ref, toRaw, onUnmounted, watch } from 'vue';
import CommonView from 'common/components/Layout/CommonView.vue';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useGlobalGameStore } from '@/store/global/game.store';
import { storeToRefs } from 'pinia';
import loading from 'common/components/FullLoading/loading.vue';
import SvgIcon from 'common/components/SvgIcon';
import Recommend from './recommend/Index.vue';
import SendTypeDialog from './components/SendTypeDialog.vue';
import { createAudience, updateAudience } from '@/store/audience/overview/utils/formSubmit';
import { useGoto } from '@/router/goto';

const store = useAixAudienceOverviewFormStore();

const { gotoAudience, goBack } = useGoto();

const { gameCode } = storeToRefs(useGlobalGameStore());
const isShowFooter = true; // funcom 和 demo下都需要展示

// const goalRef = ref();
const recommendRef = ref();
const sendTypeDialogRef = ref();


async function submit() {
  try {
    const recommendValid = await recommendRef.value.validate();
    if (recommendValid !== true) return;
    store.showReqLoading();
    await (store.isAdd ? createAudience(toRaw(store.formData)) : updateAudience(toRaw(store.formData)));
    store.hideReqLoading();
    return gotoAudience();
  } catch (e: any) {
    store.hideReqLoading();
    console.log(e);
  }
}

// 页面销毁时重置表单
onUnmounted(() => {
  store.resetFormData();
});

watch(
  () => gameCode.value,
  () => {
    store.init();
    if (store.isAdd) {
      store.resetFormData();
      store.setSendType('ads');
    }
  },
  { deep: true },
);
</script>
<style lang="scss" scoped>
:deep(.t-form__label--required label)::before {
  content: '';
  display: none;
}
:deep(.t-form__label--required label)::after {
  display: inline-block;
  margin-right: var(--td-comp-margin-xs);
  color: var(--td-error-color);
  line-height: var(--td-line-height-body-medium);
  content: '*';
}
:deep(.isolate) {
  height: 100%;
}
</style>
