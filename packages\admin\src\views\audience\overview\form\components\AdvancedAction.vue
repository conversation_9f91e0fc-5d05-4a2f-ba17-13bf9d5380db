<template>
  <t-form
    ref="formRef"
    label-width="0px"
    :data="formDataInner"
    :rules="rules"
  >
    <div class="flex flex-col advanced-action-list">
      <div
        v-for="(item, index) in formDataInner.list"
        :key="item.key"
        class="flex items-center gap-x-[8px]"
      >
        <t-form-item
          label=""
          class="w-[306px]"
          :name="`list[${index}].key`"
        >
          <t-select
            v-model="item.key"
            class="w-[306px]"
            placeholder=""
          >
            <t-option
              v-for="opt in advancedActionList"
              :key="opt.value"
              :value="opt.value"
              :label="opt.text"
              :disabled="formDataInner.list.some(action => action.key === opt.value)"
            />
          </t-select>
        </t-form-item>
        <t-form-item
          :key="item.key"
          label=""
          :name="`list[${index}].values`"
        >
          <div class="flex items-center justify-between w-[306px]">
            <!-- <div class="max-w-[134px]"> -->
            <t-input-number
              v-model="formDataInner.list[index].values[0]"
              theme="normal"
              class="w-[134px]"
              :label="getAdvancedActionItem(item.key)?.before"
              placeholder=""
              :min="getAdvancedActionItem(item.key)?.min"
              :decimal-places="getAdvancedActionItem(item.key)?.precision"
              :allow-input-over-limit="false"
            />
            <!-- </div> -->
            <Text class="block" content="To" />
            <!-- <div class="max-w-[134px]"> -->
            <t-input-number
              v-model="formDataInner.list[index].values[1]"
              :label="getAdvancedActionItem(item.key)?.before"
              class="w-[134px]"
              theme="normal"
              placeholder=""
              :max="getAdvancedActionItem(item.key)?.max"
              :decimal-places="getAdvancedActionItem(item.key)?.precision"
              :allow-input-over-limit="false"
            />
            <!-- </div> -->
          </div>
          <div>
            <Text
              class="cursor-pointer ml-[8px]"
              content="Delete"
              color="var(--aix-text-color-brand)"
              @click="() => formDataInner.list = formDataInner.list.filter(action => action.key !== item.key)"
            />
          </div>
        </t-form-item>
      </div>
    </div>
    <Text
      v-if="isShowAdd"
      class="cursor-pointer mt-[8px] inline-block"
      content="Add"
      color="var(--aix-text-color-brand)"
      size="l"
      @click="onAdd"
    />
  </t-form>
</template>
<script lang="ts" setup>
import { watch, ref, computed, PropType, toRaw, reactive } from 'vue';
import Text from 'common/components/Text';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import type { IAdvancedActionList } from '@/views/audience/overview/type';
import type { IIAudienceFormOptionAdvancedActionList } from 'common/service/audience/overview/type';
import { has, isArray } from 'lodash-es';
import { storeToRefs } from 'pinia';

interface IModelValue {
  [key: string]: number[],
}

const emit = defineEmits(['update:modelValue']);

const props = defineProps({
  modelValue: {
    type: Object as PropType<IModelValue>,
    required: true,
  },
});

const { advancedActionList,
} = storeToRefs(useAixAudienceOverviewFormStore());

const formDataInner = reactive<{
  list: IAdvancedActionList[]
}>({
  list: [],
});

const rules = {
  values: [
    { validator: checkValues, trigger: 'change' },
  ],
};

const formRef  = ref();

const isShowAdd = computed(() => (
  advancedActionList.value.length > 0 && formDataInner.list.length < advancedActionList.value.length
));


function checkValues(val: (string|number)[]) {
  if (isArray(val) && Number(val[0] || 0) > Number(val[1] || 0)) {
    return { result: false, message: '请选择正确的范围', type: 'error' };
  }
  return true;
};

function onAdd() {
  const actionItem = advancedActionList.value.find(item => (
    !formDataInner.list.some(action => action.key === item.value)
  ));
  if (actionItem) {
    formDataInner.list.push({
      key: actionItem.value,
      before: actionItem?.before as any,
      precision: actionItem.precision,
      max: actionItem.max,
      min: actionItem.min,
      values: [0, 0],
    });
  }
}

function getAdvancedActionItem(val: string) {
  return  advancedActionList.value.find(item => item.value === val);
}

// 当modelValue 或者advancedActionList 变化时应该给listInner赋值
watch(() => [advancedActionList.value, props.modelValue], ([newAdvancedActionList, newModelValue]) => {
  (newAdvancedActionList as IIAudienceFormOptionAdvancedActionList[]).forEach((item) => {
    if (has((newModelValue as IModelValue), item.value) && (newModelValue as IModelValue)[item.value].length === 2) {
      if (!formDataInner.list.some(itemInner => itemInner.key === item.value)) {
        formDataInner.list.push({
          key: item.value,
          before: item?.before,
          precision: item.precision,
          max: item.max,
          min: item.min,
          values: [(newModelValue as IModelValue)[item.value][0], (newModelValue as IModelValue)[item.value][1]],
        });
      }
    }
  });
}, { deep: true, immediate: true });

watch(() => [...formDataInner.list], (val) => {
  emit('update:modelValue', val.map(item => toRaw(item)));
}, { deep: true });

defineExpose({
  validate: () => formRef?.value?.validate(),
});

</script>
<style lang="scss" scoped>
.advanced-action-list {
  div{
    :deep(.t-form__item) {
      margin-right: 0;
    }
  }
  div:not(:last-child) {
    :deep(.t-form__item) {
      margin-bottom: var(--td-comp-margin-xxl);
    }
  }
}
</style>
