<template>
  <t-select
    ref="selectRef"
    v-bind="useAttrs()"
    :model-value="formData.tableName"
    @update:model-value="(val: string) => setTableName(val)"
    @popup-visible-change="onPopupVisibleChange "
  >
    <t-option
      v-for="item in customTableListInner"
      :key="item.source_table"
      :value="item.source_table"
      :label="`${item.source_table}|${item.remark}`"
    >
      <div class="flex items-center gap-x-[8px]">
        <div
          class="truncate"
          :title="item.source_table"
          :style="{width: `${(width - 60) * 0.6}px`}"
        >
          {{ item.source_table }}
        </div>
        <div>|</div>
        <div
          class="flex items-center gap-x-[8px]"
          :style="{width: `${(width - 60) * 0.4}px`}"
          @click.stop
        >
          <t-input
            v-if="editItem && editItem.source_table === item.source_table"
            v-model.trim="editItem.remark"
          />
          <div
            v-else
            class="truncate"
            :title="item.remark"
          >
            {{ item.remark }}
          </div>
          <div
            class="cursor-pointer w-[14px] h-[14px]"
            @click.stop="onEdit(item)"
          >
            <SvgIcon
              name="pencil"
              size="14px"
            />
          </div>
        </div>
      </div>
    </t-option>
    <template #panelTopContent>
      <div class="p-[8px] bg-white-primary sticky z-10 top-0">
        <t-input v-model="keyWords" placeholder="" clearable />
      </div>
    </template>
  </t-select>
</template>
<script lang="ts" setup>
import { useAttrs, ref, computed } from 'vue';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { storeToRefs } from 'pinia';
import { useElementSize } from '@vueuse/core';
import { cloneDeep } from 'lodash-es';
import SvgIcon from 'common/components/SvgIcon';
import type { IAudienceFormOptionCustomTableList } from 'common/service/audience/overview/type';


const selectRef = ref();
const keyWords = ref('');

const editItem = ref<IAudienceFormOptionCustomTableList | null>(null);

const { width } = useElementSize(selectRef);

const { customTableList, formData } = storeToRefs(useAixAudienceOverviewFormStore());
const { updateTableNote } = useAixAudienceOverviewFormStore();

const { setTableName } = useAixAudienceOverviewFormUpdateStore();

const customTableListInner = computed(() => (
  customTableList.value.filter(item => (
    item.remark.toLocaleLowerCase().includes(keyWords.value.toLocaleLowerCase())
    || item.source_table.toLocaleLowerCase().includes(keyWords.value.toLocaleLowerCase())
  ))
));

function onEdit(item: IAudienceFormOptionCustomTableList) {
  editItem.value = cloneDeep(item);
}
async function onPopupVisibleChange(visible: boolean) {
  if (!visible && editItem.value !== null) {
    const tableName = customTableList.value.find(item => item.source_table === editItem.value?.source_table);
    if (tableName && tableName.remark !== editItem.value.remark) {
      await updateTableNote(editItem.value.id, editItem.value.remark);
    }
  }
  editItem.value = null;
}

</script>
<style lang="scss" scoped>
</style>
