<template>
  <div class=" flex flex-col gap-y-[16px]">
    <div
      v-for="item in listInner"
      :key="item.key"
      class="flex items-center gap-x-[8px]"
    >
      <t-select
        v-model="item.key"
        class="w-[306px]"
        :options="optionsInnter"
        placeholder=""
      />
      <t-date-range-picker
        v-model="item.value"
        class="w-[306px]"
        cancel-range-select-limit
      />
      <div>
        <Text
          class="cursor-pointer"
          content="Delete"
          color="var(--aix-text-color-brand)"
          @click="() => deleteItem(item.key)"
        />
      </div>
    </div>
    <Text
      v-if="isShowAdd"
      class="cursor-pointer max-w-max"
      content="Add"
      color="var(--aix-text-color-brand)"
      size="l"
      @click="onAdd"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, watch, toRaw, PropType } from 'vue';
import Text from 'common/components/Text';
import dayjs from 'dayjs';
import { remove } from 'lodash-es';
import { ACTION_RANGE_LIST } from '@/views/audience/overview/form/const';
import type { IGeneralActionList } from '@/views/audience/overview/type';

interface IModelValue {
  [key: string]: string[],
}

const props = defineProps({
  modelValue: {
    type: Object as PropType<IModelValue>,
    default: () => {},
  },
});

const emit = defineEmits(['update:modelValue']);

const listInner = ref<IGeneralActionList[]>([]);

const isShowAdd = computed(() => listInner.value.length < ACTION_RANGE_LIST.length);

const optionsInnter = computed(() => ACTION_RANGE_LIST.map(item => ({
  ...item,
  disabled: listInner.value.some(action => action.key === item.value),
})));

function onAdd() {
  const actionItem = ACTION_RANGE_LIST.find(item => !listInner.value.some(action => action.key === item.value));
  if (actionItem) {
    listInner.value.push({
      key: actionItem.value,
      value: [dayjs(new Date()).format('YYYY-MM-DD'), dayjs(new Date()).format('YYYY-MM-DD')],
    });
  }
}

function deleteItem(key: string) {
  listInner.value = remove(listInner.value, item => item.key !== key);
}

watch(() => listInner.value, (val) => {
  emit('update:modelValue', val.map(item => toRaw(item)));
}, { deep: true });

watch(() => props.modelValue, (val) => {
  Object.keys(val ?? {}).forEach((key) => {
    if (ACTION_RANGE_LIST.find(item => item.value === key) && val[key].length === 2) {
      if (!listInner.value.some(item => item.key === key)) {
        listInner.value.push({
          key,
          value: val[key],
        });
      }
    }
  });
}, { deep: true, immediate: true });
</script>
<style lang="scss" scoped>
</style>
