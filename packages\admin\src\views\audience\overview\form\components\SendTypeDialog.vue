<template>
  <BaseDialog
    ref="dialogRef"
    title="Create"
    :confirm-loading="isReqLoading"
    @confirm="onConfirm"
  >
    <t-loading
      :loading="isReqLoading"
    >
      <t-radio-group
        class="flex flex-col overflow-hidden"
        :model-value="sendType"
        @update:model-value="(val: string) => setSendType(val)"
      >
        <t-radio
          v-for="item in Object.keys(sendAudienceAbj)"
          :key="item"
          class="inline-block w-full"
          :value="item"
          :disabled="isObject(sendAudienceAbj[item].disabled) && checkDisabled(sendAudienceAbj[item].disabled!)"
        >
          <Text
            class="block mt-[20px]"
            :content="sendAudienceAbj[item].label"
          />
          <Text
            class="block"
            :content="sendAudienceAbj[item].desc"
            color="var(--aix-text-color-black-placeholder)"
          />
        </t-radio>
      </t-radio-group>
    </t-loading>
  </BaseDialog>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { storeToRefs } from 'pinia';
import { camelCase, isObject } from 'lodash-es';
import type { StringKeyAnyValueObject } from 'common/types/report';
import Text from 'common/components/Text';
import BaseDialog from 'common/components/Dialog/Base';

const dialogRef = ref();

const { sendAudienceAbj, sendType, formData, isReqLoading } = storeToRefs(useAixAudienceOverviewFormStore());
const { setSendType } = useAixAudienceOverviewFormStore();

const emit = defineEmits(['confirm']);

defineExpose({
  show: () => dialogRef.value.show(),
  hide: () => dialogRef.value.hide(),
});

function checkDisabled(obj: StringKeyAnyValueObject) {
  Object.keys(obj).some(key => obj[key] === formData.value[camelCase(key)]);
}

function onConfirm() {
  emit('confirm');
  dialogRef.value.hide();
}

</script>
<style lang="scss" scoped>

</style>
