<template>
  <t-form-item
    label="Use for which account"
    name="adAccountId"
  >
    <NewSelect
      class="w-[440px]"
      :model-value="formData.adAccountId"
      :options="accountList"
      placeholder="Select account"
      :disabled="(!isAdd || !isEmpty(accountIdUrl))"
      @update:model-value="(val: string) => setAdAccountId(val)"
    />
  </t-form-item>
</template>
<script lang="ts" setup>
import { computed, watch } from 'vue';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { useAixAudienceOverviewFormQueryStringParamsStore } from '@/store/audience/overview/form/queryStringParams.store';
import { isEmpty, isArray } from 'lodash-es';
import { storeToRefs } from 'pinia';
import NewSelect from 'common/components/NewSelect';
import type { IAudienceFormOptionAccountObj } from 'common/service/audience/overview/type';


const { formData, isAdd, accountObj } = storeToRefs(useAixAudienceOverviewFormStore());
const { setAdAccountId } = useAixAudienceOverviewFormUpdateStore();
const { accountIdUrl } = useAixAudienceOverviewFormQueryStringParamsStore();

// account list: account_obj中存储Facebook account list；root_account_obj 存储Google MCC、Facebook BM
// const accountList = media === 'Facebook' && audienceTypeText === 'Audience' ? getAccountList('Facebook', account_obj)
//     : media === 'Facebook' && audienceTypeText === 'Event' ? getAccountList('Facebook', root_account_obj)
//       : getAccountList('Google', root_account_obj);

const accountList = computed(() => getAccountList(formData.value.media, accountObj.value));

function getAccountList(media: string, obj: IAudienceFormOptionAccountObj) {
  if (isArray(obj[media]) && obj[media].length > 0) {
    return obj[media].map(item => ({
      label: `${item.name}|${item.id}`,
      value: item.id,
      tooltip: item.id,
    }));
  }
  return [];
}

watch(() => accountList.value, () => {
  setAdAccountId('');
}, { deep: true });
</script>
<style lang="scss" scoped>
</style>
