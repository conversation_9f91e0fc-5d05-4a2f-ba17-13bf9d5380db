<template>
  <t-form-item
    label=""
    label-width="0px"
  >
    <AdvancedAction
      ref="appAdvancedActionRef"
      :model-value="{
        purchase_times: formData.purchaseTimes,
        purchase_amount: formData.purchaseAmount
      }"
      @update:model-value="updateAdvancedAction"
    />
  </t-form-item>
</template>
<script lang="ts" setup>
import AdvancedAction from '../../components/AdvancedAction.vue';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import type { IAdvancedActionList } from '@/views/audience/overview/type';
import { storeToRefs } from 'pinia';
import { isFunction } from 'lodash-es';

const { formData, appAdvancedActionRef } = storeToRefs(useAixAudienceOverviewFormStore());
const { setPurchaseTimes, setPurchaseAmount  } = useAixAudienceOverviewFormUpdateStore();

const updateAdvancedActionMap: Record<string, Function> = {
  purchase_times: (val: number[]) => setPurchaseTimes(val),
  purchase_amount: (val: number[]) => setPurchaseAmount(val),
};

function updateAdvancedAction(list: IAdvancedActionList[]) {
  // 清空
  Object.keys(updateAdvancedActionMap).forEach((key) => {
    if (!list.some(item => item.key === key)) {
      updateAdvancedActionMap[key]([]);
    }
  });
  // 赋值
  list.forEach((item) => {
    if (isFunction(updateAdvancedActionMap[item.key])) {
      updateAdvancedActionMap[item.key]([item.values[0], item.values[1]]);
    }
  });
}
</script>
<style lang="scss">
</style>
