<template>
  <t-form-item
    label=""
    label-width="0px"
  >
    <GeneralAction
      :model-value="{
        installDate: formData.installDate,
        registerDate: formData.registerDate,
        activeDate: formData.activeDate,
        uninstallDate: formData.uninstallDate,
        excludeDctiveDate: formData.excludeActiveDate
      }"
      @update:model-value="updateGeneralAction"
    />
  </t-form-item>
</template>
<script lang="ts" setup>
import GeneralAction from '../GeneralAction.vue';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { storeToRefs } from 'pinia';
import { isFunction } from 'lodash-es';
import type { IGeneralActionList } from '@/views/audience/overview/type';

const { formData } = storeToRefs(useAixAudienceOverviewFormStore());

const { setInstallDate, setRegisterDate, setActiveDate,
  setUninstallDate, setExcludeActiveDate } = useAixAudienceOverviewFormUpdateStore();

const updateActionRangeMap: Record<string, Function> = {
  installDate: (val: string[]) => setInstallDate(val),
  registerDate: (val: string[]) => setRegisterDate(val),
  activeDate: (val: string[]) => setActiveDate(val),
  uninstallDate: (val: string[]) => setUninstallDate(val),
  excludeDctiveDate: (val: string[]) => setExcludeActiveDate(val),
};

function updateGeneralAction(list: IGeneralActionList[]) {
  // 清空
  Object.keys(updateActionRangeMap).forEach((key) => {
    if (!list.some(item => item.key === key)) {
      updateActionRangeMap[key]([]);
    }
  });
  // 赋值
  list.forEach((item) => {
    if (isFunction(updateActionRangeMap[item.key])) {
      updateActionRangeMap[item.key](item.value);
    }
  });
}

</script>
