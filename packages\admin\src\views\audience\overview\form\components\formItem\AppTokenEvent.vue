<template>
  <t-form-item
    label-width="180px"
    label=""
  >
    <div class="flex items-center">
      <t-form-item label="APP token" label-width="100px">
        <t-input
          :model-value="formData.appToken"
          max-length="64"
          disabled
          @update:model-value="(val: string) => setAppToken(val)"
        />
      </t-form-item>
      <t-form-item label="Event token" label-width="100px" name="eventToken">
        <t-input
          :model-value="formData.eventToken"
          placeholder="You can get from Adjust."
          max-length="64"
          @update:model-value="(val: string) => setEventToken(val)"
        />
      </t-form-item>
    </div>
  </t-form-item>
</template>
<script lang="ts" setup>
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';

import { storeToRefs } from 'pinia';
const { formData } = storeToRefs(useAixAudienceOverviewFormStore());
const { setAppToken, setEventToken } = useAixAudienceOverviewFormUpdateStore();
</script>
<style lang="scss" scoped>
</style>

