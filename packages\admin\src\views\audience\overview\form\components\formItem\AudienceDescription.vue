<template>
  <t-form-item label="Audience description" name="remark">
    <t-input
      :model-value="formData.remark"
      class="w-[440px]"
      placeholder=""
      @update:model-value="(val: string) => setRemark(val)"
    />
  </t-form-item>
</template>
<script lang="ts" setup>
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { storeToRefs } from 'pinia';

const { formData } = storeToRefs(useAixAudienceOverviewFormStore());
const { setRemark } = useAixAudienceOverviewFormUpdateStore();

</script>
