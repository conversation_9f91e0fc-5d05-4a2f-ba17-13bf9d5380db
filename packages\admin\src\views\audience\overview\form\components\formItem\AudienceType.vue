<template>
  <t-form-item label="Audience type" name="audienceType">
    <t-radio-group
      v-if="audienceTypeListInner.length > 0"
      :model-value="formData.audienceType"
      :disabled="!isAdd"
      @update:model-value="(val: string) => setAudienceType(val)"
    >
      <t-radio
        v-for="item in audienceTypeListInner"
        :key="item.value"
        :value="item.value"
        :disabled="item.disabled"
      >
        {{ item.text }}
      </t-radio>
    </t-radio-group>
  </t-form-item>
</template>
<script lang="ts" setup>
import { isArray, isEmpty } from 'lodash-es';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { useAixAudienceOverviewFormQueryStringParamsStore } from '@/store/audience/overview/form/queryStringParams.store';
import { storeToRefs } from 'pinia';
import type { IAudienceFormOptionAudienceTypeList } from 'common/service/audience/overview/type';
import { watch, computed } from 'vue';

const { formData, audienceTypeList, isAdd } = storeToRefs(useAixAudienceOverviewFormStore());
const { audienceTypeUrl } = useAixAudienceOverviewFormQueryStringParamsStore();
const { setAudienceType } = useAixAudienceOverviewFormUpdateStore();

const audienceTypeListInner = computed(() => getAudienceTypeListInner(
  audienceTypeList.value,
  formData.value.media,
  audienceTypeUrl,
));

function getAudienceTypeListInner(list: IAudienceFormOptionAudienceTypeList, media: string, url: string) {
  if (!list[media] || !isArray(list[media])) {
    return [];
  }
  return list[media].map(item => ({
    ...item,
    disabled: item.disabled || (!isEmpty(url) && url !== item.text.toLocaleLowerCase()),
  }));
}

// 当Audience type 出现禁用项时，应该选中第一个没有禁用的
watch(() => audienceTypeListInner.value, (val) => {
  if (val.some(item =>  item.disabled)) {
    const defaultActiveType = val.find(item => !item.disabled);
    if (defaultActiveType) {
      setAudienceType(defaultActiveType.value);
    }
  }
}, { deep: true });

watch(() => formData.value.media, () => {
  if (!formData.value.audienceType) {
    setAudienceType(audienceTypeListInner.value[0].value || '');
  }
}, { deep: true });

</script>
<style lang="scss" scoped>
</style>
