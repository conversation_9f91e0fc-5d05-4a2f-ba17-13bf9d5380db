<template>
  <t-form-item label="Block Alarm Msg">
    <t-radio-group
      :model-value="formData.blockAlarms"
      @update:model-value="(val: number) => setBlockAlarms(val)"
    >
      <t-radio :value="0">False</t-radio>
      <t-radio :value="1">True</t-radio>
    </t-radio-group>
  </t-form-item>
</template>
<script lang="ts" setup>
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { storeToRefs } from 'pinia';

const { formData } = storeToRefs(useAixAudienceOverviewFormStore());
const { setBlockAlarms } = useAixAudienceOverviewFormUpdateStore();
</script>
