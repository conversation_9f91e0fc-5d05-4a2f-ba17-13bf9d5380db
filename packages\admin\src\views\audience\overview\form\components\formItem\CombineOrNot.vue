<template>
  <t-form-item
    label="Combine or not"
    name="isCombine"
    :disabled="!isAdd"
  >
    <t-radio-group
      :model-value="formData.isCombine"
      @update:model-value="(val: number) => setIsCombine(val)"
    >
      <t-radio :value="1">Combine</t-radio>
      <t-radio :value="0">Not combine</t-radio>
    </t-radio-group>
  </t-form-item>
</template>
<script lang="ts" setup>
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { storeToRefs } from 'pinia';

const { formData, isAdd } = storeToRefs(useAixAudienceOverviewFormStore());

const { setIsCombine } = useAixAudienceOverviewFormUpdateStore();
</script>

<style lang="scss" scoped>
</style>
