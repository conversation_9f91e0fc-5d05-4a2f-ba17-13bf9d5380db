<template>
  <t-form-item label="Country/Market" name="country">
    <t-select
      :model-value="formData.country"
      class="w-[440px]"
      :options="optionsInner"
      placeholder="Please select"
      :disabled="!isAdd"
      multiple
      @update:model-value="(val: string[]) => setCountry(val)"
    >
      <template #panelTopContent>
        <div class="p-[8px] bg-white-primary">
          <t-input v-model="keyWords" placeholder="" clearable />
        </div>
      </template>
      <template #valueDisplay="{ value }">
        <div v-if="value.length === locationList.length" class="h-[36px] flex items-center px-[8px]">All</div>
        <div v-if="value.length > 0 && value.length < locationList.length">
          <div
            class="truncate h-[36px] flex items-center px-[8px]"
          >
            <!-- <t-tooltip placement="right" :content="value.map((tip: IOptionItem) => tip.label).join(',')">
            <div
              class="cursor-pointer truncate"
            > -->
            <!-- </div>
            </t-tooltip> -->
            {{ value.map((tip: IOptionItem) => tip.label).join(',') }}
          </div>
        </div>
      </template>
    </t-select>
  </t-form-item>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { storeToRefs } from 'pinia';
import type { IOptionItem } from '@/store/audience/overview/type';

const { formData, locationList, isAdd } = storeToRefs(useAixAudienceOverviewFormStore());
const { setCountry } = useAixAudienceOverviewFormUpdateStore();

const keyWords = ref<string>('');

const optionsInner = computed(()  => getOptionsInner(keyWords.value, locationList.value));

function getOptionsInner(content: string, list: IOptionItem[]) {
  const filterList = list.filter(item => item.label.toLocaleLowerCase().includes(content.toLocaleLowerCase()));
  return filterList.length === 0 ? [] : [
    { label: 'All', checkAll: true },
    ...filterList,
  ];
}
</script>
