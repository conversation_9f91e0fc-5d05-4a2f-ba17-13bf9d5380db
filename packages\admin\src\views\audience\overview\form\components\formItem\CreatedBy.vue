<template>
  <t-form-item
    label="Created By"
    required-mark
  >
    <t-radio-group
      :model-value="formData.createby"
      :disabled="!isAdd"
      @update:model-value="(val: string) => setCreateby(val)"
    >
      <t-radio
        v-for="item in createdByListInner"
        :key="item.value"
        :value="item.value"
        :disabled="item.disabled"
      >
        {{ item.text }}
      </t-radio>
    </t-radio-group>
  </t-form-item>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import { isEmpty } from 'lodash-es';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { getIdTypeList } from '@/store/audience/overview/utils/get';
import { useGlobalGameStore } from '@/store/global/game.store';

import { storeToRefs } from 'pinia';
const { formData, createdByList, isAdd, idTypeList } = storeToRefs(useAixAudienceOverviewFormStore());
const { setCreateby } = useAixAudienceOverviewFormUpdateStore();
const { isPcDemoGame } = useGlobalGameStore();

// 判断禁用的逻辑 调用createdByList遍历调用getIdTypeList方法
// createby参数传createdByList每一项的value
function checkDisabled(os: string, createby: string, audienceType: string, media: string, idType: string) {
  const isNotEmptyInner = (
    !isEmpty(os) && !isEmpty(createby) && !isEmpty(audienceType) && !isEmpty(media) && !isEmpty(idType)
  );
  const idTypeListInner = getIdTypeList(os, createby, audienceType, media);
  return (
    !idTypeListInner.some(item => item.value === idType) && isNotEmptyInner
    && idTypeList.value.some(item => item.value === idType)
  );
}

const createdByListInner = computed(() => {
  const list = createdByList.value.map(item => (
    {
      ...item,
      disabled: checkDisabled(
        formData.value.os,
        item.value,
        formData.value.audienceType,
        formData.value.media,
        formData.value.idType,
      ),
    }
  ));
  if (isPcDemoGame()) {
    return list.filter(item => item.value !== 'rules');
  }
  return list;
});
</script>
<style lang="scss" scoped>
</style>

