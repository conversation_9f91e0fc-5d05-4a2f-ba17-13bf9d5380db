<template>
  <t-form-item>
    <template #label>
      <div class="flex justify-start items-center">
        Event value
        <t-tooltip theme="light">
          <template #content>
            <Text
              size="small"
              content="Once enabled, the event will return with a value parameter.
              This event value can be utilized for campaigns with value bidding."
            />
          </template>
          <div class="ml-[8px] cursor-pointer">
            <IconFont
              name="info-circle"
            />
          </div>
        </t-tooltip>
      </div>
    </template>
    <t-switch
      :model-value="formData.openEventValue"
      :custom-value="[1, 0]"
      @update:model-value="(val: any) => setOpenEventValue(val)"
    />
  </t-form-item>
</template>
<script lang="ts" setup>
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { storeToRefs } from 'pinia';
import { IconFont } from 'tdesign-icons-vue-next';
import Text from 'common/components/Text';

const { formData } = storeToRefs(useAixAudienceOverviewFormStore());

const { setOpenEventValue } = useAixAudienceOverviewFormUpdateStore();

</script>
<style lang="scss" scoped>
</style>
