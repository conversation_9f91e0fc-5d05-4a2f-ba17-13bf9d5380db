<template>
  <t-form-item
    label="ID Type"
    name="idType"
  >
    <t-radio-group
      :model-value="formData.idType"
      :disabled="!isAdd"
      @update:model-value="(val: string) => setIdType(val)"
    >
      <t-radio
        v-for="item in idTypeList"
        :key="item.value"
        :value="item.value"
      >
        {{ item.label }}
      </t-radio>
    </t-radio-group>
  </t-form-item>
</template>
<script lang="ts" setup>
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { storeToRefs } from 'pinia';

const { formData, isAdd, idTypeList } = storeToRefs(useAixAudienceOverviewFormStore());
const { setIdType } = useAixAudienceOverviewFormUpdateStore();

</script>
