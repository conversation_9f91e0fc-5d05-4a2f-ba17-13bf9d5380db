<template>
  <t-form-item
    label="Membership duration"
    :name="formData.openUserTtl ? 'userTtl' : ''"
  >
    <div class="mt-[6px]">
      <t-switch
        :model-value="formData.openUserTtl"
        :custom-value="[1, 0]"
        :disabled="isDisDuration"
        @update:model-value="(val: boolean) => setOpenUserTtl(val)"
      />
      <div v-if="formData.openUserTtl" class="mt-[8px] w-[440px]">
        <t-input-adornment append="days">
          <t-input-number
            class="w-full"
            :model-value="formData.userTtl"
            theme="normal"
            placeholder="1~180"
            @update:model-value="(val: string | number) => setUserTtl(val)"
          />
        </t-input-adornment>
      </div>
    </div>
  </t-form-item>
</template>
<script lang="ts" setup>
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { useAixAudienceOverviewFormVisible } from '@/store/audience/overview/form/visible.store';
import { storeToRefs } from 'pinia';

const { formData } = storeToRefs(useAixAudienceOverviewFormStore());
const { isDisDuration } = storeToRefs(useAixAudienceOverviewFormVisible());
const { setOpenUserTtl, setUserTtl } = useAixAudienceOverviewFormUpdateStore();
</script>
