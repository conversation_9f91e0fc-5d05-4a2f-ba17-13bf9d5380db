<template>
  <t-form-item
    label="Model Name"
    label-width="100px"
    name="modelName"
    :required-mark="false"
  >
    <NewSelect
      :model-value="formData.modelName"
      class="w-[360px]"
      :options="modelNameList"
      placeholder="Please select model"
      @update:model-value="(val: string) => setModelName(val)"
    />
  </t-form-item>
</template>
<script lang="ts" setup>
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { storeToRefs } from 'pinia';
import NewSelect from 'common/components/NewSelect';

const { modelNameList, formData } = storeToRefs(useAixAudienceOverviewFormStore());
const { setModelName } = useAixAudienceOverviewFormUpdateStore();

</script>
<style lang="scss"  scoped>
</style>
