<template>
  <t-form-item name="name" class="form-item-audience_name">
    <template #label>
      <div class="flex justify-start items-center">
        {{ isSpecialName ? `${formData.media} Event Name` : "Audience name" }}
        <t-tooltip
          v-if="isSpecialName"
          theme="light"
        >
          <template #content>
            <Text
              size="small"
              :content="`上传成功后，${formData.media}面板上展示的name即为此处填写的name`"
            />
          </template>
          <div class="ml-[8px] cursor-pointer">
            <IconFont
              name="info-circle"
            />
          </div>
        </t-tooltip>
      </div>
    </template>
    <t-input
      :model-value="formData.name"
      class="w-[440px]"
      placeholder="Game_media_type_modelname_frequency"
      max-length="64"
      :disabled="!isAdd"
      @update:model-value="(val: string) => setName(val)"
    />
  </t-form-item>
</template>
<script lang="ts" setup>
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { useAixAudienceOverviewFormVisible } from '@/store/audience/overview/form/visible.store';
import { storeToRefs } from 'pinia';
import { IconFont } from 'tdesign-icons-vue-next';
import Text from 'common/components/Text';

const { formData, isAdd } = storeToRefs(useAixAudienceOverviewFormStore());
const { setName } = useAixAudienceOverviewFormUpdateStore();

const { isSpecialName } = storeToRefs(useAixAudienceOverviewFormVisible());
</script>

<style lang="scss" scoped>
.form-item-audience_name {
  :deep(.t-form__label--required ){
    label {
      display: flex;
      align-items: center;
    }
  }
}
</style>
