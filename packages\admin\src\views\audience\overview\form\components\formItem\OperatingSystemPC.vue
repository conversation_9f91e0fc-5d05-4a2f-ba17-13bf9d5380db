<template>
  <t-form-item label="Data source" name="os">
    <t-radio-group
      :model-value="formData.os"
      :disabled="(!isAdd || !isEmpty(osUrl))"
      @update:model-value="(val: string) => setOs(val)"
    >
      <t-radio value="Web">Web</t-radio>
    </t-radio-group>
  </t-form-item>
</template>
<script lang="ts" setup>
import { isEmpty } from 'lodash-es';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { useAixAudienceOverviewFormQueryStringParamsStore } from '@/store/audience/overview/form/queryStringParams.store';
import { storeToRefs } from 'pinia';
const { formData, isAdd } = storeToRefs(useAixAudienceOverviewFormStore());
const { osUrl } = useAixAudienceOverviewFormQueryStringParamsStore();
const { setOs } = useAixAudienceOverviewFormUpdateStore();


</script>
