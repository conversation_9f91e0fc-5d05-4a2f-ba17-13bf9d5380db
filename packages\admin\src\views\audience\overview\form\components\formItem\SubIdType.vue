<template>
  <t-form-item
    label="Sub ID Type"
    name="subIdType"
  >
    <t-radio-group
      :model-value="formData.subIdType"
      :disabled="!isAdd"
      @update:model-value="(val: string) => setSubIdType(val)"
    >
      <t-radio
        v-for="item in subIdTypeList"
        :key="item.value"
        :value="item.value"
      >
        {{ item.label }}
      </t-radio>
    </t-radio-group>
  </t-form-item>
</template>
<script lang="ts" setup>
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { storeToRefs } from 'pinia';

const { formData, isAdd, subIdTypeList } = storeToRefs(useAixAudienceOverviewFormStore());
const { setSubIdType } = useAixAudienceOverviewFormUpdateStore();

</script>
<style lang="scss" scoped>
</style>
