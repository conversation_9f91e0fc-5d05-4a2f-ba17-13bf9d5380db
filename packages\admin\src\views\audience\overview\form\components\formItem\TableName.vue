<template>
  <t-form-item label="Table name" name="tableName">
    <DescNote class="w-[440px]" />
  </t-form-item>
</template>
<script lang="ts" setup>
import DescNote from '../DescNote.vue';
// import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
// import { storeToRefs } from 'pinia';

// const { isAdd } = storeToRefs(useAixAudienceOverviewFormStore());
</script>
