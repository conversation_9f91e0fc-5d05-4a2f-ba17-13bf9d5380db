<template>
  <t-form-item
    label="Test setting"
    :name="formData.openTest ? 'testParam' : ''"
  >
    <div class="mt-[6px]">
      <t-switch
        :model-value="formData.openTest"
        :custom-value="[1, 0]"
        :disabled="isDisTesting"
        @update:model-value="(val: any) => setOpenTest(val)"
      />
      <div v-if="formData.openTest" class="mt-[8px] w-[440px]">
        <t-input-adornment class="w-full" append="%">
          <t-input-number
            class="w-full"
            theme="normal"
            :model-value="formData.testParam"
            placeholder=""
            :decimal-places="0"
            @update:model-value="(val: number) => setTestParam(val)"
          />
        </t-input-adornment>
      </div>
    </div>
  </t-form-item>
</template>
<script lang="ts" setup>
import { watch } from 'vue';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { useAixAudienceOverviewFormVisible } from '@/store/audience/overview/form/visible.store';
import { storeToRefs } from 'pinia';

const { formData } = storeToRefs(useAixAudienceOverviewFormStore());
const { setOpenTest, setTestParam } = useAixAudienceOverviewFormUpdateStore();

const { isDisTesting } = storeToRefs(useAixAudienceOverviewFormVisible());

watch(() => formData.value.openTest, (val) => {
  if (!val) {
    setTestParam(10);
  }
}, { deep: true, immediate: true });
</script>
