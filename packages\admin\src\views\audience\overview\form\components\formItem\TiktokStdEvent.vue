<template>
  <t-form-item
    label="Standard event"
    name="tiktokStdEvent"
  >
    <NewSelect
      class="w-[440px]"
      :model-value="formData.tiktokStdEvent"
      :options="standardEventList(formData.media, standardEvent)"
      placeholder="Select standard event"
      :disabled="!isAdd"
      @update:model-value="(val: string) => setTiktokStdEvent(val)"
    />
  </t-form-item>
</template>
<script lang="ts" setup>
import { watch } from 'vue';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import NewSelect from 'common/components/NewSelect';
import { storeToRefs } from 'pinia';
import type { IStandardEvent } from 'common/service/audience/overview/type';

const { formData, isAdd, standardEvent } = storeToRefs(useAixAudienceOverviewFormStore());
const { setTiktokStdEvent } = useAixAudienceOverviewFormUpdateStore();

function standardEventList(categoryLabel: string, sourceArray: IStandardEvent) {
  if (categoryLabel in sourceArray) {
    return sourceArray[categoryLabel];
  }
  return [];
}

watch(() => standardEvent.value, () => {
  setTiktokStdEvent('');
}, { deep: true });

</script>
<style lang="scss" scoped>
</style>

