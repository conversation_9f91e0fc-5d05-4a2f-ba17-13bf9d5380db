<template>
  <t-form-item
    label="Update frequency"
    name="updateFrequencyFormItem"
    required-mark
  >
    <t-select
      :model-value="updateFrequency"
      class="w-[440px]"
      :disabled="!isAdd"
      :options="optionsInner"
      placeholder="Please select model first"
      @update:model-value="(val: string) => updateModelValueFn(val)"
    />
  </t-form-item>
</template>
<script lang="ts" setup>
import { computed, ComputedRef, watch } from 'vue';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { storeToRefs } from 'pinia';
import type { IOptionItem } from '@/store/audience/overview/type';
import { updateFrequencyModelValueMap, updateFrequencyMap } from '@/views/audience/overview/form/const';
const { formData, frequencyListObj, isAdd, requencyList } = storeToRefs(useAixAudienceOverviewFormStore());
const { setUpdateFrequencyFormItem } = useAixAudienceOverviewFormUpdateStore();

const updateFrequency = computed(() => updateFrequencyModelValueMap()[formData.value.createby] || '');


function updateModelValueFn(val: string) {
  if (updateFrequencyMap()[formData.value.createby]) {
    updateFrequencyMap()[formData.value.createby](val);
  }
}

// 若 finalModelName有值 且 命中frequency_list_obj ，则直接取
// 若 finalModelName有值 但 不命中 frequency_list_obj ，则取 default的值
// 若 finalModelName无值，则无值
const frequencyOptions = computed(() => getFrequencyOptions(frequencyListObj.value, formData.value.modelName));
function getFrequencyOptions(obj: typeof frequencyListObj.value, modelName: string) {
  if (modelName) {
    const list = obj[modelName] ?? (obj.default || []);
    return list.map(item => ({
      label: item.text,
      value: item.value,
    }));
  }
  return [];
}

const optinosMap: Record<string, ComputedRef<IOptionItem[]>> = {
  modeling: frequencyOptions,
  rules: computed(() => requencyList.value.filter(item => item.value !== 'hourly')),
  sql: computed(() => requencyList.value),
};

const optionsInner =  computed(() => optinosMap[formData.value.createby].value || []);

watch(() => [formData.value.createby, updateFrequency.value], (val) => {
  setUpdateFrequencyFormItem({
    createby: val[0] as string,
    updateFrequency: val[1] as string,
  });
}, { deep: true, immediate: true });

</script>
