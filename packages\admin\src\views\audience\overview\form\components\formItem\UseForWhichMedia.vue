<template>
  <t-form-item label="Use for which Media" name="media">
    <t-radio-group
      :model-value="formData.media"
      :disabled="!isAdd || !isEmpty(mediaUrl)"
      @update:model-value="(val: string) => setMedia(val)"
    >
      <t-radio
        v-for="item in mediaList"
        :key="item.value"
        :value="item.value"
        :disabled="!isEmpty(mediaUrl) && mediaUrl !== item.value"
      >
        {{ item.text }}
      </t-radio>
    </t-radio-group>
  </t-form-item>
</template>
<script lang="ts" setup>
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { useAixAudienceOverviewFormQueryStringParamsStore } from '@/store/audience/overview/form/queryStringParams.store';
// import type { IAudienceFormOptionMediaList } from 'common/service/audience/overview/type';
import { isEmpty } from 'lodash-es';

import { storeToRefs } from 'pinia';
const { formData, mediaList, isAdd } = storeToRefs(useAixAudienceOverviewFormStore());
const { setMedia } = useAixAudienceOverviewFormUpdateStore();

const { mediaUrl } = useAixAudienceOverviewFormQueryStringParamsStore();

// watch(() => [mediaUrl, mediaList.value], (val) => {
//   if (!isEmpty(val[0]) && isString(val[0])) {
//     if ((val[1] as IAudienceFormOptionMediaList[]).some(item => item.text === val[0])) {
//     }
//   }
// }, { deep: true, immediate: true });
</script>
<style lang="scss" scoped>
</style>
