<template>
  <t-form-item
    label="User Range"
    label-width="100px"
    name="userRangeFormItem"
  >
    <t-select
      :model-value="formData.percentScoreLower"
      class="w-[165px]"
      :options="scoreList"
      placeholder="Please select"
      @update:model-value="(val: number) => setPercentScoreLower(val)"
    />
    <Text
      content="To"
      class="mx-[8px]"
    />
    <t-select
      :model-value="formData.percentScore"
      class="w-[165px]"
      :options="scoreList"
      placeholder="Please select"
      @update:model-value="(val: number) => setPercentScore(val)"
    />
    <!-- <t-input v-model="userRnageInner" />s -->
  </t-form-item>
</template>
<script lang="ts" setup>
import { watch } from 'vue';
import Text from 'common/components/Text';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { storeToRefs } from 'pinia';

const { scoreList, formData } = storeToRefs(useAixAudienceOverviewFormStore());

const { setPercentScoreLower, setPercentScore, setUserRangeFormItem } = useAixAudienceOverviewFormUpdateStore();


watch(() => [formData.value.percentScoreLower, formData.value.percentScore], (val) => {
  setUserRangeFormItem(val);
}, { deep: true, immediate: true });
</script>

<style lang="scss">
</style>
