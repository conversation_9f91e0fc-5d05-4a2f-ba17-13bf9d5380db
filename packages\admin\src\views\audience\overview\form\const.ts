import { computed, ComputedRef } from 'vue';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { storeToRefs } from 'pinia';
import { checkUserRange, checkTestParam,
  checkAudienceName, checkUpdateFrequency, checkUserTtl } from './utils';


export const SHOW_VALUE_MEDIA = ['Google', 'Facebook', 'Appsflyer', 'Adjust'];

export const ACTION_RANGE_LIST = [
  {
    label: 'Install',
    value: 'installDate',
  },
  {
    label: 'Register',
    value: 'registerDate',
  },
  {
    label: 'Active',
    value: 'activeDate',
  },
  {
    label: 'Uninstall',
    value: 'uninstallDate',
  },
  {
    label: 'Exclude-active',
    value: 'excludeDctiveDate',
  },
];


export const updateFrequencyModelValueMap = (): Record<string, ComputedRef<string>> => {
  const { formData } = storeToRefs(useAixAudienceOverviewFormStore());
  return {
    modeling: computed(() => formData.value.modelingUpdateFrequency),
    rules: computed(() => formData.value.rulesUpdateFrequency),
    sql: computed(() => formData.value.sqlUpdateFrequency),
  };
};

export const updateFrequencyMap = (): Record<string, (val: string) => void> => {
  const { setModelingUpdateFrequency, setRulesUpdateFrequency,
    setSqlUpdateFrequency } = useAixAudienceOverviewFormUpdateStore();
  return {
    modeling: val => setModelingUpdateFrequency(val),
    rules: val => setRulesUpdateFrequency(val),
    sql: val => setSqlUpdateFrequency(val),
  };
};

export const FORM_RULES = {
  modelName: [
    { required: true, message: 'Please complete the required field.', type: 'error', trigger: 'change' },
  ],
  subIdType: [
    // { required: true, message: '请将该必填项补充完整', type: 'error', trigger: 'change' },
  ],
  idType: [
    { required: true, message: 'Please complete the required field.', type: 'error', trigger: 'change' },
  ],
  userRangeFormItem: [
    { validator: checkUserRange, trigger: 'change' },
  ],
  newTarget: [
    { required: true, message: 'Please select an Optimization target.', type: 'error', trigger: 'change' },
  ],
  reTarget: [
    { required: true, message: 'Please select an Optimization target.', type: 'error', trigger: 'change' },
  ],
  testParam: [
    { validator: checkTestParam, trigger: 'change' },
  ],
  name: [
    {
      required: true,
      message: 'The Audience name field cannot be blank and must contain 39 characters with English letters, numbers, and underscores only.',
      type: 'error',
      trigger:
      'change',
    },
    { validator: checkAudienceName, trigger: 'blur' },
  ],
  updateFrequencyFormItem: [
    { validator: checkUpdateFrequency, trigger: 'change' },
  ],
  audienceType: [
    { required: true, message: 'Please complete the required field.', type: 'error', trigger: 'change' },
  ],
  os: [
    { required: true, message: 'Please select Data source first.', type: 'error', trigger: 'change' },
  ],
  tiktokStdEvent: [
    { required: true, message: 'Please select the standard event first.', type: 'error', trigger: 'change' },
  ],
  adAccountId: [
    { required: true, message: 'Please enter an account ID.', type: 'error', trigger: 'change' },
  ],
  media: [
    { required: true, message: 'Please select a media.', type: 'error', trigger: 'change' },
  ],
  eventToken: [
    { required: true, message: 'Please complete the required field.', type: 'error', trigger: 'blur' },
  ],
  tableName: [
    { required: true, message: 'Please select a table name.', type: 'error', trigger: 'change' },
  ],
  userTtl: [
    { validator: checkUserTtl, trigger: 'change' },
  ],
};


export const OPTIMIZATION_TARGET_LIST = [
  {
    label: 'New Install',
    value: 'new_install',
  },
  {
    label: 'Re-attribution',
    value: 're_attribution',
  },
];
