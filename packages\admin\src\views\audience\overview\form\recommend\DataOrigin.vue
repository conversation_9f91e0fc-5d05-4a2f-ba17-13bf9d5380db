<template>
  <div>
    <!-- common -->
    <CreatedBy />
    <!-- Modeling -->
    <template v-if="formData.createby === 'modeling'">
      <ModelName
        class="ml-[180px]"
      />
      <UserRange
        class="ml-[180px]"
      />
      <EventValue
        v-if="isShowEventValue"
      />
    </template>
    <!-- Custom Table -->
    <template v-if="formData.createby === 'sql'">
      <TableName />
    </template>
    <UpdateFrequency :key="formData.createby" />
    <MembershipDuration
      v-if="isShowDuration"
    />
    <Name />
    <AudienceDescription />
    <TestSetting
      v-if="isShowTesting"
    />
    <Country v-if="formData.createby !== 'sql'" />
    <!-- Rule-based -->
  </div>
</template>
<script lang="ts" setup>
import CreatedBy from '../components/formItem/CreatedBy.vue';
import ModelName from '../components/formItem/ModelName.vue';
import UserRange from '../components/formItem/UserRange.vue';
import UpdateFrequency from '../components/formItem/UpdateFrequency.vue';
import EventValue from '../components/formItem/EventValue.vue';
import MembershipDuration from '../components/formItem/MembershipDuration.vue';
import Name from '../components/formItem/Name.vue';
import AudienceDescription from '../components/formItem/AudienceDescription.vue';
import TestSetting from '../components/formItem/TestSetting.vue';
import Country from '../components/formItem/Country.vue';
import TableName from '../components/formItem/TableName.vue';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormVisible } from '@/store/audience/overview/form/visible.store';
import { storeToRefs } from 'pinia';

const { formData } = storeToRefs(useAixAudienceOverviewFormStore());
const { isShowDuration, isShowTesting, isShowEventValue } = storeToRefs(useAixAudienceOverviewFormVisible());

</script>
<style lang="scss" scoped>
</style>
