<template>
  <div>
    <UseForWhichMedia />
    <AppTokenEvent
      v-if="formData.media === 'Adjust'"
    />
    <AdAccountId
      v-if="!['Appsflyer', 'Adjust'].includes(formData.media)"
    />
  </div>
</template>
<script lang="ts" setup>
import AppTokenEvent from '../components/formItem/AppTokenEvent.vue';
import AdAccountId from '../components/formItem/AdAccountId.vue';
import UseForWhichMedia from '../components/formItem/UseForWhichMedia.vue';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { storeToRefs } from 'pinia';

const { formData } = storeToRefs(useAixAudienceOverviewFormStore());
</script>
<style lang="scss" scoped>

</style>
