<template>
  <div>
    <AudienceType />
    <OperatingSystemPC v-if="isPcGame()" />
    <OperatingSystemMobile v-if="isMobileGame()" />
    <TiktokStdEvent v-if="isShowTiktokStdEvent" />
    <IdType
      v-if="isShowIdTypeInner"
    />
    <SubIdType
      v-if="isShowSubIdTypeInner"
    />
  </div>
</template>
<script lang="ts" setup>
import { computed, watch } from 'vue';
import { isEmpty } from 'lodash-es';
import AudienceType from '../components/formItem/AudienceType.vue';
import OperatingSystemMobile from '../components/formItem/OperatingSystemMobile.vue';
import OperatingSystemPC from '../components/formItem/OperatingSystemPC.vue';
import IdType from '../components/formItem/IdType.vue';
import SubIdType from '../components/formItem/SubIdType.vue';
import { useAixAudienceOverviewFormVisible } from '@/store/audience/overview/form/visible.store';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewStore } from '@/store/audience/overview/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { useGlobalGameStore } from '@/store/global/game.store';
import type { IAudienceTable } from 'common/service/audience/overview/type';
import { storeToRefs } from 'pinia';
import TiktokStdEvent from '../components/formItem/TiktokStdEvent.vue';

const { isShowIdType, isShowSubIdType, isShowTiktokStdEvent } = storeToRefs(useAixAudienceOverviewFormVisible());
const { idTypeList, isAdd, subIdTypeList, formData } = storeToRefs(useAixAudienceOverviewFormStore());
const { audienceTableRowState } = storeToRefs(useAixAudienceOverviewStore());
const { setIdType, setSubIdType } = useAixAudienceOverviewFormUpdateStore();
const {  isMobileGame, isPcGame } = useGlobalGameStore();

console.log('isShowTiktokStdEvent', formData);
// 编辑时的id type是不是空
const isNotEmptyIdType = computed(() => !isEmpty((audienceTableRowState.value as IAudienceTable)?.id_type));
// 编辑时的id type 或者在idTypeList中是否存在
const isIdTypeExists = computed(() => (
  idTypeList.value.some(item => item.value === (audienceTableRowState.value as IAudienceTable)?.id_type)
));

// 编辑时的sub_id_type是不是为空
const isNotEmptyId = computed(() => !isEmpty((audienceTableRowState.value as IAudienceTable)?.sub_id_type));
// 编辑时的sub_id_type 在不在 subIdTypeList
const isSubIdTypeExists = computed(() => (
  subIdTypeList.value.some(item => item.value === (audienceTableRowState.value as IAudienceTable)?.sub_id_type)
));

const isShowIdTypeInner = computed(() => (
  isAdd.value ? isShowIdType.value
    : (isShowIdType.value && isNotEmptyIdType.value && isIdTypeExists.value)
));

const isShowSubIdTypeInner = computed(() => (
  isAdd.value ? isShowSubIdType.value
    : (isNotEmptyId.value && isSubIdTypeExists.value)
));

watch(() => [...idTypeList.value], (val) => {
  if (!val.some(item => item.value === formData.value.idType) && isAdd.value) {
    if (val.length > 0) {
      setIdType(val[0].value);
    } else {
      setIdType('');
    }
  }
}, { deep: true });

watch(() => [...subIdTypeList.value], (val) => {
  if (!val.some(item => item.value === formData.value.subIdType)) {
    setSubIdType('');
  }
}, { deep: true });
</script>
<style lang="scss" scoped>
</style>
