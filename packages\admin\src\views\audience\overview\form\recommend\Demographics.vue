<template>
  <div class="bg-white-primary rounded-large p-[16px] min-w-min">
    <Text
      class="block mb-[8px]"
      size="xl"
      weight="500"
      content="Actions"
    />
    <AppGeneralAction />
    <!-- hasAdvancedAction.includes(gameCode) -->
    <template v-if="hasAdvancedAction.includes(gameCode) ">
      <Text
        class="block mb-[8px] mt-[8px]"
        size="xl"
        weight="500"
        content="APP Advanced Action"
      />
      <AppAdvancedAction />
    </template>
    <template v-if="hasProfile.includes(gameCode)">
      <Text
        class="block mb-[8px] mt-[8px]"
        size="xl"
        weight="500"
        content="Profile"
      />
      <t-radio-group
        v-if="profileValueListInner.length > 0"
        :model-value="formData.profile"
        :disabled="!isAdd"
        @update:model-value="(val: string) => setProfile(val)"
      >
        <t-radio
          v-for="item in profileValueListInner"
          :key="item.value"
          :value="item.value"
        >
          {{ item.text }}
        </t-radio>
      </t-radio-group>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import { isArray } from 'lodash-es';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewFormUpdateStore } from '@/store/audience/overview/form/update.store';
import { useGlobalGameStore } from '@/store/global/game.store';
import Text from 'common/components/Text';
import AppGeneralAction from '../components/formItem/AppGeneralAction.vue';
import AppAdvancedAction from '../components/formItem/AppAdvancedAction.vue';
import type { IAudienceFormOptionProfileValueList } from 'common/service/audience/overview/type';
import { storeToRefs } from 'pinia';

const { hasAdvancedAction, formData, isAdd, hasProfile,
  profileValueList } = storeToRefs(useAixAudienceOverviewFormStore());

const { gameCode } = storeToRefs(useGlobalGameStore());
const { setProfile } = useAixAudienceOverviewFormUpdateStore();
const profileValueListInner = computed(() => getProFileListInner(profileValueList.value, gameCode.value));

function getProFileListInner(listObj: IAudienceFormOptionProfileValueList, game: string) {
  if (!listObj[game] || !isArray(listObj[game])) {
    return [];
  }
  return listObj[game];
}
</script>
<style lang="scss" scoped>
</style>
