<template>
  <t-form
    ref="recommendFormRef"
    label-width="180px"
    label-align="left"
    :data="formData"
    :rules="FORM_RULES"
  >
    <div class="setting bg-white-primary rounded-large p-[16px] min-w-min">
      <Text
        class="block mb-[8px]"
        size="xl"
        weight="500"
        content="Setting"
      />
      <DataRemote />
      <DataType />
      <DataOrigin />
      <!-- <TaskInfo /> -->
    </div>
    <Demographics
      v-if="formData.createby === 'rules' && !isPcDemoGame()"
      class="mt-[24px]"
    />
  </t-form>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import Text from 'common/components/Text';
import DataRemote from './DataRemote.vue';
import DataType from './DataType.vue';
import DataOrigin from './DataOrigin.vue';
// import TaskInfo from './TaskInfo.vue';
import Demographics from './Demographics.vue';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { storeToRefs } from 'pinia';
import { FORM_RULES } from '../const';

const { isPcDemoGame } = useGlobalGameStore();
const { formData } = storeToRefs(useAixAudienceOverviewFormStore());

const recommendFormRef = ref();

defineExpose({
  validate: () => recommendFormRef?.value?.validate(),
});

</script>
<style lang="scss" scoped>
.setting {
  div:not(:last-child) {
    :deep(.t-form__item ){
      margin-bottom: var(--td-comp-margin-xxl);
    }
  }
}

:deep(.t-form__label--top) {
  float: left;
}
</style>
