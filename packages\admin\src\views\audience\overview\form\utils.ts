import { useAixAudienceOverviewFormStore } from '@/store/audience/overview/form/index.store';
import { useAixAudienceOverviewStore } from '@/store/audience/overview/index.store';
import { storeToRefs } from 'pinia';
import type { IUpdateFrequencyFormItem } from '@/store/audience/overview/type';
import { audienceNameCheck } from 'common/service/audience/overview/index';
import { useGlobalGameStore } from '@/store/global/game.store';
import { getMockOverviewData } from '@/store/audience/overview/utils/mock/mock_overview';

export function checkUpdateFrequency(val: IUpdateFrequencyFormItem) {
  if (val.createby === 'modeling' && !val.updateFrequency) {
    return {
      result: false,
      message: 'Please first select a model type, and then fill in the update_frequency.',
      type: 'error',
    };
  }
  if (!val.updateFrequency) {
    return { result: false, message: 'Please complete the required field.', type: 'error' };
  }
  return true;
}

export function checkTestParam(val: number) {
  if (!val || val < 0 || val > 50) {
    return { result: false, message: 'Only supports positive integers within 50.', type: 'error' };
  }
  return true;
}

export async function checkAudienceName(val: string) {
  if (!val || !/^[0-9a-zA-Z_]{1,39}$/.test(val)) {
    return {
      result: false,
      message:
        'The field cannot be blank and should contain fewer than 39 characters, using only English letters, numbers, and underscores.',
      type: 'error',
    };
  }
  const { isAdd } = storeToRefs(useAixAudienceOverviewFormStore());
  if (!isAdd.value) {
    return true;
  }
  const { isDemoGame } = useGlobalGameStore();
  const { gameCode } = storeToRefs(useGlobalGameStore());
  if (isDemoGame()) {
    const { demoGameBlackAudienceIdList, demoListInSeesionByAdd } = storeToRefs(useAixAudienceOverviewStore());
    const { table = [] } = getMockOverviewData(gameCode.value);
    const isDuplicateName = [...demoListInSeesionByAdd.value, ...table]
      .filter(item => !demoGameBlackAudienceIdList.value.includes(item.id))
      .find(item => item.name === val);
    if (!isDuplicateName) return true;
    return { result: false, message: 'Audience name has already been used; please use a new name.', type: 'error' };
  }
  const { data } = await audienceNameCheck({ name: val });
  if (!data) {
    return { result: false, message: 'Audience name has already been used; please use a new name.', type: 'error' };
  }
  return true;
}

// 校验UserRange表单项
export function checkUserRange(val: number[]) {
  if (val.length !== 2 || (!val[0] && !val[1])) {
    return { result: false, message: 'User Range cannot be 0~0.', type: 'error' };
  }
  if (val[0] >= val[1]) {
    return { result: false, message: 'Please select the correct range.', type: 'error' };
  }
  return true;
}

export function checkUserTtl(val: string | number) {
  if (!/^(([1-9]{1})|([1-9]{1}[0-9]{1})|(1{1}[0-8]{1}0{1}))$/.test(String(val))) {
    return { result: false, message: 'Please enter a positive integer between 1 and 180.', type: 'error' };
  }
  return true;
}
