<template>
  <CommonView
    :hide-right="true"
    :store="store"
    :router-index="-2"
    :need-back="true"
    class="h-full"
  >
    <template #views>
      <DataContainer
        class="rounded-large"
        :total="store.params.pageTotal"
        :page-size="store.params.pageSize"
        :data="store.logList"
        :loading="store.isLoading"
        :default-page="(store.params.pageIndex || 1)"
        @on-page-change="pageChange"
      >
        <template #actionSlot><div /></template>
        <template #attributeSlot><div /></template>
        <Table
          :key="JSON.stringify(columns)"
          class="h-full"
          row-key="audience_id"
          max-height="100%"
          :columns="columns"
          :data="store.logList"
          :filter-value="defaultFilter"
          @filter-change="onFilterChange"
        />
      </DataContainer>
    </template>
  </CommonView>
</template>
<script lang="ts" setup>
import CommonView from  'common/components/Layout/CommonView.vue';
import DataContainer from 'common/components/Layout/DataContainer.vue';
import Table from 'common/components/table';
import { computed, ref, onBeforeUnmount } from 'vue';
import type { FilterValue as TFilterValue } from 'tdesign-vue-next';
import { useAixAudienceOverviewLogStore } from '@/store/audience/overview/log.store';
import { getColumns } from './columns';

const store = useAixAudienceOverviewLogStore();
const columns = computed(() => getColumns(
  store.logColumns,
  store.logOptions.statusList,
  store.logOptions.typeList,
));

const defaultFilter = ref<TFilterValue>({ type: 'upload', status: '' });
function pageChange(current: number, pageInfo: { pageSize: number}) {
  store.setPage(current, pageInfo.pageSize);
}

function onFilterChange(filterValue: TFilterValue) {
  store.setFilter(filterValue.type, filterValue.status);
}

onBeforeUnmount(() => {
  store.resetReqParams();
});

</script>
<style lang="scss" scoped>
:deep(.t-layout__header) {
  height: 16px !important;
}
:deep(.t-table__first-full-row) {
  display: none;
}

:deep(.t-table__content) {
  height: 100%;
}

</style>
