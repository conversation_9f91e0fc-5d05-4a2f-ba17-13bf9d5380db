import type { IColumns, IFilterOptionItem, ILogTable } from 'common/service/audience/overview/type';
import { formatCell, setColumnsFilterList } from '../components/columns/format';
import Text from 'common/components/Text';
import { useAixAudienceOverviewLogStore } from '@/store/audience/overview/log.store';
import { useGlobalGameStore } from '@/store/global/game.store';
import { getBaseUrlByEnv } from 'common/utils/baseUrl';

function renderTaskId(value: string) {
  const hrefUrl  = `${getBaseUrlByEnv()}/api_v2/audience/open_instance/${value}?game=${useGlobalGameStore().gameCode}`;
  return (
    <a href={hrefUrl} target="_blank">{value}</a>
  );
}

export function getColumns(
  colList: IColumns[],
  statusList: IFilterOptionItem[],
  typeList: IFilterOptionItem[],
) {
  const { isDemoGame } = useGlobalGameStore();
  const { restartTask } = useAixAudienceOverviewLogStore();
  const filterColumns = [
    {
      colKey: 'type',
      filter: {
        type: 'single',
        list: setColumnsFilterList(typeList, false),
      },
    },
    {
      colKey: 'status',
      filter: {
        type: 'single',
        list: setColumnsFilterList(statusList),
      },
    },
  ];
  const dynamicColumns = colList?.map(item => ({
    colKey: item.key,
    title: item.title,
    cell: (h: any, { row }: any) => (item.key === 'task_id' ? renderTaskId(row.task_id) : formatCell(row, item)),
    width: item.width || 150,
    ellipsis: true,
    filter: filterColumns.find(col => col.colKey === item.key)?.filter,
  }));

  const staticColumns: any[] = isDemoGame() ? [] : [
    {
      colKey: 'operation',
      title: 'Operation',
      width: 140,
      fixed: 'right',
      cell: (h: any, { row }: {row: ILogTable}) => (
        <Text
          class={'cursor-pointer'}
          color={'var(--aix-text-color-brand)'}
          content={'Rerun'}
          onClick={() => restartTask(row)}
        />
      ),
      // cell: (h: any, { row }: {row: ILogTable}) => (<div>
      //   {row.status  === 'failed'
      //     ? <Text
      //         class={'cursor-pointer'}
      //         color={'var(--aix-text-color-brand)'}
      //         content={`${row.type === 'delete' ? 'Delete' : 'Run'} once at now`}
      //         onClick={() => restartTask(row)}
      //       />
      //     : <Text
      //         content="Set as failed"
      //         color={'var(--aix-text-color-brand)'}
      //         class={'cursor-pointer'}
      //         onClick={() => setAsfailed(row.task_id)}
      //       />
      //   }
      // </div>),
    },
  ];
  return [...dynamicColumns, ...staticColumns];
}
