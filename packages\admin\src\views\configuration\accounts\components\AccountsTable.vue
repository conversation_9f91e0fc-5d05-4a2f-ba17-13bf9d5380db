<template>
  <DataContainer
    ref="dataContainer"
    class="rounded-large pt-[16px] mb-[24px] w-full h-full"
    :data="filteredList"
    :default-page="pageNum"
    :loading="store.accountsLoading"
    @on-page-change="onPageChange"
  >
    <template #attributeSlot>
      <t-space>
        <t-button @click="gotoAuthentication">
          <template #icon>
            <add-icon />
          </template>
          Add Accounts
        </t-button>
        <t-dropdown
          :options="dropdownOption"
          :disabled="btnDisabled"
          trigger="click"
          min-column-width="135px"
          @click="onDropdownClick"
        >
          <t-button
            theme="default"
            :disabled="btnDisabled"
          >
            <template #icon>
              <CopyIcon />
            </template>
            Bulk Operation
          </t-button>
        </t-dropdown>
      </t-space>
    </template>
    <template #actionSlot>
      <div class="space-x-5 flex items-center justify-center">
        <div
          class="flex items-center justify-center normal-hover"
          @click="onResetTable"
        >
          <svg-icon name="refresh" />
          <p class="pr-[20px] pl-[5px]">Refresh</p>
        </div>
        <div class="flex items-center normal-hover">
          <Download @download="download">
            <p class="px-[5px]">Download</p>
          </Download>
        </div>
      </div>
    </template>
    <t-loading
      :loading="store.refreshLoading"
      show-overlay
      size="24px"
    >
      <Table
        ref="tableRef"
        :key="store.currentChannels"
        v-model:display-columns="displayCols"
        row-key="index_id"
        :columns="cols"
        :filter-value="filter"
        :filter-row="null"
        :disable-data-page="true"
        @filter-change="setFilter"
        @select-change="rehandleSelectChange"
      />
    </t-loading>
  </DataContainer>
  <EditAccountDialog
    ref="editDialog"
    :data="editAccountData"
  />
  <SelectAccountsDialog ref="selectDialog" />
</template>
<script setup lang="ts">
import { ref, computed, watch, watchEffect } from 'vue';
import { AddIcon, CopyIcon } from 'tdesign-icons-vue-next';
import { tryOnMounted } from '@vueuse/core';
import { useRoute, useRouter } from 'vue-router';

import DataContainer from 'common/components/Layout/DataContainer.vue';
import Table from 'common/components/table';
import SelectAccountsDialog from '@/views/configuration/accounts/components/dialog/SelectAccountsDialog.vue';
import { useAdAccountsStore } from '@/store/configuration/adaccounts/adaccounts.store';
import { useAccountsTable } from '@/views/configuration/accounts/compose/accounts_table';
// import { useGlobalGameStore } from '@/store/global/game.store';
import { useFilter } from 'common/compose/table/filter';
import { omit } from 'lodash-es';
import Download from 'common/components/BusinessTable/Download.vue';
import SvgIcon from 'common/components/SvgIcon/SvgIcon.vue';
import { useDownloadFile } from 'common/compose/download-file';
import { useTips } from 'common/compose/tips';
import EditAccountDialog from './dialog/EditAccountDialog.vue';
import { upDateFormModel } from '@/store/configuration/adaccounts/channelts/manualAuth';
import { useChanneltsStore } from '@/store/configuration/adaccounts/channelts/channelts.store';
import { SpecialCondition } from '@/store/configuration/adaccounts/type';
import dayjs from 'dayjs';
import { initMessagePlugin } from '@/views/configuration/accounts/compose/message';
const channeltsstore = useChanneltsStore();
const store = useAdAccountsStore();
// const gameStore = useGlobalGameStore();
const editDialog = ref();
const selectDialog = ref();
const route = useRoute();
const router = useRouter();
const tableRef = ref();
const pageNum = ref(1);
const editAccountData = ref({});
const onPageChange = (current: number) => {
  pageNum.value = current;
};
const dataContainer = ref();
const onResetTable = () => {
  store.refreshLoading = true;
  initPageAndData();
  store.getTableList();
};
const initPageAndData = () => {
  onPageChange(1);
  dataContainer.value.setPage(1);
};

defineExpose({
  initPageAndData,
});
const btnDisabled = computed(() => {
  // 处理删除后selectedRowKeys.value还存在值问题
  const selecedItems = filteredList.value.filter(i => selectedRowKeys.value.includes(i.index_id));
  return store?.accountsList?.length === 0 || selecedItems?.length === 0;
});
tryOnMounted(() => {
  if ((route.query.get_accounts === '1' || route.query.get_accounts === '-1') && route.query.channel) {
    router.replace({
      query: omit(route.query, ['get_accounts']),
    });

    if (route.query.get_accounts === '1') {
      getAccountsList().then();
    } else if (route.query.get_accounts === '-1') {
      err(`${route.query.channel} channel server error, please try again`);
    }
  }
});

watchEffect(() => {
  const { tokenValidityInfo } = store;
  if (!tokenValidityInfo) return;
  const { expiredChannels, expiringSoonChannels } = tokenValidityInfo;
  const message = initMessagePlugin();
  if (Object.keys(expiredChannels).length > 0) {
    message.error(expiredChannels);
  }
  if (Object.keys(expiringSoonChannels).length > 0) {
    message.info(expiringSoonChannels);
  }
});

// google:***********

// 格式化表头信息  { bm : 'BM'}
const formatTableHead = (displayCols: Record<string, string | number>[]) => Object.fromEntries(
  displayCols.filter(i => i.title && i.colKey !== 'action').map(({ colKey, title }) => [colKey, title]),
);

const statusNameListMap: Record<string | number, string> = {
  0: 'Auth Invalid',
  1: 'Authorized',
  '-1': 'Auth Paused',
  '-2': 'Unauthorized',
};

const specialConditions: SpecialCondition[] = [
  {
    key: 'status',
    condition: () => true, // 总是执行
    action: value => ({
      status: statusNameListMap[value] ?? value,
    }),
  },
  {
    key: 'owner',
    condition: value => value === '-',
    action: () => ({
      owner: '',
    }),
  },
];

const getNewTables = (
  headCol: Record<string, string | number>,
  tableData: Record<string, string | number>[] | null,
) => {
  if (tableData === null) return [];
  return tableData.map((item) => {
    const newItem: Record<string, string | number> = {};

    Object.keys(item).forEach((key: string) => {
      if (headCol[key]) {
        newItem[key] = item[key].toString();
      }
      // 处理特殊条件
      specialConditions.forEach((condition) => {
        if (condition.key === key && condition.condition(item[key])) {
          Object.assign(newItem, condition.action(item[key])); // 合并返回的对象
        }
      });
    });

    return newItem;
  });
};
// 下载
const { err } = useTips();
const download = (val: string) => {
  if (store.accountsList && store.accountsList.length > 0) {
    const tableHead = formatTableHead(cols.value);
    const tableData = store.accountsList;
    const newData = getNewTables(tableHead, tableData);
    useDownloadFile(newData || [], `table.${val}`, { lock: true, isSetHeader: true, header: tableHead });
  } else {
    err('Empty Data');
  }
};
const onEditAccountInfo = (row: any) => {
  editAccountData.value = row;
  editDialog.value?.show();
  const accountData = { ...row };
  const realChannel = channeltsstore.manualChannelTabelName(row.channel);
  accountData.channel_name = realChannel;
  upDateFormModel(accountData, false);
};
const { cols, displayCols } = useAccountsTable(onEditAccountInfo);
const gotoAuthentication = () => {
  router.push({ path: '/config/accounts/add_accounts' });
};

// 获取渠道的账户列表
const getAccountsList = async () => {
  // 展示accounts dialog
  selectDialog.value?.show();
  // 触发列表刷新
  await store.refreshChannelAccountsList(route.query);
};

watch(
  () => store.channelAccountsListError,
  () => {
    err(store.channelAccountsListError as string);
  },
);
const utcTime = `(UTC+${dayjs().utcOffset() / 60})`;

const initAccountsList = computed(() => {
  if (!store.accountsList) return [];
  store.initFilterOption();
  return store.accountsList.map((item) => {
    const updatedAt = dayjs(item.updated_at);
    const newUpdatedAt = updatedAt.isValid() ? `${updatedAt.format('YYYY-MM-DD HH:mm')} ${utcTime}` : 'N/A';
    return {
      ...item,
      updated_at: newUpdatedAt,
      ...(item?.owner === '-' && { owner: '' }),
    };
  });
});
const { filter, setFilter, filteredList } = useFilter(
  {
    account_id: '',
    account_name: '',
    channel: '',
    updated_at: '',
    owner: '',
    creator: '',
    status: [],
  },
  initAccountsList,
);

const selectedRowKeys = ref<string[]>([]);
const rehandleSelectChange = (value: string[]) => {
  selectedRowKeys.value = value;
};

// TODO 根据selectItems渲染下拉内容
const dropdownOption = computed(() => {
  const selecedItems = filteredList.value.filter(i => selectedRowKeys.value.includes(i.index_id));
  return [
    {
      content: 'Link',
      value: 'link',
      disabled: selecedItems.some(i => i.status === '1' || Number(i.status) === 0),
      method: store.linkAccounts,
    },
    {
      content: 'UnLink',
      value: 'unlink',
      disabled: selecedItems.some(i => i.status === '-1' || Number(i.status) === 0),
      method: store.unlinkAccounts,
    },
    {
      content: 'Delete',
      value: 'delete',
      method: store.deleteAccounts,
    },
  ];
});
const onDropdownClick = (item: any) => {
  // 从列表中找出勾选的内容
  // selectedRowKeys.value
  const selectedItems = filteredList.value.filter(i => selectedRowKeys.value.includes(i.index_id));
  const method = dropdownOption.value.find(i => i.value === item.value)?.method;
  if (method) {
    method(selectedItems);
  }
};
</script>

<style scoped lang="scss"></style>
