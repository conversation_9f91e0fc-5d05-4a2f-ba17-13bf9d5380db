<template>
  <div
    v-for="(item, key) in store.popularAndOtherObject"
    :key="key"
  >
    <div v-show="item?.length > 0">
      <p
        class="pl-[2px] font-bold pb-[16px] text-base text-black-primary"
      >
        {{ store.searchValue ? titleObject['search'] : titleObject[key] }}
      </p>
      <t-space

        size="12px"
        class="flex items-center flex-wrap min-h-[145px] pl-[2px] pb-[32px]"
      >
        <t-card
          v-for="(channelItem, channelkey) in item"
          :key="channelkey"
          hover-shadow
          class="w-[240px] h-[130px] hover:border-none rounded-default border-solid border bg-white cursor-pointer"
          @click="gotoAuthentication(channelItem)"
        >
          <div class="flex flex-col items-center justify-center">
            <img
              class="w-[60px] h-[60px] object-contain"
              :src="channelItem?.icon_image"
              :alt="channelItem?.channel_name"
            >
            <p class="select-none capitalize">{{ channelItem?.display_name }}</p>
          </div>
        </t-card>
      </t-space>
    </div>
  </div>
  <AddAccountsDialog
    ref="addDialog"
    :data="selectItem"
  />
  <AddManualDialog
    ref="addManualDialog"
    :data="selectItem"
  />
</template>

<script setup lang="ts">
import AddAccountsDialog from '../dialog/AddAccountsDialog.vue';
import AddManualDialog from '../dialog/AddManualDialog.vue';
import { useChanneltsStore } from '@/store/configuration/adaccounts/channelts/channelts.store';
import { ref } from 'vue';
import { upDateFormModel } from '@/store/configuration/adaccounts/channelts/manualAuth';
import { ChannelTypes } from 'common/service/configuration/adaccounts/type';

const store = useChanneltsStore();
const addDialog = ref();
const selectItem = ref({});
const titleObject: Record<string, string> = {
  popular: 'Popular',
  other: 'Other Channels',
  search: 'Result',
};

const gotoAuthentication = (channelItem: ChannelTypes) => {
  selectItem.value = channelItem;
  try {
    if (!!channelItem.self_auth && Boolean(JSON.parse(channelItem?.self_auth))) {
      return addDialog.value?.show();
    }
    upDateFormModel(channelItem);
    return gotoArtificialAuth();
  } catch (error) {
    console.log(error);
  }
};
const addManualDialog = ref();
const gotoArtificialAuth = () => {
  addManualDialog.value?.show();
};
</script>

<style scoped lang="scss">
.active {
  @apply border-brand;
}
</style>
