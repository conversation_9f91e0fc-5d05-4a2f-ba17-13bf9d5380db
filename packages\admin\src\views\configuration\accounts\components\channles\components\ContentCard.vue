<template>
  <div class="pb-[20px]">
    <Input
      v-model="searchValue"
      clearable
      auto-width
      class="w-[320px]"
      placeholder="Search"
      trim
      @input="getSeachList"
    >
      <template #prefixIcon>
        <SVGIcon
          name="search-input-1"
          height="16px"
          width="16px"
        />
      </template>
    </Input>
  </div>
  <t-loading
    :loading="store.channelListLoading"
    size="25px"
    class="h-full"
  >
    <div class="flex flex-col w-full min-w-[450px] p-[20px] bg-white-primary rounded-large max-w-[100%] h-full">
      <div class="content"><slot name="content" /></div>
    </div>
  </t-loading>
</template>
<script setup lang="ts">
import { useChanneltsStore } from '@/store/configuration/adaccounts/channelts/channelts.store';
import Input from 'common/components/Input';
import SVGIcon from 'common/components/SvgIcon';
import { cloneDeep } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';
import { onBeforeRouteLeave } from 'vue-router';
const store = useChanneltsStore();
const { searchValue } = storeToRefs(store);

// 搜索功能
const tempChannelList = cloneDeep(store.channelList);
const getSeachList = computed(() => {
  if (!searchValue.value) {
    return store.updateChannelList(tempChannelList);
  }
  const tempSearchList =    tempChannelList?.filter(
    (item: any) => item.channel_name.includes(searchValue.value.toLowerCase())) || [];
  return store.updateChannelList(tempSearchList);
});
onBeforeRouteLeave((to, from, next) => {
  store.searchValue = '';
  next();
});
</script>
