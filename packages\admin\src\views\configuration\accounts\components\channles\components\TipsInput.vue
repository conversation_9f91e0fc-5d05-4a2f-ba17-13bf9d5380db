<template>
  <t-tooltip
    :key="props.item.key"
    :content="tempFormModel.data[props.item.key]"
    :visible="isShowTips"
    :hide-empty-popup="true"
  />
  <t-tag-input
    v-if="props.item.type === 'tagInput'"
    v-model="tempFormModel.data[props.item.key]"
    :maxlength="1000"
    placeholder="Press the Enter key to confirm after entering"
    clearable
  />
  <t-input
    v-else
    v-model="tempFormModel.data[props.item.key]"
    :disabled="props.item.type == 'disabled'"
    :maxlength="1000"
    :show-limit-number="props.item.type !== 'disabled'"
    :placeholder="`Please enter ${props.item.label}`"
    @mouseenter="isShowTips = true"
    @mouseleave="isShowTips = false"
  >
    <template
      v-if="props.item.type == 'disabled'"
      #suffixIcon
    >
      <UseClipboard
        v-slot="{ copy }"
        source="copy me"
      >
        <CopyIcon
          :style="{ cursor: 'pointer' }"
          @click="copy(tempFormModel.data[props.item.key]), $message.success('copy success')"
        />
      </UseClipboard>
    </template>
  </t-input>
</template>
<script setup>
import { CopyIcon } from 'tdesign-icons-vue-next';
import { UseClipboard } from '@vueuse/components';
import { ref } from 'vue';
const props = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
  formModel: {
    type: Object,
    default: () => {},
  },
});
const tempFormModel = ref(props.formModel);
const isShowTips = ref(false);
</script>
