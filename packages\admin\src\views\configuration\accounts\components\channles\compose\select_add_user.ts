import { ITableCols } from 'common/components/table/type';
import { computed, ComputedRef } from 'vue';
import { useAuthStatus } from '@/views/configuration/accounts/compose/auth_status';
import { useAdAccountsStore } from '@/store/configuration/adaccounts/adaccounts.store';
import { useStorage } from '@vueuse/core';
/**
 * 复用accounts 的cols
 */
// import { useAccountsTable } from '@/views/configuration/accounts/compose/accounts_table';
// const { cols } = useAccountsTable();

export function useSelectAddUserTable(): any {
  const store = useAdAccountsStore();
  const specialCols: Record<string, string> = {
    google: 'MCC',
    facebook: 'BM',
  };
  const cols: ComputedRef<(ITableCols | string)[]> = computed(() => {
    const channel = store.currentChannels;
    return [
      {
        colKey: 'row-select',
        type: 'multiple' as 'multiple',
        fixed: 'left',
        fixedHead: true,
      } satisfies ITableCols,
      {
        colKey: 'account_id',
        title: 'Account ID',
        ellipsis: true,
        filter: {
          type: 'input',

          // 文本域搜索
          // component: Textarea,

          resetValue: '',
          // 按下 Enter 键时也触发确认搜索
          confirmEvents: ['onEnter'],
          props: {
            placeholder: 'input account ID',
          },
          // 是否显示重置取消按钮，一般情况不需要显示
          showConfirmAndReset: true,
        },
        fixed: 'left',
      } satisfies ITableCols,
      {
        colKey: 'account_name',
        title: 'Account Name',
        width: '150px',
        ellipsis: true,
        filter: {
          type: 'input',

          // 文本域搜索
          // component: Textarea,

          resetValue: '',
          // 按下 Enter 键时也触发确认搜索
          confirmEvents: ['onEnter'],
          props: {
            placeholder: 'input account name',
          },
          // 是否显示重置取消按钮，一般情况不需要显示
          showConfirmAndReset: true,
        },
        fixed: 'left',
      } satisfies ITableCols,
      useAuthStatus('string'),
      specialCols[channel]
        ? ({
          colKey: 'owner',
          title: specialCols[channel],
          ellipsis: true,
        } satisfies ITableCols)
        : '',
    ].filter(Boolean);
  });
  const displayCols = useStorage(
    'aix-add-accounts-user-cols-key',
    ['row-select', 'account_id', 'account_name', 'owner', 'status'],
    localStorage,
  );
  return { cols, displayCols };
}
