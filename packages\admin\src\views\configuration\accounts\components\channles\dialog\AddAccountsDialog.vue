<template>
  <BaseDialog
    ref="addAccounts"
    :title="`Add ${capitalize(props.data?.channel_name)} Ads Account`"
    width="400px"
    :footer="false"
  >
    <div class="flex-1 w-full h-full min-h-[120px] flex justify-center items-center">
      <div
        class="flex items-center w-auto min-w-[190px] border border-solid px-[16px] py-[6px] rounded-lg cursor-pointer"
        @click="gotoAuthentication"
      >
        <img
          class="w-[24px] h-[24px] object-contain"
          :src="props.data?.icon_image"
          :alt="props.data?.channel_name"
        >
        <p class="select-none capitalize">Sign In With {{ props.data?.channel_name }}</p>
      </div>
    </div>
  </BaseDialog>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { useGlobalGameStore } from '@/store/global/game.store';
import BaseDialog from 'common/components/Dialog/Base/Index.vue';
import { capitalize } from 'lodash-es';
const gameStore = useGlobalGameStore();
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});
const addAccounts = ref();
defineExpose({
  show: () => addAccounts.value?.show(),
  hide: () => addAccounts.value?.hide(),
});

const gotoAuthentication = () => {
  location.href = `${location.protocol}//api.aix.levelinfinite.com/api_v2/oauth/${props.data?.channel_name}?game=${gameStore.gameCode}`;
};
</script>
<style scoped lang="scss"></style>
