<template>
  <BaseDialog
    ref="addAccountsDialog"
    :title="`Connect To ${props.data?.display_name}`"
    width="600px "
    confirm-text="Save"
    :confirm-loading="store.isChanelApiLoading"
    @close="onClose"
    @confirm="onSubmit"
  >
    <div class="text-base">
      <p>
        Step1: Sign Into
        <a
          class="text-brand cursor-pointer capitalize"
          :href="CHANNELS_OFFICIAL_WEBSITE[props.data?.channel_name]"
          target="_blank"
        >{{ props.data?.display_name }}</a>
      </p>
    </div>
    <div
      class="justify-between items-end py-[15px] text-base"
      :class="{ flex: stepTextwidth <= 400 }"
    >
      <p
        ref="stepText"
        class="max-w-fit"
      >
        {{
          CHANNEL_STEP_TEXT[props.data?.channel_name]
            ? CHANNEL_STEP_TEXT[props.data?.channel_name]
            : CHANNEL_STEP_TEXT['default']
        }}
      </p>
      <a
        class="flex items-center justify-end min-w-fit"
        :href="CHANNELS_GUIDE_URL[props.data?.channel_name]"
        target="_blank"
      >
        <SvgIcon
          name="Vector"
          size="16px"
          class="text-brand"
        />
        <p class="text-brand cursor-pointer pl-[6px]">See Instruction</p>
      </a>
    </div>
    <t-form
      ref="formRef"
      label-align="left"
      label-width="120px"
      :data="formModel.data"
      :rules="rowRules"
    >
      <div
        v-for="item in formModel.column"
        :key="item.key"
        class="mb-[24px]"
      >
        <div
          v-if="item.type === 'step3'"
          class="text-base mb-[15px]"
        >
          <p style="color: rgba(0, 0, 0, 0.6)">Step3: Obtain your account information</p>
        </div>
        <t-form-item
          :label="item.label"
          :name="item.key"
        >
          <template #label>
            <span class="text">{{ item.label }}</span>
            <t-tooltip
              v-if="item.tips"
              placement="bottom-left"
              :content="item.tips"
              class="ml-[10px]"
            >
              <HelpCircleIcon size="16px" />
            </t-tooltip>
          </template>
          <TipsInput :item="item" :form-model="formModel" />
        </t-form-item>
      </div>
    </t-form>
  </BaseDialog>
</template>
<script setup lang="ts">
import BaseDialog from 'common/components/Dialog/Base/Index.vue';
import SvgIcon from 'common/components/SvgIcon/SvgIcon.vue';
import {
  CHANNELS_GUIDE_URL,
  CHANNELS_OFFICIAL_WEBSITE,
  CHANNEL_STEP_TEXT,
  CUSTOMIZEDRULESKEY,
} from '@/store/configuration/adaccounts/channelts/const';
import { formModel, onClose } from '@/store/configuration/adaccounts/channelts/manualAuth';
import { useChanneltsStore } from '@/store/configuration/adaccounts/channelts/channelts.store';
import { ref, computed } from 'vue';
import {  HelpCircleIcon } from 'tdesign-icons-vue-next';
import { useElementSize } from '@vueuse/core';
import TipsInput from '@/views/configuration/accounts/components/channles/components/TipsInput.vue';
const store = useChanneltsStore();
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});
const formRef = ref();
const addAccountsDialog = ref();
const stepText = ref(null);
const { width: stepTextwidth } = useElementSize(stepText);
defineExpose({
  show: () => addAccountsDialog.value?.show(),
  hide: () => addAccountsDialog.value?.hide(),
});
const generateRule = (label: string, isNotRequired: Boolean) => [
  isNotRequired && { required: true, message: `${label} is required`, type: 'error' },
  { whitespace: true, message: 'The value cannot be empty' },
];
const rowRules = computed(() => {
  const { data, column } = formModel.value || {};
  if (!data || !column) return {};
  return Object.keys(data).reduce((rules: Record<string, any>, key) => {
    const newRules = { ...rules };
    const isNotRequired = !CUSTOMIZEDRULESKEY.includes(key);
    const itemLabel = column.find(item => item.key === key)?.label;
    if (itemLabel) {
      newRules[key] = generateRule(itemLabel, isNotRequired);
    }
    return newRules;
  }, {});
});
const onSubmit = async () => {
  const isValid = await formRef.value.validate();
  if (isValid === true) {
    const channelData = { ...props.data, channel: props.data.channel_name };
    const result = await store.setChannelInfo(channelData as any);
    if (result.value) {
      addAccountsDialog.value?.hide();
    }
  }
};
</script>
<style scoped lang="scss">
::v-deep {
  .t-form__label {
    white-space: normal;
    padding: inherit;
    margin-left: 12px;
    line-height: 18px;
    margin-right: 5px;
  }
  .t-form__label--required label:before {
    margin-left: -12px;
  }
}
</style>
