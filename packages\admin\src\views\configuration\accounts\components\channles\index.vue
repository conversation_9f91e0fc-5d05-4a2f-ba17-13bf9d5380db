<template>
  <div>
    <CommonView
      class="h-screen"
      :hide-right="true"
      :store="store"
      :need-back="true"
      :is-hover="true"
    >
      <template #views>
        <ContentCard>
          <template #content>
            <ChannelList v-if="store.channelList && store.channelList?.length > 0" />
            <SearchErrorCard v-else />
          </template>
        </ContentCard>
      </template>
    </CommonView>
  </div>
</template>
<script setup lang="ts">
import CommonView from 'common/components/Layout/CommonView.vue';
import ChannelList from './components/ChannelList.vue';
import ContentCard from './components/ContentCard.vue';
import SearchErrorCard from './components/SearchErrorCard.vue';
import { useChanneltsStore } from '@/store/configuration/adaccounts/channelts/channelts.store';
const store = useChanneltsStore();
</script>
