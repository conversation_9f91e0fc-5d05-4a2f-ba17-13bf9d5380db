<template>
  <BaseDialog
    ref="baseInstance"
    title="Select the accounts you want to add"
    width="728px"
    height="540px"
    confirm-text="Add"
    :confirm-disabled="selectedRowKeys.length === 0"
    @confirm="selectedAccounts"
  >
    <DataContainer
      hide-header
      class="max-h-[450px] min-h-[450px]"
      :data="accountList"
      :default-page="pageNum"
      @on-page-change="onPageChange"
    >
      <t-loading
        :loading="store.channelAccountsLoading"
        show-overlay
      >
        <Table
          :columns="columns"
          row-key="tempId"
          :filter-row="null"
          :data="newAccountsList"
          :filter-value="filter"
          :disable-data-page="true"
          @filter-change="setFilter"
          @select-change="rehandleSelectChange"
        />
      </t-loading>
    </DataContainer>
  </BaseDialog>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import Table from 'common/components/table/table.vue';
import DataContainer from 'common/components/Layout/DataContainer.vue';
import BaseDialog from 'common/components/Dialog/Base/Index.vue';
import { useSelectTable } from '@/views/configuration/accounts/compose/select_table';
import { useAdAccountsStore } from '@/store/configuration/adaccounts/adaccounts.store';
import { useFilter } from 'common/compose/table/filter';
import { useRoute } from 'vue-router';
import { setOrSendWXWorkMessageText } from 'common/service/wxwork';
const route = useRoute();

const baseInstance = ref();

defineExpose({
  show: () => baseInstance.value?.show(),
  hide: () => baseInstance.value?.hide(),
});

const selectedRowKeys = ref<string[]>([]);
const rehandleSelectChange = (value: string[]) => {
  selectedRowKeys.value = value;
};

const store = useAdAccountsStore();
const { cols: columns } = useSelectTable();

const selectedAccounts = () => {
  store.selectedChannelAccounts(
    selectedRowKeys.value,
    store.channelAccountsList?.authUser,
    route.query.channel as string,
  );
  const accountIds = selectedRowKeys.value.join(',');
  setOrSendWXWorkMessageText(route.query.channel as string, 'text', 'Add', accountIds, true);
  baseInstance.value?.hide();
};
const pageNum = ref(1);
const pageSize = ref(5);
const onPageChange = (current: number, pageInfo: any) => {
  pageNum.value = current;
  pageSize.value = pageInfo.pageSize;
};
const { filter, setFilter, filteredList } = useFilter(
  {
    customerID: '',
    descriptiveName: '',
    status: [],
    accountsStatus: [],
  },
  computed(() => store.channelAccountsList?.accounts || []),
);
const accountList = computed(() => filteredList.value.filter(i => i.level !== '0' || !i.manager));
// 筛选出level ==='0' 即GG的MCC账号
const newAccountsList = computed(() => paginate(accountList.value, pageNum.value, pageSize.value));
function paginate(data: string[], current: number, pageSize: number) {
  const startIndex = (current - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  return data.slice(startIndex, endIndex);
}
</script>
