import { ITableCols } from 'common/components/table/type';
import { DropdownOption, Button, Dropdown, Icon, DatePicker } from 'tdesign-vue-next';
import { computed, ComputedRef } from 'vue';
import { useAuthStatus } from '@/views/configuration/accounts/compose/auth_status';
import { useAdAccountsStore } from '@/store/configuration/adaccounts/adaccounts.store';
import { useStorage } from '@vueuse/core';
import { useChanneltsStore } from '@/store/configuration/adaccounts/channelts/channelts.store';
import { actionOptions, Action } from '@/store/configuration/adaccounts/type.d';
export function useAccountsTable(onEditAccountInfo: Function): any {
  const store = useAdAccountsStore();
  const channeltsStore = useChanneltsStore();

  const filterActionOptions = (row: any) => {
    const isSelf = isSelfChannel(row.channel);
    const status = Number(row.status);
    return actionOptions.filter((item) => {
      // 如果是自助渠道，过滤掉 Edit 选项
      if (isSelf && item.value === Action.Edit) return false;
      // 根据 status 过滤
      // 如果 unlink 状态，过滤掉 UnLink
      if (status === -1 && item.value === Action.UnLink) return false;
      // 如果是不可用状态，只返回 Delete
      if (status === 0 && item.value !== Action.Delete) return false;
      // 如果 link 状态，过滤掉 Link
      if (status !== -1 && status !== 0 && item.value === Action.Link) return false;
      // 其他情况保留
      return true;
    });
  };
  const onOptionClick = (evt: DropdownOption, row: any) => {
    switch (evt.value) {
      case 1:
        store.linkAccounts(row).then();
        break;
      case 2:
        store.unlinkAccounts(row);
        break;
      case 3:
        store.deleteAccounts(row);
        break;
      case 4:
        onEditAccountInfo(row);
        return;
      default:
        break;
    }
  };
  const hasInvalidAccount = computed(() => {
    const { accountsList } = store;
    return accountsList?.some(i => Number(i.status) === 0);
  });
  const channelIconUrl = (channel: string) => {
    let list = channeltsStore.channelList;
    if (!list) {
      channeltsStore.initIconUrl().then((newList) => {
        list = newList;
      });
    }
    if (list) {
      list = Array.isArray(list) ? list : (list as any)?.list;
      const itemIconUrl = list?.find((item: any) => item.channel_name.toLowerCase() === channel.toLowerCase());
      return itemIconUrl?.icon_image;
    }
    return '';
  };

  const isSelfChannel = (channel: string) => {
    const list = channeltsStore.channelList;
    if (!list || list.length <= 0) {
      return true;
    }
    if (list && Array.isArray(list)) {
      try {
        const channelItem = list?.find((item: any) => item.channel_name === channel);
        return Boolean(JSON.parse(channelItem?.self_auth || ''));
      } catch (e) {
        return false;
      }
    }
    return false;
  };
  const cols: ComputedRef<(ITableCols | string)[]> = computed(() => [
      {
        colKey: 'row-select',
        type: 'multiple' as 'multiple',
        fixed: 'left',
        fixedHead: true,
      } satisfies ITableCols,
      {
        colKey: 'index_id',
        fixedHead: true,
      } satisfies ITableCols,
      {
        colKey: 'account_id',
        title: 'Account ID',
        minWidth: 150,
        ellipsis: {
          attach: 'body',
        },
        filter: {
          type: 'input',

          // 文本域搜索
          // component: Textarea,

          resetValue: '',
          // 按下 Enter 键时也触发确认搜索
          confirmEvents: ['onEnter'],
          props: {
            placeholder: 'input account ID',
          },
          // 是否显示重置取消按钮，一般情况不需要显示
          showConfirmAndReset: true,
        },
        cell: (h: any, { row }: any) => (row.account_id ? row.account_id : '-'),
        fixed: 'left',
      } satisfies ITableCols,
      {
        colKey: 'account_name',
        title: 'Account Name',
        ellipsis: {
          attach: 'body',
        },
        minWidth: 200,
        filter: {
          type: 'input',

          // 文本域搜索
          // component: Textarea,

          resetValue: '',
          // 按下 Enter 键时也触发确认搜索
          confirmEvents: ['onEnter'],
          props: {
            placeholder: 'input account name',
          },
          // 是否显示重置取消按钮，一般情况不需要显示
          showConfirmAndReset: true,
        },
        cell: (h: any, { row }: any) => (row.account_name ? row.account_name : '-'),
        fixed: 'left',
      } satisfies ITableCols,
      {
        colKey: 'channel',
        title: 'Channel',
        minWidth: 170,
        ellipsis: {
          attach: 'body',
        },
        filter: {
          type: 'input',

          // 文本域搜索
          // component: Textarea,

          resetValue: '',
          // 按下 Enter 键时也触发确认搜索
          confirmEvents: ['onEnter'],
          props: {
            placeholder: 'input channel',
          },
          // 是否显示重置取消按钮，一般情况不需要显示
          showConfirmAndReset: true,
        },
        cell: (h: any, { row }: any) => (
          <div class={'flex items-center'}>
            <img
              v-show={channelIconUrl(row.channel)}
              class={'w-[24px] h-[24px] mr-[8px] object-scale-down'}
              src={channelIconUrl(row.channel)}
              // src={`https://static.aix.levelinfinite.com/web/${row.channel}.png`}
              alt={row.channel}
            />
            {(channeltsStore.channelNameList as any)[row.channel] || row.channel}
          </div>
        ),
      } satisfies ITableCols,
      useAuthStatus('string', true),
      {
        colKey: 'owner',
        title: 'MCC / BM',
        ellipsis: {
          attach: 'body',
        },
        filter: {
          type: 'input',

          // 文本域搜索
          // component: Textarea,

          resetValue: '',
          // 按下 Enter 键时也触发确认搜索
          confirmEvents: ['onEnter'],
          props: {
            placeholder: 'input MCC / BM',
          },
          // 是否显示重置取消按钮，一般情况不需要显示
          showConfirmAndReset: true,
        },
        cell: (h: any, { row }: any) => (row.owner ? row.owner : '-'),
      } satisfies ITableCols,
      {
        colKey: 'creator',
        title: 'Owner',
        ellipsis: {
          attach: 'body',
        },
        filter: {
          type: 'input',

          // 文本域搜索
          // component: Textarea,

          resetValue: '',
          // 按下 Enter 键时也触发确认搜索
          confirmEvents: ['onEnter'],
          props: {
            placeholder: 'input Owner',
          },
          // 是否显示重置取消按钮，一般情况不需要显示
          showConfirmAndReset: true,
        },
      } satisfies ITableCols,
      {
        colKey: 'updated_at',
        title: 'Auth Time',
        ellipsis: {
          attach: 'body',
        },
        filter: {
          component: DatePicker,
          resetValue: '',
          // 按下 Enter 键时也触发确认搜索
          confirmEvents: ['onEnter'],
          props: {
            // 'enable-time-picker': true,
            'allow-input': true,
            clearable: true,
          },
          // 是否显示重置取消按钮，一般情况不需要显示
          showConfirmAndReset: true,
        },
        render: (h: any, { row }: any) => row.updated_at || '',
      } satisfies ITableCols,
      {
        colKey: 'action',
        title: () => (
          <div class="flex items-center  justify-between ">
            {'Action'}
            {hasInvalidAccount.value && (
              <t-tooltip
                placement="top-left"
                content={'You have ad accounts that have been suspended. Please check or perform batch operations in the filter for the "Auth Status" field.'}
              >
                <div
                  style="
              color:red;
              width:36px;
              display:flex;
              justify-content:center;"
                >
                  <Icon name="notification-error"></Icon>
                </div>
              </t-tooltip>
            )}
          </div>
        ),
        fixed: 'right',
        width: 110,
        fixedTail: true,
        cell: (h: any, { row }: any) => (
          <div class="flex items-center  justify-between ">
            <Dropdown
              options={filterActionOptions(row)}
              onClick={(evt: DropdownOption) => onOptionClick(evt, row)}
            >
              <Button
                class="rotate-90"
                theme="default"
                variant="text"
                shape="square"
              >
                <Icon
                  name="ellipsis"
                  size="16"
                />
              </Button>
            </Dropdown>
            {Number(row.status) === 0 && (
              <t-tooltip
                placement="top-left"
                content="Your ad account has been paused. To reactivate it,
                please go to the channel platform to enable,
                and delete the ad account on the current page and reauthorize."
              >
                <div
                  style="
                  color:red;
                  width:36px;
                  display:flex;
                  justify-content:center;"
                >
                  <Icon name="notification-error"></Icon>
                </div>
              </t-tooltip>
            )}
          </div>
        ),
      } satisfies ITableCols,
  ].filter(Boolean),
  );
  const displayCols = useStorage(
    'aix-ad-accounts-cols-key',
    [
      'row-select',
      'account_id',
      'account_name',
      'channel',
      'status',
      'owner',
      'creator',
      'updated_at',
      'action',
    ],
    localStorage,
  );

  return { cols, displayCols };
}
