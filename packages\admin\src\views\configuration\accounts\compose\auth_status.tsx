import { Tag } from 'tdesign-vue-next';
import {
  CheckCircleFilledIcon,
  CloseCircleFilledIcon,
  ErrorCircleFilledIcon,
  InfoCircleIcon,
} from 'tdesign-icons-vue-next';
import { ITableCols } from 'common/components/table/type';
import Text from 'common/components/Text';

export function useAuthStatus(valueType: 'string' | 'number' = 'number', isAdShow?: boolean): ITableCols {
  const statusNameListMap: any = {
    0: { label: 'Auth Invalid', theme: 'danger', icon: <ErrorCircleFilledIcon /> },
    1: { label: 'Authorized', theme: 'success', icon: <CheckCircleFilledIcon />, class: '!text-[#02B875]' },
    '-1': { label: 'Auth Paused', theme: 'warning', icon: <CloseCircleFilledIcon />, class: '!text-[#F2AA09]' },
    '-2': { label: 'Unauthorized', theme: 'default', icon: <InfoCircleIcon />, class: '!text-[#747D98]' },
  };
  const baseFilterList = [
    { label: 'All', checkAll: true },
    { label: 'Authorized', value: valueType === 'string' ? '1' : 1 },
    { label: 'Auth Paused', value: valueType === 'string' ? '-1' : -1 },
    { label: 'Auth Invalid', value: valueType === 'string' ? '0' : 0 },
  ];

  const additionalFilters = [
    { label: 'Unauthorized', value: valueType === 'string' ? '-2' : -2 },
  ];

  const statusFilterList = isAdShow ? baseFilterList : [...baseFilterList, ...additionalFilters];
  const tagClass = 'px-[16px] py-[5px] h-[32px] text-[14px] ';
  return {
    colKey: 'status',
    title: 'Auth Status',
    minWidth: 140,
    cell: (h, { row }) => (
      <Tag
        theme={statusNameListMap[row.status.toString()]?.theme}
        variant="light"
        maxWidth="118px"
        class={`${tagClass} ${statusNameListMap[row.status.toString()]?.class || ''}`}
      >
        {/* {statusNameListMap[row.status]?.icon} */}
        {statusNameListMap[row.status.toString()]?.label}
      </Tag>
    ),
    filter: {
      // 过滤行中的列标题别名
      // label: '申请状态 A',
      type: 'multiple',
      resetValue: [],
      showConfirmAndReset: true,
      list: statusFilterList,
    },
  };
}

export function useAccountsStatus(): ITableCols {
  const accountsStatusMap = {
    1: 'Available',
    0: 'Disabled',
  };
  return {
    colKey: 'accountsStatus',
    title: 'Status',
    cell: (h, { row }) => accountsStatusMap[row?.accountsStatus as 1 | 0],
    filter: {
      // 过滤行中的列标题别名
      type: 'multiple',
      resetValue: [],
      showConfirmAndReset: true,
      list: [
        { label: 'All', checkAll: true },
        { label: 'Disabled', value: 0 },
        { label: 'Available', value: 1 },
      ],
    },
  };
}

export function useOwner(): ITableCols {
  return {
    colKey: 'mcc',
    ellipsis: true,
    title: 'MCC / BM',
    // @ts-ignore
    cell: (h, { row }) => (
      <Text
        class={'inline-block w-full'}
        overflow
        toolTip
        content={row?.mcc?.descriptiveName || '-'}
      />
    ),
  };
}
