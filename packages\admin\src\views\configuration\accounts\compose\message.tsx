import { MessagePlugin } from 'tdesign-vue-next';

const formatContentMessage = (expiredChannelData: Record<string, any[]>) => {
  const keys = Object.keys(expiredChannelData);
  return keys.reduce((acc, key, index) => {
    const itemStr = `${key}:[${expiredChannelData[key]}]`;
    // 如果不是最后一个键，则添加逗号和空格；否则不添加
    return acc + itemStr + (index < keys.length - 1 ? ',  ' : '');
  }, '');
};
export const initMessagePlugin = () => {
  const info = (validitySoonInfostr: Record<string, any[]>) => {
    const validitySoonInfostrs = formatContentMessage(validitySoonInfostr);
    return MessagePlugin.info(
      {
        content: () => (
          <span>
            Your authorization information will expire in 2 weeks, please re-authorize the followingchannels with the
            displayed accounts:
            <span style="color:#2355cc"> {validitySoonInfostrs}</span>
          </span>
        ),
        offset: [0, -20],
        closeBtn: true,
      },
      0,
    );
  };
  const error = (validityInfostr: Record<string, any[]>) => {
    const validityInfostrs = formatContentMessage(validityInfostr);
    return MessagePlugin.error(
      {
        content: () => (
          <span>
            Your authorization information has expired, please re-authorize the following channels with the displayed
            accounts:
            <span style="color:#2355cc"> {validityInfostrs}</span>
          </span>
        ),
        offset: [0, -20],
        closeBtn: true,
      },
      0,
    );
  };
  return {
    info,
    error,
  };
};
