import { ITableCols } from 'common/components/table/type';
import { computed, ComputedRef } from 'vue';
import { useAuthStatus, useOwner } from '@/views/configuration/accounts/compose/auth_status';

export function useSelectTable() {
  const cols: ComputedRef<ITableCols[]> = computed(() => [
    {
      colKey: 'row-select',
      type: 'multiple' as 'multiple',
      checkProps: ({ row }) => ({
        disabled: row?.accountsStatus === 0 || row.level === '0' ? true : Number(row.status) !== -2,
      }),
    },
    {
      colKey: 'customerID',
      title: 'Account ID',
      ellipsis: true,
      filter: {
        type: 'input',
        resetValue: '',
        // 按下 Enter 键时也触发确认搜索
        confirmEvents: ['onEnter'],
        props: {
          placeholder: 'input account ID',
        },
        // 是否显示重置取消按钮，一般情况不需要显示
        showConfirmAndReset: true,
      },
    },
    {
      colKey: 'descriptiveName',
      title: 'Account Name',
      ellipsis: true,
      filter: {
        type: 'input',
        resetValue: '',
        // 按下 Enter 键时也触发确认搜索
        confirmEvents: ['onEnter'],
        props: {
          placeholder: 'input account name',
        },
        // 是否显示重置取消按钮，一般情况不需要显示
        showConfirmAndReset: true,
      },
    },
    useAuthStatus(),
    useOwner(),
  ]);
  return { cols };
}
