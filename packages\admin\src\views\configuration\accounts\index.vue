<template>
  <common-view
    class="h-screen"
    :hide-right="true"
    :store="store"
    :form-props="{
      formList,
      modelValue: formModelValue,
      foldList,
      onSubmit: onSubmit,
      onReset: onReset,
      'onUpdate:foldList': (val: string[]) => setFoldList(val),
      'onUpdate:modelValue': updateFormModelValue,
    }"
  >
    <template #views>
      <AccountsTable ref="accountTable" />
    </template>
  </common-view>
</template>

<script setup lang="ts">
import CommonView from 'common/components/Layout/CommonView.vue';
import { useAdAccountsStore } from '@/store/configuration/adaccounts/adaccounts.store';
import AccountsTable from '@/views/configuration/accounts/components/AccountsTable.vue';
import { storeToRefs } from 'pinia';
import { ref } from 'vue';
const store = useAdAccountsStore();
const { formList, foldList, formModelValue } = storeToRefs(store);
const { getTableList, updateFormModelValue, onReset: onResetTable, setFoldList } = store;
const accountTable = ref();
const onSubmit = () => {
  getTableList();
  accountTable.value.initPageAndData();
};
const onReset = () => {
  onResetTable();
  accountTable.value.initPageAndData();
};
</script>
<style lang="scss" scoped>
:global(.t-dialog__delete-dialog .t-button--variant-base.t-button--theme-danger) {
  @apply bg-error-secondary;
}
</style>
