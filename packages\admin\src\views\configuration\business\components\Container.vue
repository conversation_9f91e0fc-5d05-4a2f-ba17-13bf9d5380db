<template>
  <div class="h-full w-full overflow-hidden">
    <div
      v-auto-animate
      class="h-full w-full flex bg-white-primary rounded-large p-[24px]"
    >
      <template v-if="businessStore.isLoading">
        <FullLoading class="relative" />
      </template>
      <template v-else>
        <template v-if="!isEmpty">
          <div class="h-full w-full flex flex-col">
            <div
              v-if="businessStore.isCompanyAdmin"
              class="mb-[18px]"
            >
              <t-button
                class="mr-4"
                content="New Studio"
                @click="onNewStudioBtnClick"
              >
                <template #icon><add-icon /></template>
              </t-button>
              <t-button
                content="Admin Manage"
                @click="onAdminManageBtnClick"
              >
                <template #icon><add-icon /></template>
              </t-button>
            </div>
            <StudioList :data-list="studio.studioList!" />
          </div>
        </template>
        <!-- <template v-show="isEmpty"> -->
        <div
          v-show="isEmpty"
          class="h-full w-full flex overflow-auto"
        >
          <DataEmpty
            :image-size="dataEmptyImageSize"
            class="w-full h-full inline-flex"
          >
            <template #content>
              <div class="text-black-primary text-opacity-[0.6] mt-4">
                <template v-if="businessStore.isCompanyAdmin">
                  No projects currently available，<span
                    class="text-link cursor-pointer"
                    @click="showNewStudioDialog"
                  >Click to create one</span>
                </template>
                <template v-else> You don't have permission </template>
              </div>
            </template>
          </DataEmpty>
        </div>
        <!-- </template> -->
      </template>
    </div>

    <NewStudioDialog
      v-if="newStudioDialogVisible"
      v-model:visible="newStudioDialogVisible"
    />
    <DeleteGameDialog
      v-if="game.delGameDialogVisible"
      v-model:visible="game.delGameDialogVisible"
    />
    <AdminManageDialog
      v-if="adminDialogVisible"
      v-model:visible="adminDialogVisible"
    />
    <GameDrawer />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import DataEmpty from '@/components/nullable/DataEmpty.vue';
import StudioList from './StudioList.vue';
import NewStudioDialog from './dialog/NewOrEditStudio.vue';
import GameDrawer from './drawer/index.vue';
import { useVisible } from 'common/compose/useVisible';
import FullLoading from 'common/components/FullLoading';
import useBusinessStore from '@/store/configuration/business/business.store';
import { AddIcon } from 'tdesign-icons-vue-next';
import DeleteGameDialog from './dialog/DeleteGame.vue';
import AdminManageDialog from './dialog/AdminManage.vue';
const businessStore = useBusinessStore();
const { studio, game } = businessStore;

const { visible: newStudioDialogVisible, show: showNewStudioDialog } = useVisible(false);
const { visible: adminDialogVisible, show: showAdminManageDialog } = useVisible(false);

const dataEmptyImageSize = {
  width: '30%',
};
const isEmpty = computed(() => !!!studio.studioList?.length);

const onNewStudioBtnClick = () => {
  studio.changeCurActiveStudio(null);
  showNewStudioDialog();
};
const onAdminManageBtnClick = () => {
  showAdminManageDialog();
};
</script>
<style lang="scss" scoped></style>
