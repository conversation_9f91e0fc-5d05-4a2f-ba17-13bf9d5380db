<template>
  <t-dropdown
    trigger="hover"
    placement="bottom-right"
    :options="props.options"
    :destroy-on-close="true"
  >
    <div class="flex items-center cursor-pointer z-10">
      <t-icon
        name="ellipsis"
        size="16"
      />
    </div>
  </t-dropdown>
</template>

<script setup lang="ts">
import { TdDropdownItemProps } from 'tdesign-vue-next';

interface IProps {
  options: TdDropdownItemProps[];
}
const props = defineProps<IProps>();
</script>
<style lang="scss" scoped></style>
