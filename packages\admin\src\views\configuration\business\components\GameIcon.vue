<template>
  <div
    class="h-full w-full rounded-default overflow-hidden flex items-center justify-center exclude-mode"
    :style="{ backgroundColor: bgColor ?? 'transparent' }"
  >
    <t-image
      v-if="isValidateUrl"
      alt="game icon"
      class="h-full w-full exclude-mode"
      fit="fill"
      :src="icon"
      :lazy="true"
    >
      <template #loading>
        <div class="w-full h-full relative">
          <div class="w-full h-full bg-black-primary bg-opacity-30 blur-md" />
          <div class="absolute top-0 w-full h-full flex justify-center items-center">
            <t-loading size="small" />
          </div>
        </div>
      </template>
      <template #error>
        <t-image
          :src="gameIconDefaultSVG"
          :lazy="true"
          alt="game icon default"
          fit="fill"
        >
          <template #loading>
            <div class="w-full h-full relative">
              <div class="w-full h-full bg-white-primary bg-opacity-10 blur-md" />
              <div class="absolute top-0 w-full h-full flex justify-center items-center">
                <t-loading size="small" />
              </div>
            </div>
          </template>
          <template #error>
            <div class="text-white-primary text-2xl">
              {{ showContent }}
            </div>
          </template>
        </t-image>
      </template>
    </t-image>
    <div
      v-else
      class="text-white-primary text-2xl"
    >
      {{ showContent }}
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, PropType } from 'vue';
import { isUrl } from 'common/utils/url';
import { generateContrastColor } from 'common/utils/style';
import gameIconDefaultSVG from '@/assets/svg/game-icon_default.svg';

const props = defineProps({
  icon: {
    type: String as PropType<string | undefined>,
    default: '',
  },
  gameName: {
    type: String as PropType<string | undefined>,
    default: '',
  },
  gameCode: {
    type: String as PropType<string | undefined>,
    default: '',
  },
});

const isValidateUrl = computed(() => isUrl(props?.icon || '') || props?.icon?.includes('/src/assets'));
const bgColor = computed(() => {
  const dependentStr = props.gameCode || props?.gameName || '';
  if (dependentStr === '') {
    return undefined;
  }
  return generateContrastColor(props.gameCode || props?.gameName || '');
});

const showContent = computed(() => props.gameName?.slice(0, 1).toUpperCase());
</script>

<style scoped lang="scss">
:deep(.t-image__wrapper) {
  @apply bg-transparent;
}
</style>
