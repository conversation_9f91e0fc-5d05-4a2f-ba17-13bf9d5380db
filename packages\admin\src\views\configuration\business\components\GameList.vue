<template>
  <div
    v-auto-animate
    class="grid grid-cols-1 gap-[20px] mt-[18px] md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4"
  >
    <GameListItem
      v-for="item in props.dataList"
      :key="item.game_id"
      :data="item"
      @click="gameListItemClick(item)"
    />
    <GameListItem
      v-if="store.isCompanyAdmin"
      @click="gameListItemClick()"
    >
      <template #content>
        <div class="w-full h-full flex justify-center items-center opacity-40">
          <div class="plus-logo mr-[10px] h-[50px] w-[50px] text-black-primary text-[50px]">
            <AddIcon class="fill-white-primary" />
          </div>
          <div class="plus-text leading-[20px]">New Game</div>
        </div>
      </template>
    </GameListItem>
  </div>
</template>
<script setup lang="tsx">
import { AddIcon } from 'tdesign-icons-vue-next';
import useBusinessStore from '@/store/configuration/business/business.store';
import GameListItem from './GameListItem.vue';
import type { GetStudioListGameType, Studio } from 'common/service/configuration/business/type/type';
import { inject } from 'vue';
interface IProps {
  dataList?: GetStudioListGameType[];
}
const props = defineProps<IProps>();

const store =  useBusinessStore();
const { game, studio } = store;

// inject data form studioListItem
const studioInfo = inject<Studio | null>('studio', null);

const gameListItemClick = (gameData?: GetStudioListGameType) => {
  const hasGameData = !!gameData;
  hasGameData && game.changeCurrentGame(gameData);
  studio.changeCurActiveStudio(studioInfo);
  game.showDrawer();
};
</script>
<style lang="scss" scoped></style>
