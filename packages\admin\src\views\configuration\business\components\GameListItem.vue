<template>
  <div class="h-[100px] bg-gray-primary px-[16px] py-[18px] rounded-default cursor-pointer active:bg-gray-primary">
    <template v-if="$slots.content"> <slot name="content" /></template>
    <template v-else>
      <div class="flex flex-row">
        <div class="w-[64px] h-[64px]">
          <game-icon
            :icon="props.data?.icon??gameIconDefaultSVG"
          />
        </div>
        <div class="ml-[12px] flex-1 min-w-0">
          <div class="w-full flex justify-between">
            <Text
              weight="600"
              :content="props.data?.game_name"
              class="flex-1 min-w-[100px]"
              :tool-tip="true"
              :overflow="true"
            />
            <div
              v-if="businessStore.isCompanyAdmin||businessStore.isGameAdmin"
              class="flex items-center"
              @click.stop=""
              @click.prevent=""
            >
              <EllipsisDropdown :options="ellipsisDropdownOptions" />
            </div>
          </div>
          <div class="game_info">
            <div class="game_info_item">
              <div class="label"><UserIcon size="12px" /></div>
              <div class="text">{{ props.data?.members }}</div>
            </div>
            <div class="game_info_item">
              <div class="label after:content-[':']">Code</div>
              <div class="text">{{ props.data?.game_code }}</div>
            </div>
          </div>
        </div>
      </div>
      <EditGameDialog
        v-if="editGameDialogVisible"
        v-model:visible="editGameDialogVisible"
      />
    </template>
  </div>
</template>
<script setup lang="tsx">
import type { GetStudioListGameType, Studio } from 'common/service/configuration/business/type/type';
import EllipsisDropdown from './EllipsisDropdown.vue';
import EditGameDialog from './dialog/EditGame.vue';
import { useVisible } from 'common/compose/useVisible';
import { UserIcon } from 'tdesign-icons-vue-next';
import { TdDropdownItemProps } from 'tdesign-vue-next';
import { inject } from 'vue';
import useBusinessStore from '@/store/configuration/business/business.store';
import Text from 'common/components/Text';
import GameIcon from '@/views/configuration/business/components/GameIcon.vue';
import gameIconDefaultSVG from '@/assets/svg/game-icon_default.svg';
interface IProps {
  data?: GetStudioListGameType;
}
const props = defineProps<IProps>();

const businessStore = useBusinessStore();
const { game, studio } = businessStore;

// inject data from studioListItem
const companyId = inject<number>('companyId');
const studioInfo = inject<Studio>('studio');

const { visible: editGameDialogVisible, show: showEditGameDialog } = useVisible(false);
const ellipsisDropdownOptions: TdDropdownItemProps[] = [
  {
    content: 'Rename',
    onClick: () => {
      changeCompanyAndStudioAndGameFromStore();
      showEditGameDialog();
    },
  },
  {
    content: 'Delete',
    onClick: () => {
      changeCompanyAndStudioAndGameFromStore();
      game.showDelGameDialog();
      // showDeleteGameDialog();
    },
  },
];

const changeCompanyAndStudioAndGameFromStore = () => {
  if (props.data) {
    businessStore.changeCompanyId(companyId!);
    studio.changeCurActiveStudio(studioInfo!);
    game.changeCurrentGame(props.data ?? null);
  }
};
</script>
<style lang="scss" scoped>
.game_info_item {
  @apply flex flex-row text-xs text-black-primary opacity-60;

  > .label {
    @apply pr-[5px] flex items-center;
  }
}
</style>
