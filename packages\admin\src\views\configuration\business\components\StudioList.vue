<template>
  <div
    v-auto-animate
    class="flex flex-col"
  >
    <StudioListItem
      v-for="studioItem in props.dataList"
      :key="studioItem.studio_id"
      :data="studioItem"
    />
  </div>
</template>
<script setup lang="tsx">
import { GetStudioListItemType } from 'common/service/configuration/business/type/type.d';
import StudioListItem from './StudioListItem.vue';
interface IProps {
  dataList: GetStudioListItemType[];
}
const props = defineProps<IProps>();
</script>
<style lang="scss" scoped></style>
