<template>
  <div class="aix-studio-list-item mt-[18px] first:mt-0">
    <div
      class="w-full h-[40px] bg-background flex justify-between rounded-default cursor-pointer text-black-primary"
      @click="onStudioInfoBarClick"
    >
      <div class="flex flex-row py-[10px] pl-[16px] leading-[20px] font-[600] space-x-[8px]">
        <div :class="isExpanded ? 'aix-is-expanded' : 'aix-is-closed'">
          <ChevronRightIcon size="20px" />
        </div>
        <span class="inline-block w-[250px] leading-[20px] truncate">
          <t-tooltip
            :content="props.data.studio_name"
            placement="mouse"
          >
            Studio Name: {{ props.data.studio_name }}
          </t-tooltip>
        </span>
        <span class="inline-block w-[180px] truncate leading-[20px]"> Games: {{ props.data.games.length }} </span>
      </div>
      <div
        v-if="businessStore.isCompanyAdmin"
        class="flex items-center"
        @click.stop=""
      >
        <EllipsisDropdown
          :options="ellipsisDropdownOptions"
          class="h-full px-[12px]"
        />
      </div>
    </div>
    <div v-auto-animate>
      <GameList
        v-if="showGame"
        :data-list="props.data.games"
      />
    </div>
    <EditStudioDialog
      v-if="editStudioDialogVisible"
      v-model:visible="editStudioDialogVisible"
    />
    <DeleteStudioDialog
      v-if="deleteStudioDialogVisible"
      v-model:visible="deleteStudioDialogVisible"
      :studio-name="props.data.studio_name"
      :studio-id="props.data.studio_id"
    />
  </div>
</template>
<script setup lang="tsx">
import GameList from './GameList.vue';
import EllipsisDropdown from './EllipsisDropdown.vue';
import EditStudioDialog from './dialog/NewOrEditStudio.vue';
import DeleteStudioDialog from './dialog/DeleteStudio.vue';
import { useVisible } from 'common/compose/useVisible';
import { GetStudioListItemType } from 'common/service/configuration/business/type/type.d';
import { provide, ref } from 'vue';
import useBusinessStore from '@/store/configuration/business/business.store';
import { TdDropdownItemProps } from 'tdesign-vue-next';
import { ChevronRightIcon } from 'tdesign-icons-vue-next';
import { useToggle } from '@vueuse/core';
interface IProps {
  data: GetStudioListItemType;
}
const props = defineProps<IProps>();

provide('companyId', props.data.company_id);
provide('studio', {
  studio_id: props.data.studio_id,
  studio_code: props.data.studio_code,
  studio_name: props.data.studio_name,
});

const businessStore = useBusinessStore();
const { studio } = businessStore;
const { visible: editStudioDialogVisible, show: showEditStudioDialog } = useVisible(false);
const { visible: deleteStudioDialogVisible, show: showDeleteStudioDialog } = useVisible(false);

const isExpanded = ref<boolean>(true);
const ellipsisDropdownOptions: TdDropdownItemProps[] = [
  {
    content: 'Rename',
    onClick: () => {
      changeCompanyAndStudioFromStore();
      showEditStudioDialog();
    },
  },
  {
    content: 'Delete',
    onClick: () => {
      changeCompanyAndStudioFromStore();
      showDeleteStudioDialog();
    },
  },
];
const changeCompanyAndStudioFromStore = () => {
  const { company_id: companyId } = props.data;
  businessStore.changeCompanyId(companyId);
  studio.changeCurActiveStudio(props.data);
};

const [showGame, toggle] = useToggle(true);

const onStudioInfoBarClick = () => {
  toggle();
  isExpanded.value = !isExpanded.value;
};
</script>
<style lang="scss" scoped>
.aix-is-closed {
  @apply transition ease-in-out delay-0 duration-300 rotate-0;
}
.aix-is-expanded {
  @apply transition ease-in-out delay-0 duration-300 rotate-90;
}
</style>
