<template>
  <t-button
    variant="text"
    :content="content"
    :loading="isLoading"
    :disabled="isLoading"
    @click="handleClick"
  />
</template>

<script lang="tsx" setup>
import { TransitionPresets, useTransition } from '@vueuse/core';
import { useTips } from 'common/compose/tips';
import { ref, computed, reactive } from 'vue';
import { InvitedUser } from 'common/service/configuration/business/type/type.d';
import { DialogPlugin } from 'tdesign-vue-next';
import { resendInvitationEmail } from 'common/service/configuration/business/game';
import useBusinessStore from '@/store/configuration/business/business.store';

interface IProps {
  duration: number;
  data: InvitedUser;
  content: string;
}

const props = defineProps<IProps>();
const { success, err } = useTips();
const { game } = useBusinessStore();

const targetTime = ref(0);
const originTime = ref(props.duration);
const isLoading = ref<boolean>(false);
const confirmBtn = reactive({
  content: 'Confirm',
  isLoading: false,
});

const curTime = useTransition(originTime, {
  duration: props.duration * 1000,
  transition: TransitionPresets.linear,
  onFinished() {
    isLoading.value = false;
    targetTime.value = props.duration - curTime.value;
  },
});

const content = computed(() => {
  if (!isLoading.value) {
    return props.content;
  }

  const time = curTime.value;
  if (targetTime.value > 0) {
    return `${Math.ceil(targetTime.value - time)} s`;
  }
  return `${Math.ceil(time)} s`;
});
const handleClick = () => {
  const resendEmailInstance = DialogPlugin.confirm({
    header: 'Tips',
    body: 'Are you sure you want to resend the invitation email?',
    confirmBtn,
    cancelBtn: 'Cancel',
    onConfirm: () => {
      (async () => {
        try {
          confirmBtn.isLoading = true;
          await resendInvitationEmail({
            game_id: props.data.game_id,
            invite_user_email: props.data.user_id,
          });
          success('Invitation sent successfully');
          originTime.value = targetTime.value;
          isLoading.value = true;
          resendEmailInstance.hide();
          game.initTable();
        } catch (e) {
          err((e as any)?.message || 'Resend failed');
        } finally {
          confirmBtn.isLoading = false;
        }
      })();
    },
    onClose: () => {
      resendEmailInstance.hide();
    },
  });
};
</script>
