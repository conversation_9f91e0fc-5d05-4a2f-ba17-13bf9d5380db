<template>
  <BaseDialog
    title="Admin Manage"
    width="450px"
    :visible="props.visible"
    :is-show-footer="false"
    @close="onClose"
  >
    <t-form
      ref="formRef"
      label-width="0"
      :disabled="addAdminBtnLoading"
      :data="formData"
      :rules="rules"
      @submit="onSubmit"
    >
      <div class="flex flex-row">
        <t-form-item
          name="userEmail"
          class="grow mr-4"
          :required-mark="false"
        >
          <Input
            v-model="formData.userEmail"
            placeholder="Please enter user email"
            trim
            clearable
          />
        </t-form-item>
        <t-form-item>
          <t-button
            class="w-[80px]"
            content="Add"
            :loading="addAdminBtnLoading"
            type="submit"
          />
        </t-form-item>
      </div>
    </t-form>
    <div class="h-[400px]">
      <t-table
        row-key="id"
        :data="adminUserList"
        :columns="columns"
        :loading="tableLoading"
        max-height="400px"
      >
        <template #action="{ row }">
          <Button
            theme="primary"
            variant="text"
            content="Remove"
            :disabled="!canRemove"
            @click="onRemoveBtnClick(row)"
          />
        </template>
      </t-table>
    </div>
  </BaseDialog>
</template>
<script setup lang="tsx">
import { reactive, ref, computed } from 'vue';
import BaseDialog from 'common/components/Dialog/Base';
import Input from 'common/components/Input/index.vue';
import { Form, FormRules, SubmitContext, PrimaryTableCol, DialogPlugin, Button } from 'tdesign-vue-next';
import useBusinessStore from '@/store/configuration/business/business.store';
import { useTips } from 'common/compose/tips';
import { useLoading } from 'common/compose/loading';
import {
  getAdminListByCompany,
  addAdminByCompany,
  deleteAdminByCompany,
} from 'common/service/configuration/business/company';
import {
  type GetAdminListByCompanyReturnType,
  type GetAdminListByCompanyAdminItem,
} from 'common/service/configuration/business/type/type.d';
interface IProps {
  visible?: boolean;
}
const props = withDefaults(defineProps<IProps>(), {
  visible: false,
});

const { companyId }  = useBusinessStore();
const { success, err } = useTips();
const emit = defineEmits(['update:visible']);

const formData = reactive({
  userEmail: '',
});
const formRef = ref<(InstanceType<typeof Form> & HTMLFormElement) | null>(null);
const { isLoading: tableLoading, showLoading: showTableLoading, hideLoading: hideTableLoading } = useLoading(false);
const {
  isLoading: addAdminBtnLoading,
  showLoading: showAddAdminBtnLoading,
  hideLoading: hideAddAdminBtnLoading,
} = useLoading(false);

const rules: FormRules<typeof formData> = {
  userEmail: [{ required: true, message: 'Email should not be empty' }],
};
const columns = ref<PrimaryTableCol[]>([
  {
    colKey: 'email',
    title: 'Email',
    ellipsis: {
      theme: 'light',
      placement: 'top',
      showArrow: false,
    },
  },
  { colKey: 'action', title: 'Action' },
]);
const adminUserList = ref<GetAdminListByCompanyReturnType>([]);
const deleteConfirmBtn = reactive({
  content: 'Confirm',
  loading: false,
});
const canRemove = computed(() => adminUserList.value.length > 1);

const initTable = async () => {
  try {
    showTableLoading();
    const adminList = await getAdminListByCompany({ company_id: companyId });
    changeAdminUserList(adminList);
  } catch (e) {
    err((e as any).message ?? 'Failed to get admin list');
  } finally {
    hideTableLoading();
  }
};

const changeAdminUserList = (newAdminUserList: GetAdminListByCompanyReturnType) => {
  adminUserList.value = newAdminUserList;
};

const onRemoveBtnClick = async (row: GetAdminListByCompanyAdminItem) => {
  const removeConfirmDialogInstance = DialogPlugin.confirm({
    header: 'Tips',
    body: 'Are you sure you want to delete this admin?',
    confirmBtn: deleteConfirmBtn,
    cancelBtn: 'Cancel',
    onConfirm: () => {
      (async () => {
        try {
          deleteConfirmBtn.loading = true;
          await deleteAdminByCompany({
            company_id: companyId,
            uid: row.email,
          });
          success('Removed successfully');
          removeConfirmDialogInstance.hide();
          initTable();
        } catch (e) {
          err((e as any).message ?? 'Failed to remove');
        } finally {
          deleteConfirmBtn.loading = false;
        }
      })();
    },
    onClose: () => {
      removeConfirmDialogInstance.hide();
    },
  });
};

const onClose = () => {
  emit?.('update:visible', false);
  formRef.value?.reset();
};

const onSubmit = async (context: SubmitContext<FormData>) => {
  const { validateResult } = context;

  if (validateResult === true) {
    try {
      showAddAdminBtnLoading();
      await addAdminByCompany({
        company_id: companyId,
        uid: formData.userEmail,
      });
      success('Added successfully');
      formRef.value?.reset();
      initTable();
    } catch (e) {
      err('Failed to add');
    } finally {
      hideAddAdminBtnLoading();
    }
  }
};

initTable();
</script>
<style lang="scss" scoped>

</style>
