<template>
  <BaseDialog
    title="Delete Game"
    width="450px"
    class="t-dialog__delete-dialog"
    confirm-text="Delete"
    confirm-theme="danger"
    :visible="props.visible"
    :confirm-loading="confirmBtnLoading"
    @close="onClose"
    @confirm="onConfirm"
  >
    <t-form
      ref="formRef"
      class="pb-[20px]"
      label-align="top"
      :data="formData"
      :rules="rules"
      @submit="onSubmit"
    >
      <t-form-item
        name="gameName"
        :required-mark="false"
      >
        <template #label>
          <div class="whitespace-normal break-words text-sm leading-[20px]">
            Please enter“<span class="font-[600]">{{ storeGame?.curGame?.game_name }}</span>”below to confirm deletion
          </div>
        </template>
        <div class="mt-[8px] w-full"><Input v-model="formData.gameName" trim /></div>
      </t-form-item>
    </t-form>
  </BaseDialog>
</template>
<script setup lang="tsx">
import { reactive, ref } from 'vue';
import BaseDialog from 'common/components/Dialog/Base';
import Input from 'common/components/Input/index.vue';
import { Form, FormRules, SubmitContext } from 'tdesign-vue-next';
import useBusinessStore from '@/store/configuration/business/business.store';
import { useTips } from 'common/compose/tips';
import { deleteGame } from 'common/service/configuration/business/game';
interface IProps {
  visible?: boolean;
}
const props = withDefaults(defineProps<IProps>(), {
  visible: false,
});

const businessStore = useBusinessStore();
const { success, err } = useTips();
const { game: storeGame, studio } = businessStore;
const emit = defineEmits(['update:visible']);

const formData = reactive({
  gameName: '',
});
const formRef = ref<(InstanceType<typeof Form> & HTMLFormElement) | null>(null);
const confirmBtnLoading = ref<boolean>(false);
const rules: FormRules<typeof formData> = {
  gameName: [
    { required: true, message: 'Game Name should not be empty' },
    {
      validator: (val: string) => storeGame?.curGame?.game_name === val,
      message: 'Information input error, please check and fill in again and try again.',
    },
  ],
};

const onConfirm = () => {
  formRef.value?.submit();
};

const onClose = () => {
  emit?.('update:visible', false);
  formRef.value?.reset();
  !storeGame.drawerVisible && businessStore.reset();
};

const onSubmit = async (context: SubmitContext<FormData>) => {
  const { validateResult } = context;

  if (validateResult === true) {
    try {
      confirmBtnLoading.value = true;
      await deleteGame({
        company_id: businessStore.companyId,
        studio_id: studio.curActiveStudio!.studio_id,
        game_id: storeGame.curGame!.game_id!,
      });
      success('Deleted successfully');
      storeGame.drawerVisible && storeGame.hideDrawer();
      onClose();
      studio.updateStudioList();
    } catch (e) {
      err((e as any)?.message || 'Deleted failed');
    } finally {
      confirmBtnLoading.value = false;
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.t-input__extra) {
  @apply top-[50px];
}
</style>
