<template>
  <BaseDialog
    title="Delete Studio"
    class="t-dialog__delete-dialog"
    confirm-text="Delete"
    confirm-theme="danger"
    width="450px"
    :visible="props.visible"
    :confirm-loading="confirmBtnLoading"
    @close="onClose"
    @confirm="onConfirm"
  >
    <t-form
      ref="formRef"
      class="pb-[20px]"
      label-align="top"
      :data="formData"
      :rules="rules"
      @submit="onSubmit"
    >
      <t-form-item
        name="studioName"
        :required-mark="false"
      >
        <template #label>
          <div class="whitespace-normal break-words text-sm leading-[20px]">
            "Deleting the project studio will delete all sub-game projects and members, which cannot be restored. Please
            enter“
            <span class="font-[600]">{{ props.studioName }}</span>
            ”below to complete the deletion.
          </div>
        </template>
        <div class="mt-[8px] w-full"><Input v-model="formData.studioName" trim /></div>
      </t-form-item>
    </t-form>
  </BaseDialog>
</template>
<script setup lang="tsx">
import { inject, reactive, ref } from 'vue';
import Input from 'common/components/Input/index.vue';
import { Form, FormRules, SubmitContext } from 'tdesign-vue-next';
import useBusinessStore from '@/store/configuration/business/business.store';
import { Studio } from 'common/service/configuration/business/type/type.d';
import BaseDialog from 'common/components/Dialog/Base';
import { useTips } from 'common/compose/tips';
import { deleteStudio } from 'common/service/configuration/business/studio';
interface IProps {
  studioName: string;
  studioId: number;
  visible?: boolean;
}
const props = withDefaults(defineProps<IProps>(), {
  visible: false,
});

const businessStore = useBusinessStore();
const { studio } = businessStore;
const { success, err } = useTips();

const emit = defineEmits(['update:visible']);

const studioInfo = inject<Studio>('studio');

const formData = reactive({
  studioName: '',
});
const formRef = ref<(InstanceType<typeof Form> & HTMLFormElement) | null>(null);
const confirmBtnLoading = ref<boolean>(false);
const rules: FormRules<typeof formData> = {
  studioName: [
    { required: true, message: 'Studio Name should not be empty' },
    {
      validator: (val: string) => props.studioName === val,
      message: 'Information input error, please check and fill in again and try again.',
    },
  ],
};

const onConfirm = () => {
  formRef.value?.submit();
};

const onClose = () => {
  emit?.('update:visible', false);
  formRef.value?.reset();
  businessStore.reset();
};

const onSubmit = async (context: SubmitContext<FormData>) => {
  const { validateResult } = context;

  if (validateResult === true) {
    try {
      confirmBtnLoading.value = true;
      await deleteStudio({
        company_id: businessStore.companyId,
        studio_id: studioInfo!.studio_id,
      });
      success('Deleted successfully');
      onClose();
      studio.updateStudioList();
    } catch (error) {
      err((error as any)?.message || 'Deleted failed');
    } finally {
      confirmBtnLoading.value = false;
    }
  }
};
</script>
<style lang="scss" scoped>

</style>
