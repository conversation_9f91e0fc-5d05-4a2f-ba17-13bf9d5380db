<template>
  <BaseDialog
    title="Rename Game"
    width="450px"
    :visible="props.visible"
    :confirm-loading="confirmBtnLoading"
    :confirm-disabled="!canEdit"
    @close="onClose"
    @confirm="onConfirm"
  >
    <div>
      <t-form
        ref="formRef"
        class="h-[80px]"
        :data="formData"
        label-align="top"
        :rules="rules"
        @submit="onSubmit"
      >
        <t-form-item
          label="Game Name"
          name="gameName"
          :required-mark="true"
        >
          <div class="w-full"><Input v-model="formData.gameName" trim /></div>
        </t-form-item>
      </t-form>
    </div>
  </BaseDialog>
</template>
<script setup lang="tsx">
import { reactive, ref, computed } from 'vue';
import Input from 'common/components/Input/index.vue';
import { SubmitContext, Form, FormRules } from 'tdesign-vue-next';
import useBusinessStore from '@/store/configuration/business/business.store';
import { NAME_PATTERN } from '../const';
import { useLoading } from 'common/compose/loading';
import BaseDialog from 'common/components/Dialog/Base';
import { modifyGame } from 'common/service/configuration/business/game';
import { useTips } from 'common/compose/tips';

interface IProps {
  visible: boolean;
}
const props = defineProps<IProps>();

const businessStore = useBusinessStore();
const { game, studio } = businessStore;
const { success, err } = useTips();

const formRef = ref<(InstanceType<typeof Form> & HTMLFormElement) | null>(null);
const {
  isLoading: confirmBtnLoading,
  showLoading: showConfirmBtnLoading,
  hideLoading: hideConfirmBtnLoading,
} = useLoading(false);

const emit = defineEmits(['update:visible']);

const formData = reactive({
  gameName: game.curGame?.game_name ?? '',
});
const canEdit = computed(() => formData.gameName !== (game.curGame?.game_name ?? ''));

const rules: FormRules<typeof formData> = {
  gameName: [
    { required: true, message: 'Game Name should not be empty' },
    {
      validator: val => val.length <= 20,
      message: 'Game Name can be up to 20 characters',
    },
    {
      pattern: NAME_PATTERN,
      message:
        'Game Name can only contain numbers, letters, spaces, and underscores. It can only start and end with letters.',
    },
  ],
};

const onConfirm = () => {
  formRef.value?.submit();
};

const onClose = () => {
  emit?.('update:visible', false);
  formRef.value?.reset();
  businessStore.reset();
};

const onSubmit = async (context: SubmitContext<FormData>) => {
  const { validateResult } = context;
  if (validateResult === true && game.curGame) {
    try {
      showConfirmBtnLoading();
      await modifyGame({
        game_name: formData.gameName,
        game_id: game.curGame.game_id!,
      });
      success('Modified successfully');
      studio.updateStudioList();
      onClose();
    } catch (e) {
      err((e as any)?.message || 'Modified failed');
    } finally {
      hideConfirmBtnLoading();
    }
  }
};
</script>

<style lang="scss" scoped>
:deep(.t-input__extra) {
  @apply top-[40px];
}
</style>
