<template>
  <BaseDialog
    title="Edit User"
    width="500px"
    confirm-text="Save"
    :visible="props.visible"
    :confirm-loading="confirmBtnLoading"
    @close="onClose"
    @confirm="onConfirm"
  >
    <div class="flex flex-col">
      <div class="flex flex-row h-[42px] mb-4">
        <div class="label">Email</div>
        <div class="text">{{ props.data?.user_id }}</div>
      </div>
    </div>
    <t-form
      ref="formRef"
      :data="formData"
      @submit="onSubmit"
    >
      <t-form-item
        label="Role"
        name="role"
        label-align="left"
        label-width="50px"
        class="role_form_item"
      >
        <div class="max-w-[350px]">
          <Select
            v-model="formData.role_id"
            title=""
            :list="game?.roleOptions ?? []"
            :multiple="false"
            max-width="300"
            :no-wrap-line="true"
          />
        </div>
      </t-form-item>
    </t-form>
  </BaseDialog>
</template>
<script setup lang="tsx">
import { reactive, ref, watch } from 'vue';
import BaseDialog from 'common/components/Dialog/Base';
import { Form, SubmitContext } from 'tdesign-vue-next';
import useBusinessStore from '@/store/configuration/business/business.store';
import { InvitedUser } from 'common/service/configuration/business/type/type.d';
import { difference } from 'lodash-es';
import { useTips } from 'common/compose/tips';
import Select from 'common/components/Select';
import { modifyUser } from 'common/service/configuration/business/game';
interface IProps {
  visible: boolean;
  data: InvitedUser | null;
}

type TDifferentRoleIds = {
  addRoleIds: number[];
  removeRoleIds: number[];
};
const props = withDefaults(defineProps<IProps>(), {
  data: null,
});
const emit = defineEmits(['update:visible']);
const businessStore = useBusinessStore();
const { success, err } = useTips();
const { game, studio } = businessStore;

const formRef = ref<(InstanceType<typeof Form> & HTMLFormElement) | null>(null);

const formData = reactive({
  role_id: props.data?.role_id,
});
const confirmBtnLoading = ref<boolean>(false);

const onConfirm = () => {
  formRef.value?.submit();
};

const onClose = () => {
  emit('update:visible', false);
  formRef.value?.reset();
};

const onSubmit = async (context: SubmitContext<FormData>) => {
  const { validateResult } = context;

  if (validateResult === true) {
    try {
      confirmBtnLoading.value = true;
      // TODO: 当前版本仅支持单选角色，此写法为了兼容后续多选
      const oldRoleIds = props.data && props.data?.role_id >= 0 ? [props.data.role_id] : [];
      const newRoleIds = formData.role_id && formData.role_id >= 0 ? [formData.role_id] : [];
      const { addRoleIds, removeRoleIds } = getAddAndRemoveRoleIds(oldRoleIds, newRoleIds);

      await modifyUser({
        company_id: businessStore.companyId,
        game_id: game.curGame!.game_id!,
        studio_id: studio.curActiveStudio?.studio_id,
        user_status: props.data!.invite_status,
        add_roles: addRoleIds,
        remove_roles: removeRoleIds,
        uid: props.data!.user_id,
      });
      success('Modified successfully');
      onClose();
      game.initTable();
    } catch (error) {
      err((error as any)?.message || 'Modified failed');
    } finally {
      confirmBtnLoading.value = false;
    }
  }
};

const getAddAndRemoveRoleIds = (oldRoleIds: number[], newRoleIds: number[]): TDifferentRoleIds => {
  const addRoleIds = difference(newRoleIds, oldRoleIds);
  const removeRoleIds = difference(oldRoleIds, newRoleIds);

  return {
    addRoleIds,
    removeRoleIds,
  };
};

watch(
  () => props.data?.role_id,
  () => {
    formData.role_id = props.data?.role_id;
  },
);
watch(
  () => props.visible,
  () => {
    formData.role_id = props.data?.role_id;
  },
);
</script>
<style lang="scss" scoped>
.label {
  @apply w-[50px] text-opacity-40 text-black-primary flex items-center;
}
.text {
  @apply text-black-primary text-opacity-90 flex items-center;
}
:deep(.t-form__label) {
  @apply text-opacity-40 text-black-primary;
}
</style>
