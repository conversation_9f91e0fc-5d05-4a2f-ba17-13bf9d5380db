<template>
  <BaseDialog
    title="Invite User"
    width="500px"
    confirm-text="Invite"
    :visible="props.visible"
    :confirm-loading="confirmBtnLoading"
    @close="onClose"
    @confirm="onConfirm"
  >
    <t-form
      ref="formRef"
      class="pb-[20px]"
      :data="formData"
      :rules="rules"
      @submit="onSubmit"
    >
      <t-form-item
        label="Email Address"
        name="invite_user_email"
        :required-mark="true"
        label-align="top"
        help="Please press the Enter key to confirm the email address of the invited user."
      >
        <div class="w-full invite_email">
          <t-tag-input
            v-model="inviteEmailProxy"
            v-model:inputValue="tagInputValue"
            clearable
            excess-tags-display-type="break-line"
            @input-change="onTagInputValueChange"
            @blur="onTagInputBlur"
            @change="onTagInputChange"
            @paste="onTagInputPaste"
          >
            <template #valueDisplay="{ value, onClose: tagInputClose }">
              <t-tag
                v-for="(item, index) in value"
                :key="item"
                :theme="generateTagTheme(item)"
                :content="item"
                variant="light"
                closable
                @close="() => tagInputClose(index)"
              />
            </template>
          </t-tag-input>
        </div>
      </t-form-item>
      <t-form-item
        label="Role"
        name="role_id"
        label-align="left"
        label-width="50px"
        class="role_form_item"
      >
        <div class="max-w-[350px]">
          <Select
            v-model="formData.role_id"
            title=""
            :list="game.roleOptions"
            :multiple="false"
            max-width="300"
            :no-wrap-line="true"
          />
        </div>
      </t-form-item>
    </t-form>
  </BaseDialog>
</template>
<script setup lang="tsx">
import { reactive, ref, watch, computed } from 'vue';
import BaseDialog from 'common/components/Dialog/Base';
import { Form, FormRules, InputValueChangeContext, SubmitContext } from 'tdesign-vue-next';
import useBusinessStore from '@/store/configuration/business/business.store';
import { inviteUser } from 'common/service/configuration/business/game';
import { useTips } from 'common/compose/tips';
import { TValidateEmailStatusList, validateEmail } from 'common/service/user/register';
import { EmailStatus } from 'common/service/user/enum';
import { uniq } from 'lodash-es';
import Select from 'common/components/Select';
import validator from 'validator';
import isEmail = validator.isEmail;
import { useDebounceFn } from '@vueuse/core';

interface IProps {
  visible: boolean;
}
const props = withDefaults(defineProps<IProps>(), {});
const emit = defineEmits(['update:visible']);

const businessStore = useBusinessStore();
const { success, err } = useTips();
const { game, studio } = businessStore;

const formData = reactive<{
  invite_user_email: string[];
  role_id?: number;
}>({
  invite_user_email: [],
  role_id: undefined,
});

const inviteEmailProxy = computed<string[]>({
  get() {
    return formData.invite_user_email;
  },
  set(newVal: string[]) {
    formData.invite_user_email = newVal.map(item => item.trim()).filter(item => !!item);
  },
});

const tagInputValue = ref<string>('');
const tempTagInputValue = ref<string>('');

const confirmBtnLoading = ref<boolean>(false);

const formRef = ref<(InstanceType<typeof Form> & HTMLFormElement) | null>(null);

// 延迟任务队列，调整函数调用时机
let delayTaskQueue: Array<Function> = [];
const rules: FormRules<typeof formData> = {
  invite_user_email: [
    { required: true, message: 'Email Address should not be empty' },
    {
      validator: (val: string[]) => {
        const findInvalidEmail = val.find((email: string) => !isEmail(email));
        if (findInvalidEmail) {
          return {
            result: false,
            type: 'error',
            message: `${findInvalidEmail} is not a valid email address`,
          };
        }
        return true;
      },
    },
  ],
  role_id: [{ required: true, message: 'role should not be empty' }],
};

const onConfirm = () => {
  formRef.value?.submit();
};
const generateTagTheme = (email: string) => (isEmail(email) ? 'success' : 'danger');
const onClose = () => {
  emit?.('update:visible', false);
  formRef.value?.reset();
};
const onSubmit = async (context: SubmitContext<FormData>) => {
  const { validateResult } = context;

  if (validateResult === true) {
    try {
      confirmBtnLoading.value = true;
      const { companyId } = businessStore;
      const roleId = formData.role_id;
      const inviteUserList = uniq(formData.invite_user_email);

      // 检查邮箱是否被使用
      const { list: inviteEmailStatusList } = await validateEmail({
        emails: inviteUserList,
        company_id: companyId,
      });

      const invalidEmailList = getInvalidEmailList(inviteEmailStatusList);
      if (invalidEmailList.length > 0) {
        throw {
          message: `${invalidEmailList.join(',')} has been invited by another company`,
        };
      }
      const result = (await inviteUser({
        game_id: game.curGame!.game_id,
        company_id: companyId,
        studio_id: studio.curActiveStudio!.studio_id,
        role_id: roleId,
        invite_user_email: inviteUserList,
      })) as any;

      if (!result) {
        err('Somethings wrong, no user invited');
        return;
      };

      success('Invited successfully');
      onClose();
      game.initTable();
    } catch (e) {
      err((e as any)?.message || 'Invited failed');
    } finally {
      confirmBtnLoading.value = false;
    }
  }
};

const getInvalidEmailList = (emailList: TValidateEmailStatusList): string[] => {
  const res = emailList.reduce((invalidEmailList, curEmail) => {
    curEmail.status === EmailStatus.INVALID && invalidEmailList.push(curEmail.email);
    return invalidEmailList;
  }, [] as string[]);
  return res;
};

const onTagInputValueChange = (inputValue: string, context: InputValueChangeContext) => {
  const { trigger } = context;

  if (trigger === 'input') {
    setTempTagInputValue(inputValue);
  }

  if (!!delayTaskQueue.length) {
    delayTaskQueue.forEach((fn) => {
      fn();
    });
    delayTaskQueue = [];
  }
};

const onTagInputChange = () => {
  setTempTagInputValue('');
};

const setTempTagInputValue = useDebounceFn((val) => {
  tempTagInputValue.value = val;
}, 100);

const onTagInputBlur = () => {
  if (!!tempTagInputValue.value) {
    inviteEmailProxy.value = [...inviteEmailProxy.value, tempTagInputValue.value];
    tempTagInputValue.value = '';
  }
};

const onTagInputPaste = ({ pasteValue }: { e: ClipboardEvent; pasteValue: string }) => {
  const emailList = [];
  if (tagInputValue.value) {
    emailList.push(tagInputValue.value);
  }

  emailList.push(...pasteValue.split(/[,;]/).filter(item => !!item));
  inviteEmailProxy.value = [...inviteEmailProxy.value, ...emailList];

  const delayTask = () => {
    tagInputValue.value = '';
    setTempTagInputValue('');
  };

  delayTaskQueue.push(delayTask);
};

watch(
  () => game.adminRole,
  () => {
    formData.role_id = game.adminRole?.role_id ?? undefined;
  },
);
watch(
  () => props.visible,
  () => {
    formData.role_id = game.adminRole?.role_id ?? undefined;
  },
);
</script>
<style lang="scss" scoped>
:deep(.t-input__help) {
  @apply relative top-[3px];
}

.role_form_item {
  :deep(.t-input__help) {
    @apply relative left-[-50px];
  }

  :deep(.t-input__extra) {
    @apply left-[-50px];
  }
}

.invite_email {
  @apply h-[150px];

  :deep(.t-input__wrap) {
    @apply h-full;
  }

  :deep(.t-input) {
    @apply h-full overflow-auto items-start;
  }
}

:deep(.t-tag) {
  &.t-tag--success {
    .t-icon.t-icon-close {
      color: var(--td-success-color);

      &:hover {
        color: var(--td-success-color-hover);
      }
    }
  }

  &.t-tag--danger {
    .t-icon.t-icon-close {
      color: var(--td-error-color);

      &:hover {
        color: var(--td-error-color-hover);
      }
    }
  }
}

:deep(.t-input) {
  @apply border-[1px] border-solid border-gray-placeholder;
  box-shadow: 0px 2px 12px rgba(86, 97, 168, 0.12);

  &:hover {
    @apply border-brand;
    box-shadow: 0px 2px 12px rgba(86, 97, 168, 0.2);
  }

  &.t-input--focused {
    @apply border-brand;
    box-shadow: 0px 2px 12px rgba(86, 97, 168, 0.2);
  }
}

:deep(.t-is-error) {
  .t-input {
    @apply border-error-primary;
    box-shadow: 0px 2px 12px rgba(86, 97, 168, 0.1);
  }
}
</style>
