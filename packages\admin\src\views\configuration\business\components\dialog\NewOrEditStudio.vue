<template>
  <BaseDialog
    width="450px"
    :visible="props.visible"
    :title="title"
    :confirm-text="(confirmBtn.content as string)"
    :confirm-loading="confirmBtn.loading"
    :confirm-disabled="!canEdit"
    @close="onClose"
    @confirm="onConfirm"
  >
    <div>
      <t-form
        ref="formRef"
        class="pb-[20px]"
        :data="formData"
        label-align="top"
        :rules="rules"
        @submit="onSubmit"
      >
        <t-form-item
          label="Studio Name"
          name="studioName"
          :required-mark="true"
        >
          <div class="w-full"><Input v-model="formData.studioName" trim /></div>
        </t-form-item>
      </t-form>
    </div>
  </BaseDialog>
</template>
<script setup lang="tsx">
import { computed, reactive, ref, watch } from 'vue';
import Input from 'common/components/Input/index.vue';
import { SubmitContext, Form, FormRules, ButtonProps } from 'tdesign-vue-next';
import useBusinessStore from '@/store/configuration/business/business.store';
import { NAME_PATTERN } from '../const';
import { v4 as uuidv4 } from 'uuid';
import BaseDialog from 'common/components/Dialog/Base';
import { createStudio, modifyStudio } from 'common/service/configuration/business/studio';
import { useTips } from 'common/compose/tips';
interface IProps {
  visible: boolean;
}
const props = defineProps<IProps>();
const businessStore = useBusinessStore();
const { studio } = businessStore;
const { success, err } = useTips();

const emit = defineEmits(['update:visible']);

const formRef = ref<(InstanceType<typeof Form> & HTMLFormElement) | null>(null);
const formData = reactive({
  studioName: studio.curActiveStudio?.studio_name ?? '',
});
const isNew = computed(() => !studio.curActiveStudio?.studio_id);
const canEdit = computed(() => formData.studioName !== (studio.curActiveStudio?.studio_name ?? ''));
const rules: FormRules<typeof formData> = {
  studioName: [
    { required: true, message: 'Studio Name should not be empty' },
    {
      validator: val => val.length <= 20,
      message: 'Studio name can be up to 20 characters',
    },
    {
      pattern: NAME_PATTERN,
      message:
        'Studio Name can only contain numbers, letters, spaces, and underscores. It can only start and end with letters.',
    },
  ],
};

const confirmBtn = reactive<ButtonProps>({
  content: '',
  loading: false,
});
const title = computed<string>(() => (isNew.value ? 'New Studio' : 'Rename Studio'));

const onConfirm = () => {
  formRef.value?.submit();
};

const onClose = () => {
  emit?.('update:visible', false);
  formRef.value?.reset();
  businessStore.reset();
};

const onSubmit = async (context: SubmitContext<FormData>) => {
  const { validateResult } = context;
  if (validateResult === true) {
    confirmBtn.loading = true;

    if (isNew.value) {
      try {
        await createStudio({
          studio_name: formData.studioName,
          company_id: businessStore.companyId,
          studio_code: uuidv4(),
        });
        success('Created successfully');
      } catch (error) {
        err((error as any)?.message || 'Created failed');
        confirmBtn.loading = false;
        return;
      }
    } else {
      try {
        await modifyStudio({
          studio_code: studio.curActiveStudio!.studio_code,
          studio_name: formData.studioName,
          company_id: businessStore.companyId,
          studio_id: studio.curActiveStudio!.studio_id,
        });
        success('Modified successfully');
      } catch (error) {
        err((error as any)?.message || 'Modified failed');
        confirmBtn.loading = false;
        return;
      }
    }
    studio.updateStudioList();
    confirmBtn.loading = false;
    onClose();
  }
};

watch(
  () => isNew.value,
  () => {
    confirmBtn.content = isNew.value ? 'Create' : 'Save';
  },
  { immediate: true },
);
</script>

<style lang="scss" scoped>
:deep(.t-input__extra) {
  @apply top-[40px];
}
</style>
