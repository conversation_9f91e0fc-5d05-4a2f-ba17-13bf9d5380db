<template>
  <Dropdown
    :options="props.options"
    :min-column-width="120"
    :popup-props="{ destroyOnClose: false, overlayInnerClassName: 'action-dropdown' }"
  >
    <t-button variant="text">
      <template #icon>
        <MoreIcon />
      </template>
    </t-button>
  </Dropdown>
</template>

<script setup lang="ts">
import { Dropdown, type DropdownOption } from 'tdesign-vue-next';
import { MoreIcon } from 'tdesign-icons-vue-next';

interface IProps {
  options: DropdownOption[]
}

const props = defineProps<IProps>();
</script>

<style lang="scss" scoped>
:global(.action-dropdown .t-dropdown__item) {
  @apply p-0;
}
</style>
