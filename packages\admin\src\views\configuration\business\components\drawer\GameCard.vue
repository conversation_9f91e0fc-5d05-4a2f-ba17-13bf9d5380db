<template>
  <div
    v-auto-animate
    class="flex flex-col rounded-default bg-white-primary py-[15px] px-[20px]"
  >
    <div class="flex items-end font-[600] text-base text-black-primary leading-[22px]">
      Game info
      <template v-if="visibleDependenceByType('Default')">
        <div
          class="ml-[8px] text-link text-sm font-[400] cursor-pointer hover:underline underline-offset-auto"
          @click="onEditBtnClick"
        >
          Edit
        </div>
      </template>
    </div>
    <template v-if="visibleDependenceByType('Default')">
      <GameCardInfo :game="curGameInfo" />
    </template>
    <template v-else>
      <GameCardForm
        ref="gameFormRef"
        v-model:type="gameCardType"
        :form-data="gameFormData"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import type { GetGameMetaReturnDataType } from 'common/service/configuration/business/type/type';
import GameCardInfo from './GameCardInfo.vue';
import GameCardForm from './GameCardForm.vue';
import { IGameFormData, TGameCardType } from '../../type';
import useBusinessStore from '@/store/configuration/business/business.store';
import { computed, ref, watch } from 'vue';
interface IProps {
  game?: GetGameMetaReturnDataType;
  type?: TGameCardType;
}
const props = withDefaults(defineProps<IProps>(), {
  game: undefined,
  type: 'Default',
});

const businessStore = useBusinessStore();
const { game: storeGame } = businessStore;
const gameCardType = ref<TGameCardType>(props.type);
const visibleDependenceByType = computed(() => (type: TGameCardType) => gameCardType.value === type);
const curGameInfo = computed(() => storeGame.curGame);

const gameFormRef = ref<InstanceType<typeof GameCardForm> | null>(null);
const isChanged = computed<boolean>(() => gameFormRef.value?.isChanged ?? false);

const gameFormData = ref<IGameFormData>({
  gameName: props.game?.game_name ?? '',
  gameCode: props.game?.game_code ?? '',
  iosID: props.game?.ios_appid ?? '',
  androidID: props.game?.android_appid ?? '',
  image: props.game?.icon ? [{ url: props.game.icon }] : [],
  type: props.game?.type ?? 'pc',
});

const onEditBtnClick = () => {
  gameCardType.value = 'Update';
};

watch(
  () => props.game,
  () => {
    gameFormData.value = {
      gameName: props.game?.game_name ?? '',
      gameCode: props.game?.game_code ?? '',
      iosID: props.game?.ios_appid ?? '',
      androidID: props.game?.android_appid ?? '',
      image: props.game?.icon ? [{ url: props.game.icon }] : [],
      type: props.game?.type ?? 'pc',
    };
  },
);

defineExpose({
  isChanged,
});
</script>
<style lang="scss" scoped></style>
