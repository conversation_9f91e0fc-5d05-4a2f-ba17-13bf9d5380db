<template>
  <t-form
    ref="formRef"
    label-align="top"
    layout="inline"
    class="overflow-hidden"
    :data="formData"
    :rules="rules"
    :disabled="saveBtnIsLoading"
    @submit="onSubmit"
  >
    <div class="f-full w-full flex flex-row pb-[10px] overflow-auto">
      <div class="flex-1 min-w-[400px]">
        <div class="grid grid-cols-2 gap-[20px] w-full">
          <t-form-item
            label="Name"
            :required-mark="true"
            name="gameName"
            class="w-full"
          >
            <Input
              v-model="formData.gameName"
              trim
            />
          </t-form-item>

          <t-form-item
            label="Game Code"
            :required-mark="true"
            name="gameCode"
            class="w-full"
          >
            <Input
              v-model="formData.gameCode"
              :disabled="gameCodeIsDisabled"
              trim
            />
          </t-form-item>
          <t-form-item
            label="IOS APP ID"
            name="iosID"
            class="w-full"
          >
            <Input
              v-model="formData.iosID"
              trim
            />
          </t-form-item>
          <t-form-item
            label="Android APP ID"
            name="androidID"
            class="w-full"
          >
            <Input
              v-model="formData.androidID"
              trim
            />
          </t-form-item>
          <t-form-item
            label="Game Type"
            name="type"
            class="w-full"
          >
            <t-select
              v-model="formData.type"
              :options="GAME_TYPE_OPTIONS"
            />
          </t-form-item>
        </div>
      </div>
      <div class="px-[10px]">
        <t-divider
          layout="vertical"
          class="h-full"
        />
      </div>
      <div>
        <t-form-item
          name="image"
          class="!mr-[0px]"
        >
          <template #label>
            <div class="flex flex-row">
              <div class="mr-[10px]">Logo</div>
              <div class="text-xs text-gray-primary opacity-40 flex items-center">
                Upload square image (Max support 500x500px,JPG or PNG)
              </div>
            </div>
          </template>
          <UploadImage
            v-model="formData.image"
            accept=".jpg, .png"
          />
        </t-form-item>
      </div>
    </div>
  </t-form>
  <template v-if="footerBtnVisible">
    <div class="footer flex flex-row justify-start mt-[20px]">
      <div class="mr-[16px]">
        <t-button
          theme="default"
          @click="onUndoBtnClick"
        >
          Undo
        </t-button>
      </div>
      <div>
        <t-button
          theme="primary"
          type="submit"
          :disabled="!isChanged"
          :loading="saveBtnIsLoading"
          @click="onSaveBtnClick"
        >
          Save
        </t-button>
      </div>
    </div>
  </template>
</template>

<script setup lang="ts">
import { computed, inject, reactive, ref } from 'vue';
import Input from 'common/components/Input/index.vue';
import UploadImage from './UploadImage.vue';
import { DialogPlugin, Form, FormRules, SubmitContext } from 'tdesign-vue-next';
import { CODE_PATTERN, NAME_PATTERN, GAME_TYPE_OPTIONS } from '../const';
import { useEventBus } from '@vueuse/core';
import useBusinessStore from '@/store/configuration/business/business.store';
import { IGameFormData, TGameCardType } from '../../type';
import { TGame } from '@/store/configuration/business/useGame';
import { ValidateGameCodeStatus } from '../../enum';
import {
  createGame as createGameService,
  modifyGame as modifyGameService,
  validateGameCode,
} from 'common/service/configuration/business/game';
import { useTips } from 'common/compose/tips';

interface IProps {
  type: TGameCardType;
  formData: IGameFormData;
}
const props = defineProps<IProps>();
const businessStore = useBusinessStore();
const { game, studio } = businessStore;

const { success, err } = useTips();
const gameFormEventBus = useEventBus<string>('GameForm');

// inject data from './index.vue'
const changeCreateBtnLoading = inject<(isLoading: boolean) => void>('changeCreateBtnLoading');

const formRef = ref<(InstanceType<typeof Form> & HTMLFormElement) | null>(null);
const formData = reactive<IGameFormData>({ ...props.formData });
const rawFormData = JSON.stringify(props.formData);
const saveBtnIsLoading = ref<boolean>(false);
const isChanged = computed<boolean>(() => JSON.stringify(formData) !== rawFormData);
const gameCodeIsDisabled = computed<boolean>(() => props.type === 'Update');

const rules: FormRules<typeof formData> = {
  gameName: [
    { required: true, message: 'Name should not be empty' },
    {
      validator: val => val.length <= 20,
      message: 'Name can be up to 20 characters',
    },
    {
      pattern: NAME_PATTERN,
      message: 'Name can only contain numbers, letters, and underscores. It cannot start or end with an underscore.',
    },
  ],
  gameCode: [
    { required: true, message: 'Game Code should not be empty' },
    {
      validator: val => val.length <= 20,
      message: 'Game code can be up to 20 characters',
    },
    {
      validator: (val) => {
        if (/[A-Z]+/.test(val)) {
          return {
            result: false,
            message: 'Game code must be lowercase',
            type: 'error',
          };
        }
        if (val === 'demo' && !gameCodeIsDisabled.value) {
          return {
            result: false,
            message: 'demo has been occupied by the system',
            type: 'error',
          };
        }
        return true;
      },
    },
    {
      pattern: CODE_PATTERN,
      message:
        'Game code can only contain numbers, letters, and underscores. It cannot start or end with an underscore.',
    },
    {
      trigger: 'blur',
      validator: async (val) => {
        if (gameCodeIsDisabled.value) {
          return true;
        }
        const { status } = await validateGameCode({ game_code: val });
        const flag = status === ValidateGameCodeStatus.VALID;
        return {
          result: flag,
          message: flag ? '' : 'Game Code already exists',
          type: flag ? 'success' : 'error',
        };
      },
    },
  ],
};
const footerBtnVisible = computed<boolean>(() => props.type === 'Update');

const emit = defineEmits(['update:type']);

const onUndoBtnClick = () => {
  if (isChanged.value) {
    const confirmDia = DialogPlugin.confirm({
      header: 'Tips',
      body: 'The entered information will not be saved. Are you sure you want to leave?',
      confirmBtn: 'Confirm',
      cancelBtn: 'Cancel',
      onConfirm: () => {
        props.type === 'Update' && emit('update:type', 'Default');
        resetRawFormData();
        confirmDia.hide();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
  } else {
    props.type === 'Update' && emit('update:type', 'Default');
    resetRawFormData();
  }
};

const onSaveBtnClick = () => {
  formRef.value?.submit();
};
const onSubmit = async (context: SubmitContext<FormData>) => {
  const { validateResult } = context;
  if (validateResult === true) {
    if (props.type === 'New') {
      createGame();
    } else {
      modifyGame();
    }
  }
};

const createGame = async () => {
  try {
    changeCreateBtnLoading?.(true);
    const res = await createGameService({
      game_code: formData.gameCode,
      game_name: formData.gameName,
      ios_appid: formData.iosID,
      android_appid: formData.androidID,
      icon: formData.image[0]?.response?.url ?? undefined,
      studio_id: studio.curActiveStudio!.studio_id,
      company_id: businessStore.companyId,
      type: formData.type,
    });
    success('Created successfully');
    game.changeCurrentGame(res as TGame);
    emit('update:type', 'Default');
    game.initDrawer();
    studio.updateStudioList();
  } catch (e) {
    err((e as any)?.message || 'Created failed');
  } finally {
    changeCreateBtnLoading?.(false);
  }
};

const modifyGame = async () => {
  try {
    if (game.curGame) {
      saveBtnIsLoading.value = true;
      const gameList = await modifyGameService({
        game_id: game.curGame.game_id!,
        game_code: formData.gameCode,
        game_name: formData.gameName,
        ios_appid: formData.iosID,
        type: formData.type,
        android_appid: formData.androidID,
        icon: formData.image[0]?.url ?? formData.image[0]?.response?.url,
      });
      success('Modified successfully');
      studio.updateStudioList();
      game.changeCurrentGame({
        ...game.curGame,
        ...gameList[0],
      });
      emit('update:type', 'Default');
    }
  } catch (e) {
    err((e as any)?.message || 'Modified failed');
    return;
  } finally {
    saveBtnIsLoading.value = false;
  }
};
const resetRawFormData = () => {
  for (const [key, val] of Object.entries(props.formData)) {
    formData[key as keyof IGameFormData] = val;
  }
};

gameFormEventBus.on(() => {
  onSaveBtnClick();
});

defineExpose({
  isChanged,
});
</script>
<style lang="scss" scoped>
:deep(.t-form__label) {
  @apply pr-[0];
}
</style>
