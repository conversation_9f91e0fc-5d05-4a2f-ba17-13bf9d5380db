<template>
  <div class="flex flex-row pt-[20px]">
    <div class="game_icon h-[65px] w-[65px] mr-[20px]">
      <GameIcon
        :icon="props?.game?.icon??gameIconDefaultSVG"
      />
    </div>
    <div class="game_info grid grid-cols-3 gap-x-[50px]">
      <div class="game_info_item">
        <div class="game_info_item_label">Name:</div>
        <div
          class="game_info_item_text"
          :title="props.game?.game_name"
        >
          {{ props.game?.game_name }}
        </div>
      </div>
      <div class="game_info_item">
        <div class="game_info_item_label">Game Code:</div>
        <div
          class="game_info_item_text"
          :title="props.game?.game_code"
        >
          {{ props.game?.game_code }}
        </div>
      </div>
      <div class="game_info_item">
        <div class="game_info_item_label">Game Type:</div>
        <div
          class="game_info_item_text"
          :title="props.game?.type"
        >
          {{ props.game?.type||'PC' }}
        </div>
      </div>
      <div class="game_info_item">
        <div class="game_info_item_label">IOS APP ID:</div>
        <div class="game_info_item_text">{{ props.game?.ios_appid || '-' }}</div>
      </div>
      <div class="game_info_item">
        <div class="game_info_item_label">Android APP ID:</div>
        <div class="game_info_item_text">{{ props.game?.android_appid || '-' }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { GetGameMetaReturnDataType } from 'common/service/configuration/business/type/type';
import GameIcon from '@/views/configuration/business/components/GameIcon.vue';
import gameIconDefaultSVG from '@/assets/svg/game-icon_default.svg';

interface IProps {
  game: GetGameMetaReturnDataType | null;
}
const props = withDefaults(defineProps<IProps>(), {
  game: null,
});
</script>
<style lang="scss" scoped>
.game_info_item {
  @apply flex flex-row items-center;
  > .game_info_item_label {
    @apply text-sm font-[600] leading-[20px] text-black-primary text-opacity-40 mr-[8px] ;
  }
  > .game_info_item_text {
    @apply text-sm leading-[20px] text-black-primary text-opacity-90 truncate max-w-[200px];
  }
}
</style>
