<template>
  <t-upload
    ref="uploadRef"
    v-model:files="value"
    theme="image"
    :accept="props.accept"
    :max="1"
    :request-method="requestMethod"
    :before-upload="onBeforeUpload"
    :show-upload-progress="true"
    @change="onChange"
  />
</template>

<script setup lang="ts">
import { PropType, ref } from 'vue';
import { MessagePlugin, RequestMethodResponse, UploadFile as TdUploadFile } from 'tdesign-vue-next';
import { ErrCode, UploadFile } from 'common/components/FileUpload';
import { getIMWH } from 'common/utils/file';
import { UploadError } from '../utils';
import { UploadErrorMessage } from '../../type';
import { v4 as uuidv4 } from 'uuid';
import { upload } from 'common/components/FileUpload/util';
const props = defineProps({
  server: {
    // 请求接口，默认走aix默认域名，
    type: String as PropType<'aix' | 'site'>,
    default: 'aix',
    required: false,
  },
  game: {
    // 游戏game_code
    type: String,
    default: '',
    required: false,
  },
  cosPath: {
    // 上传cos路径，会覆盖game
    type: String,
    default: 'gameicon',
    required: false,
  },
  accept: {
    // 接受上传的文件类型
    type: String,
    default: '',
    required: false,
  },
  sizeLimit: {
    // 文件大小限制，0不限制。单位M
    type: Number,
    default: 0,
    required: false,
  },
  uploadCos: {
    // 是否上传cos
    type: Boolean,
    default: true,
    required: false,
  },
  whLimit: {
    // 宽高限制
    type: Object as PropType<{ width: number; height: number }>,
    default: () => ({ width: 500, height: 500 }),
  },
  modelValue: {
    type: Array as PropType<Array<TdUploadFile>>,
    default: () => [],
  },
});
const value = ref<TdUploadFile[]>(props.modelValue ?? []);
const emit = defineEmits(['fail', 'success', 'update:modelValue']);

const requestMethod = async (file: TdUploadFile): Promise<RequestMethodResponse> => new Promise((resolve) => {
  const newRawFile = file.raw as UploadFile;
  newRawFile.id = uuidv4();
  (async () => {
    try {
      const res = await upload({
        file: newRawFile,
        game_code: props.game,
        path: `${props.cosPath}/${newRawFile.id}`,
        type: props.server,
      });

      if (!res.code) {
        const timer = setTimeout(() => {
          resolve({
            status: 'success',
            response: res,
          });
          clearTimeout(timer);
        }, 2000);
      } else {
        emit('fail', res);
        resolve({
          status: 'fail',
          response: res,
        });
      }
    } finally {
    }
  })();
});

const onBeforeUpload = async (file: TdUploadFile) => {
  try {
    checkImageSize(file);
    file.raw && (await checkImageWH(file.raw));
  } catch (error) {
    const msg = (error as UploadErrorMessage)?.message ?? 'image upload error';
    MessagePlugin.error(msg);

    return false;
  }

  return true;
};

const checkImageSize = (file: TdUploadFile): never | void => {
  const { size } = file;
  if (props.sizeLimit > 0) {
    const limitSize = props.sizeLimit * 1024 * 1024;
    if (size && size > limitSize) {
      throw new UploadError({ code: ErrCode.FileInvalid, message: 'invalid image size' });
    }
  }
};

const checkImageWH = async (file: UploadFile): Promise<void> | never => {
  const { width: widthLimit, height: heightLimit } = props.whLimit;
  if (file) {
    const [imageWidth, imageHeight] = await getIMWH(file);
    console.log(imageWidth, imageHeight);

    if (imageWidth !== imageHeight) {
      throw new UploadError({ code: ErrCode.FileInvalid, message: 'The uploaded image must be square' });
    }
    if (imageWidth > widthLimit || imageHeight > heightLimit) {
      throw new UploadError({
        code: ErrCode.FileInvalid,
        message: `Maximum support for pictures of ${widthLimit}*${heightLimit}px`,
      });
    }
  }
};

const onChange = (value: TdUploadFile[]) => {
  emit('update:modelValue', value);
};
</script>
<style lang="scss" scoped>
:deep(.t-upload__card-container) {
  @apply bg-gray-primary;
}
:deep(.t-upload__card-name) {
  @apply hidden;
}
</style>
