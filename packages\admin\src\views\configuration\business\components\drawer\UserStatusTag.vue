<template>
  <template v-if="isWaitAccept">
    <t-tooltip
      theme="light"
      :show-arrow="false"
      overlay-class-name="w-[200px]"
    >
      <t-tag :theme="stateTagTheme">
        {{ stateTagText }}
      </t-tag>
      <template #content>
        <span class="break-all">{{ inviteUrl }}</span>
        <FileCopyIcon
          class="cursor-pointer m-1 hover:text-link"
          @click="onCopyBtnClick(inviteUrl)"
        />
      </template>
    </t-tooltip>
  </template>
  <template v-else>
    <t-tag :theme="stateTagTheme">
      {{ stateTagText }}
    </t-tag>
  </template>
</template>

<script setup lang="ts">
import { useClipboard } from '@vueuse/core';
import { computed } from 'vue';
import { InviteStatus } from '../const';
import { MessagePlugin } from 'tdesign-vue-next';
import { InvitedUser } from 'common/service/configuration/business/type/type.d';
import  { FileCopyIcon } from 'tdesign-icons-vue-next';
interface IProps {
  userData: InvitedUser;
}
const props = defineProps<IProps>();

const { copy } = useClipboard();

const isWaitAccept = computed(() => props.userData.invite_status === InviteStatus.WAIT_ACCEPT);

const stateTagTheme = computed(() => {
  const map: Record<number, string> = {
    [InviteStatus.INVALIDS]: 'danger',
    [InviteStatus.VALID]: 'success',
    [InviteStatus.WAIT_ACCEPT]: 'default',
  };
  return map[props.userData.invite_status];
});

const stateTagText = computed(() => {
  const map: Record<number, string> = {
    [InviteStatus.INVALIDS]: 'Invalid',
    [InviteStatus.VALID]: 'Valid',
    [InviteStatus.WAIT_ACCEPT]: 'Pending',
  };
  return map[props.userData.invite_status];
});

const inviteUrl = computed(() => `${location.origin}/portal/register?token=${props.userData.invite_token}&email=${props.userData.user_id}`);

const onCopyBtnClick = (url: string) => {
  copy(url);
  MessagePlugin.success('Copy successfully');
};
</script>
<style lang=""></style>
