<template>
  <DataContainer
    class="h-full w-full pt-[20px]"
    :loading="game.tableLoading"
    :page-size="game.pageSize"
    :default-page="game.pageNum"
    :total="game.total"
    @on-page-change="onPageChange"
  >
    <template #attributeSlot>
      <t-button @click="emit('showInviteUserDialog')">
        <template #icon>
          <add-icon />
        </template>
        <template #content>
          <span class="mr-[8px]">Invite</span>
        </template>
      </t-button>
    </template>
    <template #actionSlot>
      <svg-icon
        class="normal-hover"
        name="more"
        @click="showTableSelect"
      />
    </template>
    <Table
      ref="tableRef"
      v-model:displayColumns="displayTableCol"
      row-key="id"
      multiple-sort
      class="h-full"
      max-height="100%"
      :data="game.invitedUsers"
      :columns="tableCol"
      :sort="game.tableSort"
      :filter-row="null"
      :filter-value="game.tableFilterValue"
      @filter-change="game.changeTableFilterValue"
      @sort-change="game.changeTableSort"
    />
  </DataContainer>
  <EditUserDialog
    v-if="editUserDialogVisible"
    v-model:visible="editUserDialogVisible"
    :data="curUser"
  />
</template>

<script setup lang="tsx">
import DataContainer from 'common/components/Layout/DataContainer.vue';
import { ref } from 'vue';
import { AddIcon } from 'tdesign-icons-vue-next';
import useBusinessStore from '@/store/configuration/business/business.store';
import { PageInfo } from 'tdesign-vue-next';
import EditUserDialog from '../dialog/EditUser.vue';
import { useTable } from '../../compose/useTable';
import SvgIcon from 'common/components/SvgIcon/SvgIcon.vue';
import Table from 'common/components/table';

const emit = defineEmits(['showInviteUserDialog']);

// Hooks
const businessStore = useBusinessStore();

const { game } = businessStore;
const { curUser, editUserDialogVisible, tableCol, displayTableCol } = useTable();

const tableRef = ref<InstanceType<typeof Table> | null>(null);

const showTableSelect = () => {
  tableRef.value?.showMetricsSelect();
};

const onPageChange = async (_: number, pageinfo: PageInfo) => {
  const { current, pageSize } = pageinfo;
  game.changePage(current, pageSize);
};
</script>
