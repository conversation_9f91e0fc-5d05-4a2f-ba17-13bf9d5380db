<template>
  <t-drawer
    attach="body"
    size="70%"
    class="game_drawer flex"
    :close-on-esc-keydown="false"
    :close-btn="true"
    :visible="game.drawerVisible"
    :on-close="onClose"
    :size-draggable="true"
    :destroy-on-close="true"
  >
    <template #header>
      <div class="font-[600] text-base">{{ title }}</div>
    </template>
    <template #body>
      <div
        v-auto-animate
        class="flex h-full w-full flex-col bg-transparent overflow-hidden"
      >
        <template v-if="game.drawerLoading">
          <FullLoading class="relative" />
        </template>
        <template v-else>
          <GameCard
            ref="gameCardRef"
            :game="(game.curGame as GetGameMetaReturnDataType)"
            :type="gameCardType"
          />
          <div class="flex flex-1 mt-[20px] rounded-default bg-white-primary overflow-y-auto">
            <template v-if="hasUser && isNew">
              <DataEmpty
                :image-size="dataEmptyImageSize"
                class="w-full h-full"
              >
                <template #content>
                  <div class="text-black-primary text-opacity-[0.6]">
                    Unable to invite members at this time. Please create a game project first.
                  </div>
                </template>
              </DataEmpty>
            </template>
            <template v-else>
              <UserTableCard @show-invite-user-dialog="showInviteUserDialog" />
            </template>
          </div>
        </template>
      </div>
    </template>
    <template #footer>
      <div class="w-full flex justify-between">
        <div>
          <template v-if="deleteBtnVisible && (businessStore.isCompanyAdmin||businessStore.isGameAdmin)">
            <t-button
              variant="outline"
              theme="danger"
              ghost
              @click="onDeleteGameBtnClick"
            >
              Delete
            </t-button>
          </template>
        </div>
        <div>
          <t-button
            theme="default"
            @click="onClose"
          >
            {{ cancelText }}
          </t-button>
          <template v-if="visibleDependenceByType('New')">
            <t-button
              theme="primary"
              type="submit"
              :loading="createBtnLoading"
              @click="handleFormSubmit"
            >
              Create
            </t-button>
          </template>
        </div>
      </div>
    </template>
  </t-drawer>
  <InviteUser
    v-if="inviteUserDialogVisible"
    v-model:visible="inviteUserDialogVisible"
  />
</template>

<script setup lang="ts">
import GameCard from './GameCard.vue';
import UserTableCard from './UserTableCard.vue';
import useBusinessStore from '@/store/configuration/business/business.store';
import { computed, provide, ref } from 'vue';
import InviteUser from '../dialog/InviteUser.vue';
import { useVisible } from 'common/compose/useVisible';
import { useEventBus } from '@vueuse/core';
import { TGameCardType } from '../../type';
import FullLoading from 'common/components/FullLoading/loading.vue';
import DataEmpty from '@/components/nullable/DataEmpty.vue';
import { DialogPlugin } from 'tdesign-vue-next';
import type { GetGameMetaReturnDataType } from 'common/service/configuration/business/type/type';

const { visible: inviteUserDialogVisible, show: showInviteUserDialog } = useVisible();
const gameFormEventBus = useEventBus<string>('GameForm');
const businessStore = useBusinessStore();
const { game } = businessStore;

const isNew = computed(() => !game.curGame);
const dataEmptyImageSize = {
  width: '30%',
};
const createBtnLoading = ref<boolean>(false);
const gameCardRef = ref<InstanceType<typeof GameCard> | null>(null);

const gameCardType = computed<TGameCardType>(() => (isNew.value ? 'New' : 'Default'));
const hasUser = computed(() => !game.invitedUsers.length);
const deleteBtnVisible = computed(() => !isNew.value);
const title = computed(() => (isNew.value ? 'New Game' : 'Game Management'));
const visibleDependenceByType = computed(() => (type: TGameCardType) => gameCardType.value === type);
const cancelText = computed(() => (gameCardType.value === 'New' ? 'Cancel' : 'Close'));

const handleFormSubmit = () => {
  gameFormEventBus.emit();
};

const onDeleteGameBtnClick = async () => {
  game.showDelGameDialog();
};

const onClose = () => {
  const isChanged = gameCardRef.value?.isChanged ?? false;
  if (isChanged) {
    const confirmDia = DialogPlugin.confirm({
      header: 'Tips',
      body: 'The entered information will not be saved. Are you sure you want to leave?',
      confirmBtn: 'Confirm',
      cancelBtn: 'Cancel',
      onConfirm: () => {
        confirmDia.hide();
        game.hideDrawer();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
    return;
  }
  game.hideDrawer();
};

const changeCreateBtnLoading = (isLoading: boolean) => {
  createBtnLoading.value = isLoading;
};


provide('changeCreateBtnLoading', changeCreateBtnLoading);
</script>
<style lang="scss" scoped>
:global(.game_drawer .t-drawer__content-wrapper) {
  @apply bg-background;
}
:global(.game_drawer .t-drawer__header) {
  @apply border-none;
}
:global(.game_drawer .t-drawer__body) {
  @apply pt-[0px];
}
:global(.game_drawer .t-drawer__close-btn) {
  @apply bg-transparent text-opacity-60 text-black-primary;
}

:deep(.t-layout) {
  @apply bg-transparent;
}
:deep(.t-layout__footer) {
  @apply rounded-b-default;
}
</style>
