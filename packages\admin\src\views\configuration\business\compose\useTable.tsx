import useBusinessStore from '@/store/configuration/business/business.store';
import { useStorage } from '@vueuse/core';
import Text from 'common/components/Text';
import { useTips } from 'common/compose/tips';
import { useVisible } from 'common/compose/useVisible';
import { hardDeleteUserForGame } from 'common/service/configuration/business/game';
import { InvitedUser } from 'common/service/configuration/business/type/type';
import { AutoComplete, Button, DialogPlugin, PrimaryTableCol, TdDropdownItemProps } from 'tdesign-vue-next';
import { computed, reactive, ref } from 'vue';
import { InviteStatus } from '../components/const';
import UserStatusTag from '../components/drawer/UserStatusTag.vue';
import TimeButton from '../components/button/TimeButton.vue';
import ActionDropdown from '../components/drawer/ActionDropdown.vue';

export function useTable() {
  const { game } = useBusinessStore();
  const { visible: editUserDialogVisible, show: showEditUserDialog } = useVisible();
  const { success, err } = useTips();
  const curUser = ref<InvitedUser | null>(null);
  const deleteConfirmBtn = reactive({
    content: 'Confirm',
    loading: false,
  });
  const statusOptions = [
    { label: 'All', checkAll: true },
    {
      label: 'Valid',
      value: InviteStatus.VALID,
    },
    {
      label: 'Pending',
      value: InviteStatus.WAIT_ACCEPT,
    },
  ];

  const tableCol = ref<PrimaryTableCol[]>([
    {
      colKey: 'user_id',
      title: 'Email',
      ellipsis: {
        theme: 'light',
        placement: 'top',
        showArrow: false,
      },
      filter: {
        type: 'input',
        resetValue: '',
        confirmEvents: ['onEnter'],
        showConfirmAndReset: true,
        component: AutoComplete,
      },
    },
    {
      colKey: 'role_id',
      title: 'Role',
      width: 150,
      ellipsis: {
        theme: 'light',
        placement: 'top',
        showArrow: false,
      },
      filter: {
        type: 'multiple',
        resetValue: [],
        list: [{ label: 'All', checkAll: true }, ...game.roleOptions],
        showConfirmAndReset: true,
      },
      cell: (_, { row: { role_id } }) => <Text content={roleName(role_id)} />,
    },
    {
      colKey: 'creator',
      title: 'Inviter',
      ellipsis: {
        theme: 'light',
        placement: 'top',
        showArrow: false,
      },
      filter: {
        type: 'input',
        resetValue: '',
        confirmEvents: ['onEnter'],
        showConfirmAndReset: true,
        component: AutoComplete,
      },
    },
    {
      colKey: 'invite_status',
      width: 100,
      title: 'State',
      filter: {
        type: 'multiple',
        resetValue: [],
        list: statusOptions,
        showConfirmAndReset: true,
      },
      cell: (_, { row }) => <UserStatusTag userData={row as InvitedUser}></UserStatusTag>,
    },
    {
      colKey: 'created_at',
      title: 'Invited Time',
      width: 150,
      ellipsis: {
        theme: 'light',
        placement: 'top',
        showArrow: false,
      },
      sorter: true,
      cell: (_, { row }) => <Text content={convertUTCToLocalDate(row.created_at)} />,
    },
    {
      colKey: 'updated_at',
      title: 'Updated Time',
      width: 150,
      ellipsis: {
        theme: 'light',
        placement: 'top',
        showArrow: false,
      },
      sorter: true,
      cell: (_, { row }) => <Text content={convertUTCToLocalDate(row.updated_at)} />,
    },
    {
      colKey: 'action',
      title: 'Action',
      width: 100,
      cell: (_, { row }) => {
        const disabled = editOrDelBtnIsDisabled(row as InvitedUser);
        const isWaitAccept = (row as InvitedUser).invite_status === InviteStatus.WAIT_ACCEPT;
        const options: TdDropdownItemProps[] = [
          {
            content: () => (
                <Button
                  variant="text"
                  content="Manage"
                />
            ),
            value: 'Manage',
            disabled,
            onClick: () => onManageBtnClick(row as InvitedUser),
          },
          {
            content: () => (
              <Button
                variant="text"
                content="Delete"
              />
            ),
            value: 'Delete',
            disabled,
            onClick: () => onDeleteBtnClick(row as InvitedUser),
          },
        ];
        if (isWaitAccept) {
          options.push({
            content: () => (
              <TimeButton
                duration={60}
                data={row as InvitedUser}
                content="Resend email"
              />
            ),
            value: 'Resent Email',
          });
        }
        return <ActionDropdown options={options}></ActionDropdown>;
      },
    },
  ]);

  const checkUserIsAdminRole = (roleId: number) => roleId === game.adminRole?.role_id;
  const adminUserCanEdit = computed(() => !((game.adminRole?.counts ?? 0) > 1));
  const roleName = (roleId: number) => {
    const role = game.roles.find((item: { role_id: number }) => item.role_id === roleId);
    return role?.role_name ?? '';
  };
  const convertUTCToLocalDate = (UTCDateStr: string) => new Date(UTCDateStr).toLocaleDateString();
  const editOrDelBtnIsDisabled = (row: InvitedUser) => adminUserCanEdit.value && checkUserIsAdminRole(row.role_id);
  const onManageBtnClick = (row: InvitedUser) => {
    curUser.value = row;
    showEditUserDialog();
  };

  const onDeleteBtnClick = (row: InvitedUser) => {
    const deleteDialogInstance = DialogPlugin.confirm({
      header: 'Tips',
      body: 'Are you sure you want to delete this role? Once deleted, the member will no longer have access to this project.',
      confirmBtn: deleteConfirmBtn,
      cancelBtn: 'Cancel',
      onConfirm: () => {
        (async () => {
          try {
            deleteConfirmBtn.loading = true;
            await hardDeleteUserForGame({
              game: row.game_id,
              email: row.user_id,
              role_id: row.role_id,
            });
            success('Deleted successfully');
            deleteDialogInstance.hide();
            game.initTable();
          } catch (e) {
            err((e as any)?.message || 'Deleted failed');
          } finally {
            deleteConfirmBtn.loading = false;
          }
        })();
      },
      onClose: () => {
        deleteDialogInstance.hide();
      },
    });
  };

  const displayTableCol = useStorage(
    'aix-business-user-table-cols-key',
    ['user_id', 'role_id', 'creator', 'invite_status', 'created_at', 'updated_at', 'action'],
    localStorage,
  );

  return {
    tableCol,
    editUserDialogVisible,
    curUser,
    displayTableCol,
  };
}
