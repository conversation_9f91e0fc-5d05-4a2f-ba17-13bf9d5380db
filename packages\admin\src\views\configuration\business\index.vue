<template>
  <common-view :hide-right="true" :store="businessStore">
    <template #views>
      <ContentContainer />
    </template>
  </common-view>
</template>

<script setup lang="ts">
import useBusinessStore from '@/store/configuration/business/business.store';
import CommonView from 'common/components/Layout/CommonView.vue';
import ContentContainer from './components/Container.vue';

const businessStore = useBusinessStore();
</script>
<style lang="scss" scoped>
:global(.t-dialog__delete-dialog .t-button--variant-base.t-button--theme-danger) {
  @apply bg-error-secondary;
}
</style>
