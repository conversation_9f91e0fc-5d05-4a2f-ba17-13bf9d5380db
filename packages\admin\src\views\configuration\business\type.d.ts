import { ErrCode } from 'common/components/FileUpload';
import { UploadFile } from 'tdesign-vue-next';

export interface UploadErrorMessage {
  code?: ErrCode,
  message?: string
}


export interface IRoleOption {
  value: string | number,
  label: string
}


export interface IGameFormData {
  gameName: string,
  gameCode: string,
  iosID: string,
  androidID: string,
  type:string
  image: UploadFile[],
}


export interface IEditUserFormData {
  roleId?: number,
}

export type TGameCardType = 'New' | 'Update' | 'Default';

