<template>
  <div class="w-full max-w-[400px] min-w-[400px] bg-background rounded-r-large p-5">
    <t-form
      v-if="selectedRuleIndex >= 0"
      ref="formRef"
      :data="newRulesListFormData"
      :rules="rules"
      :show-error-message="isOSAndSpendType ? false : true"
      @submit="onSubmit"
    >
      <FieldName />
      <FieldType />
      <Selections v-if="isTypeDropdownList || isTypeCreateDropdownList" />
      <DefaultValue v-else />
      <FieldFormat v-if="isTypeDate" />
    </t-form>
    <Text
      v-else
      content="Please select the option on the left first"
      color="var(--aix-text-color-gray-primary)"
      class="flex w-full h-full justify-center items-center"
    />
  </div>
</template>
<script setup lang="ts">
import { useCampaignNamingStore } from '@/store/configuration/campaign_naming/index.store';
import Text from 'common/components/Text';
import { IListItem } from 'common/service/configuration/campaign_naming/type';
import { storeToRefs } from 'pinia';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { reactive, ref } from 'vue';
import { CUSTOMIZATION_DIALOG } from '../const';
import DefaultValue from './formItem/DefaultValue.vue';
import FieldName from './formItem/FieldName.vue';
import FieldType from './formItem/FieldType.vue';
import Selections from './formItem/Selections.vue';
import FieldFormat from './formItem/FieldFormat.vue';

const { updateCampaignNameRuleService, getCampaignNameRulesService } = useCampaignNamingStore();
const {
  newRulesListFormData, selectedRuleIndex, isTypeDropdownList, hasNewVersion,
  isTypeCreateDropdownList, isTypeDate, newSelectionInputError, isOSAndSpendType, isCustomCampaignUpdating,
} = storeToRefs(useCampaignNamingStore());

const formRef = ref<any>(null);

defineExpose({
  formRef,
  validate: () => formRef.value?.validate(),
});

const nameValidator = (val: string) => {
  if (!val) {
    return { result: false, message: 'The field cannot be blank', type: 'error' };
  };
  if (val && !/^[A-Z]/.test(val)) {
    return { result: false, message: 'First letter must be uppercase', type: 'error' };
  }
  if (val && !/^[A-Za-z\s]+$/.test(val)) {
    return { result: false, message: 'Name can only contain letters and spaces', type: 'error' };
  };
  if (val.length > 40) {
    return { result: false, message: 'Can only enter up to 40 characters', type: 'error' };
  }
  const isDuplicate = newRulesListFormData.value.listItem.filter(item => item.name === val).length > 1;
  if (isDuplicate) {
    return { result: false, message: 'The name cannot be the same as other fields', type: 'error' };
  }
  return { result: true, message: '', type: 'success' };
};

const newSelectionInputValidator = (val: string) => {
  if (val && !/^[A-Za-z\s]+$/.test(val)) {
    newSelectionInputError.value = true;
    return { result: false, message: 'Can only contain letters and spaces', type: 'error' };
  };
  const isDuplicate = (newRulesListFormData.value.listItem[selectedRuleIndex.value].data as string[]).includes(val);
  if (isDuplicate) {
    newSelectionInputError.value = true;
    return { result: false, message: 'This selection already exists', type: 'error' };
  }
  if (val.length > 40) {
    newSelectionInputError.value = true;
    return { result: false, message: 'Can only enter up to 40 characters', type: 'error' };
  }
  newSelectionInputError.value = false;
  return { result: true, message: '', type: 'success' };
};

const defaultValueValidator = (val: string) => {
  if (val && !/^[A-Za-z\s]+$/.test(val) && !isTypeDate.value) {
    return { result: false, message: 'Can only contain letters and spaces', type: 'error' };
  };
  return { result: true, message: '', type: 'success' };
};

const rules = {
  name: [{ validator: nameValidator }],
  newSelectionInput: [{ validator: newSelectionInputValidator }],
  default_value: [{ validator: defaultValueValidator }],
};

const saveConfirmBtn = reactive({
  content: 'Confirm',
  loading: false,
});

const refreshConfirmBtn = reactive({
  content: 'Refresh Page',
  loading: false,
});

const onSubmit = async () => {
  // 校验名字
  const errorRule = newRulesListFormData.value.listItem
    .filter((item: IListItem) => !nameValidator(item.name).result);
  // 前选择编辑的规则是否有错误
  const isCurrentError = errorRule.filter(item => item.key
    === newRulesListFormData.value.listItem[selectedRuleIndex.value].key);
  // 如果当前选项没有错误，这里将会定位到有错误的规则
  if (errorRule.length && !isCurrentError.length) {
    const firstErrorField = errorRule[0];
    selectedRuleIndex.value = newRulesListFormData.value.listItem
      .findIndex((item: IListItem) => item.key === firstErrorField.key);
  };

  if (!errorRule.length) {
    const saveConfirmDialogInstance = DialogPlugin.confirm({
      theme: 'warning',
      header: CUSTOMIZATION_DIALOG.SAVE.HEADER,
      body: CUSTOMIZATION_DIALOG.SAVE.BODY,
      confirmBtn: saveConfirmBtn,
      cancelBtn: 'Cancel',
      onConfirm: () => {
        (async () => {
          try {
            saveConfirmBtn.loading = true;
            await updateCampaignNameRuleService();
            saveConfirmDialogInstance.hide();
            if (isCustomCampaignUpdating.value) {
              isUpdatingCustomDialog();
            } else if (hasNewVersion.value) {
              hasNewVersionCustomDialog();
            } else if (!hasNewVersion.value && !isCustomCampaignUpdating.value) {
              await getCampaignNameRulesService();
            }
          } catch (e) {
            MessagePlugin.error((e as any).message ?? 'Failed to save');
          } finally {
            saveConfirmBtn.loading = false;
          }
        })();
      },
      onClose: () => {
        saveConfirmDialogInstance.hide();
      },
    });
  }
};

const isUpdatingCustomDialog = () => {
  const updatingConfirmDialogInstance = DialogPlugin.confirm({
    theme: 'warning',
    header: CUSTOMIZATION_DIALOG.IS_UPDATING.HEADER,
    body: CUSTOMIZATION_DIALOG.IS_UPDATING.BODY,
    confirmBtn: 'Close',
    cancelBtn: 'Cancel',
    onConfirm: () => {
      updatingConfirmDialogInstance.hide();
    },
    onClose: () => {
      updatingConfirmDialogInstance.hide();
    },
  });
};

const hasNewVersionCustomDialog = () => {
  const hasNewVersionDialogInstance = DialogPlugin.confirm({
    theme: 'warning',
    header: CUSTOMIZATION_DIALOG.HAS_NEW_VERSION.HEADER,
    body: CUSTOMIZATION_DIALOG.HAS_NEW_VERSION.BODY,
    confirmBtn: refreshConfirmBtn,
    cancelBtn: 'Cancel',
    onConfirm: () => {
      (async () => {
        try {
          refreshConfirmBtn.loading = true;
          await getCampaignNameRulesService();
          hasNewVersionDialogInstance.hide();
        } catch (e) {
          MessagePlugin.error((e as any).message ?? 'Failed to refresh');
        } finally {
          refreshConfirmBtn.loading = false;
        }
      })();
    },
    onClose: () => {
      hasNewVersionDialogInstance.hide();
    },
  });
};
</script>
<style lang="scss" scoped>
</style>
