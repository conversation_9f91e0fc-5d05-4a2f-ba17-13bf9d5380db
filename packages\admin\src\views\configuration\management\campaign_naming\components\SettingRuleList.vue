<template>
  <div class="w-full flex flex-wrap content-start gap-6 p-5">
    <draggable
      :model-value="[...modelValue, ADD_BUTTON]"
      class="flex flex-row content-start flex-wrap gap-y-[10px] gap-x-7"
      item-key="index"
      handle=".handle"
      drag-class="drag"
      ghost-class="ghost"
      :move="onMove"
      @start="dragStart"
      @end="dragEnd"
      @update:model-value="updateModelValue"
    >
      <template #item="{ element, index }">
        <div
          class="rounded-small draggable-item relative"
          :class="isHyphen ? 'hyphen' : 'underscore'"
        >
          <div class="flex flex-row font-normal text-sm leading-[24px] text-left gap-6">
            <t-input
              v-if="element.type !== COMPONENT_TYPE.BUTTON"
              :key="index"
              :value="element.name"
              readonly
              placeholder="Please enter"
              :input-class="['max-w-[200px] min-w-[200px] cursor-pointer', {
                invisible: isDragging(index),
                'input-active': isRuleSelected(index),
              }]"
              @click="selectRule(index)"
            >
              <template #prefix-icon>
                <div
                  class="w-[16px] h-[16px] flex justify-between items-center"
                  :class="{
                    handle: !isRuleChangeOrderDisabled(element),
                    'cursor-move': !isRuleChangeOrderDisabled(element),
                    'cursor-not-allowed': isRuleChangeOrderDisabled(element),
                  }"
                >
                  <MoveIcon
                    size="14px"
                    color="var(--aix-text-color-black-disabled)"
                  />
                </div>
              </template>
              <template #suffix-icon>
                <div
                  class="w-[16px] h-[16px] flex justify-between items-center"
                  :class="{
                    invisible: isRuleRemoveDisabled(element),
                    // 'cursor-pointer': !isRuleDisabled(element),
                    // 'cursor-not-allowed': isRuleDisabled(element),
                  }"
                  @click="onDeleteClick(index)"
                >
                  <DeleteIcon
                    size="14px"
                    color="var(--aix-text-color-black-placeholder)"
                  />
                </div>
              </template>
            </t-input>
            <t-button
              v-else
              class="min-w-[200px]"
              theme="primary"
              variant="dashed"
              :disabled="isDisabledAddBtn"
              :content="element.name"
              @click="addRuleClick"
            />
          </div>
        </div>
      </template>
    </draggable>
  </div>
</template>
<script setup lang="ts">
import { MODEL_VALUE_SEPARATOR } from 'common/components/CustomNamingRules/const';
import { IListItem } from 'common/service/configuration/campaign_naming/type';
import { DeleteIcon, MoveIcon } from 'tdesign-icons-vue-next';
import { PropType, computed, ref } from 'vue';
import Draggable from 'vuedraggable';
import { ADD_BUTTON, COMPONENT_TYPE, DEFAULT_RULES } from '../const';

const props = defineProps({
  modelValue: {
    type: Array as PropType<IListItem[]>,
    default: () => [],
  },
  previousCustomRule: {
    type: Array as PropType<IListItem[]>,
    default: () => [],
  },
  currentSelectedRuleIndex: {
    type: Number,
    default: undefined,
  },
  isLockCfg: {
    type: Object,
    default: () => {},
  },
  delimiter: {
    type: String,
    default: MODEL_VALUE_SEPARATOR,
  },
});
const emits = defineEmits(['onDelete', 'onAddRule', 'onSelectedRuleChange', 'update:model-value']);

const modelValue = computed(() => props.modelValue);
const dragRef = ref<boolean>(false);
const draggingIndex = ref(null);
const disabledList = computed(() => props.previousCustomRule.map(item => item.key));
const isDisabledAddBtn = computed(() => modelValue.value.length >= 20);
// const isRuleDisabled = (rule: IListItem) => disabledList.value.includes(rule.key);

const isHyphen = computed(() => props.delimiter === '-');

// 是否禁止移除规则
const isRuleRemoveDisabled = (rule: IListItem) => {
  if ((!props.isLockCfg.removeRule && DEFAULT_RULES.includes(rule.name))
    || (props.isLockCfg.removeRule && disabledList.value.includes(rule.key))) {
    return true;
  }
  return false;
};
// 是否禁止更换排序
const isRuleChangeOrderDisabled = (rule: IListItem) => {
  if (props.isLockCfg.changeOrder && disabledList.value.includes(rule.key)) {
    return true;
  }
  return false;
};

const isRuleSelected = (index: number) => props.currentSelectedRuleIndex === index;

const isDragging = (index: number) => dragRef.value && draggingIndex.value === index;

const onMove = (event: any) => {
  // relatedContext: 将停靠的对象
  // 如果二次编辑排序打开，不限制排序
  if (!props.isLockCfg.changeOrder) return;

  // 二次编辑关闭将限制
  const previousRule = props.previousCustomRule.map(item => item.key);
  return !previousRule.includes(event.relatedContext.element.key);
};

const dragStart = (event: any) => {
  dragRef.value = true;
  draggingIndex.value = event.oldIndex;
};
const dragEnd = (event: any) => {
  dragRef.value = false;
  draggingIndex.value = event.newIndex;
  emits('onSelectedRuleChange', event.newIndex);
};

const selectRule = (index: number) => {
  emits('onSelectedRuleChange', index);
};

const onDeleteClick = (index: number) => {
  emits('onDelete', index);
};

const addRuleClick = () => {
  emits('onAddRule');
};

const updateModelValue = (value: IListItem[]) => {
  // 排除model value里的按钮
  const newModelValue = value.filter(item => item.type !== COMPONENT_TYPE.BUTTON);
  emits('update:model-value', newModelValue);
};
</script>
<style lang="scss" scoped>
:deep(.draggable-item.hyphen) {
  &:not(:last-child)::after {
    content: '-';
    position: absolute;
    top: calc(0% + 5px);
    left: calc(100% + 10px);
  }
}

:deep(.draggable-item.underscore) {
  &:not(:last-child)::after {
    content: '_';
    position: absolute;
    top: calc(0% + 10px);
    left: calc(100% + 10px);
  }
}

:deep(.input-active) {
  background-color: var(--aix-bg-color-brand);
  svg,
  .t-input__inner,
  .t-input__inner::placeholder {
    color: var(--aix-text-color-white-primary);
  }
}

:deep(.hyphen.drag) {
    &:not(:last-child)::after{
    content: none;
  }
}

:deep(.ghost) {
  border: 1px dashed;
  border-radius: 0.5rem;
  border-color: inherit;
}
</style>
