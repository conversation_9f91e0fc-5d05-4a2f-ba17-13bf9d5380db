<template>
  <t-form-item
    :label="isTypeDate ? 'Selections' : 'Default value'"
    :name="`listItem[${selectedRuleIndex}].default_value`"
    label-align="left"
  >
    <t-input
      v-if="isTypeText"
      v-model="newRulesListFormData.listItem[selectedRuleIndex].default_value"
      :maxcharacter="40"
      @blur="setDefaultValue"
    />
    <t-date-picker
      v-if="isTypeDate"
      :value="newRulesListFormData.listItem[selectedRuleIndex].default_value"
      format="YYYY-MM-DD"
      value-type="YYYYMMDD"
      :disable-date="{before: dayjs(new Date(new Date().getTime() - 24 * 3600 * 1000))}"
      @change="setDefaultValue"
    />
  </t-form-item>
</template>
<script setup lang="ts">
import { useCampaignNamingStore } from '@/store/configuration/campaign_naming/index.store';
import dayjs from 'dayjs';
import { storeToRefs } from 'pinia';

const { setDefaultValue } = useCampaignNamingStore();
const { isTypeText, isTypeDate, selectedRuleIndex, newRulesListFormData } = storeToRefs(useCampaignNamingStore());
</script>
<style lang="scss" scoped>
:deep(.t-date-picker) {
    width: 100%
}
</style>
