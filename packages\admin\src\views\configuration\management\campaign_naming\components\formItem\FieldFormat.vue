<template>
  <t-form-item
    label="Format"
    :name="`listItem[${selectedRuleIndex}].format`"
    label-align="left"
  >
    <t-radio-group
      :value="(newRulesListFormData.listItem[selectedRuleIndex].data as any).format"
      @change="setFormat"
    >
      <t-radio
        v-for="item in FORMAT_OPTIONS"
        :key="item.value"
        :value="item.value"
      >
        {{ item.label }}
      </t-radio>
    </t-radio-group>
  </t-form-item>
</template>
<script setup lang="ts">
import { useCampaignNamingStore } from '@/store/configuration/campaign_naming/index.store';
import { storeToRefs } from 'pinia';
import { FORMAT_OPTIONS } from '../../const';

const { setFormat } = useCampaignNamingStore();
const { selectedRuleIndex, newRulesListFormData } = storeToRefs(useCampaignNamingStore());

</script>
