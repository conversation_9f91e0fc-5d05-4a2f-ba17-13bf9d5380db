<template>
  <t-form-item
    label="Field Name"
    :name="`listItem[${selectedRuleIndex}].name`"
    label-align="left"
  >
    <t-input
      v-if="!isOSAndSpendType"
      v-model="newRulesListFormData.listItem[selectedRuleIndex].name"
      :maxcharacter="40"
      @blur="setName"
    />
    <Text
      v-else
      class="px-[8px]"
      :content="newRulesListFormData.listItem[selectedRuleIndex].name"
    />
  </t-form-item>
</template>
<script setup lang="ts">
import { useCampaignNamingStore } from '@/store/configuration/campaign_naming/index.store';
import Text from 'common/components/Text';
import { storeToRefs } from 'pinia';

const { setName } = useCampaignNamingStore();
const { isOSAndSpendType, newRulesListFormData, selectedRuleIndex } = storeToRefs(useCampaignNamingStore());
</script>
