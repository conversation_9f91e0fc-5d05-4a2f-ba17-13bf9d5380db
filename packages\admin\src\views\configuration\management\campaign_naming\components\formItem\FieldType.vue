<template>
  <t-form-item
    label="Field Type"
    :name="`listItem[${selectedRuleIndex}].type`"
    label-align="left"
  >
    <t-radio-group
      v-if="!isOSAndSpendType && !isEditTypeDisabled"
      :value="getRadioRuleType(newRulesListFormData.listItem[selectedRuleIndex].type)"
      class="bg-white-primary"
      @change="setType"
    >
      <t-radio-button
        v-for="item in TYPE_OPTIONS"
        :key="item.value"
        :value="item.value"
      >
        {{ item.label }}
      </t-radio-button>
    </t-radio-group>
    <Text
      v-else
      class="px-[8px]"
      :content="getRuleType(newRulesListFormData.listItem[selectedRuleIndex].type)"
    />
  </t-form-item>
</template>
<script setup lang="ts">
import { useCampaignNamingStore } from '@/store/configuration/campaign_naming/index.store';
import Text from 'common/components/Text';
import { storeToRefs } from 'pinia';
import { COMPONENT_TYPE, TYPE_OPTIONS } from '../../const';
import { computed } from 'vue';

const { setType } = useCampaignNamingStore();
const { isOSAndSpendType, selectedRuleIndex, newRulesListFormData, isLockCfg } = storeToRefs(useCampaignNamingStore());

const getRuleType = (value: string) => {
  if (value === COMPONENT_TYPE.CUSTOM_DROPDOWN_LIST) {
    return TYPE_OPTIONS.find(item => item.value === COMPONENT_TYPE.DROPDOWN_LIST)?.label;
  };
  return TYPE_OPTIONS.find(item => item.value === value)?.label;
};

const getRadioRuleType = (value: string) => {
  if (value === COMPONENT_TYPE.CUSTOM_DROPDOWN_LIST) {
    return COMPONENT_TYPE.DROPDOWN_LIST;
  }
  return value;
};

const isEditTypeDisabled = computed(() => isLockCfg.value.editType);
</script>
