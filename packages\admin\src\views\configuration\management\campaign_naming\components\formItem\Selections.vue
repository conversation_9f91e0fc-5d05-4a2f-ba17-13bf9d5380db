<template>
  <t-form-item
    label="Selections"
    :name="`listItem[${selectedRuleIndex}].data`"
    label-align="left"
  >
    <div class="flex flex-col w-full">
      <t-list
        class="border w-full rounded-default pb-5"
        layout="vertical"
      >
        <t-list-item class="p-0 overflow-y-auto list-item">
          <!-- eslint-disable-next-line vue/valid-v-model -->
          <draggable
            v-if="!isEmpty(newRulesListFormData.listItem[selectedRuleIndex]?.data)
              && Array.isArray(newRulesListFormData.listItem[selectedRuleIndex]?.data)"
            v-model="selectedOptions"
            class="w-full h-[200px]"
            item-key="index"
            handle=".handle"
            @start="drag = true"
            @end="drag = false"
          >
            <template #item="{ element, index }">
              <div class="inline-flex items-center w-full border-b p-2">
                <MoveIcon class="handle cursor-move" size="16px" />
                <Text
                  :key="index"
                  :content="element"
                  overflow
                  tool-tip
                  tips-placement="left"
                  class="w-full px-3"
                />
                <MinusCircleIcon
                  class="cursor-pointer"
                  :class="{
                    hidden: isDefaultSelection(element),
                  }"
                  size="16px"
                  @click="removeDropdown(index)"
                />
              </div>
            </template>
          </draggable>
          <Text
            v-else
            content="Please add new selections."
            color="var(--aix-text-color-gray-primary)"
            class="flex w-full justify-center items-center h-[200px]"
          />
        </t-list-item>
        <t-list-item class="p-0 w-full">
          <t-form-item
            name="newSelectionInput"
            label-width="0"
          >
            <div
              class="rounded-b-large w-full px-3 pt-3 border-solid border-t-[1px] flex justify-end z-10"
            >
              <t-input
                v-model="newRulesListFormData.newSelectionInput"
                @blur="onBlur"
              />
              <t-button
                theme="primary" variant="text" content="+ Add"
                :disabled="!newRulesListFormData.newSelectionInput"
                @click="addDropdown"
              />
            </div>
          </t-form-item>
        </t-list-item>
      </t-list>
      <t-checkbox
        class="pt-2"
        :checked="isTypeCreateDropdownList"
        @change="handleCheckBox"
      >
        Allow input in Preview
      </t-checkbox>
    </div>
  </t-form-item>
</template>
<script setup lang="ts">
import { useCampaignNamingStore } from '@/store/configuration/campaign_naming/index.store';
import Text from 'common/components/Text';
import { isEmpty } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { MinusCircleIcon, MoveIcon } from 'tdesign-icons-vue-next';
import { ref, computed } from 'vue';
import Draggable from 'vuedraggable';
import { COMPONENT_TYPE, DEFAULT_SELECTIONS } from '../../const';

const { setNewDropdown, addDropdown, removeDropdown, setAllowInputInPreview } = useCampaignNamingStore();
const { isTypeCreateDropdownList, newRulesListFormData, selectedRuleIndex } = storeToRefs(useCampaignNamingStore());

const drag = ref<boolean>(false);
const selectedOptions = computed(() => {
  const options = newRulesListFormData.value.listItem[selectedRuleIndex.value].data as string[];
  return options;
});

const isDefaultSelection = computed(() => {
  const currentRuleName = newRulesListFormData.value.listItem[selectedRuleIndex.value].name;
  return (value: string) => {
    if (currentRuleName === 'OS' && DEFAULT_SELECTIONS.OS.includes(value)) {
      return true;
    }
    if (currentRuleName === 'Spend Type' && DEFAULT_SELECTIONS.SPEND_TYPE.includes(value)) {
      return true;
    }
    return false;
  };
});

const handleCheckBox = (value: any) => {
  if (value) {
    setAllowInputInPreview(COMPONENT_TYPE.CUSTOM_DROPDOWN_LIST);
  } else {
    setAllowInputInPreview(COMPONENT_TYPE.DROPDOWN_LIST);
  }
};

const onBlur = (value: string) => {
  setNewDropdown(value);
};
</script>
<style lang="scss" scoped>
:deep(.t-form__item.t-form-item__newSelectionInput) {
  width: 100%;
}
:deep(.t-input__extra) {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
</style>
