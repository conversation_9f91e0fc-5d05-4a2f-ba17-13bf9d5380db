export const TITLE_TOOLTIP = `You can customize your campaign naming rules here and use them as criteria when creating campaigns. 
You can add and edit fields at any time and adjust the field order by dragging and dropping. 
The fields you create will be used as custom dimensions in Pivot named Campaigns (Details), 
enabling you to filter and drill-down for analysis.`;

export const TYPE_OPTIONS = [
  {
    label: 'Dropdown list',
    value: 'dropdown_list',
  },
  {
    label: 'Text',
    value: 'text',
  },
  {
    label: 'Date',
    value: 'date',
  },
];

export const FORMAT_OPTIONS = [
  {
    label: 'YYYYMMDD',
    value: 'YYYYMMDD',
  },
  {
    label: 'YYMMDD',
    value: 'YYMMDD',
  },
];

export const DEFAULT_TYPE = 'dropdown_list';

export const CUSTOMIZATION_DIALOG = {
  SAVE: {
    HEADER: 'Are you sure to Save Customization?',
    BODY: 'This action will save your current customization settings. Do you want to proceed?',
  },
  DELETE: {
    HEADER: 'Are you sure to delete field?',
    BODY: 'Once deleted, its historical data will be cleared.',
  },
  LEAVE: {
    HEADER: 'Are you sure to leave?',
    BODY: 'Not saved yet, do you want to leave?',
  },
  IS_UPDATING: {
    HEADER: 'Update Not Available! Another user is currently updating customization settings.',
    BODY: 'Please try again later as someone else is modifying the customization for this game.',
  },
  HAS_NEW_VERSION: {
    HEADER: 'Update Not Available!',
    BODY: 'Another user has updated to a new version. Please refresh the page to continue editing.',
  },
};

export const DEFAULT_SELECTIONS = {
  OS: ['and', 'ios', 'pc', 'steam', 'all'],
  SPEND_TYPE: ['newinstall', 'reattribution', 'preregister', 'others'],
};

export const COMPONENT_TYPE = {
  CUSTOM_DROPDOWN_LIST: 'custom_dropdown_list',
  DROPDOWN_LIST: 'dropdown_list',
  TEXT: 'text',
  DATE: 'date',
  BUTTON: 'button',
} as const;

export const ADD_BUTTON = {
  name: '+ Add',
  type: 'button',
};

export const DEFAULT_RULES = ['OS', 'Spend Type'];

export const DELIMITER_OPTIONS = [
  {
    value: '-',
    label: 'hyphen ( - )',
  },
  {
    value: '_',
    label: 'underscore ( _ )',
  },
];
