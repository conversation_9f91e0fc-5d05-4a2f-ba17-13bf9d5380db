<template>
  <common-view
    class="h-screen"
    :hide-right="true"
    :store="store"
  >
    <template #title>
      <div class="flex items-center">
        <h1 class="text-2xl text-black-placeholder leading-[32px] font-[600] truncate">
          {{ 'Setting /' }}
        </h1>
        <span>&nbsp;</span>
        <h1 class="text-2xl leading-[32px] font-[600] truncate">
          {{ 'Campaign Naming' }}
        </h1>
        <t-tooltip :content="TITLE_TOOLTIP" class="mx-2 text-black-placeholder">
          <HelpCircleIcon size="17px" />
        </t-tooltip>
      </div>
    </template>
    <template #views>
      <div class="h-auto w-full">
        <div class="w-full flex flex-col bg-white-primary rounded-t-large p-[24px] gap-8">
          <div class="w-full flex flex-col">
            <Text content="Setting" type="subTitle" />
            <div class="w-full flex flex-row justify-between border border-solid rounded-large mt-3 h-[500px]">
              <div class="w-full flex flex-col overflow-y-auto">
                <div class="flex flex-row content-start flex-wrap gap-x-7 px-5 pt-5">
                  <Text
                    weight="500"
                    content="Delimiter"
                  />
                  <t-radio-group
                    :model-value="newRulesListFormData.delimiter"
                    :options="DELIMITER_OPTIONS"
                    @update:model-value="(val: string) => setDelimiter(val)"
                  />
                </div>

                <!-- 左区域新增设置 -->
                <SettingRuleList
                  v-loading="isLoading"
                  :model-value="newRulesListFormData.listItem"
                  :previous-custom-rule="previousRulesListItem"
                  :current-selected-rule-index="selectedRuleIndex"
                  :is-lock-cfg="isLockCfg"
                  :delimiter="newRulesListFormData.delimiter"
                  @on-selected-rule-change="onSelectedRuleChange"
                  @on-delete="onDeleteBtnClick"
                  @on-add-rule="addRule"
                  @update:model-value="updateSettingRuleModelValue"
                />
              </div>
              <!-- 右边区域编辑的区域 -->
              <EditRule ref="editRuleRef" />
            </div>
          </div>

          <!-- 预览区域 -->
          <div class="w-full">
            <Text content="Preview" type="subTitle" />
            <CustomNamingRules
              v-model="customNamingRuleModelValue"
              :options="customNamingRuleOptions"
              :model-value-type="MODEL_VALUE_TYPE.OBJECT"
              :model-value-separator="newRulesListFormData.delimiter"
              :loading="isLoading"
              label="Campaign Name"
            />
          </div>
        </div>
        <footer
          class="bg-white-primary rounded-b-large
                w-full py-[16px] px-[32px] border-solid border-t-[1px] flex justify-end gap-[16px]"
        >
          <t-button
            content="Cancel"
            theme="default"
            :disabled="!hasUnsavedChanges"
            @click="onCancelBtnClick"
          />
          <t-button
            content="Save"
            theme="primary"
            :disabled="isSaveBtnDisable"
            @click="onSaveBtnClick"
          />
        </footer>
      </div>
    </template>
  </common-view>
</template>

<script setup lang="ts">
import { useCampaignNamingStore } from '@/store/configuration/campaign_naming/index.store';
import CommonView from 'common/components/Layout/CommonView.vue';
import Text from 'common/components/Text';
import { IListItem } from 'common/service/configuration/campaign_naming/type';
import { cloneDeep, isEqual } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { HelpCircleIcon } from 'tdesign-icons-vue-next';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { v4 as uuidv4 } from 'uuid';
import { onBeforeUnmount, onMounted, ref, watch } from 'vue';
import EditRule from './components/EditRule.vue';
import SettingRuleList from './components/SettingRuleList.vue';
import { CUSTOMIZATION_DIALOG, DEFAULT_TYPE, DELIMITER_OPTIONS, TITLE_TOOLTIP } from './const';

import type { IOption as CustomNamingRulesOption, TFormModelValue } from 'common/components/CustomNamingRules';
import CustomNamingRules, { COMPONENT_TYPE } from 'common/components/CustomNamingRules';
import { MODEL_VALUE_TYPE } from 'common/components/CustomNamingRules/const';
import { TComponentType } from 'common/components/CustomNamingRules/types';

const store = useCampaignNamingStore();
const { previousRulesListItem, newRulesListFormData, selectedRuleIndex, isLoading, isLockCfg,
  previousDelimiter } = storeToRefs(store);

const isSaveBtnDisable = ref<boolean>(true);
const hasUnsavedChanges = ref<boolean>(false);

const editRuleRef = ref();

const onSaveBtnClick = () => {
  editRuleRef.value.formRef?.submit();
};

const onCancelBtnClick = () => {
  const cancelConfirmDialogInstance = DialogPlugin.confirm({
    theme: 'warning',
    header: CUSTOMIZATION_DIALOG.LEAVE.HEADER,
    body: CUSTOMIZATION_DIALOG.LEAVE.BODY,
    confirmBtn: 'Confirm',
    cancelBtn: 'Cancel',
    onConfirm: () => {
      try {
        newRulesListFormData.value.listItem = cloneDeep(previousRulesListItem.value);
        newRulesListFormData.value.delimiter = cloneDeep(previousDelimiter.value);
        selectedRuleIndex.value = undefined;
        cancelConfirmDialogInstance.hide();
      } catch (e) {
        MessagePlugin.error((e as any).message ?? 'Failed to cancel');
      }
    },
    onClose: () => {
      cancelConfirmDialogInstance.hide();
    },
  });
};

const addRule = () => {
  const newEmptyRule: IListItem = {
    key: uuidv4(),
    name: '',
    type: DEFAULT_TYPE,
    data: [],
    default_value: '',
  };
  newRulesListFormData.value.listItem.push(newEmptyRule);
  selectedRuleIndex.value = newRulesListFormData.value.listItem.length - 1;
};

const updateSettingRuleModelValue = (value: IListItem[]) => {
  newRulesListFormData.value.listItem = value;
};

const deleteSettingRule = (index: number) => {
  const lastIndex = newRulesListFormData.value.listItem.length - 1;
  selectedRuleIndex.value = isEqual(index, lastIndex) ? index - 1 : index;
  newRulesListFormData.value.listItem.splice(index, 1);
};

const onSelectedRuleChange = (index: number) => {
  selectedRuleIndex.value = index;
  newRulesListFormData.value.newSelectionInput = '';
};

const onDeleteBtnClick = (delItem: any) => {
  const removeConfirmDialogInstance = DialogPlugin.confirm({
    theme: 'warning',
    header: CUSTOMIZATION_DIALOG.DELETE.HEADER,
    body: CUSTOMIZATION_DIALOG.DELETE.BODY,
    confirmBtn: 'Confirm',
    cancelBtn: 'Cancel',
    onConfirm: () => {
      try {
        deleteSettingRule(delItem);
        MessagePlugin.success('Delete successful');
        removeConfirmDialogInstance.hide();
      } catch (e) {
        MessagePlugin.error((e as any).message ?? 'Failed to delete');
      }
    },
    onClose: () => {
      removeConfirmDialogInstance.hide();
    },
  });
};

const setDelimiter = (val: string) => {
  newRulesListFormData.value.delimiter = val;
  if (!selectedRuleIndex.value) selectedRuleIndex.value = 0;
};

onMounted(() => {
  window.addEventListener('beforeunload', handleBeforeUnload);
});

onBeforeUnmount(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload);
});

const handleBeforeUnload = (event: any) => {
  if (hasUnsavedChanges.value) {
    event.preventDefault();
  }
};

watch(() => [
  newRulesListFormData.value.listItem,
  newRulesListFormData.value.delimiter,
], ([formDataVal, delimiterVal]) => {
  if (!isEqual(previousRulesListItem.value, formDataVal) || !isEqual(previousDelimiter.value, delimiterVal)) {
    isSaveBtnDisable.value = false;
    hasUnsavedChanges.value = true;
  } else {
    isSaveBtnDisable.value = true;
    hasUnsavedChanges.value = false;
  }

  if (newRulesListFormData.value.newSelectionInput) {
    editRuleRef.value.formRef?.validate();
  }
}, { deep: true });

// 生成预览组件的参数
const customNamingRuleOptions = ref<CustomNamingRulesOption[]>([]);
const customNamingRuleModelValue = ref<TFormModelValue>({});
watch(() => newRulesListFormData.value.listItem, (newVal) => {
  customNamingRuleOptions.value = [];
  customNamingRuleModelValue.value = {};
  for (const ruleFrom of newVal) {
    const { data, type, name, default_value: defaultValue } = ruleFrom;
    const newType = type === 'custom_dropdown_list' ? COMPONENT_TYPE.CUSTOM_DROPDOWN_LIST : type;
    const isDropdownType = [COMPONENT_TYPE.DROPDOWN_LIST, COMPONENT_TYPE.CUSTOM_DROPDOWN_LIST].includes(newType as any);
    customNamingRuleOptions.value.push({
      label: name || 'Please enter',
      value: name || uuidv4(),
      data: isDropdownType ? (data as string[] || [])?.map(item => ({ label: item, value: item })) : undefined,
      type: newType as unknown as  TComponentType,
    });
    customNamingRuleModelValue.value[name] = (data as string[])?.[0] ?? defaultValue;
  }
}, { deep: true });
</script>
<style lang="scss" scoped>
:deep(.t-form-inline .t-form__item) {
  margin: auto;
  min-width: auto;
}

:deep(.t-input__help span) {
  text-align: center;
}
</style>
