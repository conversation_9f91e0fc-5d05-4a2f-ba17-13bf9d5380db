<template>
  <t-form
    ref="formRef"
    label-align="left"
    :data="innerFormData"
    :rules="formRules"
    :label-width="130"
  >
    <div class="mb-[5px]">
      <Text type="subTitle" content="Metric Type" />
    </div>
    <!-- 指标类型：点击指标卡切换 -->
    <div class="w-full flex items-center justify-center mb-[20px]" :class="{ 'is-disabled': isEdit }">
      <div
        v-for="(item, index) in METRIC_TYPE_OPTIONS"
        :key="index"
        class="card-item"
        :class="{ active: innerFormData.metric_type === item.value }"
        @click="changeMetricType(item.value)"
      >
        <div class="flex items-center py-[6px]">
          <SvgIcon
            :name="item.icon"
            size="16px"
          />
          <div class="font-bold text-[15px] ml-[6px]">{{ item.label }}</div>
        </div>
        <Text
          class="py-[6px]"
          theme="info"
          size="small"
          :content="item.desc"
        />
      </div>
    </div>
    <div class="mb-[10px]">
      <Text type="subTitle" content="Basis Information" />
    </div>
    <!-- 指标名称 -->
    <t-form-item label="Metric Name" name="metric_name">
      <t-input-adornment :prepend="isCohortType ? 'D(X)' : ''">
        <t-input v-model="innerFormData.metric_name" class="w-[240px]" :maxlength="50" />
      </t-input-adornment>
    </t-form-item>
    <!-- 指标描述 -->
    <t-form-item label="Description" name="description">
      <t-textarea
        v-model="innerFormData.description"
        placeholder="Please enter"
        clearable
        :maxlength="3000"
        :autosize="{ minRows: 5, maxRows: 5 }"
      />
    </t-form-item>
    <div class="mb-[10px]">
      <Text type="subTitle" content="Calculation" />
    </div>
    <!-- 下拉框：事件 -->
    <t-form-item label="Event" name="event_name">
      <t-select v-model="innerFormData.event_name" class="w-[235px]" :disabled="isEdit">
        <t-option
          v-for="(item, index) in eventList"
          :key="index"
          :value="item.value"
          :label="item.label"
        >
          {{ item.label }}
        </t-option>
      </t-select>
    </t-form-item>
    <!-- 单选框：值类型 -->
    <t-form-item label="Units" name="value_type">
      <t-radio-group v-model="innerFormData.value_type" :disabled="isEdit">
        <t-radio v-for="(item, index) in VALUE_TYPE_OPTIONS" :key="index" :value="item.value">
          {{ item.label }}
        </t-radio>
      </t-radio-group>
    </t-form-item>
    <!-- 单选框：是否累计 -->
    <t-form-item v-if="isCohortType" label="Cumulative" name="is_cumulative">
      <t-radio-group v-model="innerFormData.is_cumulative" :disabled="isEdit">
        <t-radio v-for="(item, index) in CUMULATIVE_OPTIONS" :key="index" :value="item.value">
          {{ item.label }}
        </t-radio>
      </t-radio-group>
    </t-form-item>
    <!-- 单选框：绝对值或者比例 -->
    <t-form-item v-if="isCohortType" label="Format" name="format">
      <t-radio-group v-model="innerFormData.format" :disabled="isEdit">
        <t-radio v-for="(item, index) in FORMAT_OPTIONS" :key="index" :value="item.value">
          {{ item.label }}
        </t-radio>
      </t-radio-group>
    </t-form-item>
  </t-form>
</template>
<script setup lang="ts">
import { ref, PropType, computed } from 'vue';
import { IMetric, METRIC_TYPE, IMetricForm, IOption, VALUE_TYPE, METRIC_FORMAT } from '@/store/configuration/management/type.d';
import { METRIC_TYPE_OPTIONS, VALUE_TYPE_OPTIONS, CUMULATIVE_OPTIONS, FORMAT_OPTIONS } from './const';
import Text from 'common/components/Text';
import SvgIcon from 'common/components/SvgIcon';
import { cloneDeep } from 'lodash-es';

const props = defineProps({
  data: {
    type: Object as PropType<IMetric | null>,
    default: () => null,
  },
  eventList: {
    type: Array as PropType<IOption[]>,
    default: () => [],
  },
  metricType: {
    type: String as PropType<METRIC_TYPE>,
    default: METRIC_TYPE.COHORT,
  },
});

const formRules = {
  metric_name: [
    { required: true, message: 'Please enter metric name', trigger: 'blur' },
    // 不允许输入单引号
    { pattern: /^[^']*$/, message: 'Please enter a valid metric name', trigger: 'blur' },
    // 如果是字母开头，则必须是大写字母
    { pattern: /^(?:[A-Z][\s\S]*|[^\p{L}][\s\S]*)$/u, message: 'If the input starts with a letter, it must be uppercase', trigger: 'blur' },
  ],
  description: [
    // 不允许输入单引号
    { pattern: /^[^']*$/, message: 'Please enter a valid description', trigger: 'blur' },
  ],
  event_name: [
    { required: true, message: 'Please select event', trigger: 'change' },
  ],
  value_type: [
    { required: true, message: 'Please select value type', trigger: 'change' },
  ],
  is_cumulative: [
    { required: true, message: 'Please select cumulative', trigger: 'change' },
  ],
  format: [
    { required: true, message: 'Please select format', trigger: 'change' },
  ],
};

// 表单默认值
const defaultFormData: Record<string, IMetricForm> = {
  [METRIC_TYPE.COHORT]: {
    metric_type: METRIC_TYPE.COHORT,
    metric_name: '',
    description: '',
    event_name: '',
    value_type: VALUE_TYPE.USER_NUM,
    is_cumulative: 1,
    format: METRIC_FORMAT.ABSOLUTE,
  },
  [METRIC_TYPE.DAILY]: {
    metric_type: METRIC_TYPE.DAILY,
    metric_name: '',
    description: '',
    event_name: '',
    value_type: VALUE_TYPE.USER_NUM,
  },
};

const innerFormData = ref<IMetricForm>(defaultFormData[props.metricType]);
const formRef = ref();
const isEdit = computed(() => !!props.data?.id);
const isCohortType = computed(() => innerFormData.value.metric_type === METRIC_TYPE.COHORT);

// 进入页面时，如果是编辑状态，将原数据进行拷贝
if (isEdit.value && props.data) {
  innerFormData.value = cloneDeep(props.data);
}

// 切换指标类型，替换默认值
const changeMetricType = (type: METRIC_TYPE) => {
  if (isEdit.value) return;
  innerFormData.value = defaultFormData[type];
};

defineExpose({
  validate: () => formRef.value?.validate(),
  getFormData: () => innerFormData.value,
});

</script>
<style lang="scss" scoped>
.card-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 15px;
  border: 1px solid #EFEFF0;
  border-radius: 8px;
  margin-top: 10px;
  cursor: pointer;

  &:hover {
    border: 1px solid #5086F3;
  }

  &.active {
    border: 2px solid #5086F3;
    background-color: #E5EDFD;
    padding: 14px;
  }
}
.card-item + .card-item {
  margin-left: 20px;
}
.is-disabled {
  pointer-events: none;
  .card-item {
    background-color: #F0F1F6;
    opacity: 0.6;
  }
  .card-item.active {
    border-color: #CBD0DE;
    background-color: #F0F1F6;
    opacity: 1;
  }
}
</style>
