<template>
  <div class="flex items-center justify-center">
    <!-- 数据为空展示 -->
    <div
      v-if="metricList.length === 0 && !loading"
      class="flex flex-col items-center justify-center mt-[150px]"
    >
      <SvgIcon
        name="nothing"
        size="240px"
        color="#ffffff"
      />
      <div>There is currently no custom {{ capitalize(metricType) }} Metric</div>
      <t-link hover="color" theme="primary" @click="emits('onCreate')">
        click to add
      </t-link>
    </div>
    <Table
      v-else
      row-key="id"
      drag-sort="row-handler"
      resizable
      max-height="calc(100vh - 280px)"
      :loading="loading"
      :data="metricList"
      :columns="columns"
      :pagination="pagination"
      :on-page-change="onPageChange"
      :on-drag-sort="onDragSort"
    />
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import { IMetric, METRIC_TYPE } from '@/store/configuration/management/type.d';
import Table from 'common/components/table';
import SvgIcon from 'common/components/SvgIcon';
import { capitalize } from 'lodash-es';
const emits = defineEmits(['onCreate']);
defineProps({
  metricList: {
    type: Array as PropType<IMetric[]>,
    default: () => [],
  },
  metricType: {
    type: String,
    default: METRIC_TYPE.COHORT,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  columns: {
    type: Array,
    default: () => [],
  },
  pagination: {
    type: Object,
    default: () => {},
  },
  onPageChange: {
    type: Function,
    default: () => {},
  },
  onDragSort: {
    type: Function,
    default: () => {},
  },
});

</script>

<style lang="scss" scoped>
::v-deep {
  .t-table td.t-table__handle-draggable {
    text-align: left;
  }
}
</style>
