import { METRIC_TYPE, METRIC_FORMAT, VALUE_TYPE } from '@/store/configuration/management/type.d';

export const TABS_OPTIONS = [
  {
    label: 'Cohort Metric',
    value: METRIC_TYPE.COHORT,
  },
  {
    label: 'Daily Metric',
    value: METRIC_TYPE.DAILY,
  },
];

export const METRIC_TYPE_OPTIONS = [
  {
    label: 'Cohort Metrics',
    value: METRIC_TYPE.COHORT,
    icon: 'cohort-metric',
    desc: 'Cohort Metrics description and calculations will depend upon which day install.',
  },
  {
    label: 'Daily Metrics',
    value: METRIC_TYPE.DAILY,
    icon: 'daily-metric',
    desc: 'Daily metric will be calculated based on the date the event occurred.',
  },
];

export const VALUE_TYPE_OPTIONS = [
  {
    label: 'Unique User',
    value: VALUE_TYPE.USER_NUM,
  },
  {
    label: 'Event Count',
    value: VALUE_TYPE.EVENT_NUM,
  },
  {
    label: 'Revenue',
    value: VALUE_TYPE.EVENT_VALUE,
  },
];

export const FORMAT_OPTIONS = [
  {
    label: 'Value',
    value: METRIC_FORMAT.ABSOLUTE,
  },
  {
    label: 'Ratio',
    value: METRIC_FORMAT.PROPORTION,
  },
];

export const CUMULATIVE_OPTIONS = [
  {
    label: 'Yes',
    value: 1,
  },
  {
    label: 'No(By Day)',
    value: 0,
  },
];
