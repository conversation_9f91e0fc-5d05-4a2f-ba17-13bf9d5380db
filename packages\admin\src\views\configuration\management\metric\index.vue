<template>
  <CommonView
    :router-index="-2"
    :hide-right="true"
  >
    <template #views>
      <!-- 页签 -->
      <t-tabs v-model="mStore.metricType" theme="card" @change="mStore.onTabChange">
        <t-tab-panel
          v-for="item in TABS_OPTIONS"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        >
          <div class="metric-container p-[12px]">
            <t-space>
              <t-button theme="primary" @click="mStore.openDrawer">
                <template #icon><add-icon /></template>
                Create
              </t-button>
              <t-button theme="default" @click="mStore.openPreviewDialog">Preview</t-button>
            </t-space>
            <!-- 表格：配置列表 -->
            <MetricsTable
              class="mt-[12px]"
              :loading="mStore.isTableLoading"
              :metric-list="mStore.metricList"
              :metric-type="mStore.metricType"
              :columns="renderColumns"
              :pagination="mStore.pagination"
              :on-page-change="mStore.onPageChange"
              :on-drag-sort="mStore.onDragSort"
              @on-create="mStore.openDrawer"
            />
          </div>
        </t-tab-panel>
      </t-tabs>
      <!-- 抽屉组件 -->
      <t-drawer
        v-model:visible="mStore.isShowDrawer"
        attach="body"
        :size="650"
        :destroy-on-close="true"
        :close-btn="true"
      >
        <template #header>
          <div class="w-full flex items-center justify-center">
            <Text type="title" :content="mStore.isEditDrawer ? 'Edit Metric' : 'Create Metric'" />
          </div>
        </template>
        <DrawerForm
          ref="drawerFormRef"
          :event-list="mStore.eventList"
          :data="mStore.currentEditData"
          :metric-type="mStore.metricType"
        />
        <template #footer>
          <t-space class="flex flex-row-reverse">
            <t-button @click="handleConfirm">{{ mStore.isEditDrawer ? 'Save' : 'Create' }}</t-button>
            <t-button v-if="mStore.isEditDrawer" theme="default" @click="mStore.handleDeleteMetric">Delete</t-button>
            <t-button theme="default" @click="mStore.isShowDrawer=false">Cancel</t-button>
          </t-space>
        </template>
      </t-drawer>
      <!-- 预览弹窗 -->
      <CustomizeColumnsDialog
        v-model:visible="mStore.previewDialogVisible"
        type="group"
        mode="preview"
        title="Metric"
        :loading="mStore.isPreviewDialogLoading"
        :list="mStore.previewMetricList"
      />
    </template>
  </CommonView>
</template>
<script setup lang="tsx">
import { ref } from 'vue';
import Text from 'common/components/Text';
import CommonView from 'common/components/Layout/CommonView.vue';
import MetricsTable from './MetricsTable.vue';
import DrawerForm from './DrawerForm.vue';
import { Link, Switch, Tooltip } from 'tdesign-vue-next';
import { AddIcon, MoveIcon } from 'tdesign-icons-vue-next';
import { useBIManagementMetricStore } from '@/store/configuration/management/metric.store';
import { IMetric } from '@/store/configuration/management/type.d';
import { TABS_OPTIONS } from './const';
import CustomizeColumnsDialog from 'common/components/CustomizeColumnsDialog';
import dayjs from 'dayjs';

// store
const mStore = useBIManagementMetricStore();
mStore.init();

const drawerFormRef = ref();

// 抽屉点击 Create/Save 按钮触发
const handleConfirm = async () => {
  // 表单校验
  const isValidate = await drawerFormRef.value.validate();
  if (isValidate !== true) return;
  // 获取表单数据
  const formData = drawerFormRef.value.getFormData();
  if (mStore.isEditDrawer) {
    mStore.handleUpdateMetric(formData);
  } else {
    mStore.handleCreateMetric(formData);
  }
};

const renderColumns = [
  {
    title: 'Metric Name',
    colKey: 'drag', // 列拖拽排序必要参数
    width: 200,
    ellipsis: (_h: any, { row }: { row: IMetric }) => row.metric_name,
    cell: (h: any, { row }: { row: IMetric }) => (
      h(
        'span',
        {},
        [
          h(MoveIcon, {}, ''),
          h('span', {}, row.metric_name),
        ],
      )
    ),
  },
  {
    title: 'Start Date',
    colKey: 'create_time',
    ellipsis: true,
    cell: (_h: any, { row }: { row: IMetric }) => (
      row.create_time?.value ? dayjs(row.create_time.value).format('YYYY-MM-DD') : ''
    ),
  },
  {
    title: 'Event',
    colKey: 'event_name',
    ellipsis: true,
  },
  {
    title: 'Creator',
    colKey: 'creator',
    ellipsis: true,
  },
  {
    title: 'Description',
    colKey: 'description',
    ellipsis: true,
  },
  {
    title: (h: any) => (
      h(Tooltip, { content: 'Control whether metrics are displayed in the user\'s selection panel.' }, 'State')
    ),
    colKey: 'is_visible',
    cell: (h: any, { row }: { row: IMetric }) => (
      h(Switch, {
        customValue: [1, 0],
        value: row.is_visible,
        onChange: (val: 0 | 1) => mStore.handleSwitchMetricVisible(val, row),
      })
    ),
  },
  {
    title: 'Actions',
    colKey: 'actions',
    fixed: 'right',
    cell: (h: any, { row }: { row: IMetric }) => (
      h(Link, {
        hover: 'color',
        theme: 'primary',
        onClick: () => mStore.openDrawer(row),
      }, 'Edit')
    ),
  },
];

</script>
<style lang="scss" scoped>
.metric-container {
  height: calc(100vh - 152px);
}

::v-deep .t-tabs__nav--card.t-tabs__nav-item {
  border: none;
}
</style>
