<template>
  <div class="flex flex-col w-full min-w-[450px] max-w-[900px]">
    <div class="header font-[600] text-base text-black-primary">{{ props.title }}</div>
    <div class="content pb-[20px]"><slot name="content" /></div>
    <div class="footer flex justify-start mt-[20px]">
      <t-button
        theme="primary"
        type="submit"
        :loading="confirmLoading"
        :disabled="isEdit"
        @click="onSaveBtnClick"
      >
        Save
      </t-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface IProps {
  title: string;
  confirmLoading?: boolean;
  rawData: any;
  formData: any;
}

const props = withDefaults(defineProps<IProps>(), {
  confirmLoading: false,
});

const emit = defineEmits(['update:confirmLoading', 'undo', 'confirm']);
const isEdit = computed(() => JSON.stringify(removePropertiesByNullOrUndefined(props.rawData))
    === JSON.stringify(removePropertiesByNullOrUndefined(props.formData)));

const removePropertiesByNullOrUndefined = (originObj: Record<string, any>) => {
  const mirrorObj: { [key: string]: any } = {};
  Object.keys(originObj).forEach((key) => {
    if (originObj[key] !== null && originObj[key] !== undefined) {
      mirrorObj[key] = originObj[key];
    }
  });
  return mirrorObj;
};

const onSaveBtnClick = async () => {
  emit('confirm');
};
</script>
<style lang="scss" scoped></style>
