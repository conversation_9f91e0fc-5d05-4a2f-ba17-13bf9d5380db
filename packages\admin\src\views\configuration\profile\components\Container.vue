<template>
  <div class="h-full w-full overflow-hidden">
    <div v-auto-animate class="h-full w-full bg-white-primary rounded-large p-[24px]">
      <template v-if="profileStore.isLoading">
        <FullLoading class="relative" />
      </template>
      <template v-else>
        <div class="h-full w-full flex flex-col overflow-auto">
          <BaseInfoFormCard />
          <div><t-divider /></div>
          <EditPasswordCard />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import FullLoading from 'common/components/FullLoading';
import BaseInfoFormCard from './form/basicInfo.vue';
import EditPasswordCard from './form/editPassword.vue';
import useProfileStore from '@/store/configuration/profile/profile.store';
const profileStore = useProfileStore();
</script>
<style lang="scss" scoped></style>
