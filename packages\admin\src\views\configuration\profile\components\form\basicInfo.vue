<template>
  <BaseCard
    v-model:confirm-loading="confirmLoading"
    title="Basic Information"
    :raw-data="rawData"
    :form-data="formData"
    @undo="onUndo"
    @confirm="onConfirm"
  >
    :form-data="formData" @undo="onUndo" @confirm="onConfirm" >
    <template #content>
      <t-form
        ref="formRef"
        label-align="top"
        :disabled="confirmLoading"
        :data="formData"
        :rules="rules"
        @submit="onSubmit"
      >
        <div class="grid grid-cols-2 gap-x-[18px]">
          <t-form-item
            label="First Name"
            name="first_name"
            :required-mark="true"
          >
            <div class="w-full"><Input v-model="formData.first_name" trim /></div>
          </t-form-item>
          <t-form-item
            label="Last Name"
            name="last_name"
            :required-mark="true"
          >
            <div class="w-full"><Input v-model="formData.last_name" trim /></div>
          </t-form-item>
          <t-form-item
            label="Company"
            name="company"
          >
            <div class="w-full"><Input v-model="formData.company" trim /></div>
          </t-form-item>
          <t-form-item
            label="Title"
            name="title"
          >
            <div class="w-full"><Input v-model="formData.title" trim /></div>
          </t-form-item>
        </div>
      </t-form>
    </template>
  </BaseCard>
</template>

<script setup lang="ts">
import Input from 'common/components/Input';
import BaseCard from '../BaseCard.vue';
import { Form, FormRules, SubmitContext } from 'tdesign-vue-next';
import { reactive, ref, watch, computed } from 'vue';
import useProfileStore from '@/store/configuration/profile/profile.store';
import { useLoading } from 'common/compose/loading';
import { modifyUser } from 'common/service/configuration/profile';
import { useTips } from 'common/compose/tips';
import validator from 'validator';
import matches = validator.matches;

export interface IUserFormData {
  first_name?: string;
  last_name?: string;
  title?: string;
  company?: string;
}

const profileStore = useProfileStore();
const { success: successTip, err: errTip } = useTips();
const formData = reactive<IUserFormData>({
  first_name: profileStore.user?.first_name ?? undefined,
  last_name: profileStore.user?.last_name ?? undefined,
  title: profileStore.user?.title ?? undefined,
  company: profileStore.user?.company ?? undefined,
});

const { isLoading: confirmLoading, showLoading, hideLoading } = useLoading(false);
const formRef = ref<(InstanceType<typeof Form> & HTMLFormElement) | null>(null);
const rules: FormRules<typeof formData> = {
  first_name: [
    {
      required: true,
      message: 'First name can not be empty',
    },
    {
      trigger: 'change',
      validator: (val: string) => {
        try {
          if (`${formData.first_name}${formData.last_name}`.length > 20) {
            throw 'Name cannot exceed 20 characters.';
          }
          if (val && !matches(val, /^[A-z\u4e00-\u9fa5]+$/)) {
            throw 'First name only supports Chinese and letters';
          }
        } catch (e) {
          return {
            result: false,
            type: 'error',
            message: e as string,
          };
        }

        return true;
      },
    },
  ],
  last_name: [
    {
      required: true,
      message: 'Last name can not be empty',
    },
    {
      trigger: 'change',
      validator: (val: string) => {
        try {
          if (val && !matches(val, /^[A-z\u4e00-\u9fa5]+$/)) {
            throw 'First name only supports Chinese and letters';
          }
        } catch (e) {
          return {
            result: false,
            type: 'error',
            message: e as string,
          };
        }

        return true;
      },
    },
  ],
  title: [
    {
      trigger: 'blur',
      validator: (val) => {
        if (val?.length > 20) {
          return {
            result: false,
            type: 'error',
            message: 'The title cannot exceed 20 characters.',
          };
        }
        return true;
      },
    },
  ],
  company: [
    {
      trigger: 'blur',
      validator: (val) => {
        if (val?.length > 20) {
          return {
            result: false,
            type: 'error',
            message: 'The company cannot exceed 20 characters.',
          };
        }
        return true;
      },
    },
  ],
};
const rawData = computed(() => {
  const userInfo = profileStore?.user;
  if (userInfo) {
    const { first_name: firstName, last_name: lastName, title, company } = userInfo;
    return {
      first_name: firstName,
      last_name: lastName,
      title,
      company,
    };
  }
  return {};
});
const onSubmit = async (context: SubmitContext<FormData>) => {
  const { validateResult } = context;
  if (validateResult === true) {
    try {
      showLoading();
      const user = await modifyUser({
        uid: profileStore?.user!.uuid,
        first_name: formData.first_name!,
        last_name: formData.last_name!,
        title: formData.title,
        company: formData.company,
      });
      successTip('Modified successfully');
      profileStore.changeUser(user);
    } catch (e) {
      errTip((e as any).message ?? 'Modified failed');
    } finally {
      hideLoading();
    }
  }
};

const onUndo = () => {
  resetForm(profileStore.user);
};

const onConfirm = () => {
  formRef.value?.submit();
};

const resetForm = (data: Partial<IUserFormData> | null) => {
  formData.first_name = data?.first_name;
  formData.last_name = data?.last_name;
  formData.title = data?.title;
  formData.company = data?.company;
  formRef.value?.clearValidate();
};

watch(
  () => profileStore.user,
  () => resetForm(profileStore.user),
);
</script>
<style lang="scss" scoped></style>
