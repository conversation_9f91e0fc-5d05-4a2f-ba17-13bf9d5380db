<template>
  <BaseCard
    v-model:confirm-loading="confirmLoading"
    :raw-data="rawData"
    :form-data="formData"
    title="Change Password"
    @undo="onUndo"
    @confirm="onConfirm"
  >
    <template #content>
      <t-form
        ref="formRef"
        :data="formData"
        label-align="top"
        :rules="rules"
        @submit="onSubmit"
      >
        <div class="grid grid-cols-1">
          <t-form-item
            label="Old Password"
            name="oldPassword"
            :required-mark="true"
          >
            <div class="w-full">
              <Input
                v-model="formData.oldPassword"
                type="password"
              />
            </div>
          </t-form-item>
          <t-form-item
            label="New Password"
            name="newPassword"
            :required-mark="true"
          >
            <div class="w-full">
              <Input
                v-model="formData.newPassword"
                type="password"
              />
            </div>
          </t-form-item>
          <t-form-item
            label="Confirm Password"
            name="confirmNewPassword"
            :required-mark="true"
          >
            <div class="w-full">
              <Input
                v-model="formData.confirmNewPassword"
                type="password"
              />
            </div>
          </t-form-item>
        </div>
      </t-form>
    </template>
  </BaseCard>
</template>

<script setup lang="ts">
import Input from 'common/components/Input/index.vue';
import BaseCard from '../BaseCard.vue';
import { Form, FormRules, SubmitContext } from 'tdesign-vue-next';
import { reactive, ref } from 'vue';
import { useLoading } from 'common/compose/loading';
import { resetPassWord } from 'common/service/user/logout';
import { useTips } from 'common/compose/tips';
const { success: successTips, err: errorTips } = useTips();
const { isLoading: confirmLoading, showLoading, hideLoading } = useLoading(false);
const rawData = {
  oldPassword: '',
  newPassword: '',
  confirmNewPassword: '',
};
const formData = reactive({ ...rawData });
const formRef = ref<(InstanceType<typeof Form> & HTMLFormElement) | null>(null);
const rules: FormRules<typeof formData> = {
  oldPassword: [
    {
      required: true,
      message: 'Old password can not be empty',
    },
  ],
  newPassword: [
    {
      required: true,
      message: 'New password can not be empty',
    },
    {
      trigger: 'blur',
      validator: (val) => {
        try {
          const len = val.length;
          if (len > 20 || len < 8) {
            throw 'Letters and Numbers, the length is [8,20].';
          }
          if (!/(?=.*[a-z])/.test(val)) {
            throw 'At least one lowercase letter is required.';
          }
          if (!/(?=.*[A-Z])/.test(val)) {
            throw 'At least one uppercase letter is required.';
          }
          if (!/(?=.*[0-9])/.test(val)) {
            throw 'At least one number letter is required.';
          }
        } catch (error) {
          return {
            result: false,
            type: 'error',
            message: error as string,
          };
        }
        return true;
      },
    },
  ],
  confirmNewPassword: [
    {
      trigger: 'blur',
      validator: (val: string) => {
        if (val !== formData.newPassword) {
          return {
            result: false,
            type: 'error',
            message: 'The two entered passwords do not match',
          };
        }

        return true;
      },
    },
  ],
};

const onSubmit = async (context: SubmitContext<FormData>) => {
  const { validateResult } = context;
  if (validateResult === true) {
    try {
      showLoading();
      await resetPassWord({
        old_password: formData.oldPassword,
        new_password: formData.newPassword,
      });
      successTips('Reset password successfully');
      formRef.value?.reset();
    } catch (e) {
      errorTips((e as any).message ?? e);
    } finally {
      hideLoading();
    }
  }
};
const onUndo = () => {
  formRef.value?.reset();
};
const onConfirm = () => {
  formRef.value?.submit();
};
</script>
<style lang="scss" scoped></style>
