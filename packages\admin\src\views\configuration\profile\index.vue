<template>
  <common-view
    class="h-screen"
    :hide-right="true"
    :store="profileStore"
  >
    <template #views>
      <ContentContainer />
    </template>
  </common-view>
</template>

<script setup lang="ts">
import useProfileStore from '@/store/configuration/profile/profile.store';
import CommonView from 'common/components/Layout/CommonView.vue';
import ContentContainer from './components/Container.vue';

const profileStore = useProfileStore();
</script>
<style lang="scss" scoped></style>
