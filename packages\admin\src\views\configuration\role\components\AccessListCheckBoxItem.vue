<template>
  <div v-if="isLeaf">
    <t-checkbox
      class="min-w-[150px]"
      :value="props.value"
      :label="props.label"
      :checked="checked"
      :indeterminate="indeterminate"
      @change="onChange"
    />
  </div>
  <check-box-group
    v-else
    :label="props.label"
    :value="props.value"
    :checked="checked"
    :indeterminate="indeterminate"
    :children="props.children"
    :checked-value="checkedValue"
    @change="checkedBoxGroupChange"
  />
</template>
<script setup lang="ts">
import { computed, ref, inject, Ref } from 'vue';

import CheckBoxGroup from './CheckBoxGroup.vue';
import intersection from 'lodash-es/intersection';

interface IProps {
  value: string;
  label: string;
  children?: IProps[];
}

const privateValue = inject<Ref<string[]>>('value');
const updateValue = inject<(checked: boolean, values: string[]) => void>('updateValue');

// State Section
const props = defineProps<IProps>();

const isLeaf = ref<boolean>(!props?.children?.length);

const valueList = computed(() => props.value.split(','));

const checked = computed(() => {
  if (isLeaf.value) {
    return privateValue?.value?.includes(props.value) ?? false;
  }
  return valueList.value.every(item => privateValue?.value.includes(item));
});

const checkedValue = computed(() => intersection(privateValue?.value, valueList.value));

const indeterminate = computed(() => {
  if (isLeaf.value) {
    return false;
  }
  return checkedValue.value.length > 0 && !checked.value;
});

const onChange = (checked: boolean) => {
  updateValue?.(checked, valueList.value);
};

const checkedBoxGroupChange = (checked: boolean, value: string[]) => {
  updateValue?.(checked, value);
};

// Method Section

// Watch Section

// LiftCycle Section
</script>
<style lang="scss" scoped></style>
