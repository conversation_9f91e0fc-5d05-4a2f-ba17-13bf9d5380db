<template>
  <t-collapse-panel>
    <template #header>
      <Checkbox
        :value="props.value"
        :label="props.label"
        :checked="checked"
        :indeterminate="indeterminate"
        :is-leaf="false"
        @change="onChange"
      />
    </template>
    <div class="flex flex-row flex-wrap gap-4">
      <AccessListCheckBoxItem
        v-for="(item, index) in props.children"
        :key="index"
        :label="item.label"
        :value="item.value"
        :children="item?.children"
      />
    </div>
  </t-collapse-panel>
</template>

<script setup lang="ts">
import { difference, union } from 'lodash-es';
import { computed, provide } from 'vue';
import Checkbox from './Checkbox.vue';
import AccessListCheckBoxItem from '@/views/configuration/role/components/AccessListCheckBoxItem.vue';

interface IProps {
  label: string;
  value: string;
  checkedValue: string[];
  children?: IProps[];
}

const props = defineProps<IProps>();

const emits = defineEmits(['change']);

const valueList = computed(() => props.value.split(','));
const modelValue = computed(() => props.checkedValue);

const checked = computed(() => valueList.value.every(item => modelValue.value.includes(item)));
const indeterminate = computed(() => modelValue.value.length > 0 && !checked.value);

const onChange = (checked: boolean, _: unknown, { e }: { e: Event }) => {
  e.stopPropagation();
  const newValue  = checked ? valueList.value : [];
  emits('change', newValue);
};

const updateValue = (checked: boolean, values: string[]) => {
  const newValue = (checked ? union : difference)(modelValue.value, values);
  emits('change', newValue);
};


provide('value', modelValue);
provide('updateValue', updateValue);
</script>

<style scoped lang="scss">
:deep(.t-collapse-panel__content) {
  @apply p-4;
}

:deep(.t-divider) {
  @apply m-0;
}
</style>
