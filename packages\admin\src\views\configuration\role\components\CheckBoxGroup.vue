<template>
  <div class="w-full gap-4">
    <div class="font-semibold flex flex-row gap-2 mb-3">
      <div class="whitespace-nowrap">
        <Checkbox
          :checked="props.checked"
          :value="props.value"
          :label="props.label"
          :indeterminate="props.indeterminate"
          :is-leaf="false"
          @change="handleSelectAll"
        />
      </div>
      <div class="grow flex items-center justify-center">
        <t-divider :dashed="true" />
      </div>
    </div>
    <div class="flex flex-row gap-4">
      <t-checkbox-group v-model="modelValue">
        <div
          v-for="secondChild in props.children"
          :key="secondChild.label"
          class="inline-block min-w-[150px]"
        >
          <div v-if="secondChild.children?.length">
            <div
              v-for="secondInnerChild in secondChild.children"
              :key="secondInnerChild.label"
            >
              <div v-if="secondInnerChild.children?.length">
                <div v-for="i in secondInnerChild.children" :key="i">
                  <t-checkbox
                    :label="`${secondChild.label}.${secondInnerChild.label}.${i.label}`"
                    :value="i.value"
                    @change="(checked:boolean) => onChange(checked, i.value)"
                  />
                </div>
              </div>
              <t-checkbox
                v-else
                :label="`${secondChild.label}.${secondInnerChild.label}`"
                :value="secondInnerChild.value"
                @change="(checked:boolean) => onChange(checked, secondInnerChild.value)"
              />
            </div>
          </div>
          <t-checkbox
            v-else
            :label="secondChild.label"
            :value="secondChild.value"
            @change="(checked:boolean) => onChange(checked, secondChild.value)"
          />
        </div>
      </t-checkbox-group>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import Checkbox from './Checkbox.vue';
import { difference, union } from 'lodash-es';

interface ChildrenIProps {
  value: string;
  label: string;
  children?: ChildrenIProps[];
}

interface IProps {
  value: string;
  label: string;
  // 已勾选的叶子节点value
  checkedValue: string[];
  checked: boolean;
  indeterminate?: boolean;
  children?: ChildrenIProps[];
}

const emits = defineEmits(['change']);

// State Section
const props = defineProps<IProps>();
const modelValue = ref(props.checkedValue);
const valueList = computed(() => props.value.split(','));

// Method Section
const handleSelectAll = (checked: boolean) => {
  modelValue.value = checked ? valueList.value : [];
  emits('change', checked, valueList.value);
};

const onChange = (checked: boolean, value: string) => {
  modelValue.value = (checked ? union : difference)(modelValue.value, [value]);
  emits('change', checked, [value]);
};

// Watch Section
watch(
  () => props.checkedValue,
  (values: string[]) => {
    modelValue.value = values;
  },
);

// LiftCycle Section
</script>
<style lang="scss" scoped></style>
