<template>
  <t-checkbox
    :value="props.value"
    :checked="props.checked"
    :indeterminate="props.indeterminate"
    @change="onChange"
  >
    <template #label>
      <slot name="label">
        <div v-if="!props.isLeaf">
          <div class="font-semibold">{{ props.label }}</div>
        </div>
        <div v-else>
          {{ props.label }}
        </div>
      </slot>
    </template>
  </t-checkbox>
</template>

<script setup lang="ts">
interface IProps {
  value: string;
  label: string;
  isLeaf?: boolean;
  checked: boolean;
  indeterminate?: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  isLeaf: true,
  indeterminate: false,
});

const emits = defineEmits(['change']);
const onChange = (checked: boolean, context: { e: Event }) => {
  emits('change', checked, [props.value], context);
};
</script>

<style scoped lang="scss"></style>
