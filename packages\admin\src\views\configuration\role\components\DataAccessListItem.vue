<template>
  <div class="w-full flex gap-2 flex-col">
    <div class="flex justify-between w-full h-[36px]">
      <Checkbox
        class="text-base"
        :is-leaf="false"
        :label="props.label"
        :value="childLeafValue"
        :checked="checked"
        :indeterminate="indeterminate"
        @change="handleSelectAll"
      >
        <template #label>
          <div class="font-semibold text-base">{{ props.label }}</div>
        </template>
      </Checkbox>
      <Input
        v-model="filterValue"
        :type="props.formStatus === STATUS.VIEW ? 'hidden' : 'text'"
        clearable
        class="w-1/6"
      >
        <template #suffix>
          <SearchIcon />
        </template>
      </Input>
    </div>
    <div class="flex flex-col gap-2">
      <t-collapse
        v-auto-animate
        class="flex flex-col gap-4"
        expand-icon-placement="right"
        :default-value="props.collapseExpand"
        :expand-on-row-click="false"
        :borderless="true"
      >
        <AccessListItem
          v-for="item in filterOptions"
          :key="item!.value"
          :label="item!.label"
          :value="item!.value"
          :checked-value="getCheckedValue(item!.value)"
          :children="item?.children"
          @change="updateValues => handleAccessListItemChange(item.value, updateValues)"
        />
      </t-collapse>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { SearchIcon } from 'tdesign-icons-vue-next';
import Checkbox from './Checkbox.vue';
import Input from 'common/components/Input';
import AccessListItem from './AccessListItem.vue';
import { refDebounced } from '@vueuse/core';
import { filterOptionsByLabel, formatOption } from '@/views/configuration/role/utils';
import { STATUS } from '@/views/configuration/role/components/type.d';
import { difference, intersection, union } from 'lodash-es';
import { useRoleStore } from '@/store/configuration/role/role.store';
import { storeToRefs } from 'pinia';

interface IProps {
  // data:any
  modelValue: string[];
  options: any[];
  label: string;
  collapseExpand: number[];
  formStatus: STATUS;
}

const props = defineProps<IProps>();

const emits = defineEmits(['update:modelValue']);

const { curRole } = storeToRefs(useRoleStore());

// State Section
const modelValue = ref<string[]>(props.modelValue);
const filterValue = ref<string>('');
const childLeafValues = computed(() => formatOptions.value?.reduce((resList, option) => resList.concat(option.value.split(',')), [] as string[]));
const childLeafValue = computed(() => childLeafValues.value.join(','));

const checked = computed(() => childLeafValues.value.every(val => modelValue.value.includes(val)));

const indeterminate = computed(() => !!modelValue.value.length && !checked.value);

const debouncedFilterValue = refDebounced(filterValue, 300);

const formatOptions = computed(() => props.options?.map(item => formatOption(item, modelValue.value)).filter(Boolean));

const filterOptions = computed(() => filterOptionsByLabel(debouncedFilterValue.value, formatOptions.value));

// Method Section
const handleSelectAll = (checked: boolean) => {
  modelValue.value = checked ? childLeafValues.value : [];
  emits('update:modelValue', modelValue.value);
};

const getCheckedValue = (childLeafValue: string) => {
  const childLeafValues = childLeafValue.split(',');
  return intersection(childLeafValues, modelValue.value);
};

const handleAccessListItemChange = (value: string, updateValues: string[]) => {
  const childLeafValues = value.split(',');
  const diffValueByChild = difference(modelValue.value, childLeafValues);
  modelValue.value = union(diffValueByChild, updateValues);
  emits('update:modelValue', modelValue.value);
};

// Set All Selected if SYSTEM Reserved Role
onMounted(() => {
  if (curRole.value?.game_code === '') {
    modelValue.value = childLeafValues.value;
    emits('update:modelValue', modelValue.value);
  }
});

// Watch Section

// LiftCycle Section
</script>
<style lang="scss" scoped>
:deep(.t-collapse-panel) {
  @apply rounded-default overflow-hidden border-solid border-[1px];
}

:deep(.t-collapse-panel__header) {
  @apply bg-background h-10 font-semibold text-[#202A41];
}
</style>
