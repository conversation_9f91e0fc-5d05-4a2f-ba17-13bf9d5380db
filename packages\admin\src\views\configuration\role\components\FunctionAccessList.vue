<template>
  <t-collapse
    class="flex flex-col gap-4"
    expand-icon-placement="right"
    :expand-on-row-click="false"
    :borderless="true"
    :default-expand-all="true"
  >
    <AccessListItem
      v-for="(item, index) in options"
      :key="index"
      :label="item!.label"
      :value="String(item!.value)"
      :checked-value="item!.checkedValue"
      :children="item?.children"
      @change="(value)=>onChange(String(item!.value), value)"
    />
  </t-collapse>
</template>

<script setup lang="ts">
import AccessListItem from '@/views/configuration/role/components/AccessListItem.vue';
import { FunctionAccessListOption } from '@/views/configuration/role/components/type';
import { difference, union } from 'lodash-es';

import { computed, ref, onMounted } from 'vue';
import { useRoleStore } from '@/store/configuration/role/role.store';
import { storeToRefs } from 'pinia';
import { formatOption, generateParentDictionary } from '@/views/configuration/role/utils';

interface IProps {
  modelValue: string[];
  options: FunctionAccessListOption[];
}

const props = defineProps<IProps>();

const emits = defineEmits(['update:modelValue']);

const modelValue = ref<string[]>(props.modelValue);

const { sysMenuParentDictionary, sysMenuLeavesValues, curRole } = storeToRefs(useRoleStore());

const options = computed(() => props.options.map(item => formatOption(item, modelValue.value)).filter(Boolean));
const childLeafValues = computed(() => options.value?.reduce((resList, option) => resList.concat(option.value.split(',')), [] as string[]));
sysMenuParentDictionary.value = generateParentDictionary(options.value);
sysMenuLeavesValues.value = options.value.map(item => item.value.split(',')).flat();

const onChange = (childLeafValue: string, value: string[]) => {
  const childLeafValues = childLeafValue.split(',');
  modelValue.value = union(difference(modelValue.value, childLeafValues), value);
  emits('update:modelValue', modelValue.value);
};

// Set All Selected if SYSTEM Reserved Role
onMounted(() => {
  if (curRole.value?.game_code === '') {
    modelValue.value = childLeafValues.value;
    emits('update:modelValue', modelValue.value);
  }
});
</script>
<style scoped lang="scss">
:deep(.t-collapse-panel) {
  @apply rounded-default overflow-hidden border-solid border-[1px];
}

:deep(.t-collapse-panel__header) {
  @apply bg-background h-10 font-semibold text-[#202A41];
}
</style>
