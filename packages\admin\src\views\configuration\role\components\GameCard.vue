<script setup lang="ts">
import GameIcon from '@/views/configuration/business/components/GameIcon.vue';
import Text from 'common/components/Text';

interface IProps {
  icon?: string;
  name: string;
}

const props = defineProps<IProps>();
</script>

<template>
  <div
    class="flex items-center h-16 bg-white rounded-default gap-2 p-4 border-[2px] transition-all duration-300
    max-w-[160px] overflow-hidden hover:cursor-pointer hover:border-[#5086F3] hover:bg-[#E5EDFD]"
  >
    <div class="image-section h-8 aspect-square">
      <GameIcon :game-name="props.name || 'abc'" />
    </div>
    <Text
      class="w-full"
      :content="props.name"
      :overflow="true"
    />
  </div>
</template>

<style scoped lang="scss"></style>
