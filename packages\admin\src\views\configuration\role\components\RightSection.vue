<template>
  <div v-if="roleInfoLoading" class="w-full h-full flex justify-center items-center">
    <t-loading />
  </div>
  <div
    v-else-if="!roleInfoLoading && curRole"
    class="h-full flex flex-col justify-end rounded-default overflow-hidden"
  >
    <div class="grow overflow-hidden">
      <t-form
        ref="formRef"
        class="h-full"
        reset-type="initial"
        :data="formData"
        :disabled="status === STATUS.VIEW"
        :rules="rules"
        @submit="onSubmit"
      >
        <!-- :data="formData"
        :rules="rules"
        reset-type="initial"
        style="max-width: 100%"
        @reset="onReset"
        @submit="onSubmit" -->
        <t-tabs
          v-model="tabValue"
          theme="card"
        >
          <t-tab-panel
            class="p-4"
            value="FunctionAccess"
            label="Function Access"
            :destroy-on-hide="false"
          >
            <t-form-item
              label="Role Name"
              name="roleName"
            >
              <div
                v-if="status === STATUS.VIEW"
                class="font-semibold"
              >
                {{ formData.roleName }}
              </div>
              <Input
                v-else
                v-model="formData.roleName"
                class="w-1/5"
              />
            </t-form-item>
            <t-form-item
              name="funcAccess"
              class="w-full"
              :label-width="0"
            >
              <FunctionAccessList
                v-model="formData.funcAccess"
                class="w-full"
                :options="sysMenuTree"
                :form-status="status"
              />
            </t-form-item>
          </t-tab-panel>
          <t-tab-panel
            v-auto-animate
            class="data_access__content"
            value="DataAccess"
            label="BI Data Access"
            :destroy-on-hide="false"
          >
            <t-form-item
              :label-width="0"
              name="metric"
            >
              <DataAccessListItem
                v-model="formData.metric"
                label="Metric"
                :options="metricTree"
                :collapse-expand="[0,1,2]"
                :form-status="status"
              />
            </t-form-item>
            <t-form-item
              :label-width="0"
              name="mediaSrc"
            >
              <DataAccessListItem
                v-model="formData.mediaSrc"
                label="Media Source"
                :options="mediaSrcTree"
                :collapse-expand="[]"
                :form-status="status"
              />
            </t-form-item>
            <t-form-item
              :label-width="0"
              name="geo"
            >
              <DataAccessListItem
                v-model="formData.geo"
                class="bg-white"
                label="Geo"
                :options="geoTree"
                :collapse-expand="[]"
                :form-status="status"
              />
            </t-form-item>
          </t-tab-panel>
          <t-tab-panel
            v-auto-animate
            class="data_access__content"
            value="KOLDataAccess"
            label="KOL Data Access"
            :destroy-on-hide="false"
          >
            <t-form-item
              :label-width="0"
              name="kolRegion"
            >
              <DataAccessListItem
                v-model="formData.kolRegion"
                class="bg-white"
                label="Region/Country"
                :options="kolRegion"
                :collapse-expand="[]"
                :form-status="status"
              />
            </t-form-item>
          </t-tab-panel>
        </t-tabs>
      </t-form>
    </div>
    <div
      v-auto-animate
      class="footer flex justify-end gap-2 p-4 border-t-[1px] border-[#DCDFE8]"
    >
      <t-button
        v-for="item in buttonList"
        :key="item.content"
        :loading="item?.loading"
        :content="item.content"
        :theme="item?.theme"
        :disabled="item?.disabled"
        @click="item?.onClick"
      />
    </div>
  </div>
  <div
    v-else
    class="w-full h-full flex justify-center items-center"
  >
    Please choose a role first
  </div>
</template>
<script setup lang="tsx">
import { ref, computed, reactive, watch } from 'vue';
import { MessagePlugin, DialogPlugin, SubmitContext, Form, FormRules } from 'tdesign-vue-next';
import FunctionAccessList from '@/views/configuration/role/components/FunctionAccessList.vue';
import Input from 'common/components/Input';
import DataAccessListItem from './DataAccessListItem.vue';
import { useRoleStore } from '@/store/configuration/role/role.store';
import { storeToRefs } from 'pinia';
import { deleteRole, updateRole, createRole } from 'common/service/configuration/role/role';
import { addParentsIntoArray, formatDataAccess, dataAccessJSONToArray } from '@/views/configuration/role/utils';
import { SYSTEM_ROLE_IDS, STATUS } from '@/views/configuration/role/components/type.d';
import validator from 'validator';
import isAlphanumeric = validator.isAlphanumeric;

interface ButtonListReturn {
  content: string;
  theme?: string;
  loading?: boolean;
  disabled?: boolean;
  onClick?: () => void;
}

interface FormDataType {
  roleName: string,
  funcAccess: string[],
  metric: string[],
  mediaSrc: string[],
  geo: string[],
  kolRegion: string[],
}

const {
  sysMenuParentDictionary,
  sysMenuLeavesValues,
  sysMenuTree,
  metricTree,
  geoTree,
  kolRegion,
  mediaSrcTree,
  curRole,
  roleInfoLoading,
  roleListDisabled,
  gameCode,
} = storeToRefs(useRoleStore());

useRoleStore().resetRoleManagePage();

const tabValue = ref('FunctionAccess');
const status = ref<STATUS>(STATUS.VIEW);
const formRef = ref<(InstanceType<typeof Form> & HTMLFormElement) | null>(null);

const formData = reactive<FormDataType>({
  roleName: '',
  funcAccess: [],
  metric: [],
  mediaSrc: [],
  geo: [],
  kolRegion: [],
});

const deleteConfirmBtn = reactive({
  content: 'Confirm',
  loading: false,
});

const saveBtnLoading = ref(false);

const buttonList = computed<ButtonListReturn[]>(() => {
  const viewButtonList = [
    {
      content: 'Delete',
      theme: 'default',
      needStatus: [STATUS.VIEW],
      loading: false,
      onClick() {
        const roleUserCount = curRole.value?.count;
        if (Number(roleUserCount) === 0) {
          deleteTemplate();
        } else {
          MessagePlugin.error('Role having users cannot be deleted');
        }
      },
    },
    {
      content: 'Duplicate',
      needStatus: [STATUS.VIEW],
      theme: 'default',
      loading: false,
      async onClick() {
        roleInfoLoading.value = true;
        roleListDisabled.value = true;
        status.value = STATUS.CREATE;
        await useRoleStore().duplicateRole();
        roleInfoLoading.value = false;
      },
    },
    {
      content: 'Edit',
      needStatus: [STATUS.VIEW],
      theme: 'primary',
      loading: false,
      onClick() {
        roleListDisabled.value = true;
        status.value = STATUS.EDIT;
      },
    },
    {
      content: 'Cancel',
      theme: 'default',
      needStatus: [STATUS.EDIT, STATUS.CREATE],
      loading: false,
      async onClick() {
        roleInfoLoading.value = true;
        await useRoleStore().cancelDuplicate();

        if (status.value === STATUS.CREATE) {
          MessagePlugin.success('Cancel Duplicate Role');
          curRole.value = undefined;
        } else {
          setFormData();
          MessagePlugin.success('Cancel Edit Role');
        }

        status.value = STATUS.VIEW;
        roleListDisabled.value = false;
        roleInfoLoading.value = false;
      },
    },
    {
      content: 'Save',
      needStatus: [STATUS.EDIT],
      loading: saveBtnLoading.value,
      onClick() {
        formRef.value?.submit();
      },
    },
    {
      content: 'Confirm',
      needStatus: [STATUS.CREATE],
      loading: saveBtnLoading.value,
      onClick() {
        formRef.value?.submit();
      },
    },
  ];

  // 默认角色， 仅返回复制的功能按钮
  if (curRole.value?.id === SYSTEM_ROLE_IDS.ADMIN
    || curRole.value?.id === SYSTEM_ROLE_IDS.MEMBER
  ) {
    return [viewButtonList[1]];
  }

  return viewButtonList.filter(item => item.needStatus.includes(status.value));
});

const rules: FormRules<typeof formData> = {
  roleName: [
    { required: true, message: 'Role Name should not be empty' },
    {
      validator: (val: string) => {
        if (!isAlphanumeric(val, 'en-US', { ignore: ' ' })) {
          return {
            result: false,
            type: 'error',
            message: 'Only letters and numbers allowed for Role Name',
          };
        }
        return true;
      },
    },
  ],
  metric: [
    { required: true, message: 'Please choose at least one metric' },
  ],
  mediaSrc: [
    { required: true, message: 'Please choose at least one mediaSrc' },
  ],
  geo: [
    { required: true, message: 'Please choose at least one geo' },
  ],
};

const setFormData = () => {
  formData.roleName = curRole.value?.role_name ?? '';
  formData.funcAccess = jsonStringToArray(curRole.value?.route_ids);

  if (curRole.value?.metrics.charAt(0) === '[') {
    formData.metric = dataAccessJSONToArray(curRole.value?.metrics);
    formData.mediaSrc = dataAccessJSONToArray(curRole.value?.media_src);
    formData.geo = dataAccessJSONToArray(curRole.value?.goes);
    formData.kolRegion = dataAccessJSONToArray(curRole.value?.kol_region);
  } else {
    formData.metric = stringToArray(curRole.value?.metrics);
    formData.mediaSrc = stringToArray(curRole.value?.media_src);
    formData.geo = stringToArray(curRole.value?.goes);
    formData.kolRegion = stringToArray(curRole.value?.kol_region);
  }
};

const onSubmit = async (context: SubmitContext<FormData>) => {
  const { validateResult, firstError } = context;
  if (validateResult === true) {
    try {
      saveBtnLoading.value = true;
      const funcAccessWithParent = addParentsIntoArray(
        formData.funcAccess,
        sysMenuLeavesValues.value,
        sysMenuParentDictionary.value,
      );
      const sortedFuncAccess = funcAccessWithParent.sort((a, b) => a.localeCompare(b, undefined, { numeric: true }));

      const metricFormatted = formatDataAccess(metricTree.value, formData.metric);
      const mediaSrcFormatted = formatDataAccess(mediaSrcTree.value, formData.mediaSrc);
      const geoFormatted = formatDataAccess(geoTree.value, formData.geo);
      const kolRegionFormatted = formatDataAccess(kolRegion.value, formData.kolRegion);

      if (status.value === STATUS.EDIT) {
        await updateRole({
          id: curRole.value?.id,
          role_name: formData.roleName,
          route_ids: sortedFuncAccess,
          goes: geoFormatted,
          media_src: mediaSrcFormatted,
          metrics: metricFormatted,
          kol_region: kolRegionFormatted,
        });
        MessagePlugin.success('Successfully Updated Role');
      } else if (status.value === STATUS.CREATE) {
        await createRole({
          role_name: formData.roleName,
          route_ids: sortedFuncAccess,
          goes: geoFormatted,
          media_src: mediaSrcFormatted,
          metrics: metricFormatted,
          kol_region: kolRegionFormatted,
          game_code: gameCode.value,
        });
        MessagePlugin.success('Successfully Duplicated Role');
      }

      await useRoleStore().initGameList();
      status.value = STATUS.VIEW;
      roleListDisabled.value = false;
    } catch (e: any) {
      MessagePlugin.error(e.message);
    } finally {
      saveBtnLoading.value = false;
    }
  } else {
    MessagePlugin.error(firstError ?? 'Failed to submit');
  }
};

const stringToArray = (str?: string) => {
  if (!str) {
    return [];
  }
  return str.split(',');
};

const jsonStringToArray = (str?: string) => {
  if (!str) {
    return [];
  }
  return JSON.parse(str);
};

watch(
  () => [curRole.value],
  async () => {
    if (curRole.value) {
      tabValue.value = 'FunctionAccess';
      await setFormData();
      roleInfoLoading.value = false;
    }
  }, { deep: true, immediate: true },
);

const deleteTemplate = async () => {
  const confirm = DialogPlugin.confirm({
    theme: 'danger',
    header: 'Are you sure to delete this role?',
    body: `${curRole.value?.role_name} will be deleted`,
    cancelBtn: 'Cancel',
    confirmBtn: deleteConfirmBtn,
    zIndex: 1000,
    onConfirm: () => {
      confirmCallback();
    },
  });
  const confirmCallback = async () => {
    try {
      deleteConfirmBtn.loading = true;
      await deleteRole(curRole.value!.id);
      MessagePlugin.success('Successfully delete game role');
      confirm.hide();
      await useRoleStore().initGameList();
    } catch (err: any) {
      MessagePlugin.error(err.message);
    } finally {
      deleteConfirmBtn.loading = false;
    }
  };
};
</script>

<style scoped lang="scss">
:deep(.t-tabs__nav-scroll) {
  @apply bg-[#747D9833];
}

:deep(.t-tabs__nav--card.t-tabs__nav-item) {
  @apply border-none px-5 text-[#5086F3] bg-transparent font-semibold;
  &.t-is-active {
    @apply bg-white;
  }
}

:deep(.t-tabs) {
  @apply flex flex-col h-full;
  > .t-tabs__content {
    @apply grow overflow-auto;
    > .t-tab-panel {
      @apply h-full;
    }
  }
}

:deep(.t-checkbox.t-is-disabled .t-checkbox__label) {
    @apply text-black-primary;
}

.data_access__content {
  > div {
    @apply p-4 mb-0 border-b-[16px];
  }

  > div:last-child {
    @apply border-none;
  }
}
</style>
