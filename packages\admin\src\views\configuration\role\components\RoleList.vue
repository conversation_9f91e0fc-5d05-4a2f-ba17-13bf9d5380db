<template>
  <div
    v-loading="isLoading"
    class="relative h-full px-3"
    :class="{ 'disabled-list': roleListDisabled }"
  >
    <div
      v-if="tabBarOffsetTop !== undefined"
      class="aix-role-tabs__bar h-[40px] z-10 absolute left-0 py-2 transition-all duration-200"
      :style="{
        top: tabBarOffsetTop + 'px',
      }"
    >
      <div class="w-[3px] bg-[#5086F3] h-full" />
    </div>
    <RoleListItem
      v-for="item in curRolesByGame"
      :key="item.id"
      :data="item"
      :can-edit="item.level!=='system'"
      :is-active="item.id === curRoleId"
      @change="onActiveRoleNameChange"
    />
  </div>
</template>


<script setup lang="ts">
import RoleListItem from './RoleListItem.vue';
import { computed, ref, watch } from 'vue';
import { useRoleStore } from '@/store/configuration/role/role.store';
import { storeToRefs } from 'pinia';
import { RoleItem } from '@/views/configuration/role/components/type';


const roleStore = useRoleStore();
const { curRolesByGame, curRole, roleListDisabled, isLoading, roleInfoLoading } = storeToRefs(roleStore);
roleStore.init();

const tabBarOffsetTop = ref<number>();
const curRoleId = computed(() => curRole.value?.id ?? '');

const onActiveRoleNameChange = (payload: { data: RoleItem; ref: HTMLDivElement }) => {
  const { data, ref } = payload;

  if (data.id !== curRoleId.value) {
    roleInfoLoading.value = true;
  }

  if (ref) {
    const { offsetTop } = ref;
    tabBarOffsetTop.value = offsetTop;
  }

  curRole.value = data;
};

const activeRoleNameIndicator = () => {
  if (curRole.value && curRolesByGame.value) {
    const activeIndex = curRolesByGame.value.findIndex((item: any) => item.id === curRole.value?.id);
    return activeIndex * 40;
  }
  return undefined;
};

watch(
  () => [curRole.value],
  () => {
    if (!curRole.value) {
      tabBarOffsetTop.value = undefined;
    } else {
      tabBarOffsetTop.value = activeRoleNameIndicator();
    }
  },
);
</script>


<style scoped lang="scss">
.disabled-list {
  pointer-events: none;
  color: var(--td-text-color-disabled);
}
</style>
