<template>
  <div
    ref="itemRef"
    class="item cursor-pointer flex flex-row  hover:bg-background rounded-default h-10 p-2 items-center"
    :class="{ 'active-role': isActive }"
    @click="handleItemClick"
  >
    <div class="role-name overflow-hidden text-ellipsis whitespace-nowrap" :title="props.data.role_name">
      {{ props.data.role_name }}
    </div>
    <div v-if="props.data.count !== undefined" class="count">({{ props.data.count }})</div>
  </div>
</template>

<script lang="ts" setup>
import { ref, unref } from 'vue';
import { useRipple } from '../hooks/useRipple';
import { RoleItem } from '@/views/configuration/role/components/type';
import { useRoleStore } from '@/store/configuration/role/role.store';
import { storeToRefs } from 'pinia';

const { roleInfoLoading } = storeToRefs(useRoleStore());

interface IProps {
  data: RoleItem
  canEdit: boolean;
  isActive?: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  isActive: false,
});

const emits = defineEmits(['change']);
const itemRef = ref<HTMLDivElement>();
useRipple(itemRef);
const handleItemClick = () => {
  if (!roleInfoLoading.value) {
    emits('change', {
      data: props.data,
      ref: unref(itemRef),
    });
  }
};
</script>

<style scoped lang="scss">
.active-role {
  color: var(--aix-text-color-brand);
}
</style>
