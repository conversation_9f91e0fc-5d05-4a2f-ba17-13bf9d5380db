export interface FunctionAccessListOption {
  label: string;
  value: number;
  children?: FunctionAccessListOption[];
}

export interface OptionsMapItem {
  _parentKey?: number;
  label: string;
  checked: boolean;
  indeterminate: boolean;
  value: number | number[];
  children?: OptionsMapItem[];
}

export interface AccessOptionItem {
  label: string;
  value: string;
  checkedValue: string[];
  children?: AccessOptionItem[];
  parentValue?: number,
}

export interface ParentDictItem {
  parent: number;
  children: string[];
}

export interface RoleItem {
  id: number;
  route_ids: string;
  game_code: string;
  role_name: string;
  role_info_id: string;
  goes: string;
  media_src: string;
  metrics: string;
  kol_region: string;
  count: number;
}

export enum SYSTEM_ROLE_IDS {
  ADMIN = 1,
  MEMBER = 2,
}

export enum STATUS {
  VIEW = 0,
  EDIT,
  CREATE,
}