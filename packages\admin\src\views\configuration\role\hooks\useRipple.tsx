import { CSSProperties, MaybeRef, ref, unref } from 'vue';
import { tryOnMounted, tryOnUnmounted } from '@vueuse/core';

/**
 * 参考TDesign官网实现
 * https://github1s.com/Tencent/tdesign-vue-next/blob/develop/src/hooks/useRipple.ts
 * @param el
 * @param fixedRippleColor
 */

const defaultRippleColor = 'rgba(0, 0, 0, 0.25)';
const period = 200;
const noneRippleBg = 'rgba(0, 0, 0, 0)';

/**
 * 用于为el节点增加styles ,migrate from tdesign-vue repo
 * @param el HTMLElement
 * @param styles
 */
function setStyle(el: HTMLElement, styles: CSSProperties): void {
  const keys = Object.keys(styles);
  keys.forEach((key) => {
    // @ts-ignore
    // eslint-disable-next-line no-param-reassign
    el.style[key] = styles[key];
  });
}

// 设置动画颜色 get the ripple animation color
const getRippleColor = (el: HTMLElement, fixedRippleColor?: string) => {
  // get fixed color from params
  if (fixedRippleColor) {
    return fixedRippleColor;
  }
  // get dynamic color from the dataset
  if (el?.dataset?.ripple) {
    return el.dataset.ripple;
  }
  // use css variable
  const cssVariable = getComputedStyle(el).getPropertyValue('--ripple-color');
  if (cssVariable) {
    return cssVariable;
  }
  return defaultRippleColor;
};

export function useRipple(el: MaybeRef<HTMLElement | undefined>, fixedRippleColor?: string) {
  const rippleContainer = ref<HTMLDivElement | null>(null);

  const rippleRef = ref<HTMLDivElement | null>();

  const handleAddRipple = (e: MouseEvent) => {
    const rawEl = unref(el);
    if (!rawEl) return;

    const rawFixedRippleColor = unref(fixedRippleColor);
    const rippleColor = getRippleColor(rawEl, rawFixedRippleColor);

    if (e.button !== 0 || !el) return;
    if (rippleRef.value) {
      return;
    }

    const elStyle = getComputedStyle(rawEl);
    const elBorder = parseInt(elStyle.borderWidth, 10);
    const border = elBorder > 0 ? elBorder : 0;
    const width = rawEl.offsetWidth;
    const height = rawEl.offsetHeight;

    if (rippleContainer.value?.parentNode === null) {
      setStyle(rippleContainer.value, {
        position: 'absolute',
        left: `${0 - border}px`,
        top: `${0 - border}px`,
        width: `${width}px`,
        height: `${height}px`,
        borderRadius: elStyle.borderRadius,
        pointerEvents: 'none',
        overflow: 'hidden',
      });
      rawEl.appendChild(rippleContainer.value);
    }
    // 新增一个ripple
    const ripple = document.createElement('div');
    rippleRef.value = ripple;

    setStyle(ripple, {
      marginTop: '0',
      marginLeft: '0',
      right: `${width}px`,
      width: `${width + 20}px`,
      height: '100%',
      transition: `transform ${period}ms cubic-bezier(.38, 0, .24, 1), background ${period * 2}ms linear`,
      transform: 'skewX(-8deg)',
      pointerEvents: 'none',
      position: 'absolute',
      zIndex: 0,
      backgroundColor: rippleColor,
      opacity: '0.9',
    });

    // fix zIndex：避免遮盖内部元素
    const elMap = new WeakMap();
    for (let n = rawEl.children.length, i = 0; i < n; ++i) {
      const child = rawEl.children[i];
      if ((child as HTMLElement).style.zIndex === '' && child !== rippleContainer.value) {
        (child as HTMLElement).style.zIndex = '1';
        elMap.set(child, true);
      }
    }

    // fix position
    const initPosition = rawEl.style.position ? rawEl.style.position : getComputedStyle(rawEl).position;
    if (initPosition === '' || initPosition === 'static') {
      // eslint-disable-next-line no-param-reassign
      rawEl.style.position = 'relative';
    }
    rippleContainer.value?.insertBefore(ripple, rippleContainer.value?.firstChild);

    setTimeout(() => {
      ripple.style.transform = `translateX(${width}px)`;
    }, 0);
    // 清除动画节点 clear ripple container
    const handleClearRipple = () => {
      ripple.style.backgroundColor = noneRippleBg;

      if (!rawEl) return;

      rawEl.removeEventListener('pointerup', handleClearRipple, false);
      rawEl.removeEventListener('pointerleave', handleClearRipple, false);

      setTimeout(() => {
        ripple.remove();
        rippleRef.value = null;

        if (rippleContainer.value?.children.length === 0) rippleContainer.value?.remove();
      }, period * 2 + 100);
    };
    rawEl.addEventListener('pointerup', handleClearRipple, false);
    rawEl.addEventListener('pointerleave', handleClearRipple, false);
  };

  tryOnMounted(() => {
    const rawEl = unref(el);
    if (!rawEl) {
      return;
    }

    rippleContainer.value = document.createElement('div');
    rawEl.addEventListener('pointerdown', handleAddRipple, false);
  });

  tryOnUnmounted(() => {
    const rawEl = unref(el);
    rawEl?.removeEventListener('pointerdown', handleAddRipple, false);
  });
}
