<template>
  <common-view
    class="h-screen overflow-hidden"
    :content-overflow="true"
    :hide-right="true"
  >
    <template #views>
      <div class="w-full h-full flex flex-col rounded-default gap-4 overflow-auto">
        <!--        <div class="w-full">-->
        <!--          <SwiperContainers-->
        <!--            class="w-full h-full"-->
        <!--            :list="data"-->
        <!--          >-->
        <!--            <template #default="props: { data: any, index: number }">-->
        <!--              <GameCard :name="props.data" />-->
        <!--            </template>-->
        <!--          </SwiperContainers>-->
        <!--        </div>-->
        <div class="grow flex flex-row gap-6 overflow-hidden">
          <div class="h-full w-[240px] min-w-[240px] rounded-default bg-white py-5">
            <div class="flex flex-col gap-4 h-full">
              <div class="font-semibold text-[#202A41] text-base px-5">Role List</div>
              <div class="grow overflow-auto">
                <RoleList />
              </div>
            </div>
          </div>
          <div class="bg-white rounded-default grow">
            <RightSection />
          </div>
        </div>
      </div>
    </template>
  </common-view>
</template>

<script setup lang="ts">
import CommonView from 'common/components/Layout/CommonView.vue';
// import SwiperContainers from 'common/components/SwiperContainers/index.vue';
// import GameCard from './components/GameCard.vue';
import RoleList from './components/RoleList.vue';
import RightSection from '@/views/configuration/role/components/RightSection.vue';

// const data = ['superroot1111111111111111111', 'superroot', 'admin'];

</script>
<style lang="scss" scoped>
:deep(.swiper-slide) {
  flex: unset;
  width: unset;
}

:deep(.t-tabs__nav--card) {
  @apply rounded-none;
}

:deep(.t-tabs) {
  @apply rounded-none;
}
</style>
