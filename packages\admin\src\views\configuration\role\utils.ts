import { AccessOptionItem, FunctionAccessListOption, ParentDictItem } from '@/views/configuration/role/components/type';
import { IPreviewMetric } from '../../../store/configuration/management/type';
import { intersection } from 'lodash-es';

export const formatOption = (
  option: FunctionAccessListOption,
  values: string[],
  depth = 0,
): AccessOptionItem => {
  const { children, label, value } = option;

  const newOption = { label, parentValue: value } as unknown as AccessOptionItem;
  if (children) {
    newOption.children = (
      children.map(item => formatOption(item, values, depth + 1)).filter(Boolean) as AccessOptionItem[]
    ).sort((a, b) => {
      const FLAG = {
        BOTH_NOT_HAS_CHILD: 0,
        A_HAS_CHILD: 1,
        B_HAS_CHILD: 2,
        BOTH_HAS_CHILD: 3,
      };

      let flag = FLAG.BOTH_NOT_HAS_CHILD;
      a.children && (flag |= FLAG.A_HAS_CHILD);
      b.children && (flag |= FLAG.B_HAS_CHILD);

      switch (flag) {
        case FLAG.BOTH_NOT_HAS_CHILD:
        case FLAG.BOTH_HAS_CHILD:
          return a.label.localeCompare(b.label);
        case FLAG.B_HAS_CHILD:
          return -1; // B_TO_A;
        case FLAG.A_HAS_CHILD:
        default:
          return 1; // A_TO_B;
      }
    });

    const childLeafValues = newOption.children.reduce(
      (resValues, { value }) => [...resValues].concat(value.split(',')),
      [] as string[],
    );

    newOption.checkedValue = intersection(childLeafValues, values);
    newOption.value = childLeafValues.join(',');
  } else {
    newOption.value = String(option.value);
    delete newOption.parentValue;

    // 处理只有一层结构的情况
    if (depth === 0) {
      newOption.checkedValue = intersection([newOption.value], values);
    }
  }

  return newOption;
};

export const filterOptionsByLabel = (filterValue: string, options?: AccessOptionItem[]) => {
  if (!options?.length) {
    return;
  }

  const newOptions: AccessOptionItem[] = [];

  for (const option of options) {
    const newOption = { ...option };
    const { label, children } = newOption;
    if (children) {
      // 当前节点非叶子节点，但是序列化后没有子元素，
      // 说明该节点的所有子节点label都不包含filterValue，当前非叶子节点也不应该显示
      newOption.children = filterOptionsByLabel(filterValue, children);
      if (newOption.children?.length) {
        newOptions.push(newOption);
      }
    } else {
      if (label.toLowerCase().includes(filterValue.toLowerCase())) {
        newOptions.push(newOption);
      }
    }
  }
  return newOptions;
};

export const generateParentDictionary = (arr: AccessOptionItem[]) => {
  const parentChildDict: ParentDictItem[] = [];

  arr.forEach(obj => traverse(obj, null, parentChildDict));

  return parentChildDict;
};

const traverse = (node: AccessOptionItem, parent: number | null, parentChildDict: ParentDictItem[]) => {
  if (node.parentValue) {
    // eslint-disable-next-line no-param-reassign
    parent = node.parentValue;
  }

  if (Array.isArray(node.children)) {
    node.children.forEach(child => traverse(child, parent, parentChildDict));
  }

  if (parent) {
    const existingEntry = parentChildDict.find(entry => entry.parent === parent);
    if (existingEntry) {
      existingEntry.children.push(...node.value.split(',').filter(child => !existingEntry.children.includes(child)));
    } else {
      parentChildDict.push({ parent, children: node.value.split(',') });
    }
  }
};

export const addParentsIntoArray = (
  arr: string[],
  sysMenuLeavesValues: string[],
  parentChildDict: ParentDictItem[],
) => {
  const result = intersection(arr, sysMenuLeavesValues);
  arr.forEach((item) => {
    const entries = parentChildDict.filter(entry => entry.children.includes(item));
    entries.forEach((entry) => {
      if (!result.includes(entry.parent.toString())) {
        result.push(entry.parent.toString());
      }
    });
  });

  return result;
};

export const formatDataAccess = (
  oriDataAccesses: (AccessOptionItem | IPreviewMetric)[],
  selectedValues: string[],
): string => {
  const formattedDataAccess = oriDataAccesses.reduce((
    acc: { value: string; children: { value: string }[] }[],
    oriDataAccess: AccessOptionItem | IPreviewMetric,
  ) => {
    if (oriDataAccess.children) {
      const filteredChildren = oriDataAccess.children
        .filter(child => selectedValues.includes(child.value))
        .map(child => ({ value: child.value }));  // Remove the label field
      if (filteredChildren.length > 0) {
        acc.push({
          value: oriDataAccess.value,
          children: filteredChildren,
        });
      }
    }
    return acc;
  }, []);

  return JSON.stringify(formattedDataAccess);
};

export const dataAccessJSONToArray = (str?: string) => {
  if (!str) {
    return [];
  }

  const dataAccesses: AccessOptionItem[] | IPreviewMetric[]  = JSON.parse(str);
  return dataAccesses.flatMap(dataAccess => (dataAccess.children ? dataAccess.children.map(child => child.value) : []));
};
