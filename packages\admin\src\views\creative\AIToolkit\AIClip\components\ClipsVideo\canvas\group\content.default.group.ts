import Konva from 'konva';
import { CORNER_RADIUS } from '../../constant';
/**
 * 视频默认内容区
 */
export class ContentDefaultGroup extends Konva.Group {
  constructor(config?: Konva.GroupConfig) {
    super(config);
    this.init();
  }

  public init() {
    this.drawShape();
  }

  public drawShape() {
    const borderRect = new Konva.Rect({
      width: this.width(),
      height: this.height(),
      stroke: '#818181',
      strokeWidth: 1,
      cornerRadius: CORNER_RADIUS,
      dash: [5, 5],
    });

    const text = new Konva.Text({
      x: this.width() / 2,
      y: this.height() / 2,
      text: 'You can add video footage here and start creating clips~',
      fill: '#818181',
      fontSize: 12,
    });
    text.offsetX(text.width() / 2);
    text.offsetY(text.height() / 2);

    this.add(borderRect, text);
  }
}
