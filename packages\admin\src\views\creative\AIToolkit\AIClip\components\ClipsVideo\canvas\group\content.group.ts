import Konva from 'konva';
import { HANDLE_BORDER_WIDTH } from '../../constant';
import { generateVideoElement } from '../../utils/video';
import { HandleGroup } from './handle.group';
/**
 * 视频内容
 */
export class ContentGroup extends Konva.Group {
  private videoInstance!: HTMLVideoElement;
  private canvasInstance: HTMLCanvasElement;
  private imageFrameSize = 10;

  private imageGroup!: Konva.Group;
  private handleGroup!: HandleGroup;
  constructor(config?: Konva.GroupConfig) {
    super(config);
    this.init();
    this.canvasInstance = document.createElement('canvas');
  }

  public init() {
    this.drawShape();
    this.initVideoInstance();
    this.initListeners();
  }

  public load(videoUrl: string) {
    this.videoInstance.src = videoUrl;
    this.handleGroup.load();
  }

  public reset() {
    this.destroyChildren();
  }

  public drawShape() {
    const imageSize = this.imageFrameSize;
    const imageWidth = this.width() / imageSize;
    console.log(imageWidth);

    const imageGroup = new Konva.Group({
      height: this.height(),
      width: this.width(),
    });

    for (let i = 0; i < imageSize; i++) {
      const image = new Konva.Image({
        image: undefined,
        x: i * imageWidth,
        y: 0,
        width: imageWidth,
        height: this.height(),
      });
      imageGroup.add(image);
    }

    const handleGroup = new HandleGroup({
      height: this.height() + 2 * HANDLE_BORDER_WIDTH,
      x: 0,
      y: 0,
    });
    this.imageGroup = imageGroup;
    this.handleGroup = handleGroup;
    this.add(this.imageGroup, handleGroup);
  }

  private initListeners() {
    this.videoInstance.addEventListener('loadeddata', () => {
      console.log('content video loadeddata');
      console.log(this.videoInstance.videoHeight);
      this.canvasInstance.width = this.videoInstance.videoWidth;
      this.canvasInstance.height = this.videoInstance.videoHeight;
      this.videoInstance.currentTime = 0;
    });
    this.videoInstance.addEventListener('canplay', () => {
      let timer: NodeJS.Timeout | undefined = undefined;
      const { currentTime } = this.videoInstance;
      const { duration } = this.videoInstance;
      const imageFrameTime = duration / this.imageFrameSize;
      if (currentTime >= duration) {
        return;
      }

      // const videoFrameImage = await this.captureVideoFrame();

      this.captureVideoFrame().then((videoFrameImage) => {
        // 默认全部显示第一帧图片, 之后更新图片
        if (currentTime === 0) {
          this.imageGroup.children?.forEach((element) => {
            (element as Konva.Image).image(videoFrameImage);
          });
          timer = setTimeout(() => {
            this.videoInstance.currentTime = currentTime + imageFrameTime;
            timer && clearTimeout(timer);
          }, 300);
        } else {
          const index = Math.floor(currentTime / imageFrameTime);

          const imageFrame = this.imageGroup.children?.[index] as Konva.Image;
          imageFrame.image(videoFrameImage);

          this.videoInstance.currentTime = currentTime + imageFrameTime;
        }
      });
    });
  }

  private initVideoInstance() {
    const videoInstance = generateVideoElement();
    this.videoInstance = videoInstance;
  }

  private captureVideoFrame(): Promise<HTMLImageElement> {
    console.log('start paint video frame image');
    this.canvasInstance.width = this.videoInstance.videoWidth;
    this.canvasInstance.height = this.videoInstance.videoHeight;

    return new Promise((resolve) => {
      const videoFrameImage = new Image();
      const context = this.canvasInstance.getContext('2d');
      context!.drawImage(this.videoInstance, 0, 0, this.videoInstance.videoWidth, this.videoInstance.videoHeight);
      const dataURL = this.canvasInstance.toDataURL('image/png');
      videoFrameImage.src = dataURL;
      videoFrameImage.onload = () => {
        resolve(videoFrameImage);
      };
    });
  }
}
