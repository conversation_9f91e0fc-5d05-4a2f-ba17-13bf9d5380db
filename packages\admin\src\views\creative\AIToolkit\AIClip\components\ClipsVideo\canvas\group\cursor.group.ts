import Konva from 'konva';
import { MASK_LINE_WIDTH, TIMELINE_CONTENT_PADDING_BOTTOM } from '../../constant';
import { proxyDragBoundFunc } from '../../utils/drag';
import { useVideoClipConfigStore } from '../../store/config.store';
import { Vector2d } from 'konva/lib/types';
import eventBus from '../../utils/event';

/**
 * 播放控制句柄
 */
export class CursorGroup extends Konva.Group {
  private triangleCursor!: Konva.Line;
  private readonly videoClipConfigStore = useVideoClipConfigStore();
  private readonly eventBus = eventBus;

  constructor(config?: Konva.GroupConfig) {
    super(config);
    this.init();
  }

  init() {
    console.log(this);
    this.drawShape();
    this.initListeners();
    this.draggable(true);
    const { leftBound, rightBound } = this.videoClipConfigStore.getConfig().timeLineConfig;
    this.dragBoundFunc(proxyDragBoundFunc.call(this, leftBound, rightBound, (pos: Vector2d) => {
      this.eventBus.emit('cursor-changed', {
        x: pos.x - leftBound,
      });
    }));
  }

  drawShape() {
    this.triangleCursor = new Konva.Line({
      x: 0,
      y: 0,
      points: [0, 0, 9, 0, 9, 10, 5, 16, 0, 10],
      stroke: '#016EFF',
      strokeWidth: 1,
      closed: true,
    });
    const verticalLineByTriangle = new Konva.Line({
      points: [5, 18, 5, this.clipHeight() - TIMELINE_CONTENT_PADDING_BOTTOM],
      stroke: 'white',
      strokeWidth: 0.4,
      lineJoin: 'round',
      dash: [10, 5],
    });
    verticalLineByTriangle.offsetX(-verticalLineByTriangle.width() / 2);
    this.add(this.triangleCursor, verticalLineByTriangle);
    this.offsetX(this.triangleCursor.width() / 2 + MASK_LINE_WIDTH);
  }

  initListeners() {
    this.triangleCursor.on('mouseover', () => {
      document.body.style.cursor = 'pointer';
    });
    this.triangleCursor.on('mouseout', () => {
      document.body.style.cursor = 'default';
    });
  }

  updateCursorPositionByTime(curTime: number) {
    const { secondaryScaleToSeconds,
      secondaryScaleToPixel, leftBound } = this.videoClipConfigStore.getConfig().timeLineConfig;
    const curX = (curTime / secondaryScaleToSeconds) * secondaryScaleToPixel;
    this.x(curX + leftBound);
  }
}
