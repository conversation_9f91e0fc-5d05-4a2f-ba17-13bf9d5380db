import Konva from 'konva';
import { proxyDragBoundFunc } from '../../utils/drag';
import { useVideoClipConfigStore } from '../../store/config.store';
import { Vector2d } from 'konva/lib/types';
import { CORNER_RADIUS, TIMELINE_CONTENT_PADDING_X } from '../../constant';
import eventBus from '../../utils/event';
import { ClipsVideoShapeId, useKonvaStore } from '../../store/konva.store';
import { ToolTipLabel } from './timeTooltip.group';
import { formatVideoTime, getTimeByX } from '../../utils/time';
/**
 * 手柄
 */
export class HandleGroup extends Konva.Group {
  private readonly handleStrokeWidth = 4;
  private readonly handleStrokeColor = '#016EFF';
  private readonly handleDragRectWidth = 12;
  private readonly videoClipConfigStore = useVideoClipConfigStore();
  private readonly konvaStore = useKonvaStore();
  private readonly eventBus = eventBus;

  private handleRect!: Konva.Rect;
  private handleLeftDragRect!: Konva.Rect;
  private handleRightDragRect!: Konva.Rect;

  private leftBound!: number;
  private rightBound!: number;

  // 手柄handleRect内边位置
  private leftPosition = 0;
  private rightPosition = 0;

  constructor(config?: Konva.GroupConfig) {
    super(config);
    this.init();
  }

  public load() {
    const { startTime = 0, endTime = 0 } = this.videoClipConfigStore.getConfig().videoConfig;
    const { secondaryScaleToPixel, secondaryScaleToSeconds } = this.videoClipConfigStore.getConfig().timeLineConfig;

    this.leftPosition = (startTime / secondaryScaleToSeconds) * secondaryScaleToPixel + TIMELINE_CONTENT_PADDING_X;
    this.rightPosition =      (endTime / secondaryScaleToSeconds)
    * secondaryScaleToPixel + TIMELINE_CONTENT_PADDING_X + this.handleStrokeWidth;

    this.setWidth(this.rightPosition - this.leftPosition);
    this.x((startTime / secondaryScaleToSeconds) * secondaryScaleToPixel);

    this.visible(true);
  }

  private init() {
    this.drawShape();
    this.initListeners();
    this.draggable(true);
  }

  private drawShape() {
    const { leftBound, rightBound } = this.videoClipConfigStore.getConfig().timeLineConfig;

    const handleRect = new Konva.Rect({
      // width: 0, // 真实宽度可计算需要减去strokeWidth
      height: this.height(),
      x: 0,
      y: 0,
      stroke: this.handleStrokeColor,
      strokeWidth: this.handleStrokeWidth,
      cornerRadius: CORNER_RADIUS,
    });
    this.handleRect = handleRect;

    handleRect.offsetY(handleRect.strokeWidth());

    this.leftBound = leftBound;
    this.rightBound = rightBound;

    this.leftPosition = this.leftBound;

    // handleRect.width()并不是真实的内容去宽度，他两边还包含了strokeWidth/2的宽度
    this.rightPosition = this.leftPosition + handleRect.width() - this.handleStrokeWidth;

    const handleLeftDragRect = new Konva.Rect({
      width: this.handleDragRectWidth,
      height: this.height() * 0.6,
      x: 0,
      y: 0,
      stroke: this.handleStrokeColor,
      strokeWidth: this.handleStrokeWidth,
      cornerRadius: CORNER_RADIUS,
      fill: 'white',
      draggable: true,
    });
    handleLeftDragRect.offsetY(-handleLeftDragRect.height() / 4);
    handleLeftDragRect.offsetX(handleLeftDragRect.width() / 2);

    handleLeftDragRect.dragBoundFunc(pos => proxyDragBoundFunc.call(
      handleLeftDragRect,
      // 左边界拖拽基准线为左stoke中心，需要减去strokeWidth的一半
      this.leftBound - this.handleStrokeWidth / 2,
      this.rightPosition - this.handleStrokeWidth / 2,
      (pos: Vector2d) => {
        const { x } = pos;

        // x位置是strokeWidth的中间，所以要加上strokeWidth的一半，才是内边的leftPosition
        this.leftPosition = x + this.handleStrokeWidth / 2;
        this.setWidth(this.rightPosition - this.leftPosition);
        this.x(this.leftPosition - this.leftBound);

        const leftToolTip = this.konvaStore.getKonvaNode(ClipsVideoShapeId.leftToolTip);
        leftToolTip.x(this.leftPosition);
        leftToolTip.setTooltipText(formatVideoTime(getTimeByX(this.leftPosition - TIMELINE_CONTENT_PADDING_X)));

        this.fire('handle-changed');
      },
    )(pos));

    const handleRightDragRect = new Konva.Rect({
      width: this.handleDragRectWidth,
      height: this.height() * 0.6,
      x: handleRect.width(),
      y: 0,
      stroke: this.handleStrokeColor,
      strokeWidth: this.handleStrokeWidth,
      cornerRadius: CORNER_RADIUS,
      fill: 'white',
      draggable: true,
    });
    handleRightDragRect.offsetY(-handleLeftDragRect.height() / 4);
    handleRightDragRect.offsetX(handleRightDragRect.width() / 2);
    handleRightDragRect.dragBoundFunc(pos => proxyDragBoundFunc.call(
      handleRightDragRect,
      this.leftPosition + this.handleStrokeWidth / 2,
      this.rightBound + this.handleStrokeWidth / 2,
      (pos: Vector2d) => {
        const { x } = pos;
        const rightTooltip: ToolTipLabel = this.konvaStore.getKonvaNode(ClipsVideoShapeId.rightToolTip);
        // x位置是strokeWidth的中间，所以要加上strokeWidth的一半，才是内边的rightPosition
        this.rightPosition = x - this.handleStrokeWidth / 2;
        this.setWidth(this.rightPosition - this.leftPosition);
        this.x(this.leftPosition - this.leftBound);
        rightTooltip.x(this.rightPosition);
        rightTooltip.setTooltipText(formatVideoTime(getTimeByX(this.rightPosition - TIMELINE_CONTENT_PADDING_X)));
        this.fire('handle-changed');
      },
    )(pos));

    this.dragBoundFunc(pos => proxyDragBoundFunc.call(
      this.handleRect,
      this.leftBound,
      this.rightBound - handleRect.width() + this.handleStrokeWidth,
      (pos) => {
        const { x } = pos;
        this.leftPosition = x;
        this.rightPosition = x + handleRect.width() - this.handleStrokeWidth;

        const leftToolTip = this.konvaStore.getKonvaNode(ClipsVideoShapeId.leftToolTip);
        leftToolTip.x(this.leftPosition);
        leftToolTip.setTooltipText(formatVideoTime(getTimeByX(this.leftPosition - TIMELINE_CONTENT_PADDING_X)));
        this.fire('handle-changed');
      },
    )(pos));

    this.handleLeftDragRect = handleLeftDragRect;
    this.handleRightDragRect = handleRightDragRect;
    this.offsetX(handleRect.strokeWidth() / 2);
    this.add(handleRect, handleRightDragRect);
    this.add(handleRect, handleLeftDragRect, handleRightDragRect);
    this.visible(false);
  }
  private initListeners() {
    this.handleRect.on('mouseover', () => {
      document.body.style.cursor = 'pointer';
    });
    this.handleRect.on('mouseout', () => {
      document.body.style.cursor = 'default';
    });

    this.handleLeftDragRect.on('mouseover', () => {
      document.body.style.cursor = 'col-resize';
    });
    this.handleLeftDragRect.on('mouseout', () => {
      document.body.style.cursor = 'default';
    });
    this.handleLeftDragRect.on('dragstart', (evt) => {
      // eslint-disable-next-line no-param-reassign
      evt.cancelBubble = true;
      const leftToolTip = this.konvaStore.getKonvaNode(ClipsVideoShapeId.leftToolTip);
      leftToolTip.visible(true);
    });
    this.handleLeftDragRect.on('dragend', (evt) => {
      // eslint-disable-next-line no-param-reassign
      evt.cancelBubble = true;
      const leftToolTip = this.konvaStore.getKonvaNode(ClipsVideoShapeId.leftToolTip);
      leftToolTip.visible(false);
    });
    this.on('dragstart', () => {
      const leftToolTip = this.konvaStore.getKonvaNode(ClipsVideoShapeId.leftToolTip);
      leftToolTip.visible(true);
    });
    this.on('dragend', () => {
      const leftToolTip = this.konvaStore.getKonvaNode(ClipsVideoShapeId.leftToolTip);
      leftToolTip.visible(false);
    });

    this.handleRightDragRect.on('mouseover', () => {
      document.body.style.cursor = 'col-resize';
    });
    this.handleRightDragRect.on('mouseout', () => {
      document.body.style.cursor = 'default';
    });
    this.handleRightDragRect.on('dragstart', (evt) => {
      // eslint-disable-next-line no-param-reassign
      evt.cancelBubble = true;
      const rightTooltip = this.konvaStore.getKonvaNode(ClipsVideoShapeId.rightToolTip);
      rightTooltip.visible(true);
    });

    this.handleRightDragRect.on('dragend', (evt) => {
      // eslint-disable-next-line no-param-reassign
      evt.cancelBubble = true;
      const rightTooltip = this.konvaStore.getKonvaNode(ClipsVideoShapeId.rightToolTip);
      rightTooltip.visible(false);
    });

    // 手柄宽度变化触发事件
    this.on('handle-changed', () => {
      this.updateClipTime();
    });
  }

  private updateClipTime() {
    const { secondaryScaleToPixel, secondaryScaleToSeconds } = this.videoClipConfigStore.getConfig().timeLineConfig;
    const startTime =      ((this.leftPosition - TIMELINE_CONTENT_PADDING_X)
     / secondaryScaleToPixel) * secondaryScaleToSeconds;
    const endTime =      ((this.rightPosition - TIMELINE_CONTENT_PADDING_X)
    / secondaryScaleToPixel) * secondaryScaleToSeconds;

    this.videoClipConfigStore.setConfig({
      videoConfig: {
        ...this.videoClipConfigStore.getConfig().videoConfig,
        startTime,
        endTime,
      },
    });
    console.log(startTime, endTime);

    this.eventBus.emit('clip-time-updated', { startTime, endTime });
  }

  private setWidth(targetWidth: number): void {
    let newTargetWidth = targetWidth;
    if (newTargetWidth < 0) {
      console.error('width must be greater than 0');
      newTargetWidth = 0;
    }
    this.handleRect.width(newTargetWidth + this.handleStrokeWidth);
    this.handleRightDragRect.x(this.handleRect.width());
  }
}
