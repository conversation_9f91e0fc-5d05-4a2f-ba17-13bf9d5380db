import Konva from 'konva';
import { MASK_LINE_WIDTH, TIMELINE_CONTENT_PADDING_X } from '../../constant';
import { useVideoClipConfigStore } from '../../store/config.store';
import { formatVideoTime } from '../../utils/time';
/**
 * 标尺线
 */
export class MarkLineGroup extends Konva.Group {
  private readonly lineHeight = 8;
  private readonly ruleHeight = 14;
  private readonly color = '#5C5C5C';
  private readonly videoClipConfigStore = useVideoClipConfigStore();

  private ruleLineGroup!: Konva.Group;

  constructor(config?: Konva.GroupConfig) {
    super(config);
    this.init();
  }

  public init() {
    this.drawShape();
    this.initListeners();
  }

  public repaint() {
    this.ruleLineGroup.destroyChildren();
    this.paintMarkLine();
  }

  private drawShape() {
    const shadowLine = new Konva.Line({
      x: 0,
      y: -2.2,
      points: [0, 0, this.width(), 0],
      stroke: '#17161D',
      strokeWidth: 2,
      shadowColor: 'black',
      shadowBlur: 3,
      shadowOffsetY: 2,
      // shadowOpacity: 0.6,
    });

    this.ruleLineGroup = new Konva.Group({
      x: TIMELINE_CONTENT_PADDING_X,
      y: 0,
      height: this.height(),
      width: this.width() - 2 * TIMELINE_CONTENT_PADDING_X,
    });

    this.paintMarkLine();
    this.add(this.ruleLineGroup, shadowLine);
  }
  private initListeners() {}

  private paintMarkLine() {
    const { lineHeight, ruleHeight, color } = this;
    const { timeLineConfig } = this.videoClipConfigStore.getConfig();
    const { primaryScaleToSecondaryScale, secondaryScaleToPixel, secondaryScaleToSeconds } = timeLineConfig;
    console.log({ ...timeLineConfig });

    for (let i = 0; i <= this.ruleLineGroup.width(); i += secondaryScaleToPixel) {
      const isPrimaryScale = i % (secondaryScaleToPixel * primaryScaleToSecondaryScale) === 0;
      if (isPrimaryScale) {
        const numberText = new Konva.Text({
          x: i + 3,
          y: lineHeight + 5,
          text: formatVideoTime((i / secondaryScaleToPixel) * secondaryScaleToSeconds),
          fontSize: 8,
          fill: color,
        });

        this.ruleLineGroup.add(numberText);
      }
      const marker = new Konva.Line({
        points: [i, 0, i, isPrimaryScale ? ruleHeight : lineHeight],
        stroke: color,
        strokeWidth: MASK_LINE_WIDTH,
        lineCap: 'round',
        lineJoin: 'round',
      });
      this.ruleLineGroup.add(marker);
    }
  }
}
