import Konva from 'konva';
import BackwardSvg from '../../asset/backward.svg';
import ForwardSvg from '../../asset/forward.svg';
import VideoPauseSvg from '../../asset/video-pause.svg';
import VideoPlaySvg from '../../asset/video-play.svg';
import VolumeSvg from '../../asset/volume.svg';
import MuteSvg from '../../asset/mute.svg';

import { CONTROL_LAYER_HEIGHT } from '../../constant';
import { ClipsVideoShapeId, useKonvaStore } from '../../store/konva.store';
import { useVideoClipConfigStore } from '../../store/config.store';
import eventBus from '../../utils/event';

export class ControlLayer extends Konva.Layer {
  private readonly konvaStore = useKonvaStore();
  private readonly videoClipConfigStore = useVideoClipConfigStore();
  private readonly eventBus = eventBus;

  private videoPlayBtn!: Konva.Image;
  private videoPauseBtn!: Konva.Image;
  private muteBtn!: Konva.Image;
  private volumeBtn!: Konva.Image;

  constructor(config?: Konva.LayerConfig) {
    super(config);
    this.init();
  }

  public init() {
    this.drawShape();
    this.initListeners();
  }

  // 绘制图形
  public drawShape() {
    const btnSpace = this.clipHeight();

    const videoControlGroup = new Konva.Group({
      width: this.konvaStore.getKonvaNode(ClipsVideoShapeId.stage).width(),
      height: CONTROL_LAYER_HEIGHT,
    });

    // generate play btn
    this.generateBtn('play-btn', VideoPlaySvg, (btn) => {
      this.videoPlayBtn = btn;
      btn.visible(true);
      btn.x(this.width() / 2);
      videoControlGroup.add(this.videoPlayBtn);

      // generate forward btn
      let forwardImage: Konva.Image | null = null;
      this.generateBtn('forward-btn', ForwardSvg, (btn) => {
        forwardImage = btn;
        btn.x(this.videoPlayBtn?.getPosition().x + btnSpace);
        videoControlGroup.add(forwardImage);

        this.generateBtn('mute-btn', MuteSvg, (btn) => {
          this.muteBtn = btn;
          btn.height(btn.height() * 1.5);
          btn.width(btn.width() * 1.5);
          btn.offsetY(btn.height() / 2);
          btn.x(forwardImage!.getPosition().x + btnSpace);
          videoControlGroup.add(this.muteBtn);
        });

        this.generateBtn('volume-btn', VolumeSvg, (btn) => {
          this.volumeBtn = btn;
          btn.height(btn.height() * 1.5);
          btn.width(btn.width() * 1.5);
          btn.offsetY(btn.height() / 2);
          btn.visible(false);
          btn.x(forwardImage!.getPosition().x + btnSpace);
          videoControlGroup.add(this.volumeBtn);
        });
      });

      // generate backward btn
      let backwardImage: Konva.Image | null = null;
      this.generateBtn('backward-btn', BackwardSvg, (btn) => {
        backwardImage = btn;
        btn.x(this.videoPlayBtn?.getPosition().x - btnSpace);
        videoControlGroup.add(backwardImage);
      });
    });

    // generate pause btn
    this.generateBtn('pause-btn', VideoPauseSvg, (btn) => {
      this.videoPauseBtn = btn;
      btn.visible(false);
      btn.x(this.width() / 2);
      videoControlGroup.add(this.videoPauseBtn);
    });

    this.add(videoControlGroup);
  }
  public reset() {
    this.videoPlayBtn.visible(true);
    this.videoPauseBtn.visible(false);
  }

  public initListeners() {
    this.on('click', (event) => {
      const { src } = this.videoClipConfigStore.getConfig().videoConfig;
      if (!src) return;
      const shape = event.target;
      const shapeName = shape.name();
      console.log(shapeName);

      switch (shapeName) {
        case 'play-btn':
          this.videoPlayBtn.visible(false);
          this.videoPauseBtn.visible(true);
          this.eventBus.emit('play-video');
          break;
        case 'forward-btn':
          this.eventBus.emit('forward-video');
          break;
        case 'backward-btn':
          this.eventBus.emit('backward-video');
          break;
        case 'pause-btn':
          this.videoPlayBtn.visible(true);
          this.videoPauseBtn.visible(false);
          this.eventBus.emit('pause-video');
          break;
        case 'mute-btn':
          this.muteBtn.visible(false);
          this.volumeBtn.visible(true);
          this.eventBus.emit('video-mute', { mute: false });
          break;
        case 'volume-btn':
          this.volumeBtn.visible(false);
          this.muteBtn.visible(true);
          this.eventBus.emit('video-mute', { mute: true });
          break;
        default:
          break;
      }
    });
  }

  private generateBtn(id: string, imageUrl: string, callback: (image: Konva.Image) => void) {
    Konva.Image.fromURL(imageUrl, (svg) => {
      svg.setAttrs({
        y: this.clipHeight() / 2,
        width: this.clipHeight() / 2,
        height: this.clipHeight() / 2,
        id,
        name: id,
      });
      svg.offsetY(svg.height() / 2);
      svg.offsetX(svg.width() / 2);
      svg.on('mouseover', () => {
        const { src } = this.videoClipConfigStore.getConfig().videoConfig;
        document.body.style.cursor = src ? 'pointer' : 'not-allowed';
      });
      svg.on('mouseout', () => {
        document.body.style.cursor = 'default';
      });
      callback(svg);
    });
  }
}
