// 默认的Rect边框圆角
export const CORNER_RADIUS = 8;

// stage
export const STAGE_BACKGROUND_COLOR = '#17161D';

// Control Layer
export const CONTROL_LAYER_HEIGHT = 72;
export const CONTROL_LAYER_BACKGROUND_COLOR = '#17161D';

/**
 * timeline layer
 */

// 时间轴刻度线宽度
export const MASK_LINE_WIDTH = 1;
export const MASK_LINE_GROUP_HEIGHT = 30;
// 内容区域X轴填充
export const TIMELINE_CONTENT_PADDING_X = 20;
// 内容区底部padding
export const TIMELINE_CONTENT_PADDING_BOTTOM = 12;

/**
 * handle group
 */
// 手柄边框宽度
export const HANDLE_BORDER_WIDTH = 4;
