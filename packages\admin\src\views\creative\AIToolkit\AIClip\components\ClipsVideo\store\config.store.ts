import { VideoClipConfig } from '../types';
import { StoreUniqueKey, defineStore } from './index';

export const useVideoClipConfigStore = defineStore(StoreUniqueKey.VideoClipConfig, () => {
  let defaultConfig: VideoClipConfig = {
    videoConfig: {
      startTime: 0,
      endTime: 0,
      src: '',
      poster: '',
      duration: 0,
    },
    timeLineConfig: {
      leftBound: 0,
      rightBound: 0,
      primaryScaleToSecondaryScale: 10,
      secondaryScaleToPixel: 10,
      secondaryScaleToSeconds: 0.1,
    },
    container: undefined,
  };

  const setConfig = (newConfig: Partial<VideoClipConfig>) => (defaultConfig = {
    ...defaultConfig,
    ...newConfig,
  });

  const getConfig = () => defaultConfig;

  return {
    setConfig,
    getConfig,
  };
});
