export type EventCallback = (...args: any[]) => void;
export class EventBus {
  private eventMap = new Map<string, EventCallback[]>();
  on(eventName: string, cb: EventCallback) {
    const events = this.eventMap.get(eventName);
    if (events) {
      events.push(cb);
    } else {
      this.eventMap.set(eventName, [cb]);
    }
  }

  once(eventName: string, cb: EventCallback) {
    return new Promise((resolve) => {
      this.on(eventName, cb);
      resolve(true);
    }).then(() => {
      this.off(eventName, cb);
    });
  }

  off(eventName: string, cb: EventCallback) {
    const events = this.eventMap.get(eventName);
    const newEvents = events?.filter(fn => cb !== fn) || [];
    this.eventMap.set(eventName, newEvents!);
  }

  emit(eventName: string, payload?: Record<string, any>) {
    const events = this.eventMap.get(eventName);
    if (events) {
      events.forEach((cb) => {
        cb(payload);
      });
    }
  }
}
const eventBus = new EventBus();

export default eventBus;
