<template>
  <BaseDialog
    ref="dialogRef"
    cancel-text="Cancel"
    width="530px"
    :visible="props.visible"
    @close="onClose"
  >
    <template #title>
      <div class="w-full">DownLoading Clips Video</div>
    </template>
    <template #default>
      <div class="p-4">
        <div class="progress">
          <div class="">{{ progressText }}</div>
          <t-progress
            theme="line"
            :color="{ from: '#0052D9', to: '#00A870' }"
            :percentage="percentage"
            :status="status"
          />
        </div>
        <div class="help mt-8 flex justify-center font-semibold">
          Note:Please stay at this page until the download is complete.
        </div>
      </div>
    </template>
    <template #footer>
      <div class="w-full flex justify-center">
        <t-button
          :content="percentage===100?'Close':'Cancel'"
          @click="onClose"
        />
      </div>
    </template>
  </BaseDialog>
</template>
<script lang="ts" setup>
import BaseDialog from 'common/components/Dialog/Base';
import { ref, computed, watch } from 'vue';
import { StatusEnum } from 'tdesign-vue-next';
import {
  ClipsVideoTask,
  getVideoClipStatus,
  GetVideoClipStatusResponse,
  TaskStatus,
  GetVideoClipStatusTask,
} from 'common/service/creative/aigc_toolkit';
import { useDownloadFile, DownloadFileTaskQueue } from 'common/compose/download-file';
import { autoSuitProto } from 'common/utils/url';
interface IProps {
  visible: boolean;
  videoClipsTask: ClipsVideoTask[];
}

interface PollingTaskFunction {
  (): void;
  stop?: () => void;
}

type ClipsVideoTaskStatusMap = {
  [key in TaskStatus]: number;
};

const emit = defineEmits(['close']);
const props = defineProps<IProps>();

const status = computed<StatusEnum>(() => (percentage.value === 100 ? 'success' : 'active'));
const percentage = computed(() => {
  const total = Object.keys(videoClipsTaskStatus.value).length * props.videoClipsTask.length;

  if (total === 0) return 0;
  if (videoClipsTaskStatus.value.START_DOWNLOAD === props.videoClipsTask.length) return 100;

  const statusList = [TaskStatus.WAITING, TaskStatus.PROCESSING, TaskStatus.FINISH, TaskStatus.START_DOWNLOAD];
  const finishedTaskCount = statusList.reduce(
    (acc, cur, index) => acc + videoClipsTaskStatus.value[cur] * (index + 1),
    0,
  );

  return Math.floor((finishedTaskCount / total) * 100);
});
const progressText = computed(() => `Processing scenes  ${videoClipsTaskStatus.value.START_DOWNLOAD}/${props.videoClipsTask.length}.`);
const videoClipsTaskStatus = ref<ClipsVideoTaskStatusMap>({
  [TaskStatus.PROCESSING]: 0,
  [TaskStatus.FINISH]: 0,
  [TaskStatus.WAITING]: 0,
  [TaskStatus.START_DOWNLOAD]: 0,
});
const downloadTaskQueue = ref<DownloadFileTaskQueue>(new DownloadFileTaskQueue());
// 下载任务队列
const downloadTask = ref<string[]>([]);

const pollingTaskFunction = (delay = 2000) => {
  let immediate = true;
  let timer: NodeJS.Timeout | null = null;
  const returnFunc: PollingTaskFunction = function () {
    if (immediate) {
      immediate = false;
      getVideoClipsTaskStatus();
      return;
    }

    timer = setTimeout(() => {
      getVideoClipsTaskStatus();
    }, delay);
  };
  returnFunc.stop = () => {
    timer && clearTimeout(timer);
    pollingTask = null;
  };

  return returnFunc;
};

let pollingTask: PollingTaskFunction | null = pollingTaskFunction();

const onClose = () => {
  emit('close');
  reset();
};

const getVideoClipsTaskStatus = async () => {
  try {
    const params = props.videoClipsTask.map(item => item.task_id);
    const videoClipsTaskList = await getVideoClipStatus(params);
    handleClipsVideoTaskResponse(videoClipsTaskList);
  } catch (err) {}
};

const handleClipsVideoTaskResponse = (clipsVideoTask: GetVideoClipStatusResponse) => {
  const newVideoClipsTaskStatus: ClipsVideoTaskStatusMap = {
    [TaskStatus.PROCESSING]: 0,
    [TaskStatus.FINISH]: 0,
    [TaskStatus.WAITING]: 0,
    [TaskStatus.START_DOWNLOAD]: videoClipsTaskStatus.value[TaskStatus.START_DOWNLOAD],
  };
  clipsVideoTask.forEach((task) => {
    const { status, ext } = task;

    switch (status) {
      case TaskStatus.WAITING:
        newVideoClipsTaskStatus[TaskStatus.WAITING] += 1;
        break;
      case TaskStatus.PROCESSING:
        newVideoClipsTaskStatus[TaskStatus.PROCESSING] += 1;
        break;
      case TaskStatus.FINISH:
        newVideoClipsTaskStatus[TaskStatus.FINISH] += 1;
        ext && downloadVideo(task);
        break;
      default:
        break;
    }
  });
  videoClipsTaskStatus.value = {
    ...newVideoClipsTaskStatus,
    [TaskStatus.FINISH]: newVideoClipsTaskStatus.FINISH - newVideoClipsTaskStatus.START_DOWNLOAD,
  };
  const { FINISH, START_DOWNLOAD } = videoClipsTaskStatus.value;

  if (FINISH + START_DOWNLOAD !== props.videoClipsTask.length) {
    pollingTask?.();
    return;
  }
};
const downloadVideo = (task: GetVideoClipStatusTask) => {
  const { ext, taskId } = task;
  const videoName = props.videoClipsTask.find(item => item.task_id === taskId)?.video_name;
  try {
    if (ext?.FileUrl && ext?.FileType && videoName) {
      if (downloadTask.value.includes(taskId)) return;

      downloadTaskQueue.value.add(() => {
        const downloadUrl = `${ext?.FileUrl}?download_name=${encodeURIComponent(videoName)}.${ext?.FileType}`;
        useDownloadFile(autoSuitProto(downloadUrl), ext.MediaName, { isUrl: true });

        videoClipsTaskStatus.value = {
          ...videoClipsTaskStatus.value,
          [TaskStatus.FINISH]: videoClipsTaskStatus.value[TaskStatus.FINISH] - 1,
          [TaskStatus.START_DOWNLOAD]: videoClipsTaskStatus.value[TaskStatus.START_DOWNLOAD] + 1,
        };
      });

      downloadTask.value.push(taskId);
    } else {
      videoClipsTaskStatus.value = {
        ...videoClipsTaskStatus.value,
        [TaskStatus.FINISH]: videoClipsTaskStatus.value[TaskStatus.FINISH] - 1,
        [TaskStatus.START_DOWNLOAD]: videoClipsTaskStatus.value[TaskStatus.START_DOWNLOAD] + 1,
      };
      throw 'download url err';
    }
  } catch (e) {
    console.log(e as unknown as string);
  }
};

const reset = () => {
  videoClipsTaskStatus.value = {
    [TaskStatus.PROCESSING]: 0,
    [TaskStatus.FINISH]: 0,
    [TaskStatus.WAITING]: 0,
    [TaskStatus.START_DOWNLOAD]: 0,
  };
  downloadTaskQueue.value.clear();
  downloadTask.value = [];
  pollingTask?.stop?.();
  pollingTask = null;
};


watch(
  () => props.visible,
  () => {
    if (props.visible) {
      pollingTask = pollingTaskFunction();
      pollingTask();
    }
  },
);
</script>

<style lang="scss" scoped>
// :deep(.t-is-disabled) {
//   & path {
//     fill: aqua;
//   }
// }

:deep(.t-progress__inner) {
  transition: width 1s ease-in-out;
}
</style>
