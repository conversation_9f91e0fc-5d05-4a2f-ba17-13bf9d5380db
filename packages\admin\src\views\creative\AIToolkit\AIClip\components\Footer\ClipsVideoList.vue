<template>
  <div class="h-full w-full bg-gray pt-[10px]">
    <div
      v-auto-animate
      class="h-full flex flex-row justify-start gap-[16px] overflow-auto"
    >
      <ClipsVideoListItem
        v-for="(clipsVideo, index) in props.videoList"
        :key="index"
        :clips-video="clipsVideo"
        @click="() => onSelectClipsVideo(clipsVideo)"
      />
    </div>
  </div>
</template>
<script lang="tsx" setup>
import ClipsVideoListItem from './ClipsVideoListItem.vue';
import { useAIClipStore } from '@/store/creative/toolkit/ai_clips.store';
import { useDebounceFn } from '@vueuse/core';
import { ClipsVideo } from 'common/service/creative/aigc_toolkit/type';
interface IProps {
  videoList: ClipsVideo[];
}
const props = defineProps<IProps>();
const { setCurrentClipsVideo, showLoadClipsVideoLoading } = useAIClipStore();
const onSelectClipsVideo = (clipsVideo: ClipsVideo) => {
  showLoadClipsVideoLoading();
  UpdateCurrentClipsVideoDebounce({
    ...clipsVideo,
    video_name: '',
  });
};

const UpdateCurrentClipsVideoDebounce = useDebounceFn((clipsVideo: ClipsVideo) => {
  setCurrentClipsVideo({
    ...clipsVideo,
    video_name: '',
  });
}, 1000);
</script>

