<template>
  <div
    v-auto-animate
    class="h-full w-full flex flex-col bg-white overflow-auto"
  >
    <div class="flex justify-between">
      <div class="title font-semibold">Selected Clips</div>
      <div class="export_clips_video flex justify-end">
        <t-button
          theme="primary"
          :disabled="clipsVideosCount === 0"
          :content="exportClipsVideoBtnText"
          @click="exportClipsVideo"
        >
          <template #icon>
            <SvgIcon
              class="mr-[16px] fill-black"
              name="video-play"
              size="20"
            />
          </template>
        </t-button>
      </div>
    </div>
    <template v-if="clipsVideos.length">
      <div class="flex-1 bg-white w-full overflow-hidden mt-[10px] h-full">
        <ClipsVideoList :video-list="clipsVideos" />
      </div>
    </template>
    <template v-else>
      <div class="w-full h-full flex justify-center items-center text-black text-opacity-40 text-[14px]">
        Please select the clips you want to export from above.
      </div>
    </template>
    <ExportClipsVideoDialog
      :visible="dialogVisible"
      :video-clips-task="videoClipsTask"
      @close="closeDialog"
    />
  </div>
</template>
<script lang="ts" setup>
import { computed, provide, ref } from 'vue';
import { useAIClipStore } from '@/store/creative/toolkit/ai_clips.store';
import ClipsVideoList from './ClipsVideoList.vue';
import { storeToRefs } from 'pinia';
import { ClipsVideoTask, EmitVideoClip } from 'common/service/creative/aigc_toolkit';
import ExportClipsVideoDialog from '../Dialog/ExportClipsVideoDialog.vue';
import SvgIcon from 'common/components/SvgIcon';

const { clipsVideos } = storeToRefs(useAIClipStore());
const { setClipsVideos } = useAIClipStore();

const dialogVisible = ref(false);
const clipsVideosCount = computed(() => clipsVideos.value?.length ?? 0);
const exportClipsVideoBtnText = computed(() => {
  if (clipsVideosCount.value === 0) return 'Export Clips';
  return `Export ${clipsVideosCount.value} Clips`;
});
const videoClipsTask = ref<ClipsVideoTask[]>([]);

const exportClipsVideo = async () => {
  try {
    videoClipsTask.value = await EmitVideoClip({ video_list: clipsVideos.value });
    dialogVisible.value = true;
  } catch (error) {}
};

const deleteClipsVideo = (clipsVideo: any) => {
  clipsVideos.value = clipsVideos.value.filter(video => video !== clipsVideo);
  setClipsVideos(clipsVideos.value);
};

const closeDialog = () => {
  dialogVisible.value = false;
};
provide('deleteClipsVideo', deleteClipsVideo);
</script>

