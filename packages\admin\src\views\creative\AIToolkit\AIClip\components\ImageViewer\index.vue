<template>
  <t-image-viewer
    v-model="visible"
    :default-index="props.defaultIndex"
    :images="props.images"
  >
    <template #trigger>
      <div
        class="w-full h-full flex justify-center items-center overflow-hidden cursor-pointer"
        @click="show"
      >
        <img
          :alt="props.url"
          :src="props.url"
        >
      </div>
    </template>
  </t-image-viewer>
</template>
<script setup lang="ts">
import { useVisible } from 'common/compose/useVisible';

interface IProps {
  defaultIndex: number;
  images: string[];
  url: string;
}

const { visible, show } = useVisible(false);
const props = defineProps<IProps>();
</script>

<style lang="scss" scoped></style>
