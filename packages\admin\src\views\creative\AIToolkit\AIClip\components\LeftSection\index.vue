<template>
  <div class="h-full flex flex-col w-full">
    <t-form
      ref="formRef"
      label-align="top"
      :data="formData"
      :rules="rules"
      @submit="onSubmit"
    >
      <t-form-item
        v-if="selectGameVisible"
        name="gameCode"
        label="Select game"
        label-width="120px"
        label-align="left"
        class="text-base font-semibold"
      >
        <t-select
          v-model="formData.gameCode"
          clearable
        >
          <t-option
            v-for="item in gameOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <div class="flex flex-row w-full gap-2 h-[48px]">
              <div class="aspect-square h-full p-2">
                <t-image
                  :alt="`${item.value}.png`"
                  fit="cover"
                  shape="round"
                  :lazy="true"
                  :src="item.image"
                >
                  <template #loading>
                    <img :src="GameIcon">
                  </template>
                </t-image>
              </div>
              <div class="grow flex items-center">{{ item.label }}</div>
            </div>
          </t-option>
          <template #valueDisplay>
            <div class="w-full h-full py-1 flex flex-row gap-2">
              <div
                v-if="curSelectedGame"
                class="aspect-square h-full"
              >
                <t-image
                  fit="cover"
                  shape="round"
                  :src="curSelectedGame.image"
                />
              </div>
              <div class="grow flex items-center">
                <template v-if="curSelectedGame?.value">
                  <span>{{ curSelectedGame.label }}</span>
                </template>
                <template v-else>
                  <span class="text-black-placeholder">Please select game.</span>
                </template>
              </div>
              <div />
            </div>
          </template>
        </t-select>
      </t-form-item>
      <t-form-item
        class="bg-white rounded-default mb-[16px]"
        name="text"
      >
        <Textarea
          v-model="formData.text"
          :maxlength="3000"
          placeholder="Please enter text"
        />
        <template #label>
          <div class="flex flex-row gap-2 items-center">
            <div class="text-black-primary text-base font-semibold">Search by text prompt</div>
            <div class="cursor-pointer">
              <t-popup
                placement="bottom-left"
                show-arrow
                destroy-on-close
              >
                <SvgIcon
                  name="error"
                  size="16"
                />
                <template #content>
                  <ul class="list-disc max-w-[400px] ml-4 my-2 mr-1">
                    <li class="break-keep">
                      You can use text to describe what you want to search in video content, such as "black ball
                      entering pocket" or "ultimate shot!".
                    </li>
                    <li class="break-keep">
                      The system will return clips that match the closest with the text, therefore there will always be
                      some returned clips even if you search for something that are not in the database (e.g. Ethel the
                      aardvark goes quantity surveying).
                    </li>
                  </ul>
                </template>
              </t-popup>
            </div>
          </div>
        </template>
      </t-form-item>
      <div class="grid grid-cols-2">
        <t-form-item name="image">
          <Upload
            v-model="formData.image"
            :game="(gameCode as string)"
          />
          <template #label>
            <div class="flex flex-row gap-2 items-center">
              <div class="text-black-primary text-base font-semibold">Search by image/video</div>
              <div class="cursor-pointer">
                <t-popup
                  content="Please upload an image/video to search clips with similar content."
                  placement="top-left"
                  show-arrow
                  destroy-on-close
                >
                  <SvgIcon
                    name="error"
                    size="16"
                  />
                </t-popup>
              </div>
            </div>
          </template>
        </t-form-item>
        <t-form-item class="flex justify-end">
          <t-button
            class="mt-8"
            content="Search Clips"
            :loading="sidebarLoading"
            type="submit"
          />
        </t-form-item>
      </div>
    </t-form>

    <div class="w-full flex flex-1 flex-col overflow-hidden">
      <div class="label font-semibold text-[14px]">Select & Edit Video Clips</div>

      <div class="relative h-full w-full mt-[16px] overflow-hidden">
        <div
          v-show="sidebarLoading"
          class="flex h-full w-full justify-center items-center absolute z-10 bg-black bg-opacity-60 rounded-default"
        >
          <t-loading size="20px" />
        </div>
        <template v-if="videoList.length">
          <Video-List
            ref="videoListRef"
            v-infinite-scroll="onLoadMore"
            :video-list="videoList"
            :can-load-more="canLoadMore"
          />
        </template>
        <template v-else-if="!videoList.length && !sidebarLoading">
          <div class="flex justify-center items-center h-full w-full">
            <template v-if="serverError">
              The reason for this can be<br>
              - EITHER No videos matched with your text query<br>
              - OR Your text query is unclear, please follow the instructions given above for text prompt
              <br>
            </template>
            <template v-else>Not Data</template>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Textarea from 'common/components/Textarea';
import VideoList from '../VideoList/VideoList.vue';
import { computed, provide, reactive, ref } from 'vue';
import { Form, FormRules, SubmitContext } from 'tdesign-vue-next';
import { getVideoByText } from 'common/service/creative/aigc_toolkit';
import { useAIClipStore } from '@/store/creative/toolkit/ai_clips.store';
import { storeToRefs } from 'pinia';
import { vInfiniteScroll } from '@vueuse/components';
import { sleep } from 'common/utils/common';
import { useTips } from 'common/compose/tips';
import Upload from '../Upload/Upload.vue';
import { useRouteQuery } from '@vueuse/router';
import { SvgIcon } from 'common/components/SvgIcon';
import { GAME_OPTIONS, GAME_MC_OPTIONS, CND_HOST } from '../../constant/index';
import { Video } from 'common/service/creative/aigc_toolkit/type';
import GameIcon from '@/assets/svg/game-icon_default.svg';

const { sidebarLoading, offset } = storeToRefs(useAIClipStore());
const { showSidebarLoading, hideSidebarLoading } = useAIClipStore();
const { setOffset } = useAIClipStore();
const { err } = useTips();

const gameCode = useRouteQuery<string>('game');

const formRef = ref<(InstanceType<typeof Form> & HTMLFormElement) | null>(null);
const formData = reactive({
  text: '',
  image: [] as string[],
  mode: 1,
  gameCode: '',
});

const selectGameVisible = computed(() => ['demo_mobile', 'mc_demo'].includes(gameCode.value));
const gameOptions = computed(() => {
  if (gameCode.value === 'demo_mobile') return GAME_OPTIONS;
  if (gameCode.value === 'mc_demo') return GAME_MC_OPTIONS;
  return [];
});
const curSelectedGame = computed(() => gameOptions.value.find(item => item.value === formData.gameCode));
const pageSize = ref(50);

const videoListRef = ref<InstanceType<typeof VideoList>>();
const canLoadMore = ref(true);
const selectText = ref('');
const videoList = ref<Video[]>([]);

const rules: FormRules<typeof formData> = {
  text: [
    {
      validator: () => {
        if (!!formData.image.length || formData.text !== '') {
          return true;
        }
        return {
          message: 'Please enter a prompt or upload an image/video to enable video search.',
          result: false,
          type: 'error',
        };
      },
    },
  ],
  gameCode: [
    {
      required: true,
    },
  ],
};

const onSubmit = async ({ validateResult, e }: SubmitContext<typeof formData>) => {
  e?.preventDefault();
  if (validateResult === true) {
    setOffset(0);
    videoList.value = [];
    selectText.value = formData.text;
    try {
      showSidebarLoading();
      const newVideoList = await getData();
      if (newVideoList.length < pageSize.value) {
        canLoadMore.value = false;
      } else {
        canLoadMore.value = true;
        setOffset(offset.value + pageSize.value);
      }
      videoList.value = [...newVideoList];
    } catch (e) {
      err((e as unknown as Error).message);
    } finally {
      hideSidebarLoading();
    }
  }

  return;
};

const onLoadMore = async () => {
  if (canLoadMore.value === false) return;

  const startTime = new Date().getTime();
  if (!selectText.value && !formData.image[0]) return;
  try {
    videoListRef.value?.showLoading();
    const newVideoList = await getData();
    if (newVideoList.length < pageSize.value) {
      canLoadMore.value = false;
    }

    const endTime = new Date().getTime();
    const time = endTime - startTime;
    time < 1000 && (await sleep(1000 - time));

    videoList.value = [...videoList.value, ...newVideoList];
    setOffset(offset.value + pageSize.value);
  } catch {
  } finally {
    videoListRef.value?.hideLoading();
  }
};

const serverError = ref(false);
const getData = async () => {
  const res = await getVideoByText({
    text: selectText.value || '',
    limit: pageSize.value,
    offset: offset.value,
    image: formData.image[0] || '',
    mode: formData.mode,
    ...(!!formData.gameCode && selectGameVisible
      ? { game_code: formData.gameCode } : {}),
  }).catch(() => ({
    list: [],
    code: 1, // 超时或者其他后台错误
  }));
  const { list, code } = res;
  serverError.value = code === 1;
  if (code === 1) {
    return [];
  }
  return list.map((i) => {
    const urlObj = new URL(i.video_url);
    const coverObj = new URL(i.cover_url);
    urlObj.host = CND_HOST;
    coverObj.host = CND_HOST;
    return { ...i, video_url: urlObj.toString(), cover_url: coverObj.toString() };
  });
};
provide('onLoadMore', onLoadMore);
</script>
