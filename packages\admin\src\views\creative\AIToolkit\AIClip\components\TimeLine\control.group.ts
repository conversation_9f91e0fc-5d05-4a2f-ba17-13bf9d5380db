
import Konva from 'konva';
import { HANDLE_WIDTH, MASK_LINE_WIDTH, STAGE_PADDING_X, TimeLineStoreKey } from './constant';
import { TimeLineEvent } from './stage';
import { TimeLine, TimeLineGroup } from './types.d';
import { FilmTripGroup } from './filmtrip.group';
import { proxyDragBoundFunc } from './util/group';

export class ControlGroup extends TimeLineGroup {
  private filmTripGroup!: FilmTripGroup;
  private triangleCursor!: Konva.Line;

  constructor(config: TimeLine.GroupConfig) {
    super(config);
    this.filmTripGroup = this.cacheMap?.getKonvaNode(TimeLineStoreKey.FilmTrip);
    this.init();
  }

  init() {
    this.drawShape();
    this.initListeners();
    this.draggable(true);
    this.dragBoundFunc(proxyDragBoundFunc.call(
      this,
      STAGE_PADDING_X,
      this.filmTripGroup.contentRectRightBound - HANDLE_WIDTH + STAGE_PADDING_X,
      () => {
        const stage: Konva.Stage = this.cacheMap?.getKonvaNode(TimeLineStoreKey.TimeLineStage);
        stage.fire(TimeLineEvent.CONTROL_CHANGE);
      },
    ));
  }

  drawShape() {
    this.triangleCursor = new Konva.Line({
      x: 12,
      y: 0,
      points: [0, 0, 8, 0, 8, 16, 4, 20, 0, 16],
      fill: '#00D2FF',
      stroke: 'black',
      strokeWidth: 1,
      closed: true,
    });
    const verticalLineByTriangle = new Konva.Line({
      points: [16, 16, 16, 100],
      stroke: 'red',
      strokeWidth: MASK_LINE_WIDTH,
      lineJoin: 'round',
      dash: [10, 5],
    });

    this.add(this.triangleCursor, verticalLineByTriangle);
  }

  initListeners() {
    this.triangleCursor.on('mouseover', () => {
      document.body.style.cursor = 'pointer';
    });
    this.triangleCursor.on('mouseout', () => {
      document.body.style.cursor = 'default';
    });
  }
}
