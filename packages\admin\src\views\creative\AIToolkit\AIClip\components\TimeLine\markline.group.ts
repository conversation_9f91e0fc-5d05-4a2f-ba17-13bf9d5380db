import Konva from 'konva';
import { MASK_LINE_WIDTH } from './constant';
import { useTimeLineConfigStore } from './store';


export class MarkLineGroup extends Konva.Group {
  private readonly lineHeight = 5;
  private readonly ruleHeight = 2 * this.lineHeight;
  private readonly markerHeight = 15;
  private config = useTimeLineConfigStore().getConfig();

  constructor(config: Konva.GroupConfig) {
    super(config);
    this.init();
  }


  init() {
    this.drawShape();
  }

  drawShape() {
    const rulerLine = new Konva.Line({
      x: 0,
      y: this.ruleHeight,
      points: [0, this.ruleHeight, this.width(), this.ruleHeight],
      stroke: 'white',
      strokeWidth: MASK_LINE_WIDTH,
      lineCap: 'round',
      lineJoin: 'round',
    });

    const markerGroup = new Konva.Group({
      x: 0,
      y: this.markerHeight,
    });

    const interval = this.config.scale! * this.config.scaleInterval!;
    for (let i = 0; i <= this.width(); i += 10) {
      if (i % 100 === 0) {
        const numberText = new Konva.Text({
          x: i,
          y: -14,
          text: (i / interval).toFixed(1),
          fontSize: 10,
          fill: 'white',
        });
        numberText.offsetX(numberText.width() / 2);
        markerGroup.add(numberText);
      }
      const marker = new Konva.Line({
        points: [i, this.ruleHeight, i, i % 100 === 0 ? 0 : this.ruleHeight / 2],
        stroke: 'white',
        strokeWidth: MASK_LINE_WIDTH,
        lineCap: 'round',
        lineJoin: 'round',
      });
      markerGroup.add(marker);
    }

    this.add(rulerLine);
    this.add(markerGroup);
  }
}
