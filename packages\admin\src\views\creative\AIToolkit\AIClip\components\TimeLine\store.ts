
enum StoreUniqueKey {
  Konva = 'KONVA',
  TimeLineConfig = 'TIMELINE_CONFIG'
}

export function defineStore<T>(key: string, initialState: (...arg: any[]) => T extends Object ? T : never) {
  const storeMap = new Map<string, T>();
  if (storeMap.has(key)) {
    return () => storeMap.get(key)!;
  }
  const result: T = initialState();
  storeMap.set(key, result);
  return () => result;
}


export const useKonvaStore = defineStore(StoreUniqueKey.Konva, () => {
  const konvaCache = new Map<string, any>();

  const getKonvaNode = (key: string) => konvaCache.get(key);
  const setKonvaNode = (key: string, value: any) => {
    konvaCache.set(key, value);
  };
  return {
    konvaCache,
    getKonvaNode,
    setKonvaNode,
  };
});

export type TimeLineConfig = {
  // 缩放比例
  scale?: number
  // markLine 刻度间隔
  scaleInterval?: number,
  // 视频剪裁开始位置
  startTime?: number,
  // 视频剪裁结束位置
  endTime?: number,
  // 视频时长
  duration?: number,
  // canvas 宿主
  container: HTMLDivElement | string
  // video 宿主
  video?: HTMLVideoElement | string
  // 视频选区高度
  selectionHeight?: number
  // 时间轴高度
  markLineHeight?: number
  // 控制条比例: video time : control length
  // controlProportion?: number,

};

export const useTimeLineConfigStore = defineStore(StoreUniqueKey.TimeLineConfig, () => {
  const config = {
    startTime: 0,
    endTime: 0,
    scale: 1,
    scaleInterval: 5,
    duration: 0,
  } as unknown as TimeLineConfig;

  const setConfig = (newConfig: Partial<TimeLineConfig>) => {
    returnConfig.config = {
      ...config,
      ...newConfig,
    };

    return returnConfig.config;
  };

  const getConfig = () => returnConfig.config;
  const returnConfig = {
    config,
    setConfig,
    getConfig,
  };
  return returnConfig;
});
