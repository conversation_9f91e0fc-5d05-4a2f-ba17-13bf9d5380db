// type EventHandler<T> = {
//     callback: (res: { data: Record<string, any> }) => void,
//     type: T
// }

import { TimeLineEvent } from '../stage';
import { TimeLineConfig } from '../store';

export type EventCallback = (...args: any[]) => void;

export class BaseStage {
  readonly container: HTMLDivElement | null;


  private eventMap = new Map<TimeLineEvent, EventCallback[]>();

  constructor(config: Partial<TimeLineConfig>) {
    const { container } = config;
    if (!container) {
      throw new Error('container is not defined');
    }
    this.container = typeof container === 'string' ? document.getElementById(container) as HTMLDivElement : container;
  }


  on(eventName: TimeLineEvent, cb: EventCallback) {
    const events = this.eventMap.get(eventName);
    if (events) {
      events.push(cb);
    } else {
      this.eventMap.set(eventName, [cb]);
    }
  }

  once(eventName: TimeLineEvent, cb: EventCallback) {
    return new Promise((resolve) => {
      this.on(eventName, cb);
      resolve(true);
    }).then(() => {
      this.off(eventName, cb);
    });
  }

  off(eventName: TimeLineEvent, cb: EventCallback) {
    const events = this.eventMap.get(eventName);
    const newEvents = events?.filter(fn => cb !== fn) || [];
    this.eventMap.set(eventName, newEvents!);
  }

  emit(eventName: TimeLineEvent, payload?: Record<string, any>) {
    const events = this.eventMap.get(eventName);
    if (events) {
      events.forEach((cb) => {
        cb(payload);
      });
    }
  }
}
