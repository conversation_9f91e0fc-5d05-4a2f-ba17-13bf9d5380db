
import { fileToUrl } from './file';
import { useKonvaStore } from '../store';
import { TimeLineStoreKey } from '../constant';
import { TimeLineEvent } from '../stage';
type CallbackFuncTyper = (thumbnail: string, index: number) => void;

type VideoThumbnailsConfig = {
  width: number, // 容器的宽度
  height: number, // 容器的高度
  thumbnailNum: number, // 切片的数量
  videoFile: File | string, // 视频源
  videoInstance?: HTMLVideoElement, // 是否公用instance
};

/**
 * 视频缩略图生成类
*/
export class FilmTrip {
  public duration = 0;

  private readonly width: number;
  private readonly height: number;
  private readonly videoUrl: string;
  private readonly thumbnailNum: number;
  private readonly videoInstance: HTMLVideoElement;
  private canvasInstance: HTMLCanvasElement;
  private konvaStore = useKonvaStore();
  constructor({ width, height, thumbnailNum, videoFile, videoInstance }: VideoThumbnailsConfig) {
    this.width = width;
    this.height = height;
    this.thumbnailNum = thumbnailNum;
    this.videoUrl = this.formatFile(videoFile);
    this.videoInstance = videoInstance ?? document.createElement('video');
    this.canvasInstance = document.createElement('canvas');
    this.canvasInstance.width = Math.floor(this.width / this.thumbnailNum);
    this.canvasInstance.height = this.height;
    this.videoInstance.addEventListener('error', (ex) => {
      this.konvaStore.getKonvaNode(TimeLineStoreKey.TimeLine).emit(TimeLineEvent.LOAD_VIDEO_ERROR);
      throw new Error(`error when loading video file ${ex}`);
    });
    this.initListeners();
  }

  initListeners() {

  }

  formatFile(file: File | string) {
    if (typeof file === 'string') {
      return file;
    }
    if (file?.type?.match('video')) {
      return fileToUrl(file as File);
    }
    throw new TypeError('File type err， only support video file！');
  }

  /**
   * 生成略缩图
   * @param cb
   */
  async generateVideoThumbnails(cb: CallbackFuncTyper) {
    this.duration = await this.getVideoDuration();
    const timeGap = this.duration / this.thumbnailNum;
    const fractions = new Array(this.thumbnailNum + 1).fill(0)
      .map((_, i) => i * timeGap);

    // extract video thumbnail once seeking is complete
    this.videoInstance.addEventListener('seeked', () => {
      cb && this.catchVideoImageToBase64().then((res) => {
        cb(res, Math.floor(this.videoInstance.currentTime / timeGap) * this.canvasInstance.width);
      });
    });
    for (const [index, time] of fractions.entries()) {
      await this.seekTo(index >= fractions.length - 1 ? time - 2 : time);
    }
  }

  catchVideoImageToBase64(): Promise<string> {
    return new Promise((resolve) => {
      // define a canvas to have the same dimension as the video
      // draw the video frame to canvas
      const ctx = this.canvasInstance.getContext('2d');
      ctx!.drawImage(
        this.videoInstance, 0, 0, this.videoInstance.videoWidth,
        this.videoInstance.videoHeight, 0, 0, this.canvasInstance.width, this.canvasInstance.height,
      );
      // return the canvas image as a blob
      // then convert it to base 64
      ctx!.canvas.toBlob(
        (blob) => {
          const reader = new FileReader();
          reader.readAsDataURL(blob as Blob);
          reader.onloadend = function () {
            resolve(reader.result as string);
          };
        },
        'image/jpeg',
        1, /* quality */
      );
    });
  }


  seekTo(time: number) {
    return new Promise((resolve, reject) => {
      if (this.videoInstance.duration < time) {
        reject('video is too short.');
        return;
      }
      setTimeout(() => {
        console.log('here', time);
        this.videoInstance.currentTime = time;
        resolve('');
      }, 200);
    });
  }


  // generate the video duration either via url
  getVideoDuration(): Promise<number> {
    return new Promise((resolve) => {
      this.videoInstance.addEventListener('loadeddata', () => {
        resolve(this.videoInstance.duration);
      });
      this.videoInstance.preload = 'metadata';
      this.videoInstance.src = this.videoUrl;
      // Load video in Safari / IE11
      this.videoInstance.muted = true;
      this.videoInstance.crossOrigin = 'Anonymous';
      this.videoInstance.playsInline = true;
      // this.videoInstance.play().then();
    });
  }
}
