import Konva from 'konva';
import { Vector2d } from 'konva/lib/types';

export function proxyDragBoundFunc(this: Konva.Group, leftBound: number, rightBound:
number, callback?: (pos: Vector2d, code?: DragBoundErrorMessageCode) => void) {
  console.log(leftBound, rightBound);

  return (pos: Vector2d): Vector2d => {
    let { x } = pos;
    try {
      if (x < leftBound) {
        throw new DragBoundErrorMessage({
          message: 'x overflow leftBound',
          code: DragBoundErrorMessageCode.X_LESS_THAN_ZERO,
        });
      }
      if (x > rightBound) {
        throw new DragBoundErrorMessage({
          message: 'x overflow rightBound',
          code: DragBoundErrorMessageCode.X_GREATER_THAN_CONTENT_WIDTH,
        });
      }

      callback?.(pos);
    } catch (e) {
      if (e instanceof DragBoundErrorMessage) {
        console.error(e.message);
        switch (e.code) {
          case DragBoundErrorMessageCode.X_LESS_THAN_ZERO:
            x = leftBound;
            break;
          case DragBoundErrorMessageCode.X_GREATER_THAN_CONTENT_WIDTH:
            x = rightBound;
            break;
          default: break;
        }
        const res: Vector2d = {
          x,
          y: this.absolutePosition().y,
        };
        callback?.(res, e.code);
      } else {
        console.error(e);
      }
    }
    return {
      x,
      y: this.absolutePosition().y,
    };
  };
}

enum DragBoundErrorMessageCode {
  'X_LESS_THAN_ZERO',
  'X_GREATER_THAN_CONTENT_WIDTH'
}

interface DragBoundErrorMessageOption {
  message: string
  code: DragBoundErrorMessageCode
}
class DragBoundErrorMessage extends Error {
  public code: DragBoundErrorMessageCode;
  constructor(config: DragBoundErrorMessageOption) {
    super(config.message);
    this.code = config.code;
  }
}

