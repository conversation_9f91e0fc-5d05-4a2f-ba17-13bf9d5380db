<template>
  <t-upload
    ref="uploadRef"
    v-model="files"
    theme="file"
    accept="image/*,video/*"
    tips="Only support video or image"
    :use-mock-progress="true"
    :mock-progress-duration="300"
    :max="0"
    :multiple="true"
    :show-upload-progress="true"
    :request-method="requestMethod"
    :before-upload="onBeforeUpload"
    @change="handleChange"
  />
</template>
<script setup lang="ts">
import { Upload, RequestMethodResponse, UploadFile as TdUploadFile, MessagePlugin } from 'tdesign-vue-next';
import { ref, PropType } from 'vue';
import { UploadFile } from 'common/components/FileUpload';
import { v4 as uuidv4 } from 'uuid';
import { upload } from 'common/components/FileUpload/util';

const props = defineProps({
  server: {
    // 请求接口，默认走aix默认域名，
    type: String as PropType<'aix' | 'site'>,
    default: 'aix',
    required: false,
  },
  game: {
    // 游戏game_code
    type: String,
    default: '',
    required: false,
  },
  modelValue: {
    type: Array as PropType<Array<string>>,
    default: () => [],
  },
});

const uploadRef = ref<InstanceType<typeof Upload>>();
const files = ref<TdUploadFile>([]);

const emits = defineEmits(['update:modelValue']);

const requestMethod = async (files: TdUploadFile[]): Promise<RequestMethodResponse> => new Promise((resolve) => {
  const [file] = files;
  const newRawFile = file.raw as UploadFile;

  newRawFile.id = uuidv4();
  try {
    upload({
      file: newRawFile,
      // game_code: props.game,
      // path: `${props.game}/advanced_video_search/${newRawFile.id}`,
      path: `advanced_video_search/${newRawFile.id}`,
      type: props.server,
    }).then((res) => {
      if (!res.code) {
        MessagePlugin.success('Upload success!');
        resolve({
          status: 'success',
          response: res,
        });
      } else {
        resolve({ status: 'fail', error: 'upload error!', response: {} });
      }
    });
  } catch {
    MessagePlugin.error('Upload error!');
    resolve({ status: 'fail', error: 'upload error!', response: {} });
  } finally {
  }
});

const onBeforeUpload = async (file: TdUploadFile) => {
  const isAllowType = checkFileType(['video', 'image'], file);

  if (!isAllowType) {
    MessagePlugin.error('Please upload a image or video');
    return false;
  }

  return true;
};

const checkFileType = (allowTypes: string[], file: TdUploadFile) => {
  const fileType = file.type;
  return allowTypes.some(type => fileType?.includes(type)) || false;
};

const handleChange = (files: TdUploadFile[]) => {
  const urls = files.map(file => file.url);
  emits('update:modelValue', urls);
};
</script>

<style lang="scss" scoped></style>
