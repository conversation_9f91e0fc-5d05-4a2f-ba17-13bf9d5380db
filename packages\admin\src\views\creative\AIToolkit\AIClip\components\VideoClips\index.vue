<template>
  <div
    v-auto-animate
    class="w-full aspect-[5/4] flex flex-col justify-center items-center bg-black rounded-default relative"
  >
    <div
      ref="clipsVideoRef"
      class="w-full h-full overflow-hidden rounded-lg"
    />
    <div
      class="absolute right-4"
      :style="selectBtnStyle"
    >
      <t-button
        content="Select"
        @click="selectClipsVideo"
      />
    </div>
    <div
      v-if="loadClipsVideoLoading"
      class="absolute h-full w-full z-10 flex justify-center items-center bg-black bg-opacity-60 rounded-default"
    >
      <t-loading
        v-show="videoLoadingStatus === 'active'"
        size="small"
        text="Loading..."
      />
      <div
        v-show="videoLoadingStatus === 'error'"
        class="text-error-primary"
      >
        Sorry, the video failed to load.
      </div>
    </div>
  </div>
</template>
<script lang="tsx" setup>
import { ref, watch, onMounted, computed, CSSProperties } from 'vue';
import { useAIClipStore } from '@/store/creative/toolkit/ai_clips.store';
import { storeToRefs } from 'pinia';
import { ClipVideo } from '../ClipsVideo/index';
import { useWindowSize, watchDebounced, useElementBounding } from '@vueuse/core';
import { ClipsVideo } from 'common/service/creative/aigc_toolkit/type';


type LoadingStatus = 'active' | 'success' | 'error';
const { currentClipsVideo, clipsVideos, loadClipsVideoLoading } = storeToRefs(useAIClipStore());
const { setClipsVideos, hideLoadClipsVideoLoading } = useAIClipStore();
const { width, height } = useWindowSize();

const clipsVideoRef = ref<HTMLDivElement>();
const videoClip = ref<InstanceType<typeof ClipVideo>>();

const videoLoadingStatus = ref<LoadingStatus>('active');
const { width: containerWidth } = useElementBounding(clipsVideoRef);
const containerHeight = ref<number | undefined>();
const selectBtnStyle = computed<CSSProperties>(() => (!!containerHeight.value
  ? {
    top: `${containerHeight.value}px`,
  }
  : {}));

const selectClipsVideo = () => {
  if (currentClipsVideo.value && videoClip.value) {
    const { startTime = 0, endTime = 0 } = videoClip.value.getTime();
    const clipsVideo: ClipsVideo = {
      ...currentClipsVideo.value,
      start_time: startTime,
      end_time: endTime,
    };
    setClipsVideos([...clipsVideos.value, clipsVideo]);
  }
};

const loadVideo = () => {
  console.log('loading video');

  if (currentClipsVideo.value) {
    const {
      start_time: startTime,
      end_time: endTime,
      video_url: videoUrl,
      cover_url: coverUrl,
    } = currentClipsVideo.value;
    videoLoadingStatus.value = 'active';
    videoClip.value?.load({
      src: videoUrl,
      startTime,
      endTime,
      poster: coverUrl,
    });
  }
};

const initClipsVideo = () => {
  videoClip.value = new ClipVideo({
    container: clipsVideoRef.value!,
  });
  videoClip.value.on('loaded', () => {
    hideLoadClipsVideoLoading();
  });

  videoClip.value.on('video-error', () => {
    videoLoadingStatus.value = 'error';
  });
};

watch(currentClipsVideo, loadVideo);
watch(containerWidth, (width?: number) => {
  console.log(width);

  containerHeight.value = ((width ?? 0) / (16 / 9)) * 1.05;
});

watchDebounced(
  [width, height],
  async () => {
    initClipsVideo();
    setTimeout(() => {
      loadVideo();
    });
  },
  { debounce: 500, maxWait: 1000 },
);

onMounted(() => {
  initClipsVideo();
});
</script>
