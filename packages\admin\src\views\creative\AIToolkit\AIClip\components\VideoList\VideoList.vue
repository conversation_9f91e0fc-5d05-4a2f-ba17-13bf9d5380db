<template>
  <div
    class="flex flex-col overflow-y-scroll h-full"
    style="contain: strict"
  >
    <div
      :style="videoListStyle"
      class="grid gap-[16px] grow justify-between"
    >
      <Video-List-Item
        v-for="(video, index) in props.videoList"
        :key="video.video_name + index"
        :video="video"
        @click="() => onClick(video)"
      />
    </div>

    <div
      v-show="props.canLoadMore"
      class="flex justify-center items-center w-full leading-[20px] z-10 bg-white pt-[16px]"
    >
      <t-loading
        text="Loading more..."
        size="small"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import VideoListItem from './VideoListItem.vue';
import { useAIClipStore } from '@/store/creative/toolkit/ai_clips.store';
import { useLoading } from 'common/compose/loading';
import { useDebounceFn } from '@vueuse/core';
import { ref, provide } from 'vue';
import videojs from 'video.js';
import { ClipsVideo, Video } from 'common/service/creative/aigc_toolkit/type';
interface IProps {
  videoList: Video[];
  canLoadMore: boolean;
}

const props = defineProps<IProps>();
const { setCurrentClipsVideo, showLoadClipsVideoLoading, showSidebar } = useAIClipStore();

const videoListStyle = ref({
  gridTemplateColumns: `repeat(auto-fit, ${window.screen.width > 2000 ? 250 : 220}px)`,
});

const curHoverPlayVideo = ref<videojs.Player | undefined>(undefined);

const { showLoading, hideLoading } = useLoading(false);
const onClick = (video: Video) => {
  const clipsVideo = {
    ...video,
    origin_video_name: video.video_name,
    video_name: '',
  };
  showSidebar();
  showLoadClipsVideoLoading();
  UpdateCurrentClipsVideoDebounce(clipsVideo);
};

const UpdateCurrentClipsVideoDebounce = useDebounceFn((clipsVideo: ClipsVideo) => {
  setCurrentClipsVideo(clipsVideo);
}, 1000);

const setCurHoverPlayVideo = (video?: videojs.Player) => {
  curHoverPlayVideo.value = video;
};

provide('curHoverPlayVideo', curHoverPlayVideo);
provide('setCurHoverPlayVideo', setCurHoverPlayVideo);
defineExpose({
  showLoading,
  hideLoading,
});
</script>

