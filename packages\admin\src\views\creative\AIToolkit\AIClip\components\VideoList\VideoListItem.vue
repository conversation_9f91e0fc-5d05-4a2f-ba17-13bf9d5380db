<template>
  <div
    class="border-[1px] border-[#D8D7DA] aspect-video border-solid rounded-default
    cursor-pointer relative box-content overflow-hidden"
  >
    <div
      class="absolute z-10 flex justify-center items-center bg-[#1E222A] bg-opacity-60
      font-bold w-[50px] h-[20px] left-[4px] top-[4px] rounded-default text-[12px] text-white opacity-60"
    >
      {{ duration }}
    </div>
    <div
      class="flex h-full w-full"
      @mouseenter="onMouseEnter"
      @mouseleave="onMouseLEave"
    >
      <Image
        :url="props.video.cover_url"
        class="h-full w-full"
      />
      <transition name="fade">
        <div
          v-show="visible"
          class="w-full h-full absolute z-10 rounded-default bg-black"
        >
          <VideoPlay
            ref="videoPlayer"
            :video="props.video"
          />
        </div>
      </transition>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, ref, onUnmounted, inject, Ref } from 'vue';
import Image from '../Image/index.vue';
import VideoPlay from './Video.vue';
import videojs from 'video.js';
import { useVisible } from 'common/compose/useVisible';
import { Video } from 'common/service/creative/aigc_toolkit/type';
interface IProps {
  video: Video;
}
const props = defineProps<IProps>();

const curHoverPlayVideo = inject<Ref<videojs.Player | undefined>>('curHoverPlayVideo');
const setCurHoverPlayVideo = inject<(video?: videojs.Player) => void>('setCurHoverPlayVideo');

const duration = computed(() => formatVideoTime(props.video.end_time - props.video.start_time));
const { visible, show: showVideo, hide: hideVideo } = useVisible(false);
const timer = ref<NodeJS.Timeout>();
const videoPlayer = ref<InstanceType<typeof VideoPlay>>();
const formatVideoTime = (time: number): string => {
  if (time > 3600) {
    throw new Error('Videos longer than 1 hour are currently not supported');
  }
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  const milliseconds = Number(Number(`${time}`.replace(/\d+(?=.)/, '0')).toFixed(1)) * 10;
  return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}.${milliseconds}`;
};

const onMouseEnter = () => {
  curHoverPlayVideo?.value?.pause();
  timer.value = setTimeout(() => {
    showVideo();
    setCurHoverPlayVideo?.(videoPlayer.value?.player);
    videoPlayer.value?.play();
  }, 500);
};

const onMouseLEave = () => {
  timer.value && clearTimeout(timer.value);
  videoPlayer.value?.player?.pause();
  setCurHoverPlayVideo?.();
  hideVideo();
};

onUnmounted(() => {
  timer.value && clearTimeout(timer.value);
});
</script>

<style>
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
