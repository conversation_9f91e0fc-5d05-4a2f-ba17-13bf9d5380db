import { TAlToolkit, EAlToolkit } from '../types/index.d';

export const CND_HOST = 'ua-cdn.intlgame.com';

export const AL_TOOLKIT: TAlToolkit[] = [
  {
    name: EAlToolkit['Text to Al Clips'],
    path: '/creative/toolkit/al/clips',
    image: '',
  },
  {
    name: EAlToolkit['Al Templates'],
    path: '/creative/toolkit/al/templates',
    image: '',
  },
  {
    name: EAlToolkit['Text to Al Video'],
    path: '/creative/toolkit/al/video',
    image: '',
  },
];

const IMAGE_BASE_URL = 'https://static.aix.intlgame.com/gameicon';
export const GAME_OPTIONS: Array<{
  image: string;
  label: string;
  value: string;
}> = [
  {
    image: `${IMAGE_BASE_URL}/splash_damage/dirty_bomb.jpg`,
    label: 'Dirty Bomb',
    value: 'dirty_bomb',
  },
  {
    image: `${IMAGE_BASE_URL}}/splash_damage/gears5.jpg`,
    label: 'Gears 5',
    value: 'gears5',
  },
  {
    image: `${IMAGE_BASE_URL}}/splash_damage/gears_tactics.jpg`,
    label: 'Gears Tactics',
    value: 'gears_tactics',
  },
  {
    image: `${IMAGE_BASE_URL}}/splash_damage/transformers_reactivate.png`,
    label: 'Transformers Reactivate',
    value: 'transformers_reactivate',
  },
];

// mc_demo游戏展示的下拉列表
export const GAME_MC_OPTIONS = [
  {
    image: `${IMAGE_BASE_URL}/miniclip/baseball_clash.png`,
    label: 'Baseball Clash',
    value: 'baseball_clash',
  },
  {
    image: `${IMAGE_BASE_URL}/miniclip/8_ball_pool.png`,
    label: '8 ball pool',
    value: '8_ball_pool',
  },
  {
    image: `${IMAGE_BASE_URL}/miniclip/ultimate_golf.png`,
    label: 'Ultimate Golf',
    value: 'ultimate_golf',
  },
  {
    image: `${IMAGE_BASE_URL}/miniclip/pure_sniper.png`,
    label: 'pure sniper',
    value: 'pure_sniper',
  },
  {
    image: `${IMAGE_BASE_URL}/miniclip/carrom_pool.png`,
    label: 'Carrom Pool',
    value: 'carrom_pool',
  },
  {
    image: `${IMAGE_BASE_URL}/miniclip/basketball_stars.png`,
    label: 'basketball stars',
    value: 'basketball_stars',
  },
  {
    image: `${IMAGE_BASE_URL}/miniclip/agar_io.png`,
    label: 'agar.io',
    value: 'agar_io',
  },
  {
    image: `${IMAGE_BASE_URL}/miniclip/subway_surfers.png`,
    label: 'subway surfers',
    value: 'subway_surfers',
  },
  {
    image: `${IMAGE_BASE_URL}/miniclip/football_strike.png`,
    label: 'Football Strike - Multiplayer Socce',
    value: 'football_strike',
  },
  {
    image: `${IMAGE_BASE_URL}/miniclip/football_battle.png`,
    label: 'Football Battle',
    value: 'football_battle',
  },
  {
    image: `${IMAGE_BASE_URL}/miniclip/bubble_shooter.png`,
    label: 'bubble shooter',
    value: 'bubble_shooter',
  },
];
