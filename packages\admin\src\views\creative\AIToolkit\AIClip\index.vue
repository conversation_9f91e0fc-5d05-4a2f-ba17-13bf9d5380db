<template>
  <common-view
    class="h-screen"
    :hide-right="true"
    :store="toolkitStore"
  >
    <template #views>
      <div class="rounded-lg w-full flex flex-col flex-1 relative overflow-hidden">
        <div class="w-full h-full flex flex-row">
          <div class="bg-white rounded-default relative flex-1 p-[20px] flex flex-col">
            <LeftSection />
          </div>
          <div
            class="flex flex-row relative h-full ml-[20px] min-w-[16px] rounded-default"
            :class="{
              'bg-white': !sidebarVisible,
            }"
          >
            <div
              class="absolute top-0 bottom-0 -left-[9px] my-auto shadow-[0_3px_6px_0_rgba(0,0,0,.03)]
              flex justify-center items-center w-5 h-[60px] rounded-default bg-white-primary cursor-pointer
               border border-black-disabled z-20
               border-solid box-border hover:shadow-[0_3px_6px_0_rgba(0,0,0,.1)] transform-z-10 hover:border-brand"
              @click="() => (sidebarVisible ? hideSidebar() : showSidebar())"
            >
              <svg-icon
                name="arrow"
                size="12"
                class="transition"
                :class="sidebarVisible ? '-rotate-90' : 'rotate-90'"
              />
            </div>

            <transition name="fade">
              <div
                v-show="sidebarVisible"
                class="rounded-default flex overflow-hidden flex-col w-[40vw]"
              >
                <Video-Clips />
                <div class="w-full flex flex-1 overflow-hidden p-[20px] bg-white mt-[16px] rounded-default">
                  <Footer />
                </div>
              </div>
            </transition>
          </div>
        </div>
      </div>
    </template>
  </common-view>
</template>

<script setup lang="ts">
import CommonView from 'common/components/Layout/CommonView.vue';
import LeftSection from './components/LeftSection/index.vue';
import VideoClips from './components/VideoClips/index.vue';
import { useAIClipStore } from '@/store/creative/toolkit/ai_clips.store';
import Footer from './components/Footer/index.vue';
import SvgIcon from 'common/components/SvgIcon';
import { storeToRefs } from 'pinia';
const toolkitStore = useAIClipStore();
const { sidebarVisible } = storeToRefs(toolkitStore);
const { hideSidebar, showSidebar } = toolkitStore;
</script>
<style lang="scss" scoped>
.fade-enter-active {
  width: 40vw;
  opacity: 1;
  transition: all 0.3s ease-in-out;
}

.fade-leave-active {
  width: 40vw;
  opacity: 1;
  transition: all 0.3s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  transform: translateX(40vw);
  width: 0;
  opacity: 0;
}
</style>
