<template>
  <div
    id="wavesurfer"
    ref="waveSurferRef"
  />
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import WaveSurfer from 'wavesurfer.js';
import Regions, { type Region } from 'wavesurfer.js/dist/plugins/regions';
import TimelinePlugin from 'wavesurfer.js/dist/plugins/timeline';
interface IProps {
  value?: [number, number];
}
const props = defineProps<IProps>();

const waveSurferRef = ref<HTMLDivElement>();

const curActiveRegion = ref<Region | null>(null);
const ws = ref<WaveSurfer>();
const wsRegionsPlugin = ref<Regions>();
const value = ref<[number, number] | undefined>(props.value);
const audioDuration = ref<number>(0);

const emits = defineEmits(['update:value']);

onMounted(() => {
  // Create an instance of WaveSurfer
  ws.value = WaveSurfer.create({
    container: waveSurferRef.value!,
    waveColor: 'rgba(0,0,0,.45)',
    progressColor: '#619bff',
    minPxPerSec: 100,
    barWidth: 2,
    cursorWidth: 2,
    height: 100,
  });
  // https://static.aix.intlgame.com/audio/Fitz%20and%20The%20Tantrums%20-%20HandClap.ogg
  ws.value.load('https://static.aix.intlgame.com/audio/Fitz%20and%20The%20Tantrums%20-%20HandClap.ogg');

  wsRegionsPlugin.value = ws.value.registerPlugin(Regions.create());

  // 设置代理，在调用saveRegion时，清空所有region，保证只有一个region
  const wsRegionsProxy = new Proxy(wsRegionsPlugin.value, {
    get(target, prop) {
      if (prop === 'saveRegion') {
        wsRegionsProxy.clearRegions();
      }
      return Reflect.get(target, prop);
    },
  });
  wsRegionsProxy.enableDragSelection({
    color: 'rgba(255, 0, 0, 0.1)',
  });
  // play on selected wsRegion
  wsRegionsPlugin.value.on('region-clicked', (region, e) => {
    e.stopPropagation(); // prevent triggering a click on the waveform
    curActiveRegion.value = region;
    region.play();
  });
  wsRegionsPlugin.value.on('region-out', (region: Region) => {
    // region play end
    if (curActiveRegion?.value?.id === region.id) {
      ws.value!.playPause();
      curActiveRegion.value = null;
    }
  });
  wsRegionsPlugin.value.on('region-created', (region) => {
    const { start, end } = region;
    value.value = [start.toFixed(2), end.toFixed(2)].map(Number) as [number, number];
    emits('update:value', value.value);
  });

  wsRegionsPlugin.value.on('region-updated', (region) => {
    console.log(region);
    const { start, end } = region;
    value.value = [start.toFixed(2), end.toFixed(2)].map(Number) as [number, number];
    emits('update:value', value.value);
  });

  // Initialize the Timeline plugin
  ws.value.registerPlugin(TimelinePlugin.create({
    height: 20,
    insertPosition: 'beforebegin',
    timeInterval: 0.2,
    primaryLabelInterval: 5,
    secondaryLabelInterval: 1,
    style: {
      fontSize: '12px',
      color: '#5e5e5e',
    },
  }));

  // Play on click
  ws.value.on('interaction', () => {
    if (curActiveRegion.value) {
      curActiveRegion.value = null;
    }
    ws.value?.play();
  });

  ws.value.on('decode', (duration: number) => {
    audioDuration.value = duration;
  });

  // // Rewind to the beginning on finished playing
  ws.value.on('finish', () => {
    ws.value!.setTime(0);
  });
});


defineExpose({
  load: (src: string) => {
    ws.value?.load(src);
  },
  wsInstance: ws,
});
</script>

<style lang="scss"></style>
