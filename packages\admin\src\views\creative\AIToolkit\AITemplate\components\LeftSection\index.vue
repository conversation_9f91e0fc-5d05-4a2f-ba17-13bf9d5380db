<template>
  <div class="rounded-default bg-white-primary p-[20px] flex flex-col gap-[20px]">
    <div class="flex flex-col">
      <div class="text-base font-semibold">Text to Template</div>
      <div class="h-[150px] mt-[16px] w-full">
        <t-button
          theme="default"
          variant="dashed"
          class="w-full !h-full"
          @click="handleAddTemplate"
        >
          Add
          <template #icon>
            <t-icon
              name="add"
              size="24"
            />
          </template>
        </t-button>
      </div>
      <!-- <div class="border-gray-primary border-[1px] rounded-default border-dashed h-[150px]">123</div> -->
    </div>
    <div class="flex grow flex-col overflow-hidden">
      <div class="text-sm font-semibold">Template List</div>
      <div class="flex grow border-gray-primary border-[1px] mt-[16px] rounded-default overflow-hidden">
        <TemplateList
          :data-source="templateList"
          class="p-2 overflow-auto"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { Form } from 'tdesign-vue-next';
import { useAiTemplateStore } from '@/store/creative/toolkit/ai_template.store';
import { inject, Ref } from 'vue';
import { storeToRefs } from 'pinia';
import TemplateList from '../TemplateList/index.vue';

const { initTemplateSlots } = useAiTemplateStore();
const { templateList } = storeToRefs(useAiTemplateStore());

// inject data from AITemplate/index.vue
const formRef = inject<Ref<InstanceType<typeof Form> & HTMLFormElement>>('formRef');

const handleAddTemplate = () => {
  formRef?.value.reset();
  initTemplateSlots();
};
</script>

<style lang="scss" scoped></style>
