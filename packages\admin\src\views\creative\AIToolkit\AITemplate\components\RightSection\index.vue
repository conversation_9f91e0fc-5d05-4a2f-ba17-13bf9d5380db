<template>
  <div class="bg-white-primary rounded-default p-[20px] flex flex-col gap-[16px] overflow-hidden">
    <div class="text-base font-semibold">{{ title }}</div>
    <div class="grow overflow-auto px-[16px]">
      <SlotForm />
    </div>
    <div class="flex justify-end gap-[16px]">
      <t-button
        content="preview"
        theme="default"
      />
      <t-button
        content="Save"
        theme="primary"
        @click="handleSavaBtnClick"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { Form } from 'tdesign-vue-next';
import SlotForm from '../SlotForm/index.vue';
import { inject, Ref, computed } from 'vue';
import { useAiTemplateStore } from '@/store/creative/toolkit/ai_template.store';

const { curTemplateFormData } = useAiTemplateStore();

const title = computed(() => (curTemplateFormData.templateId === undefined ? 'New template' : 'Edit template '));
// inject data from AITemplate/index.vue
const formRef = inject<Ref<InstanceType<typeof Form> & HTMLFormElement>>('formRef');

const handleSavaBtnClick = () => {
  formRef?.value?.submit();
};
</script>

<style lang="scss" scoped></style>
