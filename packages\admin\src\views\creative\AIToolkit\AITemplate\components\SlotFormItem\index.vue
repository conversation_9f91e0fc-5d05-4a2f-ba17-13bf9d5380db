<template>
  <div class="border-gray-primary border-[1px] rounded-lg p-[20px] gap-[16px] flex flex-col">
    <div class="relative">
      <div class="text-base font-semibold">
        {{ title }}
      </div>
      <div
        class="absolute cursor-pointer right-0 top-0 rounded-[50%] w-[16px] h-[16px] p-1
        box-content flex justify-center items-center hover:bg-gray-primary"
        @click="handleCloseBtnClick"
      >
        <t-icon
          name="close"
          size="20"
        />
      </div>
    </div>
    <div class="grow flex gap-[16px] flex-col">
      <div>
        <t-form-item
          :name="`templateSlots[${props.index}].duration`"
          label="Duration"
          label-width="110px"
          label-align="left"
        >
          <t-input-number
            v-model="formData.templateSlots[props.index].duration"
            class="w-full"
            min="0"
            step="0.1"
            :decimal-places="2"
          >
            <template #suffix><span>s</span></template>
          </t-input-number>
        </t-form-item>
      </div>
      <t-divider
        align="left"
        class="text-gray-500"
      >
        Video
      </t-divider>
      <div>
        <t-form-item
          label="Change speed"
          label-width="110px"
          label-align="left"
        >
          <t-switch v-model="formData.templateSlots[props.index].video_info.can_change_speed" />
        </t-form-item>

        <t-form-item
          label="Muted"
          label-width="110px"
          label-align="left"
        >
          <t-switch v-model="formData.templateSlots[props.index].video_info.mute" />
        </t-form-item>
      </div>
      <t-divider
        align="left"
        class="text-gray-500"
      >
        Audio
      </t-divider>

      <div v-auto-animate>
        <t-form-item
          label="Load audio"
          label-width="110px"
          label-align="left"
        >
          <t-switch v-model="isLoadAudio" />
        </t-form-item>
        <div v-if="isLoadAudio">
          <t-form-item
            :name="`templateSlots[${props.index}].audio_info.audio_url`"
            label="Audio URL"
            label-width="110px"
            label-align="left"
          >
            <t-input
              v-model="formData.templateSlots[props.index].audio_info.audio_url"
              @blur="handleAudioInputBlur"
            />
          </t-form-item>
          <t-form-item
            :name="`templateSlots[${props.index}].audio_info.audio_range`"
            label="Audio range"
            label-width="110px"
            label-align="left"
          >
            <t-range-input
              v-model="formData.templateSlots[props.index].audio_info.audio_range"
              :disabled="!isAudioUrl"
              :placeholder="['Start time', 'End time']"
            />
          </t-form-item>
          <AudioWaveSurfer
            v-if="isAudioUrl"
            ref="audioWaveSurferRef"
            v-model:value="formData.templateSlots[props.index].audio_info.audio_range"
          />
        </div>
      </div>
    </div>
  </div>
  <div
    v-if="!props.isEnd"
    class="w-full grid grid-cols-2 justify-center gap-4"
  >
    <t-form-item
      :name="`templateSlots[${props.index}].transform`"
      label="Transition Effect"
      label-width="140px"
      label-align="left"
    >
      <t-select v-model="formData.templateSlots[props.index].transform">
        <t-option
          v-for="item in transitionEffectOptions"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </t-select>
    </t-form-item>

    <t-form-item
      :name="`templateSlots[${props.index}].transformDuration`"
      label="Transition duration"
      label-width="140px"
      label-align="left"
    >
      <t-input-number
        v-model="formData.templateSlots[props.index].transformDuration"
        theme="normal"
        :max="15"
        :min="0"
        :step="0.1"
        :decimal-places="2"
        :allow-input-over-limit="false"
      />
      <!-- <t-input-number
        v-model="formData.templateSlots[props.index].duration"
        class="w-full"
        min="0"
        step="0.1"
        :decimal-places="2"
      /> -->
    </t-form-item>
  </div>
</template>
<script setup lang="tsx">
import { computed, inject, ref } from 'vue';
import { type TemplateFormDataType } from '../../types/index.d';

import { TransformType } from '../../constant/index';
import AudioWaveSurfer from '../AudioWaveSurfer/index.vue';
import validator from 'validator';
interface IProps {
  index: number;
  isEnd: boolean;
}
const transitionEffectOptions = computed(() => Object.keys(TransformType).map((i: string) => ({
  value: i,
  label: TransformType[i],
})));

const props = defineProps<IProps>();
const formData = inject<TemplateFormDataType>('formData')!;

const title = computed(() => `Slot ${props.index + 1}`);
const isLoadAudio = ref(false);
const isAudioUrl = ref(false);
const audioWaveSurferRef = ref<InstanceType<typeof AudioWaveSurfer>>();

const emits = defineEmits(['close']);
const handleCloseBtnClick = () => {
  emits('close');
};

const handleAudioInputBlur = (value: string) => {
  isAudioUrl.value = validator.isURL(value);
  audioWaveSurferRef.value?.load(value);
};
</script>
<style scoped>
:deep(.t-divider) {
  margin: 0;
}
</style>
