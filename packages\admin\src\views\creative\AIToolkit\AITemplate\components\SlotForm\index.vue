<template>
  <div class="flex flex-col gap-[16px] grow">
    <t-form
      ref="formRef"
      :rules="rules"
      :data="curTemplateFormData"
      @submit="handleFormSubmit"
    >
      <t-form-item
        name="name"
        label="Template Name"
        label-width="130px"
      >
        <t-input
          v-model="curTemplateFormData.name"
          clearable
        />
      </t-form-item>
      <t-form-item
        name="ratio"
        label="Ratio"
        label-width="130px"
      >
        <t-select v-model="curTemplateFormData.ratio">
          <t-option
            v-for="item in RATIO_OPTIONS"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          >
            {{ item.label }}
          </t-option>
        </t-select>
      </t-form-item>
      <div
        v-auto-animate
        class="flex flex-col gap-[16px]"
      >
        <SlotFormItem
          v-for="(templateSlot, index) in curTemplateFormData.templateSlots"
          :key="templateSlot.id"
          :index="index"
          :is-end="index === curTemplateSlots.length - 1"
          @close="deletedSlot(templateSlot.id!)"
        />
      </div>
    </t-form>
    <div class="w-full">
      <t-button
        content="Add Slot"
        variant="dashed"
        class="w-full"
        @click="addSlot"
      >
        <template #icon>
          <t-icon
            name="add"
            size="16"
          />
        </template>
      </t-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { provide, ref, inject, MaybeRef, onMounted } from 'vue';
import { useAiTemplateStore } from '@/store/creative/toolkit/ai_template.store';
import { storeToRefs } from 'pinia';
import { RATIO_OPTIONS } from '../../constant/index';
import SlotFormItem from '../SlotFormItem/index.vue';
import { Form, MessagePlugin, SubmitContext } from 'tdesign-vue-next';
import { type TemplateFormDataType } from '../../types/index.d';
import {
  CanvasConfig,
  VideoInfo,
  AudioInfo,
  TemplateSlot,
  VideoTransition,
  CreateTemplateParam,
  ModifyTemplateParam,
} from 'common/service/creative/aigc_toolkit/type.d';
import { createTemplate, modifyTemplate } from 'common/service/creative/aigc_toolkit/ai_template';
const { curTemplateSlots } = storeToRefs(useAiTemplateStore());
const { curTemplateFormData } = useAiTemplateStore();
const { addSlot, deletedSlot, getTemplates } = useAiTemplateStore();

// inject data from AITemplate/index.vue
const changeFormRef = inject<(form: MaybeRef) => void>('changeFormRef');

const formRef = ref<InstanceType<typeof Form> & HTMLFormElement>();

const rules = {
  name: [{ required: true, message: 'Please enter Template Name', trigger: 'blur' }],
  ratio: [{ required: true, message: 'Please select Ratio', trigger: 'blur' }],
  duration: [{ required: true, message: 'Please enter template duration', trigger: 'blur' }],
  audio_url: [
    { required: true, message: 'Please enter audio url', trigger: 'blur' },
    { url: { protocols: ['http', 'https'] }, message: 'Please enter valid url', trigger: 'blur' },
  ],
  audio_range: [{ required: true, message: 'Please enter audio range', trigger: 'blur' }],
  transform: [{ required: true, message: 'Please select transition effect', trigger: 'blur' }],
  transformDuration: [{ required: true, message: 'Please enter transform duration', trigger: 'blur' }],
};

const handleFormSubmit = async ({ validateResult, firstError }: SubmitContext<TemplateFormDataType>) => {
  if (validateResult === true) {
    const isEdit = curTemplateFormData.templateId !== undefined;

    const params = formatRequestParam(curTemplateFormData, isEdit);
    try {
      if (isEdit) {
        await modifyTemplate(params as ModifyTemplateParam);
      } else {
        await createTemplate(params as CreateTemplateParam);
      }
      MessagePlugin.success(`${isEdit ? 'Modified' : 'Created'} successfully`);
      getTemplates();
    } catch (e) {
      MessagePlugin.error((e as unknown as Error).message ?? `${isEdit ? 'Modified' : 'Created'} Template failure`);
    }
  } else {
    MessagePlugin.warning(firstError ?? '');
  }
};

const formatRequestParam = (
  formData: TemplateFormDataType,
  isEdit: boolean,
): CreateTemplateParam | ModifyTemplateParam => {
  const templateName = formData.name ?? '';

  const [canvasWidth, canvasHeight] = formData.ratio!.split('*').map(Number);

  const canvas: CanvasConfig = {
    color: 'black',
    width: canvasWidth,
    height: canvasHeight,
  };

  const slotList = formData.templateSlots.map((slot, index, originList): TemplateSlot => {
    const isEnd = originList.length === index + 1;
    const videoInfo: VideoInfo = {
      can_change_speed: slot.video_info.can_change_speed,
      audio: {
        mute: slot.video_info.mute,
        gain: 0,
      },
    };

    let audioInfo: AudioInfo | undefined;
    let transition: VideoTransition | undefined;

    if (slot.audio_info.load_audio) {
      audioInfo = {
        url: slot.audio_info.audio_url!,
        range_time: slot.audio_info.audio_range!,
      };
    }

    if (!isEnd) {
      transition = {
        duration: slot.transformDuration ?? 0,
        transitions: slot.transform ?? '',
      };
    }

    return {
      slot_id: index,
      video_info: videoInfo,
      duration: Number(slot.duration),
      audio_info: audioInfo,
      transition,
    };
  });

  return {
    template: {
      template_name: templateName,
      template_config: {
        canvas,
        slots: slotList,
      },
    },
    ...(isEdit
      ? {
        template_id: curTemplateFormData.templateId,
      }
      : {}),
  };
};

provide('formData', curTemplateFormData);

onMounted(() => {
  changeFormRef?.(formRef);
});
</script>
<style scoped></style>
