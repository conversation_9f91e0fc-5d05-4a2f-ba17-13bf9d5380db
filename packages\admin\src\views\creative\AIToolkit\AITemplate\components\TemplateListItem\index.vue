<template>
  <div
    class="w-full h-[40px] rounded-default flex justify-between items-center
     cursor-pointer bg-background px-2 hover:bg-black-secondary"
    @click="handleTemplateClick"
  >
    <div>{{ props.data.template_name }}</div>
    <div
      class="pl-2"
      @click.stop="deleteTemplate"
    >
      <SvgIcon
        name="delete"
        size="16"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { reactive } from 'vue';
import SvgIcon from 'common/components/SvgIcon';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';

import { GetAllTemplatesResponseDataType, TemplateSlot as BackendTemplateSlot } from 'common/service/creative/aigc_toolkit/type.d';
import { deleteTemplateById } from 'common/service/creative/aigc_toolkit/ai_template';
import { useAiTemplateStore } from '@/store/creative/toolkit/ai_template.store';
import { TemplateSlot as FrontendTemplateSlot } from '../../types';

interface IProps {
  data: GetAllTemplatesResponseDataType[number];
}
const props = defineProps<IProps>();
const { getTemplates, curTemplateFormData } = useAiTemplateStore();

const confirmBtn = reactive({
  content: 'Confirm',
  loading: false,
});

const deleteTemplate = () => {
  const confirmDialog = DialogPlugin.confirm({
    header: 'Tips',
    body: 'Are you sure delete it?',
    confirmBtn,
    cancelBtn: 'Cancel',
    onConfirm: () => {
      confirmBtn.loading = true;
      deleteTemplateById(props.data.template_id)
        .then(() => {
          MessagePlugin.success('Deleted successfully');
          getTemplates();
        })
        .catch((e) => {
          MessagePlugin.error((e as unknown as Error).message ?? 'Deleted failure');
        })
        .finally(() => {
          confirmBtn.loading = false;
          confirmDialog.hide();
        });
    },
    onClose: () => {
      confirmDialog.hide();
    },
  });
};
const handleTemplateClick = () => {
  console.log('handleTemplateClick');
  formatDataByTemplateFormDataType(props.data);
};

const formatDataByTemplateFormDataType = (data: GetAllTemplatesResponseDataType[number]) => {
  console.log(data);
  const { template_id: templateId, template_name: templateName, template_config: templateConfig } = data;
  curTemplateFormData.name = templateName;
  curTemplateFormData.templateId = templateId;
  curTemplateFormData.ratio = `${templateConfig.canvas.width}*${templateConfig.canvas.height}`;


  const slots: FrontendTemplateSlot[] = templateConfig.slots.map((slot: BackendTemplateSlot, index: number, slots) => {
    const { duration } = slot;
    const isEnd = slots.length === index + 1;
    const isLoadAudio = !!slot.audio_info?.url ;

    const res: FrontendTemplateSlot = {
      duration: String(duration),
      video_info: {
        can_change_speed: slot.video_info.can_change_speed,
        mute: slot.video_info.audio.mute,
      },
      audio_info: {
        load_audio: isLoadAudio,
        ...(isLoadAudio ? {
          audio_url: slot.audio_info?.url,
          audio_range: slot.audio_info?.range_time,
        } : {}),

      },
      ...(!isEnd ? {
        transform: slot.transition?.transitions,
        transformDuration: slot.transition?.duration,
      } : {}),
    };

    return res;
  });

  // 不能直接替换,会失去响应式
  curTemplateFormData.templateSlots.length = 0;
  curTemplateFormData.templateSlots.push(...slots) ;
};
</script>

<style lang="scss" scoped></style>
