<template>
  <!-- <div class="w-full h-full p-2 flex"> -->
  <template v-if="props?.dataSource?.length > 0">
    <div
      v-auto-animate
      class="w-full space-y-2"
    >
      <TemplateListItem
        v-for="item in props.dataSource"
        :key="item.template_id"
        :data="item"
      />
    </div>
  </template>
  <template v-else>
    <div class="w-full h-full flex justify-center items-center">There is no template,please add it first.</div>
  </template>
  <!-- </div> -->
</template>
<script setup lang="ts">
// import { ref } from 'vue';
import TemplateListItem from '../TemplateListItem/index.vue';
import { GetAllTemplatesResponseDataType } from 'common/service/creative/aigc_toolkit/type.d';
interface IProps {
  dataSource: GetAllTemplatesResponseDataType;
}
const props = defineProps<IProps>();
</script>

<style lang="scss" scoped></style>
