export const TransformType: Record<string, string> = {
  ImageFadeInFadeOut: '图像淡入淡出',
  BowTieHorizontal: '水平蝴蝶结',
  BowTieVertical: '垂直蝴蝶结',
  ButterflyWaveScrawler: '晃动',
  Cannabisleaf: '枫叶',
  Circle: '弧形收放',
  CircleCrop: '圆环聚拢',
  Circleopen: '椭圆聚拢',
  Crosswarp: '横向翘曲',
  Cube: '立方体',
  DoomScreenTransition: '幕布',
  Doorway: '门廊',
  Dreamy: '波浪',
  DreamyZoom: '水平聚拢',
  FilmBurn: '火烧云',
  GlitchMemories: '抖动',
  Heart: '心形',
  InvertedPageCurl: '翻页',
  Luma: '腐蚀',
  Mosaic: '九宫格',
  Pinwheel: '风车',
  PolarFunction: '椭圆扩散',
  PolkaDotsCurtain: '弧形扩散',
  Radial: '雷达扫描',
  RotateScaleFade: '上下收放',
  Squeeze: '上下聚拢',
  Swap: '放大切换',
  Swirl: '螺旋',
  UndulatingBurnOutSwirl: '水流蔓延',
  Windowblinds: '百叶窗',
  WipeDown: '向下收起',
  WipeLeft: '向左收起',
  WipeRight: '向右收起',
  WipeUp: '向上收起',
  ZoomInCircles: '水波纹',
};
export const RATIO_OPTIONS = [
  { label: '1920*1080', value: '1920*1080' },
];
