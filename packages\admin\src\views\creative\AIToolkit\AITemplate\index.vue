<template>
  <common-view
    class="h-screen overflow-hidden"
    :store="aiTemplateStore"
    :hide-right="true"
  >
    <template #views>
      <div class="flex w-full h-full gap-[20px] overflow-hidden">
        <LeftSection class="w-[400px]" />
        <RightSection class="flex-1" />
      </div>
    </template>
  </common-view>
</template>

<script setup lang="ts">
import CommonView from 'common/components/Layout/CommonView.vue';
import LeftSection from './components/LeftSection/index.vue';
import RightSection from './components/RightSection/index.vue';
import { provide, ref, MaybeRef, unref } from 'vue';
import { Form } from 'tdesign-vue-next';
import { useAiTemplateStore } from '@/store/creative/toolkit/ai_template.store';
const aiTemplateStore = useAiTemplateStore();
const formRef = ref<InstanceType<typeof Form>>();
const changeFormRef = (newForm: MaybeRef) => {
  formRef.value = unref(newForm);
};

provide('formRef', formRef);
provide('changeFormRef', changeFormRef);
</script>
<style lang="scss" scoped></style>
