export interface TemplateSlot {
  id?: number;
  duration?: string;
  transform?: string;
  transformDuration?: number;

  video_info: {
    can_change_speed: boolean;
    mute: boolean;
  };
  audio_info: {
    load_audio: boolean;
    audio_url?: string;
    audio_range?: [number, number];
  };
}

export interface TemplateFormDataType {
  name?: string;
  ratio?: string;
  templateId?: number;
  templateSlots: TemplateSlot[];
}
