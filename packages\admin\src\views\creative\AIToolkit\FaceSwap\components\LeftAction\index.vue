<template>
  <div class="left-action flex flex-col justify-between h-[100%]">
    <div class="top h-[100%] flex flex-col">
      <Setting class="mb-[12px]" />
      <face-setting />
      <div class="action flex justify-end mt-[24px]">
        <t-button
          theme="primary"
          :disabled="detectedFaces.length === 0"
          @click="() => preview()"
        >
          Preview
        </t-button>
        <t-button
          class="ml-[12px]"
          theme="success"
          :disabled="detectedFaces.length === 0"
          @click="generate"
        >
          Generate
        </t-button>
      </div>
    </div>

    <!-- 预览弹框  -->
    <t-dialog
      v-model:visible="previewDialog"
      header="Frames preview"
      top="80px"
      width="800"
      :confirm-on-enter="true"
      cancel-btn="Close"
      @close="onClosePreview"
    >
      <div class="preview-face-list min-h-[200px] max-h-[600px] overflow-y-auto">
        <div class="flex mb-[20px]">
          <div class="flex-1 flex justify-center text-[large] font-bold">Detected</div>
          <div class="flex-1 flex justify-center text-[large] font-bold">Target</div>
        </div>
        <div
          v-for="(item, index) in previewList"
          :key="index"
          class="flex relative justify-center items-center mb-[16px]"
        >
          <div class="flex flex-1 justify-center">
            <img :src="`${CDN}/${item.source}`" class="w-[252px] h-[132px] object-contain bg-[#000]" alt="">
          </div>
          <arrow-right-icon size="32" class="face-right" />
          <div class="flex flex-1 justify-center">
            <img :src="`${CDN}/${item.target}`" class="w-[252px] h-[132px] object-contain bg-[#000]" alt="">
          </div>
        </div>
        <div class="flex relative justify-center items-center">
          <t-loading v-if="previewLoading" />
        </div>
      </div>
      <template #confirmBtn>
        <t-button
          class="ml-[10px]" theme="primary" :loading="previewMoreLoading"
          :disable="previewLoading"
          @click="previewMore"
        >
          Preview more frames
        </t-button>
      </template>
    </t-dialog>

    <download-dialog
      v-model="processDialog"
      tip="Your video is being generated and will be ready in a few minutes"
    />
  </div>
</template>
<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import Setting from '../Setting/index.vue';
import FaceSetting from '../Setting/FaceSetting.vue';
import { FILE_CDN_COM as CDN } from 'common/config';
import { useAIFaceSwapStore } from '@/store/creative/toolkit/ai_face_swap.store';
import { ArrowRightIcon } from 'tdesign-icons-vue-next';
import DownloadDialog from '../../../components/DownloadDIalog.vue';

const { preview, generate } = useAIFaceSwapStore();
const {
  previewLoading, previewMoreLoading, previewDialog, previewList, processDialog, detectedFaces,
} = storeToRefs(useAIFaceSwapStore());

const previewMore = () => {
  preview(true);
  setTimeout(() => {
    const list = document.querySelector('.preview-face-list') as HTMLDivElement;
    list.scrollTop = 99999;
  }, 100);
};

const onClosePreview = () => {
  previewList.value = [];
};
</script>
