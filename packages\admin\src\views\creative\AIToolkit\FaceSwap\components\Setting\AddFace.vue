<template>
  <t-dialog
    v-model:visible="showDialog"
    header="Add Face"
    width="600"
    :destroy-on-close="true"
    :confirm-on-enter="true"
  >
    <t-form
      ref="formRef" :data="formData" :rules="rules"
      class="min-h-[320px]"
    >
      <t-form-item label="Face" name="facePath" required>
        <uploader
          accept="image/png, image/jpeg, image/jpg"
          file-path="ai_toolkit/temp_files"
          tips="Supported format: .png/.jpg/.jpeg"
          @change="onFileChange"
        />
      </t-form-item>
      <t-form-item label="Label" name="label">
        <t-select
          v-model="formData.label"
          class="w-[336px]" clearable
          placeholder="select label"
        >
          <t-option
            v-for="value in labelList" :key="value" :label="value"
            :value="value"
          />
        </t-select>
      </t-form-item>
    </t-form>
    <template #confirmBtn>
      <t-button
        class="ml-[10px]" theme="primary" :loading="faceUploading"
        @click="onConfirm"
      >
        Submit
      </t-button>
    </template>
  </t-dialog>
</template>
<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { UploadFile as TdUploadFile } from 'tdesign-vue-next/es/upload/type';
import { useAIFaceSwapStore } from '@/store/creative/toolkit/ai_face_swap.store';
import Uploader from '@/views/creative/AIToolkit/components/Uploader/index.vue';
import { UploadReq } from 'common/components/FileUpload/type';
import { storeToRefs } from 'pinia';
import { MessagePlugin } from 'tdesign-vue-next';

const { faceUpload, getFaces } = useAIFaceSwapStore();
const { labelList } = storeToRefs(useAIFaceSwapStore());
const showDialog = ref(false);

const formRef = ref();
const formData = reactive({
  facePath: '',
  asset_id: '',
  label: '',
});

const rules = {
  facePath: [{
    required: true,
    validator: () => {
      if (formData.facePath !== '') return true;
      return {
        message: 'Please upload face.', result: false, type: 'error',
      };
    },
  }],
  label: [{
    required: true,
    validator: (val: string) => {
      if (val) return true;
      return {
        message: 'Please select label.', result: false, type: 'error',
      };
    },
  }],
};

const onFileChange = (fileList: TdUploadFile[]) => {
  if (fileList.length === 0) {
    formData.asset_id = '';
    formData.facePath = '';
    return;
  }
  const res = fileList[0].response as UploadReq;
  formData.asset_id = res.id as string;
  formData.facePath = res.key as string;
};

const show = () => {
  Object.keys(formData).forEach((k) => {
    formData[k as keyof typeof formData] = '';
  });
  showDialog.value = true;
};

const faceUploading = ref(false);
const onConfirm = async () => {
  const validateRes = await formRef.value.validate();
  if (validateRes !== true) return;

  faceUploading.value = true;
  const res = await faceUpload({
    asset_id: formData.asset_id,
    image_path: formData.facePath,
    label: formData.label,
  });
  // 延迟500毫秒，否则图片会404
  setTimeout(() => {
    faceUploading.value = false;
    if (res.code !== 0) {
      MessagePlugin.error(res.message);
      return;
    }
    MessagePlugin.success('Create success');
    showDialog.value = false;
    getFaces();
  }, 500);
};

defineExpose({
  show,
});
</script>
