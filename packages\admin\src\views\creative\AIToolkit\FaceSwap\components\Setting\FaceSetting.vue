<template>
  <div class="face-faces flex flex-[1] flex-col">
    <div class="font-bold text-lg mb-[12px]">Setting</div>
    <div class="flex border-[1px] flex-[1]">
      <div class="replace w-[32%] border-r-[1px] p-[12px] flex flex-col">
        <div class="flex mb-[12px]">
          <div class="flex-1 flex justify-center title">Detected</div>
          <div class="flex-1 flex justify-center title">Target</div>
        </div>
        <t-loading
          :loading="faceDetecting" text="Face detecting..."
          class="flex-grow flex-shrink overflow-auto h-[232px]"
        >
          <div class="replace-faces">
            <template v-for="(item, index) in detectedFaces" :key="item.Image">
              <div class="flex relative justify-center mb-[12px]">
                <div class="flex flex-1 justify-center">
                  <img :src="`${CDN}/${item.Image}`" class="w-[60px] h-[60px] rounded-[50%]" alt="">
                </div>
                <arrow-right-icon size="24" class="face-right" />
                <div
                  class="flex flex-1 justify-center cursor-pointer w-[60px] h-[60px]"
                  :class="replaceIndex === index ? 'face-active' : ''"
                >
                  <div v-if="replaceFaces[index]" class="relative">
                    <img
                      :src="`${CDN}/${(replaceFaces[index] as Face).image_path}`"
                      class="w-[60px] h-[60px] rounded-[50%]" alt=""
                      @click="indexFace(index)"
                    >
                    <CloseCircleFilledIcon
                      color="red" size="16px" class="absolute top-[0px] right-[-4px] cursor-pointer"
                      @click="deleteFace(index)"
                    />
                  </div>
                  <div
                    v-else
                    class="to-be-replace w-[60px] h-[60px] rounded-[50%] flex flex-center bg-[#eee]"
                    @click="indexFace(index)"
                  >
                    <AddIcon size="24" color="#999" />
                  </div>
                </div>
              </div>
            </template>
            <div
              v-if="detectedFaces.length === 0 && !faceDetecting"
              class="text-center text-black-placeholder my-[24px]"
            >
              No face detected
            </div>
          </div>
        </t-loading>
      </div>
      <div class="library p-[12px] w-[68%] flex flex-col">
        <div class="flex items-center justify-between">
          <div class="title">Face Library</div>
          <div class="flex items-center cursor-pointer text-brand text-[16px]" @click="add">
            <AddIcon />
            <span>Add Faces</span>
          </div>
        </div>
        <div class="filter">
          <t-select
            v-model="selectedLabels" class="w-[240px] my-[12px]" multiple
            clearable :min-collapsed-num="2"
            placeholder="select label"
            @change="getFaces"
          >
            <t-option
              v-for="value in labelList" :key="value" :label="value"
              :value="value"
            />
          </t-select>
        </div>
        <div v-loading="faceLibLoading" class="face-list flex-grow flex-shrink overflow-auto h-[232px]">
          <div
            v-for="face in faceList" :key="face.asset_id"
            class="relative face inline-block w-[100px] h-[100px] m-[4px] cursor-pointer"
            @click="selectFace(face)"
          >
            <div
              v-if="face.creator === username"
              class="hover cursor-pointer absolute right-0 top-0 z-10"
              @click.stop="closeFace($event, face)"
            >
              <CloseCircleIcon
                color="var(--aix-bg-color-error-primary)"
                size="16"
              />
            </div>
            <img :src="`${CDN}/${face.image_path}`" class="rounded-[50%]" alt="">
          </div>
        </div>
      </div>
    </div>
    <add-face ref="addFaceRef" />
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { MessagePlugin } from 'tdesign-vue-next';
import { ArrowRightIcon, AddIcon, CloseCircleFilledIcon, CloseCircleIcon } from 'tdesign-icons-vue-next';
import { useAIFaceSwapStore } from '@/store/creative/toolkit/ai_face_swap.store';
import { faceDelete } from 'common/service/creative/aigc_toolkit/face_swap';
import type { Face } from 'common/service/creative/aigc_toolkit/type';
import AddFace from './AddFace.vue';
import { FILE_CDN_COM as CDN } from 'common/config';
import Cookies from 'js-cookie';

const username = Cookies.get('aix-username') as string;
const { getFaces } = useAIFaceSwapStore();
const {
  faceList, labelList, selectedLabels, faceLibLoading, detectedFaces, faceDetecting, replaceFaces,
} = storeToRefs(useAIFaceSwapStore());

const replaceIndex = ref(0);
const indexFace = (index: number) => {
  replaceIndex.value = index;
};

const selectFace = (face: Face) => {
  replaceFaces.value[replaceIndex.value] = face;
};

const deleteFace = (index: number) => {
  replaceFaces.value[index] = null;
};

const addFaceRef = ref();
const add = () => {
  addFaceRef.value.show();
};

const closeFace = async (e: Event, face: Face) => {
  const res = await faceDelete({
    assetId: face.asset_id,
  });
  if (res.code) {
    return MessagePlugin.error(res.message);
  }
  MessagePlugin.success('Delete success');
  getFaces();
};
</script>
<style lang="scss">
.face-faces {
  .title {
    font-weight: bold;
  }
  .face-right {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
  }
  .face-active {
    .to-be-replace, img {
      border: 2px solid var(--aix-bg-color-brand);
    }
  }
}
</style>
