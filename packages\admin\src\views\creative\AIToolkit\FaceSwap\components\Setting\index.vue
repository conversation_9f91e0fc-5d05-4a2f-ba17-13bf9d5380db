<template>
  <div class="face-setting flex h-[326px]">
    <div class="w-[45%]">
      <div class="flex items-center mb-[24px]">
        <span class="font-bold text-lg">
          Original Video
        </span>
        <t-tooltip :content="uploadTip">
          <InfoCircleIcon size="16" class="cursor-pointer ml-[6px]" />
        </t-tooltip>
      </div>
      <div class="flex">
        <t-form class="flex-1" label-align="top">
          <t-form-item class="flex flex-col">
            <Uploader
              ref="uploaderRef"
              accept="video/mp4, video/mov, video/quicktime"
              tips=""
              :max-duration="120"
              file-path="ai_toolkit/temp_files"
              @change="onFileChange"
            />
          </t-form-item>
        </t-form>
      </div>
    </div>
    <div class="w-[55%]">
      <div class="font-bold text-lg mb-[24px]">Parameters</div>
      <div class="params-item">
        <div class="params-label">
          Facial restoration
          <t-tooltip :content="tip1">
            <InfoCircleIcon size="16" class="cursor-pointer ml-[4px]" />
          </t-tooltip>
        </div>
        <t-slider
          v-model="faceParams.facial_restoration" :show-tooltip="true" label="${value}%%"
          :marks="[20, 40, 60, 80]"
        />
      </div>
      <div class="params-item">
        <div class="params-label">
          Retention of facial margin
          <t-tooltip :content="tip2">
            <InfoCircleIcon size="16" class="cursor-pointer ml-[4px]" />
          </t-tooltip>
        </div>
        <t-slider
          v-model="faceParams.facial_margin" :show-tooltip="true" label="${value}%%"
          :marks="[20, 40, 60, 80]"
        />
      </div>
      <div class="params-item">
        <div class="params-label">
          Keep original mouth shape
          <t-tooltip :content="tip3">
            <InfoCircleIcon size="16" class="cursor-pointer ml-[4px]" />
          </t-tooltip>
        </div>
        <t-switch v-model="faceParams.keep_origin_mouth_shape" />
      </div>
      <div class="params-item">
        <div class="params-label">
          Keep original face position
          <t-tooltip :content="tip4">
            <InfoCircleIcon size="16" class="cursor-pointer ml-[4px]" />
          </t-tooltip>
        </div>
        <t-switch v-model="faceParams.keep_origin_face_position" />
      </div>
      <div class="params-item">
        <div class="params-label">
          Model
          <t-tooltip :content="tip5">
            <InfoCircleIcon size="16" class="cursor-pointer ml-[4px]" />
          </t-tooltip>
        </div>
        <t-radio-group v-model="faceParams.model" class="w-[100%]">
          <t-radio value="GFPGAN">GFPGAN</t-radio>
          <t-radio class="mr-[0px]" value="CodeFormer">CodeFormer</t-radio>
        </t-radio-group>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { InfoCircleIcon } from 'tdesign-icons-vue-next';
import { UploadFile as TdUploadFile } from 'tdesign-vue-next';
import { useAIFaceSwapStore } from '@/store/creative/toolkit/ai_face_swap.store';
import { FaceTask } from 'common/service/creative/aigc_toolkit/type';
import Uploader from '@/views/creative/AIToolkit/components/Uploader/index.vue';
import { UploadReq } from 'common/components/FileUpload';
import { faceBus } from '../../../utils/event';
import { FILE_CDN_COM as CDN } from 'common/config';

const { detect, clearInfo, faceParams } = useAIFaceSwapStore();

const uploadTip = 'Supported format: .mp4/.mov; The video should be less than 2mins and'
  + ' the file size should be lower than 100M.';
const tip1 = 'The degree of facial repair overlaid on the face-swapping result. A high value may lead to excessive'
  + ' saturation, creating an oil painting-like effect, while a low value can cause facial blurring.';
const tip2 = 'The degree of preservation of the original facial area.'
  + ' Increasing the threshold can better handle boundary cases like bangs.';
const tip3 = 'Preserve the original mouth area to maintain the structural integrity of the mouth during speech';
const tip4 = 'Keep the generated face within the original face frame edge.'
  + ' Enabling this option can better handle facial occlusion but may lead to edge duplication.';
const tip5 = 'GFPGAN provides higher fidelity restoration results but may lead to some abnormalities (such as four eyes),'
  + ' while CodeFormer offers more stable effects but has limited precision.';

const uploaderRef = ref();
const onFileChange = async (fileList: TdUploadFile[], validVideo: boolean) => {
  if (fileList.length === 0) {
    clearInfo();
    return;
  }
  if (!validVideo) return;

  const file = fileList[0].response as UploadReq;
  const assetId = file.id as string;
  const facePath = file.key as string;
  detect(assetId, facePath);
};

// 选择任务，预览视频
faceBus.on((event) => {
  const task = JSON.parse(event) as FaceTask;
  uploaderRef.value.setFiles([
    {
      type: 'video/mp4',
      status: 'success',
      name: task.asset_id,
      url: `${CDN}/${task.origin_video}`,
    },
  ]);
});
</script>
<style lang="scss" scoped>
.params-item {
  display: flex;
  margin-bottom: 32px;
  align-items: center;
  .params-label {
    display: flex;
    align-items: center;
    font-weight: bold;
    margin-right: 24px;
    color: #9f9f9f;
    width: 204px;
    min-width: 204px;
  }
  .t-radio:nth-child(2) {
    margin-right: 0;
  }
}
</style>
