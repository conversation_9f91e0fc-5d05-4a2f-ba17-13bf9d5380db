<template>
  <t-loading :loading="taskLoading" class="overflow-y-auto px-[20px]">
    <video-tasks :task-list="taskList" :active-id="curTask?.task_id" :select-task="selectTask" />
  </t-loading>
  <template v-if="taskList.length === 0 && !taskLoading">
    <div class="text-black-placeholder text-center w-full">No Data</div>
  </template>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useAIFaceSwapStore } from '@/store/creative/toolkit/ai_face_swap.store';
import { FaceTask } from 'common/service/creative/aigc_toolkit/type';
import { faceBus } from '../../../utils/event';
import { faceVideoTaskDetail } from 'common/service/creative/aigc_toolkit/face_swap';
import VideoTasks from '../../../components/VideoTasks.vue';

const { faceParams } = useAIFaceSwapStore();
const {
  taskList, taskLoading, curAssetId, curVidePath, detectedFaces, replaceFaces,
} = storeToRefs(useAIFaceSwapStore());

const curTask = ref();
const selectTask = async (select: FaceTask) => {
  // 获取详情，主要是配置信息（数据太大，不能放在列表中）
  const res = await faceVideoTaskDetail(select.task_id as string);
  const item = {
    ...select,
    target_info: res.target.target_info,
    source_pics: res.target.source_pics,
  };
  curTask.value = item;
  const { status } = item;
  detectedFaces.value = item.target_info;
  replaceFaces.value = item.source_pics.map((item) => {
    if (!item) return null;
    return {
      asset_id: '',
      image_path: item.Image,
      info_path: item.Info_path,
      label: '',
      creator: '',
    };
  });
  curAssetId.value = item.asset_id;
  curVidePath.value = item.origin_video;
  if (item.setting) {
    Object.assign(faceParams, item.setting);
  }
  if (status === 2) {
    faceBus.emit(JSON.stringify(item));
  }
};
</script>
