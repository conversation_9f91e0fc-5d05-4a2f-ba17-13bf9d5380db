<template>
  <common-view
    class="h-screen face-swap"
    :hide-right="true"
    :store="aiFaceSwapStore"
  >
    <template #views>
      <slide-page class="min-w-[1360px]">
        <template #left>
          <div class="bg-white rounded-default relative flex-1 p-[20px] flex flex-col">
            <left-action />
          </div>
        </template>
        <template #right>
          <div class="w-full flex flex-1 flex-col overflow-hidden bg-white rounded-default">
            <div>
              <div class="flex justify-between items-center p-[20px]">
                <span class="font-bold text-lg">Preview</span>
                <DownloadIcon
                  class="cursor-pointer" size="16" color="var(--aix-text-color-brand)"
                  @click="downloadVideo"
                />
              </div>
              <div class="mx-[20px] h-[274px] box-border bg-[#F0F1F6] rounded-[4px]">
                <video-viewer class="h-[100%] bg-[#000] rounded-[8px]" :video="previewVideo" />
              </div>
            </div>
            <div class="flex justify-between items-center my-[20px] px-[20px]">
              <span class="font-bold text-lg">Task List</span>
              <RefreshIcon
                class="cursor-pointer" size="16" color="var(--aix-text-color-brand)"
                @click="getTasks"
              />
            </div>
            <task-list />
          </div>
        </template>
      </slide-page>
    </template>
  </common-view>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { RefreshIcon, DownloadIcon } from 'tdesign-icons-vue-next';
import CommonView from 'common/components/Layout/CommonView.vue';
import { FaceTask } from 'common/service/creative/aigc_toolkit/type';
import { useAIFaceSwapStore } from '@/store/creative/toolkit/ai_face_swap.store';
import SlidePage from '../components/SlidePage.vue';
import LeftAction from './components/LeftAction/index.vue';
import TaskList from './components/Task/index.vue';
import VideoViewer from '../components/VideoViewer.vue';
import { FILE_CDN_COM as CDN } from 'common/config';
import { faceBus } from '../utils/event';
import { downloadVideo as download } from 'common/service/creative/aigc_toolkit/face_swap';

const aiFaceSwapStore = useAIFaceSwapStore();
const { getTasks } = aiFaceSwapStore;

const previewVideo = ref();

// 选择任务，预览视频
faceBus.on((event) => {
  const task = JSON.parse(event) as FaceTask;
  previewVideo.value = {
    url: `${CDN}/${task.target_video}`,
  };
});

const downloadVideo = () => {
  if (previewVideo.value?.url) download(previewVideo.value.url);
};
</script>
<style lang="scss">
.face-swap {
  .right-content {
    max-width: 560px !important;
  }
}
</style>
