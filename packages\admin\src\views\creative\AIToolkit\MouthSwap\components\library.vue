<template>
  <div class="presenter-box h-[200px] flex-1 flex flex-col">
    <div class="flex justify-between mb-[12px]">
      <span class="font-bold">Presenter Library</span>
      <div class="flex items-center cursor-pointer text-brand" @click="addPresenter">
        <AddIcon />
        <span>Add Presenter</span>
      </div>
    </div>
    <div class="flex flex-1 flex-col h-[200px]">
      <t-select
        v-model="filter.label"
        label="Label:"
        class="w-[160px]"
        placeholder="select label"
        @change="getLibrary"
      >
        <t-option
          v-for="item in labelList" :key="item.value" :label="item.label"
          :value="item.value"
        />
      </t-select>
      <div v-loading="libraryLoading" class="flex flex-1 flex-wrap content-start overflow-y-auto pr-[12px]">
        <template v-if="videoList.length > 0">
          <div v-for="item in videoList" :key="item.id" class="relative p-[6px] w-[188px] h-[120px]">
            <video
              :src="item.url" :class="'w-full h-full object-cover' +
                (activeLib === item.id ? ' border-brand border-[2px]' : 'border-[1px]')" @click="selectVideo(item)"
            />
            <CloseCircleFilledIcon
              v-if="item.username !== 'admin'"
              color="red" size="16px" class="absolute top-[2px] right-[2px] cursor-pointer"
              @click="deleteItem(item)"
            />
          </div>
        </template>
        <div v-else-if="!libraryLoading" class="text-black-placeholder text-center w-full pt-[24px]">No Data</div>
      </div>
    </div>
  </div>
  <t-dialog
    v-model:visible="showDialog"
    header="Add Present"
    width="600"
    :destroy-on-close="true"
    :confirm-on-enter="true"
  >
    <t-form
      ref="formRef" :data="formData" :rules="rules"
      class="min-h-[350px]"
    >
      <t-form-item label="Present" name="path" required>
        <uploader
          accept="video/mp4, video/mov, video/quicktime"
          tips="Supported format: .mp4/.mov"
          :max-duration="120"
          file-path="ai_toolkit/lipsync_warehouse"
          @change="onFileChange"
        />
      </t-form-item>
      <t-form-item label="Label" name="label">
        <t-select
          v-model="formData.label"
          class="w-[320px]" clearable
          placeholder="select label"
        >
          <t-option
            v-for="item in labelList" :key="item.value" :label="item.value"
            :value="item.value"
          />
        </t-select>
      </t-form-item>
    </t-form>
    <template #confirmBtn>
      <t-button
        class="ml-[10px]" theme="primary" :loading="libraryUploading"
        @click="onConfirmAdd"
      >
        Submit
      </t-button>
    </template>
  </t-dialog>
  <t-dialog
    v-model:visible="deleteDialog"
    header="Delete"
    body="Confirm delete presenter?"
    @confirm="confirmDelete"
  />
</template>
<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { AddIcon, CloseCircleFilledIcon } from 'tdesign-icons-vue-next';
import {
  getLipLibrary, addLipLibrary, deleteLipLibrary, lipLabels,
} from 'common/service/creative/aigc_toolkit/mouth_swap';
import { LipLibrary } from 'common/service/creative/aigc_toolkit/type';
import Uploader from '../../components/Uploader/index.vue';
import { UploadFile as TdUploadFile } from 'tdesign-vue-next/es/upload/type';
import { UploadReq } from 'common/components/FileUpload';
import { MessagePlugin } from 'tdesign-vue-next';
import { useAIMouthSwapStore } from '@/store/creative/toolkit/ai_mouth_swap.store';

const { activeLib } = storeToRefs(useAIMouthSwapStore());

const emits = defineEmits(['select']);

// 添加预置库
const showDialog = ref(false);
const addPresenter = () => {
  formData.path = '';
  formData.label = '';
  showDialog.value = true;
};

const videoList = ref<LipLibrary[]>([]);

const labelList = ref<{ label: string, value: string }[]>([]);
const getLipLabels = async () => {
  const labels = (await lipLabels()).map(item => ({ label: item, value: item }));
  labelList.value = [
    { label: 'All', value: 'All' },
  ].concat(labels);
};

const filter = reactive({
  label: 'All',
});

const selectVideo = (item: LipLibrary) => {
  emits('select', item);
  activeLib.value = item.id;
};

const libraryLoading = ref(false);
const getLibrary = async () => {
  libraryLoading.value = true;
  videoList.value = await getLipLibrary(filter.label);
  libraryLoading.value = false;
};

const formData = reactive({
  path: '',
  label: '',
});

const rules = {
  path: [{
    required: true,
    validator: () => {
      if (formData.path !== '') return true;
      return {
        message: 'Please upload video.', result: false, type: 'error',
      };
    },
  }],
  label: [{
    required: true,
    validator: (val: string) => {
      if (val) return true;
      return {
        message: 'Please select label.', result: false, type: 'error',
      };
    },
  }],
};

const formRef = ref(); // 表单对象

const onFileChange = (fileList: TdUploadFile[]) => {
  if (fileList.length === 0) {
    formData.path = '';
    return;
  }
  const res = fileList[0].response as UploadReq;
  formData.path = res.key as string;
};

const libraryUploading = ref(false);
const onConfirmAdd = async () => {
  const validateRes = await formRef.value.validate();
  if (validateRes !== true) return;

  libraryUploading.value = true;
  const res = await addLipLibrary(formData.label, formData.path);
  libraryUploading.value = false;
  if (res.code === 0) {
    MessagePlugin.success('Create success');
    showDialog.value = false;
    getLibrary();
  } else {
    MessagePlugin.error(res.message);
  }
};

const deleteItem = (item: LipLibrary) => {
  target.value = item;
  deleteDialog.value = true;
};

const deleteDialog = ref(false);
const target = ref<LipLibrary>();
const confirmDelete = async () => {
  if (!target.value) return;
  const res = await deleteLipLibrary(target.value?.id);
  if (res.code === 0) {
    MessagePlugin.success('Delete success');
    deleteDialog.value = false;
    getLibrary();
  } else {
    MessagePlugin.error('Delete fail');
  }
};

onMounted(() => {
  getLipLabels();
  getLibrary();
});
</script>
