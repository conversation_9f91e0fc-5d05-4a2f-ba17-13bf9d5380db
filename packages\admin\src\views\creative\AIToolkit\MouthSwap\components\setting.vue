<template>
  <div class="flex flex-col w-[808px] lip-setting">
    <div class="flex items-center mb-[12px]">
      <span class="font-bold text-lg">Setting</span>
      <t-tooltip :content="uploadTip">
        <InfoCircleIcon size="16" class="ml-[6px]" />
      </t-tooltip>
    </div>
    <div class="flex mb-[24px]">
      <div class="mr-[24px]">
        <div class="font-bold mb-[12px]">Presenter</div>
        <t-form
          ref="videoFormRef" :data="formData" :label-width="0"
          :rules="rules"
        >
          <t-form-item class="mouth-video-form flex flex-col items-center" name="video">
            <Uploader
              ref="videoUploader"
              accept="video/mp4, video/mov, video/quicktime"
              tips="Supported format: .mp4/.mov"
              :max-duration="120"
              file-path="ai_toolkit/mouth_swap/face"
              :use-name="true"
              @change="videoChange"
            />
          </t-form-item>
        </t-form>
      </div>
      <div>
        <div class="font-bold mb-[12px]">Audio</div>
        <t-form
          ref="audioFormRef" :data="formData" :label-width="0"
          :rules="rules"
        >
          <t-form-item class="mouth-audio-form flex flex-col" name="audio">
            <Uploader
              ref="audioUploader"
              accept="audio/wav, audio/mp3, audio/mpeg, audio/ogg, audio/webm, audio/flac"
              tips="Supported format: .wav/.mp3"
              :max-duration="120"
              :use-name="true"
              file-path="ai_toolkit/mouth_swap/audio"
              @change="audioChange"
            />
          </t-form-item>
        </t-form>
      </div>
    </div>
    <library @select="onSelect" />
    <div class="flex justify-end items-end mt-[12px]">
      <t-button theme="primary" @click="generate">Generate</t-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { storeToRefs } from 'pinia';
import { MessagePlugin } from 'tdesign-vue-next';
import Uploader from '../../components/Uploader/index.vue';
import Library from './library.vue';
import { UploadFile as TdUploadFile } from 'tdesign-vue-next/es/upload/type';
import { UploadReq } from 'common/components/FileUpload';
import { useAIMouthSwapStore } from '@/store/creative/toolkit/ai_mouth_swap.store';
import { videoGenerate } from 'common/service/creative/aigc_toolkit/mouth_swap';
import { MouthTask, LipLibrary } from 'common/service/creative/aigc_toolkit/type';
import { mouthBus } from '../../utils/event';
import { FILE_CDN_COM as CDN } from 'common/config';
import { InfoCircleIcon } from 'tdesign-icons-vue-next';

const { formData, getTasks } = useAIMouthSwapStore();
const { activeLib } = storeToRefs(useAIMouthSwapStore());

const uploadTip = 'Supported format:mp4/.mov; The file size should be lower than 100M. Notice: '
  + '1. Only the first 90 seconds of the video & audio will be used'
  + ' for further processing, and the original sound will be automatically removed.'
  + ' 2. The video processed will be automatically converted to 720p, 24fps format.';

const videoFormRef = ref(); // 视频表单对象
const audioFormRef = ref(); // 音频表单对象

const rules = {
  video: [{
    required: true,
    message: 'Video is required',
  }],
  audio: [{
    required: true,
    message: 'Audio is required',
  }],
};

const videoUploader = ref();
const videoChange = async (fileList: TdUploadFile[], validVideo: boolean) => {
  if (!validVideo) return;
  if (fileList.length === 0) {
    formData.video = '';
    activeLib.value = 0;
    return;
  }

  const file = fileList[0].response as UploadReq;
  formData.video = file.key as string;
  activeLib.value = 0;
};

const audioUploader = ref();
const audioChange = async (fileList: TdUploadFile[], validAudio: boolean) => {
  if (!validAudio) return;
  if (fileList.length === 0) {
    formData.audio = '';
    return;
  }

  const file = fileList[0].response as UploadReq;
  formData.audio = file.key as string;
};

const onSelect = (item: LipLibrary) => {
  const videoName = item.video_path.split('/').pop();
  videoUploader.value.setFiles([
    { type: 'video/mp4', status: 'success', name: videoName, url: item.url },
  ]);
  formData.video = item.video_path;
};

const generate = async () => {
  const res1 = await videoFormRef.value.validate();
  const res2 = await audioFormRef.value.validate();

  if (res1 === true && res2 === true) {
    const res = await videoGenerate({
      face: formData.video,
      audio: formData.audio,
    });
    if (res.code === 0) {
      MessagePlugin.success('generate success');
      getTasks();
    } else {
      MessagePlugin.error(res.message);
    }
  }
};

const eventBus = ref();
onMounted(() => {
  eventBus.value = mouthBus.on((name, data: MouthTask) => {
    if (name === 'selectTask') {
      formData.video = data.origin_video;
      const videoName = data.origin_video.split('/').pop();
      videoUploader.value.setFiles([
        { type: 'video/mp4', status: 'success', name: videoName, url: `${CDN}/${data.origin_video}` },
      ]);

      const audioName = data.origin_audio.split('/').pop();
      formData.audio = data.origin_audio;
      audioUploader.value.setFiles([
        { type: 'audio/wav', status: 'success', name: audioName, url: `${CDN}/${data.origin_audio}` },
      ]);
    }
  });
});

onUnmounted(() => {
  eventBus.value();
});
</script>
<style lang="scss">
.mouth-video-form {
  .t-upload__dragger {
    width: 420px;
    height: 240px;
  }
}

.presenter-box {
  border: 1px solid #eee;
  padding: 16px;
}

.lip-setting {
  .mouth-video-form {
    .t-upload__dragger {
      height: 180px;
      width: 300px;
      overflow-y: hidden;
    }
  }
}

.mouth-audio-form {
  .t-upload__dragger {
    width: 360px;
    height: 100px;
  }
  .media-box {
    height: 65%;
  }
}
</style>
