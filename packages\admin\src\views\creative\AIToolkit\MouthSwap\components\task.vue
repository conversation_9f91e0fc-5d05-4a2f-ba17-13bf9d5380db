<template>
  <t-loading :loading="taskLoading" class="overflow-y-auto">
    <video-tasks :task-list="taskList" :active-id="curTask?.task_id" :select-task="selectTask" />
  </t-loading>
  <template v-if="taskList.length === 0 && !taskLoading">
    <div class="text-black-placeholder text-center w-full flex flex-center h-full">No Data</div>
  </template>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useAIMouthSwapStore } from '@/store/creative/toolkit/ai_mouth_swap.store';
import { MouthTask } from 'common/service/creative/aigc_toolkit/type';
import { FILE_CDN_COM as CDN } from 'common/config';
import { mouthBus } from '../../utils/event';
import VideoTasks from '../../components/VideoTasks.vue';

const { getTasks, previewVideo } = useAIMouthSwapStore();
const { taskLoading, taskList, activeLib } = storeToRefs(useAIMouthSwapStore());

const curTask = ref();
const selectTask = (item: MouthTask) => {
  curTask.value = item;
  const { status } = item;
  if (status === 2) {
    previewVideo.url = `${CDN}/${item.target_video}`;
  }
  mouthBus.emit('selectTask', item);
  activeLib.value = 0;
};

onMounted(() => {
  getTasks();
});
</script>
<style scoped>
.t-loading__parent {
  position: initial;
}
</style>
