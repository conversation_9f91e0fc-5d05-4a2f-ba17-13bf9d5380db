<template>
  <common-view
    class="h-screen mouth-swap"
    :hide-right="true"
  >
    <template #views>
      <div class="flex flex-[1] h-[100px] p-[20px] bg-white rounded-default overflow-hidden">
        <setting />
        <div class="divider" />
        <div class="flex flex-col flex-1 h-full">
          <div class="mb-[24px]">
            <div class="flex justify-between items-center mb-[24px]">
              <span class="font-bold text-lg">Preview</span>
              <DownloadIcon
                class="cursor-pointer" size="16" color="var(--aix-text-color-brand)"
                @click="downloadVideo"
              />
            </div>
            <div class="min-h-[452px] box-border flex flex-center p-[36px]">
              <video-viewer
                v-if="previewVideo.url"
                class="h-[380px] w-[640px] bg-[#000] rounded-[8px]"
                :video="previewVideo"
              />
              <div v-else class="text-black-placeholder text-center w-full">No Data</div>
            </div>
          </div>
          <div class="flex flex-col flex-grow overflow-hidden relative">
            <div class="flex justify-between font-bold text-lg mb-[12px]">
              <span>Task List</span>
              <RefreshIcon
                class="cursor-pointer" size="16" color="var(--aix-text-color-brand)"
                @click="getTasks"
              />
            </div>
            <task />
          </div>
        </div>
      </div>
    </template>
  </common-view>
</template>
<script setup lang="ts">
import CommonView from 'common/components/Layout/CommonView.vue';
import Setting from './components/setting.vue';
import VideoViewer from '../components/VideoViewer.vue';
import { DownloadIcon, RefreshIcon } from 'tdesign-icons-vue-next';
import { useAIMouthSwapStore } from '@/store/creative/toolkit/ai_mouth_swap.store';
import Task from './components/task.vue';
import { downloadVideo as download } from 'common/service/creative/aigc_toolkit/face_swap';

const { getTasks, previewVideo } = useAIMouthSwapStore();

const downloadVideo = () => {
  if (previewVideo.url) download(previewVideo.url);
};
</script>
<style lang="scss">
.mouth-swap {
  .divider {
    width: 1px;
    background: #eee;
    margin: 20px 32px;
    box-sizing: content-box;
  }
}
</style>
