<template>
  <t-progress
    class="cursor-pointer"
    theme="circle"
    :percentage="percentage"
    :status="'success'"
    color="var(--aix-text-color-brand)"
    size="18"
    :stroke-width="2"
  >
    <template #label>
      <audio
        ref="audioRef"
        class="hidden"
        :src="url"
        @timeupdate="updatePercentage"
        @ended="onAudioEnded"
      />
      <template v-if="loading">
        <t-loading size="small" />
      </template>
      <template v-else>
        <CaretRightSmallIcon
          v-if="isPaused"
          size="12"
          color="var(--aix-text-color-brand)"
          @click="startPlay"
        />
        <PauseIcon
          v-if="!isPaused"
          size="12"
          color="var(--aix-text-color-brand)"
          @click="pause"
        />
      </template>
    </template>
  </t-progress>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { CaretRightSmallIcon, PauseIcon } from 'tdesign-icons-vue-next';

const isPaused = ref(true);
const percentage = ref(0);

const props = defineProps({
  url: {
    type: String,
    default: '',
  },
  loading: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['audio']);

const audioRef = ref<HTMLAudioElement | null>(null);

const startPlay = () => new Promise<void>((resolve) => {
  if (!audioRef.value) {
    resolve();
    return;
  }
  if (!props.url) {
    // 没有音频，请求tts接口
    emit('audio');
  } else {
    audioRef.value.removeEventListener('ended', () => {});
    audioRef.value.addEventListener('ended', () => {
      resolve();
    });
    audioRef.value.play();
    isPaused.value = false;
  }
});

const pause = () => {
  if (!audioRef.value) return;
  audioRef.value.pause();
  isPaused.value = true;
};

// 更新进度条
const updatePercentage = () => {
  if (!audioRef.value) return;

  percentage.value = (audioRef.value.currentTime / audioRef.value.duration) * 100;
};

// 音频播放结束
const onAudioEnded = () => {
  isPaused.value = true;
  percentage.value = 0;
};

defineExpose({
  startPlay,
});
</script>
