<template>
  <div class="text-voices flex-1 flex flex-col mb-[12px]">
    <div class="font-bold text-lg mb-[12px]">Audio&Voices</div>
    <div class="flex justify-between mb-[12px]">
      <div class="flex action-left">
        <div class="flex flex-center mr-[18px]">
          <t-checkbox
            v-model="checkAll"
            :indeterminate="indeterminate"
            :disabled="textVoices.length === 0"
            class="ml-[6px]"
            @change="onAllChange"
          />
          <span>Select all</span>
        </div>
        <t-button theme="primary" :disabled="textVoices.length === 0" @click="playAll">
          <template #icon><play-circle-stroke-icon /></template>
          Play All
        </t-button>
      </div>
      <div class="flex justify-end">
        <t-select
          v-model="language"
          :disabled="vttLoading"
          class="w-[150px] ml-[12px]"
          :options="LANGUAGES"
          @change="onLanguageChange"
        />
        <t-button
          class="ml-[12px]" theme="default" :disabled="!formData.video || vttLoading"
          @click="regenerateVt"
        >
          <template #icon><rotate-icon /></template>
          Regenerate Text
        </t-button>
        <t-dropdown
          class="ml-[12px]" :options="actionProps" :min-column-width="120"
          trigger="click" :disabled="!canAction"
          @click="selectAction"
        >
          <t-button theme="default" variant="outline" :disabled="!canAction">
            Action
            <template #suffix>
              <chevron-down-icon />
            </template>
          </t-button>
        </t-dropdown>
      </div>
    </div>
    <div
      class="flex-grow border rounded-[8px] p-[6px] overflow-y-auto h-[200px]"
      :class="checkRes.time ? 'border-[#EBECF1]' : 'border-error-primary'"
    >
      <div v-if="textVoices.length === 0 || vttLoading" class="flex text-black-placeholder flex-center w-full h-full">
        <t-loading v-if="vttLoading" text="Texts will be ready in a few minutes..." />
        <template v-else>
          <div v-if="uploaded" class="flex flex-center">
            <t-button variant="text" theme="primary" @click="() => addVoice()">click to add</t-button>
          </div>
          <span v-else>please upload video</span>
        </template>
      </div>
      <t-t-s v-else ref="ttsRef" />
    </div>
    <span v-if="!checkRes.time && !vttLoading" class="text-error-primary">
      The time should not exceed the total video time, and the time for different texts should not overlap.
    </span>
  </div>
</template>
<script lang="ts" setup>
/* eslint-disable no-param-reassign */
import { ref, computed, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { DropdownProps, DialogPlugin } from 'tdesign-vue-next';
import { PlayCircleStrokeIcon, RotateIcon, ChevronDownIcon } from 'tdesign-icons-vue-next';
import { LANGUAGES, SPEAKERS, TONES } from '../const';
import { useAIVideoDubStore } from '@/store/creative/toolkit/ai_video_dub.store';
import TTS from './tts.vue';
import { dubBus } from '../../utils/event';

const { formData, addVoice, regenerateVt } = useAIVideoDubStore();
const { language, textVoices, vttLoading, checkedIndexList, checkRes } = storeToRefs(useAIVideoDubStore());

const uploaded = computed(() => !!formData.video);
const canAction = computed(() => checkedIndexList.value.length > 0);
const actionProps = computed(() => [
  {
    content: 'Speaker',
    value: 'speaker',
    children: SPEAKERS.map(item => ({
      ...item,
      type: 'speaker',
      content: item.label,
    })),
  },
  {
    content: 'Tone',
    value: 'tone',
    children: TONES.map(item => ({
      ...item,
      type: 'tone',
      content: item.label,
    })),
  },
  {
    content: 'Delete',
    value: 'delete',
  },
]);

// 选择全部
const checkAll = ref(false);

// 是否半选
const indeterminate = computed(() => !textVoices.value.every(item => item.checked)
  && textVoices.value.some(item => item.checked));

const onAllChange = (val: boolean) => {
  textVoices.value.forEach(item => item.checked = val);
};

// 监听选择变化
watch(() => textVoices.value, () => {
  if (textVoices.value.length === 0) checkAll.value = false;
  else checkAll.value = textVoices.value.every(item => item.checked);
}, {
  deep: true,
});

const selectAction: DropdownProps['onClick'] = (data) => {
  const { type, value } = data;
  if (value === 'delete') {
    const dialog = DialogPlugin.confirm({
      body: 'Are you sure to delete it?',
      confirmBtn: {
        content: 'Confirm',
      },
      cancelBtn: {
        content: 'Cancel',
      },
      onConfirm: () => {
        textVoices.value = textVoices.value.filter((item, index) => !checkedIndexList.value.includes(index));
        dialog.hide();
      },
    });
  }
  if (type === 'speaker') {
    dubBus.emit('speaker', value);
  }
  if (type === 'tone') {
    dubBus.emit('tone', value);
  }
};

// 切换语言，重新生成字幕
const onLanguageChange = () => {
  regenerateVt();
};

const ttsRef = ref();
const playAll = () => {
  ttsRef.value.getAllAudio(true);
};

defineExpose({
  getAllAudio: () => ttsRef.value.getAllAudio(false),
});
</script>
