<template>
  <template v-for="(item, index) in textVoices" :key="index">
    <div
      class="tts-item flex p-[12px] rounded-[8px] mb-[12px]"
      :class="activeIndex === index ? 'bg-[#F0F1F6]' : ''"
      @click="activeIndex = index"
    >
      <div class="flex-[1]">
        <div class="flex items-center mb-[12px]">
          <t-checkbox v-model="item.checked" />
          <div class="flex mr-[12px]">
            <div v-show="item?.timeEditable">
              <t-input
                ref="timeInputs"
                v-model="item.timeRange"
                class="w-[220px]"
                @blur="changeTimeEditable(item, index)"
              />
            </div>
            <span v-show="!item.timeEditable" @click="changeTimeEditable(item, index)">
              {{ item.timeRange }}
            </span>
          </div>
          <t-select
            v-model="item.voice_id"
            class="mr-[12px]"
            :borderless="true"
            style="width: 100px"
            auto-width
            placeholder=""
            size="small"
            :options="SPEAKERS"
            @change="() => onSpeakerChange(index)"
          />
          <t-select
            v-model="item.style"
            :borderless="true"
            style="width: 100px"
            auto-width
            placeholder=""
            size="small"
            :options="TONES"
            @change="() => onStyleChange(index)"
          />
        </div>
        <div class="flex items-center">
          <audio-item
            ref="audioItemRefs"
            :url="item.audio_url"
            :loading="item.loading || false"
            @audio="() => getAudio(index, true)"
          />
          <div class="flex ml-[12px]">
            <div v-show="item?.textEditable">
              <t-input
                ref="textInputs"
                v-model="item.text"
                class="w-[240px]"
                placeholder="Please input"
                @blur="changeTextEditable(item, index)"
              />
            </div>
            <span v-show="!item.textEditable" @click="changeTextEditable(item, index)">
              {{ item.text }}
            </span>
          </div>
        </div>
      </div>
      <div class="flex items-center">
        <t-button
          variant="text"
          class="m-1 w-[30px] h-[30px] bg-gray-primary"
          @click="deleteVoice(index)"
        >
          <MinusIcon color="var(--aix-text-color-gray-primary)" size="24px" />
        </t-button>
        <t-button
          variant="text"
          class="m-1 w-[30px] h-[30px] bg-[#DBECFF]"
          @click="addVoice(index)"
        >
          <PlusIcon color="#3B5FE7" size="24px" />
        </t-button>
      </div>
    </div>
  </template>
</template>
<script setup lang="ts">
/* eslint-disable no-param-reassign */
import { onMounted, onUnmounted, ref } from 'vue';
import { MinusIcon, PlusIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { SPEAKERS, TONES } from '../const';
import AudioItem from './audioItem.vue';
import { storeToRefs } from 'pinia';
import { useAIVideoDubStore } from '@/store/creative/toolkit/ai_video_dub.store';
import { dubTTS } from 'common/service/creative/aigc_toolkit/ai_dub';
import { TextVoice } from 'common/service/creative/aigc_toolkit/type';
import { validateTimeRange } from '../utils';
import { dubBus } from '../../utils/event';

const { deleteVoice, addVoice } = useAIVideoDubStore();
const { language, textVoices, activeIndex } = storeToRefs(useAIVideoDubStore());

// 时间输入框
const timeInputs = ref();
const changeTimeEditable = (item: TextVoice, index: number) => {
  const textInput = timeInputs.value[index];
  if (!item.timeEditable) {
    item.timeEditable = true;
    setTimeout(() => {
      textInput.focus();
    }, 100);
  } else {
    // 校验时间合法性
    if (!validateTimeRange(item.timeRange)) {
      MessagePlugin.warning('TimeRange is invalid');
    } else {
      item.timeEditable = false;
    }
  }
};

// 文本输入框
const textInputs = ref();
const originText = ref('');
const changeTextEditable = (item: TextVoice, index: number) => {
  const textInput = textInputs.value[index];
  if (!item.textEditable) {
    item.textEditable = true;
    originText.value = item.text;
    setTimeout(() => {
      textInput.focus();
    }, 100);
  } else {
    if (!item.text) {
      MessagePlugin.warning('Text is required');
    } else {
      item.textEditable = false;
      if (originText.value !== item.text) {
        getAudio(index);
      }
    }
  }
};

// 音频播放处理
const audioItemRefs = ref();
const getAudio = async (index: number, play = false): Promise<void> => {
  const target = textVoices.value[index];
  target.loading = true;
  const res = await dubTTS({
    voice_id: target.voice_id,
    text: target.text,
    style: target.style,
    language: language.value,
  });
  target.audio_url = res.voice_file_url;
  target.loading = false;
  const audioItem = audioItemRefs.value[index];
  if (play) {
    setTimeout(() => {
      audioItem.startPlay();
    }, 500);
  }
};

const onSpeakerChange = (index: number) => {
  getAudio(index);
};

const onStyleChange = (index: number) => {
  getAudio(index);
};

// 消息通信，处理批量speaker、tone
const bus = ref();
onMounted(() => {
  bus.value = dubBus.on((name, val: string) => {
    if (name === 'speaker' || name === 'tone') {
      // 批量修改speaker、tone
      textVoices.value.forEach((item, index) => {
        if (name === 'speaker') item.voice_id = val;
        if (name === 'tone') item.style = val;
        getAudio(index);
      });
    }
  });
});

// 获取全部音频，判断是否需要顺序播放
const getAllAudio = async (play = true) => {
  const pList: Promise<void>[] = [];
  textVoices.value.forEach((item, index) => {
    if (item.audio_url) return;
    pList.push(getAudio(index));
  });
  if (pList.length > 0) {
    await Promise.all(pList); // 等待全部音频获取完成
  }

  if (play) {
    for (let i = 0; i < textVoices.value.length; i++) {
      const audioItem = audioItemRefs.value[i];
      await audioItem.startPlay();
    }
  }
};

onUnmounted(() => {
  bus.value();
});

defineExpose({
  getAllAudio,
});
</script>
