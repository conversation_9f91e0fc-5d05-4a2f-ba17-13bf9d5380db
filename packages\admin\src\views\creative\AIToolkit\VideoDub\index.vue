<template>
  <common-view
    class="h-screen mouth-swap"
    :hide-right="true"
  >
    <template #views>
      <div class="flex flex-1 p-[20px] bg-white rounded-default overflow-y-auto">
        <setting ref="settingRef" />
        <div class="divider" />
        <div class="flex flex-col flex-[4] h-full">
          <div class="mb-[24px]">
            <div class="flex justify-between items-center mb-[24px]">
              <span class="font-bold text-lg">Preview</span>
              <DownloadIcon
                class="cursor-pointer" size="16" color="var(--aix-text-color-brand)"
                @click="downloadVideo"
              />
            </div>
            <div class="min-h-[300px] box-border flex flex-center px-[24px] py-[12px]">
              <video-viewer
                v-if="previewVideo.url"
                class="h-[280px] w-[450px] bg-[#000] rounded-[8px]"
                :video="previewVideo"
              />
              <div v-else class="text-black-placeholder text-center w-full">No Data</div>
            </div>
          </div>
          <div class="flex relative flex-col flex-grow overflow-hidden">
            <div class="flex justify-between font-bold text-lg mb-[12px]">
              <span>Task List</span>
              <RefreshIcon
                class="cursor-pointer" size="16" color="var(--aix-text-color-brand)"
                @click="getTasks"
              />
            </div>
            <task ref="taskRef" />
          </div>
        </div>
      </div>
    </template>
  </common-view>
</template>
<script setup lang="ts">
import { ref, onUnmounted, onMounted } from 'vue';
import CommonView from 'common/components/Layout/CommonView.vue';
import Setting from './setting.vue';
import Task from './tasks.vue';
import VideoViewer from '../components/VideoViewer.vue';
import { DownloadIcon, RefreshIcon } from 'tdesign-icons-vue-next';
import { useAIVideoDubStore } from '@/store/creative/toolkit/ai_video_dub.store';
import { getDubTasks } from 'common/service/creative/aigc_toolkit/ai_dub';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { DubTask, VTTDetail } from 'common/service/creative/aigc_toolkit/type';

const { query }  = useRoute();
const { getTasks, previewVideo, stopPoll, getTextVoices } = useAIVideoDubStore();
const { curVttId, curTask } = storeToRefs(useAIVideoDubStore());

const settingRef = ref(); // 设置对象
const taskRef = ref(); // 任务列表对象

// 下载压缩包
const downloadVideo = () => {
  if (curTask.value) {
    taskRef.value.onDownload(curTask.value as DubTask);
  }
};

onMounted(async () => {
  const { vtt_id: vttId, task_id: taskId } = query;
  if (taskId) {
    const { tasks = [] } = await getDubTasks(Number(taskId));
    if (tasks.length > 0) {
      taskRef.value.selectTask(tasks[0]);
    }
  } else if (vttId) {
    curVttId.value = Number(vttId);
    const vtt = await getTextVoices() as VTTDetail;
    settingRef.value.setFiles(vtt.video_url, vtt.media_name);
  }
});

onUnmounted(() => {
  stopPoll();
});
</script>
<style lang="scss">
.mouth-swap {
  .divider {
    width: 1px;
    background: #eee;
    margin: 20px 20px;
    box-sizing: content-box;
  }
}
</style>
