<template>
  <div class="flex flex-col flex-[5]">
    <div class="mb-[24px]">
      <div class="flex justify-between mb-[24px]">
        <div class="flex flex-center">
          <span class="font-bold text-lg">Original Video</span>
          <span class="ml-[12px] text-gray-primary">Supported format: .mp4/.mov</span>
        </div>
        <div v-if="false">
          <t-button theme="primary" variant="text" @click="selectMedia">
            <template #icon><add-icon /></template>
            Add from library
          </t-button>
        </div>
      </div>
      <t-form
        ref="videoFormRef" :data="formData" :label-width="0"
        :rules="rules"
      >
        <t-form-item class="mouth-video-form flex flex-col" name="video">
          <Uploader
            ref="videoUploader"
            :accept="mediaAccept"
            :max-size="100"
            file-path="ai_toolkit/video_dub/video"
            :use-name="true"
            @change="videoChange"
            @video-duration="videoDuration"
          />
        </t-form-item>
      </t-form>
    </div>
    <TextVoices ref="textVoicesRef" />
    <div class="flex justify-end items-end">
      <t-button
        theme="primary"
        :disabled="!checkRes.total && textVoices.length > 0"
        :loading="generateLoading"
        @click="generate"
      >
        Generate
      </t-button>
    </div>
    <MaterialSelect
      ref="materialSelect"
      init-type="aix"
      :max-img-num="0"
      :min-img-num="0"
      :max-video-num="1"
      :min-video-num="1"
      :disabled-media="true"
      :need-validate="false"
      :submit-loading="selectLoading"
      @select="onMediaSelect"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { storeToRefs } from 'pinia';
import { MessagePlugin } from 'tdesign-vue-next';
import { AddIcon } from 'tdesign-icons-vue-next';
import Uploader from '../components/Uploader/index.vue';
import TextVoices from './TextVoices/index.vue';
import { UploadFile as TdUploadFile } from 'tdesign-vue-next/es/upload/type';
import { UploadReq } from 'common/components/FileUpload';
import { useAIVideoDubStore } from '@/store/creative/toolkit/ai_video_dub.store';
import { generateVideo, sigUrlToCdn } from 'common/service/creative/aigc_toolkit/ai_dub';
import { DubTask } from 'common/service/creative/aigc_toolkit/type';
import { dubBus } from '../utils/event';
import MaterialSelect from '@/views/trade/ads_creation/common/components/Media/MaterialSelect.vue';
import { MaterialItem } from '@/views/trade/ads_creation/common/template/media.type';
import { getPreviewUrl } from 'common/service/creative/library/get-preview-url';

const { formData, getTasks, clearDub } = useAIVideoDubStore();
const { checkRes, textVoices, curVttId, videoValid, canPoll, language } = storeToRefs(useAIVideoDubStore());

const mediaAccept = ref('video/mp4, video/mov, video/quicktime'); // 支持的视频格式
const videoFormRef = ref(); // 视频表单对象
const textVoicesRef = ref(); // 文本语音设置对象
const maxSize = ref(100 * 1024 * 1024); // 视频最大100M

const rules = {
  video: [{
    required: true,
    message: 'video is required',
  }],
};

const videoUploader = ref();
const videoChange = async (fileList: TdUploadFile[], validVideo: boolean, validSize: boolean) => {
  videoValid.value = validVideo && validSize;
  if (!videoValid.value) return;

  if (fileList.length === 0) {
    formData.video = '';
    formData.duration = 0;
    clearDub();
    return;
  }

  canPoll.value = true;
  const file = fileList[0].response as UploadReq;
  formData.video = file.url as string;
};

// 视频加载完成事件
const videoDuration = (duration: number) => {
  formData.duration = duration;
};

// 生成视频，如果有没生成的语音，需要先生成
const generateLoading = ref(false);
const generate = async () => {
  generateLoading.value = true;
  await textVoicesRef.value.getAllAudio();
  const res = await generateVideo(curVttId.value, textVoices.value);
  generateLoading.value = false;
  if (res.code === 0) {
    MessagePlugin.success('generate success');
    getTasks();
  } else {
    MessagePlugin.error(`generate fail: ${res.message}`);
  }
};

// 素材库选择
const materialSelect = ref();
const selectMedia = () => {
  selectLoading.value = false;
  materialSelect.value.show();
};
const selectLoading = ref(false);
const onMediaSelect = async (
  sData: MaterialItem[],
  depotToken: { arthubCode: string, publicToken: string, type: string },
) => {
  const { name, asset_id: assetId, duration, material_ext: ext, poster, formate } = sData[0];
  const size = Number(ext.universal.size) || 0;

  if (size > 0 && size > maxSize.value) {
    MessagePlugin.error('Video size cannot exceed 100M');
    materialSelect.value.hideDialog();
    return;
  }

  if (!mediaAccept.value.includes(formate as string)) {
    MessagePlugin.error('Video format is not supported');
    materialSelect.value.hideDialog();
    return;
  }

  selectLoading.value = true;

  // 获取签名视频url
  let videoUrl = '';
  let fileName = '';
  if (depotToken.publicToken) {
    videoUrl = await getPreviewUrl({
      arthubCode: depotToken.arthubCode,
      publicToken: depotToken.publicToken,
      assetId,
    });
  } else {
    const uuid = 'a9db3813-2566-4f83-9711-dc6de7932bcb&at=APZUnTXn-0LfIoy0TukqHqHnC1Kh%3A1713779187600';
    videoUrl = `https://drive.usercontent.google.com/download?id=${assetId}&export=download&authuser=0&confirm=t&uuid=${uuid}`;
    fileName = `${name}.mp4`;
  }

  // 转换为永久有效的cdn地址
  const cdnUrl = (await sigUrlToCdn(videoUrl, 'ai_toolkit/video_dub/video', fileName)).url;
  videoUploader.value.setFiles([
    { type: 'video/mp4', status: 'success', name, url: cdnUrl, poster },
  ]);
  canPoll.value = true;
  formData.video = cdnUrl;
  formData.duration = +duration;
  selectLoading.value = false;
  materialSelect.value.hideDialog();
};

const setFiles = (url: string, name: string) => {
  videoUploader.value.setFiles([
    { type: 'video/mp4', status: 'success', name, url },
  ]);
};

// 接受任务选择消息通知
const bus = ref();
onMounted(() => {
  bus.value = dubBus.on((name, item: DubTask) => {
    if (name === 'selectTask') {
      setFiles(item.origin_video, item.media_name);
      canPoll.value = false;
      if (item.language) {
        language.value = item.language.toLowerCase();
      }
      formData.duration = 0; // 切换任务，把原来duration清0
      formData.video = item.origin_video;
      textVoices.value = item.textVoices;
      curVttId.value = item.vtt_id;
    }
  });
});

onUnmounted(() => {
  bus.value();
});

defineExpose({
  setFiles,
});
</script>
<style lang="scss">
.mouth-video-form {
  .t-upload__dragger {
    width: 400px;
    height: 240px;
  }
}
</style>
