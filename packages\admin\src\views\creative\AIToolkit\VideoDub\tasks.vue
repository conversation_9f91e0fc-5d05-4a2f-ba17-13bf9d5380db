<template>
  <t-loading :loading="taskLoading" class="overflow-y-auto">
    <video-tasks
      :task-list="taskList"
      :active-id="curTask?.task_id"
      :select-task="selectTask"
      :custom-download="true"
      @download="onDownload"
    />
  </t-loading>
  <template v-if="taskList.length === 0 && !taskLoading">
    <div class="text-black-placeholder text-center w-full">No Data</div>
  </template>
</template>
<script setup lang="ts">
import { onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { MessagePlugin } from 'tdesign-vue-next';
import { useAIVideoDubStore } from '@/store/creative/toolkit/ai_video_dub.store';
import { DubTask } from 'common/service/creative/aigc_toolkit/type';
import { getDubTasks, downloadTaskZip } from 'common/service/creative/aigc_toolkit/ai_dub';
import VideoTasks from '../components/VideoTasks.vue';
import { dubBus } from '../utils/event';

const { getTasks, previewVideo } = useAIVideoDubStore();
const { taskLoading, taskList, curTask } = storeToRefs(useAIVideoDubStore());

const selectTask = (item: DubTask) => {
  curTask.value = item;
  const { status } = item;
  if (status === 2) previewVideo.url = item.target_video;
  else previewVideo.url = '';
  dubBus.emit('selectTask', item);
};

const onDownload = async (item: DubTask) => {
  if (item.download_url) {
    window.open(item.download_url);
  } else {
    const updateTime = (new Date(item.update_time)).getTime();
    // 10分钟还没下载完成，需要重新触发下载
    if (Date.now() - updateTime > 10 * 60 * 1000) {
      downloadTaskZip(Number(item.task_id));
    }
    MessagePlugin.warning({
      content: 'The download will complete in a few seconds',
      duration: 0,
    });
    const intervalId = setInterval(() => {
      (async () => {
        const { tasks = [] } = await getDubTasks(Number(item.task_id));
        if (tasks[0].download_url) {
          getTasks(); // 重新筛选列表
          clearInterval(intervalId);
          MessagePlugin.closeAll();
          window.open(tasks[0].download_url);
        }
      })();
    }, 2000);
  }
};

onMounted(() => {
  getTasks();
});

defineExpose({
  selectTask,
  onDownload,
});
</script>
<style scoped>
.t-loading__parent {
  position: initial;
}
</style>
