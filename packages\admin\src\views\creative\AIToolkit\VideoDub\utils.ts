import { TextVoice } from 'common/service/creative/aigc_toolkit/type';

// 获取下一个音频的时间范围
export function getNextTimeRange(timeRange: string) {
  const timeParts = timeRange.split(' --> ');
  const endTime = timeParts[1];

  const endTimeParts = endTime.split(':');
  const endHours = parseInt(endTimeParts[0], 10);
  const endMinutes = parseInt(endTimeParts[1], 10);
  const endSecondsAndMilliseconds = endTimeParts[2].split(',');
  const endSeconds = parseInt(endSecondsAndMilliseconds[0], 10);
  const endMilliseconds = parseInt(endSecondsAndMilliseconds[1], 10);

  const newStartMilliseconds = endMilliseconds;
  const newEndMilliseconds = endMilliseconds;

  const newStartSeconds = endSeconds;
  let newEndSeconds = endSeconds + 5;

  const newStartMinutes = endMinutes;
  let newEndMinutes = endMinutes;

  const newStartHours = endHours;
  let newEndHours = endHours;

  if (newEndSeconds >= 60) {
    newEndSeconds %= 60;
    newEndMinutes += 1;
  }
  if (newEndMinutes >= 60) {
    newEndMinutes %= 60;
    newEndHours += 1;
  }

  const newStartTime = `${String(newStartHours).padStart(2, '0')}:${String(newStartMinutes)
    .padStart(2, '0')}:${String(newStartSeconds)
    .padStart(2, '0')},${String(newStartMilliseconds)
    .padStart(3, '0')}`;

  const newEndTime = `${String(newEndHours).padStart(2, '0')}:${String(newEndMinutes)
    .padStart(2, '0')}:${String(newEndSeconds)
    .padStart(2, '0')},${String(newEndMilliseconds)
    .padStart(3, '0')}`;

  return `${newStartTime} --> ${newEndTime}`;
}

// 校验时间范围是否合法
export function validateTimeRange(timeRange: string) {
  const regex = /^\d{2}:\d{2}:\d{2},\d{3} --> \d{2}:\d{2}:\d{2},\d{3}$/;

  // Check if the format is valid
  if (!regex.test(timeRange)) {
    return false;
  }

  const times = timeRange.split(' --> ');
  const startTime = times[0];
  const endTime = times[1];

  // Convert time to seconds for comparison
  const startSeconds = convertTimeToSeconds(startTime);
  const endSeconds = convertTimeToSeconds(endTime);

  // Check if the end time is greater than the start time
  return endSeconds > startSeconds;
}


function convertTimeToSeconds(time: string) {
  const parts = time.split(':');
  const hours = parseInt(parts[0], 10);
  const minutes = parseInt(parts[1], 10);
  const secondsAndMilliseconds = parts[2].split(',');
  const seconds = parseInt(secondsAndMilliseconds[0], 10);
  const milliseconds = parseInt(secondsAndMilliseconds[1], 10);

  return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000;
}


// 全量校验文本语音列表
export function checkTextVoices(list: TextVoice[], duration: number) {
  let time = true;
  let required = true;

  for (let i = 0; i < list.length; i++) {
    const item = list[i];

    // 检查必填字段
    if (!item.text || !item.timeRange || !item.voice_id || !item.style) {
      required = false;
    }

    const timeRangeParts = item.timeRange.split(' --> ');
    const startTime = convertTimeToSeconds(timeRangeParts[0]);
    const endTime = convertTimeToSeconds(timeRangeParts[1]);

    // 检查开始时间是否小于结束时间
    if (startTime >= endTime) {
      time = false;
    }

    // 检查结束时间必须小于总时长duration
    if (duration > 0 && endTime > duration) {
      time = false;
    }

    // 检查当前段的结束时间是否小于下一段的开始时间
    if (i < list.length - 1) {
      const nextItem = list[i + 1];
      const nextTimeRangeParts = nextItem.timeRange.split(' --> ');
      const nextStartTime = convertTimeToSeconds(nextTimeRangeParts[0]);
      if (endTime > nextStartTime) {
        time = false;
      }
    }
  }

  const total = required && time;
  return { total, time, required };
}
