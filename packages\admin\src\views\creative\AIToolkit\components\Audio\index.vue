<template>
  <div class="flex items-center h-[40px]">
    <div class="cursor-pointer">
      <PauseCircleStrokeIcon
        v-if="playing" color="#999" size="32"
        @click="pause"
      />
      <PlayCircleStrokeIcon
        v-else color="#999" size="32"
        @click="play"
      />
    </div>
    <audio
      ref="audioRef" :src="replaceAiUrl(url)" @loadedmetadata="onload"
      @ended="onEnd"
    />
    <div class="text-[large] ml-[12px]">{{ name }}</div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { PlayCircleStrokeIcon, PauseCircleStrokeIcon } from 'tdesign-icons-vue-next';
import { replaceAiUrl } from '@/util/creative/replaceUrl';

defineProps({
  url: {
    type: String,
    required: false,
    default: '',
  },
  name: {
    type: String,
    required: false,
    default: '',
  },
});

const audioRef = ref();
const playing = ref(false); // 正在播放

const onload = () => {
  console.log('audio can play');
};

const onEnd = () => {
  playing.value = false;
  audioRef.value!.pause();
  audioRef.value!.currentTime = 0;
};

const play = () => {
  playing.value = true;
  audioRef.value?.play();
};

const pause = () => {
  playing.value = false;
  audioRef.value?.pause();
};
</script>
