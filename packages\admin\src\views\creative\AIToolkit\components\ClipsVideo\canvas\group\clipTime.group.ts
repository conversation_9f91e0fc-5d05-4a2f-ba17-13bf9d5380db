import Konva from 'konva';
import ClipTimeSvg from '../../asset/time.svg';
import { EventBus } from '../../utils/event';
import { formatVideoTime } from '../../utils/time';
import { useVideoClipConfigStore } from '../../store/config.store';
export class ClipDuration extends Konva.Group {
  private readonly fontSize = 12;
  private defaultTime = '00:00:00.00';
  private clipDurationText!: Konva.Text;
  private eventBus;
  private videoClipConfigStore = useVideoClipConfigStore();

  constructor(config: Konva.GroupConfig, eventBus: EventBus) {
    super(config);
    this.eventBus = eventBus;
    this.init();
  }

  public init() {
    this.drawShape();
    this.initListeners();
  }

  public load() {
    const { startTime = 0, endTime = 0 } = this.videoClipConfigStore.getConfig().videoConfig;
    const clipDuration = endTime - startTime;
    this.clipDurationText.text(formatVideoTime(clipDuration));
  }

  private async drawShape() {
    const clipDurationIcon: Konva.Image = await new Promise((resolve) => {
      Konva.Image.fromURL(ClipTimeSvg, (image: Konva.Image) => {
        image.setAttrs({
          x: 0,
          y: this.height() / 2,
          width: 16,
          height: 16,
        });
        image.offsetY(image.height() / 2);
        resolve(image);
      });
    });

    this.clipDurationText = new Konva.Text({
      x: clipDurationIcon.width() + clipDurationIcon.x() + 4,
      y: this.height() / 2,
      text: this.defaultTime,
      fontSize: this.fontSize,
      fill: 'white',
    });
    this.clipDurationText.offsetY(this.clipDurationText.height() / 2 - 1);
    this.add(clipDurationIcon, this.clipDurationText);
    this.offsetX(this.width());
  }

  private initListeners() {
    this.eventBus.on('clip-time-updated', (clipTime: { startTime: number; endTime: number }) => {
      const { startTime, endTime } = clipTime;
      const clipDuration = endTime - startTime;
      this.clipDurationText.text(formatVideoTime(clipDuration));
    });
  }
}
