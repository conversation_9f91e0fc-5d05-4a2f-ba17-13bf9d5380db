import Konva from 'konva';
import { useVideoClipConfigStore } from '../../store/config.store';
import { EventBus } from '../../utils/event';
import { formatVideoTime } from '../../utils/time';
import { ClipDuration } from './clipTime.group';

// 当前播放事件和视频总时长
export class TimeGroup extends Konva.Group {
  private readonly fontSize = 12;
  private defaultTime = '00:00:00.00';
  private durationTimeText!: Konva.Text;
  private curTimeText!: Konva.Text;
  private readonly eventBus;
  private clipDurationGroup!: ClipDuration;
  private videoClipConfigStore = useVideoClipConfigStore();
  constructor(config: Konva.GroupConfig, eventBus: EventBus) {
    super(config);
    this.eventBus = eventBus;
    this.init();
  }

  public init() {
    this.drawShape();
  }

  public drawShape() {
    const driveLine = new Konva.Line({
      x: this.width() / 2,
      y: this.height() / 2,
      points: [0, 0, 0, 16],
      stroke: '#5C5C5C',
      strokeWidth: 1,
    });
    driveLine.offsetX(driveLine.width() / 2);
    driveLine.offsetY(8);

    this.curTimeText = new Konva.Text({
      y: this.height() / 2,
      x: driveLine.getPosition().x - 4,
      text: this.defaultTime,
      fill: '#7064FF',
      align: 'center',
      verticalAlign: 'middle',
      fontSize: this.fontSize,
    });
    this.curTimeText.offsetY(this.curTimeText.height() / 2 - 1);
    this.curTimeText.offsetX(this.curTimeText.width());

    this.durationTimeText = new Konva.Text({
      y: this.height() / 2,
      x: driveLine.getPosition().x + 4,
      text: this.defaultTime,
      fontSize: this.fontSize,
      fill: 'white',
    });
    this.durationTimeText.offsetY(this.durationTimeText.height() / 2 - 1);

    this.clipDurationGroup = new ClipDuration({
      width: 100,
      x: this.width() - 110,
      y: 0,
      height: this.height(),
    }, this.eventBus);

    this.add(driveLine, this.curTimeText, this.durationTimeText, this.clipDurationGroup);
  }

  public setDuration(duration: number) {
    this.durationTimeText.text(formatVideoTime(duration));
  }

  public setCurTime(curTime: number) {
    this.curTimeText.text(formatVideoTime(curTime));
  }

  public reset() {
    this.setCurTime(0);
    this.setDuration(0);
  }

  public load(videoCurrentTime: number) {
    this.setCurTime(videoCurrentTime);
    this.clipDurationGroup.load();
  }
}
