import Konva from 'konva';
import {
  HANDLE_BORDER_WIDTH,
  MASK_LINE_GROUP_HEIGHT,
  TIMELINE_CONTENT_PADDING_BOTTOM,
  TIMELINE_CONTENT_PADDING_X,
} from '../../constant';
import { useVideoClipConfigStore } from '../../store/config.store';
import { ClipsVideoShapeId, useKonvaStore } from '../../store/konva.store';
import { ContentDefaultGroup } from '../group/content.default.group';
import { ContentGroup } from '../group/content.group';
import { CursorGroup } from '../group/cursor.group';
import { MarkLineGroup } from '../group/markLine.group';
import { ToolTipLabel } from '../group/timeTooltip.group';
import { EventBus } from '../../utils/event';

export class TimeLineLayer extends Konva.Layer {
  public playing = false;
  public duration = 0;

  private readonly konvaStore = useKonvaStore();
  private readonly videoClipConfigStore = useVideoClipConfigStore();
  private readonly eventBus;
  private leftToolTip!: ToolTipLabel;
  private rightToolTip!: ToolTipLabel;

  private cursorGroup!: CursorGroup;
  private contentGroup!: ContentGroup;

  private markLineGroup!: MarkLineGroup;

  private defaultContentGroup!: ContentDefaultGroup;

  constructor(config: Konva.LayerConfig, eventBus: EventBus) {
    super(config);
    this.eventBus = eventBus;
    this.init();
  }

  public init() {
    this.drawShape();
    this.initListeners();
  }

  // 绘制图形
  public drawShape() {
    const stage = this.konvaStore.getKonvaNode(ClipsVideoShapeId.stage);
    const stageWidth = stage.width();

    // 内容区宽度
    const contentWidth = stageWidth - 2 * TIMELINE_CONTENT_PADDING_X;
    if (contentWidth < 0) return;

    this.videoClipConfigStore.setConfig({
      timeLineConfig: {
        ...this.videoClipConfigStore.getConfig().timeLineConfig,
        leftBound: TIMELINE_CONTENT_PADDING_X,
        rightBound: TIMELINE_CONTENT_PADDING_X + contentWidth,
      },
    });

    const markLineGroup = new MarkLineGroup({
      width: stageWidth,
      height: MASK_LINE_GROUP_HEIGHT,
    });

    this.konvaStore.setKonvaNode(ClipsVideoShapeId.cursor, this.cursorGroup);

    const contentGroup = new ContentGroup({
      x: TIMELINE_CONTENT_PADDING_X,
      y: markLineGroup.height(),
      width: contentWidth,
      height: this.clipHeight() - markLineGroup.height() - TIMELINE_CONTENT_PADDING_BOTTOM,
      visible: false,
    }, this.eventBus);

    const defaultContentGroup = new ContentDefaultGroup({
      x: TIMELINE_CONTENT_PADDING_X - HANDLE_BORDER_WIDTH,
      y: markLineGroup.height(),
      width: stageWidth - 2 * (TIMELINE_CONTENT_PADDING_X - HANDLE_BORDER_WIDTH),
      height: this.clipHeight() - markLineGroup.height() - TIMELINE_CONTENT_PADDING_BOTTOM,
    });

    this.cursorGroup = new CursorGroup({
      x: TIMELINE_CONTENT_PADDING_X,
      visible: false,
      clipHeight: this.clipHeight(),
    }, this.eventBus);

    const { leftBound } = this.videoClipConfigStore.getConfig().timeLineConfig;
    this.leftToolTip = new ToolTipLabel({
      x: leftBound,
      y: contentGroup.y() - HANDLE_BORDER_WIDTH,
      visible: false,
    });
    this.konvaStore.setKonvaNode(ClipsVideoShapeId.leftToolTip, this.leftToolTip);

    this.rightToolTip = new ToolTipLabel({
      x: leftBound,
      y: contentGroup.y() - HANDLE_BORDER_WIDTH,
      visible: false,
    });
    this.konvaStore.setKonvaNode(ClipsVideoShapeId.rightToolTip, this.rightToolTip);

    this.contentGroup = contentGroup;
    this.markLineGroup = markLineGroup;
    this.defaultContentGroup = defaultContentGroup;

    this.add(markLineGroup);
    this.add(this.defaultContentGroup);
    this.add(this.contentGroup, this.cursorGroup, this.leftToolTip, this.rightToolTip);
  }

  public load(videoUrl: string) {
    const { startTime = 0, endTime = 0 } = this.videoClipConfigStore.getConfig().videoConfig;
    const { secondaryScaleToSeconds, secondaryScaleToPixel } = this.videoClipConfigStore.getConfig().timeLineConfig;
    this.defaultContentGroup.visible(false);

    this.cursorGroup.updateCursorPositionByTime(startTime ?? 0);

    this.cursorGroup.visible(true);
    this.contentGroup.visible(true);
    this.contentGroup.load(videoUrl);

    this.leftToolTip.x((startTime / secondaryScaleToSeconds) * secondaryScaleToPixel + TIMELINE_CONTENT_PADDING_X);
    this.rightToolTip.x((endTime / secondaryScaleToSeconds) * secondaryScaleToPixel + TIMELINE_CONTENT_PADDING_X);

    this.markLineGroup.repaint();
  }

  public updateCursorPosition(x: number) {
    this.cursorGroup.x(x);
  }

  public initListeners() {
    this.eventBus.on(
      'time-update',
      ({ currentTime, videoInstance }: { currentTime: number; videoInstance: HTMLVideoElement }) => {
        const radio = currentTime / videoInstance.duration;
        const { leftBound, rightBound } = this.videoClipConfigStore.getConfig().timeLineConfig;
        this.cursorGroup.x(radio * (rightBound - leftBound) + leftBound);
      },
    );

    this.eventBus.on('loadedmetadata', () => {
      this.load(this.videoClipConfigStore.getConfig().videoConfig.src!);
    });

    this.on('wheel', (e) => {
      console.log(e);
    });
  }
}
