import Konva from 'konva';
import { useVideoClipConfigStore } from '../../store/config.store';
import { ClipsVideoShapeId, useKonvaStore } from '../../store/konva.store';
import { VideoConfig } from '../../types';
import { generateVideoElement } from '../../utils/video';
import { TimeGroup } from '../group/time.group';
import { EventBus } from '../../utils/event';

export class VideoLayer extends Konva.Layer {
  public playing = false;
  public duration = 0;

  private readonly konvaStore = useKonvaStore();
  private readonly videoClipConfigStore = useVideoClipConfigStore();
  private videoInstance!: HTMLVideoElement;
  private readonly eventBus;
  private animation!: Konva.Animation;
  private timeGroup!: TimeGroup;
  private coverImage?: Konva.Image;
  private videoImage?: Konva.Image;

  constructor(config: Konva.LayerConfig, eventBus: EventBus) {
    super(config);
    this.eventBus = eventBus;
    this.init();
  }

  public init() {
    this.initVideoInstance();
    this.drawShape();
    this.initAnimation();
    this.initListeners();
  }

  // 页面插入video视频
  public initVideoInstance() {
    this.videoInstance = generateVideoElement();
    this.videoInstance.loop = true;
    this.videoInstance.addEventListener('loadedmetadata', (evt) => {
      console.log('loadedmetadata', evt);

      this.generateVideoImage();

      this.duration = this.videoInstance.duration;
      this.timeGroup.setDuration(this.duration);

      // update timeline config
      const { timeLineConfig } = this.videoClipConfigStore.getConfig();
      const { leftBound, rightBound, primaryScaleToSecondaryScale } = timeLineConfig;
      const timeLineContentWidth = rightBound - leftBound;
      // 计算可以生成多少的刻度
      const scaleCount = Math.floor(timeLineContentWidth / primaryScaleToSecondaryScale);
      const secondaryScaleToSeconds = this.duration / scaleCount;
      this.videoClipConfigStore.setConfig({
        videoConfig: {
          ...this.videoClipConfigStore.getConfig().videoConfig,
          duration: this.duration,
        },
        timeLineConfig: {
          ...timeLineConfig,
          secondaryScaleToSeconds,
        },
      });

      this.eventBus.emit('loadedmetadata', {
        videoInstance: this.videoInstance.cloneNode(true),
      });
    });

    this.videoInstance.addEventListener('ready', () => {
      console.log('video ready');
    });
    this.videoInstance.addEventListener('canplay', () => {
      console.log('video canplay');
      this.eventBus.emit('loaded');
      this.draw();
    });
    this.videoInstance.addEventListener('waiting', () => {
      console.log('video waiting');
    });
    this.videoInstance.addEventListener('error', () => {
      this.eventBus.emit('video-error');
    });
  }

  public drawShape() {
    const stage = this.konvaStore.getKonvaNode(ClipsVideoShapeId.stage);
    const imageHeight = (stage.width() / 16) * 9;

    const videoBg = new Konva.Rect({
      height: imageHeight,
      width: stage.width(),
      fill: 'black',
    });


    // 时间文本
    this.timeGroup = new TimeGroup({
      y: 1.06 * imageHeight,
      height: 0.1 * imageHeight,
      width: stage.width(),
    }, this.eventBus);

    // this.coverImage = coverImage;

    this.add(videoBg, this.timeGroup);
  }

  public initListeners() {
    this.eventBus.on('play-video', () => {
      this.coverImage?.visible(false);
      this.videoInstance
        .play()
        .then(() => {
          this.animation.start();
          this.playing = true;
        })
        .catch(() => {});
    });
    this.eventBus.on('forward-video', () => {
      console.log('forward-btn');
      this.videoInstance.currentTime += 0.1;
      this.timeGroup.setCurTime(this.videoInstance.currentTime);
      this.eventBus.emit('time-update', {
        currentTime: this.videoInstance.currentTime,
        videoInstance: this.videoInstance,
      });
    });
    this.eventBus.on('backward-video', () => {
      console.log('backward-btn');
      this.videoInstance.currentTime -= 0.1;
      this.timeGroup.setCurTime(this.videoInstance.currentTime);
      this.eventBus.emit('time-update', {
        currentTime: this.videoInstance.currentTime,
        videoInstance: this.videoInstance,
      });
    });
    this.eventBus.on('pause-video', () => {
      this.videoInstance.pause();
      this.animation.stop();
      this.playing = false;
    });

    this.eventBus.on('cursor-changed', (evt: { x: number }) => {
      this.coverImage?.visible?.(false);
      const { secondaryScaleToPixel, secondaryScaleToSeconds } = this.videoClipConfigStore.getConfig().timeLineConfig;
      const curTime = (evt.x / secondaryScaleToPixel) * secondaryScaleToSeconds;
      this.timeGroup.setCurTime(curTime);
      this.videoInstance.currentTime = curTime;
    });

    this.eventBus.on('video-mute', ({ mute }: {mute: boolean}) => {
      console.log(mute);

      this.videoInstance.muted = mute;
    });
  }

  public load(videoConfig: VideoConfig) {
    const coverImage = new Image();
    // load cover image
    if (videoConfig.poster) {
      coverImage.src = videoConfig.poster;
      coverImage.onload = () => {
        const { width, height } = coverImage;
        this.generateCoverImage(width, height);
        this.coverImage?.image(coverImage);
      };
    }

    // load video
    this.videoInstance.src = `${videoConfig.src!}?t=${new Date().getTime()}`;
    this.videoInstance.currentTime = videoConfig.startTime || 0;
    this.timeGroup.load(this.videoInstance.currentTime);

    this.videoImage?.image(this.videoInstance);
  }

  public reset() {
    this.coverImage?.visible(true);
    this.coverImage?.image(undefined);

    this.videoImage?.destroy();
    this.videoImage = undefined;
    this.coverImage?.destroy();
    this.coverImage = undefined;

    this.videoInstance.src = '';
    this.videoInstance.currentTime = 0;
    this.videoInstance.pause();
    this.timeGroup.reset();
  }

  private generateVideoImage() {
    const { videoHeight, videoWidth } = this.videoInstance;
    this.videoImage = this.generateImage(videoWidth, videoHeight);
    this.videoImage.image(this.videoInstance);
    this.add(this.videoImage);
    this.videoImage.zIndex(1);
  }

  private generateCoverImage(imageWidth: number, imageHeight: number) {
    this.coverImage = this.generateImage(imageWidth, imageHeight);
    this.coverImage.zIndex(2);
    this.add(this.coverImage);
  }

  /**
   * 根据宿主高度，按比例缩放原视频
   * @param originWidth 源视频或图片宽度
   * @param originHeight 源视频或图片高度
   */
  private generateImage(originWidth: number, originHeight: number): Konva.Image {
    const fillColor = 'black';

    const stage = this.konvaStore.getKonvaNode(ClipsVideoShapeId.stage);
    const imageDefaultWidth = stage.width();
    const imageDefaultHeight = (stage.width() / 16) * 9;

    const originImageRatio = originWidth / originHeight;
    const imageHeight = imageDefaultHeight;
    const imageWidth = imageHeight * originImageRatio;

    const image = new Konva.Image({
      x: imageDefaultWidth / 2,
      image: undefined,
      width: imageWidth,
      height: imageHeight,
      fill: fillColor,
    });
    image.offsetX(image.width() / 2);

    return image;
  }

  private initAnimation() {
    this.animation = new Konva.Animation(() => {
      const { currentTime } = this.videoInstance;
      this.timeGroup.setCurTime(currentTime);
      this.eventBus.emit('time-update', { currentTime, videoInstance: this.videoInstance });
    }, this);
  }
}
