import Konva from 'konva';

import { VideoLayer } from './canvas/layer/video.layer';
import { ClipsVideoShapeId, useKonvaStore } from './store/konva.store';
import { ControlLayer } from './canvas/layer/control.layer';
import { TimeLineLayer } from './canvas/layer/timeLine.layer';
import { STAGE_BACKGROUND_COLOR } from './constant';
import { useVideoClipConfigStore } from './store/config.store';
import { VideoClipConfig, VideoConfig } from './types';
import { EventBus } from './utils/event';
import { v4 as uuidv4 } from 'uuid';

export class ClipVideo {
  private readonly konvaStore = useKonvaStore();
  private readonly videoClipConfigStore = useVideoClipConfigStore();
  private videoLayer!: VideoLayer;
  private controlLayer!: ControlLayer;
  private readonly eventBus = new EventBus(uuidv4());

  constructor(config: Partial<VideoClipConfig>) {
    this.videoClipConfigStore.setConfig(config);
    // this.config = config;
    this.init();
  }

  public init() {
    const { container } = this.videoClipConfigStore.getConfig();
    if (!container) {
      throw new Error('container is required');
    }

    // get container width and height
    const { width, height } = container.getBoundingClientRect();

    const stage = new Konva.Stage({
      container,
      width,
      height,
      id: ClipsVideoShapeId.stage,
      name: ClipsVideoShapeId.stage,
    });

    stage.container().style.backgroundColor = STAGE_BACKGROUND_COLOR;

    this.konvaStore.setKonvaNode(ClipsVideoShapeId.stage, stage);
    const videoHeight = (width / 16) * 9;

    const videoLayer = new VideoLayer({
      clipHeight: videoHeight * 1.16,
    }, this.eventBus);

    const controlLayer = new ControlLayer({
      y: videoHeight,
      clipHeight: videoHeight * 0.1,
    }, this.eventBus);

    const timeLineLayer = new TimeLineLayer({
      y: videoLayer.clipHeight(),
      clipHeight: height - videoLayer.clipHeight(),
    }, this.eventBus);

    this.videoLayer = videoLayer;
    this.controlLayer = controlLayer;
    stage.add(videoLayer, controlLayer, timeLineLayer);

    this.eventBus.emit('init');
  }

  public load(config: VideoConfig) {
    if (!config.src) {
      console.warn('video src is required');
      return;
    }
    this.videoClipConfigStore.setConfig({
      videoConfig: config,
    });

    this.controlLayer.reset();
    this.videoLayer.reset();
    this.videoLayer.load({
      src: config.src!,
      poster: config.poster,
      startTime: config.startTime,
      endTime: config.endTime,
    });
  }
  emit(evtName: string, payload: any) {
    this.eventBus.emit(evtName, payload);
  }
  on(evtName: string, cb: (...args: any[]) => void) {
    this.eventBus.on(evtName, cb);
  }

  getTime() {
    const { startTime, endTime } = this.videoClipConfigStore.getConfig().videoConfig;
    return { startTime, endTime };
  }

  reset() {
    this.eventBus.emit('pause-video');
    this.controlLayer.reset();
    this.videoLayer.reset();
  }
}
