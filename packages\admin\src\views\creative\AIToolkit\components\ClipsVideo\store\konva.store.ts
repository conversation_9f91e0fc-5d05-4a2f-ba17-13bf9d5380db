import { StoreUniqueKey, defineStore } from './index';

export enum ClipsVideoShapeId {
  stage = 'video-clips-stage',
  cursor = 'video-clips-cursor',
  leftToolTip = 'video-clips-left-tooltip',
  rightToolTip = 'video-clips-right-tooltip',
}

export const useKonvaStore = defineStore(StoreUniqueKey.VideoClipConfig, () => {
  const konvaCache = new Map<string, any>();

  const getKonvaNode = (key: string) => konvaCache.get(key);
  const setKonvaNode = (key: string, value: any) => {
    konvaCache.set(key, value);
  };
  return {
    konvaCache,
    getKonvaNode,
    setKonvaNode,
  };
});
