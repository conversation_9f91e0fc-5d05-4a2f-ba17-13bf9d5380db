export interface VideoConfig {
  startTime?: number;
  endTime?: number;
  src?: string;
  poster?: string;
  duration?: number;
}
export interface TimeLineConfig {
  leftBound: number;
  rightBound: number;

  // 一个主刻度里面有多少个小刻度
  primaryScaleToSecondaryScale: number;
  // 一个次级刻度宽度拥有多少像素
  secondaryScaleToPixel: number;
  // 一个次级刻度代表的秒数
  secondaryScaleToSeconds: number;
}

export interface VideoClipConfig {
  container?: HTMLDivElement;
  videoConfig: VideoConfig;
  timeLineConfig: TimeLineConfig;
  [key: string]: any;
}

export class BaseLayer extends (<T extends Constructor>(parentClass: T) => {
  return class extends parentClass {
    public readonly konvaStore = useKonvaStore();
  };
}) {}
