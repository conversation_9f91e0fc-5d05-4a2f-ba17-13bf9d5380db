export type EventCallback = (...args: any[]) => void;
export class EventBus {
  private readonly id: string;
  private eventMap = new Map<string, EventCallback[]>();
  constructor(id: string) {
    this.id = id;
  }
  on(eventName: string, cb: EventCallback) {
    const events = this.eventMap.get(`${this.id}:${eventName}`);
    if (events) {
      events.push(cb);
    } else {
      this.eventMap.set(`${this.id}:${eventName}`, [cb]);
    }
  }

  once(eventName: string, cb: EventCallback) {
    return new Promise((resolve) => {
      this.on(`${this.id}:${eventName}`, cb);
      resolve(true);
    }).then(() => {
      this.off(`${this.id}:${eventName}`, cb);
    });
  }

  off(eventName: string, cb: EventCallback) {
    const events = this.eventMap.get(`${this.id}:${eventName}`);
    const newEvents = events?.filter(fn => cb !== fn) || [];
    this.eventMap.set(`${this.id}:${eventName}`, newEvents!);
  }

  emit(eventName: string, payload?: Record<string, any>) {
    const events = this.eventMap.get(`${this.id}:${eventName}`);
    if (events) {
      events.forEach((cb) => {
        cb(payload);
      });
    }
  }
}

export default EventBus;
