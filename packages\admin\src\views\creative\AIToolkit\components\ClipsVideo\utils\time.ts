import { useVideoClipConfigStore } from '../store/config.store';

/**
 * 00:00.00
 * 根据秒数生成规范的时间,最大支持到小时级别
 * example：3406.87 : 56:46.86
 * @param time
 * @returns string 规范化的时间格式
 */
export const formatVideoTime = (time: number): string => {
  let lave = time;
  const hours = Math.floor(lave / 3600);
  lave = lave % 3600;

  const minutes = Math.floor(lave / 60);
  lave = lave % 60;

  const seconds = Math.floor(lave % 60);

  const milliseconds = Number(`${lave}`.replace(/\d+(?=.)/, '0'))
    .toFixed(2)
    .slice(2);
  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(
    2,
    '0',
  )}.${milliseconds}`;
};

/**
 * 生成一个时间轴刻度间隔
 * @param markLineLength    时间轴的长度
 * @param videoTime         视频时长
 * @return                  时间轴刻度间隔
 */
export const getTimeLineScaleInterval = (markLineLength: number, videoTime: number) => {
  const formatTime = Number(videoTime.toFixed(3));
  return Number((markLineLength / formatTime).toFixed(2));
};

export const getTimeByX = (x: number) => {
  const { secondaryScaleToPixel, secondaryScaleToSeconds } = useVideoClipConfigStore().getConfig().timeLineConfig;
  return (x / secondaryScaleToPixel) * secondaryScaleToSeconds;
};
