<template>
  <t-dialog
    v-model:visible="visible"
    header="Processing..."
    width="600"
    :cancel-btn="null"
    :confirm-btn="null"
    @close="onClose"
  >
    <template #body>
      <div class="text-center my-[24px] text-base">{{ tip }}</div>
      <div class="flex flex-center">
        <t-button theme="primary" @click="onClose">
          Run in the back ground
        </t-button>
      </div>
    </template>
  </t-dialog>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue';

const emits = defineEmits(['update:modelValue']);
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  tip: {
    type: String,
    default: '',
  },
});
const visible = ref(props.modelValue);

const onClose = () => {
  visible.value = false;
  emits('update:modelValue', false);
};

watch(() => props.modelValue, (val) => {
  emits('update:modelValue', val);
  visible.value = val;
});
</script>
