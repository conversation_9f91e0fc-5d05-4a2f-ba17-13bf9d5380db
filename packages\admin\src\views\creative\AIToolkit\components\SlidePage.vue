<template>
  <div class="slide-page rounded-lg w-full flex flex-col flex-1 relative overflow-hidden">
    <div class="w-full h-full flex flex-row">
      <slot name="left" />
      <div
        class="right-content flex flex-row relative ml-[20px] min-w-[16px] rounded-default"
        :class="sidebarVisible ? 'flex-1 max-w-[40vw]' : 'bg-white'"
      >
        <div
          class="absolute top-0 bottom-0 -left-[9px] my-auto shadow-[0_3px_6px_0_rgba(0,0,0,.03)]
              flex justify-center items-center w-5 h-[60px] rounded-default bg-white-primary cursor-pointer
               border border-black-disabled z-20
               border-solid box-border hover:shadow-[0_3px_6px_0_rgba(0,0,0,.1)] transform-z-10 hover:border-brand"
          @click="() => (sidebarVisible ? hideSidebar() : showSidebar())"
        >
          <svg-icon
            name="arrow"
            size="12"
            class="transition"
            :class="sidebarVisible ? '-rotate-90' : 'rotate-90'"
          />
        </div>
        <transition name="fade">
          <div
            v-show="sidebarVisible"
            class="rounded-default flex overflow-hidden flex-col w-[100%] justify-between"
          >
            <slot name="right" />
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { provide } from 'vue';
import SvgIcon from 'common/components/SvgIcon';
import { useVisible } from 'common/compose/useVisible';

const { visible: sidebarVisible, hide: hideSidebar, show: showSidebar } = useVisible(true);

// 供子组件使用
provide('hide', hideSidebar);
provide('show', showSidebar);
</script>
<style lang="scss" scoped>
.fade-enter-active {
  width: 40vw;
  opacity: 1;
  transition: all 0.3s ease-in-out;
}

.fade-leave-active {
  width: 40vw;
  opacity: 1;
  transition: all 0.3s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  transform: translateX(40vw);
  width: 0;
  opacity: 0;
}
</style>
