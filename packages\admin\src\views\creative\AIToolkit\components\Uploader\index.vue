<template>
  <div class="flex flex-col" @drop="dropHandler">
    <t-upload
      v-model="filesList"
      class="face-uploader"
      :accept="accept"
      :tips="tips || ''"
      theme="file"
      :max="maxNum || 1"
      :use-mock-progress="true"
      :multiple="multiple || false"
      :is-batch-upload="true"
      :mock-progress-duration="300"
      :request-method="requestMethod"
      :upload-all-files-in-one-request="true"
      :status="uploadStatus"
      draggable
      :auto-upload="true"
      :before-all-files-upload="beforeUpload"
      @change="onFileChange"
      @drop="onDrop"
    >
      <template #file-list-display="{files}">
        <t-loading v-if="uploading" class="face-loading" size="24px" />
        <div
          v-for="(item, index) in files"
          v-else :key="item.url" class="media-box w-[100%] h-[85%]"
        >
          <div v-if="item.type.includes('audio')">
            <audio-box :url="replaceAiUrl(item.url)" :name="item.name" />
          </div>
          <div v-else class="relative w-[100%] h-[100%] bg-[#000]">
            <template v-if="item.type.includes('image')">
              <img
                :src="item.url" class="w-[100%] h-[100%] object-contain"
                alt=""
              >
            </template>
            <template v-if="item.type.includes('video')">
              <video
                :src="replaceAiUrl(item.url)"
                :poster="item.poster || undefined"
                class="w-[100%] h-[100%]"
                :canplay="onVideoLoaded"
              />
              <PlayIcon
                color="#FFF" size="48px" class="play-icon"
                @click="previewVideo(index as number)"
              />
            </template>
          </div>
        </div>
      </template>
    </t-upload>
    <small class="t-upload__tips t-size-s t-upload__tips-error">
      {{ !validDuration ? 'Video duration exceeds limit' : '' }}
    </small>
  </div>
  <VideoDialog
    ref="videoDialogRef"
    :file-name="videoName"
    :video-url="videoUrl"
  />
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { UploadFile as TdUploadFile } from 'tdesign-vue-next/es/upload/type';
import { MessagePlugin, RequestMethodResponse } from 'tdesign-vue-next';
import { UploadFile } from 'common/components/FileUpload';
import { upload } from 'common/components/FileUpload/util';
import { PlayIcon } from 'tdesign-icons-vue-next';
import VideoDialog from '../VideoDialog.vue';
import AudioBox from '../Audio/index.vue';
import { v4 as uuidv4 } from 'uuid';
import { replaceAiUrl } from '@/util/creative/replaceUrl';

const filesList = ref<TdUploadFile[]>([]);
const uploading = ref(false);
const validDuration = ref(true);
const validSize = ref(true);
const uploadStatus = ref('default'); // default or fail

const emit = defineEmits(['change', 'videoDuration']);

const props = defineProps<{
  accept: string,
  tips?: string,
  maxDuration?: number,  // 视频最大时长，单位s
  maxSize?: number, // 文件最大大小，单位M
  filePath: string,
  multiple?: boolean,
  maxNum?: number,
  useName?: boolean, // 使用原始文件名
}>();

const onFileChange = () => {
  // 单文件上传，需要将filesList重新赋值
  if (!props.multiple && filesList.value[0]) filesList.value = [filesList.value[0]];

  // 延迟事件，让video加载完
  setTimeout(() => {
    emit('change', filesList.value, validDuration.value, validSize.value);
  }, 500);
};

const setFiles = (list: TdUploadFile[]) => {
  filesList.value = list;
};

const videoDialogRef = ref();
const videoName = ref('');
const videoUrl = ref('');
const previewVideo = (index: number) => {
  const target = filesList.value[index] as TdUploadFile;
  videoName.value = target.name as string;
  videoUrl.value = replaceAiUrl(target.url) as string;
  videoDialogRef.value.show();
};

// 文件上传前钩子，返回false则不继续上传
function beforeUpload(files: TdUploadFile[]) {
  const validSize = files.every((item) => {
    if (!props.maxSize) return true;
    return item.size as number <= props.maxSize * 1024 * 1024;
  });
  if (!validSize) {
    MessagePlugin.error(`Video size cannot exceed ${props.maxSize}M`);
  }
  return validSize;
}

const requestMethod = async (file: TdUploadFile | TdUploadFile[]): Promise<RequestMethodResponse> => new Promise(
  (resolve) => {
    const isSingle = !(file instanceof Array);
    const rawFile = (isSingle ? file.raw : file[0].raw) as UploadFile;

    uploading.value = true;
    validDuration.value = true;
    rawFile.id = uuidv4();

    // 隐藏Cancel按钮
    setTimeout(() => {
      const cancelBtn = document.querySelector('.t-upload__dragger-progress-cancel') as HTMLElement;
      cancelBtn.style.display = 'none';
    });
    upload({
      file: rawFile,
      path: props.filePath || `ai_toolkit/face_swap/${rawFile.id}`,
      type: 'aix',
      useName: props.useName,
    }).then((res) => {
      uploading.value = false;
      const cancelBtn = document.querySelector('.t-upload__dragger-progress-cancel') as HTMLElement;
      cancelBtn.style.display = 'initial';
      if (!res.code) {
        res.url = res.url?.replace('https://static.aix.intlgame.cn', 'https://static.aix.intlgame.com');
        res.url = replaceAiUrl(res.url);
        console.log('url', res.url);
        resolve({ status: 'success', response: res });
      } else {
        resolve({ status: 'fail', error: 'upload error!', response: {} });
      }
    });
  });

// 文件拖入时触发
function dropHandler(event: DragEvent) {
  event.preventDefault();
  const files = (event.dataTransfer?.files || []) as FileList;
  if (files.length === 0) return;
  let valid = true;
  // eslint-disable-next-line @typescript-eslint/prefer-for-of
  for (let i = 0; i < files.length; i++) {
    if (!files[i].type || !props.accept.includes(files[i].type)) {
      valid = false;
      break;
    }
  }
  if (!valid) {
    MessagePlugin.error('The file format is not supported');
  }
}

// upload组件文件拖如时触发
const onDrop = () => {
  validDuration.value = true;
};

const onVideoLoaded = (e: Event) => {
  const { duration } = (e.target as HTMLVideoElement);
  if (props.maxDuration && props.maxDuration > 0) {
    validDuration.value = duration <= props.maxDuration;
  }
  emit('videoDuration', duration);
};

defineExpose({
  setFiles,
});
</script>
<style lang="scss">
.face-uploader {
  .t-upload__dragger {
    position: relative;
    height: 234px;
    width: 320px;

    .t-upload__dragger-btns {
      position: initial;
      text-align: center;
    }

    .t-upload__trigger {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
    }
  }

  .face-loading {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
  }

  .play-icon {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
  }
}
</style>
