<template>
  <t-dialog
    v-if="visible"
    v-model:visible="visible"
    class="video-dialog t-image-viewer__dialog preview"
    destroy-on-close
    placement="center"
    :footer="false"
    :header="previewName"
    draggable
    width="900px"
    attach="body"
  >
    <div class="relative w-full h-[560px] bg-black-primary flex items-center">
      <video-viewer
        class="absolute w-[100%] h-[100%]"
        :video="{ url: videoUrl }"
        :start-time="startTime"
        :end-time="endTime"
      />
    </div>
  </t-dialog>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue';
import VideoViewer from './VideoViewer.vue';

const props = defineProps<{
  videoUrl: string,
  fileName?: string,
  startTime?: number,
  endTime?: number,
}>();

const previewName = computed(() => {
  if (props.fileName) return props.fileName;
  const names = props.videoUrl.split('/');
  return names[names.length - 1];
});

const visible = ref(false);

const show = () => {
  visible.value = true;
};

defineExpose({
  show,
});
</script>
<style lang="scss">
.video-dialog {
  .t-dialog__body {
    padding: 0;
  }
}
</style>
