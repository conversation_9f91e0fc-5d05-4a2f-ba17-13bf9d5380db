<template>
  <div
    v-auto-animate
    class="w-full aspect-[5/4] flex flex-col justify-center items-center bg-black rounded-default relative"
  >
    <div
      ref="clipsVideoRef"
      class="w-full h-full overflow-hidden rounded-lg"
    />
    <div
      v-if="showSelect"
      class="absolute right-2"
      :style="selectBtnStyle"
    >
      <t-button
        size="small" variant="text" theme="primary"
        @click="selectClipsVideo"
      >
        <template #icon><add-icon class="mr-[-6px]" /></template>
        Add to Selected
      </t-button>
    </div>
    <div
      v-if="loadClipsVideoLoading"
      class="absolute h-full w-full z-10 flex justify-center items-center bg-black bg-opacity-60 rounded-default"
    >
      <t-loading
        v-show="videoLoadingStatus === 'active'"
        size="small"
        text="Loading..."
      />
      <div
        v-show="videoLoadingStatus === 'error'"
        class="text-error-primary"
      >
        Sorry, the video failed to load.
      </div>
    </div>
  </div>
</template>
<script lang="tsx" setup>
import { ref, watch, onMounted, onUnmounted, computed, CSSProperties, inject } from 'vue';
import { AddIcon } from 'tdesign-icons-vue-next';
import { useAIClipStore } from '@/store/creative/toolkit/ai_clips.store';
import { useAiSVEditorStore } from '@/store/creative/toolkit/ai_smart_video_editor.store';
import { storeToRefs } from 'pinia';
import { ClipsVideo } from 'common/service/creative/aigc_toolkit/type';
import { ClipVideo } from '../ClipsVideo';
import { useElementBounding } from '@vueuse/core';
import { useLoading } from 'common/compose/loading';
import { replaceAiUrl } from '@/util/creative/replaceUrl';

type LoadingStatus = 'active' | 'success' | 'error';
const from = inject('from', 'page') as string;
const page = inject('page', 'editor') as string;
const { clipsVideos } = storeToRefs(useAIClipStore());
const { setClipsVideos } = useAIClipStore();
const { selectedVideo } = useAiSVEditorStore();

const {
  isLoading: loadClipsVideoLoading,
  showLoading: showLoadClipsVideoLoading,
  hideLoading: hideLoadClipsVideoLoading,
} = useLoading(false);

const props = withDefaults(defineProps<{
  showSelect?: boolean,
  clipVideo?: ClipsVideo | null,
}>(), {
  showSelect: true,
  clipVideo: null,
});

const clipsVideoRef = ref<HTMLDivElement>();
const videoClip = ref<InstanceType<typeof ClipVideo>>();

const videoLoadingStatus = ref<LoadingStatus>('active');
const { width: containerWidth } = useElementBounding(clipsVideoRef);
const containerHeight = ref<number | undefined>();
const selectBtnStyle = computed<CSSProperties>(() => (!!containerHeight.value
  ? { top: `${containerHeight.value + 2}px` } : {}));

const selectClipsVideo = () => {
  if (props.clipVideo && videoClip.value) {
    const { startTime = 0, endTime = 0 } = videoClip.value.getTime();
    const clipsVideo: ClipsVideo = {
      ...props.clipVideo,
      start_time: startTime,
      end_time: endTime,
    };
    if (from === 'dialog') {
      selectedVideo.clip_id = clipsVideo.id;
      selectedVideo.url = replaceAiUrl(clipsVideo.video_url || '');
      selectedVideo.start_time = clipsVideo.start_time || 0;
      selectedVideo.end_time = clipsVideo.end_time || 0;
      setClipsVideos([clipsVideo]);
      console.log(clipsVideo);
    } else {
      setClipsVideos([...clipsVideos.value, clipsVideo]);
    }
  }
};

const loadVideo = () => {
  if (!props.clipVideo?.video_url) return;
  console.log('loading video');

  if (props.clipVideo) {
    showLoadClipsVideoLoading();
    const {
      start_time: startTime,
      end_time: endTime,
      video_url: videoUrl,
      cover_url: coverUrl,
      preview_url: previewUrl,
    } = props.clipVideo;
    videoLoadingStatus.value = 'active';
    videoClip.value?.load({
      src: replaceAiUrl(previewUrl || videoUrl), // 优先使用previewUrl
      startTime,
      endTime,
      poster: replaceAiUrl(coverUrl || ''),
    });
  }
};

const initClipsVideo = () => {
  videoClip.value = new ClipVideo({
    container: clipsVideoRef.value,
    hideTime: page === 'templates',  // 模板页面，需要隐藏时间轴
  });
  videoClip.value.on('loaded', () => {
    hideLoadClipsVideoLoading();
  });

  videoClip.value.on('video-error', () => {
    videoLoadingStatus.value = 'error';
    window.aegis.report('loadVideo error', props.clipVideo); // 打印日志
  });
};

watch(() => props.clipVideo, loadVideo);
watch(containerWidth, (width?: number) => {
  containerHeight.value = ((width ?? 0) / (16 / 9)) * 1.05;
});

onMounted(() => {
  initClipsVideo();
});

// 组件销毁，停止播放
onUnmounted(() => {
  videoClip.value?.reset();
});
</script>
