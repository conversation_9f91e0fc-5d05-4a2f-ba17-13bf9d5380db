<template>
  <t-dialog
    v-model:visible="favorDialog"
    header="Add Collection"
    width="600"
    :confirm-on-enter="true"
    :on-cancel="() => favorDialog = false"
    :on-confirm="onFavorConfirm"
  >
    <t-form>
      <t-form-item label="File Name" name="fileName">
        <t-input v-model="formData.fileName" placeholder="folder name" />
      </t-form-item>
      <t-form-item v-if="folders.length > 0" label="Folder" name="folderId">
        <t-select v-model="formData.folderId" placeholder="select folder">
          <t-option
            v-for="item in folders" :key="item.folder_id" :label="item.name"
            :value="item.folder_id"
          />
        </t-select>
      </t-form-item>
      <t-form-item v-else label="Folder Name" name="folderName">
        <t-input v-model="formData.folderName" placeholder="folder name" />
      </t-form-item>
    </t-form>
  </t-dialog>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { Video } from 'common/service/creative/aigc_toolkit/type';
import { useAIClipFavorStore } from '@/store/creative/toolkit/ai_clips_favor.store';
import { MessagePlugin } from 'tdesign-vue-next';

const { folders } = storeToRefs(useAIClipFavorStore());
const { getFolders, favorVideoClip } = useAIClipFavorStore();

const emit = defineEmits(['success']);

const props = defineProps<{
  videoClip?: Video,
}>();

// 视频片段收藏相关
const favorDialog = ref(false);
const formData = reactive({
  fileName: '', // 自定义名称
  folderId: '', // 文件夹id（有创建文件夹时使用）
  folderName: '', // 文件夹名称（未创建过文件夹时使用）
});

const show = () => {
  favorDialog.value = true;
  setTimeout(() => {
    let videoName = props.videoClip?.video_name || '';
    if (!videoName) videoName = props.videoClip?.video_url.split('/').pop() as string;
    formData.fileName = videoName;
  });
};

async function onFavorConfirm() {
  if (!formData.folderId) {
    MessagePlugin.error('Please select folder');
    return;
  }
  const res = await favorVideoClip({
    folderId: formData.folderId as unknown as number || undefined,
    folderName: formData.folderName || undefined,
    fileName: formData.fileName,
    clip: props.videoClip as Video,
  });
  if (res.code !== 0) {
    MessagePlugin.error(res.message);
    return;
  }
  MessagePlugin.success('Star success');
  favorDialog.value = false;
  emit('success');
}

onMounted(() => {
  getFolders();
});

defineExpose({
  show,
});
</script>
