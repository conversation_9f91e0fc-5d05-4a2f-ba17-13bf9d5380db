<template>
  <BaseDialog
    :visible="true"
    title="Prompt Guidance"
    width="1000px"
    class="prompt-guidance flex overflow-hidden"
    top="5vh"
    :is-show-footer="false"
  >
    <div class="px-5 h-full w-full flex">
      <ul class="w-full space-y-4 list-decimal flex flex-col overflow-auto">
        <li
          v-for="(guidance, index) in guidanceList"
          :key="index"
        >
          <div class="flex flex-col gap-2">
            <p>{{ guidance.text }}</p>
            <div class="flex flex-row gap-2">
              <div
                v-for="(image, imageIndex) in guidance.image"
                :key="imageIndex"
                class="w-full"
              >
                <ImageViewer
                  :key="imageMap.get(image)"
                  :images="imageList"
                  :url="image"
                  :default-index="(imageMap.get(image) as number)"
                />
                <!-- <Image
                  :url="image"
                  class="w-full min-h-[10px] aspect-auto"
                  fit="contain"
                /> -->
              </div>
            </div>
          </div>
          <div
            v-if="guidance.subParagraphs"
            class="px-4 pt-4"
          >
            <ol class="w-full space-y-4 list-disc">
              <li
                v-for="(subParagraph, idx) in guidance.subParagraphs"
                :key="idx"
              >
                <p>{{ subParagraph.text }}</p>
                <div class="flex flex-row gap-2">
                  <div
                    v-for="(subImage, subImageIndex) in subParagraph.image"
                    :key="subImageIndex"
                    class="w-full"
                  >
                    <ImageViewer
                      :key="imageMap.get(subImage)"
                      :images="imageList"
                      :url="subImage"
                      :default-index="(imageMap.get(subImage) as number)"
                    />
                  </div>
                </div>
              </li>
            </ol>
          </div>
        </li>
      </ul>
    </div>
  </BaseDialog>
</template>
<script setup lang="ts">
import BaseDialog from 'common/components/Dialog/Base';
import { ref, computed } from 'vue';
import ImageViewer from '../ImageViewer/index.vue';

type Guidance = {
  text: string;
  image: [string, string];
  subParagraphs?: Guidance[];
};

const imageBaseUrl = 'https://static.aix.intlgame.com/advanced_video_search';
const guidanceList = ref<Guidance[]>([
  {
    text: 'If you want to search the video clips more precisely, you can use the text query with more details, for example, using "shooting on the top of the building" instead of "shooting".',
    image: [
      `${imageBaseUrl}/d419e27b-3585-4b4f-8e4e-6195281145a4/image1.png`,
      `${imageBaseUrl}/958dafdf-b0f9-493b-aa06-9a8a56c073b8/image2.png`,
    ],
  },
  {
    text: 'If you search the video clips contains multiple conditions, you can describe them with details in the query sentence. In addition to this, you can also use "," to separate different conditions to explicitly tell the search engine to return the clips that satisfying all the conditions. ',
    image: [
      `${imageBaseUrl}/e8df0f19-4588-4599-a0e7-be5672d53a7e/image3.png`,
      `${imageBaseUrl}/8389f427-bf6e-4670-a77e-4616a559ebc1/image4.png`,
    ],
  },
  {
    text: 'If you want to search the clips from a subset with a certain topic, you can use ":" with putting the topic in the fronter and the query text in the later. For instance, "zombie: shooting, a group of zombies" means you search the clips both containing "shooting" and "a group of zombies" from the "zombie" videos. Note that the model only supports "zombie" topic currently.',
    image: [
      `${imageBaseUrl}/f00ec5a8-9c3d-4396-bd8e-66d905199831/image5.png`,
      `${imageBaseUrl}/34bdd0e2-3d1b-44c3-b69f-e0fe2096d4e7/image6.png`,
    ],
  },
  {
    text: 'If you can not search the clips that meet your needs, you can change the words with similar meanings or make some modifications on the query text. For example, if you want to search the clips that some people are celebrating the victory, the model may not return the satisfying clips you want. You can change the text query to "teammates are celebrating the victory", and vice verse. ',
    image: [
      `${imageBaseUrl}/c3341984-7e9f-464d-919a-0ac61d78f2f9/image7.png`,
      `${imageBaseUrl}/c735aa40-f713-413d-afed-c54e20ff5a17/image8.png`,
    ],

    subParagraphs: [
      {
        text: 'For another example, search results with the text query of "zombie: shooting, a group of zombies" and "zombie: shooting, zombies" are also different. You may need to adjust the text query properly to obtain good results.',
        image: [
          `${imageBaseUrl}/17caa433-1da6-4434-9db0-e338ecdafe5e/image9.png`,
          `${imageBaseUrl}/dc6b9697-ebbd-48b5-9875-a7cc7d1bdaf9/image10.png`,
        ],
      },
    ],
  },

  {
    text: 'For some queries, you can use more detailed text to describe them. For example, you want to search the clips of lurk attack. If you use the query text of "lurk attack" to search and the model does not return the correct clips. Then, you can try to use the text like "a man hiding behind the stone gives the person a attack". Because the text contain more detailed semantics, the model may be easy to understand.',
    image: [
      `${imageBaseUrl}/801d0d87-7593-4e2f-af95-96f6eb07abf5/image11.png`,
      `${imageBaseUrl}/2069f531-bccf-4c3e-bb2d-60c13df977cb/image12.png`,
    ],
  },
  {
    text: 'If you want to search the clips containing the same person, you can describe the appearance in the query text, such as "a man wearing a white shirt and a steel helmet".',
    image: [
      `${imageBaseUrl}/ae8f4cdc-70ec-4dab-bdf8-2945dd414dbc/image13.png`,
      `${imageBaseUrl}/45812fe3-b2c8-4021-a265-b10ddd1eea77/image14.png`,
    ],
  },
  {
    text: 'In some cases, if you search the clips with too detailed query text, the search engine may be hard to return the satisfying clips. You can remove some conditions and let the search engine return more clips, then you try to select the correct clips from the returned results.',
    image: [
      `${imageBaseUrl}/145e0660-06ce-40bb-85fa-5a69aef87861/image15.png`,
      `${imageBaseUrl}/194cd9f1-0a04-40e1-9a4b-8b768c97888f/image16.png`,
    ],
  },
  {
    text: 'If you don\'t obtain the wanted clips, you can modify the query text and try again. Modifying the query includes but does not limited to the following ways: changing words, adding/removing conditions, expressing your query in another way. For example, the results of "rifle shooting" and "shooting" may be different. As stated before, the results of "a group of zombies" and "zombies" are also different. You need to adjust the query text adaptively to obtain the best results.',
    image: [
      `${imageBaseUrl}/4ab470e2-1a82-45d9-a892-418dcdfcd574/image17.png`,
      `${imageBaseUrl}/0999fc41-aea3-4d82-8449-b4412a07d318/image18.png`,
    ],
  },
]);

const imageMap = computed<Map<string, number>>(() => accordingToGuidanceListGenerateImageList(guidanceList.value));
const imageList = computed(() => [...imageMap.value.keys()]);

const accordingToGuidanceListGenerateImageList = (guidanceList: Guidance[]): Map<string, number> => {
  let index = 0;
  const cacheMap = new Map(new Map());

  function dfs(guidanceList: Guidance[]): void {
    guidanceList.forEach((cur) => {
      cur.image.forEach((imageUrl) => {
        cacheMap.set(imageUrl, index);
        index += 1;
      });
      if (cur.subParagraphs && cur.subParagraphs.length > 0) {
        dfs(cur.subParagraphs);
      }
    });
  }
  dfs(guidanceList);

  return cacheMap;
};

</script>

<style lang="scss" scoped>
:global(.prompt-guidance .t-dialog) {
  @apply w-[60%] max-w-[1000px] h-[89vh] overflow-hidden;
}
:global(.prompt-guidance .t-dialog__body) {
  @apply w-full h-full overflow-hidden;
}
</style>
