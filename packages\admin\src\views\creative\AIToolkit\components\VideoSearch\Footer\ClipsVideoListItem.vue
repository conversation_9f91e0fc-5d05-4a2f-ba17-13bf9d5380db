<template>
  <div class="flex flex-col cursor-pointer  h-full aspect-[16/14] rounded-default overflow-hidden">
    <div class="relative">
      <template v-if="props.clipsVideo.cover_url">
        <Image
          :url="replaceAiUrl(props.clipsVideo.cover_url)"
          class="w-full aspect-video"
        />
      </template>
      <template v-else>
        <video :src="replaceAiUrl(props.clipsVideo.video_url || '')" class="w-full aspect-video" />
      </template>
      <div
        class="h-[20px] w-[20px] absolute right-[4px] -top-[2px] p-[2px] z-10 cursor-pointer"
        @click.stop="() => deleteClipsVideo?.(props.clipsVideo)"
      >
        <t-icon
          class="h-[20px] w-[20px] rounded-full hover:text-black hover:text-opacity-60"
          name="close-circle-filled"
        />
      </div>
    </div>

    <div
      ref="textRef"
      v-auto-animate
      class="text flex justify-center mt-2 cursor-text w-full"
      @click.stop="onClick"
    >
      <template v-if="!focused">
        <Text
          class="inline-block w-full"
          :overflow="true"
          :tool-tip="true"
          :content="props.clipsVideo.video_name ?? ''"
        />
      </template>
      <div
        v-if="focused"
        class="h-full w-full"
      >
        <Input
          ref="inputRef"
          v-model="text"
          :autofocus="true"
          class="!w-[124px] h-[32px]"
          @blur="onInputBlur"
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { inject, ref } from 'vue';
import { ClipsVideo } from 'common/service/creative/aigc_toolkit/type';
import Input from 'common/components/Input';
import { InputValue } from 'tdesign-vue-next';
import { useAIClipStore } from '@/store/creative/toolkit/ai_clips.store';
import { storeToRefs } from 'pinia';
import Image from '../Image/index.vue';
import Text from 'common/components/Text';
import { replaceAiUrl } from '@/util/creative/replaceUrl';
interface IProps {
  clipsVideo: ClipsVideo;
}
const props = defineProps<IProps>();
const { clipsVideos } = storeToRefs(useAIClipStore());
const { setClipsVideos } = useAIClipStore();


const deleteClipsVideo = inject<(video: ClipsVideo) => void>('deleteClipsVideo');

const textRef = ref();
const inputRef = ref<InstanceType<typeof Input>>();
const focused = ref(false);
const text = ref<string>(props.clipsVideo.video_name ?? '');

const onClick = () => {
  focused.value = true;
};

const onInputBlur = (value: InputValue) => {
  focused.value = false;
  if (value) {
    const { clipsVideo } = props;
    clipsVideo.video_name = value as string;
    setClipsVideos([...clipsVideos.value]);
  }
};
</script>

<style lang="scss" scoped>
:deep(.t-input) {
  width: 150px;
  height: 30px;
}
</style>
