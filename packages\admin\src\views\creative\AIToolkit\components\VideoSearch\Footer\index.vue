<template>
  <div
    v-auto-animate
    class="h-full w-full flex flex-col bg-white overflow-auto"
  >
    <div class="flex justify-between">
      <div class="title font-semibold">Selected Clips</div>
    </div>
    <template v-if="clipsVideos.length">
      <div class="flex-1 bg-white w-full overflow-hidden mt-[10px] h-full min-h-[100px]">
        <ClipsVideoList :video-list="clipsVideos" />
      </div>
    </template>
    <template v-else>
      <div class="w-full h-full flex justify-center items-center text-black text-opacity-40 text-[14px]">
        Please select the clips you want to export from above.
      </div>
    </template>
    <ExportClipsVideoDialog
      :visible="dialogVisible"
      :video-clips-task="videoClipsTask"
      @close="closeDialog"
    />
    <t-dialog
      v-model:visible="dialogProjectfileVisible"
      @confirm="exportProjectfileConfirm"
    >
      <template #header>
        <div>
          <span>Languages</span>
          <Tooltip
            content="Materials for certain languages are still in production, possibly lacking audios or announcements.
                We will continue to update the materials."
          >
            <InfoCircleIcon class="h-[16px]" />
          </Tooltip>
        </div>
      </template>
      <div class="relative h-[40px]" :class="{ 't-is-error': isLanguageError}">
        <t-select
          v-if="gameCode === 'hok_prod'"
          v-model="exportedLanguages" label="Language："
          multiple
          :options="CLIPS_LANGUAGE_OPTIONS" placeholder="Please select a language"
          class="mr-[16px]"
          @change="onLanguaChange"
        />
        <div v-if="isLanguageError" class="t-input__extra bottom-[-15px]">Please select a language</div>
      </div>
    </t-dialog>
    <div v-if="from !== 'dialog'" class="export_clips_video flex justify-end">
      <div class="flex">
        <t-dropdown
          v-if="gameCode === 'hok_prod'"
          :disabled="clipsVideosCount === 0"
          :loading="exportClipsVideoLoading"
          :options="[{content: 'Video', value: 'video'}, { content: 'Project file', value: 'project_file' }]"
          :max-column-width="120" :max-height="200" @click="exportClipsVideoCheck"
        >
          <t-button
            theme="primary"
            :disabled="clipsVideosCount === 0"
          >
            <span class="flex items-center px-[8px]">
              {{ exportClipsVideoBtnText }}
              <chevron-down-icon size="16" />
            </span>
          </t-button>
        </t-dropdown>
        <t-button
          v-else
          theme="primary"
          :loading="exportClipsVideoLoading"
          :disabled="clipsVideosCount === 0"
          :content="exportClipsVideoBtnText"
          @click="() => exportClipsVideo({value: 'video'})"
        >
          <template #icon>
            <SvgIcon class="mr-[16px] fill-black" name="video-play" size="20" />
          </template>
        </t-button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, provide, inject } from 'vue';
import { useAIClipStore } from '@/store/creative/toolkit/ai_clips.store';
import ClipsVideoList from './ClipsVideoList.vue';
import { storeToRefs } from 'pinia';
import { EmitVideoClip, ClipsVideoTask } from 'common/service/creative/aigc_toolkit';
import ExportClipsVideoDialog from '../Dialog/ExportClipsVideoDialog.vue';
import SvgIcon from 'common/components/SvgIcon';
import { CLIPS_LANGUAGE_OPTIONS } from '../constant';
import { useGlobalGameStore } from '@/store/global/game.store';
import { ChevronDownIcon, InfoCircleIcon } from 'tdesign-icons-vue-next';
import { Tooltip } from 'tdesign-vue-next';

const from = inject('from', 'page') as string;
const { clipsVideos, currenSearchText, currentSearchType } = storeToRefs(useAIClipStore());
const { setClipsVideos } = useAIClipStore();
const { gameCode } = storeToRefs(useGlobalGameStore());


const exportedLanguages = ref<string[]>(['EN']);

const dialogVisible = ref(false);
const dialogProjectfileVisible = ref(false);
const clipsVideosCount = computed(() => clipsVideos.value?.length ?? 0);
const exportClipsVideoBtnText = computed(() => {
  if (clipsVideosCount.value === 0) return 'Export Clips';
  return `Export ${clipsVideosCount.value} Clips`;
});
const videoClipsTask = ref<ClipsVideoTask[]>([]);
const exportClipsVideoLoading = ref(false);
const isLanguageError = ref(false);

const onLanguaChange = () => {
  if (exportedLanguages.value.length === 0) {
    isLanguageError.value = true;
    return false;
  }
  isLanguageError.value = false;
  return true;
};
const exportClipsVideoCheck = async (params: { value: string }) => {
  if (params.value === 'video') {
    return exportClipsVideo(params);
  }
  dialogProjectfileVisible.value = true;
};

const exportProjectfileConfirm = () => {
  if (isLanguageError.value) {
    return;
  }
  isLanguageError.value = false;
  dialogProjectfileVisible.value = false;
  exportClipsVideo({ value: 'project_file' });
};


const exportClipsVideo = async (params: { value: string }) => {
  try {
    exportClipsVideoLoading.value = true;
    const clipsVideoTaskList = await EmitVideoClip({
      video_list: clipsVideos.value,
      text: currentSearchType.value === 'text' ? currenSearchText.value : undefined,
      languages: exportedLanguages.value,
      video_only: params.value === 'video',
    });
    videoClipsTask.value = clipsVideoTaskList;
    dialogVisible.value = true;
    exportClipsVideoLoading.value = false;
  } catch (error) {}
};

const deleteClipsVideo = (clipsVideo: any) => {
  clipsVideos.value = clipsVideos.value.filter(video => video !== clipsVideo);
  setClipsVideos(clipsVideos.value);
};

const closeDialog = () => {
  dialogVisible.value = false;
};
provide('deleteClipsVideo', deleteClipsVideo);
</script>

