<template>
  <t-image
    fit="cover"
    :src="replaceAiUrl(props.url)"
    :lazy="true"
  >
    <template #loading>
      <div
        class="flex items-center justify-center w-full h-full bg-black
        bg-opacity-60 backdrop-blur-[10px] rounded-default"
      >
        Loading
        <!-- <Loading
          loading
          size="small"
          :delay="0"
        /> -->
      </div>
    </template>
    <template #error>
      <ImageErrorIcon size="20" />
    </template>
  </t-image>
</template>
<script lang="ts" setup>
import { ImageErrorIcon } from 'tdesign-icons-vue-next';
import { replaceAiUrl } from '@/util/creative/replaceUrl';

interface IProps {
  url: string;
}

const props = defineProps<IProps>();
</script>
<style lang="scss" scoped></style>
