<template>
  <div class="left-section h-full flex flex-col w-full" :class="from + '-section'">
    <t-form ref="formRef1" :data="formData" :rules="rules">
      <t-form-item
        v-if="selectGameVisible"
        name="gameCode"
        label="Select game"
        label-width="120px"
        label-align="left"
        class="text-base font-semibold"
      >
        <t-select
          v-model="formData.gameCode"
          clearable
        >
          <t-option
            v-for="item in GAME_OPTIONS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <div class="flex flex-row w-full gap-2 h-[48px]">
              <div class="aspect-square h-full p-2">
                <t-image
                  :alt="`${item.value}.png`"
                  fit="cover"
                  shape="round"
                  :lazy="true"
                  loading="loading..."
                  :src="replaceAiUrl(item.image)"
                />
              </div>
              <div class="grow flex items-center">{{ item.label }}</div>
            </div>
          </t-option>
          <template #valueDisplay>
            <div class="w-full h-full py-1 flex flex-row gap-2">
              <div
                v-if="curSelectedGame"
                class="aspect-square h-full"
              >
                <t-image
                  fit="cover"
                  shape="round"
                  :src="replaceAiUrl(curSelectedGame.image)"
                />
              </div>
              <div class="grow flex items-center">
                <template v-if="curSelectedGame?.value">
                  <span>{{ curSelectedGame.label }}</span>
                </template>
                <template v-else>
                  <span class="text-black-placeholder">Please select game.</span>
                </template>
              </div>
            </div>
          </template>
        </t-select>
      </t-form-item>
      <t-radio-group v-model="searchType" class="mb-[12px]">
        <t-radio-button value="text">
          <div class="flex justify-center items-center">
            <div class="mr-[8px]">Search by Text</div>
            <div v-if="!isHok" class="cursor-pointer" @click="showPromptDialog">
              <t-popup
                content="Click to see how to use."
                placement="top-left"
                show-arrow
                destroy-on-close
              >
                <SvgIcon name="video_search_help" size="16" />
              </t-popup>
            </div>
            <div v-else class="cursor-pointer">
              <t-popup
                placement="bottom-left"
                show-arrow
                destroy-on-close
              >
                <SvgIcon name="error" size="16" />
                <template #content>
                  <p class="max-w-[400px] ml-4 my-2 mr-1">
                    Please enter hero names or actions. (English or Chinese)<br>
                    Examples:yalian; yalian kills three enemies; yalian kills lvbu
                  </p>
                </template>
              </t-popup>
            </div>
          </div>
        </t-radio-button>
        <t-radio-button value="media">
          <div class="flex justify-center items-center">
            <div>Search by image/video</div>
          </div>
        </t-radio-button>
      </t-radio-group>
    </t-form>

    <div class="relative">
      <t-form
        v-show="searchType === 'text'" ref="formRef2" :data="formData"
        :rules="rules"
      >
        <t-form-item
          class="text-form-item bg-white rounded-default mb-[16px]"
          name="text"
        >
          <Textarea
            v-model="formData.text"
            :maxlength="3000"
            placeholder="Please enter text"
          />
        </t-form-item>
      </t-form>
      <t-form
        v-show="searchType === 'media'"
        ref="formRef3"
        class="relative mb-[12px]" :data="formData" :rules="rules"
        layout="inline"
        label-align="top"
      >
        <t-form-item name="image" class="flex-1">
          <template #label>
            <div class="flex items-center">
              <div class="mr-[8px]">Image/Video</div>
              <div class="cursor-pointer">
                <t-popup
                  content="Please upload an image/video to search clips with similar content.
                Supported format: .png/.jpg/.jpeg/.mp4/.mov"
                  placement="top-left"
                  show-arrow
                  destroy-on-close
                >
                  <SvgIcon name="error" size="16" />
                </t-popup>
              </div>
            </div>
          </template>
          <Uploader
            ref="uploaderRef"
            accept="image/png, image/jpeg, image/jpg, video/mp4, video/mov, video/quicktime"
            file-path="ai_toolkit/temp_files"
            :multiple="true"
            :max-num="5"
            @change="onFileChange"
          />
        </t-form-item>
        <t-form-item name="description" class="flex-1" style="margin-right: 0">
          <template #label>
            <div class="flex items-center">
              <div class="mr-[8px]">Description(optional)</div>
              <div class="cursor-pointer">
                <t-popup
                  content="If you add a text description, the result will return clips relevant to
                   your media and text content."
                  placement="top-left"
                  show-arrow
                  destroy-on-close
                >
                  <SvgIcon name="error" size="16" />
                </t-popup>
              </div>
            </div>
          </template>
          <t-textarea
            v-model="formData.description" class="w-full" placeholder=""
            :autosize="{ minRows: from === 'dialog' ? 3 : 6, maxRows: 'dialog' ? 3 : 6 }"
          />
        </t-form-item>
      </t-form>
      <div
        :class="searchType === 'media' ?
          `absolute right-[2px] ${from === 'page' ? 'bottom-[12px]' : 'bottom-[2px]'}` : 'flex justify-end mt-[12px]'"
      >
        <t-button :loading="sidebarLoading" @click="onSubmit">Search</t-button>
      </div>
    </div>

    <div class="w-full flex flex-1 flex-col overflow-hidden">
      <div class="label font-semibold text-[14px]">Select & Edit Video Clips</div>
      <div v-if="!isDemo && isPubgm" class="filter flex justify-between my-[4px]">
        <div class="flex">
          <InputCascader
            v-if="!isHok"
            v-model="formData.labels"
            class="clip-label-select"
            title="Label"
            :labels="['First', 'Second', 'Third']"
            :options="labelOptions"
          />
          <t-select
            v-model="formData.section" label="Section: " class="w-[160px] ml-[12px]"
            :options="SECTION_OPTIONS"
          />
          <t-select
            v-model="formData.type" label="Type: " class="w-[160px] ml-[12px]"
            :options="TYPE_OPTIONS"
          />
        </div>
        <t-select
          v-model="formData.sort_by" label="Sort by: " class="w-[188px]"
          placholder="Sort by" :options="SORT_OPTIONS"
        />
      </div>
      <div class="relative h-full w-full mt-[8px] overflow-hidden">
        <div
          v-show="sidebarLoading"
          class="flex h-full w-full justify-center items-center absolute z-10 bg-black bg-opacity-60 rounded-default"
        >
          <t-loading size="20px" />
        </div>
        <template v-if="videoList.length">
          <VideoList
            ref="videoListRef"
            :video-list="videoList"
            :can-load-more="canLoadMore"
            :hide-menu="isDemo || !isPubgm"
            @load-more="onLoadMore"
          />
        </template>
        <template v-else-if="!videoList.length && !sidebarLoading">
          <div class="flex justify-center items-center h-full w-full">
            The reason for this can be<br>
            - EITHER No videos matched with your text query<br>
            - OR Your text query is unclear, please follow the instructions given above for text prompt
            <br>
          </div>
        </template>
      </div>
    </div>
  </div>
  <SearchByPromptHelp
    :visible="promptDialogVisible"
    @close="hidePromptDialog"
  />
</template>

<script setup lang="ts">
import Textarea from 'common/components/Textarea';
import VideoList from '../VideoList/VideoList.vue';
import { computed, inject, onMounted, provide, reactive, ref, watchEffect } from 'vue';
import { FormRules, TdOptionProps } from 'tdesign-vue-next';
import { Video } from 'common/service/creative/aigc_toolkit/type';
import { getVideoByText } from 'common/service/creative/aigc_toolkit';
import { useAIClipStore } from '@/store/creative/toolkit/ai_clips.store';
import { useAiSVEditorStore } from '@/store/creative/toolkit/ai_smart_video_editor.store';
import { storeToRefs } from 'pinia';
import { sleep } from 'common/utils/common';
import Uploader from '../../Uploader/index.vue';
import { useRouteQuery } from '@vueuse/router';
import { SvgIcon } from 'common/components/SvgIcon';
import SearchByPromptHelp from '../Dialog/SearchByPromptHelp.vue';
import { useVisible } from 'common/compose/useVisible';
import { GAME_OPTIONS, SECTION_OPTIONS, TYPE_OPTIONS, SORT_OPTIONS } from '../constant/index';
import { clipBus } from '../../../utils/event';
import { dealUrl } from '../../../utils/tools';
import { getLabelOptions } from 'common/service/creative/library/manage-labels';
import InputCascader from 'common/components/InputCascader';
import { UploadFile as TdUploadFile } from 'tdesign-vue-next/es/upload/type';
import { UploadReq } from 'common/components/FileUpload';
import { replaceAiUrl } from '@/util/creative/replaceUrl';


const from = inject('from', 'page') as string;
const { sidebarLoading, offset, currenSearchText, currentSearchType } = storeToRefs(useAIClipStore());
const { showSidebarLoading, hideSidebarLoading } = useAIClipStore();
const { setOffset } = useAIClipStore();
const { replaceInfo, addClipInfo } = useAiSVEditorStore();
const { clipAction } = storeToRefs(useAiSVEditorStore());

const gameCode = useRouteQuery<string>('game');
const queryText = useRouteQuery<string>('text').value || '';
let queryClipUrl = useRouteQuery<string>('clip_url').value || '';
if (queryClipUrl) queryClipUrl = dealUrl(queryClipUrl);

const isHok = computed(() => gameCode.value.includes('hok'));
const isDemo = computed(() => gameCode.value.includes('demo'));
const isPubgm = computed(() => gameCode.value.includes('pubgm'));
const { visible: promptDialogVisible, show: showPromptDialog, hide: hidePromptDialog } = useVisible(false);

const formRef1 = ref(); // 游戏下拉选择表单
const formRef2 = ref(); // textarea表单
const formRef3 = ref(); // 素材查询表单

const formData = reactive<{
  text: string,
  description: string,
  image: string[],
  labels: string[],
  sort_by: string,
  type: string,
  section: string,
  mode: number,
  gameCode: string,
}>({
  text: '', // 文本搜索（弹框方式需要带入text）
  description: '', // 视频文本搜索
  image: queryClipUrl ? [queryClipUrl] : [],
  labels: [],
  sort_by: '',
  section: 'All',
  type: 'All', // 片段类型
  mode: 1,
  gameCode: '',
});

const uploaderRef = ref();
const onFileChange = async (fileList: TdUploadFile[]) => {
  if (fileList.length === 0) {
    formData.image = [];
    return;
  }
  formData.image = fileList.map(item => (item.response as UploadReq).url) as string[];
};

const arrowGameCodeList = ['demo', 'miniclip'];
const selectGameVisible = computed(() => arrowGameCodeList.includes(gameCode.value ?? ''));
const curSelectedGame = computed(() => GAME_OPTIONS.find(item => item.value === formData.gameCode));
const pageSize = ref(50);

const videoListRef = ref();
const canLoadMore = ref(true);
const selectText = ref('');
const videoList = ref<Video[]>([]);

const searchType = ref(queryClipUrl ? 'media' : 'text');
const uploadRef = ref(); // 上传对象
const dataLoading = ref(false); // 数据请求中

const rules: FormRules<typeof formData> = {
  text: [{
    validator: () => {
      if (formData.text !== '') return true;
      if (searchType.value === 'media') return true;
      return {
        message: 'Please enter text to enable video search.', result: false, type: 'error',
      };
    },
  }],
  image: [{
    validator: () => {
      if (!!formData.image.length) return true;
      if (searchType.value === 'text') return true;
      return {
        message: 'Please upload an image/video to enable video search.', result: false, type: 'error',
      };
    },
  }],
  gameCode: [
    {
      required: true,
    },
  ],
};

const labelOptions = ref([]);
const TypeMap = {
  end: 'Ending',
  opening: 'Opening',
  body: 'All',
};

const onSubmit = async () => {
  const res1 = await formRef1.value?.validate();
  const res2 = await formRef2.value?.validate();
  const res3 = await formRef3.value?.validate();
  if (res1 === true && res2 === true && res3 === true) {
    triggerSearch();
  }
};

async function triggerSearch() {
  setOffset(0);
  videoList.value = [];
  selectText.value = formData.text;
  showSidebarLoading();
  const newVideoList = await getData();
  if (newVideoList.length < pageSize.value) {
    canLoadMore.value = false;
  } else {
    canLoadMore.value = true;
    setOffset(offset.value + pageSize.value);
  }
  hideSidebarLoading();
  videoList.value = [...newVideoList];
}

const onLoadMore = async () => {
  if (canLoadMore.value === false) return; // 数据到底，忽略
  if (dataLoading.value) return; // 数据正在请求，忽略

  const startTime = new Date().getTime();
  if (!selectText.value && !formData.image[0]) return;

  videoListRef.value?.showLoading();
  const newVideoList = await getData();
  if (newVideoList.length < pageSize.value) {
    canLoadMore.value = false;
  }

  const endTime = new Date().getTime();
  const time = endTime - startTime;
  time < 1000 && (await sleep(1000 - time));

  videoList.value = [...videoList.value, ...newVideoList];
  setOffset(offset.value + pageSize.value);
  videoListRef.value?.hideLoading();
};

watchEffect(() => {
  currenSearchText.value = searchType.value === 'text' ? formData.text : formData.description;
  currentSearchType.value = searchType.value;
});

const getData = async () => {
  dataLoading.value = true;
  const searchText = searchType.value === 'text' ? formData.text : formData.description;
  const gameCode = !!formData.gameCode && selectGameVisible ? formData.gameCode : undefined;
  const image = searchType.value === 'media' ? (formData.image.join(',') || '') : '';
  const res = await getVideoByText({
    text: searchText || '',
    limit: pageSize.value,
    offset: offset.value,
    image,
    type: formData.type,
    section: formData.section,
    mode: formData.mode,
    filter_type: formData.labels.join(','),
    sort_type: formData.sort_by,
    game_code: gameCode,
  });
  res.list = res.list.map((item) => {
    // eslint-disable-next-line no-param-reassign
    item.video_url = replaceAiUrl(item.video_url);
    const parsedUrl = new URL(item.video_url);
    const hostName = `${parsedUrl.protocol}//${parsedUrl.hostname}`;
    return Object.assign({
      clip_url: `${hostName}/clip_retrieval/pubgm_all_clips/${encodeURIComponent(item.clip_name)}`, // 片段url
    }, item);
  });
  dataLoading.value = false;
  return res.list;
};

provide('onLoadMore', onLoadMore);
provide('searchInNewTab', searchInNewTab);

// 跳转到新的搜索页面
function searchInNewTab(video: Video) {
  const { protocol, host, pathname, search } = location;
  const url = `${protocol}//${host}${pathname}${search}&text=${formData.text}&clip_url=${video.clip_url}`;
  window.open(url);
}

// 初始化参数
async function initParams() {
  const labels = await getLabelOptions();
  const filterLabels = labels.filter((item: TdOptionProps) => item.value === '设计');
  labelOptions.value = filterLabels.length === 0 ? labels : filterLabels;
  if (from === 'dialog') {
    let initText = '';
    let initType = 'All';
    // 添加视频，根据封面搜索
    if (clipAction.value === 'add') {
      initText = addClipInfo.text;
      initType = addClipInfo.type;
      if (addClipInfo.targetUrl.length > 0) {
        const files = addClipInfo.targetUrl.map(item => ({ type: item.type, status: 'success', name: 'media', url: item.url }));
        uploaderRef.value.setFiles(files);
        searchType.value = 'media';
        formData.image = addClipInfo.targetUrl.map(item => item.url as string);
        formData.description = initText;
      }
    }

    // 替换视频，根据封面搜索
    if (clipAction.value === 'replace') {
      initText = replaceInfo.scene?.text as string;
      initType = replaceInfo.type;
      if (replaceInfo.targetUrl.length > 0) {
        const files = replaceInfo.targetUrl.map(item => ({ type: item.type, status: 'success', name: 'media', url: item.url }));
        uploaderRef.value.setFiles(files);
        searchType.value = 'media';
        formData.image = replaceInfo.targetUrl.map(item => item.url as string);
        formData.description = initText;
      }
    }
    formData.text = queryText || initText;
    formData.section = TypeMap[initType as keyof typeof TypeMap];
    // triggerSearch();
  }

  // url里带上查询片段参数
  if (queryClipUrl) {
    formData.image = [queryClipUrl];
    uploaderRef.value.setFiles([
      {
        type: 'video/mp4',
        status: 'success',
        name: 'video',
        url: queryClipUrl,
      },
    ]);
    triggerSearch();
  }
}

onMounted(async () => {
  // 接收右键菜单消息
  clipBus.on((event: string) => {
    const video = JSON.parse(event) as Video;
    uploadRef.value.setFiles([{
      url: video.video_url,
      type: 'video/mp4',
    }]);
    // 如果当前在文本搜索，需要带上文本信息
    if (searchType.value === 'text') {
      formData.description = formData.text;
    }
    searchType.value = 'media';
  });

  initParams();
});
</script>
<style lang="scss" scoped>
.t-popup__content {
  .clip-label-select {
    width: 640px;
  }
}
</style>
<style lang="scss">
.left-section {
  .text-form-item {
    .t-form__controls {
      margin-left: 0 !important;
    }
  }

  .t-upload__dragger {
    width: 300px;
    height: 200px;

    .t-upload__dragger-btns {
      bottom: 10px;
    }
  }
}

.dialog-section {
  .t-upload__dragger {
    width: 180px;
    height: 100px;
  }
}
</style>
