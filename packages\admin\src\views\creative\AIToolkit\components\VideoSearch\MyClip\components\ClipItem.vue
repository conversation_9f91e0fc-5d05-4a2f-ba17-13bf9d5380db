<template>
  <Item
    class="clip-item"
    :class="[active ? 'clip-item-active': '']"
    :data="videoData"
    mode="modal"
    :need-detail-drawer="false"
    :need-preview="false"
    :need-checkbox="false"
    @on-preview-detail="onPreview"
  >
    <template #bottom>
      <div class="p-[10px] font-[500] text-xs text-black-primary cursor-pointer" @click="select">
        <p class="mt-0 mb-[6px] leading-[16px] flex items-center justify-between h-[16px]">
          <hover-title :title="data.file_name" />
        </p>
        <p class="mt-0 mb-[6px] leading-[16px] flex items-center h-[16px]">
          Created:<span class="font-bold ml-[4px]">{{ data.create_date }}</span>
        </p>
        <div class="action flex justify-end">
          <img
            :src="CDN + '/ai_toolkit/images/star2.png'" class="cursor-pointer w-[18px] h-[18px]" alt=""
            @click="cancelStar"
          >
          <t-loading
            v-if="downloading" :loading="downloading" class="ml-[6px]"
            size="small"
          />
          <download-icon
            v-else class="cursor-pointer ml-[6px]" size="18px"
            @click="downloadVideo"
          />
        </div>
      </div>
    </template>
  </Item>
  <VideoDialog
    ref="videoDialog"
    :file-name="data.file_name"
    :video-url="replaceAiUrl(data.video_url)"
    :start-time="data.start_time"
    :end-time="data.end_time"
  />
</template>
<script setup lang="ts">
import { PropType, computed, ref, inject } from 'vue';
import { DownloadIcon } from 'tdesign-icons-vue-next';
import Item from 'common/components/creative/MaterialItem/Item.vue';
import type { FavorClip } from 'common/service/creative/aigc_toolkit/type';
import HoverTitle from 'common/components/creative/MaterialItem/HoverTitle.vue';
import { FILE_CDN_COM as CDN } from 'common/config';
import { useAIClipFavorStore } from '@/store/creative/toolkit/ai_clips_favor.store';
import { MessagePlugin } from 'tdesign-vue-next';
import { IMaterialItem } from 'common/components/creative/MaterialItem';
import VideoDialog from '../../../VideoDialog.vue';
// import { downloadFile } from 'common/components/FileUpload/util';
import { exportCommonVideo } from 'common/service/creative/aigc_toolkit/smart_video';
import { useAiSVEditorStore } from '@/store/creative/toolkit/ai_smart_video_editor.store';
import { storeToRefs } from 'pinia';
import { replaceAiUrl } from '@/util/creative/replaceUrl';

const from = inject('from', 'page') as string;
// const ClipBucket = 'ua-sg-1300342648';
// const ClipCDN = 'https://ua-cdn.intlgame.com';

const { getFolderClips, getClips, delFavorClip } = useAIClipFavorStore();

const { setClipVideo } = useAiSVEditorStore();
const { selectedVideo } = storeToRefs(useAiSVEditorStore());

const props = defineProps({
  data: {
    type: Object as PropType<FavorClip>,
    default: () => {},
  },
});

const downloading = ref(false);
const downloadVideo = async () => {
  const { data } = props;
  const url =  replaceAiUrl(data.video_url);
  // const mediaKey = replaceAiUrl(data.video_url).replace(`${ClipCDN}/`, '');
  downloading.value = true;
  await exportCommonVideo(url, data.file_name);
  // const link = document.createElement('a');
  // link.setAttribute('href', url);
  // link.setAttribute('target', '_blank');
  // link.setAttribute('download', data.file_name);
  // link.click();
  downloading.value = false;
  // downloadFile({
  //   key: mediaKey,
  //   bucket: ClipBucket,
  //   downloadName: data!.file_name,
  //   finish: () => {
  //     downloading.value = false;
  //   },
  // });
};

const videoData = computed<IMaterialItem>(() => ({
  id: `${props.data.clip_id}`,
  title: props.data.file_name,
  type: 'video',
  url: replaceAiUrl(props.data.clip_url || props.data.video_url),
  poster: replaceAiUrl(props.data.cover_url),
  createdAt: props.data.create_at,
  ext: {},
  syncedMedia: [],
}));

// 视频预览
const videoDialog = ref();
const onPreview = () => {
  videoDialog.value.show();
};

const cancelStar = async () => {
  await delFavorClip(props.data.clip_name);
  MessagePlugin.success('Cancel success');
  getFolderClips();
  getClips(); // 重新获取所有收藏数据
};

const active = computed(() => videoData.value.id === selectedVideo.value.clip_id && from === 'dialog');
const select = () => {
  const { data }: { data: FavorClip } = props;
  setClipVideo(data.video_url, videoData.value.id, data.start_time, data.end_time);
};
</script>
<style scoped lang="scss">
.clip-item {
  height: 242px;
}
.clip-item-active {
  border: 2px solid var(--aix-border-color-brand);
}
</style>
