<template>
  <div class="ml-[12px] bg-white rounded-default relative flex-1 flex flex-col">
    <div class="p-[20px] pt-[20px] mb-[32px] h-[36px] flex justify-end">
      <t-input
        v-model="keywords" class="w-[200px]" placeholder="Search"
        clearable
      >
        <template #suffixIcon>
          <search-icon :style="{ cursor: 'pointer' }" />
        </template>
      </t-input>
    </div>
    <div v-loading="loading" class="clip-content">
      <clip-item v-for="(item, index) in favorClips" :key="index" :data="item" />
      <div v-if="favorClips.length === 0 && !loading" class="clip-nodata">No Data</div>
    </div>
    <t-pagination
      v-model:current="pageNum"
      class="clip-pagination rounded-default"
      size="small"
      :total="total"
      :default-page-size="pageSize"
      :page-size-options="[10, 20, 50, 100]"
      @current-change="onPageChange"
      @page-size-change="onSizeChange"
    />
  </div>
</template>
<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { useAIClipFavorStore } from '@/store/creative/toolkit/ai_clips_favor.store';
import ClipItem from './ClipItem.vue';
import { SearchIcon } from 'tdesign-icons-vue-next';
import { watchDebounced } from '@vueuse/core';

const { onPageChange, onSizeChange, getFolderClips } = useAIClipFavorStore();
const {
  total, pageSize, pageNum, keywords, favorClips, loading,
} = storeToRefs(useAIClipFavorStore());

watchDebounced(() => keywords.value, () => {
  getFolderClips();
}, { debounce: 500 });
</script>
<style lang="scss">
.clip-content {
  display: grid;
  overflow-y: auto;
  height: 100%;
  justify-content: space-between;
  grid-template-columns: repeat(auto-fill, minmax(160px, 193px));
  align-items: start;
  row-gap: 16px;
  -moz-column-gap: 16px;
  column-gap: 16px;
  padding-left: 16px;
  padding-bottom: 16px;
  padding-right: 16px;
}
.clip-pagination {
  width: 100%;
  padding: 16px;
  background-color: var(--aix-bg-color-white-primary);
  filter: drop-shadow(0 -1px 2px rgba(2, 17, 41, 0.1));
  height: 40px;
  display: flex;
  align-items: center;
}
.clip-nodata {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  color: var(--aix-bg-color-gray-secondary);
}
</style>
