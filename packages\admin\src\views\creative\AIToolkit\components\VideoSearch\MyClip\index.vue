<template>
  <div class="my-clips flex flex-1">
    <div class="bg-white rounded-default relative p-[20px] flex flex-col w-[200px]">
      <div class="cursor-pointer text-brand" @click="create">+ Create Directory</div>
      <TreeMenu
        v-if="folderList.length > 0"
        :tree-list="folderList"
        :default-actived="curFolder"
        @active-change="activeChange"
      />
    </div>
    <clip-list />
  </div>
  <t-dialog
    v-model:visible="createDialog"
    header="Create Folder"
    width="600"
    :confirm-on-enter="true"
    :on-cancel="() => createDialog = false"
    :on-confirm="onFolderConfirm"
  >
    <t-form>
      <t-form-item label="Folder Name" name="folderName">
        <t-input v-model="formData.folderName" placeholder="folder name" />
      </t-form-item>
    </t-form>
  </t-dialog>
</template>
<script setup lang="ts">
import { ref, reactive } from 'vue';
import { storeToRefs } from 'pinia';
import TreeMenu from 'common/components/TreeMenu';
import ClipList from './components/ClipList.vue';
import type { ITree } from 'common/components/TreeMenu';
import { MessagePlugin } from 'tdesign-vue-next';
import { useAIClipFavorStore } from '@/store/creative/toolkit/ai_clips_favor.store';

const { getFolders, createFavorFolder, changeFolder } = useAIClipFavorStore();
const { curFolder, folderList } = storeToRefs(useAIClipFavorStore());

const createDialog = ref(false);
const formData = reactive({
  folderName: '', // 文件夹名称
});

function create() {
  formData.folderName = '';
  createDialog.value = true;
}
async function onFolderConfirm() {
  const res = await createFavorFolder(formData.folderName);
  if (res.code !== 0) {
    return MessagePlugin.error(res.message);
  }
  MessagePlugin.success('create success');
  createDialog.value = false;
  getFolders();
}

function activeChange(item: ITree) {
  changeFolder(item.value as string);
}
</script>
