// 默认的线条边框宽度
export const STROKE_WIDTH = 4;
// 手柄宽度
export const HANDLE_WIDTH = 4 * STROKE_WIDTH;
// 手柄边框宽度
export const HANDLE_BORDER = 2 * STROKE_WIDTH;
// 手柄颜色
export const HANDLE_COLOR = 'blue';
// 图层左右边距
export const STAGE_PADDING_X = 8;
// 图像高度
export const IMAGE_HEIGHT = 50;
// 内容区域高度
export const FILM_TRIP_HEIGHT = IMAGE_HEIGHT + 4 * STROKE_WIDTH;
// 时间轴高度
export const MASK_LINE_HEIGHT = 40;
// 时间轴刻度线宽度
export const MASK_LINE_WIDTH = 1;

// 默认的Rect边框圆角
export const CORNER_RADIUS = 12;


export enum TimeLineStoreKey {
  'FilmTrip' = 'FilmTripGroup',
  'Selection' = 'SelectionGroup',
  'MarkLine' = 'MarkLineGroup',
  'TimeLineStage' = 'TimeLineStage',
  'TimeLine' = 'TimeLine',
  'Control' = 'ControlGroup',
}
