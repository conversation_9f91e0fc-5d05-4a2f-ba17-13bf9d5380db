import Konva from 'konva';
import { CORNER_RADIUS, STAGE_PADDING_X, STROKE_WIDTH } from './constant';
import { TimeLine, TimeLineGroup } from './types.d';

import { useKonvaStore, useTimeLineConfigStore } from './store';
import { FilmTrip } from './util/filmtrip';
import { getTimeLineScaleInterval } from './util/time';

function createImageByBase64(value: string, config: Partial<Konva.ImageConfig>): Promise<Konva.Image> {
  return new Promise((resolve, reject) => {
    const imageElement = new Image();
    imageElement.src = value;
    imageElement.onerror = reject;
    imageElement.onload = () => {
      resolve(new Konva.Image({
        ...config,
        image: imageElement,
      }));
    };
  });
}

export class FilmTripGroup extends TimeLineGroup {
  public contentRectLeftBound: number;
  public contentRectRightBound: number;

  private contentRect: Konva.Rect | undefined;
  private contentGroup: Konva.Group | undefined;
  private imageGroup!: Konva.Group | undefined;
  private konvaStore = useKonvaStore();
  private configStore = useTimeLineConfigStore();

  constructor(config: TimeLine.GroupConfig) {
    super(config);
    this.contentRectLeftBound = 4 * STROKE_WIDTH;
    this.contentRectRightBound = this.width() - 2 * 2 * STROKE_WIDTH;
    this.init();
  }

  init() {
    this.drawShape();
  }

  drawShape() {
    const outerRect = new Konva.Rect({
      x: STROKE_WIDTH,
      y: STROKE_WIDTH,
      width: this.width() - 2 * STROKE_WIDTH,
      height: this.height() - 2 * STROKE_WIDTH,
      stroke: 'gray',
      strokeWidth: STROKE_WIDTH,
      cornerRadius: CORNER_RADIUS,
    });
    const innerRect = new Konva.Rect({
      x: 3 * STROKE_WIDTH,
      y: 3 * STROKE_WIDTH,
      width: this.width() - 3 * 2 * STROKE_WIDTH,
      height: this.height() - 3 * 2 * STROKE_WIDTH,
      stroke: 'black',
      fill: 'black',
      strokeWidth: STROKE_WIDTH,
      cornerRadius: CORNER_RADIUS - STROKE_WIDTH,
    });
    this.contentRect = new Konva.Rect({
      x: this.contentRectLeftBound,
      y: 4 * STROKE_WIDTH,
      strokeWidth: STROKE_WIDTH,
      width: this.width() - 4 * 2 * STROKE_WIDTH,
      height: this.height() - 4 * 2 * STROKE_WIDTH,
    });
    this.contentGroup = new Konva.Group({
      x: 0,
      y: 0,
    });
    this.contentGroup.add(this.contentRect);
    this.imageGroup = new Konva.Group({
      x: -STAGE_PADDING_X,
      y: 0,
    });
    this.add(outerRect, innerRect, this.contentGroup, this.imageGroup);
  }

  public async addImageByVideo(video: File | string, videoRef?: HTMLVideoElement, callback?: () => void) {
    this.imageGroup?.destroyChildren();

    const filmTrip = new FilmTrip({
      height: this.contentRect!.height(),
      width: this.contentRect!.width(),
      thumbnailNum: 10,
      videoFile: video,
    });
    const { x } = this.contentRect!.getAbsolutePosition()!;
    const duration = await filmTrip.getVideoDuration();
    const scaleInterval = getTimeLineScaleInterval(this.contentRect!.width(), duration);

    this.configStore?.setConfig({
      ...this.configStore.getConfig(),
      duration,
      scaleInterval,
    });

    filmTrip.generateVideoThumbnails((thumbnail: string, position) => {
      createImageByBase64(thumbnail, {
        x: x + position,
        y: 4 * STROKE_WIDTH,
      }).then((image) => {
        this.imageGroup?.add(image);
        callback?.();
      });
    });
  }
}
