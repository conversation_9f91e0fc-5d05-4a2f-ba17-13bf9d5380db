import Konva from 'konva';
import { CORNER_RADIUS, HANDLE_BORDER, HANDLE_COLOR, HANDLE_WIDTH, STAGE_PADDING_X, STROKE_WIDTH, TimeLineStoreKey } from './constant';
import { TimeLine, TimeLineGroup } from './types.d';
import { TimeLineEvent } from './stage';
import { ToolTipLabel } from './tooltip.label';
import { useTimeLineConfigStore } from './store';

import { Vector2d } from 'konva/lib/types';
import { FilmTripGroup } from './filmtrip.group';
import { proxyDragBoundFunc } from './util/group';
import { formatVideoTime } from './util/time';

type SelectionGroupBound = {
  leftBound: number,
  rightBound: number,
};
type SelectionGroupBoundKey = keyof SelectionGroupBound;

export class SelectionGroup extends TimeLineGroup {
  public leftBound!: number;
  public rightBound!: number;
  public contentWidth: number;

  // 手柄区宽度
  private readonly handleWidth = HANDLE_WIDTH;
  // 手柄边框
  private readonly handleBorder = HANDLE_BORDER;
  // 手柄填充色
  private readonly handleColor = HANDLE_COLOR;

  private filmTripGroup!: FilmTripGroup;

  // konva实例
  private leftDropRect!: Konva.Rect;
  private rightDropRect!: Konva.Rect;
  private outerRect!: Konva.Rect;
  private innerRect!: Konva.Rect;
  private leftFillRect!: Konva.Rect;
  private rightFillRect!: Konva.Rect;
  private leftToolTip!: ToolTipLabel;
  private rightToolTip!: ToolTipLabel;


  private configStore = useTimeLineConfigStore();


  constructor(config: TimeLine.GroupConfig) {
    super(config);
    this.filmTripGroup = this.cacheMap?.getKonvaNode(TimeLineStoreKey.FilmTrip);
    this.contentWidth = this.width() - 2 * this.handleWidth;
    this.leftBound = this.x() + STAGE_PADDING_X;
    this.rightBound = this.leftBound + this.contentWidth;
    this.init();
  }

  public init() {
    this.draggable(true);
    this.initDragBoundFunc();
    this.drawShape();
    this.initListeners();
  }

  private initDragBoundFunc() {
    const { handleWidth, filmTripGroup, contentWidth } = this;
    const { contentRectLeftBound, contentRectRightBound } = filmTripGroup;
    const dragLeftBound = contentRectLeftBound - handleWidth + STAGE_PADDING_X;
    const dragRightBound = contentRectRightBound - handleWidth - contentWidth + STAGE_PADDING_X;

    this.dragBoundFunc(proxyDragBoundFunc.call(
      this,
      dragLeftBound,
      dragRightBound,
      (pos) => {
        const { leftBound } = this.updateBound('leftBound', pos.x);
        const { rightBound } = this.updateBound('rightBound', pos.x + contentWidth);
        this.updateContentWidth(leftBound, rightBound);
        this.leftToolTip.visible(true);
        const time = ((leftBound - STAGE_PADDING_X) / this.configStore.getConfig().scaleInterval!).toFixed(1);
        this.leftToolTip?.getText().text(formatVideoTime(Number(time)));
        this.fire('rangeChange');
      },
    ));
  }

  private drawShape() {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const THIS = this;
    this.outerRect = new Konva.Rect({
      x: 1 * STROKE_WIDTH,
      y: 1 * STROKE_WIDTH,
      width: 2 * 3 * STROKE_WIDTH + this.contentWidth,
      height: this.height() - 2 * STROKE_WIDTH,
      stroke: this.handleColor,
      strokeWidth: STROKE_WIDTH,
      cornerRadius: CORNER_RADIUS,
    });
    this.innerRect = new Konva.Rect({
      x: 1.5 * STROKE_WIDTH,
      y: 1.5 * STROKE_WIDTH,
      width: 2 * 2.5 * STROKE_WIDTH + this.contentWidth,
      height: this.height() - 3 * STROKE_WIDTH,
      stroke: this.handleColor,
      strokeWidth: 2 * STROKE_WIDTH,
      cornerRadius: CORNER_RADIUS,
      fill: 'rgba(255, 255, 255, 0.2)',
    });
    this.leftFillRect = new Konva.Rect({
      x: 1.5 * STROKE_WIDTH,
      y: 2 * STROKE_WIDTH,
      width: 2.5 * STROKE_WIDTH,
      height: this.height() - 2 * this.handleBorder,
      fill: this.handleColor,
    });
    this.rightFillRect = new Konva.Rect({
      x: 2 * 2 * STROKE_WIDTH + this.contentWidth,
      y: 2 * STROKE_WIDTH,
      width: 2.5 * STROKE_WIDTH,
      height: this.height() - 2 * this.handleBorder,
      fill: this.handleColor,
    });
    this.leftDropRect = new Konva.Rect({
      x: 0,
      y: 0,
      width: this.handleWidth,
      height: this.height(),
      draggable: true,
      dragBoundFunc(pos) {
        return proxyDragBoundFunc.call(
          THIS,
          THIS.filmTripGroup.contentRectLeftBound - THIS.handleWidth + STAGE_PADDING_X,
          THIS.rightBound,
          (pos: Vector2d) => {
            const { rightBound, leftBound } = THIS.updateBound('leftBound', pos.x);
            THIS.updateContentWidth(pos.x, rightBound);
            THIS.x(pos.x - STAGE_PADDING_X);
            THIS.leftToolTip.visible(true);
            const time = ((leftBound - STAGE_PADDING_X) / THIS.configStore.getConfig().scaleInterval!).toFixed(1);
            THIS.leftToolTip?.setTooltipText(formatVideoTime(Number(time)));
            THIS.fire('rangeChange');
          },
        )(pos);
      },
    });

    this.rightDropRect = new Konva.Rect({
      x: 2 * 2 * STROKE_WIDTH + this.contentWidth,
      y: 0,
      width: this.handleWidth,
      height: this.height(),
      draggable: true,
      dragBoundFunc(pos) {
        return proxyDragBoundFunc.call(
          THIS,
          THIS.leftBound + THIS.handleWidth,
          THIS.filmTripGroup.contentRectRightBound + STAGE_PADDING_X,
          (pos: Vector2d) => {
            const { leftBound, rightBound } = THIS.updateBound('rightBound', pos.x - THIS.handleWidth);
            THIS.rightToolTip.visible(true);
            THIS.rightToolTip?.x(THIS.handleWidth + THIS.contentWidth);

            const time = ((rightBound - STAGE_PADDING_X) / THIS.configStore.getConfig().scaleInterval!).toFixed(1);
            THIS.rightToolTip?.setTooltipText(formatVideoTime(Number(time)));
            THIS.updateContentWidth(leftBound + THIS.handleWidth, pos.x);
            THIS.fire('rangeChange');
          },
        )(pos);
      },
    });

    this.leftToolTip = new ToolTipLabel({
      x: this.handleWidth,
      y: 0,
      width: 100,
      height: 20,
      visible: false,
    });

    this.rightToolTip = new ToolTipLabel({
      x: this.handleWidth + this.contentWidth,
      y: 0,
      width: 100,
      height: 20,
      visible: false,
    });


    this.add(
      this.innerRect, this.outerRect, this.leftFillRect, this.rightFillRect,
      this.leftDropRect, this.rightDropRect,
    );
    this.add(this.leftToolTip, this.rightToolTip);
  }


  private initListeners() {
    this.outerRect.on('mouseover', () => {
      document.body.style.cursor = 'pointer';
    });
    this.outerRect.on('mouseout', () => {
      document.body.style.cursor = 'default';
    });
    this.leftDropRect.on('mouseover', () => {
      document.body.style.cursor = 'col-resize';
    });
    this.leftDropRect.on('mouseout', () => {
      document.body.style.cursor = 'default';
    });
    this.rightDropRect.on('mouseover', () => {
      document.body.style.cursor = 'col-resize';
    });
    this.rightDropRect.on('mouseout', () => {
      document.body.style.cursor = 'default';
    });
    this.on('dragend', () => {
      this.leftToolTip.visible(false);
      this.rightToolTip.visible(false);
    });

    // 手柄宽度变化触发事件
    this.on('rangeChange', () => {
      const { contentWidth, leftBound, rightBound } = this;
      this.outerRect.width(2 * 3 * STROKE_WIDTH + contentWidth);
      this.innerRect.width(2 * 2.5 * STROKE_WIDTH + contentWidth);
      this.rightFillRect.x(2 * 2 * STROKE_WIDTH + contentWidth);
      this.rightDropRect.x(2 * 2 * STROKE_WIDTH + contentWidth);
      this.initDragBoundFunc();
      this.cacheMap?.getKonvaNode(TimeLineStoreKey.TimeLineStage).fire(TimeLineEvent.RANGE_CHANGE, {
        leftBound,
        rightBound,
      });
    });
  }


  private updateBound(key: SelectionGroupBoundKey, value: number): SelectionGroupBound {
    switch (key) {
      case 'leftBound':
        this.leftBound = value;
        break;
      case 'rightBound':
        this.rightBound = value;
        break;
      default:
        break;
    }
    return {
      leftBound: this.leftBound,
      rightBound: this.rightBound,
    };
  }

  private updateContentWidth(leftBound: number, rightBound: number) {
    this.contentWidth = rightBound - leftBound;
    this.width(this.contentWidth);
  }
}
