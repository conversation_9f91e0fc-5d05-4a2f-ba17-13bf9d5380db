import Konva from 'konva';
import { HANDLE_WIDTH, STAGE_PADDING_X, TimeLineStoreKey } from './constant';
import { ControlGroup } from './control.group';
import { BaseStage } from './util/baseStage';

import { FilmTripGroup } from './filmtrip.group';
import { MarkLineGroup } from './markline.group';
import { SelectionGroup } from './selection.group';
import { TimeLineConfig, useKonvaStore, useTimeLineConfigStore } from './store';

export enum TimeLineEvent {
  'RANGE_CHANGE' = 'range-change', // 选区发生变化，无论是位置变化，还是选中区域变化
  'CONTROL_CHANGE' = 'control-change', // 控制区发生变化
  'LOAD_VIDEO_ERROR' = 'load-video-error', // 视频加载失败
}

export class TimeLine extends BaseStage {
  private stage!: Konva.Stage;
  private staticLayer!: Konva.Layer;
  private dynamicLayer!: Konva.Layer;
  private filmTripGroup!: FilmTripGroup;

  private controlGroup?: ControlGroup;
  private selectionGroup?: SelectionGroup;
  private markLineGroup?: MarkLineGroup;
  private store = useKonvaStore();
  private configStore = useTimeLineConfigStore()!;

  constructor(config: TimeLineConfig) {
    super(config);
    if (this.container) {
      this.configStore?.setConfig(config);
      this.init();
    }
  }

  init() {
    const { width, height } = this.container!.getBoundingClientRect();
    console.log('width: ', width, 'height: ', height);
    const markLineHeight = 40;
    const controlHeight = 40;

    this.staticLayer = new Konva.Layer();
    this.dynamicLayer = new Konva.Layer();

    this.stage = new Konva.Stage({
      container: this.container!,
      x: STAGE_PADDING_X,
      width: width - 2 * STAGE_PADDING_X,
      height,
    });

    this.filmTripGroup = new FilmTripGroup({
      y: markLineHeight + controlHeight,
      width: width - 4 * STAGE_PADDING_X,
      height: height - (markLineHeight + controlHeight),
      name: TimeLineStoreKey.FilmTrip,
    });
    this.initListeners();
    this.store?.setKonvaNode(TimeLineStoreKey.TimeLineStage, this.stage);
    this.store?.setKonvaNode(TimeLineStoreKey.TimeLine, this);
    this.staticLayer.add(this.filmTripGroup);
    this.stage.add(this.staticLayer, this.dynamicLayer);
  }

  initListeners() {
    this.stage.on(TimeLineEvent.RANGE_CHANGE, (evt) => {
      const scaleInterval = this.configStore.getConfig().scaleInterval!;
      const { leftBound, rightBound } = evt as unknown as { leftBound: number; rightBound: number; width: number };
      this.emit(TimeLineEvent.RANGE_CHANGE, {
        startTime: ((leftBound - STAGE_PADDING_X) / scaleInterval).toFixed(1),
        endTime: ((rightBound - STAGE_PADDING_X) / scaleInterval).toFixed(1),
      });
    });
    this.stage.on(TimeLineEvent.CONTROL_CHANGE, () => {
      const controlGroup = this.store.getKonvaNode(TimeLineStoreKey.Control);
      this.emit(TimeLineEvent.CONTROL_CHANGE, {
        playTime: (controlGroup.x() / this.configStore.getConfig().scaleInterval!).toFixed(1),
      });
    });
  }

  public async setVideoFile(video: File | string, videoRef?: HTMLVideoElement) {
    if (this.stage && this.staticLayer) {
      this.clearLayers();
      await this.filmTripGroup?.addImageByVideo(video, videoRef, () => {
        this.staticLayer?.draw();
      });
    }
  }

  public setControlPosition(time: number) {
    console.log('current control position: ', time);
    this.controlGroup?.x(time * this.configStore.getConfig().scaleInterval!);
  }

  public drawShapes() {
    const { width, height } = this.container!.getBoundingClientRect();
    const markLineHeight = 40;
    const controlHeight = 40;

    this.markLineGroup = new MarkLineGroup({
      x: this.filmTripGroup.contentRectLeftBound,
      y: controlHeight + 10,
      width: this.filmTripGroup.contentRectRightBound - this.filmTripGroup.contentRectLeftBound,
      visible: true,
    });

    const { startTime = 0, endTime = 0 } = this.configStore.getConfig()!;
    const scaleInterval = this.configStore.getConfig().scaleInterval!;
    const selectionGroupWith = (endTime - startTime) * scaleInterval + 2 * HANDLE_WIDTH;
    this.selectionGroup = new SelectionGroup({
      x: startTime * scaleInterval,
      y: markLineHeight + controlHeight,
      width: selectionGroupWith,
      height: height - (markLineHeight + controlHeight),
      name: TimeLineStoreKey.Selection,
    });

    this.controlGroup = new ControlGroup({
      y: controlHeight,
      width,
      height: height - markLineHeight,
      fill: 'red',
      name: TimeLineStoreKey.Control,
    });

    this.staticLayer.add(this.markLineGroup);
    this.dynamicLayer.add(this.selectionGroup, this.controlGroup);
  }
  public clearLayers() {
    this.dynamicLayer.destroyChildren();
    this.markLineGroup?.destroyChildren();
  }
  public setSelectionRange(startTime: number, endTime: number) {
    this.configStore.setConfig({
      ...this.configStore.getConfig(),
      startTime,
      endTime,
    });
  }
}
