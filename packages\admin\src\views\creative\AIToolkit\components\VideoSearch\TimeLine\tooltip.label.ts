import Konva from 'konva';
import { LabelConfig } from 'konva/lib/shapes/Label';


export class ToolTipLabel extends Konva.Label {
  constructor(config: LabelConfig) {
    super(config);
    this.init();
  }

  init() {
    this.drawShape();
  }

  drawShape() {
    const tooltipText = new Konva.Text({
      id: 'tooltip-text',
      fontSize: 10,
      padding: 6,
      text: '00:00:00',
      fill: 'white',
    });

    const tooltipArrow = new Konva.Tag({
      fill: 'gray',
      pointerDirection: 'down',
      pointerWidth: 8,
      pointerHeight: 4,
      cornerRadius: 10,
      lineJoin: 'round',
    });
    this.add(tooltipArrow, tooltipText);
  }

  setTooltipText(text: string) {
    // this.findOne('#tooltip-text');
    this.getText().text(text);
  }
}
