import Konva from 'konva';
import { useKonvaStore } from './store';

export class TimeLineGroup extends Konva.Group {
  public cacheMap = useKonvaStore();

  // 如果不加name，那么就不会被缓存
  constructor(config: TimeLine.GroupConfig) {
    const { name, ...konvaConfig } = config;
    super(konvaConfig);
    name && this.cacheMap.setKonvaNode(name, this);
  }
}


declare namespace TimeLine {
  export type GroupConfig = Konva.GroupConfig & { name?: string };
}
