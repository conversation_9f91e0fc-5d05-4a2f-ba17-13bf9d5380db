
/**
 * 00:00.00
 * 根据秒数生成规范的时间,最大支持到分钟级别
 * example：3406.87 : 56:46.86
 * @param time
 * @returns string 规范化的时间格式
 */
export const formatVideoTime = (time: number): string => {
  if (time > 3600) {
    throw new Error('Videos longer than 1 hour are currently not supported');
  }
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  const milliseconds = Number(Number(`${time}`.replace(/\d+(?=.)/, '0')).toFixed(1)) * 10;
  return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}.${milliseconds}`;
};


/**
 * 生成一个时间轴刻度间隔
 * @param markLineLength    时间轴的长度
 * @param videoTime         视频时长
 * @return                  时间轴刻度间隔
 */
export const getTimeLineScaleInterval = (markLineLength: number, videoTime: number) => {
  const formatTime = Number(videoTime.toFixed(3));
  return Number((markLineLength / formatTime).toFixed(2));
};


