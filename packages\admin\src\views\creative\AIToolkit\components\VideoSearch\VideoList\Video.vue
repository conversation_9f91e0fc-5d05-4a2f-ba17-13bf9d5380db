<template>
  <video
    ref="videoPlayerRef"
    class="w-full h-full toolkit-video aspect-auto flex justify-center"
  />
</template>

<script lang="ts" setup>
import { replaceAiUrl } from '@/util/creative/replaceUrl';
import { Video } from 'common/service/creative/aigc_toolkit/type';
import videojs, { VideoJsPlayerOptions } from 'video.js';
import 'video.js/dist/video-js.css';
import { shallowRef, onMounted, shallowReactive } from 'vue';

interface IProps {
  video: Video;
}
type VideoPlayer = ReturnType<typeof videojs>;
const props = defineProps<IProps>();
const videoPlayer = shallowRef<VideoPlayer>();
const videoPlayerRef = shallowRef<HTMLVideoElement>();
const videoPlayerOptions = shallowReactive<VideoJsPlayerOptions>({
  autoplay: false,
  controls: false,
  loop: false,
  muted: true,
  sources: [
    {
      src: replaceAiUrl(props.video.preview_url || props.video.video_url), // 预览优先使用preview_url
      type: 'video/mp4',
    },
  ],
  preload: 'none',
});

onMounted(() => {
  videoPlayer.value = videojs(videoPlayerRef.value!, videoPlayerOptions);

  videoPlayer.value!.on('loadedmetadata', () => {
    videoPlayer.value?.currentTime(props.video.start_time);
    console.log('duration:', videoPlayer.value?.duration());
    console.log(videoPlayer.value);
  });

  videoPlayer.value!.on('timeupdate', () => {
    const currentTime = videoPlayer.value?.currentTime();

    if (currentTime && currentTime >= props.video.end_time) {
      videoPlayer.value?.pause();
      videoPlayer.value?.currentTime(props.video.start_time);
      videoPlayer.value?.play()?.catch(() => {});
    }
  });
});

defineExpose({
  player: videoPlayer,
  play: () => {
    videoPlayer.value?.play()?.catch(() => {});
  },
});
</script>
<style scoped lang="scss">
video {
  @apply bg-transparent absolute z-10 aspect-auto h-full;
}
</style>
