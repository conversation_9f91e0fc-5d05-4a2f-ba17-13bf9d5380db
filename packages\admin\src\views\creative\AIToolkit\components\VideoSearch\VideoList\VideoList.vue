<template>
  <div
    v-infinite-scroll="loadMore"
    class="flex flex-col overflow-y-scroll h-full"
    style="contain: strict"
  >
    <div
      :style="videoListStyle"
      class="grid gap-[16px] grow justify-between"
    >
      <video-list-item
        v-for="(video, index) in props.videoList"
        :key="video.video_name + index"
        :video="video"
        :is-favor="isFavor(video)"
        :active="checkItemSelected(video)"
        :hide-menu="hideMenu"
        @click="() => onClick(video)"
        @favor="onFavor"
      />
    </div>
    <div
      v-show="props.canLoadMore"
      class="flex justify-center items-center w-full leading-[20px] z-10 bg-white pt-[16px]"
    >
      <t-loading
        text="Loading more..."
        size="small"
      />
    </div>
  </div>
  <video-favor ref="videoFavorRef" :video-clip="clipVideo" @success="favorSuccess" />
</template>

<script setup lang="ts">
import { Video, ClipsVideo } from 'common/service/creative/aigc_toolkit/type';
import VideoListItem from './VideoListItem.vue';
import { storeToRefs } from 'pinia';
import { useAIClipStore } from '@/store/creative/toolkit/ai_clips.store';
import { useAIClipFavorStore } from '@/store/creative/toolkit/ai_clips_favor.store';
import { useLoading } from 'common/compose/loading';
import { useDebounceFn } from '@vueuse/core';
import { ref, provide, inject } from 'vue';
import { vInfiniteScroll } from '@vueuse/components';
import videojs from 'video.js';
import VideoFavor from '@/views/creative/AIToolkit/components/VideoFavor.vue';

interface IProps {
  videoList: Video[];
  canLoadMore: boolean;
  hideMenu: boolean;
}

const emit = defineEmits(['loadMore']);

const { getClips, getFolders } = useAIClipFavorStore();
const { allFavorClips } = storeToRefs(useAIClipFavorStore());

const props = defineProps<IProps>();
const aiClipStore  = useAIClipStore();
const { setCurrentClipsVideo } = aiClipStore;

const checkItemSelected = (video: Video) => video.video_url === aiClipStore.currentClipsVideo?.video_url
  && video.start_time === aiClipStore.currentClipsVideo?.start_time
  && video.end_time === aiClipStore.currentClipsVideo?.end_time;

const videoListStyle = ref({
  gridTemplateColumns: `repeat(auto-fit, ${window.screen.width > 2000 ? 250 : 220}px)`,
});

const curHoverPlayVideo = ref<videojs.Player | undefined>(undefined);

const { showLoading, hideLoading } = useLoading(false);
const showRight = inject('show') as Function;
const onClick = (video: Video) => {
  const clipsVideo = {
    ...video,
    origin_video_name: video.video_name,
    video_name: '',
  };
  showRight();
  UpdateCurrentClipsVideoDebounce(clipsVideo);
};

const UpdateCurrentClipsVideoDebounce = useDebounceFn((clipsVideo: ClipsVideo) => {
  setCurrentClipsVideo(clipsVideo);
}, 1000);

const setCurHoverPlayVideo = (video?: videojs.Player) => {
  curHoverPlayVideo.value = video;
};

// 视频片段收藏
const videoFavorRef = ref();
const clipVideo = ref<Video | undefined>();
function onFavor(video: Video) {
  clipVideo.value = video;
  videoFavorRef.value.show();
}
const favorSuccess = () => {
  getClips();
  getFolders();
};

function isFavor(video: Video) {
  return !!allFavorClips.value.find(item => item.clip_name === video.clip_name);
}

// 加载更多（放在外层组件不生效）
const loadMore = () => {
  emit('loadMore');
};

provide('curHoverPlayVideo', curHoverPlayVideo);
provide('setCurHoverPlayVideo', setCurHoverPlayVideo);
defineExpose({
  showLoading,
  hideLoading,
});
</script>

