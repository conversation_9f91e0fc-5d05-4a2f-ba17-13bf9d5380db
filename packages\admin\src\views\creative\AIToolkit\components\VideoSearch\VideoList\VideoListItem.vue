<template>
  <div class="clip-video-item relative">
    <div
      class="clip-video border-[1px] border-[#D8D7DA] aspect-video border-solid rounded-default
    cursor-pointer relative overflow-hidden"
      :class="[active ? 'border-brand border-[8px] box-border': 'box-content']"
    >
      <div
        class="absolute z-10 flex justify-center items-center bg-[#1E222A] bg-opacity-60
      font-bold w-[50px] h-[20px] left-[4px] top-[4px] text-[12px] text-white opacity-60"
      >
        {{ duration }}
      </div>
      <div
        class="absolute z-[20] flex justify-center items-center bg-[#1E222A] bg-opacity-60 right-[4px]
       bottom-[4px] p-[2px]"
      >
        <img
          :src="`${CDN}/ai_toolkit/images/star${isFavor ? '2' : '1'}.png`"
          class="cursor-pointer w-[18px] h-[18px]" alt=""
          @click="triggerFavor"
        >
      </div>
      <t-dropdown
        v-if="!hideMenu"
        :options="menuOptions" :min-column-width="200"
        @click="onMenuAction"
      >
        <div
          class="absolute z-[20] w-[20px] h-[20px] right-[6px] top-[6px]
       flex justify-center items-center bg-[#1E222A] bg-opacity-60"
        >
          <MoreIcon class="absolute" size="16" color="#eee" />
        </div>
      </t-dropdown>
      <div
        class="flex h-full w-full"
        @mouseenter="onMouseEnter"
        @mouseleave="onMouseLEave"
      >
        <Image
          :url="props.video.cover_url"
          class="h-full w-full"
        />
        <transition name="fade">
          <div
            v-show="visible"
            class="w-full h-full absolute z-10 bg-black"
          >
            <VideoPlay
              ref="videoPlayer"
              :video="props.video"
            />
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>
<script lang="tsx" setup>
import { Video } from 'common/service/creative/aigc_toolkit/type';
import { computed, ref, onUnmounted, inject, Ref } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import Image from '../Image/index.vue';
import { MoreIcon } from 'tdesign-icons-vue-next';
import VideoPlay from './Video.vue';
import videojs from 'video.js';
import { useVisible } from 'common/compose/useVisible';
import dayjs from 'dayjs';
import { FILE_CDN_COM as CDN } from 'common/config';
import { useAIClipFavorStore } from '@/store/creative/toolkit/ai_clips_favor.store';
import { useGoto } from '@/router/goto';
import { useGlobalGameStore } from '@/store/global/game.store';
import { MENU_OPTIONS as menuOptions } from './utils';

interface IProps {
  video: Video;
  active: Boolean;
  isFavor: Boolean;
  hideMenu: Boolean;
}

const props = defineProps<IProps>();
const emit = defineEmits(['favor']);
const { goToAixLibrary } = useGoto();
const { getClips, delFavorClip } = useAIClipFavorStore();

const curHoverPlayVideo = inject<Ref<videojs.Player | undefined>>('curHoverPlayVideo');
const setCurHoverPlayVideo = inject<(video?: videojs.Player) => void>('setCurHoverPlayVideo');
const searchInNewTab = inject<(video: Video) => void>('searchInNewTab');

const duration = computed(() => formatVideoTime(props.video.end_time - props.video.start_time));
const { visible, show: showVideo, hide: hideVideo } = useVisible(false);
const timer = ref<NodeJS.Timeout>();
const videoPlayer = ref();
const formatVideoTime = (time: number): string => {
  if (time > 3600) {
    throw new Error('Videos longer than 1 hour are currently not supported');
  }
  return String((dayjs as any).duration(Math.round(time * 1000))
    .format('MM:ss.SSS'))
    .slice(0, -1);
};

const onMouseEnter = () => {
  curHoverPlayVideo?.value?.pause();
  timer.value = setTimeout(() => {
    showVideo();
    setCurHoverPlayVideo?.(videoPlayer.value?.player);
    videoPlayer.value?.play();
  }, 500);
};

const onMouseLEave = () => {
  timer.value && clearTimeout(timer.value);
  videoPlayer.value?.player?.pause();
  setCurHoverPlayVideo?.();
  hideVideo();
};

// 收藏、取消收藏
const triggerFavor = async () => {
  if (!props.isFavor) {
    emit('favor', props.video);
  } else {
    await delFavorClip(props.video.clip_name);
    MessagePlugin.success('Cancel success');
    getClips();
  }
};

const onMenuAction = (data: { value: number }) => {
  if (data.value === 1) {
    const { gameCode } = useGlobalGameStore();
    goToAixLibrary({
      node: props.video.node_id || undefined,
      game: gameCode,
    });
  }
  // 打开新页面
  if (data.value === 2) {
    searchInNewTab?.(props.video);
  }
};

// const searchClip = () => {
//   clipBus.emit(JSON.stringify(props.video));
// };

onUnmounted(() => {
  timer.value && clearTimeout(timer.value);
});
</script>

<style lang="scss">
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
