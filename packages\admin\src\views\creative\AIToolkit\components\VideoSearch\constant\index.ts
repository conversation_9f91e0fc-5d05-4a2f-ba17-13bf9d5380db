export enum EAlToolkit {
  'Text to Al Clips' = 'Text to Al Clips',
  'Al Templates' = 'Al Templates',
  'Text to Al Video' = 'Text to Al Video',
}

export type TAlToolkit = {
  name: EAlToolkit;
  path: string;
  image: string;
};


const IMAGE_BASE_URL = 'https://static.aix.intlgame.com/gameicon/miniclip/';
export const GAME_OPTIONS: Array<{
  image: string;
  label: string;
  value: string;
}> = [
  {
    image: `${IMAGE_BASE_URL}warframe.png`,
    label: 'Warframe',
    value: 'warframe',
  },
  {
    image: `${IMAGE_BASE_URL}baseball_clash.png`,
    label: 'Baseball Clash',
    value: 'baseball_clash',
  },
  {
    image: `${IMAGE_BASE_URL}8_ball_pool.png`,
    label: '8 ball pool',
    value: '8_ball_pool',
  },
  {
    image: `${IMAGE_BASE_URL}ultimate_golf.png`,
    label: 'Ultimate Golf',
    value: 'ultimate_golf',
  },
  {
    image: `${IMAGE_BASE_URL}pure_sniper.png`,
    label: 'pure sniper',
    value: 'pure_sniper',
  },
  {
    image: `${IMAGE_BASE_URL}carrom_pool.png`,
    label: 'Carrom Pool',
    value: 'carrom_pool',
  },
  {
    image: `${IMAGE_BASE_URL}basketball_stars.png`,
    label: 'basketball stars',
    value: 'basketball_stars',
  },
  {
    image: `${IMAGE_BASE_URL}agar_io.png`,
    label: 'agar.io',
    value: 'agar_io',
  },
  {
    image: `${IMAGE_BASE_URL}subway_surfers.png`,
    label: 'subway surfers',
    value: 'subway_surfers',
  },
  {
    image: `${IMAGE_BASE_URL}football_strike.png`,
    label: 'Football Strike - Multiplayer Socce',
    value: 'football_strike',
  },
  {
    image: `${IMAGE_BASE_URL}football_battle.png`,
    label: 'Football Battle',
    value: 'football_battle',
  },
  {
    image: `${IMAGE_BASE_URL}bubble_shooter.png`,
    label: 'bubble shooter',
    value: 'bubble_shooter',
  },
  {
    image: `${IMAGE_BASE_URL}bubble_shooter.png`,
    label: 'conan_exiles_gameplay',
    value: 'conan_exiles_gameplay',
  },
  {
    image: `${IMAGE_BASE_URL}bubble_shooter.png`,
    label: 'conan_exiles_trailer',
    value: 'conan_exiles_trailer',
  },
];

export const SECTION_OPTIONS = [
  { label: 'All', value: 'All' },
  { label: 'Opening', value: 'Opening' },
  { label: 'Ending', value: 'Ending' },
];

export const TYPE_OPTIONS = [
  { label: 'All', value: 'All' },
  { label: 'UE', value: 'UE' },
  { label: 'Gameplay', value: 'Gameplay' },
  { label: 'Human', value: 'Human' },
];

export const SORT_OPTIONS = [
  { label: 'Default', value: '' },
  { label: 'Performance', value: 'Performance' },
  { label: 'Date Created', value: 'Date Created' },
];

export const CLIPS_LANGUAGE_OPTIONS = [
  { label: 'EN', value: 'EN' },
  { label: 'AR', value: 'AR' },
  { label: 'RU', value: 'RU' },
  { label: 'PTBR', value: 'PTBR' },
  { label: 'TR', value: 'TR' },
  { label: 'ES', value: 'ES' },
  { label: 'JP', value: 'JP' },
  { label: 'KR', value: 'KR' },
  { label: 'MS', value: 'MS' },
  { label: 'ID', value: 'ID' },
];
