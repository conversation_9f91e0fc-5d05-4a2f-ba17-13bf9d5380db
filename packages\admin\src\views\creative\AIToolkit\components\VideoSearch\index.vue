<template>
  <t-tabs v-model="curTab" @change="tabChange">
    <t-tab-panel value="search" label="Search Clips" />
    <t-tab-panel value="clips" label="My Clips" />
  </t-tabs>
  <div
    class="rounded-lg w-full flex flex-col flex-1 relative overflow-hidden"
    :class="from === 'dialog' ? 'h-[700px]' : ''"
  >
    <div v-if="curTab === 'search'" class="w-full h-full flex flex-row">
      <slide-page>
        <template #left>
          <div
            class="bg-white rounded-default relative flex-1 p-[20px] flex flex-col"
            :class="from === 'dialog' ? 'p-[0px] pt-[8px]' : ''"
          >
            <left-section />
          </div>
        </template>
        <template #right>
          <video-clips :clip-video="currentClipsVideo" />
          <div v-if="from === 'dialog'" class="h-[150px] mt-[20px]">
            <Footer :cur-tab="curTab" />
          </div>
          <div v-else class="w-full flex flex-1 overflow-hidden p-[20px] bg-white mt-[16px] rounded-default">
            <Footer :cur-tab="curTab" />
          </div>
        </template>
      </slide-page>
    </div>
    <div v-if="curTab === 'clips'" class="w-full h-full flex flex-row">
      <my-clip />
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref, inject } from 'vue';
import SlidePage from '../SlidePage.vue';
import MyClip from './MyClip/index.vue';
import LeftSection from './LeftSection/index.vue';
import VideoClips from '@/views/creative/AIToolkit/components/VideoEditor/index.vue';
import Footer from './Footer/index.vue';
import { useAIClipFavorStore } from '@/store/creative/toolkit/ai_clips_favor.store';
import { storeToRefs } from 'pinia';
import { useAIClipStore } from '@/store/creative/toolkit/ai_clips.store';
import { useAiSVEditorStore } from '@/store/creative/toolkit/ai_smart_video_editor.store';

const from = inject('from', 'page') as string;
const { getFolders, getClips } = useAIClipFavorStore();
const { currentClipsVideo } = storeToRefs(useAIClipStore());
const { setClipVideo, replaceInfo } = useAiSVEditorStore();
const { setClipsVideos } = useAIClipStore();

const tab = location.hash.replace('#', '') || 'search';
const curTab = ref(tab);

function tabChange(val: string) {
  location.hash = val;
  setClipVideo('', ''); // 切换tab，清空选中的视频
}

onMounted(() => {
  getFolders(); // 获取用户创建的文件夹列表
  getClips(); // 获取用户收藏的所有视频

  // 替换弹框，需要延迟还在选中视频
  if (from === 'dialog') {
    const { scene } = replaceInfo;
    if (scene) {
      const media = scene!.media_urls[replaceInfo.videoIndex];
      const clipVideo = {
        video_url: media.url,
        start_time: media.start_time,
        end_time: media.end_time,
      };
      setTimeout(() => {
        currentClipsVideo.value = clipVideo;
        setClipsVideos([clipVideo]);
      }, 500);
    }
  }
});
</script>
