<template>
  <div class="flex flex-wrap justify-between min-h-[100px] pr-[6px]">
    <div
      v-for="(item, index) in taskList"
      :key="index"
      :class="'flex flex-col w-[244px] h-[168px] border-[#D8D7DA] mb-[16px] rounded-[8px] p-[12px] ' +
        (activeId === item.task_id ? ' border-brand border-[2px]' : 'border-[1px]')"
    >
      <div class="top flex justify-between items-center">
        <div>
          <span class="font-bold">Task {{ taskList.length - index }}</span>
          <span class="text-gray-primary ml-[4px]">({{ item.create_time }})</span>
        </div>
        <template v-if="item.status === 2">
          <DownloadIcon class="cursor-pointer" size="16" @click="download(item)" />
        </template>
      </div>
      <div class="mt-[12px] flex-1 h-[100px] cursor-pointer" @click="selectTask(item)">
        <div v-if="item.status !== 2" class="bg-[#F0F1F6] rounded-[4px] w-[100%] h-[100%] flex-center">
          <template v-if="item.status === 3">
            <ErrorTriangleIcon color="var(--aix-bg-color-error-primary)" size="16px" class="mr-[6px]" />
            <span class="text-error-primary">Failed</span>
          </template>
          <template v-if="item.status === 1">
            <t-loading size="16px" class="mr-[6px]" />
            <span class="text-brand">Processing...</span>
          </template>
          <template v-if="item.status === 0">
            <t-loading size="16px" class="mr-[6px]" />
            <span class="text-brand">Waiting...</span>
          </template>
        </div>
        <div v-else class="bg-[black] rounded-[4px] w-[100%] h-[100%] flex-center">
          <video
            :src="isCdnUrl(item) ? item.target_video : `${CDN}/${item.target_video}`"
            class="object-contain w-[100%] h-[100%]"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts" generic="Task extends BaseTask">
import { FILE_CDN_COM as CDN } from 'common/config';
import { DownloadIcon, ErrorTriangleIcon } from 'tdesign-icons-vue-next';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { BaseTask } from 'common/service/creative/aigc_toolkit/type';
import { downloadVideo } from 'common/service/creative/aigc_toolkit/face_swap';

const emit = defineEmits(['download']);
const props = withDefaults(defineProps<{
  taskList: Task[],
  activeId?: string | number,
  selectTask: Function,
  customDownload?: boolean,
}>(), {
  taskList: () => [],
  activeId: '',
  selectTask: () => {},
  customDownload: false,
});

const isCdnUrl = (item: Task) => item.target_video.includes('static.aix');

const download = (item: Task) => {
  if (props.customDownload) {
    emit('download', item);
  } else {
    const url = isCdnUrl(item) ? item.target_video : `${CDN}/${item.target_video}`;
    downloadVideo(url);
  }
};
</script>
