<template>
  <video
    v-if="video?.url"
    :src="video.url"
    :class="`${videoWidth} ${videoHeight}`"
    controls
    controlsList="nodownload noplaybackrate"
    disablePictureInPicture
    @loadedmetadata="onLoadedMetadata"
    @timeupdate="onTimeUpdate"
  />
</template>
<script lang="ts" setup>
import { computed, ref } from 'vue';

type Video = {
  url: string
};

const props = withDefaults(
  defineProps<{
    video: Video | undefined,
    width?: string | number | undefined,
    height?: string | number | undefined,
    startTime?: number,
    endTime?: number,
  }>(),
  {
    width: undefined,
    height: undefined,
    startTime: 0,
    endTime: 0,
  },
);

const videoWidth = computed(() => {
  if (props.width) {
    const num = Number(props.width);
    if (Number.isNaN(num)) return props.width;
    return `w-[${props.width}px]`;
  }
  return 'w-[100%]';
});
const videoHeight = computed(() => {
  if (props.height) {
    const num = Number(props.height);
    if (Number.isNaN(num)) return props.height;
    return `h-[${props.height}px]`;
  }
  return 'w-[100%]';
});

// 控制开始、结束时间
const onLoadedMetadata = (event: Event) => {
  const videoElement = event.target as HTMLVideoElement;
  if (props.startTime) {
    videoElement.currentTime = props.startTime;
  }
};

const hasPaused = ref(false);
const onTimeUpdate = (event: Event) => {
  const videoElement = event.target as HTMLVideoElement;
  if (props.endTime && videoElement.currentTime >= props.endTime && !hasPaused.value) {
    videoElement.pause();
    hasPaused.value = true;
  }
};
</script>
