import { ITemplateList, IVideoList, Scene } from 'common/service/creative/aigc_toolkit/smart_video.type';

type SceneKey = 'script_scene_opening' | 'script_scene_body' | 'script_scene_ending';


export function removeClipUrl(target: ITemplateList | IVideoList) {
  ['script_scene_opening', 'script_scene_body', 'script_scene_ending'].forEach((key) => {
    (target[key as SceneKey]).forEach((scene: Scene) => {
      scene.media_urls.forEach((item) => {
        // eslint-disable-next-line no-param-reassign
        item.url = '';
      });
    });
  });
}

export const dealUrl = (url: string) => {
  const names = url.split('/');
  const name = encodeURIComponent(decodeURIComponent(names.pop() as string));
  names.push(name);
  return names.join('/');
};
