<template>
  <div class="bubble-chart flex flex-col flex-[1] w-full">
    <div class="flex flex-warp justify-between px-[16px] items-center">
      <div />
      <div class="flex items-center gap-x-[8px] mb-[6px]">
        <NewSelect
          :options="metricListX"
          :model-value="metricX"
          class="w-[150px]"
          @change="onMetricChangeX"
        >
          <template #label>
            <span class="text-gray-primary">X</span>
          </template>
        </NewSelect>
        <NewSelect
          :options="metricListY"
          :model-value="metricY"
          class="w-[150px]"
          @change="onMetricChangeY"
        >
          <template #label>
            <span class="text-gray-primary">Y</span>
          </template>
        </NewSelect>
      </div>
    </div>
    <div
      v-if="isLoading"
      v-loading="isLoading"
      class="w-full h-full flex justify-center items-center"
    />
    <div v-else ref="chartRef" class="flex-[1] h-[1px] w-[full]">
      <VChart
        autoresize
        :option="option"
        :style="{
          width: `${chartWidth}px`,
          height: `${chartHeight}px`
        }"
        @click="(val: any) => onChartClick(val)"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue';
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent, MarkLineComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { ScatterChart } from 'echarts/charts';
import NewSelect from 'common/components/NewSelect';
import { logNormalize } from 'common/utils/number';
import type { OptionData } from 'tdesign-vue-next';
import { BubbleChartData } from 'common/service/creative/label/insight/type';
import { BubbleDataItem } from './type';
import { useElementSize } from '@vueuse/core';
import { CHARTCOLORSET10, CHARTCOLORSET20, CHARTCOLORSET30 } from 'common/components/BasicChart/colorset';
import { TOOLTIP_TEXT_STYLE, TOOLTIP_EXTRA_CSS_TEXT } from 'common/components/BasicChart/options';
import { splitNameBySize } from '../utils';
import { storeToRefs } from 'pinia';
import { useLabelsInsightStore } from '@/store/creative/labels/labels-insight.store';

const { allMetrics } = storeToRefs(useLabelsInsightStore());

use([
  CanvasRenderer, ScatterChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, MarkLineComponent,
]);

const props = withDefaults(defineProps<{
  title: string,
  metricListX: OptionData[],
  metricListY: OptionData[],
  metricX: string,
  metricY: string,
  avgX: number,
  avgY: number,
  labelData: BubbleChartData[],
  isLoading: boolean,
  valueFormat: (value: number, col: string) => string,
}>(), {
  title: '',
  metricX: 'spend',
  metricY: 'ctr', // 'installs',
  isLoading: false,
});

const emits = defineEmits(['update:metricX', 'update:metricY', 'labelClick']);


const onMetricChangeX = (val: any) => {
  emits('update:metricX', val);
};

const onMetricChangeY = (val: string) => {
  emits('update:metricY', val);
};

const metricNameX = computed(() => {
  const target = props.metricListX.find(item => item.value === props.metricX);
  return target?.label;
});

const metricNameY = computed(() => {
  const target = props.metricListY.find(item => item.value === props.metricY);
  return target?.label;
});

const isPercentMetricX = computed(() => {
  const custom = allMetrics.value.find(item => item.key === props.metricX);
  return custom?.format === 'percent';
});

const isPercentMetricY = computed(() => {
  const custom = allMetrics.value.find(item => item.key === props.metricY);
  return custom?.format === 'percent';
});

const chartRef = ref();

const { width: chartWidth, height: chartHeight } = useElementSize(chartRef);

const option =  computed(() => {
  const showData = props.labelData.map(item => item.xVal * item.yVal);
  const normalizedData = logNormalize(showData, 20, 80);

  // 根据数组的长度来取不同的的颜色集合
  const color = (() => {
    if (props.labelData.length > 20) return CHARTCOLORSET30;
    return props.labelData.length > 10 ? CHARTCOLORSET20 : CHARTCOLORSET10;
  })();

  const data = props.labelData.map((item, index) => {
    const secondLabel = item.name.split('|')[1];
    const size = normalizedData[index];
    return {
      name: secondLabel,
      value: [item.xVal, item.yVal],
      asset_num: item.asset_num || 0,
      // itemStyle: { color: RADAR_COLORS[index % RADAR_COLORS.length] },
      itemStyle: { color: color[index] },
      showValue: size,
      totalName: item.name,
    };
  });

  // 图例的数据
  const legendData = data.map(item => item.name);
  const seriesList = data.map(item => ({
    type: 'scatter',
    data: [item],
    name: item.name,
    label: {
      show: true,
      formatter(params: Record<string,  any>) {
        const { seriesName: secondLabel, seriesIndex } = params;
        const size = normalizedData[seriesIndex];
        return splitNameBySize(secondLabel, size);
      },
    },
    symbolSize: item.showValue,
    itemStyle: item.itemStyle,
    markLine: props.avgX > 0 && props.avgY > 0 ? {
      silent: true,
      data: [
        { xAxis: props.avgX },
        { yAxis: props.avgY.toFixed(4) }, // eg.https://blog.csdn.net/weixin_43872895/article/details/120322382
      ],
      label: {
        show: false,
      },
      lineStyle: {
        color: '#d2d2d2',
      },
      animation: false,
      symbol: 'none', // 去掉线两端的箭头
    } : undefined,
  }));

  return {
    legend: {
      data: legendData,
      type: 'scroll',
      orient: 'horizontal',
      bottom: 0, // 将图例放在底部
      padding: [0, 16],
      itemGap: 24,
    },
    tooltip: {
      trigger: 'item',
      textStyle: {
        ...TOOLTIP_TEXT_STYLE,
      },
      borderWidth: 0,
      extraCssText: TOOLTIP_EXTRA_CSS_TEXT,
      formatter: tooltipFormatter,
    },
    grid: {
      top: 42,    // 上边距
      // bottom: 12, // 下边距
      bottom: 50, // 下边距
      left: 20,   // 左边距
      right: 40,  // 右边距
      containLabel: true, // 确保标签不会溢出容器
    },
    xAxis: {
      min: 0,
      show: props.labelData.length > 0,
      name: metricNameX.value,
      nameLocation: 'middle',
      nameGap: 20,
      splitLine: {
        show: false, // 隐藏 X 轴的网格线
      },
      axisLabel: {
        formatter: (value: number) => (isPercentMetricX.value ? `${(value * 100).toFixed(2)}%` : value),
      },
    },
    yAxis: {
      min: 0,
      show: props.labelData.length > 0,
      name: metricNameY.value,
      type: 'value',
      splitLine: {
        show: false, // 隐藏 X 轴的网格线
      },
      nameTextStyle: {
        align: 'left',
      },
      axisLabel: {
        formatter: (value: number) => (isPercentMetricY.value ? `${(value * 100).toFixed(2)}%` : value),
      },
    },
    series: seriesList,
  };
});

function tooltipFormatter(params: { data: BubbleDataItem }) {
  const { data } = params;
  const [firstLabel, secondLabel] = data.totalName.split('|');
  const value1 = props.valueFormat ? props.valueFormat(data.value[0], props.metricX) : data.value[0];
  const value2 = props.valueFormat ? props.valueFormat(data.value[1], props.metricY) : data.value[1];
  const title = `<p style="font-size: 12px; color: #000;
        font-weight: bold; line-height: 20px;
        margin-left: 6px;">${secondLabel}-${firstLabel}</p>`;
  const renderItem = (name: string, value: string | number) => (
    `<p style="
        margin-top: 4px; padding:6px 9px;
        box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
        border-radius: 4px; background-color: #fff;"
    > ${name}
      <span style="
        color: #1D2129;
        font-family: 'Byte Number';
        font-weight: bold; float: right;
        margin-left: 16px;">${value}</span>
    </p>`
  );
  return `
    <div>
      ${title}
      ${renderItem(`X ${metricNameX.value}:`, value1)}
      ${renderItem(`Y ${metricNameY.value}:`, value2)}
      ${renderItem('Creatives:', data.asset_num)}
    </div>
  `;
}

const onChartClick = (params: { seriesType: string, data: BubbleDataItem }) => {
  if (params.seriesType === 'scatter') {
    emits('labelClick', params.data);
  }
};

</script>
