<template>
  <t-loading :loading="props.isLoading">
    <div
      class="rounded-default border-[1px] border-[#ebecf1] py-[16px] flex flex-col gap-y-[16px] w-full h-full"
    >
      <div class="flex flex-warp justify-between px-[16px] items-center">
        <Text
          :content="props.leftTopLabel"
          weight="600"
          class="font-semibold whitespace-nowrap"
          size="title"
        />
        <div class="flex items-center gap-x-[8px]">
          <slot name="dtstatdate">
            <DateRangePicker
              :model-value="dtstatdate"
              :presets="presets"
              :max-date="dayjs().format(FORMAT)"
              value-type=""
              @update:model-value="onDtstatdateChange"
              @blur="onDtstatdateBlur"
            />
          </slot>
          <NewSelect
            :options="metricList"
            :model-value="metric"
            class="w-[180px]"
            @change="(val: any) => onMetricChange(val)"
          />
        </div>
      </div>
      <div
        v-if="props.isLoading"
        class="w-full h-[300px] flex justify-center items-center"
      >
        <DataEmpty />
      </div>
      <div
        v-else
        ref="chartContainerRef"
        class="flex-1"
        :data-height="chartHeight"
      >
        <v-chart
          v-if="chartHeight > 0"
          autoresize
          :option="echatrsOptions"
          :style="{
            height: `${chartHeight}px`,
            width: 'auto'
          }"
        />
      </div>
    </div>
  </t-loading>
</template>
<script lang="ts" setup>
import { PropType, computed, ref, watch } from 'vue';
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart } from 'echarts/charts';
import dayjs from 'dayjs';
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components';
import type { OptionData } from 'tdesign-vue-next';
import Text from 'common/components/Text';
import DateRangePicker from 'common/components/DateTimePicker';
import DataEmpty from 'common/components/NullAble/DataEmpty.vue';
import { isFunction, isArray, isEqual, cloneDeep, orderBy } from 'lodash-es';
import { useElementSize } from '@vueuse/core';
import NewSelect from 'common/components/NewSelect';

use([
  GridComponent, CanvasRenderer, LineChart, TitleComponent, TooltipComponent, LegendComponent,
]);

const FORMAT = 'YYYYMMDD';
const LEGEND_PLACEMENT = {
  BOTTOM: 'bottom',
  TOP: 'top',
} as const;

const TOOLTIP_SORT_TYPE = {
  DESC: 'desc',
  ASC: 'asc',
  DEFAULT: '',
} as const;;

type TFieldLabels = Record<string, string>;
type TValueFormat = (value: any) => any;
type TAreaStyleColor = Record<string, [string, string]>;
type TLegendPlacement = typeof LEGEND_PLACEMENT[keyof typeof LEGEND_PLACEMENT];
type TTooltipSortType = typeof TOOLTIP_SORT_TYPE[keyof typeof TOOLTIP_SORT_TYPE];

const emits = defineEmits(['update:dtstatdate', 'update:metric']);
const props = defineProps({
  // 左上角显示的文本
  leftTopLabel: {
    type: String,
    default: '',
  },
  // 数据源
  tableData: {
    type: Array as PropType<Record<string, any>[]>,
    default: () => [],
  },
  // tableData中每一项是对象，折线图的要显示哪些字段的？
  pickFields: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  // 字段的外显文本
  fieldLabels: {
    type: Object as PropType<TFieldLabels>,
    default: () => ({}),
  },
  // metric 下拉列表
  metricList: {
    type: Array as PropType<OptionData[]>,
    default: () => [],
  },
  // 选择metric
  metric: {
    type: String,
    default: '',
  },
  dtstatdate: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  // 是不是loading状态
  isLoading: {
    type: Boolean,
    default: false,
  },
  // 折线图颜色数组
  colors: {
    type: Array as PropType<string[]>,
    default: undefined,
  },
  tooltipValueFormat: {
    type: Function as PropType<TValueFormat>,
    default: undefined,
  },
  yAxisValueFormat: {
    type: Function as PropType<TValueFormat>,
    default: undefined,
  },
  isShowLegend: {
    type: Boolean,
    default: true,
  },
  dtstatdateField: {
    type: String,
    default: 'dtstatdate',
  },
  areaStyleColor: {
    type: Object as PropType<TAreaStyleColor>,
    default: () => ({}),
  },
  isShowSymbol: {
    type: Boolean,
    default: undefined,
  },
  legendPlacement: {
    type: String as PropType<TLegendPlacement>,
    default: 'top',
  },
  tooltipSortType: {
    type: String as PropType<TTooltipSortType>,
    default: '',
  },
});

// 根据chartContainerRef的宽度设置 图表的宽度
const chartContainerRef = ref<HTMLDivElement>();
const { height: chartHeight } = useElementSize(chartContainerRef);

const presets = computed(() => ({
  'Last 7 Days': [dayjs().subtract(6, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  'Last 14 Days': [dayjs().subtract(13, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  'Last 30 Days': [dayjs().subtract(29, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  'Last 90 Days': [dayjs().subtract(89, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  'Last 180 Days': [dayjs().subtract(179, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  'Last 365 Days': [dayjs().subtract(364, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  // [maxData当前年第一天,maxData]
  'Year to Date': [dayjs().startOf('year')
    .format(FORMAT), dayjs().format(FORMAT)],
}));

// 搞个中间变量中转一下
const dtstatdateInner = ref<string[]>([]);
watch(() => props.dtstatdate, (newVal, oldVal) => {
  if (!isEqual(newVal, oldVal)) {
    dtstatdateInner.value = cloneDeep(newVal);
  }
}, { immediate: true, deep: true });

const onDtstatdateChange = (val: string[]) => {
  dtstatdateInner.value = val?.map(date => dayjs(date).format(FORMAT));
  // emits('update:dtstatdate', val.map(date => dayjs(date).format(FORMAT)));
};
const onDtstatdateBlur = () => {
  if (!isEqual(props.dtstatdate, dtstatdateInner.value)) {
    emits('update:dtstatdate', dtstatdateInner.value);
  }
};

const onMetricChange = (val: string) => emits('update:metric', val);

// tooltip 自定义渲染
const renderTooltip = (params: Record<string, any>[]) => {
  const date = params?.[0]?.axisValue;
  const formatValue = (value: any) => {
    if (isFunction(props.tooltipValueFormat)) {
      return props.tooltipValueFormat(value);
    }
    return value;
  };
  const { ASC: acs, DESC: desc } = TOOLTIP_SORT_TYPE;
  const sortType = props.tooltipSortType;
  const isSort = sortType === acs ||  sortType === desc;
  const toolTipData = isSort ? orderBy(params, 'value', sortType) : params;

  const list = toolTipData.map(item => (
    `<div class="flex items-center gap-x-[16px] justify-between overflow-hidden">
      <div class="flex items-center gap-x-[8px]">
        ${item.marker}
        <div class="text-[#d2d4d9]">${item.seriesName}</div>
      </div>
      <div class="text-white">${formatValue(item.value)}</div>
    </div>
  `));
  return `
    <div class="flex p-[16px] pr-[8px] rounded-default flex-col gap-y-[8px] bg-[#1c293f]">
      <div class="text-white pr-[8px]">${dayjs(date).format('YYYY-MM-DD')}</div>
      <div class="creative-metric-line-tooltip-data-container max-h-[250px]  pr-[8px] overflow-y-auto">
        <div class="flex flex-col gap-y-[8px]">${list.join('')}</div>
      </div>
  </div>`;
};

// 图例配置
const legendCfg = computed(() => {
  if (!props.isShowLegend) return {};
  const legendData = props.pickFields.map(field =>  props.fieldLabels[field] || field);
  return {
    legend: {
      data: legendData,
      icon: 'circle',
      y: props.legendPlacement,
      type: 'scroll',
      padding: [0, 16],
      itemGap: 24,
    },
  };
});
// 颜色
const colorCfg = computed(() => {
  if (isArray(props.colors) && props.colors.length > 0) return {
    color: props.colors,
  };
  return {};
});
// x轴数据
const xAxisData = computed(() => props.tableData.map(item => item[props.dtstatdateField]));
// 折线图区域的样式
const getAreaStyle = (field: string) => {
  const colors = props.areaStyleColor[field];
  if (isArray(colors) && colors.length === 2) {
    return {
      areaStyle: {
        color: {
          type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [{
            offset: 0, color: colors[0], // 0% 处的颜色
          }, {
            offset: 1, color: colors[1], // 100% 处的颜色
          }],
          global: false, // 缺省为 false
        },
      },
    };
  }
  return {};
};
// 边距
const grid = computed(() => {
  const { isShowLegend } = props;
  const top = props.legendPlacement === LEGEND_PLACEMENT.TOP && isShowLegend ? 32 : 8;
  const bottom = props.legendPlacement === LEGEND_PLACEMENT.BOTTOM && isShowLegend ? 32 : 8;
  return { containLabel: true, left: 16, top, bottom, right: 16 };
});
// 数据源
const series = computed(() => props.pickFields.map((field) => {
  const data =  props.tableData.map(item => item[field]);
  const name =  props.fieldLabels[field] || field;
  // 开始时间和结束时间是否相等
  const dateIsequal = (props.dtstatdate.length === 2 && props.dtstatdate[0] === props.dtstatdate[1]);
  return {
    data,
    name,
    type: 'line',
    smooth: true,
    symbol: 'circle',
    symbolSize: 8,
    showSymbol: props.isShowSymbol ?? dateIsequal,
    ...getAreaStyle(field),
    emphasis: {
      disabled: true, // 防止hover时背景色被覆盖
    },
  };
}));


const echatrsOptions = computed(() => (
  {
    ...colorCfg.value,
    ...legendCfg.value,
    tooltip: {
      trigger: 'axis', padding: 0, formatter: renderTooltip,
      appendTo: 'body', appendToBody: true, enterable: true,
      hideDelay: 800,
    },
    grid: grid.value,
    xAxis: {
      type: 'category',
      data: xAxisData.value,
      // 隐藏x轴刻度
      axisTick: { show: false },
      axisLine: {
        // x轴线
        lineStyle: { type: 'dashed', color: '#e4e7ed' },
      },
      axisLabel: {
        color: '#747D98', // x轴label
        formatter: (value: string) => dayjs(value).format('MM-DD'),
      },
    },
    yAxis: {
      type: 'value',
      splitLine: { lineStyle: { type: 'dashed' } /* 网格线虚线 */ },
    },
    series: series.value,
  }
));

</script>

<style lang="scss">
.creative-metric-line-tooltip-data-container {
  /* Chorem */
  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    padding-left: 8px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    @apply rounded-default;
    background-color: #fff;
    border: 1px solid transparent;
    transition: all .3s;
  }

  /* Firefox */
  // scrollbar-width: 4px;
  // scrollbar-color: #fff transparent;
}
</style>
