<template>
  <t-loading :loading="props.isLoading">
    <div
      class="rounded-default border-[1px] border-[#ebecf1] py-[16px] flex flex-col gap-y-[16px] w-full h-full"
    >
      <div class="flex flex-warp justify-between px-[16px] items-center">
        <Text
          :content="props.leftTopLabel"
          weight="600"
          class="font-semibold whitespace-nowrap"
          size="title"
        />
        <div class="flex items-center gap-x-[8px]">
          <slot name="dtstatdate">
            <DateRangePicker
              :model-value="dtstatdate"
              :presets="presets"
              :max-date="dayjs().format(FORMAT)"
              value-type=""
              @update:model-value="onDtstatdateChange"
              @blur="onDtstatdateBlur"
            />
          </slot>
          <NewSelect
            :options="metricList"
            :model-value="metric"
            class="w-[180px]"
            @change="(val: any) => onMetricChange(val)"
          />
        </div>
      </div>
      <div
        v-if="props.isLoading || !props.tableData.length"
        class="w-full h-[300px] flex justify-center items-center"
      >
        <DataEmpty />
      </div>
      <div
        v-else
        ref="chartContainerRef"
        class="flex-1"
        :data-height="chartHeight"
      >
        <BasicChart
          v-if="chartHeight > 0"
          :data="chartData"
          :data-item-field="DATA_ITEM_FIELD"
          :data-group-item-field="DATA_GROUP_ITEM_FIELD"
          :data-value-filed="DATA_VALUE_FILTE"
          :is-show-legend="props.isShowLegend"
          :x-axis-label-format="(value: string) => dayjs(value).format('MM-DD')"
          :tooltip-value-format="formatTooltipValue"
          :format-tooltip-name="(value: string) => dayjs(value).format('YYYY-MM-DD')"
          chart-type="line"
          tooltip-sort="desc"
          :chart-style="{
            height: `${chartHeight}px`
          }"
          :legend-props="legendProps"
          :grid="grid"
          detail-type="smooth"
          :series="series"
          :color="props.color"
          :chart-tooltip="{confine: false}"
          :tooltip-filter-zero="false"
        />
      </div>
    </div>
  </t-loading>
</template>
<script lang="ts" setup>
import { PropType, computed, ref, watch } from 'vue';
import dayjs from 'dayjs';
import type { OptionData } from 'tdesign-vue-next';
import Text from 'common/components/Text';
import DateRangePicker from 'common/components/DateTimePicker';
import DataEmpty from 'common/components/NullAble/DataEmpty.vue';
import { isFunction, isArray, isEqual, cloneDeep, flatMap, omit, orderBy, groupBy, difference } from 'lodash-es';
import { useElementSize } from '@vueuse/core';
import NewSelect from 'common/components/NewSelect';
import BasicChart from 'common/components/BasicChart';
import { DEFAULT_LEGEND_PROPS } from 'common/components/BasicChart/options';


const FORMAT = 'YYYYMMDD';
const LEGEND_PLACEMENT = {
  BOTTOM: 'bottom',
  TOP: 'top',
} as const;


const DATA_ITEM_FIELD = 'type';
const DATA_GROUP_ITEM_FIELD = 'date';
const DATA_VALUE_FILTE = 'value';

type TFieldLabels = Record<string, string>;
type TValueFormat = (value: any) => any;
type TAreaStyleColor = Record<string, [string, string]>;
type TLegendPlacement = typeof LEGEND_PLACEMENT[keyof typeof LEGEND_PLACEMENT];

const emits = defineEmits(['update:dtstatdate', 'update:metric']);
const props = defineProps({
  // 左上角显示的文本
  leftTopLabel: {
    type: String,
    default: '',
  },
  // 数据源
  tableData: {
    type: Array as PropType<Record<string, any>[]>,
    default: () => [],
  },
  // tableData中每一项是对象，折线图的要显示哪些字段的？
  pickFields: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  // 字段的外显文本
  fieldLabels: {
    type: Object as PropType<TFieldLabels>,
    default: () => ({}),
  },
  // metric 下拉列表
  metricList: {
    type: Array as PropType<OptionData[]>,
    default: () => [],
  },
  // 选择metric
  metric: {
    type: String,
    default: '',
  },
  dtstatdate: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  // 是不是loading状态
  isLoading: {
    type: Boolean,
    default: false,
  },
  // 折线图颜色数组
  color: {
    type: Array as PropType<string[]>,
    default: undefined,
  },
  tooltipValueFormat: {
    type: Function as PropType<TValueFormat>,
    default: undefined,
  },
  yAxisValueFormat: {
    type: Function as PropType<TValueFormat>,
    default: undefined,
  },
  isShowLegend: {
    type: Boolean,
    default: true,
  },
  dtstatdateField: {
    type: String,
    default: 'dtstatdate',
  },
  areaStyleColor: {
    type: Object as PropType<TAreaStyleColor>,
    default: () => ({}),
  },
  isShowSymbol: {
    type: Boolean,
    default: undefined,
  },
  legendPlacement: {
    type: String as PropType<TLegendPlacement>,
    default: 'top',
  },
  legendSortDescByFristDay: {
    type: Boolean,
    default: false,
  },
});

// 根据chartContainerRef的宽度设置 图表的宽度
const chartContainerRef = ref<HTMLDivElement>();
const { height: chartHeight } = useElementSize(chartContainerRef);

const presets = computed(() => ({
  'Last 7 Days': [dayjs().subtract(6, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  'Last 14 Days': [dayjs().subtract(13, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  'Last 30 Days': [dayjs().subtract(29, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  'Last 90 Days': [dayjs().subtract(89, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  'Last 180 Days': [dayjs().subtract(179, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  'Last 365 Days': [dayjs().subtract(364, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  // [maxData当前年第一天,maxData]
  'Year to Date': [dayjs().startOf('year')
    .format(FORMAT), dayjs().format(FORMAT)],
}));

// 搞个中间变量中转一下
const dtstatdateInner = ref<string[]>([]);
watch(() => props.dtstatdate, (newVal, oldVal) => {
  if (!isEqual(newVal, oldVal)) {
    dtstatdateInner.value = cloneDeep(newVal);
  }
}, { immediate: true, deep: true });

const onDtstatdateChange = (val: string[]) => {
  dtstatdateInner.value = val?.map(date => dayjs(date).format(FORMAT));
  console.log('111', dtstatdateInner.value);
  // emits('update:dtstatdate', val.map(date => dayjs(date).format(FORMAT)));
};
const onDtstatdateBlur = () => {
  if (!isEqual(props.dtstatdate, dtstatdateInner.value)) {
    emits('update:dtstatdate', dtstatdateInner.value);
  }
};

const onMetricChange = (val: string) => emits('update:metric', val);

const formatTooltipValue =  (value: any) => {
  if (isFunction(props.tooltipValueFormat)) {
    return props.tooltipValueFormat(value);
  }
  return value;
};

// 折线图区域的样式
const getAreaStyle = (field: string) => {
  const colors = props.areaStyleColor[field];
  if (isArray(colors) && colors.length === 2) {
    return {
      areaStyle: {
        color: {
          type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [{
            offset: 0, color: colors[0], // 0% 处的颜色
          }, {
            offset: 1, color: colors[1], // 100% 处的颜色
          }],
          global: false, // 缺省为 false
        },
      },
    };
  }
  return {};
};
// 边距
const grid = computed(() => {
  const { isShowLegend } = props;
  const top = props.legendPlacement === LEGEND_PLACEMENT.TOP && isShowLegend ? 32 : 8;
  const bottom = props.legendPlacement === LEGEND_PLACEMENT.BOTTOM && isShowLegend ? 32 : 8;
  return { containLabel: true, left: 16, top, bottom, right: 16 };
});


// 图例重新排序, 按照第一天的数值降序排列
const legendDataSort = (legendData: string[]) => {
  if (!props.legendSortDescByFristDay) return legendData;
  const types = firstData.value.map(item => item[DATA_ITEM_FIELD]);
  const fields: string[] = [];
  for (const typeItem of types) {
    if (legendData.includes(typeItem)) {
      fields.push(typeItem);
    }
  }
  return [
    ...fields,
    ...difference(legendData, fields),
  ];
};

// 数据源
const series = computed(() => legendDataSort(props.pickFields).map((field) => {
  const data =  props.tableData.map(item => item[field]);
  const name =  props.fieldLabels[field] || field;

  // 开始时间和结束时间是否相等
  const dateIsequal = (props.dtstatdate.length === 2 && props.dtstatdate[0] === props.dtstatdate[1]);
  return {
    data,
    name,
    type: 'line',
    smooth: true,
    // symbol: 'circle',
    symbolSize: 8,
    showSymbol: props.isShowSymbol ?? dateIsequal,
    ...getAreaStyle(field),
    emphasis: {
      disabled: true, // 防止hover时背景色被覆盖
    },
  };
}));


const legendProps = computed(() => {
  if (!props.isShowLegend) return {};
  return {
    ...omit(DEFAULT_LEGEND_PROPS, ['top']),
    left: 'center', // 图例居中
    y: props.legendPlacement,
    padding: [0, 16],
    itemGap: 24,
  };
});

// 转格式，转成 BasicChart 需要的格式
const chartData = computed(() => flatMap(props.tableData.map(row => convertDataRow(row))));
function convertDataRow(row: Record<string, any>) {
  const date = row[props.dtstatdateField];
  const list: Record<string, any>[] = [];
  for (const key of props.pickFields) {
    list.push({
      [DATA_GROUP_ITEM_FIELD]: date,
      [DATA_ITEM_FIELD]: props.fieldLabels[key] || key,
      [DATA_VALUE_FILTE]: row[key],
    });
  }
  return orderBy(list, DATA_VALUE_FILTE, 'desc');
}

// 获取第一天的数据
const firstData = computed(() => {
  const groupData = groupBy(chartData.value, DATA_GROUP_ITEM_FIELD);
  const sortDates = orderBy(Object.keys(groupData), undefined, 'asc');
  const firstDay = sortDates[0];
  const list = orderBy(groupData[firstDay] || [], DATA_VALUE_FILTE, 'desc');
  return list;
});

</script>

<style lang="scss">
</style>
