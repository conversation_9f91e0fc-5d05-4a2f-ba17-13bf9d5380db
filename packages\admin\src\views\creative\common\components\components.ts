import { defineAsyncComponent } from 'vue';

interface COMPONENT {
  [key: string]: any,
}

const components: COMPONENT = {
  't-date-picker': defineAsyncComponent(() => import('tdesign-vue-next/es/date-picker').then(DatePicker => DatePicker)),
  't-input': defineAsyncComponent(() => import('tdesign-vue-next/es/input').then(Input => Input)),
  't-select': defineAsyncComponent(() => import('tdesign-vue-next/es/select').then(Select => Select)),
  CDateRangePicker: defineAsyncComponent(() => import('common/components/DateRangePicker')),
  Switch: defineAsyncComponent(() => import('../../insight/report/components/switch.vue')),
  DeliveryDate: defineAsyncComponent(() => import('../../dashboard/components/DeliveryDate.vue')),
  Country: defineAsyncComponent(() => import('common/components/CountryCascader')),
  CommonSelect: defineAsyncComponent(() => import('common/components/creative/search/CommonInputSelect')),
  SearchBox: defineAsyncComponent(() => import('common/components/SearchBox')),
  Label: defineAsyncComponent(() => import('../../dashboard/components/Label.vue')),
  AccountId: defineAsyncComponent(() => import('../../dashboard/components/AccountId.vue')),
  DateRangePicker: defineAsyncComponent(() => import('../../dashboard/components/DateRangePicker.vue')),
  CampaignName: defineAsyncComponent(() => import('common/components/creative/search/CampaignName')),
  InputCascader: defineAsyncComponent(() => import('common/components/InputCascader')),
  Select: defineAsyncComponent(() => import('common/components/Select')),
  ImpressionDate: defineAsyncComponent(() => import('../../dashboard/components/ImpressionDate.vue')),
  Cron: defineAsyncComponent(() => import('../../dashboard/components/Cron.vue')),
};
export default components;
