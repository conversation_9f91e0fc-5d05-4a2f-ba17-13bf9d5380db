import { MEDIA } from '@@/index';
import { PropType } from 'vue';
import components from './components';
import { SEARCH_FORM_CONFIG } from './search';

export const commonProps = {
  model: {
    type: String,
    default: 'creative',
  },
  game: {
    type: String,
    default: 'pubgm',
  },
  media: {
    type: String as PropType<MEDIA>,
    default: 'google',
  },
  accountId: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: '',
  },
};

export const COMMON_SEARCH_SCHEMA = [
  {
    name: components.DateRangePicker,
    props: {
      allowInput: true,
      firstDayOfWeek: 1,
    },
    ext: {
      key: 'dtstattime',
      label: 'Date',
      isAllowClose: false,
      isHide: false,
    },
  },
  // {
  //   name: components.CommonSelect,
  //   props: {
  //     title: 'Size',
  //     multiple: true,
  //     componentsKey: 'size',
  //     commonInfo: {
  //       module: 'creative_pivot',
  //     },
  //   },
  //   ext: {
  //     key: 'asset_size',
  //     label: 'Size',
  //     isAllowClose: true,
  //     isHide: false,
  //   },
  // },
  // {
  //   name: components.SearchBox,
  //   props: {
  //     title: 'Search',
  //   },
  //   ext: {
  //     key: 'string_search',
  //     label: 'Search',
  //   },
  // },
  ...SEARCH_FORM_CONFIG,
  // {
  //   name: components.CommonSelect,
  //   props: {
  //     title: 'OS',
  //     multiple: true,
  //     componentsKey: 'os',
  //     commonInfo: {
  //       module: 'creative_pivot',
  //     },
  //   },
  //   ext: {
  //     key: 'platform',
  //     label: 'OS',
  //     isAllowClose: true,
  //     isHide: true,
  //   },
  // },
  // {
  //   name: components.CampaignName,
  //   props: {
  //     title: 'Campaign',
  //     multiple: true,
  //   },
  //   ext: {
  //     key: 'campaign_name',
  //     label: 'Campaign',
  //     isAllowClose: true,
  //   },
  // },
  // {
  //   name: components.CommonSelect,
  //   props: {
  //     title: 'Campaign Type',
  //     multiple: true,
  //   },
  //   ext: {
  //     key: 'campaign_type',
  //     label: 'Campaign Type',
  //     isAllowClose: true,
  //   },
  // },
  // {
  //   name: components.AccountId,
  //   props: {
  //     commonInfo: {
  //       module: 'creative_pivot',
  //     },
  //   },
  //   ext: {
  //     key: 'account_id',
  //     label: 'Account Id',
  //     isAllowClose: true,
  //   },
  // },
  // {
  //   name: components.Country,
  //   props: {
  //     commonInfo: {
  //       module: 'creative_pivot',
  //     },
  //   },
  //   ext: {
  //     key: 'country_code',
  //     label: 'Country/Market',
  //     isAllowClose: true,
  //   },
  // },
  // {
  //   name: components.CommonSelect,
  //   props: {
  //     title: 'Label Name',
  //   },
  //   ext: {
  //     key: 'label_name',
  //     label: 'Label Name',
  //     isAllowClose: true,
  //   },
  // },
  // {
  //   name: components.Label,
  //   props: {
  //     valueType: 'every',
  //   },
  //   ext: {
  //     key: 'all_label',
  //     label: 'Label',
  //     isAllowClose: true,
  //   },
  // },
  {
    name: components.CommonSelect,
    props: {
      title: 'Type',
      multiple: true,
    },
    ext: {
      key: 'asset_type',
      label: 'Type',
      isAllowClose: true,
    },
  },
  {
    name: components.CommonSelect,
    props: {
      title: 'NetWork',
      multiple: true,
    },
    ext: {
      key: 'network',
      label: 'NetWork',
      isAllowClose: true,
    },
  },
  // {
  //   name: components.CommonSelect,
  //   props: {
  //     title: 'Language',
  //     multiple: true,
  //   },
  //   ext: {
  //     key: 'asset_language',
  //     label: 'Language',
  //     isAllowClose: true,
  //   },
  // },
  // {
  //   name: components.CommonSelect,
  //   props: {
  //     title: 'Conversion Action',
  //     multiple: true,
  //     maxWidth: 'auto',
  //   },
  //   ext: {
  //     key: 'conversion_action_name',
  //     label: 'Conversion Action',
  //     isAllowClose: true,
  //   },
  // },
  // {
  //   name: components.ImpressionDate,
  //   props: {
  //     title: 'Impression Date',
  //   },
  //   ext: {
  //     key: 'impression_date',
  //     label: 'Impression Date',
  //     isAllowClose: true,
  //   },
  // },
];
export const IMPRESSION_DATE_SCHEMA = [
  {
    name: components.ImpressionDate,
    props: {
      title: 'Impression Date',
    },
    ext: {
      key: 'impression_date',
      label: 'Impression Date',
      isAllowClose: true,
    },
  },
];

export const GPP_SEARCH_SCHEMA = [
  // {
  //   name: components.DeliveryDate,
  //   props: {
  //     title: 'Delivery Date',
  //     clearable: true,
  //   },
  //   ext: {
  //     key: 'asset_delivery_date',
  //     label: 'Delivery Date',
  //     isAllowClose: true,
  //   },
  // },
  // {
  //   name: components.CommonSelect,
  //   props: {
  //     title: 'Format',
  //     multiple: true,
  //   },
  //   ext: {
  //     key: 'asset_format',
  //     label: 'Format',
  //     isAllowClose: true,
  //   },
  // },
  // {
  //   name: components.CommonSelect,
  //   props: {
  //     title: 'Perform',
  //     multiple: true,
  //   },
  //   ext: {
  //     key: 'asset_perform',
  //     label: 'Perform',
  //     isAllowClose: true,
  //   },
  // },
  // {
  //   name: components.CommonSelect,
  //   props: {
  //     title: 'Stage',
  //     multiple: true,
  //   },
  //   ext: {
  //     key: 'asset_stage',
  //     label: 'Stage',
  //     isAllowClose: true,
  //   },
  // },
];
export const HOK_SEARCH_SCHEMA = [
  // {
  //   name: components.CommonSelect,
  //   props: {
  //     title: 'Creative Content',
  //     multiple: true,
  //   },
  //   ext: {
  //     key: 'extract_label4',
  //     label: 'Creative Content',
  //     isAllowClose: true,
  //   },
  // },
];
export const PUBGM_SEARCH_SCHEMA = [];
