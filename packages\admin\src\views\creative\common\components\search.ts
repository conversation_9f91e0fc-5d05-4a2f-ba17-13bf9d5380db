
import { BaseCommonSearchInput } from 'common/components/CommonSearchInputBox';
import type { IFormDynamicItem } from 'common/components/FormContainer/type';

export const SEARCH_FORM_CONFIG: readonly IFormDynamicItem[] = [
  {
    name: BaseCommonSearchInput,
    props: {
      label: 'Asset Name',
      tagInputProps: {
        class: 'w-[216px]',
      },
    },
    ext: {
      key: 'asset_name',
      label: 'Asset Name',
      isAllowClose: false,
      isHide: false,
    },
  },
  {
    name: BaseCommonSearchInput,
    props: {
      label: 'YouTube Link',
      tagInputProps: {
        class: 'w-[216px]',
      },
    },
    ext: {
      key: 'youtube_id',
      label: 'YouTube Link',
      isAllowClose: true,
      isHide: true,
    },
  },
  {
    name: BaseCommonSearchInput,
    props: {
      label: 'Campaign Name',
      tagInputProps: {
        class: 'w-[240px]',
      },
    },
    ext: {
      key: 'campaign_name',
      label: 'Campaign Name',
      isAllowClose: true,
      isHide: true,
    },
  },
  {
    name: BaseCommonSearchInput,
    props: {
      label: 'Ad Group Name',
      tagInputProps: {
        class: 'w-[240px]',
      },
    },
    ext: {
      key: 'ad_group_name',
      label: 'Ad Group Name',
      isAllowClose: true,
      isHide: true,
    },
  },
  {
    name: BaseCommonSearchInput,
    props: {
      label: 'Ad Name',
      tagInputProps: {
        class: 'w-[200px]',
      },
    },
    ext: {
      key: 'ad_name',
      label: 'Ad Name',
      isAllowClose: true,
      isHide: true,
    },
  },
] as const;


// 搜索框中的 Serial, Play, Custom 这三个选项不要了
// 需求: https://tapd.woa.com/tapd_fe/20427967/story/detail/1020427967120443746
export const EXCLUDE_SEARCH_BOX_KEYS = [
  'asset_serial_id', 'asset_play', 'asset_custom_name',
] as const;
