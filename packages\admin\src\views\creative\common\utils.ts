// 根据气泡大小分割名称
export function splitNameBySize(name: string, size: number) {
  let len = 1;
  if (size >= 80) len = 6;
  else if (size >= 70) len = 5;
  else if (size >= 50) len = 4;
  else if (size >= 40) len = 3;
  else if (size >= 30) len = 2;
  if (name.length <= len) return name;
  return `${name.slice(0, len)}...`;
}

/**
 * 随机生成尽可能分散的n个点
 *
 * @param x1 - 节点1X坐标
 * @param y1 - 节点2X坐标
 * @param x2 - 节点3X坐标
 * @param y2 - 节点4X坐标
 * @returns 返回两个几点间的距离
 */
export function calculateDistance(x1: number, y1: number, x2: number, y2: number) {
  return Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
}
