<template>
  <CommonView
    :form-props="{
      formList: formRenderList,
      modelValue: formModelValue,
      foldList: formFoldList,
      'onUpdate:modelValue': updateFormModelValue,
      'onUpdate:foldList': updateFormFoldList,
      onSubmit: onFormSubmit,
      onReset: onFormReset,
      isShowGlobalLoading: isViewLoading || isInitLoading,
      confirmDisabled: isViewLoading || isChartLoading || isTableLoading,
      resetDisabled: isViewLoading || isChartLoading || isTableLoading,
    }"
    :tab-props="{
      list: viewList,
      modelValue: viewId,
      isShowViewType: false,
      shareParams: shareViewParams,
      'onUpdate:modelValue': onViewChange,
      onAddView,
      onUpdateView,
      onDeleteView
    }"
  >
    <template #views>
      <div class=" flex flex-col gap-y-[24px]">
        <metricCard />
        <Chart />
        <Table />
      </div>
    </template>
  </CommonView>
</template>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import CommonView from 'common/components/Layout/CommonView.vue';
import { useCreativeDashboardAfStore } from '@/store/creative/dashboard-af/index.store';
import { useWatchGameChange } from 'common/compose/request/game';
import metricCard from './metric-card.vue';
import Chart from './chart.vue';
import Table from './table.vue';

const store = useCreativeDashboardAfStore();
const {
  onFormReset, onFormSubmit, updateFormFoldList, updateFormModelValue,
  onAddView, onUpdateView, onDeleteView, onViewChange,
} = store;
const {
  formRenderList, formModelValue, formFoldList,
  viewList, viewId, shareViewParams, isViewLoading,
  isInitLoading, isTableLoading, isChartLoading,
} = storeToRefs(store);


useWatchGameChange(async () => {
  store.infnPageConfig();
});
</script>
<style lang="scss" scoped>

</style>
