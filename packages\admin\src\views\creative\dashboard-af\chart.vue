<template>
  <div>
    <CollapseCard ref="collapseCardRef" :is-expand="true">
      <div v-if="isChartLoading || isInitLoading" class="h-[300px] relative ">
        <FullLoading />
      </div>
      <template v-else>
        <div v-if="!chartRenderList.length" class="h-[300px] flex justify-center items-center">
          <DataEmpty />
        </div>
        <BasicChart
          v-else
          data-group-item-field="dtstatdate"
          chart-type="line"
          :data="chartRenderList"
          :data-value-filed="cardActiveMetric"
          data-item-field="key"
          :tooltip-value-format="chartValueFromat"
          :y-axis-label-format="chartValueFromat"
          is-show-legend
          is-open-effect-scatter
        />
      </template>
    </CollapseCard>
  </div>
</template>
<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useCreativeDashboardAfStore } from '@/store/creative/dashboard-af/index.store';
import CollapseCard from 'common/components/trade/ads-management/collapse-card/index';
import BasicChart from 'common/components/BasicChart';
import FullLoading from 'common/components/FullLoading';
import DataEmpty from '@/components/nullable/DataEmpty.vue';
import { getFormatRule } from '@/store/creative/dashboard-af/utils';
import dayjs from 'dayjs';
import { orderBy } from 'lodash-es';
import TemplateHelper from 'common/utils/template/templateHelper';

const collapseCardRef = ref<InstanceType<typeof CollapseCard>>();

const store = useCreativeDashboardAfStore();
const {
  lineChartData, cardActiveMetric, cardActiveMetricLabel,
  isChartLoading, groupMetricList, isInitLoading,
} = storeToRefs(store);

const chartRenderList = computed(() => {
  const orderByList = orderBy([...lineChartData.value], ['dtstatdate'], ['asc']);
  return orderByList.map(item => ({
    ...item,
    key: cardActiveMetricLabel.value,
    dtstatdate: dayjs(`${item.dtstatdate}`).format('YYYY-MM-DD'),
  }));
});

// 选中的指标卡
const cardActiveMetricItem = computed(() => (
  groupMetricList.value[cardActiveMetric.value]?.[0]
));


function chartValueFromat(value: any) {
  const instance = new TemplateHelper();
  const { format, key } = cardActiveMetricItem.value ?? {};
  const  formatValue = getFormatRule({ decimalLen: 3 })[format] ?? [];
  const formatData = instance.formatData({ [key]: value }, [{ name: key, value: formatValue }]);
  return formatData[key];
}


watch(() => [...lineChartData.value], (val) => {
  const dataList = val.map(item => item.dtstatdate);
  const isExpand = new Set(dataList).size > 1;
  if (isExpand) {
    collapseCardRef.value?.show();
  } else {
    collapseCardRef.value?.hide();
  }
});
</script>

<style lang="scss" scoped>

</style>
