<template>
  <div>
    <div v-if="isHideCard" class="rounded-large bg-white-primary w-full h-[100px] relative ">
      <FullLoading v-if="isTableLoading || isInitLoading" class="rounded-large overflow-hidden" />
    </div>
    <MetricCardSwiper
      v-else
      :list="metricSwiperList as IMetricListItem[]"
      :active-index-outter="metricCardActiveIndex"
      @change="onMetricCardChange"
    />
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useCreativeDashboardAfStore } from '@/store/creative/dashboard-af/index.store';
import MetricCardSwiper from 'common/components/MetricCardSwiper/index.vue';
import type { IMetricListItem } from 'common/components/MetricCardSwiper/type';
import FullLoading from 'common/components/FullLoading';

const store = useCreativeDashboardAfStore();
const { onMetricCardChange } = store;
const { metricSwiperList, metricCardActiveIndex, isTableLoading, isInitLoading } = storeToRefs(store);

const isHideCard = computed(() => [
  !metricSwiperList.value.length,
  isTableLoading.value,
  isInitLoading.value,
].some(item => item));
</script>

<style lang="scss" scoped>

</style>
