<template>
  <div>
    <BusinessTable
      :model-value="tableModelValue"
      :attr-list="tableAttrList"
      :is-loading="isTableLoading || isInitLoading"
      :table-data="{
        data: tableDataList,
        rowKey: '',
        count: tableDataCount,
      }"
      :table-props="{
        maxHeight: 700
      }"
      use-table-data-download
      :metric-list="formatMetriclist(metricList)"
      :table-columns-rule="tableColumnsRule"
      :fetch-data-list="(fetchDataList as any)"
      :download-file-name="downloadFileName"
      is-showfilter-total
      @update:model-value="updateModelValue"
    />
    <Preview
      ref="previewRef"
      :hide-trigger="true"
      :get-url="getPreviewUrl"
      :title="previewItem.title"
      :type="previewItem.type"
    />
  </div>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import BusinessTable from 'common/components/BusinessTable';
import { storeToRefs } from 'pinia';
import { useCreativeDashboardAfStore } from '@/store/creative/dashboard-af/index.store';
import { useGlobalGameStore } from '@/store/global/game.store';
import { formatMetriclist, generateApiReqParams, getFormatRule } from '@/store/creative/dashboard-af/utils';
import type { IModelValue as IBusinessTableModelValue  } from 'common/components/BusinessTable';
import { getTableDataService } from 'common/service/creative/dashboard-af/index';
import { isEqual, isPlainObject, pick } from 'lodash-es';
import dayjs from 'dayjs';
import Preview from 'common/components/Dialog/Preview';
import { isUrl } from 'common/utils/url';

const store = useCreativeDashboardAfStore();
const { updateTableModelValue, getTableData, getLineChart } = store;
const {
  attrList,  metricList, tableModelValue, tableDataCount,
  tableDataList, formModelValue, isTableLoading, isInitLoading,
} = storeToRefs(store);

const { gameCode } = storeToRefs(useGlobalGameStore());

// 预览
const previewRef = ref<InstanceType<typeof Preview> >();
const allowPreviewAssetTypes = ['VIDEO', 'IMAGE'];
const previewItem = ref<{
  url: string,
  title: string,
  type: 'video' | 'image'
}>({ url: '', title: '', type: 'image' });
const getPreviewUrl = () => Promise.resolve(previewItem.value.url);
// 打开预览弹窗
const onPreviewClick = (row: Record<string, any>) => {
  previewItem.value = {
    url: row.asset_url,
    title: row.asset_name,
    type: row.asset_type.toLocaleLowerCase(),
  };
  previewRef.value?.show();
};

// 自定义渲染attr的列
const attrCustomerCol = {
  asset_name: {
    cell: (_h: Function, { row }: {row: Record<string, any>}) => {
      const { asset_name: assetName, asset_url: assetUrl, asset_type: assetType } = row;
      if (isUrl(assetUrl) && allowPreviewAssetTypes.includes(assetType)) {
        return _h('a', {
          href: 'javascript:;',
          'data-href': assetUrl,
          onClick: () => onPreviewClick(row),
          class: 'text-brand hover:text-link',
        }, [assetName]);
      }
      return assetName;
    },
    ellipsis: (_h: Function, { row }: {row: Record<string, any>}) => row.asset_name,
  },
  asset_url: {
    cell: (_h: Function, { row }: {row: Record<string, any>}) => {
      const { asset_url: assetUrl } = row;
      if (!isUrl(assetUrl)) return assetUrl;
      return _h('a', {
        href: assetUrl,
        target: '_blank',
        class: 'text-brand hover:text-link',
      }, [assetUrl]);
    },
    ellipsis: (_h: Function, { row }: {row: Record<string, any>}) => row.asset_url,
  },
  impression_date: {
    cell: (_h: Function, { row }: {row: Record<string, any>}) => {
      const { impression_date: impressionDate } = row;
      if (/^\d{8}$/.test(`${impressionDate}`)) {
        return dayjs(`${impressionDate}`).format('YYYY-MM-DD');
      }
      return impressionDate;
    },
  },
};

const tableAttrList = computed(() => (
  attrList.value.map((item) => {
    const customCell = attrCustomerCol[item.key as keyof typeof attrCustomerCol];
    return {
      label: item.title,
      value: item.key,
      width: item.width,
      ellipsis: true,
      ...(isPlainObject(customCell) ? { ...customCell } : {}),
    };
  })
));

// 表格格式化
const tableColumnsRule = computed(() => (
  metricList.value.map((item) => {
    const formatValue = getFormatRule()[item.format] ?? [];
    return {
      name: item.key,
      value: formatValue,
    };
  })
));

// 下载的文件名
const downloadFileName = computed(() => {
  const dateRange = formModelValue.value.dtstatdate.map(item => dayjs(item).format('YYYY-MM-DD'));
  return `${dateRange.join('-')}_${gameCode.value}`;
});

const updateModelValue = (modelValue: IBusinessTableModelValue) => {
  const metricIsUpdate = !isEqual(modelValue.metric, tableModelValue.value.metric);
  const groupIsUpdate = !isEqual(modelValue.groupby, tableModelValue.value.groupby);
  updateTableModelValue({
    ...modelValue,
    pageIndex: metricIsUpdate || groupIsUpdate ? 1 : modelValue.pageIndex,
  });
  getTableData();
  // metric发发生变化时才需要重新拉图表
  if (metricIsUpdate) {
    getLineChart();
  }
};

// 导出excel时，传给组建的callback
const fetchDataList = async (modelValue: IBusinessTableModelValue) => {
  // 直接调service中的方法
  const params = generateApiReqParams(formModelValue.value, modelValue);
  const res = await getTableDataService({
    ...params.tableParams,
    needCount: true,
  });
  const { groupby, metric } = tableModelValue.value;
  const pickKeys = [...groupby, ...metric];
  const data = res.data.map((rowItem) => {
    const {
      extra_asset_url: extraAssetUrl,
      extra_asset_type: extraAssetType,
      extra_asset_serial_id: extraAssetSerialId,
    } = rowItem;
    const newRowItem = {
      ...rowItem,
      asset_url: extraAssetUrl,
      asset_type: extraAssetType,
      asset_serial_id: extraAssetSerialId,
    };
    return pick(newRowItem, pickKeys);
  });
  return [{
    ...res,
    data,
  }];
};

</script>

<style lang="scss" scoped>

</style>
