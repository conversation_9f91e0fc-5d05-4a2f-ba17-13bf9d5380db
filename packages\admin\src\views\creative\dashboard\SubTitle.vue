<template>
  <tab-view
    v-model="creativeDashboard.view.currentViewId"
    :list="creativeDashboard.view.viewList"
    :loading="creativeDashboard.view.loading"
    :game="props.game"
    :on-share-request="creativeDashboard.view.shareView"
    :filter-view-type="filterViewType"
    :show-download="!creativeDashboard.showSwiper"
    :download-ing="downloadLoading"
    download-show-type="dropdown"
    download-module="creative_top_report"
    @update="creativeDashboard.updateViewList"
    @add="creativeDashboard.addViewHandler"
    @delete="creativeDashboard.view.deleteView"
    @download="download"
  />
  <BaseDialog
    ref="downloadRef"
    title="Report Details"
    confirm-text="apply"
    :width="600"
    @confirm="fileMessageConfirm"
    @close="closeFileMessage"
  >
    <t-form
      ref="formRef"
      :rules="rules"
      :data="fileMessageForm"
      :label-width="150"
      @submit="onSubmit"
    >
      <t-form-item
        label="Report Name"
        name="fileName"
      >
        <Input v-model="fileMessageForm.fileName" placeholder="Report Name" />
      </t-form-item>
      <t-form-item
        label="Add Description"
        name="fileHeader"
      >
        <!-- <Input v-model="fileMessageForm.fileHeader" placeholder="Add description" /> -->
        <Textarea
          v-model="fileMessageForm.fileHeader"
          placeholder="Add description"
          :autosize="{ minRows: 6, maxRows: 6 }"
        />
      </t-form-item>
    </t-form>
  </BaseDialog>
</template>
<script setup lang="ts">
import TabView from './components/TabView';
import { useCreativeDashboardStore } from '@/store/creative/dashboard/dashboard.store';
import Textarea from 'common/components/Textarea';
import { capitalize, computed, ref, watch } from 'vue';
import { IDownLoadOpt, useDownloadFile } from 'common/compose/download-file';
import { MetricItemType } from '@/store/creative/dashboard/dashboard';
import { WEEKLY_TOP_ATTRIBUTE, WEEKLY_TOP_CUSTOM_ATTRIBUTE } from '@/store/creative/dashboard/dashboard.const';
import { checkTypeNoImgOrVideo, linkToPreview } from '@/store/creative/dashboard/utils';
import Input from 'common/components/Input';
import { SubmitContext } from 'tdesign-vue-next';
import BaseDialog from 'common/components/Dialog/Base';

const downloadLoading = ref(false);
const props = defineProps({
  game: {
    type: String,
    default: 'default',
  },
});
const creativeDashboard = useCreativeDashboardStore();
const downloadRef = ref();
const initFileMessage = {
  fileName: '',
  fileHeader: '',
};
const fileMessageForm = ref(initFileMessage);
const formRef = ref();
const rules = {
  fileName: [{ required: true, message: 'File Name is required', type: 'error' }],
};
// 过滤掉单选框中选择view game用
const filterViewType = computed(() => (props.game === 'pubgm'
  ? () => []
  : (options: {label: string, value: string}[]) => options));

const download = () => {
  Object.keys(initFileMessage).forEach((key) => {
    fileMessageForm.value[key as keyof typeof initFileMessage] = initFileMessage[key as keyof typeof initFileMessage];
  });
  downloadRef.value.show();
  // downloadHeader();
};

const fileMessageConfirm = () => {
  formRef.value.submit();
};

const closeFileMessage = () => {
  downloadRef.value.hide();
};

const onSubmit = (context: SubmitContext<FormData>) => {
  if (context.validateResult === true) {
    const fileName = `${fileMessageForm.value.fileName}.xlsx`;
    downloadHeader(fileName);
    closeFileMessage();
  }
};

const downloadHeader = (fileName?: string) => {
  downloadLoading.value = true;
  creativeDashboard.downloadHandler().then((data: any) => {
    const otherFileName = fileName || `${creativeDashboard.form.dtstattime.map(item => item.split(' ')[0]).join('-')}-top-report.xlsx`;
    const header: IDownLoadOpt['header'] = {};
    creativeDashboard.form.groupby.forEach((key) => {
      header[key] = creativeDashboard.attribute.find(item => item.colKey === key)?.label || key;
    });
    if (!creativeDashboard.showSwiper && !creativeDashboard.filterAssetName) {
      WEEKLY_TOP_ATTRIBUTE
        .concat(checkTypeNoImgOrVideo(creativeDashboard.form.asset_type) ? [] : WEEKLY_TOP_CUSTOM_ATTRIBUTE)
        .forEach((key) => {
          header[key] = creativeDashboard.attribute.find(item => item.colKey === key)?.label || capitalize(key);
        });
    }
    creativeDashboard.form.metric.forEach((key) => {
      header[key] = creativeDashboard.metricList.find((item: MetricItemType) => item.colKey === key)?.title || key;
    });
    const newData = data.map((item: any) => {
      const temp = item;
      if (!creativeDashboard.showSwiper) {
        temp.preview = linkToPreview(temp.asset_type, temp.asset_url);
      }
      return temp;
    });
    const { isLoading } = useDownloadFile(newData, otherFileName, {
      header,
      lock: true,
      imgColumn: ['preview'],
      needMergeKey: creativeDashboard.form.groupby,
      isLink: ['asset_url'],
      headerString: fileMessageForm.value.fileHeader,
    }, 'excel');
    const loadingWatch = watch(
      () => isLoading.value,
      (value) => {
        console.log('loadingWatch', value);
        // downloadLoading.value = value;
        if (!value) {
          loadingWatch();
        }
      },
      {
        deep: true,
      },
    );
  })
    .catch((e) => {
      downloadLoading.value = false;
      console.error(e);
    });
};
</script>
