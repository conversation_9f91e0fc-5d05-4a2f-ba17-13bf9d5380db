<template>
  <div
    v-loading="!creativeDashboard.allLoading && creativeDashboard.tableLoading"
    class="rounded-extraLarge bg-[#FFFFFF] p-[16px]"
  >
    <data-container
      :total="creativeDashboard.tableTotal"
      :page-size="creativeDashboard.form.pageSize"
      :default-page="creativeDashboard.form.pageIndex / creativeDashboard.form.pageSize + 1"
      :page-size-options="[20, 30, 50, 100, 200]"
      :hide-footer="!creativeDashboard.showSwiper"
      @on-page-change="onPageChange"
    >
      <template #attributeSlot>
        <div class="flex items-center gap-x-[8px]">
          <AttrDropdownList
            class="w-auto"
            :attr-max="50"
            :disabled="creativeDashboard.allLoading || creativeDashboard.tableLoading"
            :list="attrList"
            :values="creativeDashboard.form.groupby"
            @change="onAttrChange"
          />
        </div>
      </template>
      <template #actionSlot>
        <p
          class="cursor-pointer p-[5px] flex items-center text-[#747D98] hover:bg-background hover:text-[#4981F2] svg-icon rounded-default"
          @click="showTableSelect"
        >
          <svg-icon
            class="normal-hover svg mr-[6px]"
            name="more"
          />
          Metrics
        </p>
        <div class="flex items-center text-[#747D98]">
          <Download
            v-if="creativeDashboard.showSwiper"
            show-type="dropdown"
            module="creative_dashboard"
            :game="gameStore.gameCode"
          />
          <p
            v-else
            class="flex items-center text-black-secondary p-[5px] cursor-pointer whitespace-nowrap"
            @click="openTopSetting"
          >
            <t-icon
              class="normal-hover svg mr-[6px]"
              name="arrow-down"
            />
            Top Setting
          </p>
        </div>
        <div class="flex">
          <div
            v-if="!creativeDashboard.showSwiper"
            class="flex items-center text-black-secondary p-[5px]"
          >
            <p class="whitespace-nowrap">Filter AssetName</p>

            <t-switch
              v-model="creativeDashboard.filterAssetName"
              class="ml-[5px]"
              size="large"
              @change="filterTotalChange"
            />
          </div>
          <div class="flex items-center text-black-secondary p-[5px]">
            <p class="whitespace-nowrap">
              {{ t('filterTotal') }}
            </p>
            <t-switch
              v-model="creativeDashboard.filterTotal"
              class="ml-[5px]"
              size="large"
            />
          </div>
        </div>
      </template>
      <Table
        v-if="!creativeDashboard.allLoading && !creativeDashboard.tableLoading"
        v-model:display-columns="creativeDashboard.displayColumns"
        :loading="!creativeDashboard.allLoading && creativeDashboard.tableLoading"
        resizable
        row-key="id"
        :data="creativeDashboard.table"
        :columns="creativeDashboard.tableAllColumns"
        :horizontal-scroll-affixed-bottom="true"
        :header-affixed-top="true"
        :rowspan-and-colspan="rowspanAndColspan"
      />
    </data-container>
    <customize-columns-dialog
      v-model:visible="columnsVisible"
      :list="columnsList"
      :selected-list="columnsSelectList"
      @confirm="onConfirm"
    />
    <BaseDialog
      ref="topSettingRef"
      title="Top Setting"
      width="619px"
      confirm-text="apply"
      @confirm="settingTop"
      @close="closeSettingTop"
    >
      <t-form
        ref="formRef"
        :data="form"
        :required-mark="false"
      >
        <t-form-item
          label="Show Top"
          name="top"
        >
          <div>
            <div
              v-for="(item, index) in creativeDashboard.form.groupby.concat('asset_name')"
              :key="item"
              class="flex justify-between items-center mb-[10px]"
            >
              <p class="mr-[10px] whitespace-nowrap">{{ showAttributeLabel(item) }}</p>
              <t-select
                class="w-[150px]"
                :value="form.top[index]"
                :options="topOptions"
                @change="(value: number) => form.top[index] = value"
              />
            </div>
          </div>
        </t-form-item>
      </t-form>
    </BaseDialog>
  </div>
</template>
<script setup lang="ts">
import DataContainer from 'common/components/Layout/DataContainer.vue';
import Table from 'common/components/table';
import CustomizeColumnsDialog, { IMetric } from 'common/components/CustomizeColumnsDialog';
import SvgIcon from 'common/components/SvgIcon';
import { useCreativeDashboardStore } from '@/store/creative/dashboard/dashboard.store';
import { computed, ref, watch } from 'vue';
import { getMergeConfig, groupByType } from './utils';
import { cloneDeep } from 'lodash-es';
import { useI18n } from 'common/compose/i18n';
import { I18N_BASE } from 'common/const/i18n';
import { BaseTableCellParams } from 'tdesign-vue-next';
import BaseDialog from 'common/components/Dialog/Base';
import { TopType } from '@/store/creative/dashboard/dashboard.const';
import Download from './components/Download.vue';
import { useGlobalGameStore } from '@/store/global/game.store';
import AttrDropdownList from 'common/components/BusinessTable/attrDropdownList/Index.vue';

const { t } = useI18n([I18N_BASE]);
const creativeDashboard = useCreativeDashboardStore();
const gameStore = useGlobalGameStore();
const columnsVisible = ref(false);

const columnsList = computed(() => groupByType(creativeDashboard.metricList));
const columnsSelectList = computed(() => creativeDashboard.form.metric.map(key => ({
  colKey: key,
})),
);
const topOptions = Object.keys(TopType)
  .filter(key => typeof TopType[key as any] === 'number')
  .map(key => ({
    label: key,
    value: TopType[key as any],
  }));

const onConfirm = (value: IMetric[]) => {
  creativeDashboard.form.metric = cloneDeep(value.map(item => item.colKey));
  // 同时更改currentView的metric
  if (creativeDashboard.view.currentView.param) {
    creativeDashboard.view.currentView.param.metric = cloneDeep(value.map(item => item.colKey));
  }
  creativeDashboard.hideAttributeMetricByOther();
  creativeDashboard.getAllData();
};

const showTableSelect = () => {
  columnsVisible.value = true;
};

const onPageChange = (current: number, info: any) => {
  creativeDashboard.form.pageIndex = (current - 1) * info.pageSize;
  creativeDashboard.form.pageSize = info.pageSize;
  creativeDashboard.getTableData();
};

const attrList = computed(() => creativeDashboard.attributeSchema.map(item => ({
  label: item.ext.label,
  value: item.ext.key,
})),
);

const onAttrChange = (val: string[]) => {
  creativeDashboard.form.groupby = val;
  creativeDashboard.hideAttributeMetricByOther();
  creativeDashboard.view.currentView.param = {
    ...creativeDashboard.view.currentView.param,
    groupby: creativeDashboard.form.groupby,
  };
  creativeDashboard.getTableData();
};

watch(
  () => creativeDashboard.form.groupby,
  (groupby) => {
    const cloneGroupby = cloneDeep(groupby);
    if (cloneGroupby[cloneGroupby.length - 1] !== 'asset_name') {
      cloneGroupby.push('asset_name');
    }
    const currentTop: any = {};
    creativeDashboard.form.topKey.forEach((key, index) => {
      currentTop[key] = creativeDashboard.form.top[index];
    });

    const newTop = cloneGroupby.map(key => currentTop[key] || 10);
    creativeDashboard.form.topKey = cloneGroupby;
    creativeDashboard.form.top = newTop;
  },
  {
    deep: true,
    immediate: true,
  },
);

const mergeConfigList = computed(() => getMergeConfig(creativeDashboard.form.groupby, creativeDashboard.table, creativeDashboard.form.pageIndex),
);
const rowspanAndColspan = ({ rowIndex, colIndex }: BaseTableCellParams<any>) => {
  // columnIndex是列，recordIndex是行
  const mergeColumnCfg = mergeConfigList.value[colIndex];
  const mergeNum = mergeColumnCfg?.[rowIndex];

  if (mergeColumnCfg && mergeNum) {
    return {
      rowspan: mergeNum,
    };
  }
};

const showAttributeLabel = (key: string) => creativeDashboard.attribute.find(item => item.colKey === key)?.label || key;

const form = ref<{ top: number[] }>({
  top: [],
});
// TOP Setting
const topSettingRef = ref();
const settingTop = () => {
  creativeDashboard.form.top = cloneDeep(form.value.top);
  closeSettingTop();
};

const closeSettingTop = () => {
  topSettingRef.value.hide();
};
const openTopSetting = () => {
  form.value.top = cloneDeep(creativeDashboard.form.top);
  topSettingRef.value.show();
};

const filterTotalChange = () => {
  creativeDashboard.getTableData();
};
</script>
<style lang="scss" scoped>
.svg-icon {
  .svg {
    color: #747d98;
    fill: #747d98;
  }
}

.svg-icon:hover {
  .svg {
    color: #4981f2;
    fill: #4981f2;
  }
}

.click-class {
  .svg {
    color: #4981f2;
    fill: #4981f2;
  }

  color: #4981f2;
  fill: #4981f2;
}
</style>
