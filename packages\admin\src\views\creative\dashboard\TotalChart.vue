<template>
  <div
    class="z-1"
  >
    <div class="h-[100px] mb-[24px]">
      <metric-card-swiper
        v-if="creativeDashboard.swiperList.length"
        :list="creativeDashboard.swiperList"
        @change="onChangeHandler"
      />
      <div v-else class="rounded-large bg-white-primary w-full h-full" />
    </div>
    <div class="mb-[24px]">
      <CollapseCard ref="collapseCard" :is-expand="true">
        <div class="px-[20px] py-[10px] min-h-[300px]">
          <div
            v-if="
              creativeDashboard.allLoading ||
                creativeDashboard.chartLoading ||
                creativeDashboard.tableLoading"
            class="h-[300px]"
          >
            <FullLoading size="small" />
          </div>
          <basic-chart
            v-else-if="creativeDashboard.chartData.length > 0"
            :key="JSON.stringify(creativeDashboard.chartData)"
            data-item-field="key"
            data-group-item-field="dtstatdate"
            chart-type="line"
            :y-axis-label-format="creativeDashboard.yAxisLabelFormat"
            :color="creativeDashboard.color"
            :data="creativeDashboard.chartData"
            :data-value-filed="creativeDashboard.swiperKey"
            is-show-legend
            :grid="{
              left: 60,
              right: 20,
              bottom: 30,
            }"
          />
          <div v-else class="h-[300px] flex justify-center items-center">
            <DataEmpty />
          </div>
        </div>
      </CollapseCard>
    </div>
  </div>
</template>
<script setup lang="ts">
import MetricCardSwiper from 'common/components/MetricCardSwiper/index.vue';
import { IItem } from 'common/components/MetricCard/type.d';
import { useCreativeDashboardStore } from '@/store/creative/dashboard/dashboard.store';
import CollapseCard from 'common/components/trade/ads-management/collapse-card/index';
import BasicChart from 'common/components/BasicChart';
import { ref, watch } from 'vue';
import DataEmpty from '@/components/nullable/DataEmpty.vue';
import FullLoading from 'common/components/FullLoading';

const collapseCard = ref<InstanceType<typeof CollapseCard> | null>(null);
const creativeDashboard = useCreativeDashboardStore();
const onChangeHandler = (index: number, item: IItem & { colKey: string }) => {
  if (index >= 0) {
    creativeDashboard.swiperKey = item.colKey;
  }
};

// 根据时间来判断折叠面板是否展开
watch(
  () => creativeDashboard.view.currentView.param?.dtstattime,
  (value) => {
    if (value || creativeDashboard.view.currentViewId) {
      if (!value) {
        collapseCard?.value?.show();
        return;
      }
      const [date1, date2] = value.map((item: string) => item.split(' ')[0]);
      if (date1 !== date2) {
        collapseCard?.value?.show();
        return;
      }
    }
    collapseCard?.value?.hide();
  },
  {
    deep: true,
    immediate: true,
  },
);
</script>
