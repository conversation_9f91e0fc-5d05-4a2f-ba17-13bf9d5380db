<template>
  <InputCascader
    v-bind="attrs"
    ref="country"
    :title="title"
    :labels="props.labels"
    :button="button"
  />
  <!-- :options="commonOptions" -->
</template>
<script setup lang="ts">
import InputCascader from 'common/components/InputCascader';
import { useAttrs, PropType, ref, watch } from 'vue';
import type { TCommonKeyInfo } from '@@/component';
import { button } from 'common/utils/format';
// import { CommonItem, useCommonSelect } from '../../compose/dropdown/useCommonSelect';

const emit = defineEmits(['outReset']);
const props = defineProps({
  title: {
    type: String,
    default: 'Account Id',
  },
  labels: {
    type: Array<string>,
    default: () => (['NetWork', 'Account Id']),
  },
  commonInfo: {
    type: Object as PropType<TCommonKeyInfo>,
    default: () => ({
      game: 'default',
    }),
  },
  outReset: {
    type: Boolean,
    default: false,
  },
  onOutReset: {
    type: Function,
    default: () => {},
  },
});
const accountId = ref<InstanceType<typeof InputCascader> | null>(null);
const attrs = useAttrs();

watch(
  [() => props.outReset, () => attrs.modelValue],
  ([outReset, modelValue]) => {
    if (outReset) {
      accountId.value?.updateValue((modelValue || attrs.modelValue) as string[], 'all');
      emit('outReset', false);
      props.onOutReset(false);
    }
  },
);

// const [commonOptions, getOptions] = useCommonSelect(props.commonInfo, 'country');
// (getOptions as () => Promise<CommonItem[]>)();


</script>
