<template>
  <div
    v-if="pcMoreType.length > 1"
  >
    <div class="flex flex-col">
      <PopupText
        v-for="(item ,index) in pcMoreHeadline"
        :key="`${index}_${item}`"
        :title="item"
      />
      <PopupText
        v-for="(item ,index) in pcMoreDescription"
        :key="`${index}_${item}`"
        :title="item"
      />
    </div>
    <template
      v-for="(item, index) in pcMoreType"
      :key="`${index}_${item}`"
    >
      <div>
        <PopupText
          v-if="item !== 'video' && item !== 'image'"
          :title="pcMoreTitle[index]"
        />
        <Preview
          v-else
          ref="previewRef"
          :type="item"
          :url="pcMoreUrl[index]"
          :title="pcMoreTitle[index]"
        >
          <template #trigger>
            <div
              @click="show(index)"
            >
              <PopupText
                :title="pcMoreTitle[index]"
                class="text-brand"
              />
            </div>
          </template>
        </Preview>
      </div>
    </template>
  </div>
  <div v-else>
    <PopupText
      v-if="props.url === ''"
      :title="props.title"
    />
    <Preview
      v-else
      ref="previewRef"
      :type="props.type"
      :url="props.url"
      :title="props.title"
    >
      <template #trigger>
        <div
          @click="show(0)"
        >
          <PopupText
            v-if="showType === 'text'"
            :title="props.title"
            class="text-brand"
          />
          <img
            v-else-if="props.url"
            class="cursor-pointer"
            :src="props.url"
            width="86"
            height="54"
          >
        </div>
      </template>
    </Preview>
  </div>
</template>
<script setup lang="ts">
import Preview from 'common/components/Dialog/Preview';
import PopupText from './PopupText.vue';
import { computed, PropType, ref } from 'vue';

export type IType = 'video' | 'image' | 'text';
const props = defineProps({
  type: {
    type: String as PropType<IType>,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  url: {
    type: String,
    default: '',
  },
  headline: {
    type: String,
    default: '',
  },
  description: {
    type: String,
    default: '',
  },
  showType: {
    type: String,
    default: 'text',
  },
});

const previewRef = ref<InstanceType<typeof Preview> | null>(null);
const show = (index = 0) => {
  if (Array.isArray(previewRef?.value)) {
    previewRef?.value?.[index].show?.();
  } else {
    previewRef?.value?.show?.();
  }
};

const pcMoreType = computed(() => props.type?.split(',') || []);
const pcMoreUrl = computed(() => props.url?.split(',').map(url => url.replace('watch?v=', 'embed/')) || []);
const pcMoreHeadline = computed(() => props.headline?.split(',') || []);
const pcMoreDescription = computed(() => props.description?.split(',') || []);

const pcMoreTitle = computed(() => {
  const title = props.title?.split(',') || [];
  const titleList: string[] = [];
  pcMoreType.value.forEach((type, index) => {
    if (type === 'video') {
      titleList.push(title.shift() as string);
    } else {
      titleList.push(pcMoreUrl.value[index]);
    }
  });
  return titleList;
});
</script>
