<template>
  <div v-if="props.url">
    <div
      v-if="assetUrl.length > 1"
      class="flex flex-col"
    >
      <template
        v-for="item in assetUrl"
        :key="item"
      >
        <t-tooltip
          :content="item"
          placement="top"
        >
          <a
            :href="item"
            class="text-brand"
            target="_blank"
          >
            {{ item }}
          </a>
        </t-tooltip>
      </template>
    </div>
    <a
      v-else
      :href="props.url"
      class="text-brand"
      target="_blank"
    >
      {{ props.url }}
    </a>
  </div>
</template>
<script setup lang="ts">import { computed } from 'vue';

const props = defineProps({
  url: {
    type: String,
    default: '',
  },
});
const assetUrl = computed(() => props.url?.split(','));
</script>
