<template>
  <div
    class="h-[36px] leading-[36px] border rounded-l-default rounded-r-none border-solid
    transition-all border-gray-placeholder pl-[16px] flex items-center"
  >
    <div
      class="w-[16px] h-[16px] flex justify-between items-center handle cursor-move"
    >
      <SvgIcon
        name="handler"
        size="14px"
        color="var(--aix-text-color-black-disabled)"
      />
    </div>
    <p
      class=""
    >
      {{ props.label }}
    </p>
  </div>
</template>
<script setup lang="ts">
import SvgIcon from 'common/components/SvgIcon';
const props = defineProps({
  label: {
    type: String,
    default: '',
  },
});
</script>
