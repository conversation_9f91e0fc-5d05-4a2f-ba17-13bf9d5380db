<template>
  <div class="flex items-center">
    <t-icon
      name="chevron-left"
      class="cursor-pointer"
      @click="distance('add')"
    />
    <div ref="showContent" class="overflow-x-hidden flex">
      <div
        ref="content"
        class="flex scroll-content "
        :style="`transform: translateX(${scrollDistance}px);`"
      >
        <draggable
          v-model="renderList"
          class="flex"
          handle=".handle"
          item-key="ext.key"
          @start="dragRef = true"
          @end="dragRef = false"
          @update:model-value="updateModelValue"
        >
          <template #item="{element}">
            <div
              class="overflow-hidden flex items-center whitespace-nowrap ml-[10px] hover:opacity-[0.6]"
            >
              <Attribute
                :class="renderList.length === 1 ?
                  '!rounded-default pr-[12px] !border-r-[1px]'
                  : 'border-r-0'"
                :label="element.ext.label"
                v-bind="element.ext.props"
              />
              <close-btn
                v-if="renderList.length > 1"
                height="36px"
                :value="element.ext.key"
                @close="onClose(element)"
              />
            </div>
          </template>
        </draggable>
        <div ref="target" class="w-[30px]" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { IFormDynamicItem, IFormItem } from 'common/components/FormContainer';
import Attribute from './Attribute.vue';
import CloseBtn from 'common/components/FormContainer/CloseBtn.vue';
import { computed, ref } from 'vue';
import { useIntersectionObserver, useElementBounding, watchDebounced } from '@vueuse/core';
import Draggable from 'vuedraggable';

const scrollDistance = ref(0);
const emit = defineEmits(['close', 'update:modelValue']);
const props = defineProps({
  list: {
    type: Array<(IFormItem | IFormDynamicItem)>,
    default: () => ([]),
  },
  width: {
    type: Number,
    required: true,
  },
});
const onClose = (item: IFormItem | IFormDynamicItem) => {
  emit('close', item);
};
const renderList = computed(() => props.list.filter(item => !item.ext.isHide));
const targetIsVisible = ref(false);
const content = ref(null);
const { width: contentWidth } = useElementBounding(content);
// 拖住组件的ref
const dragRef = ref<boolean>(false);

const target = ref<any>(null);
const watchTargetRender = watchDebounced(
  () => target?.value,
  (targetValue) => {
    if (targetValue) {
      useIntersectionObserver(
        targetValue,
        ([{ isIntersecting }]) => {
          targetIsVisible.value = isIntersecting;
        },
      );
      watchTargetRender();
    }
  },
);

const distance = (type: string) => {
  const oneDistance = Math.round(contentWidth.value / renderList.value.length);
  if (type === 'add' && scrollDistance.value < 0) {
    scrollDistance.value = scrollDistance.value + oneDistance;
  }
  if (type === 'delete' && !targetIsVisible.value) {
    const minWidth = (contentWidth.value + scrollDistance.value) - props.width;
    scrollDistance.value = scrollDistance.value - (minWidth >= oneDistance ? oneDistance : minWidth);
  }
};
watchDebounced(
  () => renderList.value,
  (list, oldList) => {
    if (list.length < oldList?.length && targetIsVisible.value) {
      distance('add');
    }
  }, {
    deep: true,
  },
);

const updateModelValue = (data: (IFormItem | IFormDynamicItem)[]) => {
  emit('update:modelValue', data.map(item => item.ext.key));
};
defineExpose({
  distance,
});

</script>
<style scoped lang="scss">
.scroll-content {
  position: relative;
  transition: transform 0.5s;
}
</style>
