<template>
  <popup-input-box ref="popRef" :title="title">
    <template #button>
      <div>{{ modelValue }}</div>
    </template>
    <template #content>
      <CronLight
        :model-value="props.modelValue"
        @update:model-value="updateModelValue"
      />
    </template>
  </popup-input-box>
  <div
    class="flex bg-white-primary border-gray-placeholder border-solid border-r-0 border
    rounded-l-default rounded-r-none h-[36px] pl-[14px] items-center"
  >
    <p>
      {{ title }}
    </p>
  </div>
</template>
<script setup lang="ts">
import PopupInputBox from 'common/components/PopupInputBox/PopupInputBox.vue';
const emit = defineEmits(['update:modelValue']);
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    default: '',
  },
});

const updateModelValue = (value: string) => {
  emit('update:modelValue', value);
};
</script>
