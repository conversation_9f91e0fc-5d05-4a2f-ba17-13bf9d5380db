<template>
  <div class="flex">
    <t-dropdown
      v-if="attrs.showSelect"
      :options="attrs.options"
      class="rounded-r-none"
      @click="clickHandler"
    >
      <t-button variant="text" class="border-solid border bg-white-primary border-gray-placeholder border-r-0 ">
        <div class="flex items-center">
          <span class="tdesign-demo-dropdown__text w-[45px]">
            {{ text }}
          </span>
          <t-icon name="chevron-down" />
        </div>
      </t-button>
    </t-dropdown>
    <DateRangePicker
      v-bind="omitAttrs"
      :model-value="dateValue"
      :mode="mode"
      format="YYYY-MM-DD"
      value-type="YYYY-MM-DD"
      :presets="(modePresets as any)"
      :on-preset-click="onPresetClick"
      @update:model-value="dateOnChange"
    />
  </div>
</template>
<script setup lang="ts">
import { DateValue, DropdownOption, PresetDate } from 'tdesign-vue-next';
import DateRangePicker from 'common/components/DateRangePicker';
import { computed, useAttrs, watch } from 'vue';
import { reactiveOmit } from '@vueuse/core';
import dayjs, { Dayjs } from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import isoWeek from 'dayjs/plugin/isoWeek';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import { cloneDeep } from 'lodash-es';
import { getPresets } from '../utils';

interface IPresets {
  'Last 7 Days'?: string[],
  'Last 14 Days'?: string[],
  'Last 30 Days'?: string[],
  'Last 90 Days'?: string[],
  'Last 180 Days'?: string[],
  'Last 365 Days'?: string[],
  'Last 4 Weeks'?: string[],
  'Last 12 Weeks'?: string[],
  'Last 24 Weeks'?: string[],
  'Year to Date': string[],
  'All of Time': string[],
}

const daily = [
  'Last 7 Days',
  'Last 30 Days',
  'Last 90 Days',
  'Last 180 Days',
  'Last 365 Days',
];
const weekly = [
  'Last 4 Weeks',
  'Last 12 Weeks',
  'Last 24 Weeks',
];

dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);
dayjs.extend(advancedFormat);
dayjs.extend(quarterOfYear);

const FORMAT = 'YYYYMMDD';

const attrs = useAttrs();
const emit = defineEmits(['change:version', 'change:presets', 'update:modelValue']);
const maxDate = computed(() => dayjs((attrs.disableDate as any).after));
const minDate = computed(() => dayjs((attrs.disableDate as any).before));
const presets = computed<IPresets>(() => getPresets(maxDate.value, minDate.value, FORMAT));

const omitAttrs = reactiveOmit(attrs, 'version', 'options', 'onChangeVersion', 'onChangePresets', 'modelValue', 'update:modelValue', 'isShowPopupHeader');

const text = computed(() => {
  const item = (attrs.options as {
    content: string, value: string
  }[]).find(item => item.value === attrs.version);
  if (!item) {
    const content = (attrs.options as {
      content: string, value: string
    }[])[0];
    if (content) {
      emit('change:version', content.value);
      return content.content;
    }
    return '';
  }
  return item.content;
});

const modeMap = {
  daily: 'date',
  weekly: 'week',
};
const mode = computed(() => (modeMap[attrs.version as keyof typeof modeMap]));

const startDiffMax = (start: Dayjs) => (start.endOf('week')
  .diff(maxDate.value) > 0 ? start.startOf('week').subtract(1, 'day') : start)
  .startOf('week')
  .add(1, 'day');
const endDiffMax = (end: Dayjs) => (end.endOf('week')
  .diff(maxDate.value) > 0 ? end.startOf('week').subtract(1, 'day') : end)
  .endOf('week')
  .add(1, 'day');

const dateOnChange = (value: DateValue[]) => {
  const end = dayjs(value[1]).subtract(1, 'day');
  const start = dayjs(value[0]).subtract(1, 'day');
  emit('update:modelValue', mode.value === 'week' ? [
    startDiffMax(start)
      .format(FORMAT),
    endDiffMax(end)
      .format(FORMAT),
  ] : value);
  // 每次更改时间时如果没有点击侧边栏就把dtstattimePresets变为空值
  (attrs.onChangePresets as Function)('');
};

// 如果切换到weekly要判断是否是超出了maxDay如果超出了需要更改为前一周
const dateValue = computed(() => {
  const [min, max] = (attrs.modelValue as string[]);
  if (mode.value === 'week' && dayjs(max).day() !== 0) {
    // 获取min这一周的周一
    // 获取到max的上一周的周五
    return [
      startDiffMax(dayjs(min))
        .format('YYYY-MM-DD'),
      endDiffMax(dayjs(max))
        .format('YYYY-MM-DD'),
    ];
  }
  return (attrs.modelValue as string[]).map(date => (
    dayjs(date).format('YYYY-MM-DD')));
});

const modePresets = computed(() => {
  const tempPresets = cloneDeep(presets.value);
  if (mode.value === 'week') {
    daily.forEach((key) => {
      delete tempPresets[key as keyof typeof tempPresets];
    });
  } else {
    weekly.forEach((key) => {
      delete tempPresets[key as keyof typeof tempPresets];
    });
  }
  return tempPresets;
});

const clickHandler = (value: DropdownOption) => {
  emit('change:version', value.value);
  (attrs.onChangeVersion as Function)(value.value);
  const [min, max] = (attrs.modelValue as string[]);
  if (value.value === 'weekly' && dayjs(max).day() !== 0) {
    // 获取min这一周的周一
    // 获取到max的上一周的周五
    const newValue = [
      startDiffMax(dayjs(min))
        .format(FORMAT),
      endDiffMax(dayjs(max))
        .format(FORMAT),
    ];
    emit('update:modelValue', newValue);
  }
};

const onPresetClick = (context: { preset: PresetDate }) => {
  const presets = Object.keys(context.preset)[0];
  emit('change:presets', presets);
  (attrs.onChangePresets as Function)(presets);
};

// TODO: watch dtstattimePresets 如果这个值更改并且 当前value 不是presets中的值的话就触发onChange事件来更改
watch(
  () => attrs.dtstattimePresets,
  (value) => {
    if (value) {
      const valueList = presets.value[value as keyof typeof presets.value] as any[];
      const module = (attrs.modelValue as string[])?.map((value: string) => dayjs(value).format(FORMAT));
      if (module.toString() !== valueList.toString()) {
        dateOnChange(valueList);
      }
    }
  }, {
    deep: true,
    immediate: true,
  },
);

</script>
<style lang="scss" scoped>
:deep(.t-range-input) {
  @apply h-[36px];
  .t-input {
    @apply h-full;
  }
}
:deep(.t-form__controls-content) {
  div[multiple='true'] {
    div {
      @apply border-r-0;
    }
  }
}
:deep(.t-range-input) {
  @apply rounded-l-none;
}
</style>
