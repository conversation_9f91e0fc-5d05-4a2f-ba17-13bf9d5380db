<template>
  <div class="flex items-center">
    {{ title }}:
    <t-date-picker
      v-bind="attrs"
      :value="props.modelValue"
      @change="changeHandler"
    />
  </div>
</template>
<script lang="ts" setup>
import { PARAM_FORMAT } from '@/store/creative/dashboard/dashboard.const';
import dayjs from 'dayjs';
import { DateValue } from 'tdesign-vue-next';
import { useAttrs } from 'vue';

const emit = defineEmits(['update:modelValue']);
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    required: true,
  },
});
const attrs = useAttrs();
const changeHandler = (value: DateValue) => {
  console.log(dayjs(value).format(PARAM_FORMAT));
  emit('update:modelValue', dayjs(value).format(PARAM_FORMAT));
};
</script>
