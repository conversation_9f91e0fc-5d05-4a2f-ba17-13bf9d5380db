<template>
  <div v-if="props.showType === 'dropdown'">
    <t-dropdown
      :options="options"
      :max-column-width="200"
      :placement="props.module === 'creative_top_report' ? 'left' : 'bottom-left'"
      trigger="click"
      :popup-props="{
        visible,
        onVisibleChange: (visibleShow: boolean) => {visible = visibleShow}
      }"
      @click="downloadClick"
    >
      <div v-if="props.module === 'creative_top_report'" class="h-[14px]">
        <t-icon
          v-if="props.downloadIng || downloadLoading"
          class="h-[14px] w-[14px] mt-[-16px]"
          name="loading"
        />
        <SvgIcon
          v-else
          class="cursor-pointer"
          name="download"
          size="14px"
          color="var(--aix-text-color-black-secondary)"
        />
      </div>
      <p
        v-else
        class="cursor-pointer p-[5px] flex items-center text-black-secondary hover:bg-white-primary
            hover:text-brand svg-icon rounded-default"
        :class="{'bg-[#dcdfe8] click-class': visible}"
      >
        <t-icon
          v-if="downloadLoading"
          class="normal-hover svg mr-[6px]"
          name="loading"
        />
        <svg-icon
          v-else
          class="normal-hover svg mr-[6px]"
          color="#747D98"
          :name="downloadLoading ? 'refresh' : 'download'"
        />
        {{ t('download') }}
      </p>
    </t-dropdown>
  </div>
  <t-tooltip
    v-else
    placement="bottom"
  >
    <template #content>
      {{ t('download') }}
    </template>
    <div class="h-[14px]">
      <t-icon
        v-if="props.downloadIng"
        class="h-[14px] w-[14px] mt-[-16px]"
        name="loading"
      />
      <SvgIcon
        v-else
        class="cursor-pointer"
        name="download"
        size="14px"
        color="var(--aix-text-color-black-secondary)"
        @click="download"
      />
    </div>
  </t-tooltip>
  <Task
    ref="taskRef"
    :module="props.module"
    :message="message"
    :task-name="taskName"
  />
  <BaseDialog
    ref="downloadRef"
    title="Report Details"
    confirm-text="apply"
    :width="600"
    @confirm="fileMessageConfirm"
    @close="closeFileMessage"
  >
    <t-form
      ref="formRef"
      :rules="rules"
      :data="fileMessageForm"
      :label-width="150"
      @submit="onSubmit"
    >
      <t-form-item
        label="Report Name"
        name="fileName"
      >
        <Input v-model="fileMessageForm.fileName" placeholder="Report Name" />
      </t-form-item>
      <t-form-item
        label="Add Description"
        name="fileHeader"
      >
        <!-- <Input v-model="fileMessageForm.fileHeader" placeholder="Add description" /> -->
        <Textarea
          v-model="fileMessageForm.fileHeader"
          placeholder="Add description"
          :autosize="{ minRows: 6, maxRows: 6 }"
        />
      </t-form-item>
    </t-form>
  </BaseDialog>
</template>
<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { DOWNLOAD_OPTIONS, TOP_REPORT_DOWNLOAD_OPTIONS } from '../const';
import { useI18n } from 'common/compose/i18n';
import { I18N_BASE } from 'common/const/i18n';
import { DropdownOption, SubmitContext } from 'tdesign-vue-next';
import Task from '@/components/Task/index.vue';
import { useCreativeDashboardStore } from '@/store/creative/dashboard/dashboard.store';
import { ASYNC_PAGE_SIZE, WEEKLY_TOP_ATTRIBUTE, WEEKLY_TOP_CUSTOM_ATTRIBUTE } from '@/store/creative/dashboard/dashboard.const';
import { useAuthStageStore } from '@/store/global/auth.store';
import { checkTypeNoImgOrVideo, linkToPreview } from '@/store/creative/dashboard/utils';
import { useGlobalGameStore } from '@/store/global/game.store';
import { IDownLoadOpt, useDownloadFile } from 'common/compose/download-file';
import { MetricItemType } from '@/store/creative/dashboard/dashboard';
import SvgIcon from 'common/components/SvgIcon';
import BaseDialog from 'common/components/Dialog/Base';
import Textarea from 'common/components/Textarea';
import Input from 'common/components/Input';
import { useEnv } from 'common/compose/env';
import { generateRandomTimeBasedNumber } from 'common/utils/string';


const { t } = useI18n([I18N_BASE]);

const props = defineProps({
  showType: {
    type: String,
    default: '',
  },
  module: {
    type: String,
    default: '',
  },
  downloadIng: {
    type: Boolean,
    default: false,
  },
  game: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['download']);
const visible = ref(false);
const downloadLoading = ref(false);
const creativeDashboard = useCreativeDashboardStore();
const useAuthStage = useAuthStageStore();
const gameStore = useGlobalGameStore();
const taskRef = ref<InstanceType<typeof Task> | null>();
const downloadRef = ref();
const initFileMessage = {
  fileName: '',
  fileHeader: '',
};
const fileMessageForm = ref(initFileMessage);
const formRef = ref();
const rules = {
  fileName: [{ required: true, message: 'File Name is required', type: 'error' }],
};
const options = computed(() => {
  let tempOptions = props.module === 'creative_top_report' ? TOP_REPORT_DOWNLOAD_OPTIONS : DOWNLOAD_OPTIONS;
  if (props.game === 'pubgm' && !creativeDashboard.options.pubgmRbac.includes(useAuthStage.currentUser)) {
    tempOptions = tempOptions.filter(item => item.value !== 'schedule');
  }
  if (creativeDashboard.tableTotal >= 65536 && props.game !== 'pubgm') {
    tempOptions = tempOptions.map((item) => {
      if (item.value === 'realTime') {
        const tempItem: any = item;
        tempItem.disabled = true;
        return tempItem;
      }
      return item;
    });
  }
  return tempOptions;
});
const taskName = computed(() => creativeDashboard.view.currentView.label + generateRandomTimeBasedNumber());


const message = computed(() => (form: any) => {
  const param = creativeDashboard.getTableParam({
    pageIndex: 0,
    pageSize: ASYNC_PAGE_SIZE,
    rbacWhere: creativeDashboard.rbacWhere,
  });
  param.where = param.where.filter((item: any) => item);
  param.user = useAuthStage.currentUser;
  param.fileName = `${form.fileName || form.taskName}.xlsx`;
  param.realMetric = param.realMetric.map((item: any) => item.key);
  param.recipients = form.recipients.join(',');
  if (!creativeDashboard.showSwiper) {
    param.fileHeader = form.fileHeader;
    param.showTop = creativeDashboard.form.top;
    param.topGroup = creativeDashboard.form.groupby.concat(!creativeDashboard.showSwiper ? creativeDashboard.filterAssetName ? [] : ['asset_name'] : []);
    param.otherGroup = !creativeDashboard.showSwiper
      ? ([...(creativeDashboard.filterAssetName ? [] : WEEKLY_TOP_ATTRIBUTE), 'asset_type']
        .concat(checkTypeNoImgOrVideo(creativeDashboard.form.asset_type)
          ? []
          : (creativeDashboard.filterAssetName ? [] : WEEKLY_TOP_CUSTOM_ATTRIBUTE))
      )
      : [];
  }
  param.versionType = props.module;
  param.taskName = form.taskName;
  param.context = form.context;
  param.dtstattimePresets = creativeDashboard.dtstattimePresets;
  delete param.otherMetric;
  const { env } = useEnv();

  const path = '/creative/pivot/asyncDownload';
  const urlMap: any = {
    dev: `http://localhost:9000/api_v2${path}`,
    test: `${path}`,
    prerelease: `${path}`,
    exp: `${path}`,
    production: `${path}`,
  };

  return {
    methods: 'POST',
    url: urlMap[env.value],
    param: JSON.stringify(param),
    game: gameStore.gameCode,
  };
});

const downloadClick = (item: DropdownOption) => {
  if (item.value === 'schedule') {
    taskRef.value?.show();
  } else {
    if (props.module === 'creative_top_report') {
      Object.keys(initFileMessage).forEach((key) => {
        const tempKey = key as keyof typeof initFileMessage;
        fileMessageForm.value[tempKey] = initFileMessage[tempKey];
      });
      downloadRef.value.show();
    } else {
      downloadHandler(item);
    }
  }
};

const fileMessageConfirm = () => {
  formRef.value.submit();
};

const closeFileMessage = () => {
  downloadRef.value.hide();
};

const onSubmit = (context: SubmitContext<FormData>) => {
  if (context.validateResult === true) {
    const fileName = `${fileMessageForm.value.fileName}.xlsx`;
    downloadHandler({
      type: 'realTime',
    }, fileName);
    closeFileMessage();
  }
};

const downloadHandler = (item: DropdownOption, fileName?: string) => {
  const otherFileName = fileName || `${creativeDashboard.form.dtstattime.map(item => item.split(' ')[0]).join('-')}.${item.value}`;
  if (item.type === 'realTime') {
    downloadLoading.value = true;
    creativeDashboard.downloadHandler().then((data: any) => {
      const header: IDownLoadOpt['header'] = {};
      creativeDashboard.form.groupby.forEach((key) => {
        header[key] = creativeDashboard.attribute.find(item => item.colKey === key)?.label || key;
      });
      if (!creativeDashboard.showSwiper && !creativeDashboard.filterAssetName) {
        WEEKLY_TOP_ATTRIBUTE
          .concat(checkTypeNoImgOrVideo(creativeDashboard.form.asset_type) ? [] : WEEKLY_TOP_CUSTOM_ATTRIBUTE)
          .forEach((key) => {
            header[key] = creativeDashboard.attribute.find(item => item.colKey === key)?.label || key;
          });
      }
      creativeDashboard.form.metric.forEach((key) => {
        header[key] = creativeDashboard.metricList.find((item: MetricItemType) => item.colKey === key)?.title || key;
      });

      const newData = data.map((item: any) => {
        const temp = item;
        if (!creativeDashboard.showSwiper) {
          temp.preview = linkToPreview(temp.asset_type, temp.asset_url);
        }
        return temp;
      });

      const showMetric = creativeDashboard.tableAllColumns.map(item => item.key);
      // eslint-disable-next-line no-restricted-syntax
      for (const key in header) {
        if (!showMetric.includes(key)) delete header[key];
      }

      const { isLoading } = creativeDashboard.showSwiper ? useDownloadFile(data, otherFileName, {
        header,
      }) : useDownloadFile(newData, otherFileName, {
        header,
        lock: true,
        imgColumn: ['preview'],
        needMergeKey: creativeDashboard.form.groupby,
        isLink: ['asset_url'],
        headerString: initFileMessage.fileHeader,
      }, 'excel');
      const loadingWatch = watch(
        () => isLoading.value,
        (value) => {
          downloadLoading.value = value;
          if (!value) {
            loadingWatch();
          }
        },
        {
          deep: true,
        },
      );
    })
      .catch((e) => {
        console.error(e);
        downloadLoading.value = false;
      });
  } else {
    creativeDashboard.asyncDownloadHandler({
      user: useAuthStage.currentUser,
      fileName: otherFileName,
    });
  }
};


const download = () => {
  emit('download', {});
};

</script>
