<template>
  <div class="flex items-center">
    <p>{{ props.title }}</p>
    <DateRangePicker
      v-bind="attrs"
      :presets="(presets as any)"
      @update:date="dataChangeHandler"
    />
  </div>
</template>
<script setup lang="ts">
import { FORMAT } from '@/store/creative/dashboard/dashboard.const';
import DateRangePicker from 'common/components/DateRangePicker';
import dayjs from 'dayjs';
import { computed, useAttrs } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  maxDate: {
    type: String,
    default: '',
  },
});

const attrs = useAttrs();
const emit = defineEmits(['update:modelValue']);


interface IPresets {
  'Last 7 Days'?: string[];
  'Last 30 Days'?: string[];
  'Last 90 Days'?: string[];
  'Last 180 Days'?: string[];
  'Last 365 Days'?: string[];
}
const presets = computed<IPresets>(() => {
  const maxDate = props.maxDate || new Date();
  const secondData = dayjs(maxDate).format(FORMAT);
  return {
    'Last 7 Days': [dayjs(maxDate).subtract(6, 'day')
      .format(FORMAT), secondData],
    'Last 14 Days': [dayjs(maxDate).subtract(7, 'day')
      .format(FORMAT), secondData],
    'Last 30 Days': [dayjs(maxDate).subtract(29, 'day')
      .format(FORMAT), secondData],
    'Last 90 Days': [dayjs(maxDate).subtract(89, 'day')
      .format(FORMAT), secondData],
    'Last 180 Days': [dayjs(maxDate).subtract(179, 'day')
      .format(FORMAT), secondData],
    'Last 365 Days': [dayjs(maxDate).subtract(364, 'day')
      .format(FORMAT), secondData],
  };
});

const dataChangeHandler = (value: string[]) => {
  if (value) {
    emit('update:modelValue', value);
  }
};
</script>
<style lang="scss" scoped>
:deep(.t-range-input) {
  @apply h-[36px];
  .t-input {
    height: 100%;
  }
}
:deep(.t-form__controls-content) {
  div[multiple='true'] {
    div {
      @apply border-r-0;
    }
  }
}
:deep(.t-range-input) {
  @apply rounded-r-none;
}
</style>
