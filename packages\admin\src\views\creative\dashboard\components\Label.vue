<template>
  <div class="flex">
    <t-dropdown
      v-if="attrs.showType"
      :options="attrs.typeOptions"
      @click="clickHandler"
    >
      <t-button variant="text" class="border-solid border bg-white-primary border-gray-placeholder border-r-0 ">
        <div class="flex items-center">
          <span class="tdesign-demo-dropdown__text w-[45px]">
            {{ text }}
          </span>
          <t-icon name="chevron-down" />
        </div>
      </t-button>
    </t-dropdown>
    <InputCascader
      ref="country"
      v-bind="attrs"
      :title="title"
      :labels="props.labels"
      :button="button"
      :show-button-value-all="true"
      @update:model-value="(list: any) => emit('update:modelValue', list)"
    />
  </div>
</template>
<script setup lang="ts">
import InputCascader from 'common/components/InputCascader';
import { computed, ref, useAttrs, watch } from 'vue';
import { DropdownOption } from 'tdesign-vue-next';
import { flatten, uniq } from 'lodash-es';

const emit = defineEmits(['change:type', 'update:modelValue', 'outReset']);

const props = defineProps({
  title: {
    type: String,
    default: 'Label',
  },
  labels: {
    type: Array<string>,
    default: () => (['Label', 'First Label', 'Second Label']),
  },
  outReset: {
    type: Boolean,
    default: false,
  },
  onOutReset: {
    type: Function,
    default: () => {},
  },
  buttonDetail: {
    type: Boolean,
    default: false,
  },
});

const attrs = useAttrs();
const label = ref<InstanceType<typeof InputCascader> | null>(null);

const text = computed(() => (attrs.showType ? (attrs.typeOptions as {
  content: string, value: string
}[]).find(item => item.value === attrs.type)?.content : ''));

const clickHandler = (value: DropdownOption) => {
  emit('change:type', value.value);
  (attrs.onChangeType as Function)(value.value);
};

const button = (value: string[], options: string[]) => {
  if (value.length === 0 || options.every(key => value.includes(key))) {
    return 'All';
  }
  const newValue = uniq(flatten(value.map((item) => {
    const list = item.split('%-%');
    if (item.includes('%-%') && item.split('%-%').length === 3) {
      return list[list.length - 1];
    }
    const secondItem = (attrs as any).options[0].children.find((cItem: any) => cItem.label === list[list.length - 1]);
    if (secondItem) {
      return secondItem.children.map((item: any) => item.label);
    }
    return item;
  })));
  return newValue.length === 1 ? newValue[0] : props.buttonDetail ? newValue.join(',') : newValue.length;
};

watch(
  [() => props.outReset, () => attrs.modelValue],
  ([outReset, modelValue]) => {
    if (outReset) {
      label.value?.updateValue((modelValue || attrs.modelValue) as string[], 'all');
      emit('outReset', false);
      props.onOutReset(false);
    }
  },
);
</script>
