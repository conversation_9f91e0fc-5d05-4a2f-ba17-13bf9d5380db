<template>
  <t-tooltip
    :content="props.tips || props.title"
    placement="top"
  >
    <p
      class="cursor-pointer truncate inline-block w-full"
      :class="props.class"
      @dblclick="dblClick"
    >
      {{ props.title }}
    </p>
  </t-tooltip>
</template>
<script setup lang="ts">
import { MessagePlugin } from 'tdesign-vue-next';
import { useClipboard } from '@vueuse/core';

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  tips: {
    type: String,
    default: '',
  },
  class: {
    type: String,
    default: '',
  },
});

const { copy } = useClipboard();
const dblClick = () => {
  copy(props.title);
  MessagePlugin.success('已复制');
};
</script>
