<template>
  <div
    class="w-[357px] bg-white-primary rounded-default px-[8px] py-[6px]"
  >
    <t-space
      direction="vertical"
      :size="8"
      class="w-[100%]"
    >
      <t-list>
        <t-list-item
          v-for="item in sortOptions"
          :key="item.value"
          class="p-[0] mb-[8px] rounded-[100px] hover:bg-brand hover:text-white-primary"
          :class="{
            'bg-brand text-white-primary':
              props.orderby.find(order => order.by === props.colKey && order.order === item.value)
          }"
          @click="clickSort(item)"
        >
          <div
            class="flex w-[100%]  items-center cursor-pointer px-[8px]"
          >
            <svg-icon class="mr-[20px]" :name="item.icon" />
            <p>{{ item.content }}</p>
          </div>
        </t-list-item>
      </t-list>
      <template #separator>
        <t-divider class="m-[0px]" />
      </template>
      <t-list>
        <t-list-item
          v-for="(item, index) in filtersList"
          :key="`${item.condition}${index}`"
          class="px-[0px]"
        >
          <div class="flex items-center">
            <t-select
              v-model="item.condition"
              class="w-[97px] mr-[8px] h-[38px]"
              :options="SYMBOL_OPTIONS"
            >
              <template #suffixIcon>
                <div
                  class="w-[24px] h-[24px] rounded-default bg-white-primary flex items-center justify-center"
                >
                  <svg-icon name="arrow" size="8px" />
                </div>
              </template>
            </t-select>
            <t-input
              v-model="item.value"
              type="number"
              class="w-[103px] h-[38px] rounded-default"
              placeholder="0-100000"
              :suffix="props.format === 'percent' ? '%' : ''"
            />
            <p
              v-if="index !== 0 || filtersList.length > 1"
              class="ml-[12px] cursor-pointer"
              @click="deleteHandler(index)"
            >
              {{ t('delete') }}
            </p>
            <t-button
              class="w-[57px] h-[38px] rounded-extraLarge ml-[12px]"
              @click="addFilterItem"
            >
              {{ t('add') }}
            </t-button>
          </div>
        </t-list-item>
      </t-list>
    </t-space>
  </div>
</template>
<script setup lang="ts">
import SvgIcon from 'common/components/SvgIcon';
import { PropType, ref, watch } from 'vue';
import { useI18n } from 'common/compose/i18n';
import { I18N_BASE } from 'common/const/i18n';
import { OrderByType } from '@/store/creative/dashboard/dashboard';
import { FilterItem } from './index.d';
import { cloneDeep, multiply } from 'lodash-es';
import { SYMBOL_OPTIONS } from '../const';
const emit = defineEmits(['sort']);

const props = defineProps({
  colKey: {
    type: String,
    required: true,
    default: '',
  },
  format: { // 数据类型
    type: String,
    required: true,
    default: '',
  },
  orderby: {
    type: Array as PropType<OrderByType[]>,
    default: () => ([]),
  },
  filters: {
    type: Array as PropType<FilterItem[]>,
    default: () => ([]),
  },
});
interface SortItem {
  content: string,
  value: string,
  icon: string,
}
const sortOptions: SortItem[] = [
  {
    content: 'Sort Acending',
    value: 'asc',
    icon: 'asc-sort',
  },
  {
    content: 'Sort Descending',
    value: 'desc',
    icon: 'desc-sort',
  },
];

const { t } = useI18n([I18N_BASE]);
const filtersList = ref<FilterItem[]>([]);
const addFilterItem = () => {
  filtersList.value.push({
    condition: 1,
    value: '',
  });
};
watch(
  () => props.filters,
  (value) => {
    if (value.length === 0) {
      addFilterItem();
    } else {
      filtersList.value = value.map(item => ({
        ...item,
        value: props.format === 'percent' ? multiply(Number(item.value), 100) : Number(item.value),
      }));
    }
  }, {
    deep: true,
    immediate: true,
  },
);

defineExpose({
  filtersList,
});

const clickSort = (item: SortItem) => {
  emit('sort', {
    colKey: props.colKey,
    value: item.value,
  });
};

const deleteHandler = (index: number) => {
  filtersList.value = cloneDeep(filtersList.value).filter((_item, fIndex) => fIndex !== index);
};

</script>
