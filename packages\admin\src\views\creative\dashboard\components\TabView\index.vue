<template>
  <div>
    <view-tab
      v-bind="attrs"
      :list="defaultTabList"
      :show-num="SHOWNUM"
      :show-more-text="true"
      :dropdown-options="{
        minColumnWidth: 234,
        maxHeight: 250
      }"
      :suffix="suffix"
      :fixed-tab-list="defaultTab"
      @delete-view="deleteViewHandler"
      @edit="editHandler"
      @add="addHandler"
    >
      <template #fixedTab="fixedTabProps: any">
        <t-space>
          <template
            v-for="(item, index) in defaultTab"
            :key="item.value"
          >
            <!-- Tab项 -->
            <div
              class="tab-operate relative"
              :class="{'no-after-line': customTabList.length <= SHOWNUM && index === defaultTab.length - 1}"
            >
              <t-tooltip
                placement="bottom"
                :content="item.label"
              >
                <span
                  class="cursor-pointer truncate max-w-[200px] label-item mr-[18px]"
                  :class="{'text-brand': attrs.modelValue === item.value}"
                  @click="fixedTabProps.slotProps.clickTabHandler(item)"
                >{{ item.label }}</span>
              </t-tooltip>
            </div>
          </template>
        </t-space>
      </template>
      <template #dropdownSlot="tProps: any">
        <t-dropdown-menu>
          <template
            v-for="item in tProps.slotProps.renderList"
            :key="item.value"
          >
            <t-dropdown-item
              class="dropdown-item py-[5px] h-[40px] px-0"
              :value="item.value"
            >
              <div class="flex items-center justify-between">
                <t-tooltip
                  placement="bottom"
                  :content="item.label + suffix(item)"
                >
                  <p class="w-[150px] truncate">
                    {{ item.label }} {{ suffix(item) }}
                  </p>
                </t-tooltip>
                <div
                  v-if="item.type === 'custom'"
                  class="flex p-[6px]"
                >
                  <div
                    class="p-[6px] hover-icon"
                    @click.stop="tProps.slotProps.operationDropdownClickHandler({value: 'edit'}, {}, item.value)"
                  >
                    <svg-icon
                      class="action-box"
                      name="pencil"
                      size="16px"
                    />
                  </div>
                  <div
                    class="p-[6px] hover-icon ml-[10px]"
                    @click.stop="tProps.slotProps.operationDropdownClickHandler({value: 'delete'}, {}, item.value)"
                  >
                    <svg-icon
                      class="action-box"
                      name="delete"
                      size="16px"
                    />
                  </div>
                </div>
              </div>
            </t-dropdown-item>
          </template>
        </t-dropdown-menu>
      </template>
      <template #action>
        <div class="flex">
          <Download
            v-if="props.showDownload"
            :show-type="props.downloadShowType"
            :module="props.downloadModule"
            :game="gameStore.gameCode"
            :download-ing="downloadIng"
            @download="download"
          />
          <t-tooltip
            v-if="props.showCrontab"
            class="ml-[20px]"
            placement="bottom"
          >
            <template #content>
              {{ t('crontab') }}
            </template>
            <div>
              <SvgIcon
                class="cursor-pointer"
                name="time-cycle"
                size="14px"
                color="var(--aix-text-color-black-secondary)"
              />
            </div>
          </t-tooltip>
        </div>
      </template>
    </view-tab>
  </div>
</template>
<script setup lang="ts">
import type { ViewItem } from './type';
import { computed, useAttrs } from 'vue';
import SvgIcon from 'common/components/SvgIcon';
import ViewTab, { IViewFormData } from 'common/components/ViewTab';
import cloneDeep from 'lodash-es/cloneDeep';
import { useI18n } from 'common/compose/i18n';
import { I18N_BASE } from 'common/const/i18n';
import Download from '../Download.vue';
import { useGlobalGameStore } from '@/store/global/game.store';

const { t } = useI18n([I18N_BASE]);
const props = defineProps({
  list: {
    type: Array<ViewItem>,
    required: true,
    default: [],
  },
  showDownload: {
    type: Boolean,
    default: false,
  },
  showCrontab: {
    type: Boolean,
    default: false,
  },
  downloadIng: {
    type: Boolean,
    default: false,
  },
  downloadShowType: {
    type: String,
    default: '',
  },
  downloadModule: {
    type: String,
    default: '',
  },
});

const SHOWNUM = 3;
const emit = defineEmits(['delete', 'update', 'add', 'update:value', 'download']);

const attrs = useAttrs();
const gameStore = useGlobalGameStore();

// 默认视图最多展示3个
const defaultTab = computed(() => props.list.filter(item => item.type === 'default').slice(0, 3));
// custom + default(排除掉前三个)
const defaultTabList = computed(() => props.list
  .filter(item => !defaultTab.value.find(view => view.value === item.value)));

const customTabList = computed(() => defaultTabList.value.slice(3));

const editHandler = (data: IViewFormData) => {
  const item = cloneDeep(props.list.find(item => item.value === data.viewValue)) as ViewItem;
  item.label = data.viewName;
  item.game = data.viewType;
  emit('update', item);
};

const addHandler = (data: IViewFormData) => {
  const item = cloneDeep(props.list.find(item => item.value === attrs.modelValue)) as ViewItem;
  item.label = data.viewName;
  item.game = data.viewType || item.game;
  emit('add', item);
};
const deleteViewHandler = (value: string) => {
  const item = cloneDeep(props.list.find(item => item.value === value)) as ViewItem;
  emit('delete', item);
};

const suffix = (item: ViewItem) => (item.game === 'allgame' ? '(all)' : '');

const download = () => {
  emit('download', '');
};

</script>
<style scoped lang="scss">
.dropdown-item {
  &:hover {
    .action-box {
      display: block;
    }
  }
}
.action-box {
  display: none;
}
.hover-icon {
  &:hover {
    .svg-icon {
      @apply fill-brand;
    }
  }
}
.tab-operate {
  &:hover {
    .tab-operate-icon {
      display: block;
    }
  }
  &::after {
    content: '|';
    position: relative;
    right: 0;
    bottom: 1px;
    height: 100%;
  }
}
.no-after-line {
  &::after {
    content: '';
    position: relative;
    right: 0;
    bottom: 1px;
    height: 100%;
  }
}
.tab-operate-icon {
  display: none;
  position: absolute;
  right: 6px;
  writing-mode: tb;
  cursor: pointer;
  line-height: 6px;
  height: 15px;
  bottom: 7px;
}
</style>
