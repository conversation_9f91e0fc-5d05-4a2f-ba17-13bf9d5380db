<template>
  <t-popup
    :visible="visible && props.showPanel"
    trigger="click"
    @visible-change="visibleChange"
  >
    <template #content>
      <sort-filter-panel ref="sortFilterPanel" v-bind="attrs" @sort="emitSort" />
    </template>
    <div class="flex items-center">
      <PopupText :title="(attrs.label as string)" :tips="attrs.tips as string" class="mr-[6px] truncate w-auto" />
      <p>
        <svg-icon
          :class="{'cursor-pointer': !props.showPanel}"
          :color="
            (attrs.orderby as OrderByType[]).find(order => order.by === attrs.colKey && order.order === 'asc') ?
              'var(--aix-text-color-link)' :
              'var(--aix-text-color-black-primary)'"
          name="arrow"
          size="8"
          style="transform: rotateX(180deg)"
          @click="sortHandler('asc')"
        />
        <svg-icon
          :class="{'cursor-pointer': !props.showPanel}"
          :color="
            (attrs.orderby as OrderByType[]).find(order => order.by === attrs.colKey && order.order === 'desc') ?
              'var(--aix-text-color-link)' :
              'var(--aix-text-color-black-primary)'"
          name="arrow"
          size="8"
          @click="sortHandler('desc')"
        />
      </p>
      <t-tooltip
        v-if="attrs.label === 'Asset Name'"
        content="Double click asset name to copy the full name"
        class="ml-[8px]"
      >
        <t-icon name="info-circle" />
      </t-tooltip>
    </div>
  </t-popup>
</template>
<script setup lang="ts">
import { ref, useAttrs } from 'vue';
import SortFilterPanel from './SortFilterPanel.vue';
import SvgIcon from 'common/components/SvgIcon';
import { OrderByType } from '@/store/creative/dashboard/dashboard';
import { FilterItem } from './index.d';
import PopupText from './PopupText.vue';

const visible = ref(false);
const emit = defineEmits(['submit', 'orderBy']);
const props = defineProps({
  showPanel: {
    type: Boolean,
    default: true,
  },
});
const attrs = useAttrs();
const sortFilterPanel = ref<InstanceType<typeof SortFilterPanel> | null>(null);
const visibleChange = (visibleRes: boolean) => {
  if (!visibleRes && sortFilterPanel?.value?.filtersList) {
    const list: FilterItem[] = sortFilterPanel?.value?.filtersList
      .filter((item: any) => item.value && item.value !== 0);
    if (JSON.stringify(attrs.filters) !== JSON.stringify(list)) {
      emit('submit', list.map(item => ({
        ...item,
        value: attrs.format === 'percent' ? Number(item.value) / 100 : Number(item.value),
      })));
    }
  }
  visible.value = visibleRes;
};
const emitSort = (item: {
  colKey: string,
  value: string,
}) => {
  visible.value = false;
  emit('orderBy', item);
};
const sortHandler = (type: string) => {
  if (!props.showPanel) {
    emitSort({
      colKey: attrs.colKey as string,
      value: type,
    });
  }
};
</script>
