<template>
  <common-view
    title="Creative Performance Dashboard"
    :form-props="{
      modelValue: creativeDashboard.form,
      formList: creativeDashboard.schema,
      onSubmit: creativeDashboard.onFormSubmit,
      onClose,
      'onUpdate:modelValue': modelVal<PERSON><PERSON><PERSON><PERSON>,
      onReset: creativeDashboard.onResetHandler,
      confirmDisabled: creativeDashboard.configLoading,
      resetDisabled: creativeDashboard.configLoading,
    }"
  >
    <template #subTitle>
      <sub-title
        :game="gameStore.gameCode"
      />
    </template>
    <template #views>
      <total-chart v-if="creativeDashboard.showSwiper" />
      <Table />
    </template>
  </common-view>
</template>
<script setup lang="ts">
import SubTitle from './SubTitle.vue';
import TotalChart from './TotalChart.vue';
import Table from './Table.vue';
import { useCreativeDashboardStore, creativeStoreHandler } from '@/store/creative/dashboard/dashboard.store';
import { useGlobalGameStore } from '@/store/global/game.store';
import CommonView from 'common/components/Layout/CommonView.vue';
import { IFormDynamicItem, IFormItem } from 'common/components/FormContainer';
import { useWatchGameChange } from 'common/compose/request/game';
import { watch } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

const gameStore = useGlobalGameStore();
const creativeDashboard = useCreativeDashboardStore();
useWatchGameChange(async () => {
  creativeDashboard.init();
});

watch(
  () => route.meta.name,
  (value) => {
    if (value) {
      const newState = creativeStoreHandler(value);
      console.log(creativeDashboard);
      console.log(newState);
      Object.keys(newState).forEach((key) => {
        // @ts-ignore
        creativeDashboard[key as keyof typeof creativeDashboard] = newState[key as keyof typeof newState];
      });
    }
  }, {
    deep: true,
  },
);


const onClose = (item: IFormItem | IFormDynamicItem) => {
  const value = (creativeDashboard.form[item.ext.key as keyof typeof creativeDashboard.form] as any);
  (creativeDashboard.form[item.ext.key as keyof typeof creativeDashboard.form] as any) = Array.isArray(value) ? [] : '';
  // label需要单独处理
  if (item.ext.key === 'all_label') {
    creativeDashboard.form.label = [];
    creativeDashboard.form.label_name = [];
  }
  if (item.ext.key === 'country_code') {
    creativeDashboard.form.region = [];
  }
  if (item.ext.key === 'campaign_name') {
    creativeDashboard.form.campaign_name = {
      type: 'in',
      value: [],
    } as any;
  }
};
const modelValueHandler = (value: typeof creativeDashboard.form) => {
  creativeDashboard.form = {
    ...creativeDashboard.form,
    ...value,
  };
};

</script>
