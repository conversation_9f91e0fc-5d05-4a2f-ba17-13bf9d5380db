import { MetricItemType } from '@/store/creative/dashboard/dashboard';
import dayjs, { Dayjs } from 'dayjs';
import { uniq } from 'lodash-es';

export const groupByType = (list: MetricItemType[]) => {
  const groupList = uniq(list.map(item => item.type)).map(key => ({
    groupName: key,
    list: [] as MetricItemType[],
  }));
  list.forEach((item) => {
    const group = groupList.find(groupItem => groupItem.groupName === item.type);
    group?.list.push(item);
  });
  return groupList;
};

export const getMergeConfig = (groupby: string[], table: any[], pageIndex: number) => {
  let preMergeValue = ''; // 储存上一个的合并value值
  let sign = '';
  const groupbyNum: any = {};
  return groupby.map((attr, columnIndex) => {
    table.forEach((item, recordIndex) => {
      if (recordIndex === 0 && pageIndex === 0) return;
      const fun = (index: number, one: any) => new Array(index)
        .fill('')
        .map((_, index) => one?.[groupby[index]])
        .join('_');
      let value = fun(columnIndex + 1, item);
      // 假如说数据是这样排列的
      // 1：organic Asia 20220824
      // 2：organic Asia 20220825
      // 3：organic Asia 20220824
      // 按照原先的逻辑会把1和3计数到一起，但是中间有个不一样的值，需要重新计算3和下面一行的值是否可以合并
      // 所以给每一行都加了唯一标识符，但是计算前会去掉标识符把上一个值和当前的值进行比较再看要不要合并。
      const joinFlag = `^${new Date().getTime()}${recordIndex}^`;
      const preValue = preMergeValue.includes(sign)
        ? preMergeValue.split(sign)[0]
        : preMergeValue;
      sign = value !== preValue ? joinFlag : sign;
      value = value !== preValue ? `${value}${joinFlag}` : preMergeValue;
      if (groupbyNum[attr] === undefined) {
        groupbyNum[attr] = {};
      }
      if (groupbyNum[attr][value] === undefined) {
        groupbyNum[attr][value] = {
          startIndex: recordIndex,
          mergeNum: 0,
        };
      }
      preMergeValue = value;
      groupbyNum[attr][value].mergeNum = groupbyNum[attr][value].mergeNum + 1;
    });
    const cfg: any = { 0: 1 };
    groupbyNum[attr]
      && Object.keys(groupbyNum[attr]).map((value) => {
        const { startIndex, mergeNum } = groupbyNum[attr][value];
        return (cfg[startIndex] = mergeNum);
      });
    return cfg;
  });
};


export const getPresets = (maxDate: Dayjs, minDate: Dayjs, format: string) => ({
  'Last 7 Days': [dayjs(maxDate).subtract(7, 'day')
    .format(format), dayjs(maxDate).format(format)],
  'Last 14 Days': [dayjs(maxDate).subtract(13, 'day')
    .format(format), dayjs(maxDate).format(format)],
  'Last 30 Days': [dayjs(maxDate).subtract(30, 'day')
    .format(format), dayjs(maxDate).format(format)],
  'Last 90 Days': [dayjs(maxDate).subtract(90, 'day')
    .format(format), dayjs(maxDate).format(format)],
  'Last 180 Days': [dayjs(maxDate).subtract(180, 'day')
    .format(format), dayjs(maxDate).format(format)],
  'Last 365 Days': [dayjs(maxDate).subtract(364, 'day')
    .format(format), dayjs(maxDate).format(format)],
  'Last 4 Weeks': [dayjs(maxDate).subtract(4, 'week')
    .format(format), dayjs(maxDate).format(format)],
  'Last 12 Weeks': [dayjs(maxDate).subtract(12, 'week')
    .format(format), dayjs(maxDate).format(format)],
  'Last 24 Weeks': [dayjs(maxDate).subtract(24, 'week')
    .format(format), dayjs(maxDate).format(format)],
  // [maxData当前年第一天,maxData]
  'Year to Date': [dayjs(maxDate).startOf('year')
    .format(format), dayjs(maxDate).format(format)],
  'All of Time': [dayjs(minDate).format(format), dayjs(maxDate).format(format)],
});
