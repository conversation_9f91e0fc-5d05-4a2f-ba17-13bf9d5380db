<template>
  <t-row :gutter="16">
    <Draggable
      :model-value="renderList"
      handle=".handle"
      class="flex-wrap flex w-[100%]"
      item-key="key"
      @start="dragRef = true"
      @end="dragRef = false"
      @update:model-value="updateModelValue"
    >
      <template #item="{element}">
        <t-col
          v-if="element"
          :span="getSpan(element)"
          class="mb-[15px]"
        >
          <div>
            <Card
              :ref="el => card[element.index] = el"
              :form="element"
              :show-filter-details="creativeInsightReport.form.show_filter_details"
              :options="options"
              :schema="reportFilterStore.schema.value"
              @delete="deleteChardItem"
              @edit="editChartItem"
              @duplicate="duplicateItem"
              @export="exportItem($event, element.index)"
              @update-temp-data="updateTempData"
            />
          </div>
        </t-col>
        <t-col
          v-else
          :span="6" class="mb-[15px]"
        >
          <Add
            @add="editChartItem(ChartItem(), 'new')"
          />
        </t-col>
      </template>
    </Draggable>
  </t-row>
  <EditDialog
    ref="editDialog"
    :type="dialogType"
    :form="editForm"
  />
  <BaseDialog
    ref="chooseDownloadType"
    title="Select download type"
    @confirm="downloadHandler"
  >
    <div class="w-[300px] h-[100px]">
      <t-checkbox-group v-model="exportCheckType" :options="DOWNLOAD_TYPE" name="type" />
    </div>
  </BaseDialog>
</template>
<script setup lang="ts">
import Add from './components/add.vue';
import Card from './components/card.vue';
import { useCreativeInsightReportStore } from '@/store/creative/insight/report/index.store';
import { ChartItem, LayoutType, DOWNLOAD_TYPE } from './const';
import { computed, ref, watch } from 'vue';
import Draggable from 'vuedraggable';
import { cloneDeep, max } from 'lodash-es';
import EditDialog from './components/edit-dialog.vue';
import { setFilterSchema } from '@/store/creative/insight/report/schema';
import { useCommonSearch } from '@/compose/useCommonSearch';
import { ChartItemType } from './index.d';
import BaseDialog from 'common/components/Dialog/Base';
import { IDownLoadOpt, useDownloadFile } from 'common/compose/download-file';
import { exportImage } from 'common/utils/export';

// 拖住组件的ref
const dragRef = ref<boolean>(false);
const editDialog = ref<InstanceType<typeof EditDialog> | null>();
const chooseDownloadType = ref<InstanceType<typeof BaseDialog> | null>();
const exportItemValue = ref<any>({});
const exportCheckType = ref<string[]>([]);
const editForm = ref<ChartItemType | undefined>(undefined);
const dialogType = ref('add');
const card = ref<any[]>([]);
const updateCardValue = (index?: number) => {
  if (!index || index === 0) {
    card.value.forEach((item) => {
      item.refreshData();
    });
    return;
  }
  card.value[index as number].refreshData();
};

defineExpose({
  updateCardValue,
});

const creativeInsightReport = useCreativeInsightReportStore();

const options = computed(() => ({
  attribute: creativeInsightReport.attributeOptions,
  metric: creativeInsightReport.metricOptions,
  options: creativeInsightReport.options,
  maxDate: creativeInsightReport.maxDate,
  minDate: creativeInsightReport.minDate,
}));

const renderList = computed(() => creativeInsightReport.form.chartList.concat(''));
const getSpan = (item: ChartItemType) => (item?.layout === LayoutType.Vertical ? 12 : 6);

const updateModelValue = (data: (ChartItemType)[]) => {
  // creativeInsightReport.form.chartList = data.filter(item => item);
  creativeInsightReport.updateChartList(data.filter(item => item));
};

const deleteChardItem = (item: ChartItemType) => {
  creativeInsightReport.deleteChartList(item);
};

const editChartItem = (item: ChartItemType, type = 'edit') => {
  dialogType.value = type;
  editForm.value = cloneDeep(item);
  editDialog?.value?.showDialog();
};

const duplicateItem = (item: ChartItemType) => {
  const initItem = ChartItem();
  const maxIndex: number = max(renderList.value.map((item: ChartItemType) => item.index)) || 0;
  creativeInsightReport.addChart({
    ...item,
    id: initItem.id,
    index: maxIndex + 1,
  });
};

const reportFilterStore = useCommonSearch<any, any>();

const version = ref('daily');
const dtstattimePresets = ref('');
const updateVersion = (value: string) => {
  version.value = value;
};

const updateTempData = (item: ChartItemType) => {
  creativeInsightReport.updateChartListItem(item);
};

const changeDtstattimePresets = (value: string) => {
  dtstattimePresets.value = value;
};

watch(
  () => creativeInsightReport.options,
  (options) => {
    if (JSON.stringify(options) !== '{}') {
      reportFilterStore.setForm(ChartItem().filter);
      setFilterSchema({
        reportFilterStore,
        maxDate: creativeInsightReport.maxDate,
        minDate: creativeInsightReport.minDate,
        dtstattimePresets: dtstattimePresets.value,
        dtstattimeList: creativeInsightReport.dtstattimeList,
        version: version.value,
        options,
        onReset: false,
        updateVersion,
        changeDtstattimePresets,
      });
    }
  }, {
    deep: true,
  },
);

const exportItem = (item: ChartItemType, index: number) => {
  chooseDownloadType.value?.show();
  exportItemValue.value = item;
  exportItemValue.value.index = index;
};

const downloadHandler = () => {
  chooseDownloadType.value?.hide();
  // 为了等待弹窗关闭
  setTimeout(() => {
    exportCheckType.value.forEach((type) => {
      if (type === 'xlsx' || type === 'csv') {
        const header: IDownLoadOpt['header'] = {};
        exportItemValue.value.groupby.concat(exportItemValue.value.metric).forEach((key: string) => {
          header[exportItemValue.value.tempData[0][key]] = exportItemValue.value.tempData[0][key];
        });
        const data = exportItemValue.value.tempData.map((item: any) => {
          const tempItem = cloneDeep(item);
          Object.keys(tempItem).forEach((key: string) => {
            if (!(key in header)) {
              delete tempItem[key];
            }
          });
          return tempItem;
        });
        useDownloadFile(data, `${exportItemValue.value.name}.${type}`, {
          header,
        });
      }
      if (type === 'png') {
        exportImage(card.value[exportItemValue.value.index].card.$el, {
          name: `${exportItemValue.value.name}.${type}`,
          type,
        });
      }
    });
  });
};

</script>
