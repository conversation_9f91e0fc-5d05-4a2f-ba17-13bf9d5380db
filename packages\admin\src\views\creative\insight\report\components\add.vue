<template>
  <div id="add">
    <t-card>
      <div>
        <div class="my-[200px] text-center">
          <SvgIcon
            class="inline bg-gray-disabled p-[10px] rounded-large cursor-pointer"
            name="plus"
            size="50px"
            color="var(--aix-text-color-white-primary)"
            @click="showDialog"
          />
        </div>
      </div>
    </t-card>
  </div>
</template>
<script setup lang="ts">
import SvgIcon from 'common/components/SvgIcon';

const emit = defineEmits(['add']);
const showDialog = () => {
  emit('add');
};
</script>
