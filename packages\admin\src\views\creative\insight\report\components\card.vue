<template>
  <t-card id="card" ref="card">
    <div>
      <EchartTemplate
        v-bind="props.form"
        :chart-type="props.form.type"
        :option="chartData"
        :show-name="false"
        use-type="card"
        :loading="loading"
        :chart-style="{
          height: '400px',
        }"
        :metric-options="props.options.metric"
        :attribute-options="props.options.attribute"
      />
    </div>
    <template #title>
      <div>
        <div class="flex items-center">
          <SvgIcon
            class="mr-[5px] handle cursor-pointer"
            name="handler"
            size="14px"
            color="var(--aix-text-color-black-disabled)"
          />
          <h1>{{ props.form.name }}</h1>
        </div>
        <p class="truncate h-[24px]">{{ props.showFilterDetails ? filterDetail : "" }}</p>
      </div>
    </template>
    <template #actions>
      <t-dropdown>
        <t-icon name="ellipsis" class="cursor-pointer" size="24" />
        <t-dropdown-menu>
          <t-dropdown-item
            v-for="item in actionOptions"
            :key="item.value"
            :value="item.value"
            @click="action(item.value)"
          >
            <div class="flex items-center">
              <SvgIcon
                class="mr-[10px] cursor-pointer"
                :name="item.icon"
                size="14px"
                color="var(--aix-text-color-black-disabled)"
              />
              {{ item.content }}
            </div>
          </t-dropdown-item>
        </t-dropdown-menu>
      </t-dropdown>
    </template>
  </t-card>
</template>
<script setup lang="ts">
import { ChartItem } from '../const';
import EchartTemplate from './echart-template.vue';
import { getChartData } from 'common/service/creative/insight/get-chart-data';
import { useCardForm } from '../compose/useCardForm';
import SvgIcon from 'common/components/SvgIcon';
import { cloneDeep } from 'lodash-es';
import { connectAllLabel, getCreativePivotGetTableParam } from '@/store/creative/dashboard/utils';
import { computed, ref, watch } from 'vue';
import { IFormDynamicItem, IFormItem } from 'common/components/FormContainer';
import { OptionsItem } from 'common/components/Cascader';
import dayjs from 'dayjs';
import { unRefObj } from 'common/utils/reactive';
import { Card } from 'tdesign-vue-next';
import { useGlobalGameStore } from '@/store/global/game.store';
import { ISearchValue } from 'common/components/SearchBox';
import { getPresets } from '@/views/creative/dashboard/utils';

const props = defineProps({
  form: {
    type: Object,
    default: () => ChartItem,
  },
  showFilterDetails: {
    type: Boolean,
    default: false,
  },
  options: {
    type: Object,
    default: () => ({
      attribute: [],
      metric: [],
      options: [],
      maxDate: '',
      minDate: '',
    }),
  },
  schema: {
    type: Array<IFormItem | IFormDynamicItem>,
    default: () => [],
  },
});
const gameStore = useGlobalGameStore();
const card = ref<InstanceType<typeof Card> | null>();
const filterDetail = computed(() => {
  if (props.showFilterDetails) {
    const showFilterKey = Object.keys(props.form.filter).filter((key: string) => key !== 'chartList' && !props.form.globalFilterKey.includes(key));
    // 通过schema拿到对应的label
    const showFilter = props.schema.filter(formItem => showFilterKey.includes(formItem.ext.key))
      .map((formItem) => {
        let detail = '';
        if (formItem.ext.key in props.options.options) {
          detail = props.options.options[formItem.ext.key]
            .filter((item: OptionsItem) => props.form.filter[formItem.ext.key].includes(item.value))
            .map((item: OptionsItem) => item.label)
            .join('&');
        }
        if (formItem.ext.key === 'dtstattime') {
          detail = props.form.filter[formItem.ext.key].map((value: string) => dayjs(value).format('YYYY-MM-DD')).join('&');
        }
        return `${formItem.ext.label}:${detail}`;
      });
    return showFilter.join(',');
  }
  return '';
});


const {
  form,
  chartData,
  emit: emitData,
  setForm,
  setTempChartData,
  loading,
} = useCardForm({
  form: props.form,
  func: getChartData,
  options: props.options.options,
  game: gameStore.gameCode,
  tempData: props.form.tempData,
});
const emit = defineEmits(['delete', 'edit', 'duplicate', 'export', 'updateTempData']);

watch(
  () => props.form,
  (tempForm) => {
    const temp = cloneDeep(tempForm);
    delete temp.tempData;
    setForm(temp);
    setTempChartData(tempForm.tempData);
    initTempData();
  }, {
    deep: true,
  },
);


watch(
  () => chartData.value,
  (value) => {
    if (value.length > 0 && JSON.stringify(value) !== JSON.stringify(props.form.tempData)) {
      emit('updateTempData', {
        ...props.form,
        tempData: value,
      });
    }
  }, {
    deep: true,
  },
);


const presets = computed(() => getPresets(
  dayjs(props.options.maxDate),
  dayjs(props.options.minDate),
  'YYYYMMDD',
));

// const emitThrottle = throttle(emitData, 3000, { trailing: true, leading: false });

const refreshData = (throttle = true) => {
  const { filter } = unRefObj(form.value);
  filter.string_search?.forEach((item: ISearchValue) => {
    filter[item.field as keyof typeof filter] = cloneDeep(item.condition);
  });

  if (filter.dtstattimePresets) {
    filter.dtstattime = presets.value[filter.dtstattimePresets as keyof typeof presets.value];
  }

  delete filter.chartList;
  delete filter.string_search;
  delete filter.dtstattimePresets;
  delete filter.version;
  // 处理all_label
  const temp = connectAllLabel(filter.all_label);
  const tempFilter = cloneDeep(filter);
  delete tempFilter.all_label;
  const param = {
    ...unRefObj(form.value),
    filter,
    where: getCreativePivotGetTableParam({
      form: {
        ...tempFilter,
        ...temp,
      },
      date: {
        maxDate: props.options.maxDate,
      },
      metric: form.value.metric,
      options: props.options.options,
    }),
  };
  const options = {
    attribute: props.options.attribute,
    metric: props.options.metric,
    country_code: props.options.options.country_code,
  };
  if (throttle) {
    emitData(param, options);
    return;
  }
  emitData(param, options);
};

const initTempData = () => {
  const strTempData = JSON.stringify(props?.form?.tempData || {});
  if (strTempData === '{}' || strTempData === '[]') {
    // 如果在dialog里没有生成图表的option 此时需要重新获取一下数据
    // 在加载视图时获取对应option数据
    refreshData(false);
  }
};
initTempData();


defineExpose({
  refreshData,
  card,
});


const actionOptions = [
  { content: 'Edit', value: 'edit', icon: 'edit' },
  { content: 'Delete', value: 'delete', icon: 'delete' },
  { content: 'Duplicate', value: 'duplicate', icon: 'double-check' },
  { content: 'Export', value: 'export', icon: 'download' },
];

const action = (type: string) => {
  emit(type as 'delete' | 'edit' | 'duplicate' | 'export', props.form);
};
</script>
<style lang="scss" scoped>
:deep(.t-card__title) {
  display: block;
}

:deep(.t-card__header) {
  padding-bottom: 0;
}
:deep(.t-card__body) {
  padding-top: 0;
}
</style>
