<template>
  <div>
    <h3 v-if="showName">Chart Name:{{ props.name }}</h3>
    <div
      class="h-[400px]"
    >
      <img
        v-if="showInitDefaultImg && !props.loading"
        :src="ChartDefaultImg[showChartType as keyof typeof ChartDefaultImg] || ChartDefaultImg.bar"
      >
      <div v-else>
        <div
          v-if="props.chartType !== ChartTypeEnum.Form"
        >
          <template v-if="props.loading || showEmpty">
            <div
              v-if="props.loading"
              class="relative"
              :style="props.chartStyle"
            >
              <FullLoading size="small" />
            </div>
            <div
              v-else-if="showEmpty"
              class="flex justify-center items-center"
              :style="props.chartStyle"
            >
              <DataEmpty />
            </div>
          </template>
          <div v-else>
            <BasicChart
              v-if="showChartType !== ChartTypeEnum.Bar_Line"
              v-bind="format"
              is-show-legend
              :detail-type="props.detailType"
              :data-value-filed="dataValueFiled"
              :data-item-field="realMetricFrom === 'groupby' ? dataGroupItemField : dataItemField"
              :data-group-item-field="realMetricFrom === 'groupby' ? dataItemField : dataGroupItemField"
              :chart-type="realChartType"
              :data="data"
              :chart-style="props.chartStyle"
              :x-axis-name="xAxisName"
              :y-axis-name="yAxisName[0]"
              :y-axis-name-gap="55"
              :legendselectchanged="hideGroupValue"
              :selected="selected"
              :color="props.color"
              :tooltip-filter-zero="true"
            />
            <ComboChart
              v-else
              v-bind="format"
              is-show-legend
              :detail-type="['', '', '']"
              :data-value-filed="dataValueFiled"
              :data-item-field="dataItemField"
              :data-group-item-field="dataGroupItemField"
              :chart-type="['line', 'bar']"
              :data="data"
              :chart-style="props.chartStyle"
              :x-axis-name="xAxisName"
              :y-axis-name="yAxisName"
              :y-axis-name-gap="[55, 55]"
              :legendselectchanged="hideGroupValue"
              :selected="selected"
              :color="props.color"
              :tooltip-filter-zero="true"
            />
          </div>
        </div>
        <t-table
          v-else
          :max-height="300"
          :height="300"
          v-bind="props.option"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ChartDefaultImg, ChartTypeEnum, LayoutType, validatorList } from '../const';
import { computed, PropType, ref } from 'vue';
import BasicChart, { IData } from 'common/components/BasicChart';
import ComboChart from 'common/components/ComboChart';
import type { OptionsItem } from 'common/types/cascader';
import { useRenderFormat } from 'common/compose/table/render-format';
import { MetricItemType } from '../index.d';
import FullLoading from 'common/components/FullLoading';
import { sum } from 'lodash-es';
import { useGlobalGameStore } from '@/store/global/game.store';


const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  isShowEmpty: {
    type: Boolean,
    default: () => false,
  },
  chartStyle: {
    type: Object,
    default: () => Object.assign({}, { height: '100%', width: '100%' }),
  },
  chartType: {
    type: String,
    required: true,
    default: '',
  },
  option: {
    type: Array as PropType<Array<IData>>,
    default: () => [],
  },
  name: {
    type: String,
    default: '',
  },
  showName: {
    type: Boolean,
    default: true,
  },
  metric: {
    type: Array<string>,
    default: () => [],
  },
  groupby: {
    type: Array<string>,
    default: () => [],
  },
  detailType: {
    type: String,
    default: '',
  },
  metricOptions: {
    type: Array<MetricItemType>,
    default: () => [],
  },
  attributeOptions: {
    type: Array<OptionsItem>,
    default: () => [],
  },
  useType: {
    type: String,
    default: '',
  },
  layout: {
    type: String,
    default: '',
  },
  top: {
    type: Number,
    default: 3,
  },
  color: {
    type: Array<String>,
    default: () => [],
  },
});

const metric = computed(() => props.metric.filter(item => item));
const groupby = computed(() => props.groupby.filter(item => item));
const realMetric = computed(() => {
  if (metric.value.every(value => props.metricOptions.find(item => item.value === value))) {
    return metric.value;
  }
  if (groupby.value.every(value => props.metricOptions.find(item => item.value === value))) {
    return groupby.value;
  }
  return [];
});
const realMetricFrom = computed(() => (groupby.value.some(value => realMetric.value.includes(value)) ? 'groupby' : 'metric'));
const realGroup = computed(() => {
  if (metric.value.every(value => props.attributeOptions.find(item => item.value === value))) {
    return metric.value;
  }
  if (groupby.value.every(value => props.attributeOptions.find(item => item.value === value))) {
    return groupby.value;
  }
  return [];
});

const showChartType = computed(() => `${props.chartType}${props.detailType ? `-${props.detailType}` : ''}`);
const realChartType = computed(() => {
  if (props.chartType === 'pie' && realGroup.value.length > 1) {
    return 'sunburst';
  }
  return props.chartType;
});

const dataItemField = computed(() => (realMetric.value.length > 1 || realMetricFrom.value === 'groupby'
  ? 'xList'
  : (props.attributeOptions.find(item => item.value === (realGroup.value[1] || realGroup.value[0]))?.label || ' ')
));
const dataGroupItemField = computed(() => (realGroup.value.length === 1
    && realMetric.value.length === 1 && realMetricFrom.value === 'metric' ? ' '
  : (props.attributeOptions.find(item => item.value === realGroup.value[0])?.label || ' ')));

const dataValueFiled = computed(() => (realMetric.value.length > 1 || realMetricFrom.value === 'groupby' ? 'value'
  : (props.metricOptions.find(item => item.value === realMetric.value[0])?.label || ' ')));


const showInitDefaultImg = computed(() => (JSON.stringify(props.option) === '{}' || JSON.stringify(props.option) === '[]') && (
  realMetric.value.length === 0
    || realGroup.value.length === 0
));
// 所有的metric都为0就展示空图
const showEmpty = computed(() => {
  if (validatorList(realGroup.value) && validatorList(realMetric.value) && !props.loading) {
    if (realChartType.value === 'sunburst') {
      return props.option?.every(item => item.children.every((childrenItem: any) => childrenItem.value === 0));
    }
    return props.option?.every(item => realMetric.value.every(key => item[item[key]] === 0));
  }
  return false;
});

const tooltipValueFormat = (value: number) => {
  const optionsFindOne: any = props.option
    ?.find(item => Object.keys(item).filter(key => item[key] === value).length > 0) || {};

  const formate: any = props.metricOptions
    .find(item => item.label in optionsFindOne && optionsFindOne[item.label] === value)?.format;
  return useRenderFormat({
    format: formate === 'percent' ? formate : 'numShort',
    value,
  });
};

const formatHandler = (value: number, realMetric: any, index: number) => {
  const formate: any = props.metricOptions.find(item => item.value === realMetric[index])?.format;
  return useRenderFormat({
    // 都转化成整数
    format: formate === 'percent' ? formate : 'numShort',
    value,
    opt: 0,
  });
};

const xAxisLabelFormat = (value: string) => {
  if (realGroup.value.includes('asset_name')) {
    return `${value.slice(0, 10)}${value.length > 4 ? '...' : ''}`;
  }
  return `${value.slice(0, 4)}${value.length > 4 ? '...' : ''}`;
};

const formatTooltipName = (value: string) => `${value.slice(0, 20)}${value.length > 4 ? '...' : ''}`;

const xAxisName = computed(() => (realMetricFrom.value === 'groupby' ? ' '
  : (realGroup.value.length === 1 && realMetric.value.length === 1 ? dataItemField.value : dataGroupItemField.value)));
const yAxisName = computed(() => (realMetricFrom.value === 'groupby' ? [' '] : (
  realMetric.value.map(key => props.metricOptions.find(item => item.value === key)?.label || ' ').reverse()
)));


const format = computed(() => {
  const temp: any = {
    tooltipValueFormat,
    yAxisLabelFormat: (value: number) => formatHandler(value, realMetric.value, realMetric.value.length === 2 ? 1 : 0),
    yAxisSecondaryLabelFormat: (value: number) => formatHandler(value, realMetric.value, 0),
    xAxisLabelInterval: props.useType === 'card' && props.layout === LayoutType.Vertical,
  };
  if (props.useType === 'card' && props.layout === LayoutType.Vertical && (
    props.top === 20 || realGroup.value.includes('asset_name')
  )) {
    temp.xAxisLabelFormat = xAxisLabelFormat;
  }
  if (realGroup.value.includes('asset_name')) {
    temp.formatTooltipName = formatTooltipName;
  }
  if (realChartType.value === 'sunburst') {
    // 计算总和
    temp.dataAmountValue = props.option
      .reduce((total: number, item: any) => total + sum(item.children
        .map((children: { value: number; }) => children.value || 0)), 0);
  }

  return temp;
});
const gameStore = useGlobalGameStore();

const selectKeyList = ref<string[]>([]);
const selected = ref({});
const data = computed(() => {
  // 如果选了特殊值需要算百分比的则计算百分比
  if (gameStore.gameCode === 'pubgm') {
    // 如果有百分比的 重新计算百分比
    if (selectKeyList.value.length > 0 && realMetric.value.some(item => item.includes('%'))) {
      const key = realMetricFrom.value !== 'groupby' ? dataItemField.value : dataGroupItemField.value;

      const total = sum(props.option.filter(item => selectKeyList.value
        .includes(item[key])).map(item => item[dataValueFiled.value]));
      const data = props.option.map((item) => {
        const temp = item;
        temp[dataValueFiled.value] = temp[dataValueFiled.value] / total;
        return temp;
      });
      return data;
    }
    return props.option;
  }
  return props.option || [];
});

const hideGroupValue = (event: any) => {
  selectKeyList.value = Object.keys(event.selected).filter(key => event.selected[key]);
  selected.value = event.selected;
};

</script>
