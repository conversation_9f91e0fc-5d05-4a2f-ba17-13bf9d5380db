<template>
  <BaseDialog
    ref="chartFormRef"
    :title="t('editChart')"
    :confirm-text="t('addToReport')"
    @confirm="addReport()"
    @close="cancel()"
  >
    <div class="flex justify-between">
      <t-form
        ref="formRef"
        class="mt-[18px] mb-[4px] max-w-[650px]"
        :data="form"
        :rules="FORM_RULES"
        :required-mark="false"
        @submit="onSubmit"
      >
        <t-form-item label="Chart name" name="name">
          <Input v-model="form.name" class="w-[500px]" placeholder="placeholder" />
        </t-form-item>
        <t-form-item label="Chart type" name="type">
          <t-radio-group :value="chartType" @change="updateFormType">
            <t-radio-button
              v-for="item in CHART_LIST"
              :key="item"
              :value="item"
            >
              <SvgIcon
                :name="ChartIcon[item as keyof typeof ChartIcon]"
                color="var(--aix-text-color-black-disabled)"
                size="25px"
              />
            </t-radio-button>
          </t-radio-group>
        </t-form-item>
        <t-form-item label="Chart layout" name="layout">
          <t-radio-group v-model="form.layout">
            <t-radio-button
              v-for="item in LayoutType"
              :key="item"
              :value="item"
            >
              <div class="flex items-center">
                <SvgIcon
                  class="mr-[4px]"
                  :name="item"
                  color="var(--aix-text-color-black-disabled)"
                  size="25px"
                />
                {{ LayoutLabel[item] }}
              </div>
            </t-radio-button>
          </t-radio-group>
        </t-form-item>
        <t-form-item v-if="chartType === ChartTypeEnum.Line" label="Show Avg" name="showAvg">
          <t-switch :value="form.showAvg" @change="updateListForm($event, 'showAvg')" />
        </t-form-item>
        <t-form-item label="Metric" name="metric">
          <div>
            <div
              v-for="(item, index) in form.metric"
              :key="item"
              class="flex items-center"
              :class="{'mt-[10px]': index > 0}"
            >
              <t-select
                :value="item"
                class="mr-[10px] w-[210px]"
                :options="metricOptions"
                clearable
                @change="updateListForm($event, 'metric', index)"
              />
              <SvgIcon
                v-if="form.metric.length > 1"
                class="cursor-pointer w-[16px] h-[16px]"
                name="delete"
                size="16px"
                color="var(--aix-text-color-black-primary)"
                @click="deleteMetric(index)"
              />
            </div>
            <div
              v-if="form.groupby.filter((item: string) => item).length <= 1"
              class="flex items-center cursor-pointer mt-[13px]"
              @click="addMetric"
            >
              <SvgIcon
                class="mr-[5px]"
                name="plus"
                size="16px"
                color="var(--aix-text-color-black-primary)"
              />
              Add another metric
            </div>
          </div>
        </t-form-item>
        <div class="flex">
          <t-form-item label="Group by" name="groupby">
            <t-select
              :value="form.groupby[0]"
              :options="groupbyOptions"
              clearable
              @change="updateListForm($event, 'groupby', 0)"
            />
          </t-form-item>

          <t-form-item
            v-show="SUB_GROUP_KEY.includes(chartType) && form.metric.filter((item: string) => item).length <= 1"
            label="Sub group by"
            name="groupby"
          >
            <t-select
              :value="form.groupby[1]"
              :options="groupbyOptions"
              clearable
              @change="updateListForm($event, 'groupby', 1)"
            />
          </t-form-item>
        </div>
        <div
          class="flex"
        >
          <t-form-item label="Show top" name="top">
            <t-select
              :value="form.top"
              :options="topOptions"
              clearable
              @change="updateListForm($event, 'top')"
            />
          </t-form-item>
          <t-form-item label="Sort by" name="sort">
            <t-select
              :value="form.sort"
              :options="sortByOptions"
              @change="updateListForm($event, 'sort')"
            />
          </t-form-item>
        </div>
        <t-form-item label="Filter" name="filter">
          <FormContainer
            :model-value="reportFilterStore.form"
            :form-list="reportFilterStore.schema.value"
            :is-show-confirm-btn="false"
            :is-show-reset-btn="false"
            @close="onClose"
            @update:model-value="updateModelValue"
          />
        </t-form-item>
        <t-form-item label="Metric Filter" name="metricFilterKey">
          <div>
            <div
              v-for="(item, index) in form.metricFilterKey"
              :key="item"
              class="flex items-center"
              :class="{'mt-[10px]': index > 0}"
            >
              <t-select
                :value="item"
                class="mr-[10px] w-[210px]"
                :options="creativeInsightReport.metricOptions"
                clearable
                @change="updateListForm($event, 'metricFilterKey', index)"
              />
              <!-- 添加filter -->
              <div class="flex items-center mr-[10px]">
                <p class="w-[45px]">
                  Filter
                </p>
                <t-select
                  :value="form.metricFilter[index].condition"
                  :options="SYMBOL_OPTIONS"
                  class="w-[97px]"
                  @change="updateListForm($event, `metricFilter`, index, 'condition')"
                />
                <t-input
                  :value="getFormat(item) === 'percent' ?
                    form.metricFilter[index].value * 100 :
                    form.metricFilter[index].value"
                  type="number"
                  class="w-[103px] h-[38px] rounded-default"
                  placeholder="0-100000"
                  :suffix="getFormat(item) === 'percent' ? '%' : ''"
                  @change="updateListForm($event, `metricFilter`, index, 'value')"
                />
              </div>
              <SvgIcon
                v-if="form.metric.length > 1"
                class="cursor-pointer w-[16px] h-[16px]"
                name="delete"
                size="16px"
                color="var(--aix-text-color-black-primary)"
                @click="deleteMetricFilter(index)"
              />
            </div>
            <div
              class="flex items-center cursor-pointer mt-[13px]"
              @click="addMetricFilter"
            >
              <SvgIcon
                class="mr-[5px]"
                name="plus"
                size="16px"
                color="var(--aix-text-color-black-primary)"
              />
              Add another metric filter
            </div>
          </div>
        </t-form-item>
      </t-form>
      <EchartTemplate
        key="edit-dialog"
        :loading="loading"
        class="w-[464px]"
        v-bind="form"
        :chart-type="form.type"
        :option="chartData"
        use-type="dialog"
        :chart-style="{
          height: '400px'
        }"
        :metric-options="creativeInsightReport.metricOptions"
        :attribute-options="creativeInsightReport.attributeOptions"
      />
    </div>
  </BaseDialog>
</template>
<script setup lang='ts'>
import { useCreativeInsightReportStore } from '@/store/creative/insight/report/index.store';
import BaseDialog from 'common/components/Dialog/Base';
import { useI18n } from 'common/compose/i18n';
import { I18N_BASE } from 'common/const/i18n';
import { unRefObj } from 'common/utils/reactive';
import { SubmitContext } from 'tdesign-vue-next';
import { computed, ref, watch } from 'vue';
import { ChartItem, TopType, FORM_RULES, LayoutType, ChartTypeEnum, CHART_LIST, LayoutLabel, ChartIcon, SUB_GROUP_KEY } from '../const';
import { useCardForm } from '../compose/useCardForm';
import { useCommonSearch } from '@/compose/useCommonSearch';
import { setFilterSchema } from '@/store/creative/insight/report/schema';
import FormContainer, { IFormDynamicItem, IFormItem } from 'common/components/FormContainer';
import { getChartData } from 'common/service/creative/insight/get-chart-data';
import { cloneDeep, max, pullAt, uniq } from 'lodash-es';
import EchartTemplate from './echart-template.vue';
import SvgIcon from 'common/components/SvgIcon';
import Input from 'common/components/Input';
import { connectAllLabel, getCreativePivotGetTableParam } from '@/store/creative/dashboard/utils';
import { haveTypeOptions } from '../utils';
import { ChartItemType } from '../index.d';
import { SYMBOL_OPTIONS } from '../../../dashboard/const';
import { FilterItem } from '@/views/creative/dashboard/components';
import { useGlobalGameStore } from '@/store/global/game.store';
import { ISearchValue } from 'common/components/SearchBox';
const { t } = useI18n([I18N_BASE]);

const emitEvent = defineEmits(['hide']);
const props = defineProps({
  type: {
    type: String,
    default: 'new',
  },
  form: {
    type: Object || null,
    default: () => ChartItem(),
  },
});
const creativeInsightReport = useCreativeInsightReportStore();

const propsForm = computed(() => {
  let result: any = cloneDeep(ChartItem());
  if (props.form) {
    result = {
      ...result,
      ...props.form,
    };
  }
  Object.keys(creativeInsightReport.form).forEach((key) => {
    if (key in result.filter && result.filter[key].length === 0) {
      if (!result.globalFilterKey.includes(key)) {
        result.globalFilterKey.push(key);
      }
      result.filter[key] = cloneDeep(creativeInsightReport.form[key as keyof typeof creativeInsightReport.form]);
    }
  });
  delete result.filter.chartList;
  return result;
});
const gameStore = useGlobalGameStore();
const {
  form,
  clearForm,
  setForm,
  emit,
  abort,
  loading,
  chartData,
  setTempChartData,
  clearChartData,
} = useCardForm({
  form: propsForm.value,
  func: getChartData,
  options: creativeInsightReport.options,
  tempData: props.form.tempData,
  game: gameStore.gameCode,
});


const chartType = computed(() => `${form.value.type}${form.value.detailType ? `-${form.value.detailType}` : ''}`);

watch(
  () => chartType.value,
  (value) => {
    if (SUB_GROUP_KEY.includes(value)) {
      if (form.value.groupby.length === 1) {
        form.value.groupby.push('');
      }
    } else {
      if (form.value.groupby.length === 2) {
        form.value.groupby.pop();
      }
    }
  }, {
    deep: true,
    immediate: true,
  },
);

const chartFormRef = ref<InstanceType<typeof BaseDialog> | null>();
const formRef = ref<any>();
const topOptions = Object.keys(TopType).filter(key => typeof TopType[key as any] === 'number')
  .map(key => ({
    label: key,
    value: TopType[key as any],
  }));
const reportFilterStore = useCommonSearch<any, any>();

const addReport = () => {
  formRef?.value?.submit();
};
const onSubmit = (context: SubmitContext<FormData>) => {
  if (context.validateResult === true) {
    // emit.cancel();
    const { filter } = unRefObj(form.value);
    delete filter.chartList;
    // 判断是否又tempData如果又就放到chartItem里
    const obj = {
      ...unRefObj(form.value),
      filter,
      tempData: chartData.value,
      groupby: form.value.groupby.filter((value: string) => value),
      metric: form.value.metric.filter((value: string) => value),
    };
    if (props.type === 'new') {
      const maxIndex: number = max(creativeInsightReport.form.chartList.map((item: ChartItemType) => item.index)) || 0;
      creativeInsightReport.addChart({
        ...obj,
        index: maxIndex + 1,
      });
    } else {
      const oldChartData = cloneDeep(creativeInsightReport.form.chartList
        .find((item: ChartItemType) => item.id === form.value.id));
      const oldTempData = JSON.stringify(oldChartData.tempData);
      delete oldChartData.tempData;
      delete obj.tempData;
      const newTempData = JSON.stringify(chartData.value);
      // 判断是否有值更改如果有的话tempData为[]
      creativeInsightReport.updateChartListItem({
        ...obj,
        tempData: JSON.stringify(oldChartData) !== JSON.stringify(obj)
          && oldTempData === newTempData ? [] : chartData.value,
      });
    }
    cancel();
    chartFormRef?.value?.hide();
    emitEvent('hide');
  }
};

const showDialog = () => {
  chartFormRef?.value?.show();
};

defineExpose({
  showDialog,
});

const cancel = () => {
  abort();
  clearForm(ChartItem());
  // 重置form和chartData
  clearChartData();
  reportFilterStore.setForm(ChartItem().filter);
};

const attributeMetricOptions = computed(() => (type: string) => {
  if (type === 'metric') {
    return creativeInsightReport.metricOptions.concat(creativeInsightReport.attributeOptions);
  }
  return creativeInsightReport.attributeOptions.concat(creativeInsightReport.metricOptions);
});

const sortByOptions = computed(() => form.value.metric.map((value: string) => {
  const { label = '' } = attributeMetricOptions.value('groupby').find(item => item.value === value) || { label: '' };
  return {
    value,
    label,
  };
}));

/**
 * 当group by的下拉框选择了metric里面的值那么metric下来拉框中只能选择attribute里的值
 * 同理 group by的下拉框选择了attribute里面的值那么metric下来拉框中只能选择metric里的值
 * 当类型时form的时候是例外 group by只能选attribute metric只能选metric
 */
const groupbyOptions = computed(() => {
  if (form.value.type === ChartTypeEnum.Form || chartType.value === ChartTypeEnum.Pie_Ring || gameStore.gameCode !== 'pubgm') {
    return creativeInsightReport.attributeOptions.map((item) => {
      if (form.value.groupby.includes(item.value)) {
        return {
          ...item,
          disabled: true,
        };
      }
      return item;
    });
  }
  const options: any = haveTypeOptions({
    options: {
      attribute: creativeInsightReport.attributeOptions,
      metric: creativeInsightReport.metricOptions,
    },
    form: form.value,
    allOptions: attributeMetricOptions.value('groupby'),
    type: 'metric',
  });

  return options ? options : attributeMetricOptions.value('groupby');
});
const metricOptions = computed(() => {
  if (form.value.type === ChartTypeEnum.Form || chartType.value === ChartTypeEnum.Pie_Ring || gameStore.gameCode !== 'pubgm') {
    return creativeInsightReport.metricOptions.map((item) => {
      if (form.value.metric.includes(item.value)) {
        return {
          ...item,
          disabled: true,
        };
      }
      return item;
    });
  }

  const options: any = haveTypeOptions({
    options: {
      attribute: creativeInsightReport.attributeOptions,
      metric: creativeInsightReport.metricOptions,
    },
    form: form.value,
    allOptions: attributeMetricOptions.value('metric'),
    type: 'groupby',
  });

  return options ? options : attributeMetricOptions.value('metric');
});

const getFormat = computed(() => (key: string) => creativeInsightReport.metricOptions.find(item => item.value === key)?.format || '');


watch(
  () => props.form.tempData,
  (tempData) => {
    setTempChartData(tempData);
  }, {
    deep: true,
  },
);


watch(
  [() => propsForm.value, () => creativeInsightReport.options],
  ([value, options]) => {
    if (value) {
      setForm(value);
    }
    if (JSON.stringify(options) !== '{}') {
      reportFilterStore.setForm(value.filter);
      setFilterSchema({
        searchSortList: Object.keys(value.filter).filter(key => value.filter[key].length > 0),
        reportFilterStore,
        maxDate: creativeInsightReport.maxDate,
        minDate: creativeInsightReport.minDate,
        dtstattimePresets: form.value.filter.dtstattimePresets,
        dtstattimeList: creativeInsightReport.dtstattimeList,
        version: form.value.filter.version || 'daily',
        options,
        onReset: false,
        updateVersion,
        changeDtstattimePresets,
        showDetail: false,
      });
    }
  }, {
    deep: true,
  },
);

const updateVersion = (value: string) => {
  form.value.filter.version = value;
};

const changeDtstattimePresets = (value: string) => {
  form.value.filter.dtstattimePresets = value;
};

const addMetric = () => {
  form.value.metric.push('');
  form.value.metricFilter.push({
    condition: 5,
    value: 0,
  });
};

const addMetricFilter = () => {
  form.value.metricFilterKey.push('');
  form.value.metricFilter.push({
    condition: 5,
    value: 0,
  });
};
const deleteMetric = (index: number) => {
  pullAt(form.value.metric, [index]);
  pullAt(form.value.metricFilter, [index]);
};
const deleteMetricFilter = (index: number) => {
  pullAt(form.value.metricFilterKey, [index]);
  pullAt(form.value.metricFilter, [index]);
};

const updateListForm = (value: string, key: string, index?: number, itemKey?: string) => {
  if (index || index === 0) {
    if (itemKey) {
      form.value[key][index][itemKey] = itemKey === 'value' && getFormat.value(form.value.metricFilterKey[index]) === 'percent' ? Number(value) / 100 : value;
    } else {
      form.value[key][index] = value;
    }
  } else {
    form.value[key] = cloneDeep(value);
  }

  if (key === 'metric') {
    // 如果metric更改 并且sort 不存在于 metric里就选择其中第一个
    if (!form.value.metric.find((value: string) => value === form.value.sort)) {
      const [sort] = form.value.metric;
      form.value.sort = sort;
    }
  }
  // 把chartData置为空
  clearChartData();

  const { filter, metricFilter } = unRefObj(form.value);
  filter.string_search?.forEach((item: ISearchValue) => {
    filter[item.field as keyof typeof filter] = cloneDeep(item.condition);
  });
  delete filter.chartList;
  delete filter.string_search;
  delete filter.dtstattimePresets;
  delete filter.version;

  // 处理all_label
  const temp = connectAllLabel(filter.all_label);
  const tempFilter = cloneDeep(filter);
  delete tempFilter.all_label;
  const having: any = {};

  metricFilter.forEach((item: FilterItem, index: number) => {
    having[form.value.metricFilterKey[index]] = [item];
  });

  emit({
    ...unRefObj(form.value),
    filter,
    where: getCreativePivotGetTableParam({
      form: {
        ...tempFilter,
        ...temp,
        where: having,
      },
      date: {
        maxDate: creativeInsightReport.maxDate,
      },
      metric: uniq(form.value.metric.concat(form.value.metricFilterKey)),
      options: creativeInsightReport.options,
    }),
  }, {
    attribute: creativeInsightReport.attributeOptions,
    metric: creativeInsightReport.metricOptions,
    country_code: creativeInsightReport.options.country_code,
  });
};

const onClose = (item: IFormItem | IFormDynamicItem) => {
  form.value.globalFilterKey = form.value.globalFilterKey.filter((key: string) => item.ext.key !== key);
  const value = cloneDeep(form.value.filter);
  value[item.ext.key] = [];
  reportFilterStore.setForm(value);
  updateListForm(value, 'filter');
};

const updateModelValue = (value: any) => {
  const globalFilterKey = Object.keys(value)
    .filter(key => JSON.stringify(creativeInsightReport.form[key as keyof typeof creativeInsightReport.form])
      === JSON.stringify(value[key]));
  form.value.globalFilterKey = globalFilterKey;
  reportFilterStore.setForm(value);
  updateListForm(value, 'filter');
};

const updateFormType = (value: string) => {
  const [type, detailType] = value.split('-');
  // form.value.type = type;
  // form.value.detailType = detailType;
  updateListForm(type, 'type');
  updateListForm(detailType, 'detailType');
};
</script>
