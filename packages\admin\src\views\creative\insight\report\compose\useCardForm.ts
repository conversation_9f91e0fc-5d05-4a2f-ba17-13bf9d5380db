import { OptionsItem } from './../../../../../../../common/types/cascader.d';
import { useFetchWrapper } from 'common/compose/request/request';
import { cloneDeep, groupBy, flattenDeep, isNumber, isUndefined } from 'lodash-es';
import { computed, ref, toRaw } from 'vue';
import { ChartItemType, MetricItemType } from '../index.d';
import { FilterFormType } from '@/store/creative/insight/report';
import { ChartTypeEnum } from '../const';
import { groupByAttribute } from '@/store/creative/dashboard/utils';
import { GetTableParam } from '@/store/creative/dashboard/dashboard';
import { useGlobalGameStore } from '@/store/global/game.store';
import { BaseTableCellParams } from 'tdesign-vue-next';
import { getMergeConfig } from '@/views/creative/dashboard/utils';
import { formatDataByMetric, getAvgByKey } from '../utils';
import { useRenderFormat } from 'common/compose/table/render-format';
import { useLoading } from 'common/compose/loading';

export const useCardForm = (param: {
  form: any,
  func: any,
  options: any,
  tempData?: any,
  game: string,
}) => {
  const formObj = ref<any>(cloneDeep(param.form));
  const gameStore = useGlobalGameStore();
  const attributeOptions = ref<OptionsItem[]>([]);
  const metricOptions = ref<MetricItemType[]>([]);
  const countryCode = ref<OptionsItem[]>(param.options.country_code || []);
  const tempChartData = ref(param.tempData);
  const { isLoading, showLoading, hideLoading } = useLoading(false);
  /**
   * 更新内部form值
   * @param newValue any
   */
  const setForm = (newValue: any) => {
    const deepNewValue = cloneDeep(newValue);
    Object.keys(deepNewValue).forEach((key) => {
      if (key in formObj.value) {
        formObj.value[key] = deepNewValue[key];
      }
    });
  };

  const setTempChartData = (tempData: any) => {
    syncData.value = null;
    tempChartData.value = cloneDeep(tempData);
  };

  const clearForm = (newValue: any) => {
    formObj.value = cloneDeep(newValue);
  };

  const { loading, data: syncData, emit: getData, abort } = useFetchWrapper<GetTableParam, any[]>(
    param.func,
    {
      where: [],
      group: [],
      metric: [],
      orderby: [],
      realMetric: [],
      otherMetric: [],
      top: 0,
    },
    {
      storage: true, // 先用local storage 同步更新
      throttle: 3000, // 500ms 内只能请求一次
      trailing: true,
      immediately: false,
      reactive: false,
    },
  );


  const filterEmpty = (list: any[]) => list.filter(item => item);

  const emit = (value: ChartItemType & {where: any[]}, options: {
    attribute: OptionsItem[],
    metric: MetricItemType[],
    country_code: OptionsItem[],
  }) => {
    abort();
    // where group by metric top 必须有值
    // 判断where是否有筛选
    const haveFilter = Object.keys(value.filter)
      .some(key => (value.filter[key as keyof FilterFormType] as any[]).length > 0);
    if (
      haveFilter
      && filterEmpty(value.groupby).length > 0
      && filterEmpty(value.metric).length > 0
      && value.top && value.top > 0
    ) {
      showLoading();
      // group by 里面有些值可能是metric里的
      // metric 里面有些之可能是group by里的
      const groupby = toRaw(value.groupby).filter(item => item);
      const metric = toRaw(value.metric);
      attributeOptions.value = options.attribute;
      metricOptions.value = options.metric;
      countryCode.value = options.country_code;
      const attributeValue = options.attribute.map(({ value }) => String(value));
      const metricValue = options.metric.map(({ value }) => String(value));
      const groupIsGroup = groupby.every(value => attributeValue.includes(value));
      const metricIsMetric = metric.every(value => metricValue.includes(value));
      const param: any = {
        version: 'daily',
        group: groupIsGroup ? groupby : metric,
        orderby: [{ by: groupIsGroup ? value.sort || metric[0] : groupby[0], order: 'DESC' }],
        metric: groupby.concat(metric),
        realMetric: metricIsMetric ? metric : groupby,
        where: toRaw(value.where),
        needGroup: false, // 不要想dashboard进行根据每个group by的每一个值进行order by
        top: value.top,
        metricTop: !groupIsGroup,
      };
      const pageSize = value.top;
      if (pageSize && pageSize !== 0) {
        param.pageSize = pageSize;
        param.pageIndex = 0;
      }
      getData(param);
    }
  };

  const chartData = computed(() => {
    if (syncData.value) {
      hideLoading();
      // us region 的替换
      syncData.value = syncData.value.map((item) => {
        const tempItem: any = cloneDeep(item);
        if ('ua_region' in tempItem) {
          tempItem.ua_region = countryCode.value
            .find((item: OptionsItem) => item.label === tempItem.ua_region)?.value || tempItem.ua_region;
          tempItem.ua_region = tempItem.ua_region === 'Europe' ? 'EUR' : tempItem.ua_region;
        }
        // 判断一下metric 和 group by都有
        formObj.value.metric.concat(formObj.value.groupby).filter((key: string) => key)
          .forEach((key: string) => {
            if (!(key in tempItem) || isUndefined(tempItem[key])) {
              tempItem[key] = '';
            }
          });
        Object.keys(tempItem).forEach((key) => {
          if (!isNumber(key)) {
            // tempItem[key] = tempItem[key]?.length > 10 ? `${tempItem[key].slice(0, 10)}...` : tempItem[key];
          }
          const format = metricOptions.value.find(metricItem => metricItem.value === key)?.format;
          tempItem[key] = format ? useRenderFormat({
            format: 'float',
            opt: format === 'percent' ? 4 : 2,
            value: tempItem[key],
          }) : tempItem[key];
        });
        return tempItem;
      });

      if (formObj.value.type === ChartTypeEnum.Line && formObj.value.groupby.length > 1 && formObj.value.showAvg) {
        const groupByObj = groupBy(syncData.value, formObj.value.groupby[0]);
        const number = Object.keys(groupBy(syncData.value, formObj.value.groupby[1])).length;
        const listInList = Object.keys(groupByObj).map((key) => {
          const avg = getAvgByKey(groupByObj[key], formObj.value.metric[0], number);
          groupByObj[key].push({
            [formObj.value.groupby[0]]: groupByObj[key][0][formObj.value.groupby[0]],
            [formObj.value.groupby[1]]: 'AVG',
            [formObj.value.metric[0]]: avg,
          });
          return groupByObj[key];
        });
        syncData.value = flattenDeep(listInList);
      }

      if (formObj.value.type !== ChartTypeEnum.Form) {
        // format Data
        return formatDataByMetric(syncData?.value || [], formObj.value, {
          attributeOptions: attributeOptions.value,
          metricOptions: metricOptions.value,
        });
      }
      const metric = formObj.value.groupby.concat(formObj.value.metric).filter((item: string) => item);
      const table = groupByAttribute({
        data: cloneDeep(syncData?.value || []),
        group: formObj.value.groupby.filter((item: string) => item),
        orderby: [{ by: formObj.value.groupby[0], order: 'desc' }],
        metric,
        filterTotal: false,
        options: param.options.value,
        game: gameStore.gameCode,
        showTop: [],
      });

      const mergeConfigList = getMergeConfig(formObj.value.groupby, table, 1);
      const rowspanAndColspan = ({ rowIndex, colIndex }: BaseTableCellParams<any>) => {
        // columnIndex是列，recordIndex是行
        const mergeColumnCfg = mergeConfigList[colIndex];
        const mergeNum = mergeColumnCfg?.[rowIndex];

        if (mergeColumnCfg && mergeNum) {
          return {
            rowspan: mergeNum,
          };
        }
      };
      const tableAllColumns = attributeOptions.value.concat(metricOptions.value)
        .filter(item => metric.includes(item.value))
        .map(item => ({ colKey: item.value, title: item.label }));

      return {
        resizable: true,
        rowKey: 'id',
        horizontalScrollAffixedBottom: true,
        headerAffixedTop: true,
        data: table,
        rowspanAndColspan,
        columns: tableAllColumns,
      };
    }

    return tempChartData.value || [];
  });

  const clearChartData = () => {
    syncData.value = null;
  };

  const allLoading = computed(() => Boolean(isLoading.value || loading.value));

  return {
    form: formObj,
    setForm,
    clearForm,
    abort,

    emit,
    loading: allLoading,
    chartData,
    clearChartData,
    setTempChartData,
  };
};

