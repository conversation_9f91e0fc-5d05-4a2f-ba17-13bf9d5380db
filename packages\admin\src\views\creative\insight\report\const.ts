import { getInitForm } from '@/store/creative/insight/report/const';
import { guid } from 'common/utils/common';
import { FilterFormType } from '@/store/creative/insight/report/index.d';

const getInitFilter = () => {
  const obj: FilterFormType = getInitForm([]);
  delete obj.show_filter_details;
  return obj;
};


export enum ChartTypeEnum {
  Bar = 'bar',
  Line = 'line-smooth',
  Bar_Line = 'bar-line',
  Line_Area = 'line-area',
  Pie_Ring = 'pie-ring',
  Form = 'form',
}

const defaultImgTemplate = (type: string) => `https://static.aix.intlgame.cn/icons/${type}.png?host=aix-doc.pages.woa.com`;

export const ChartDefaultImg = {
  bar: defaultImgTemplate('bar'),
  'line-smooth': defaultImgTemplate('line'),
  'bar-line': defaultImgTemplate('bar'),
  'line-area': defaultImgTemplate('line'),
  'pie-ring': defaultImgTemplate('pie'),
  form: defaultImgTemplate('form'),
};
export const ChartIcon = {
  bar: 'bar',
  'line-smooth': 'line',
  'bar-line': 'bar-line',
  'line-area': 'line-area',
  'pie-ring': 'pie',
  form: defaultImgTemplate('form'),
};


export enum LayoutType {
  Horizontal = 'horizontal', // 横向
  Vertical = 'vertical' // 竖向
}

export const ChartItem = () => ({
  name: '',
  type: ChartTypeEnum.Bar,
  layout: LayoutType.Horizontal,
  metric: [''],
  groupby: [''],
  top: 3,
  filter: getInitFilter(),
  id: guid(''),
  index: 0,
  globalFilterKey: [],
  tempData: [],
  detailType: '',
  secondTop: 3,
  showAvg: false,
  metricFilter: [],
  metricFilterKey: [],
  color: [],
  sort: '',
});

export const LayoutLabel = {
  horizontal: '50% width',
  vertical: '100% width',
};

export enum TopType {
  Top3 = 3,
  Top5 = 5,
  Top10 = 10,
  Top20 = 20,
  Top30 = 30,
}

export const validatorList = (val: string[]) => val.filter(item => item).length > 0;

export const FORM_RULES = {
  name: [
    { required: true, message: 'Please enter Chart Name' },
  ],
  metric: [
    { validator: validatorList, message: 'Please Select Metric' },
  ],
  groupby: [
    { validator: validatorList, message: 'Please Select Group by' },
  ],
};
export const ADD_GROUP_KEY = ['line-smooth'];

export const CHART_LIST = [
  'bar',
  'line-smooth',
  'bar-line',
  'line-area',
  'pie-ring',
];

export const SUB_GROUP_KEY = [
  'line-smooth',
  'bar',
  'pie-ring',
];

export const DOWNLOAD_TYPE = ['png', 'csv', 'xlsx'];

