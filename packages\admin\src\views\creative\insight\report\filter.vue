<template>
  <div class="mb-[24px]">
    <t-alert v-if="showSaveAlert && !creativeInsightReport.loading" theme="info" class="mb-[24px]">
      <template #message>
        <div class="flex justify-between">
          <p>{{ t('changeTipsTitle') }}</p>
          <div>
            <span
              class="mr-[12px] cursor-pointer dividing-line"
              @click="onReset(true)"
            >{{ t('giveUpModification') }}</span>
            <span class="ml-[12px] cursor-pointer" @click="saveHandler">{{ t('save') }}</span>
          </div>
        </div>
      </template>
    </t-alert>
    <FormContainer
      :model-value="creativeInsightReport.form"
      :form-list="creativeInsightReport.schema"
      @submit="onSubmit"
      @close="onClose"
      @update:model-value="updateModelValue"
      @reset="onReset"
    />
  </div>
</template>
<script setup lang="ts">
import FormContainer from 'common/components/FormContainer';
import { useCreativeInsightReportStore } from '@/store/creative/insight/report/index.store';
import { useI18n } from 'common/compose/i18n';
import { I18N_BASE } from 'common/const/i18n';
import { IFormItem, IFormDynamicItem } from 'common/components/FormContainer/type';
import { computed } from 'vue';
import { unRefObj } from 'common/utils/reactive';
import { cloneDeep, isEqual } from 'lodash-es';

const { t } = useI18n([I18N_BASE]);
const creativeInsightReport = useCreativeInsightReportStore();
const emit = defineEmits(['updateGlobalFilter']);
const showSaveAlert = computed(() => {
  const form = unRefObj(creativeInsightReport.form);
  form.chartList = form.chartList.map((item: any) => {
    const temp = cloneDeep(item);
    delete temp.tempData;
    delete temp.color;
    return temp;
  });

  const currentParam = cloneDeep(creativeInsightReport.view.currentView.param);
  if (currentParam?.chartList) {
    currentParam.chartList = currentParam.chartList.map((item: any) => {
      const temp = cloneDeep(item);
      delete temp.tempData;
      delete temp.color;
      return temp;
    });
  }
  return Boolean(currentParam)
  && Boolean(form)
  && !isEqual(currentParam, form)
  && creativeInsightReport.view.currentView.label !== 'share';
});
const onSubmit = () => {
  creativeInsightReport.updateChartListFilter();
  emit('updateGlobalFilter', '');
};
const onClose = (item: IFormItem | IFormDynamicItem) => {
  creativeInsightReport.form[item.ext.key] = [];
};
const updateModelValue = (value: any) => {
  creativeInsightReport.form = value;
};
const onReset = (resetChart: boolean) => {
  creativeInsightReport.onResetHandler({ resetChart });
};
const saveHandler = () => {
  creativeInsightReport.updateViewList(creativeInsightReport.view.currentView);
};
</script>
<style lang="scss" scoped>
:deep(.t-alert__description) {
  width: 100%;
}
.dividing-line {
  position: relative;
  &::after {
    content: '';
    position: absolute;
    right: -12px;
    bottom: 0;
    height: 100%;
    width: 1px;
    @apply bg-black-secondary;
  }
}
</style>
