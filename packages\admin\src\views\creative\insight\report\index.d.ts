import { ChartType, LayoutType } from './const';
import type { FilterFormType } from '@/store/creative/insight/report/index.d';
import { MetricItemType } from '@/store/creative/dashboard/dashboard';
import { OptionsItem } from '../../../../../../common/types/cascader.d';
import { FilterItem } from '../../dashboard/components/index.d';

export type ChartItemType = {
  name: string,
  type: `${ChartType}`,
  layout: `${LayoutType}`,
  metric: string[],
  groupby: string[],
  top: number,
  filter: FilterFormType,
  id: string,
  index: number,
  globalFilterKey: (keyof FilterFormType)[],
  tempData?: any, // 临时存储 echart options
  detailType?: string, // 具体type 详细看basic chart组件
  metricFilter: FilterItem[], // metric 的 filter
  secondTop: number,
  color: string[], // 为了统一整个report颜色(pubgm特有)
  sort: string,
};

export interface MetricItemType extends OptionsItem {
  format?: string
}

export type OptionsItemType = Pick<MetricItemType, 'label' | 'value' | 'children'>;
