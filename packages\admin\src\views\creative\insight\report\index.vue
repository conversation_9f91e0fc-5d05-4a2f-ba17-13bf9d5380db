<template>
  <div
    v-if="loading || creativeInsightReport.loading"
    v-loading="loading || creativeInsightReport.loading" class="bg-white-primary h-full w-full"
  />
  <div v-else>
    <div v-if="showPage" ref="pdfDom">
      <common-view
        title="Creative Insight Report"
      >
        <template #subTitle>
          <View @download="download" />
        </template>
        <template #views>
          <Filter
            @update-global-filter="updateGlobalFilter"
          />
          <Chart
            ref="chart"
          />
        </template>
      </common-view>
    </div>
    <div v-else class="w-full h-full flex justify-center items-center">
      This new feature is now going through closed beta testing.<br>

      You will not be able to view this section before the final launch.<br>

      If you have any questions, please contact your project manager, or AiX Creative Product owner: gabbyzhang.<br>

      Thanks for your understanding!
    </div>
  </div>
</template>
<script setup lang="ts">
import CommonView from 'common/components/Layout/CommonView.vue';
import View from './view.vue';
import { useCreativeInsightReportStore } from '@/store/creative/insight/report/index.store';
import { useWatchGameChange } from 'common/compose/request/game';
import Filter from './filter.vue';
import Chart from './chart.vue';
import { computed, ref } from 'vue';
import { exportPDF } from 'common/utils/export';
import { useAuthStageStore } from '@/store/global/auth.store';
import { getMetricAndAttribute } from 'common/service/creative/insight/get-attribute-and-metric';
import { useGlobalGameStore } from '@/store/global/game.store';

const authStageStore = useAuthStageStore();
const pdfDom = ref<InstanceType<typeof HTMLElement> | null>();
const chart = ref<InstanceType<typeof Chart> | null>();
const rbac = ref<string[]>([]); // 页面白名单人员表
const loading = ref(false);

const creativeInsightReport = useCreativeInsightReportStore();
const gameStore = useGlobalGameStore();
const showPage = computed(() => (gameStore.gameCode === 'pubgm' ? rbac.value.includes(authStageStore.currentUser) : true));


const updateGlobalFilter = () => {
  creativeInsightReport.updateCardFilter();
  setTimeout(() => {
    chart?.value?.updateCardValue(); // 为了等待上面的func把内部filter更新了在触发重新请求
  });
};

const getRbac = async () => {
  loading.value = true;
  const {
    rbac: ctxRbac = [],
  } = await getMetricAndAttribute({ system: 'report' });
  rbac.value = ctxRbac;
  loading.value = false;
};

getRbac();

useWatchGameChange(async () => {
  creativeInsightReport.init();
});

const download = () => {
  exportPDF(pdfDom.value as HTMLElement, {
    name: creativeInsightReport.view.currentView.label,
    ignoreElements: (element: HTMLElement) => element.id === 'add',
  });
};

</script>
