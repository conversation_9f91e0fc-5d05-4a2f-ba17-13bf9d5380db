import { OptionsItem } from 'common/types/cascader';
import { cloneDeep, flattenDepth, groupBy } from 'lodash-es';
import { ChartIcon, ChartTypeEnum } from './const';
import { ChartItemType, MetricItemType } from './index.d';

export const formatDataByMetric = (data: any[], form: ChartItemType, options: {
  metricOptions: OptionsItem[],
  attributeOptions: MetricItemType[],
}) => {
  const allOptions = options.metricOptions.concat(options.attributeOptions);
  const tempData = cloneDeep(data).map((item) => {
    const tempItem = item;
    Object.keys(item).forEach((key) => {
      if (String(item[key]) === 'null') {
        // 把对应值改成options里的title
        tempItem[key] = '';
      }
      const newKey = allOptions.find(optionsItem => optionsItem.value === key)?.label || key;
      tempItem[newKey] = cloneDeep(tempItem[key]);
      tempItem[key] = newKey && newKey !== key ? newKey : tempItem[key];
    });
    return tempItem;
  });
  const metric = form.metric.filter(item => item);
  const groupby = form.groupby.filter(item => item);
  let realMetricFrom = 'metric';
  let realMetric: string[] = [];
  if (metric.every(value => options.metricOptions.find(item => item.value === value))) {
    realMetric = metric;
  }
  if (groupby.every(value => options.metricOptions.find(item => item.value === value))) {
    realMetric = groupby;
    realMetricFrom = 'groupby';
  }
  let realGroup: string[] = [];
  if (metric.every(value => options.attributeOptions.find(item => item.value === value))) {
    realGroup = metric;
  }
  if (groupby.every(value => options.attributeOptions.find(item => item.value === value))) {
    realGroup = groupby;
  }
  if (realMetric.length > 1 || realMetricFrom === 'groupby') {
    // 拿到数据中对应的metric并放到一个数组中
    return flattenDepth(realMetric.map((key: string) => tempData.map(item => ({
      ...item,
      xList: item[key],
      value: item[item[key]],
    }))));
  }
  if (form.type === ChartIcon[ChartTypeEnum.Pie_Ring] && form.groupby.filter(item => item).length > 1) {
    const groupbyList = groupBy(tempData, tempData[0][form.groupby[0]]);
    return Object.keys(groupbyList).map(key => ({
      name: key,
      children: groupbyList[key].map(item => ({
        name: item[item[realGroup[1]]],
        value: item[item[realMetric[0]]],
      })),
    }));
  }
  return tempData;
};

/**
 * 通过传入的类型来返回对应的options 主要做metric和group by 互斥
 * @param param {
  options: {
    attribute: OptionsItem[],
    metric: MetricItemType[],
  },
  form: any,
  allOptions: MetricItemType[],
  type: string,
}
 * @returns options
 */
export const haveTypeOptions = (param: {
  options: {
    attribute: OptionsItem[],
    metric: MetricItemType[],
  },
  form: any,
  allOptions: MetricItemType[],
  type: string,
}) => {
  const {
    form,
    options,
    allOptions,
    type,
  } = param;
  const [tempKey] = form[type].filter((item: any) => item);
  const disabledList = form[type === 'groupby' ? 'metric' : 'groupby'].filter((item: any) => item);
  if (tempKey) {
    return getOptionsByKeyFrom({
      disabledList,
      options,
      allOptions,
      fromKey: tempKey,
    });
  }
  if (!tempKey && disabledList.length > 0) {
    // true 是attribute false 是 metric
    const realDisabled = disabledList.every((key: string) => options.attribute.find(item => item.value === key));
    return getOptionsByKeyFrom({
      disabledList: options[!realDisabled ? 'attribute' : 'metric'].map(item => String(item.value)),
      options,
      allOptions,
      fromKey: String(options[!realDisabled ? 'attribute' : 'metric'][0].value),
    });
  }
};

/**
 * 通过传入的值来判断哪些options需要被disable
 * @param param {
  disabledList: string[],
  options: {
    attribute: OptionsItem[],
    metric: MetricItemType[],
  },
  allOptions: MetricItemType[],
  fromKey: string,
}
 * @returns
 */
const getOptionsByKeyFrom = (param: {
  disabledList: string[],
  options: {
    attribute: OptionsItem[],
    metric: MetricItemType[],
  },
  allOptions: MetricItemType[],
  fromKey: string,
}) => {
  let key = '';
  if (param.options.metric.find(item => item.value === param.fromKey)) {
    key = 'metric';
  }

  if (param.options.attribute.find(item => item.value === param.fromKey)) {
    key = 'attribute';
  }
  const temp = param.disabledList.concat(param.options[key as 'metric' | 'attribute']
    .map(({ value }) => String(value)));
  return param.allOptions.map((item) => {
    if (temp.includes(String(item.value))) {
      return {
        ...item,
        disabled: true,
      };
    }
    return item;
  });
};
/**
 * 得到对应key的平均值
 * @param data any[]
 * @param key string
 * @param number number
 * return number
 */
export const getAvgByKey = (data: any[], key: string, number: number) => data
  .reduce((sum, current) => sum + current[key], 0) / number;
