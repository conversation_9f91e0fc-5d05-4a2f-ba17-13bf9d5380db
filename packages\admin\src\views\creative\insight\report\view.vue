<template>
  <tab-view
    v-model="creativeInsightReport.view.currentViewId"
    :list="creativeInsightReport.view.viewList"
    :loading="creativeInsightReport.view.loading"
    :game="gameStore.gameCode"
    :on-share-request="creativeInsightReport.view.shareView"
    :filter-view-type="filterViewType"
    :show-download="true"
    @update="creativeInsightReport.updateViewList"
    @add="creativeInsightReport.addViewHandler"
    @delete="creativeInsightReport.view.deleteView"
    @download="download"
  />
</template>
<script setup lang="ts">
import TabView from '@/views/creative/dashboard/components/TabView';
import { useCreativeInsightReportStore } from '@/store/creative/insight/report/index.store';
import { useGlobalGameStore } from '@/store/global/game.store';
import { computed } from 'vue';

const emit = defineEmits(['download']);
const creativeInsightReport = useCreativeInsightReportStore();

const gameStore = useGlobalGameStore();
// 过滤掉单选框中选择view game用
const filterViewType = computed(() => (gameStore.gameCode === 'pubgm'
  ? () => []
  : (options: {label: string, value: string}[]) => options));

const download = () => {
  emit('download', '');
};
</script>
