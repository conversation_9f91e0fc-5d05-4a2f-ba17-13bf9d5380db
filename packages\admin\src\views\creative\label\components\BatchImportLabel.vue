<template>
  <BaseDialog
    ref="dialogRef"
    title="Bulk Edit Label"
    width="30%"
    confirm-text="Confirm Import"
    :confirm-loading="submitLoading"
    @confirm="confirmToUpload"
  >
    <template #default>
      <t-space
        class="w-full"
        direction="vertical"
        size="16px"
      >
        <t-space size="8px">
          <t-dropdown
            :options="options"
            min-column-width="175px"
            @click="onDownload"
          >
            <t-space>
              <t-button theme="default">
                Download Template
                <template #suffix>
                  <t-icon
                    name="chevron-down"
                    size="16"
                  />
                </template>
              </t-button>
            </t-space>
          </t-dropdown>
        </t-space>

        <t-upload
          ref="uploadRef"
          v-model="files"
          theme="file"
          :abridge-name="[10, 8]"
          draggable
          accept=".xlsx"
          :size-limit="{ size: 5, unit: 'MB', message: 'File size should not over 5MB' }"
          :request-method="requestMethod"
        />
      </t-space>
    </template>
  </BaseDialog>
</template>

<script setup lang="ts">
import BaseDialog from 'common/components/Dialog/Base';
import { ref } from 'vue';
import { useDownloadFile } from 'common/compose/download-file';
import { useTips } from 'common/compose/tips';
import { useLoading } from 'common/compose/loading';
import { get } from '@vueuse/core';
import { addLabelRuleTask } from 'common/service/creative/label/manage/labels';
import { useGlobalGameStore } from '@/store/global/game.store';
import type { UploadFile } from 'tdesign-vue-next';
import { v4 as uuidv4 } from 'uuid';
import { uploadToCos } from 'common/components/FileUpload/util';
import { useEnv } from 'common/compose/env';
import { FILE_CDN_COM } from 'common/config';
import { MessagePlugin } from 'tdesign-vue-next';

const { err: errTips, success: successTips } = useTips();
const { env } = useEnv();
const props = defineProps({
  manualLabelList: {
    type: Array,
    default: () => [],
  },
});

const dialogRef = ref();
const uploadRef = ref();
const files = ref([]);
/**
 * donwload file template start
 */
const options = [
  { value: 'default', content: '通用模板' },
  { value: 'youtube-link', content: 'YouTube Link模板' },
];

function onDownload(item: { value: string; content: string }) {
  const type = item.value;
  const configs: any = {
    default: {
      fields: {
        素材名称: 'UA31276%',
      },
      filename: '素材标签导入模板',
    },
    'youtube-link': {
      fields: {
        'Assets Link': 'https://www.youtube.com/shorts/SnPGFplm9sU',
        素材编号: 'UA31276',
      },
      filename: 'YouTube Link标签导入模板',
    },
  } as const;
  const config = configs[type] || configs.default;

  const emptyFields = Object.fromEntries(Object.keys(config.fields).map(key => [key, '']));
  const optionalRow: any = { ...emptyFields }; // 描述字段必填/可选
  const optionsRow: any = { ...emptyFields }; // 枚举可选项
  const demoRow = { ...config.fields }; // 示例数据
  const manualList = props.manualLabelList.filter((item: any) => item.label_method === 'manual');
  manualList.forEach((item: any) => {
    const { name, multiple, options, required, label_method: labelMethod } = item;
    let colName = name;
    if (labelMethod === 'intelligent') colName += '（intelligent）';
    const requireText = required ? '必填' : '可选';
    optionalRow[colName] = multiple ? `${requireText}-多选` : `${requireText}-单选`;
    optionsRow[colName] = options.join(',');
    const defaultOptions = multiple ? options : [options[0]];
    demoRow[colName] = defaultOptions.slice(0, 2).join(',');
  });
  const data = [optionalRow, optionsRow, demoRow];

  useDownloadFile(
    [
      {
        sheet: 'Sheet1',
        list: data,
      },
    ],
    `${config.filename}.xlsx`,
    {
      mode: 'group',
    },
  );
}

/**
 * donwload file template end
 */
// 提交导入内容到后台服务的loading
const { isLoading: submitLoading, showLoading: showSubmitLoading, hideLoading: hideSubmitLoading } = useLoading();

// const { cols } = useImportTable(keyAttribute, submitLoading, firstLabels, removeLabels);

const gameStore = useGlobalGameStore();

const confirmToUpload = async () => {
  const uploadFile = files.value?.[0] as UploadFile;

  const cosFilePath = uploadFile?.response?.key;
  if (!cosFilePath) {
    MessagePlugin.warning('Please upload the file first');
    return;
  }
  showSubmitLoading();
  // TODO 生成了cos地址换掉
  await addLabelRuleTask({
    gameCode: gameStore.gameCode,
    cosFile: `/${cosFilePath}`,
  })
    .then(() => {
      successTips('upload success');
      dialogRef.value.hide();
    })
    .catch((err) => {
      errTips(err.message);
    })
    .finally(() => {
      hideSubmitLoading();
    });
};

const requestMethod = (uploadFile: UploadFile) => {
  const rawFile = uploadFile.raw as File;
  const key = generateUploadKey(rawFile.name);
  return new Promise((resolve) => {
    uploadToCos({ file: rawFile, key, type: 'aix' })
      .then((data) => {
        if (data.code) {
          resolve({ status: 'fail', error: 'upload error!', response: {} });
        }
        resolve({
          status: 'success',
          response: {
            key,
            url: `${FILE_CDN_COM}/${encodeURIComponent(key)}`,
          },
        });
      })
      .catch(() => {
        resolve({ status: 'fail', error: 'upload error!', response: {} });
      });
  });
};

const generateUploadKey = (fileName: string): string => {
  const uuid = uuidv4();
  return `creative_label/${gameStore.gameCode}_${env.value}_${uuid}_${Date.now()}_${fileName}`;
};

defineExpose({
  show: () => get(dialogRef).show(),
});
</script>

<style scoped>
:deep(.t-upload__dragger) {
  width: 100%;
}
</style>
