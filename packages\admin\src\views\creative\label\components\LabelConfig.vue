<template>
  <BaseDialog v-model:visible="visible" top="10vh" @confirm="onConfirm">
    <template #title>
      {{ `${isCreate ? 'Create' : 'Edit'} Label` }}
    </template>
    <div class="w-[600px]">
      <t-form
        ref="formRef" :data="formData" label-align="top"
        :rules="rules"
      >
        <t-form-item label="First Label" name="name">
          <t-input v-model="formData.name" />
        </t-form-item>
        <t-form-item label="Filling Requirement" name="required">
          <t-radio-group v-model="formData.required">
            <t-radio-button :value="true">Required</t-radio-button>
            <t-radio-button :value="false">Optional</t-radio-button>
          </t-radio-group>
        </t-form-item>
        <t-form-item label="Choice Requirement" name="multiple">
          <t-radio-group v-model="formData.multiple">
            <t-radio-button :value="true">Multiple Choice</t-radio-button>
            <t-radio-button :value="false">Single Choice</t-radio-button>
          </t-radio-group>
        </t-form-item>
        <t-form-item label="Label Type" name="label_type">
          <t-radio-group v-model="formData.label_type" :disabled="!isCreate">
            <t-radio-button value="creative">Creative Label</t-radio-button>
            <t-radio-button value="content">Content Label</t-radio-button>
          </t-radio-group>
        </t-form-item>
        <t-form-item label="Label Level" name="label_level">
          <t-radio-group v-model="formData.label_level" :disabled="!isCreate">
            <t-radio-button value="serial">
              <div class="flex flex-center">
                <span class="bg-[#F2AA09] text-center block text-[#fff] w-[22px] h-[22px] rounded-[6px] mr-[6px]">S</span>
                Serial Label
              </div>
            </t-radio-button>
            <t-radio-button value="asset">
              <div class="flex flex-center">
                <span class="bg-[#02B875] text-center block text-[#fff] w-[22px] h-[22px] rounded-[6px] mr-[6px]">A</span>
                Asset Label
              </div>
            </t-radio-button>
          </t-radio-group>
        </t-form-item>
        <t-form-item label="Labeling Method" name="label_type">
          <t-radio-group v-model="formData.label_method" :disabled="!isCreate">
            <t-radio-button value="manual">Manual Labeling</t-radio-button>
            <t-radio-button value="intelligent">Intelligent Labeling</t-radio-button>
          </t-radio-group>
        </t-form-item>
        <t-form-item label="Second Label" name="optionList">
          <t-tag-input
            v-model="formData.optionList"
            :tag-props="{ theme: 'primary' }"
            excess-tags-display-type="break-line"
            placeholder="Press enter to add second label"
            clearable
          >
            <template #tag="{ value }">
              <Text
                :content="value" :tips-content="value" :overflow="true"
                :tool-tip="true"
                :max-length="20"
              />
            </template>
          </t-tag-input>
        </t-form-item>
      </t-form>
    </div>
  </BaseDialog>
</template>
<script setup lang="ts">
import BaseDialog from 'common/components/Dialog/Base';
import Text from 'common/components/Text';
import { reactive, ref } from 'vue';
import { FormRules, MessagePlugin } from 'tdesign-vue-next';
import { SystemLabelItem } from 'common/service/creative/label/manage/type';
import { storeToRefs } from 'pinia';
import { useLabelsSystemStore } from '@/store/creative/labels/labels-systtem.store';
import { cloneDeep } from 'lodash-es';

const { createEditLabel, getTableData } = useLabelsSystemStore();
const { totalData } = storeToRefs(useLabelsSystemStore());

const visible = ref(false);
const formRef = ref();
const isCreate = ref(true);

const initFormData = {
  id: 0,
  name: '',
  required: false,
  multiple: false,
  label_type: 'creative',
  label_level: 'serial',
  label_method: 'manual',
  label_order: 0,
  options: '',
  optionList: [],
};

const formData = reactive<SystemLabelItem>(cloneDeep(initFormData));

const show = (data?: SystemLabelItem | number) => {
  if (typeof data === 'object') {
    Object.assign(formData, data);
    isCreate.value = false;
  } else {
    Object.assign(formData, cloneDeep(initFormData));
    formData.label_order = data as number;
    isCreate.value = true;
  }
  visible.value = true;
};

const rules: FormRules = {
  name: [
    { required: true, message: 'First Label is Required', type: 'error', trigger: 'blur' },
    { whitespace: true, message: 'First Label must not be empty' },
    {
      validator: (val: string) => {
        // 检查val是否已经存在。
        if (totalData.value.some(i => i.name === val.trim()) && isCreate.value) {
          return { result: false, message: 'this label has been used', type: 'error' };
        }
        return true;
      },
      trigger: 'blur',
    },
  ],
  optionList: [
    { required: true, message: 'Options must not be empty', type: 'error', trigger: 'blur' },
    {
      validator: (val: string) => {
        if (val.length === 0) return { result: false, message: 'Options must not be empty', type: 'error' };
        return true;
      },
    },
  ],
};

const onConfirm = async () => {
  const validateRes = await formRef.value.validate();
  if (validateRes !== true) return;

  const res = await createEditLabel(formData).catch((err) => {
    console.log('createEditLabel err', err);
    return [];
  });
  if (res.length > 0) {
    getTableData();
    visible.value = false;
  } else {
    MessagePlugin.error(isCreate.value ? 'create fail' : 'update fail');
  }
};

defineExpose({
  show,
});

</script>
<style scoped lang="scss"></style>
