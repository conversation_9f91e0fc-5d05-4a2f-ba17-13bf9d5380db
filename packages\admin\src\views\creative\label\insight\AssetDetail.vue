<template>
  <CommonView
    class="h-full"
    :hide-right="true"
    :need-back="false"
    :router-index="-2"
  >
    <template #views>
      <div class="creative-detail-tabs flex h-[48px] bg-gray-placeholder w-full rounded-t-lg overflow-hidden">
        <div
          v-for="tab in tabList"
          :key="tab.value"
          class="detail-tabs-item"
          :class="{'active': tab.value === curTab}"
          @click="changeTab(tab.value)"
        >
          {{ tab.name }}
        </div>
      </div>
      <div class="label-asset-detail flex h-full bg-white-primary overflow-hidden rounded-b-lg relative">
        <div class="flex flex-col h-ful w-[420px]">
          <div
            class="flex flex-col flex-[1] h-[100px] pl-[24px] pt-[24px] pr-[24px]"
            :class="{ 'border-b-[1px]': curTab === 'timeline' && (labelTimeline.length > 0 || !expandData) }"
          >
            <div class="w-full h-[280px] mb-[24px]">
              <video-viewer
                ref="videoInstance"
                class="h-full object-contain"
                :asset-type="commonInfo.asset_type"
                :video="{ url: videoUrl }"
                @timeupdate="onTimeUpdate"
              />
            </div>
            <CommonInfo :common-list="commonList" :label-list="labelList" />
          </div>
          <div
            v-if="curTab === 'timeline'"
            class="pt-[12px] px-[24px]"
            :style="{
              height: `${expandData ? bottomH : 0}px`,
              paddingBottom: `${expandData ? 24 : 12}px`
            }"
          >
            <traffic-source v-if="expandData" />
          </div>
        </div>
        <template v-if="curTab === 'timeline'">
          <div class="flex flex-1 py-[24px] pr-[24px]">
            <no-time-line-data v-if="labelTimeline.length === 0" />
            <label-time-line
              v-else
              :is-dragging="dragging"
              :show-retention="expandData"
              :bottom-h="bottomH"
            />
          </div>
          <div
            class="absolute flex flex-center trigger-expand w-[60px] h-[20px] border-[1px] rounded-large left-[50%]
           translate-x-[-50%] translate-y-[50%] bg-white-primary z-[11]"
            :style="{ bottom: expandData ? `${bottomH}px` : '24px'}"
            @click="triggerExpand"
          >
            <ChevronDownIcon v-if="expandData" size="20" />
            <ChevronUpIcon v-else size="20" />
          </div>
          <NoRetentionData
            v-if="expandData && !hasRateData"
            :show-border-top="labelTimeline.length === 0 && expandData"
            :yt-video-config="ytVideoConfig"
          />
        </template>
        <metric v-if="curTab === 'metric'" />
      </div>
    </template>
  </CommonView>
</template>
<script setup lang="ts">
import { computed, onMounted, watch, ref, onBeforeUnmount } from 'vue';
import { ChevronUpIcon, ChevronDownIcon } from 'tdesign-icons-vue-next';
import CommonView from 'common/components/Layout/CommonView.vue';
import LabelTimeLine from './components/LabelTimeLine.vue';
import VideoViewer from './components/VideoViewer.vue';
import { Timeline } from 'common/service/creative/label/insight/type';
import CommonInfo from './components/CommonInfo.vue';
import TrafficSource from './components/TrafficSource.vue';
import NoRetentionData from './components/NoRetentionData.vue';
import Metric from './components/detail/metric/Index.vue';
import { useLabelAssetDetailStore } from '@/store/creative/labels/labels-asset-detail.store';
import { useLabelsInsightStore } from '@/store/creative/labels/labels-insight.store';
import { useLabelRetentionStore } from '@/store/creative/labels/labels-retention.store';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { videoBus } from './utils';
import { useDebounceFn } from '@vueuse/core';
import './style.scss';
import { getVideoUrl } from '@/views/creative/label/manage/utils';
import { useGlobalGameStore } from '@/store/global/game.store';
import NoTimeLineData from './components/detail/NoTimeLineData.vue';

const { gameCode } = storeToRefs(useGlobalGameStore());
const bottomH = ref(240);
const { query } = useRoute();
const { getData, changeTab, setBase } = useLabelAssetDetailStore();
const { curTab, tabList, commonInfo, commonList, labelList, labelTimeline } = storeToRefs(useLabelAssetDetailStore());
const { getConfig } = useLabelRetentionStore();
const { hasRateData, ytVideoConfig } = storeToRefs(useLabelRetentionStore());
const { initOptions } = useLabelsInsightStore();
const { firstLabelList } = storeToRefs(useLabelsInsightStore());

const dragging = ref(false);  // 正在拖动中

const expandData = ref(false); // 控制跳出率数据展开收起

watch(() => [hasRateData.value, labelTimeline.value], ([val1, val2]) => {
  if (val1 && (val2 as Timeline[]).length > 0) {
    expandData.value = true;
  }
});

const triggerExpand = () => {
  expandData.value = !expandData.value;
};

const videoUrl = computed<string>(() => {
  if (commonInfo.value.url) return commonInfo.value.url;
  if (commonInfo.value.asset_type === 'VIDEO') return getVideoUrl(gameCode.value, commonInfo.value?.asset_id || '');
  return '';
});

const videoInstance = ref();
const onTimeUpdate = (time: number) => {
  if (!commonInfo.value) return;
  const duration = commonInfo.value.duration as number;
  const ratio = time / duration;
  videoBus.emit('time-ratio', ratio);
};

// 时间轴拖动，设置视频播放时间
const debouncedFn = useDebounceFn(() => {
  if (!commonInfo.value) return;
  const duration = commonInfo.value.duration as number;
  const player = videoInstance.value.getPlayer();
  const curTime = curRatio.value * duration;
  player.currentTime = curTime;
}, 200);

const curRatio = ref(0); // 鼠标移动的时间占比
const eventInstance = ref();  // 事件监听实例
onMounted(async () => {
  const {
    asset_name: assetName,
    asset_type: assetType,
    url,
    asset_serial_id: serialId,
    asset_id: assetId,
    youtube_id: youtubeId,
    index, code, sDate, eDate, filter,
  } = query;

  if (firstLabelList.value.length <= 1) {
    initOptions(); // 初始化标签数据
  }

  if (query.code) {
    changeTab(tabList.value[1].value); // 从top素材跳转过来，切到metric
  } else {
    changeTab(tabList.value[0].value);
  }

  setBase(sDate as string, eDate as string, assetName as string, assetType as string, url as string,
    serialId as string, assetId as string, youtubeId as string, index as string, filter as string);
  await getData(code as string | undefined);
  getConfig(query.youtube_id as string); // 获取视频配置信息

  // 监听时间轴拖动事件，同步修改视频的播放进度
  eventInstance.value = videoBus.on((event, ratio: number) => {
    if (event === 'start-scroll') {
      const player = videoInstance.value.getPlayer();
      dragging.value = true;
      player.pause();
    }
    if (event === 'on-scroll') {
      curRatio.value = ratio;
      debouncedFn();
    }
    if (event === 'on-scroll-end') {
      dragging.value = false;
    }
  });
});

onBeforeUnmount(() => {
  eventInstance.value();
});
</script>
<style lang="scss" scoped>
.creative-detail-tabs {
  .detail-tabs-item {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px;
    cursor: pointer;
  }
  .active {
    background: white;
    color: var(--aix-bg-color-brand);
  }
}
</style>
