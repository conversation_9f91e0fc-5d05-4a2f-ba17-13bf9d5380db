<template>
  <CommonView
    class="creative-labels-insight"
    :form-props="{
      formList,
      modelValue: formModelValue,
      onSubmit,
      foldList,
      onReset,
      'onUpdate:modelValue': updateFormModelValue,
      'onUpdate:foldList': setFoldList,
      isShowGlobalLoading: isViewLoading || isInitLoading,
      confirmDisabled: isViewLoading || isLineChartLoading || isTableLoading,
      resetDisabled: isViewLoading || isLineChartLoading || isTableLoading,
    }"
    :tab-props="{
      list: viewList,
      modelValue: viewId,
      shareParams: shareViewParams,
      'onUpdate:modelValue': updateViewId,
      isShowViewType: false,
      onAddView: addView,
      onUpdateView: updateView,
      onDeleteView: deleteView,
    }"
  >
    <template #views>
      <div class="flex flex-col px-[16px] bg-white-primary rounded-extraLarge">
        <div v-if="false"><t-button @click="gotoLabelInsightDetail">Label Detail</t-button></div>
        <div class="flex flex-col overflow-hidden">
          <t-tabs v-model="labelType" size="medium">
            <t-tab-panel
              v-for="item in firstLabelList"
              :key="item.value"
              :value="item.value"
              :label="`${item.label}${item.label_method === 'intelligent' ? ' (Intelligent)' : ''}`"
              :disabled="isViewLoading || isLineChartLoading || isTableLoading"
            />
          </t-tabs>
          <label-charts />
          <div class="flex justify-end my-[4px]">
            <t-button
              variant="text"
              @click="changeMetrics"
            >
              <template #icon><view-column-icon /></template>Metrics
            </t-button>
            <t-button
              variant="text"
              :loading="downloading"
              @click="download"
            >
              <template #icon><download-icon /></template>Download
            </t-button>
            <div class="flex items-center ml-[4px]">
              <t-tooltip>
                <template #content>
                  <div>On: Include labeled & unlabeled</div>
                  <div>Off: Labeled only</div>
                </template>
                <InfoCircleIcon class="h-[16px]" />
              </t-tooltip>
              <span class="ml-[4px]">Include Unlabeled</span>
              <t-switch
                v-model="isFilterUnlabeled" :disabled="isTableLoading || labelFilterSelected" size="small"
                class="ml-[6px]"
                @change="onFilterUnlabeledChange"
              />
            </div>
          </div>
          <InsightTable />
        </div>
      </div>
    </template>
  </CommonView>
  <CustomizeColumnsDialog
    v-if="colDialogList?.length > 0"
    v-model:visible="colDialogVisible"
    type="group"
    :min-count="3"
    :list="colDialogList"
    :selected-list="colCheckedList"
    @confirm="changeCheckedColList"
  />
</template>
<script setup lang="ts">
import './style.scss';
import { ref, computed, watch } from 'vue';
import { DownloadIcon, ViewColumnIcon, InfoCircleIcon } from 'tdesign-icons-vue-next';
import CommonView from 'common/components/Layout/CommonView.vue';
import LabelCharts from './components/LabelCharts.vue';
import CustomizeColumnsDialog from 'common/components/CustomizeColumnsDialog';
import { useGoto } from '@/router/goto';
import { storeToRefs } from 'pinia';
import { useLabelsInsightStore } from '@/store/creative/labels/labels-insight.store';
import InsightTable from './components/InsightTable.vue';
import { useDownloadFile } from 'common/compose/download-file';
import { FormModel } from 'common/service/creative/label/insight/type';
import { useGlobalGameStore } from '@/store/global/game.store';
import { formatVal } from './utils';
import { useWatchGameChange } from 'common/compose/request/game';
import { useLabelsInsightChartStore } from '@/store/creative/labels/labels-insight-chart.store';
import { DOWNLOAD_METRICS } from '@/store/creative/labels/const';

const { gotoLabelInsightDetail } = useGoto();
const store = useLabelsInsightStore();
const {
  labelType,
  formList,
  formModelValue,
  firstLabelList,
  downloading,
  tableColumns,
  colCheckedList,
  foldList,
  viewId,
  viewList,
  shareViewParams,
  isViewLoading,
  colDialogList,
  allMetrics,
  isInitLoading,
  isTableLoading,
  isFilterUnlabeled,
} = storeToRefs(store);
const {
  getTableData,
  onReset,
  downloadAll,
  changeCheckedColList,
  setViewId,
  updateFormAndTagModelValue,
  addView,
  updateView,
  deleteView,
  init,
  setFoldList,
  getViewItem,
  onSubmit,
} = store;
const { getLabelInsightLineChartData, getBubbleLabelsData } = useLabelsInsightChartStore();
const { isLineChartLoading } = storeToRefs(useLabelsInsightChartStore());
const gameStore = useGlobalGameStore();

// 页面夹在或者切换游戏时会执行
useWatchGameChange(async () => {
  init();
});

// 更新formModelValue数据
const updateFormModelValue = (formModel: FormModel) => {
  formModelValue.value = {
    ...formModelValue.value,
    ...formModel,
  };
};

const updateViewId = (id: string) => {
  setViewId(id);
  const viewItem = getViewItem(id);
  updateFormAndTagModelValue(viewItem);
  getTableData();
  getLabelInsightLineChartData();
  getBubbleLabelsData();
};

const labelFilterSelected = computed(() => formModelValue.value.label.labelList?.length > 0);
watch(() => formModelValue.value, (val: FormModel) => {
  if (val.label.labelList?.length > 0) {
    isFilterUnlabeled.value = false;
  }
});

const formatColumns = computed(() => tableColumns.value
  .map(item => item.colKey)
  .filter(item => !['top', 'label', 'spend', 'asset_num', 'installs'].includes(item)),
);

const colDialogVisible = ref(false);

// 修改选中metrics
const changeMetrics = () => {
  colDialogVisible.value = true;
};

// 切换Unlabeled，从新获取表格数据
const onFilterUnlabeledChange = () => {
  getTableData();
};

// 导出excel文件
const download = async () => {
  const data = await downloadAll();
  const metricTitles = colCheckedList.value.map((item) => {
    const metric = allMetrics.value.find(m => m.value === item.colKey);
    return metric!.label as string;
  });
  let headers = ['Label'];

  // 处理特殊指标
  const indexTitles = [] as string[];
  DOWNLOAD_METRICS.forEach(({ title, cols }) => {
    const index = metricTitles.indexOf(title);
    if (index !== -1) {
      indexTitles.push(title);
      metricTitles.splice(index, 1);
      headers = headers.concat(cols);
    }
  });
  // 添加剩余常规指标
  headers = headers.concat(metricTitles);

  const xlsData: string[][] = [headers];
  data.forEach((item, index) => {
    let lineData: string[] = [];
    const label = index === 0 ? 'Total' : `${item.first_label}---${item.second_label}`;
    lineData.push(label);

    indexTitles.forEach((title) => {
      const { keys = [] } = DOWNLOAD_METRICS.find(metric => metric.title === title) || {};
      if (keys?.length > 0) {
        const key = keys[0];
        const keyRate = keys[1];
        const secParam = index === 0 ? '100%' : formatVal((item as any)[keyRate], keyRate, allMetrics.value);
        lineData = lineData.concat([formatVal((item as any)[key], key), secParam]);
      }
    });

    const other = formatColumns.value.map(col => formatVal((item as any)[col], col, allMetrics.value));
    xlsData.push([...lineData, ...other]);
  });
  useDownloadFile(
    [{ sheet: 'Sheet1', list: xlsData }],
    `${gameStore.gameCode}-label-Insight-${new Date().toLocaleDateString()}.xlsx`,
    {
      mode: 'group',
      isSetHeader: true,
    },
  );
};
</script>
<style lang="scss">
.creative-labels-insight {
  .t-is-top::after {
    display: none;
  }
}
</style>
