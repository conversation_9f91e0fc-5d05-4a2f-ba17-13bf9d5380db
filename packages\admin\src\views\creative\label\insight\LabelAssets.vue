<template>
  <CommonView
    :hide-right="true"
    :need-back="history.length > 1"
    :router-index="-2"
  >
    <template #views>
      <div class="bg-white-primary rounded-large p-[16px] pb-0">
        <div class="labels-content mb-[12px] flex flex-wrap">
          <div
            v-for="(label, index) in labelList"
            :key="index"
            class="flex"
          >
            <div
              v-if="index > 0"
              class="text-gray-primary mx-[12px]"
            >
              |
            </div>
            <div>{{ label.split('---')[1] }}</div>
            <div class="text-gray-primary text-[12px]">({{ label.split('---')[0] }})</div>
          </div>
        </div>
        <NewMetricTrendLineChart
          class="w-full h-[320px]"
          left-top-label=""
          :metric="lineChartMetric"
          :dtstatdate="lineChartDtstatdate"
          :table-data="lineChartData"
          :pick-fields="[lineChartMetric]"
          :metric-list="metricList"
          :field-labels="allMetricLabels"
          :is-show-legend="false"
          :area-style-color="{
            [lineChartMetric]: ['#f7f9fe', 'rgb(236, 242, 254)'],
          }"
          :is-loading="isLineChartLoading"
          :tooltip-value-format="lineChartTooltipValueFormat"
          @update:metric="updateLineChartMetric"
          @update:dtstatdate="updateLineChartDtstatdate"
        />
        <div class="bg-white-primary mt-[16px] flex items-center justify-end">
          <div class="flex items-center mb-[12px]">
            <t-button
              variant="text"
              @click="changeMetrics"
            >
              <template #icon><view-column-icon /></template>Metrics
            </t-button>
            <t-button
              variant="text" :loading="isDownloadLoading"
              @click="() => downloadExcel()"
            >
              <template #icon><download-icon /></template>Download
            </t-button>
          </div>
        </div>
        <asset-table />
      </div>
    </template>
  </CommonView>
  <CustomizeColumnsDialog
    v-if="colDialogList?.length > 0"
    v-model:visible="colDialogVisible"
    type="group"
    :min-count="3"
    :list="colDialogList"
    :selected-list="colCheckedList"
    @confirm="changeCheckedColList"
  />
</template>
<script setup lang="ts">
import './style.scss';
import { ref, onMounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import CommonView from 'common/components/Layout/CommonView.vue';
import AssetTable from './components/AssetTable.vue';
import { useLabelAssetsStore } from '@/store/creative/labels/labels-assets.store';
import { useRoute } from 'vue-router';
import { ViewColumnIcon, DownloadIcon } from 'tdesign-icons-vue-next';
import CustomizeColumnsDialog from 'common/components/CustomizeColumnsDialog/index';
import NewMetricTrendLineChart from '@/views/creative/common/components/NewMetricTrendLineChart.vue';
import { getLabelDetailTrendData } from 'common/service/creative/label/insight';
import { useLoading } from 'common/compose/loading';
import { formatVal } from '@/views/creative/label/insight/utils';
import { isIgnoreMetrics } from '@/store/creative/top/utils';

const {
  isLoading: isLineChartLoading,
  hideLoading: hideLineChartLoading,
  showLoading: showLineChartLoading,
} = useLoading(true);
const { history } = window;
const { query } = useRoute();

const { initOptionsLabelsAssets, setParams, getTopData, changeCheckedColList, downloadExcel } = useLabelAssetsStore();
const { colDialogList, colCheckedList, allMetrics, isDownloadLoading } = storeToRefs(useLabelAssetsStore());

const colDialogVisible = ref(false);

const { index: metric, labels, filter, serialIds } = query as Record<string, string>;

// url中的过滤条件
const params = JSON.parse((filter as string) || '[]');
const labelList = JSON.parse((labels as string) || '[]');
const serialList = JSON.parse((serialIds as string) || '[]');

// 这线图的metric
const lineChartMetric = ref<string>('spend');
// 折线图选择的时间
const lineChartDtstatdate = ref([params.startDate, params.endDate]);

const setIndex = () => {
  if (!isIgnoreMetrics(metric) && metric) {
    lineChartMetric.value = metric;
  }
};

const updateLineChartMetric = (val: string) => {
  lineChartMetric.value = val;
  getLineData();
};
const updateLineChartDtstatdate = (val: string[]) => {
  lineChartDtstatdate.value = val;
  getLineData();
};

// 折线图的数据
const lineChartData = ref<Record<string, any>[]>([]);
const metricList = computed(() => allMetrics.value.filter(item => item.key !== 'asset_num'));
const allMetricLabels = computed(() => {
  const list = metricList.value;
  return list.reduce<Record<string, string>>(
    (obj, item) => (item.value ? { ...obj, [item.value as string]: item.label as string } : obj),
    {},
  );
});

// 对折线图中 tooltip中的值进行格式化
const lineChartTooltipValueFormat = (value: number) => formatVal(value, lineChartMetric.value, allMetrics.value);

// 修改选中metrics
const changeMetrics = () => {
  colDialogVisible.value = true;
};

async function getLineData() {
  showLineChartLoading();
  const res = await getLabelDetailTrendData({
    startDate: lineChartDtstatdate.value[0],
    endDate: lineChartDtstatdate.value[1],
    metric: [lineChartMetric.value],
    orderby: [{ by: 'dtstatdate', order: 'ASC' }],
    group: ['dtstatdate'],
    labels: labelList,
  });
  hideLineChartLoading();
  lineChartData.value = res;
}

onMounted(async () => {
  await initOptionsLabelsAssets();
  setIndex();
  setParams(labelList, serialList, params);
  Promise.all([getTopData(), getLineData()]);
});
</script>
