<template>
  <t-table
    class="creative-labels-asset__table flex flex-col"
    row-key="asset_name"
    resizable
    :loading="isLoading"
    :columns="tableColumns"
    :sort="tableSort"
    :data="tableData"
    @sort-change="onSortChange"
  >
    <template #preview="{ row }">
      <template v-if="!row.is_total">
        <t-image
          :src="row.preview_url"
          fit="contain"
          class="w-[60px] h-[42px] bg-[#eee] cursor-pointer"
          @click="videoPreview(row)"
        >
          <template #error>
            <image-error-icon size="24" />
          </template>
          <template #loading>
            <t-loading size="small" />
          </template>
        </t-image>
      </template>
    </template>
    <template #asset_name="{ row }">
      <template v-if="row.is_total">
        <span class="font-bold">Total</span>
      </template>
      <template v-else>
        <Text
          :content="row.asset_name"
          :overflow="true"
          theme="primary"
          :need-cursor="true"
          :tool-tip="true"
          tips-placement="top-left"
          @click="goToAsset(row)"
        />
      </template>
    </template>
    <template
      v-for="col in formatColumns"
      :key="col"
      #[col]="{ row }"
    >
      <span>{{ formatVal(row[col], col, allMetrics) }}</span>
    </template>
    <template
      v-if="specialColumns.includes('spend')"
      #spend="{ row }"
    >
      <rate-column
        :first-line="!!row.is_total"
        col="spend"
        :row="row"
      />
    </template>
    <template
      v-if="specialColumns.includes('installs')"
      #installs="{ row }"
    >
      <rate-column
        :first-line="!!row.is_total"
        col="installs"
        :row="row"
        status="success"
      />
    </template>
  </t-table>
  <t-pagination
    v-model:current="pageIndex"
    v-model:page-size="pageSize"
    class="my-[24px]"
    :total="totalCount"
    size="small"
    :page-size-options="[10, 20, 50]"
    @page-size-change="onPageSizeChange"
    @current-change="onPageIndexChange"
  />
  <Preview
    ref="previewRef"
    :type="previewItem.type"
    :title="previewItem.name"
    :hide-trigger="true"
    :get-url="getUrl"
  />
</template>
<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { formatVal } from '@/views/creative/label/insight/utils';
import Text from 'common/components/Text';
import Preview from 'common/components/Dialog/Preview';
import { ImageErrorIcon } from 'tdesign-icons-vue-next';
import { useLabelAssetsStore } from '@/store/creative/labels/labels-assets.store';
import { storeToRefs } from 'pinia';
import { SortInfo } from 'tdesign-vue-next';
import { LabelAssetFilter, LabelAssetRes } from 'common/service/creative/label/insight/type';
import { useGoto } from '@/router/goto';
import RateColumn from '@/views/creative/label/insight/components/RateColumn.vue';
import { useGlobalGameStore } from '@/store/global/game.store';
import { getVideoUrl } from '../../manage/utils';

const { gotoLabelAssetDetail } = useGoto();
const { gameCode } = storeToRefs(useGlobalGameStore());

const { getTopData, defaultSort, onPageSizeChange, onPageIndexChange } = useLabelAssetsStore();
const {
  isLoading, tableColumns, tableSort, tableData, pageIndex, pageSize, totalCount, filterParams, allMetrics,
} = storeToRefs(useLabelAssetsStore());

const specialColumns = ['spend', 'asset_num', 'installs'];
const formatColumns = computed(() => tableColumns.value
  .map(item => item.colKey)
  .filter(item => !['preview', 'asset_name', ...specialColumns].includes(item)),
);

const onSortChange = (val: SortInfo | SortInfo[]) => {
  if (!val) tableSort.value = defaultSort; // 重复点击，返回undefined，置空
  else tableSort.value = val instanceof Array ? val : [val];
  getTopData();
};

// 跳转到素材详情页
const goToAsset = (row: LabelAssetRes) => {
  const filter = filterParams.value as LabelAssetFilter;
  const params = {
    keywords: filter.keywords,
    campaign_type: filter.campaign_type,
    country_code: filter.country_code,
    impression_date: filter.impression_date,
    network: filter.network,
    platform: filter.platform,
    asset_type: filter.asset_type,
  };
  gotoLabelAssetDetail({
    game: gameCode.value,
    sDate: filter.startDate!,
    eDate: filter.endDate!,
    asset_name: row.asset_name,
    asset_serial_id: row.asset_serial_id,
    asset_id: row.asset_id,
    youtube_id: row.youtube_id,
    asset_type: row.asset_type as string,
    url: row.asset_type === 'IMAGE' ? row.preview_url : '',
    filter: JSON.stringify(params),
  });
};

const previewItem = reactive<{
  type: 'image' | 'video';
  asset_id: string;
  name: string;
  preview_url?: string;
}>({
  type: 'image',
  asset_id: '',
  name: '',
});
const previewRef = ref();
const videoPreview = async (row: LabelAssetRes) => {
  previewItem.type = row.asset_type.toLowerCase() as 'image' | 'video';
  previewItem.name = row.asset_name;
  previewItem.asset_id = row.asset_id;
  previewItem.preview_url = row.preview_url;
  previewRef.value.show();
};

const getUrl = async () => {
  if (previewItem.type === 'video') {
    return getVideoUrl(gameCode.value, previewItem.asset_id);
  }
  return previewItem.preview_url as string;
};
</script>
