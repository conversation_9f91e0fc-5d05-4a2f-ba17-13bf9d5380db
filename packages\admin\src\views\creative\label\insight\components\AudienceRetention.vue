<template>
  <div v-if="ytVideoConfig?.maxDateRetention" class="flex flex-col h-full">
    <div class="flex justify-between bg-white-primary py-[12px] pr-[12px]">
      <div class="flex flex-center text-bold text-base">
        <span class="mr-[12px]">Audience Retention</span>
        <span class="text-black-placeholder text-xs">
          Last Updated: {{ lastUpdatedDate }}
        </span>
      </div>
      <div v-if="hasChartData" class="flex items-center">
        <div
          v-for="(item, index) in indexList" :key="index"
          class="flex flex-center mr-[18px] cursor-pointer"
          @click="triggerShowIndex(index)"
        >
          <div
            class="w-[10px] h-[10px] mr-[8px] rounded-full"
            :style="{
              backgroundColor: item.show ? colors[index] : '#666',
            }"
          />
          <div :style="{ color: item.show ? '#000' : '#666' }">{{ item.name }}</div>
        </div>
        <t-tooltip>
          <template #content>
            <div v-for="(item, index) in indexList" :key="index" class="mb-[12px]">
              <span class="font-bold">{{ item.name }}: </span>
              <span>{{ item.tip }}</span>
            </div>
          </template>
          <HelpCircleIcon size="14" class="ml-[4px]" />
        </t-tooltip>
      </div>
      <div>
        <t-date-picker
          v-model="formData.date"
          class="w-[150px]"
          :disable-date="{ before: minDate, after: maxDate }"
          @change="getData"
        />
      </div>
    </div>
    <div class="flex flex-[1]">
      <div>
        <div class="p-[8px] w-[94px] bg-[#E5EDFD] rounded-lg">
          <div class="text-gray-primary mb-[6px]">Avg View</div>
          <div class="flex justify-between text-[12px]">
            <div>{{ avgVal }}s</div>
            <div class="text-black-placeholder">{{ avgRate.toFixed(2) }}%</div>
          </div>
        </div>
      </div>
      <div v-show="hasChartData" class="flex flex-[1]">
        <div class="retention-chart flex-[1] h-full" />
        <div class="flex" :style="{ width: `${emptyWidth * 0.8 - 8}px` }" />
      </div>
      <div v-show="!hasChartData" class="flex flex-[1] flex-center text-gray-primary">
        {{ tip }}
      </div>
    </div>
  </div>
  <div v-else class="flex h-full flex-center text-gray-primary">
    {{ tip }}
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, computed, onUnmounted } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import { HelpCircleIcon } from 'tdesign-icons-vue-next';
import * as echarts from 'echarts';
import { EChartsOption } from 'echarts';
import { cloneDeep } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { getAudienceRetention } from 'common/service/creative/label/insight';
import { useLabelRetentionStore } from '@/store/creative/labels/labels-retention.store';
import { useGlobalGameStore } from '@/store/global/game.store';
import dayjs from 'dayjs';

defineProps<{
  emptyWidth: number
}>();

// 覆盖原来xAxis定义，否则会报xAxis.data不存在
interface ChartOpt extends EChartsOption {
  xAxis: {
    show: boolean,
    boundaryGap: boolean,
    data: string[],
  },
}

const emit = defineEmits(['showTip', 'hideTip']);

const { currentGameItem } = storeToRefs(useGlobalGameStore());
const { ytVideoConfig, youtubeId } = storeToRefs(useLabelRetentionStore());

const tip = 'Not enough views to display audience retention data.';

const lastUpdatedDate = computed(() => dayjs(ytVideoConfig.value?.maxDateRetention).format('YYYY-MM-DD'));

const minDate = computed(() => dayjs(ytVideoConfig.value!.minDate));
const maxDate = computed(() => dayjs(ytVideoConfig.value!.maxDateRetention));

const formData = reactive({
  date: ytVideoConfig.value!.maxDateRetention,
});

const avgVal = ref(0);
const avgRate = ref(0);

const colors = ['#699ef5', '#f2d127', '#f36d78'];

const indexList = ref([
  {
    index: 'video',
    name: 'The Video',
    tip: 'Show audience retention with this video at different moments. May exceed 100% if viewers rewind or re-watch.',
    show: true,
  },
  {
    index: 'game',
    name: `${currentGameItem.value.connect} Retention`,
    tip: 'Benchmark the game’s audience retention by averaging data from all similar-length videos.',
    show: false,
  },
  {
    index: 'youtube',
    name: 'YTB Retention',
    tip: 'Compare this video\'s retention during playback to similar-length YouTube videos,'
      + ' with a metric ranging from 0 to 1. A median value of 0.5 indicates average performance,'
      + ' with 0 indicating worse retention and 1 indicating better retention.',
    show: false,
  },
]);

const times = ref<string[]>([]); // x轴时间点
const dataList = ref<number[][]>([]); // 曲线数据

// 折线图基础配置
const baseOptions: ChartOpt = {
  title: {
    show: false,
  },
  tooltip: {
    trigger: 'axis',
    appendToBody: true,
    axisPointer: {
      type: 'none',
    },
    backgroundColor: 'rgba(0,0,0,0.8)',
    padding: 12,
    textStyle: {
      color: '#fff',
    },
    valueFormatter: value => `${value}%`,
  },
  grid: {
    left: 10,
    right: 6,
    bottom: 0,
    top: 8,
    containLabel: true,
  },
  xAxis: {
    show: false,
    boundaryGap: false,
    data: [],
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}%', // 给Y轴添加百分号后缀
    },
  },
  series: [],
};

const triggerShowIndex = (index: number) => {
  const target = indexList.value[index];
  target.show = !target.show;
  renderChart();
};

let myChart: echarts.ECharts;
const renderChart = () => {
  const chartDom = document.querySelector('.retention-chart') as HTMLDivElement;
  myChart = echarts.init(chartDom);

  // 监听 tooltip 显示/隐藏 事件
  myChart.on('showTip', (data: any) => {
    const hoverTime = times.value[data.dataIndex];
    emit('showTip', hoverTime);
  });
  myChart.on('hideTip', () => {
    emit('hideTip');
  });

  // 根据选中哪些指标，设置series
  const series: EChartsOption['series'] = indexList.value.filter(item => item.show).map((item) => {
    const targetIndex = indexList.value.findIndex(i => i.index === item.index);

    return {
      name: item.name,
      type: 'line',
      showSymbol: false,
      smooth: true,
      itemStyle: { color: colors[targetIndex] },
      data: dataList.value[targetIndex],
    };
  });

  const chartOptions = cloneDeep(baseOptions);
  chartOptions.xAxis.data = times.value;
  chartOptions.series = series;

  myChart.setOption(chartOptions, true);  // 不做合并，直接替换
};

const hasChartData = ref(true);
const getData = async () => {
  if (!formData.date) return;
  const res = await getAudienceRetention({
    start_date: formData.date.replaceAll('-', ''),
    youtube_id: youtubeId.value,
  });
  avgVal.value = res.avg_view_duration;
  avgRate.value = res.avg_view_rate;

  times.value = res.times;

  hasChartData.value = res.times.length > 0;  // 通过times判断是否有数据

  if (hasChartData.value) {
    const videoRates = res.video_rates.map(num => +(num * 100).toFixed(2));
    const gameRates = res.game_rates.map(num => +(num * 100).toFixed(2));
    const ytbRates = res.ytb_rates.map(num => +(num * 100).toFixed(2));
    dataList.value = [videoRates, gameRates, ytbRates];

    renderChart();
  }
};

onMounted(() => {
  getData();

  window.onresize = useDebounceFn(() => {
    if (myChart) {
      myChart.dispose();
      renderChart();
    }
  }, 500);
});

onUnmounted(() => {
  window.onresize = null;
});
</script>
