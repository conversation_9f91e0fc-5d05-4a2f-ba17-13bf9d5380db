<template>
  <div class="common-info h-[100px] flex-[1] pr-[12px] overflow-y-auto overflow-x-hidden">
    <div class="text-[18px] mb-4">Common Info</div>
    <div v-for="(item, index) in commonList" :key="index" class="flex w-full mb-2">
      <template v-if="item.value">
        <div class="min-w-[130px] text-gray-400">{{ item.label }}</div>
        <div class="flex flex-wrap">
          <span class="mr-2 mb-2 break-all">{{ item.value }}</span>
        </div>
      </template>
    </div>
    <div v-for="(item, index) in labelList" :key="index" class="flex w-full mb-2">
      <div class="min-w-[130px] text-gray-400">{{ item.first_label }}</div>
      <div class="flex flex-wrap">
        <t-tag
          v-for="(label, idx) in item.second_label" :key="idx"
          theme="primary" variant="light" max-width="240"
          class="mr-2 mb-2" :title="label"
        >
          {{ label }}
        </t-tag>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Label } from 'common/service/creative/label/insight/type';

defineProps<{
  commonList: {
    label: string, value: string | number,
  }[],
  labelList: Label[],
}>();
</script>
