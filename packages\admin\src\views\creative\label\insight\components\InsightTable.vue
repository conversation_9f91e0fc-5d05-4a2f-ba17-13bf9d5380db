<template>
  <div class="flex flex-col h-[200px] flex-1">
    <t-table
      class="creative-labels-insight__table flex flex-col"
      :loading="isTableLoading"
      :columns="tableColumns"
      :sort="tableSort"
      resizable
      row-key="index"
      :data="tableData"
      @sort-change="onSortChange"
    >
      <template #top="{ row }">
        <div v-if="row.top !== -1">
          {{ row.top }}
        </div>
      </template>
      <template #label="{ row }">
        <template v-if="row.top">
          <Text
            class="w-full block truncate"
            :class="{ 'cursor-pointer': row.top !== -1, '!text-brand': row.top !== -1 }"
            type="subTitle"
            size="normal"
            :content="row.second_label"
            :overflow="true"
            :tool-tip="true"
            :tips-content="row.second_label"
            @click="goToDetail(row)"
          />
          <div class="text-gray-primary">{{ row.first_label }}</div>
        </template>
        <div
          v-else
          class="font-bold"
        >
          Total
        </div>
      </template>
      <template
        v-for="col in formatColumns"
        :key="col"
        #[col]="{ row }"
      >
        <span>{{ formatVal(row[col], col, allMetrics) }}</span>
      </template>
      <template
        v-if="specialColumns.includes('spend')"
        #spend="{ row }"
      >
        <rate-column
          :first-line="!row.top"
          col="spend"
          :row="row"
        />
      </template>
      <template
        v-if="specialColumns.includes('asset_num')"
        #asset_num="{ row }"
      >
        <rate-column
          :first-line="!row.top"
          col="asset_num"
          :row="row"
          status="warning"
        />
      </template>
      <template
        v-if="specialColumns.includes('installs')"
        #installs="{ row }"
      >
        <rate-column
          :first-line="!row.top"
          col="installs"
          :row="row"
          status="success"
        />
      </template>
    </t-table>
    <t-pagination
      v-model:current="pageIndex"
      v-model:page-size="pageSize"
      class="my-[24px]"
      :total="totalCount"
      size="small"
      :page-size-options="[10, 20, 50]"
      @page-size-change="onPageSizeChange"
      @current-change="onPageIndexChange"
    />
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import { SortInfo } from 'tdesign-vue-next';
import { useLabelsInsightStore } from '@/store/creative/labels/labels-insight.store';
import { storeToRefs } from 'pinia';
import { formatVal } from '../utils';
import { useGoto } from '@/router/goto';
import { TableRes } from 'common/service/creative/label/insight/type';
import RateColumn from './RateColumn.vue';
import Text from 'common/components/Text';

const { gotoLabelInsightDetail } = useGoto();

const { getTableData, onPageSizeChange, onPageIndexChange, getParams, defaultSort } = useLabelsInsightStore();
const { tableData, totalCount, pageIndex, pageSize, tableColumns, isTableLoading, tableSort, allMetrics } = storeToRefs(
  useLabelsInsightStore(),
);

const specialColumns = ['spend', 'asset_num', 'installs'];
const formatColumns = computed(() => tableColumns.value.map(item => item.colKey).filter(item => !['top', 'label', ...specialColumns].includes(item)),
);

const onSortChange = (val: SortInfo | SortInfo[]) => {
  if (!val) tableSort.value = defaultSort; // 重复点击，返回undefined，置空
  else tableSort.value = val instanceof Array ? val : [val];
  getTableData();
};

const goToDetail = (row: TableRes) => {
  if (row.top === -1) return;
  const params = getParams();
  const filter = {
    startDate: params.startDate,
    endDate: params.endDate,
    keywords: params.keywords,
    campaign_type: params.campaign_type,
    country_code: params.country_code,
    impression_date: params.impression_date,
    network: params.network,
    label_search_type: params.label_search_type,
    platform: params.platform,
    asset_type: params.asset_type,
  };
  gotoLabelInsightDetail({
    labels: JSON.stringify([`${row.first_label}---${row.second_label}`]),
    filter: JSON.stringify(filter), // 查询条件
  });
};
</script>
