<template>
  <div class="label-charts flex h-[420px]">
    <div class="flex flex-[1] border-[1px] border-[#eee] rounded-default mr-[12px] p-[16px] w-[50%]">
      <BubbleChart
        title=""
        :metric-x="bubbleMetricX"
        :metric-y="bubbleMetricY"
        :metric-list-x="metricListX"
        :metric-list-y="metricListY"
        :avg-x="avgX"
        :avg-y="avgY"
        :label-data="bubbleLabelData"
        :is-loading="bubbleLoading"
        :value-format="valueFormatWithMetric"
        @update:metric-x="updateBubbleMetricX"
        @update:metric-y="updateBubbleMetricY"
        @label-click="onLabelClick"
      />
    </div>
    <NewMetricTrendLineChart
      class="w-[50%]"
      left-top-label=""
      :metric="lineChartMetric"
      :dtstatdate="lineChartDtstatdate"
      :table-data="lineChartData"
      :pick-fields="secondLabelList"
      :metric-list="metricList"
      :is-loading="isLineChartLoading"
      :tooltip-value-format="tooltipValueFormat"
      legend-placement="bottom"
      legend-sort-desc-by-frist-day
      @update:metric="onUpdateLineChartMetric"
      @update:dtstatdate="onUpdateLineChartDtstatdate"
    />
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import NewMetricTrendLineChart from '@/views/creative/common/components/NewMetricTrendLineChart.vue';
import BubbleChart from '@/views/creative/common/components/BubbleChart.vue';
import { BubbleDataItem } from '@/views/creative/common/components/type';
import { useLabelsInsightStore } from '@/store/creative/labels/labels-insight.store';
import { useLabelsInsightChartStore } from '@/store/creative/labels/labels-insight-chart.store';
import { formatVal } from '@/views/creative/label/insight/utils';
import { useGoto } from '@/router/goto';

const { gotoLabelInsightDetail } = useGoto();

const labelsInsightStore = useLabelsInsightStore();
const labelsInsightChartStore = useLabelsInsightChartStore();
const { allMetrics } = storeToRefs(labelsInsightStore);
const {
  updateLineChartMetric,
  updateLineChartDtstatdate,
  getLabelInsightLineChartData,
  updateBubbleMetricX,
  updateBubbleMetricY,
} = labelsInsightChartStore;
const {
  lineChartData,
  secondLabelList,
  lineChartMetric,
  lineChartDtstatdate,
  isLineChartLoading,
  bubbleMetricX,
  bubbleMetricY,
  avgX,
  avgY,
  bubbleLabelData,
  bubbleLoading,
} = storeToRefs(labelsInsightChartStore);

const metricList = computed(() => allMetrics.value.filter(item => item.key !== 'asset_num'));

const onUpdateLineChartMetric = (val: string) => {
  updateLineChartMetric(val);
  getLabelInsightLineChartData();
};

const onUpdateLineChartDtstatdate = (val: string[]) => {
  console.log('val', val);
  updateLineChartDtstatdate(val);
  getLabelInsightLineChartData();
};

const tooltipValueFormat = (value: any) => formatVal(value, lineChartMetric.value, allMetrics.value);

const valueFormatWithMetric = (value: number, metric: string) => formatVal(value, metric, allMetrics.value);

const metricListX = computed(() => {
  if (!metricList.value) return [];
  return metricList.value.map(item => ({
    ...item,
    disabled: item.value === bubbleMetricY.value, // 禁用Y轴的值
  }));
});

const metricListY = computed(() => {
  if (!metricList.value) return [];
  return metricList.value.map(item => ({
    ...item,
    disabled: item.value === bubbleMetricX.value, // 禁用X轴的值
  }));
});

const onLabelClick = (data: BubbleDataItem) => {
  const [firstLabel, secondLabel] = data.totalName.split('|');
  const { getParams } = labelsInsightStore;
  const params = getParams();
  const filter = {
    startDate: params.startDate,
    endDate: params.endDate,
    keywords: params.keywords,
    campaign_type: params.campaign_type,
    country_code: params.country_code,
    impression_date: params.impression_date,
    network: params.network,
    label_search_type: params.label_search_type,
    platform: params.platform,
    asset_type: params.asset_type,
  };
  gotoLabelInsightDetail({
    labels: JSON.stringify([`${firstLabel}---${secondLabel}`]),
    filter: JSON.stringify(filter), // 查询条件
  }, true);
};
</script>
