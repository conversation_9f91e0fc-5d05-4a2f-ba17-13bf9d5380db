<template>
  <div
    class="pl-[20px] pr-[16px] mb-[16px] border-l-[3px] relative"
    :style="{ borderColor: item.color }"
    @mouseenter="onMouseEnter"
    @mouseleave="onMouseLeave"
  >
    <Text
      class="w-full block truncate"
      type="subTitle" size="normal" :content="item.second_label as string"
      :overflow="true" :tool-tip="true"
      :tips-content="item.second_label as string"
    />
    <Text
      class="w-full block truncate"
      color="#747D98" :content="item.first_label"
      :tool-tip="true" :overflow="true"
      :tips-content="item.second_label as string"
    />
    <div class="more-action absolute right-[2px] top-[0] cursor-pointer">
      <t-dropdown
        v-if="showAction"
        v-model:visible="showActionBox"
        :options="actionOptions"
        trigger="click"
        :popup-props="{
          overlayClassName: 'label-action',
        }"
        @click="clickHandler"
      >
        <MoreIcon />
      </t-dropdown>
    </div>
  </div>
  <BaseDialog
    v-model:visible="showDialog"
    @confirm="onConfirm"
  >
    <template #title>
      <div v-if="actionType === 'update'">Edit</div>
      <div
        v-if="actionType === 'delete'"
        class="flex flex-center"
      >
        <ErrorCircleIcon
          color="#E37318"
          class="mr-[12px]"
        />
        Are you sure to delete the label?
      </div>
    </template>
    <div class="w-[600px]">
      <template v-if="actionType === 'update'">
        <t-form
          ref="formRef"
          :data="formData"
          :rules="rules"
          label-align="top"
        >
          <t-form-item
            label="First Label"
            name="first_label"
          >
            <t-input
              :value="item.first_label"
              :disabled="true"
            />
          </t-form-item>
          <t-form-item
            label="Second Label"
            name="second_label"
          >
            <t-select-input
              v-model:input-value="newSecondLabel"
              :popup-visible="selectVisible"
              :value="selectValue"
              placeholder="Please Select"
              :clearable="true"
              :allow-input="true"
              :maxlength="50"
              :popup-props="{
                overlayInnerStyle: {
                  maxHeight: '280px',
                  overflowY: 'auto',
                  overscrollBehavior: 'contain',
                  padding: '6px',
                },
              }"
              @blur="onSelectBlur"
              @clear="onSelectClear"
              @popup-visible-change="onPopupVisibleChange"
            >
              <template #panel>
                <ul class="pl-[4px]">
                  <li
                    v-for="sec in secondLabelList"
                    :key="sec.label"
                    class="p-[4px] cursor-pointer"
                    @click="() => onSecondLabelSelect(sec)"
                  >
                    {{ sec.label }}
                  </li>
                </ul>
              </template>
              <template #suffixIcon>
                <chevron-down-icon />
              </template>
            </t-select-input>
          </t-form-item>
          <t-form-item
            label="Reason(Optional)"
            name="reason"
          >
            <t-textarea
              v-model="formData.reason"
              placeholder="Please enter the reason to help the backend better identify labels in the future"
              :maxlength="100"
              name="reason"
              :autosize="{ minRows: 3, maxRows: 3 }"
            />
          </t-form-item>
        </t-form>
      </template>
      <template v-if="actionType === 'delete'">
        <t-form
          ref="formRef"
          :data="formData"
          :rules="rules"
          label-align="top"
        >
          <t-form-item label="" name="reason">
            <t-textarea
              v-model="formData.reason"
              :maxlength="100"
              placeholder="Please enter the reason to help the backend better identify labels in the future.（Optional）"
              name="reason"
              :autosize="{ minRows: 3, maxRows: 3 }"
            />
          </t-form-item>
        </t-form>
      </template>
    </div>
  </BaseDialog>
</template>
<script setup lang="ts">
import { ref, computed, reactive, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { ChevronDownIcon, ErrorCircleIcon, MoreIcon } from 'tdesign-icons-vue-next';
import Text from 'common/components/Text';
import { Timeline } from 'common/service/creative/label/insight/type';
import { useLabelsInsightStore } from '@/store/creative/labels/labels-insight.store';
import { useLabelAssetDetailStore } from '@/store/creative/labels/labels-asset-detail.store';
import BaseDialog from 'common/components/Dialog/Base/index';
import { updateCreativeLabel } from 'common/service/creative/label/insight/asset-detail';
import { MessagePlugin } from 'tdesign-vue-next';

const props = defineProps<{
  item: Timeline,
}>();

const { getData } = useLabelAssetDetailStore();
const { commonInfo } = storeToRefs(useLabelAssetDetailStore());
const { firstLabelList } = storeToRefs(useLabelsInsightStore());

// 通过一级标签获取二级标签列表
const secondLabelList = computed(() => {
  const target = firstLabelList.value.find(item => item.value === props.item.first_label);
  if (!target?.children) return [];
  return target.children.map(item => ({ label: item.value as string, value: item.value as string }));
});

const actionOptions = ref([
  { content: 'Edit', value: 'update' },
  { content: 'Delete', value: 'delete' },
]);

const formRef = ref();
const formData = reactive({
  second_label: '',
  reason: '',
});

const newSecondLabel = ref(''); // 输入的新的二级标签
const selectVisible = ref(false); // 下拉选择浮窗
const selectValue = ref({
  label: '',
  value: '',
});

const rules = {
  second_label: [
    {
      validator: () => {
        const val = newSecondLabel.value;
        if (!val) {
          return { result: false, message: 'second_label is required', type: 'error' };
        }
        if (val.length > 50) {
          return { result: false, message: 'second_label length must no more than 50', type: 'error' };
        }
        return true;
      },
      trigger: 'blur',
    },
  ],
};

const onSecondLabelSelect = (item: { label: string; value: string }) => {
  formData.second_label = item.value;
  selectValue.value = { label: item.value, value: item.value };
  selectVisible.value = false;
  newSecondLabel.value = item.value;
  setTimeout(() => {
    formRef.value.validate('second_label');
  });
};
const onPopupVisibleChange = (val: boolean) => {
  selectVisible.value = val;
};
const onSelectBlur = () => {
  // 如果当前有输入值，则取当前值
  newSecondLabel.value = newSecondLabel.value.trim();
  selectValue.value = { label: newSecondLabel.value, value: newSecondLabel.value };
  setTimeout(() => {
    formRef.value.validate('second_label');
  });
};
const onSelectClear = () => {
  selectValue.value = { label: '', value: '' };
  newSecondLabel.value = '';
};

const actionType = ref<'update' | 'delete'>('update');
const showDialog = ref(false);

const clickHandler = (item: { content: string; value: 'update' | 'delete' }) => {
  showActionBox.value = false;
  const { value } = item;
  actionType.value = value;
  showDialog.value = true;
  if (value === 'update') {
    formData.second_label = props.item.second_label;
    newSecondLabel.value =  props.item.second_label;
    selectValue.value = {
      label: props.item.second_label,
      value: props.item.second_label,
    };
    formData.reason = '';
  }
};

const onConfirm = async () => {
  const { item } = props;
  const params = {
    asset_name: commonInfo.value.asset_name,
    serial_id: commonInfo.value.asset_serial_id,
    first_label: item.first_label,
    second_label_old: item.second_label,
    second_label_new: selectValue.value.label,
    action_type: actionType.value,
    description: formData.reason,
  };
  const validateResult = await formRef.value.validate();
  if (validateResult !== true) return;

  // 未修改二级标签，忽略
  if (actionType.value === 'update' && params.second_label_new === params.second_label_old) {
    MessagePlugin.warning('second label not changed');
    return;
  }

  const res = await updateCreativeLabel(params);
  if (res.code) {
    MessagePlugin.error(res.message);
    return;
  }

  showDialog.value = false;
  MessagePlugin.success(`${actionType.value} success`);
  getData();
};

// 控制操作按钮的显示和隐藏
const showAction = ref(false);
const showActionBox = ref(false);
const onMouseEnter = () => {
  showAction.value = true;
};

const onMouseLeave = () => {
  if (showActionBox.value) return;
  showAction.value = false;
};

watch(() => showActionBox.value, (visible: boolean) => {
  if (!visible) showAction.value = false;
});
</script>
