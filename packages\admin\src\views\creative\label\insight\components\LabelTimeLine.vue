<template>
  <div
    ref="labelTimelineRef" class="label-time-line border-[1px] rounded-t-xl rounded-large" :style="{
      borderBottomLeftRadius: showRetention ? '16px': '0',
    }"
    @mousemove="onMousemove"
    @mouseout="onMouseout"
    @mouseenter="onMouseenter"
  >
    <time-slider
      v-if="labelTimeline.length > 0"
      :timelines="labelTimeline"
      :scroll-top="scrollTop"
      :duration="commonInfo?.duration || 0"
      :content-width="contentWidth"
      :valid-range="validRange"
      :padding-width="8"
      :init-left="leftLabelW"
    />
    <time-guides
      v-if="labelTimeline.length > 0 && showGuides && !isDragging"
      :left="mouseOffsetX" :init-left="leftLabelW"
      :padding-width="8"
    />
    <div class="flex sticky top-0 z-[10] whitespace-nowrap pointer-events-none">
      <div class="border-r-[1px]" :style="{ width: `${leftLabelW}px`}" />
      <div class="flex flex-1 px-[8px] select-none bg-gray-primary rounded-tr-xl">
        <div
          v-for="(item, index) in timeArr"
          :key="index"
          class="text-left text-xs py-[12px] bg-gray-primary select-none"
          :class="index < timeArr.length - 1 ? 'flex-1' : ''"
        >
          {{ item.time }}
        </div>
      </div>
    </div>
    <div class="flex flex-col flex-[1] h-[100px] z-[10]">
      <div
        v-if="labelTimeline.length > 0"
        ref="scrollContentRef"
        class="scroll-content flex flex-[1] h-[100px] overflow-y-auto"
        @scroll="onContentScroll"
      >
        <div
          class="flex flex-col w-[148px] left-0 z-[11] bg-white-primary
         rounded-l-lg whitespace-nowrap border-r-[1px]"
          :style="{ height: `${showRetention ? scrollHeight - bottomH + padding : scrollHeight}px` }"
        >
          <template v-for="item in labelTimeline" :key="item.first_label">
            <label-tag :item="item" />
          </template>
        </div>

        <div ref="lineEl" class="flex flex-[1] relative flex-col rounded-r-lg w-[100px]">
          <div class="px-[8px] w-full relative">
            <div
              v-for="(item, labelIndex) in labelTimeline" :key="labelIndex"
              class="flex flex-[0_0_60px] py-[24px] h-[60px] w-full relative"
            >
              <time-box
                v-for="(line, timeIndex) in item.timeline"
                :key="line.start"
                :left="widthList[labelIndex]?.timeList[timeIndex]?.left || 0"
                :width="widthList[labelIndex]?.timeList[timeIndex]?.width || 0"
                :color="item.color"
                :first-label="item.first_label"
                :second-label="item.second_label"
                :init-offset-left="x + leftLabelW + 8"
                :max-offset-left="x + width"
                :start="line.t_start"
                :end="line.t_end"
              />
            </div>
            <!-- chart hover同步展示当前时间点的标签列表  -->
            <div
              v-if="showHoverTip"
              class="absolute p-[12px] bg-[#000] opacity-[0.8] z-[12] text-white rounded-[8px] w-[180px]"
              :style="tooltipStyle"
            >
              <div class="mb-[6px]">{{ hoverTime }}</div>
              <div
                v-for="(item, labelIndex) in curTimeLines"
                :key="labelIndex"
                class="border-l-[2px] pl-[6px] mb-[12px]"
                :style="{ borderColor: item.color }"
              >
                <div class="font-bold font-lg">{{ item.second_label }}</div>
                <div class="text-xs">{{ item.first_label }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="pl-[12px] border-t-[1px]"
        :style="{
          height: `${showRetention ? bottomH - 24 : 0}px`,
          borderTopWidth: `${showRetention ? 1 : 0}px`
        }"
      >
        <audience-retention
          v-if="hasRateData && showRetention"
          :empty-width="contentWidth - validContentW"
          @show-tip="onShowChartTip"
          @hide-tip="onHideChartTip"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, ref, watch, reactive } from 'vue';
import TimeSlider from './TimeSlider.vue';
import TimeGuides from './LabelTimeGuides.vue';
import TimeBox from './TimeBox.vue';
import { useLabelAssetDetailStore } from '@/store/creative/labels/labels-asset-detail.store';
import { useLabelRetentionStore } from '@/store/creative/labels/labels-retention.store';
import { storeToRefs } from 'pinia';
import { durationToTimeArr, timeStrToSec } from '../utils';
import { useElementBounding, useElementSize } from '@vueuse/core';
import AudienceRetention from './AudienceRetention.vue';
import LabelTag from './LabelTag.vue';
import { Timeline } from 'common/service/creative/label/insight/type';

const props = defineProps<{
  bottomH: number, // 底部收缩区域高度
  showRetention: boolean,
  isDragging: boolean,
}>();

const { hasRateData } = storeToRefs(useLabelRetentionStore());
const { setInitCb } = useLabelAssetDetailStore();
const { commonInfo, labelTimeline } = storeToRefs(useLabelAssetDetailStore());

const padding = ref(24);
const bottomDisH = computed(() => props.bottomH - padding.value);  // 底部区域收缩高度
const leftLabelW = ref(148);

const labelTimelineRef = ref(null);
const { x, width } = useElementBounding(labelTimelineRef); // 右侧时间标签外层div的x轴偏移

const lineEl = ref(null);
const { width: contentWidth } = useElementSize(lineEl);

const scrollContentRef = ref();
const { height: initScrollHeight } = useElementSize(scrollContentRef);

// 右侧标签时间轴滚动高度
const scrollHeight = ref(0);

// 数据加载完成后的回调逻辑，设置滚动区域的高度
setInitCb(() => {
  const h1 = scrollContentRef.value?.scrollHeight;
  const h2 = initScrollHeight.value;
  const h = Math.max(h1, h2);  // 取大值，这样时间轴右边的边框才能填满
  if (props.showRetention) {
    scrollHeight.value = h + bottomDisH.value; // 如果初始就展开了底部，则需要加上展开底部的高度
  } else {
    scrollHeight.value = h;
  }
});

watch(() => props.showRetention, (val) => {
  const disH =  scrollContentRef.value?.scrollHeight - initScrollHeight.value; // 滚动区域差值

  // 差值小于0，表示没有滚动条
  if (disH <= 0) {
    // 展开，恢复到和滚动区域一样高度 + 底部高度
    if (val) {
      setTimeout(() => { // 需要等到滚动高度更新
        scrollHeight.value = scrollContentRef.value?.scrollHeight + bottomDisH.value;
      });
    }
    return;
  }

  if (disH <= bottomDisH.value) {
    // 滚动差值小于底部高度，收起时，需要补齐底部高度
    if (!val) {
      scrollHeight.value = initScrollHeight.value + bottomDisH.value;
    }
  } else {
    // 滚动差值大于底部高度，正常加减底部高度
    scrollHeight.value += (val ? 1 : -1) * bottomDisH.value;
  }
});

const scrollTop = ref(0);  // 滚动高度
const onContentScroll = () => {
  scrollTop.value = scrollContentRef.value.scrollTop;
  tooltipStyle.top = `${scrollTop.value + 20}px`; // 滚动同步更新浮框top
};

// 动态计算每个标签时间宽度
const widthList = computed(() => {
  const lineWidth = contentWidth.value - 16; // 减去padding距离
  if (lineWidth < 0) return [];

  return labelTimeline.value.map((labelItem, index) => {
    const lines = labelItem.timeline;
    const timeList = lines.map((item) => {
      let width = lineWidth * item.duration_ratio * validRange.value;
      if (width < 1) width = 1;  // 最小宽度
      return {
        left: lineWidth * item.start_ratio * validRange.value,
        width,
      };
    });
    return {
      index,
      timeList,
    };
  });
});

// 可拖动的最大宽度
const validContentW = computed(() => (contentWidth.value - 16) * validRange.value);

// 按秒计算时间轴
const timeArr = computed(() => {
  if (commonInfo.value) {
    const { timeArray } = durationToTimeArr(commonInfo.value.duration as number);
    return timeArray;
  }
  return [];
});

// 时间合法区域占比
const validRange = computed(() => {
  if (commonInfo.value) {
    const { rangeSeconds } = durationToTimeArr(commonInfo.value.duration as number);
    return (commonInfo.value.duration as number) / rangeSeconds;
  }
  return 0;
});

const mouseOffsetX = ref(0); // 鼠标相对时间轴区域的偏移量
const showGuides = ref(false);

// 控制hover标签样式
const hoverTime = ref('');
const toolTipWidth = ref(180);
const tooltipStyle = reactive({
  top: '20px',
  width: `${toolTipWidth.value}px`,
  left: '0px',
});

// 鼠标移动事件，显示辅助线
const onMousemove = (e: MouseEvent) => {
  showGuides.value = true;
  let offsetX = e.clientX - x.value - leftLabelW.value - 8;
  const maxX = width.value - leftLabelW.value - 8 * 2; // 右移的最大距离
  if (offsetX < 0) {
    offsetX = 0;
    showGuides.value = false;
  } else {
    showGuides.value = true;
  }
  if (offsetX >= maxX) offsetX = maxX;
  if (offsetX >= maxX) offsetX = maxX;
  mouseOffsetX.value = offsetX;

  // 计算hover浮框位置
  let toolTipX = mouseOffsetX.value + 20;
  if (toolTipX + toolTipWidth.value > maxX) {
    toolTipX = mouseOffsetX.value - toolTipWidth.value;
  }
  tooltipStyle.left = `${toolTipX}px`;
};

// 鼠标移开滚动区域，隐藏辅助线
const onMouseout = () => {
  showGuides.value = false;
};

// 鼠标进入滚动区域，展示辅助线
const onMouseenter = () => {
  showGuides.value = true;
};

const showHoverTip = ref(false);  // 控制展示hover标签

// 辅助时间轴传过的label列表
const curTimeLines = computed<Timeline[]>(() => labelTimeline.value.filter((item) => {
  const { timeline } = item;
  const timeVal = timeStrToSec(hoverTime.value);
  return timeline.some(l => timeVal >= Number(l.t_start) && timeVal <= Number(l.t_end));
}));

const onShowChartTip = (time: string) => {
  showHoverTip.value = true;
  hoverTime.value = time;
};

const onHideChartTip = () => {
  showHoverTip.value = false;
};
</script>
