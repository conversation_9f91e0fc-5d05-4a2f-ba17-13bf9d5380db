<template>
  <div
    class="absolute h-[240px] w-full bottom-[0] bg-white-primary z-[10] text-center flex flex-center"
    :class="{
      'border-t-[1px]': showBorderTop,
    }"
  >
    <div class="text-gray-primary">
      <template v-if="ytVideoConfig?.impressionDate">
        <p>There is currently no retention data generated.</p>
        <p>Please check back in a few days.</p>
      </template>
      <template v-else>
        <p>No retention data available. </p>
        <p>Possible causes: unauthorized channel or no data generated from 20240701.</p>
        <p>Please contact AiX for support.</p>
      </template>
    </div>
  </div>
</template>
<script setup lang="ts">
import { YTVideoConfig } from 'common/service/creative/label/insight/type';

defineProps<{
  ytVideoConfig?: YTVideoConfig
  showBorderTop: boolean,
}>();
</script>
