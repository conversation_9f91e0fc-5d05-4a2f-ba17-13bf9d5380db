<template>
  <div v-if="!firstLine" class="flex">
    <div class="w-full relative bg-[--td-bg-color-component] rounded-[16px] overflow-hidden">
      <div
        :class="'absolute h-full rounded-r-[16px] ' + bgColor"
        :style="{
          width: `${row[`${col}_rate`] * 100}%`,
        }"
      />
      <div class="absolute right-[8px]">
        {{ formatVal(row[col], col) }}
      </div>
    </div>
    <div class="w-[40px] ml-[12px]">
      {{ formatVal(row[`${col}_rate`], `${col}_rate`) }}
    </div>
  </div>
  <div v-else>{{ formatVal(row[col], col) }}</div>
</template>
<script setup lang="ts">
import { computed } from 'vue';
import { formatVal } from '@/views/creative/label/insight/utils';

const props = defineProps<{
  row: any,
  firstLine: boolean,
  status?: string,
  col: string,
}>();

const colorMap = {
  primary: 'bg-brand',
  success: 'bg-success-primary',
  warning: 'bg-warning-primary',
};

const bgColor = computed(() => {
  if (!props.status) return colorMap.primary;
  return colorMap[props.status as keyof typeof colorMap];
});
</script>

<style lang="scss" scoped>
:deep(.t-progress__info) {
  position: absolute;
  left: -102px !important;
}

:deep(.t-progress--over-ten .t-progress__info) {
  color: initial !important;
}
</style>
