<template>
  <div
    class="h-[12px] absolute"
    :style="{
      left: `${left}px`,
      width: `${width}px`,
      zIndex: 10,
      opacity: 0.9,
    }"
    @mousemove="handleMouseMove"
    @mouseleave="handleMouseLeave"
    @mouseenter="handleMouseEnter"
  >
    <div :style="{ backgroundColor: color }" class="rounded-sm w-full h-full" />
    <teleport to="body">
      <div v-if="isTooltipVisible" :style="tooltipStyle" class="time-box-tip">
        <div class="mb-[6px]">{{ timeStr }}</div>
        <div class="border-l-[2px] pl-[6px]" :style="{ borderColor: color }">
          <div class="font-bold font-lg">{{ secondLabel }}</div>
          <div class="text-xs">{{ firstLabel }}</div>
        </div>
      </div>
    </teleport>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue';
import { formatTime } from '../utils';

const props = defineProps<{
  left: number,  // 标签div相对lineEl除去边距边距后的左侧偏移量
  width: number, // 标签div宽度
  color?: string, // 标签div背景色
  firstLabel: string,
  secondLabel: string,
  initOffsetLeft: number, // lineEl除去边距边距后相对屏幕左侧的偏移量
  maxOffsetLeft: number, // x轴最大偏移
  start: string,
  end: string,
}>();

const isTooltipVisible = ref(false);
const tooltipStyle = ref({});
const curRatio = ref(0); // 在当前div播放时长占比
const hoverWidth = 160;

const handleMouseMove = (event: MouseEvent) => {
  const distance = event.clientX - props.left - props.initOffsetLeft;  // 鼠标x轴 - 左侧偏移，即为鼠标在当前div中移动距离
  const ratio = props.width ? distance / props.width : 0; // 鼠标在div左移的占比
  curRatio.value = ratio > 1 ? 1 : ratio;

  // 超出有边界时，将浮框放置左边
  let left = event.clientX + 10;
  if (left + hoverWidth > props.maxOffsetLeft) {
    left = event.clientX - 10 - hoverWidth;
  }

  tooltipStyle.value = {
    top: `${event.clientY}px`,
    left: `${left}px`,
  };
};

const timeStr = computed(() => {
  const cur = Number(props.start) + (Number(props.end) - Number(props.start)) * curRatio.value;
  return formatTime(cur);
});

const handleMouseEnter = () => {
  isTooltipVisible.value = true;
};

const handleMouseLeave = () => {
  isTooltipVisible.value = false;
};

</script>
<style lang="scss">
.time-box-tip {
  position: fixed;
  width: 160px;
  padding: 12px;
  background-color: #000;
  opacity: 0.8;
  border-radius: 8px;
  color: white;
  z-index: 99;
}
</style>
