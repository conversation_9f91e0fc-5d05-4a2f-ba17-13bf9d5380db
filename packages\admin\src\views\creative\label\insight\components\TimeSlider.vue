<template>
  <div class="time-slider" :style="draggableStyle">
    <div
      ref="dragEl" class="anchor z-[12]"
      :style="{
        cursor: state.isDragging ? 'grabbing' : 'grab',
      }"
      @mousedown="onMouseDown"
      @mouseup="onMouseUp"
    />
    <div class="dashed-line z-[9]" />
    <div
      class="lines"
      :style="{
        top: `${10 - scrollTop}px`,
      }"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, computed, onMounted, onBeforeUnmount } from 'vue';
import { videoBus } from '../utils';
import { Timeline } from 'common/service/creative/label/insight/type';

const props = defineProps<{
  contentWidth: number,
  validRange: number,
  paddingWidth: number,
  duration: number,
  initLeft: number,
  scrollTop: number,
  timelines: Timeline[],
}>();

const dragEl = ref<HTMLDivElement>();
const initOffsetLeft = props.initLeft + props.paddingWidth;

const eventInstance = ref();  // 事件监听实例

const state = reactive({
  isDragging: false,
  initialX: 0,
  xOffset: initOffsetLeft,
  mouseX: 0,
});

const draggableStyle = computed(() => ({
  left: `${state.xOffset}px`,
}));

// 鼠标按下事件
const onMouseDown = (event: MouseEvent) => {
  state.isDragging = true;
  state.initialX = event.clientX - state.xOffset; // 记录初始鼠标 X 位置
  state.mouseX = event.clientX; // 记录当前鼠标 X 位置

  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);

  videoBus.emit('start-scroll');
};

// 计算当前偏移量占比
const ratio = computed(() => {
  const value = (state.xOffset - initOffsetLeft) / ((props.contentWidth - props.paddingWidth * 2) * props.validRange);
  return value < 1 ? value : 1;
});

// 鼠标移动事件
const onMouseMove = (event: MouseEvent) => {
  if (state.isDragging) {
    state.mouseX = event.clientX; // 更新当前鼠标 X 位置
    const xOffset = state.mouseX - state.initialX; // 计算相对labelTimelineRef X轴偏移量

    if (xOffset < initOffsetLeft) return;  // 超出左边界，忽略

    const validWidth = props.initLeft + props.paddingWidth
      + (props.contentWidth - props.paddingWidth) * props.validRange;

    // 超出右边界，忽略，需要减去右侧边距+拖动条滚动条宽度
    if (xOffset > validWidth - props.paddingWidth - 6) return;

    state.xOffset = xOffset;

    // 计算当前偏移量占比
    videoBus.emit('on-scroll', ratio.value);
  }
};

// 鼠标松开事件，移除事件监听
const onMouseUp = () => {
  state.isDragging = false;
  document.removeEventListener('mousemove', onMouseMove);
  document.removeEventListener('mouseup', onMouseUp);

  videoBus.emit('on-scroll-end', ratio.value);
};

onMounted(() => {
  if (dragEl.value) {
    dragEl.value.addEventListener('mousedown', onMouseDown);
  }
  // 监听视频播放时间，修改滚动轴进度
  eventInstance.value = videoBus.on((event, ratio: number) => {
    if (event === 'time-ratio') {
      state.xOffset = initOffsetLeft + (props.contentWidth - props.paddingWidth * 2) * ratio * props.validRange;
    }
  });
});

onBeforeUnmount(() => {
  if (dragEl.value) {
    dragEl.value.removeEventListener('mousedown', onMouseDown);
  }
  eventInstance.value();
});
</script>
<style lang="scss">
.time-slider {
  position: absolute;
  width: 6px;
  height: calc(100% - 34px);
  top: 34px;

  .anchor {
    position: absolute;
    top: 0;
    width: 100%;
    height: 20px;
    background: #DCDFE8;
  }

  .dashed-line {
    width: 1px;
    height: 100%;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    border-left: 1px dashed #DCDFE8;
  }

  .lines {
    width: 100%;
    position: absolute;
  }
}
</style>
