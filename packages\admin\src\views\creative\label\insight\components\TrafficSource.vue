<template>
  <div class="flex h-full flex-col">
    <div class="flex justify-between">
      <div class="flex flex-center">
        <span class="text-bold text-base">Traffic Source</span>
        <t-tooltip>
          <template #content>
            <div>Data From Youtube Analytics.</div>
            <div>Time zone UTC-7</div>
          </template>
          <HelpCircleIcon size="14" class="ml-[6px]" />
        </t-tooltip>
      </div>
      <t-date-range-picker
        v-model="formData.dateRange"
        class="w-[234px]"
        :disable-date="{ before: minDate, after: maxDate }"
        @change="getData"
      />
    </div>
    <div class="traffic-source-table flex-[1] h-[1px] mt-[12px] rounded-large overflow-x-hidden overflow-y-auto">
      <div v-if="dataList.length === 0 && loaded" class="flex flex-center h-full text-black-placeholder">
        No Data
      </div>
      <div
        v-for="(item, index) in dataList"
        :key="index" class="flex mb-[1px] px-[12px] py-[6px]"
        :style="{
          backgroundColor: index === 0 ? '#E5EDFD' : '#F0F1F6',
          borderBottomRightRadius: index === dataList.length - 1 ? '16px' : '0',
          borderBottomLeftRadius: index === dataList.length - 1 ? '16px' : '0',
        }"
      >
        <div :class="'flex-[3] text-gray-primary ' + (index === 0 ? 'font-bold' : '')">
          {{ item.traffic_source_value }}
        </div>
        <div class="flex-[1] flex justify-end font-bold">{{ formatNumber(item.views) }}</div>
        <div class="flex-[1] flex justify-end text-gray-primary">{{ item.rate }}</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref, onMounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { HelpCircleIcon } from 'tdesign-icons-vue-next';
import { getTrafficSource } from 'common/service/creative/label/insight';
import { useLabelRetentionStore } from '@/store/creative/labels/labels-retention.store';
import { TrafficSourceRes } from 'common/service/creative/label/insight/type';
import dayjs from 'dayjs';

const { youtubeId, ytVideoConfig } = storeToRefs(useLabelRetentionStore());

const formData = reactive({
  dateRange: [ytVideoConfig.value!.defaultStartDateSource, ytVideoConfig.value!.maxDateSource],
});

const minDate = computed(() => dayjs(ytVideoConfig.value!.minDate));
const maxDate = computed(() => dayjs(ytVideoConfig.value!.maxDateSource));

function formatNumber(value: number): string {
  return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

const loaded = ref(false);
const dataList = ref<TrafficSourceRes[]>([]);

const getData = async () => {
  const data = await getTrafficSource({
    start_date: formData.dateRange[0].replaceAll('-', ''),
    end_date: formData.dateRange[1].replaceAll('-', ''),
    youtube_id: youtubeId.value,
  });
  loaded.value = true;
  dataList.value = data.list;
};

onMounted(() => {
  getData();
});
</script>
