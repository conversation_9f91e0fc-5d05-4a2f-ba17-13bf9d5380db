<template>
  <div v-if="video?.url">
    <template v-if="assetType === 'VIDEO'">
      <video
        v-if="!isYoutube"
        ref="videoEle"
        :src="video.url"
        :class="`${videoWidth} ${videoHeight} w-full h-full object-contain bg-black`"
        controls
        controlsList="nodownload noplaybackrate"
        disablePictureInPicture
        @loadedmetadata="onLoadedMetadata"
        @timeupdate="onTimeUpdate"
      />
      <div v-else class="w-full h-full">
        <iframe
          v-if="video.url"
          class="w-full vjs-big-play-centered"
          :src="video.url"
          style="height: 100%"
          allow="autoplay"
        />
      </div>
    </template>
    <img
      v-else
      class="w-full h-full object-contain bg-black"
      :src="video!.url"
      alt=""
    >
  </div>
</template>
<script lang="ts" setup>
import { computed, ref } from 'vue';

type Video = {
  url: string;
};

const props = withDefaults(
  defineProps<{
    video: Video | undefined;
    assetType?: string;
    width?: string | number | undefined;
    height?: string | number | undefined;
    startTime?: number;
    endTime?: number;
  }>(),
  {
    width: undefined,
    height: undefined,
    startTime: 0,
    endTime: 0,
    assetType: 'VIDEO',
  },
);

const videoEle = ref();

const emit = defineEmits(['timeupdate']);

const isYoutube = computed(() => props.video?.url.includes('youtube'));

const videoWidth = computed(() => {
  if (props.width) {
    const num = Number(props.width);
    if (Number.isNaN(num)) return props.width;
    return `w-[${props.width}px]`;
  }
  return 'w-[100%]';
});
const videoHeight = computed(() => {
  if (props.height) {
    const num = Number(props.height);
    if (Number.isNaN(num)) return props.height;
    return `h-[${props.height}px]`;
  }
  return 'w-[100%]';
});

// 控制开始、结束时间
const onLoadedMetadata = (event: Event) => {
  const videoElement = event.target as HTMLVideoElement;
  if (props.startTime) {
    videoElement.currentTime = props.startTime;
  }
};

const hasPaused = ref(false);
const onTimeUpdate = (event: Event) => {
  const videoElement = event.target as HTMLVideoElement;
  if (props.endTime && videoElement.currentTime >= props.endTime && !hasPaused.value) {
    videoElement.pause();
    hasPaused.value = true;
  }
  emit('timeupdate', videoElement.currentTime);
};

const pause = () => {
  videoEle.value.pause();
};

const currentTime = (num: number) => {
  videoEle.value.currentTime = num;
};

const getPlayer = () => videoEle.value;

defineExpose({
  pause,
  currentTime,
  getPlayer,
});
</script>
