<template>
  <div class="pt-[24px] flex flex-[1] flex-col  gap-y-[24px]  w-full pr-[24px] pb-[24px]">
    <NewMetricTrendLineChart
      class="h-[386px]"
      left-top-label="Trend"
      :metric="metric"
      :dtstatdate="dtstatdate"
      :table-data="tableData"
      :metric-list="metricList"
      :is-loading="isTrendDataLoading"
      :tooltip-value-format="(value: any) => formatValue(value, metric)"
      :field-labels="{
        [metric]: 'This Asset',
        [`avg_${metric}`]: avgMetricLabel
      }"
      :area-style-color="{
        [metric]: ['#f7f9fe', 'rgb(236, 242, 254)'],
        [`avg_${metric}`]: ['#fdfcfb', '#f7f1ef']
      }"
      :color="['#5086F3', '#f19e55']"
      :pick-fields="isShowAvgMetric ? [metric, `avg_${metric}`] : [metric]"
      @update:metric="updateMetric"
      @update:dtstatdate="updateDtstatdate"
    />
    <TrendTable
      :table-data="tableData"
      :is-loading="isTrendDataLoading"
      :metric="metric"
      :avg-metric-lable="avgMetricLabel"
      :is-show-avg-metric="isShowAvgMetric"
      :format-value="formatValue"
    />
  </div>
</template>
<script lang="ts" setup>
import TrendTable from './TrendTable.vue';
import { computed, ref, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import dayjs from 'dayjs';
import { getMetricTrendDataService } from 'common/service/creative/label/insight/asset-detail';
import { useLabelAssetDetailStore } from '@/store/creative/labels/labels-asset-detail.store';
import { useRoute } from 'vue-router';
import { useLoading } from 'common/compose/loading';
import { formatVal } from '@/views/creative/label/insight/utils';
import { isUndefined } from 'lodash-es';
import NewMetricTrendLineChart from '@/views/creative/common/components/NewMetricTrendLineChart.vue';

const { query } = useRoute();
const { commonInfo, topAssetNames, sDate, eDate, defaultIndex, allMetrics } = storeToRefs(useLabelAssetDetailStore());
const {
  isLoading: isTrendDataLoading,
  hideLoading: hideTrendDataLoading,
  showLoading: showTrendDataLoading,
} = useLoading();


const isShowAvgMetric = !!query.code;
const metric = ref(defaultIndex.value); // 右上角metric选中的值

const updateMetric = (val: string) => {
  metric.value = val;
  getMetricTrendData();
};

// 右上角时间选中的值
const defaultStart = sDate.value ? dayjs(sDate.value).format('YYYYMMDD') : dayjs().subtract(13, 'day')
  .format('YYYYMMDD');
const defaultEnd = eDate.value ? dayjs(eDate.value).format('YYYYMMDD') : dayjs().format('YYYYMMDD');
const dtstatdate = ref<string[]>([defaultStart, defaultEnd]);

const updateDtstatdate = (val: string[]) => {
  dtstatdate.value = val;
  getMetricTrendData();
};

// 表格数据
const tableData = ref<Record<string, any>[]>([]);

const formatValue = (value: any, col: string) => {
  if (isUndefined(value)) return '';
  return formatVal(value, col, metricList.value as any);
};

// 表格列
const metricLabel = computed(() => {
  const target = allMetrics.value.find(item => metric.value === item.key);
  return target ? target.title : metric.value;
});
const avgMetricLabel = computed(() => `Avg.${metricLabel.value}`);

// metric下拉选项
const metricList = computed(() => allMetrics.value.map(item => ({
  ...item,
  value: item.key,
  label: item.title,
})).filter(item => item.value !== 'asset_score'));

// 获取趋势图和表格的数据
async function getMetricTrendData() {
  showTrendDataLoading();
  const res = await getMetricTrendDataService({
    startDate: dtstatdate.value[0],
    endDate: dtstatdate.value[1],
    asset_name: [commonInfo.value!.asset_name],
    top_asset_name: topAssetNames.value, // 必传
    metric: [metric.value],
    orderby: [
      { by: 'dtstatdate', order: 'ASC' },
    ],
    group: ['dtstatdate'],
  });
  tableData.value = res;
  hideTrendDataLoading();
}

async function init() {
  await Promise.all([getMetricTrendData()]);
}

onMounted(() => {
  init();
});
</script>
