<template>
  <t-loading :loading="props.isLoading">
    <div class="rounded-default border-[1px] border-[#ebecf1] py-[16px] flex flex-col gap-y-[16px] w-full">
      <div class="flex flex-warp justify-between px-[16px] items-center">
        <Text
          content="Trend"
          weight="600"
          class="font-semibold"
          size="title"
        />
        <div class="flex items-center gap-x-[8px]">
          <DateRangePicker
            :model-value="dtstatdate"
            :presets="presets"
            :max-date="dayjs().format(FORMAT)"
            value-type=""
            @update:model-value="onDtstatdateChange"
          />
          <t-select
            :options="metricList"
            :model-value="metric"
            :filterable="true"
            class="w-[180px]"
            @change="(val: any) => onMetricChange(val)"
          />
        </div>
      </div>
      <div
        v-if="props.isLoading"
        class="w-full h-[300px] flex justify-center items-center"
      >
        <DataEmpty />
      </div>
      <v-chart
        v-else
        autoresize
        :option="echatrsOptions"
        :style="{
          height: '300px',
          width: 'auto'
        }"
      />
    </div>
  </t-loading>
</template>
<script lang="ts" setup>
import { PropType, computed } from 'vue';
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart } from 'echarts/charts';
import dayjs from 'dayjs';
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components';
import type { OptionData } from 'tdesign-vue-next';
import Text from 'common/components/Text';
import DateRangePicker from 'common/components/DateTimePicker';
import DataEmpty from 'common/components/NullAble/DataEmpty.vue';

use([
  CanvasRenderer, LineChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent,
]);

const FORMAT = 'YYYYMMDD';
const emits = defineEmits(['update:dtstatdate', 'update:metric']);
const props = defineProps({
  tableData: {
    type: Array as PropType<Record<string, any>[]>,
    default: () => [],
  },
  metricList: {
    type: Array as PropType<OptionData[]>,
    default: () => [],
  },
  metric: {
    type: String,
    default: '',
  },
  dtstatdate: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  avgMetricLable: {
    type: String,
    default: '',
  },
  metricLable: {
    type: String,
    default: 'This Asset',
  },
  isShowAvgMetric: {
    type: Boolean,
    default: true,
  },
  formatValue: {
    type: Function,
    default: () => {},
  },
});

const presets = computed(() => ({
  'Last 7 Days': [dayjs().subtract(6, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  'Last 14 Days': [dayjs().subtract(13, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  'Last 30 Days': [dayjs().subtract(29, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  'Last 90 Days': [dayjs().subtract(89, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  'Last 180 Days': [dayjs().subtract(179, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  'Last 365 Days': [dayjs().subtract(364, 'day')
    .format(FORMAT), dayjs().format(FORMAT)],
  // [maxData当前年第一天,maxData]
  'Year to Date': [dayjs().startOf('year')
    .format(FORMAT), dayjs().format(FORMAT)],
}));

const lineData = computed(()  => {
  const xAxisData: string[] = [];
  const theVideoData: number[] = [];
  const avgMetricData: number[] = [];
  for (const row of props.tableData) {
    xAxisData.push(row.dtstatdate);
    theVideoData.push(row[props.metric]);
    avgMetricData.push(row[`avg_${props.metric}`]);
  }
  return {
    xAxisData,
    avgMetricData,
    theVideoData,
  };
});

const onDtstatdateChange = (val: string[]) => {
  emits('update:dtstatdate', val.map(date => dayjs(date).format(FORMAT)));
};
const onMetricChange = (val: string) => emits('update:metric', val);

const getSeriesItem = (colors: string[]) => ({
  type: 'line',
  smooth: true,
  showSymbol: props.dtstatdate[0] === props.dtstatdate[1],
  areaStyle: {
    color: {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [{
        offset: 0, color: colors[0], // 0% 处的颜色
      }, {
        offset: 1, color: colors[1], // 100% 处的颜色
      }],
      global: false, // 缺省为 false
    },
  },
  emphasis: {
    disabled: true, // 防止hover时背景色被覆盖
  },
});

// tooltip 自定义渲染
const renderTooltip = (params: Record<string, any>[]) => {
  const date = params?.[0]?.axisValue;
  const list = params.map(item => (
    `<div class="flex items-center gap-x-[16px] justify-between">
      <div class="flex items-center gap-x-[8px]">
        ${item.marker}
        <div class="text-[#d2d4d9]">${item.seriesName}</div>
      </div>
      <div class="text-white">${props.formatValue(item.value, props.metric)}</div>
    </div>
  `));
  return `
    <div class="flex p-[16px] rounded-default flex-col gap-y-[8px] bg-[#1c293f]">
      <div class="text-white">${dayjs(date).format('YYYY-MM-DD')}</div>
      ${list.join('')}
  </div>`;
};

const echatrsOptions = computed(() => (
  {
    color: ['#5086F3', '#f19e55'],
    legend: {
      data: [props.metricLable, props.avgMetricLable], icon: 'circle',
    },
    tooltip: {
      trigger: 'axis', padding: 0, formatter: renderTooltip,
    },
    grid: {
      containLabel: true, left: 16, top: 32, bottom: 8, right: 16,
    },
    xAxis: {
      type: 'category',
      data: lineData.value.xAxisData,
      // 隐藏x轴刻度
      axisTick: { show: false },
      axisLine: {
        // x轴线
        lineStyle: { type: 'dashed', color: '#e4e7ed' },
      },
      axisLabel: {
        color: '#747D98', // x轴label
        formatter: (value: string) => dayjs(value).format('MM-DD'),
      },
    },
    yAxis: {
      type: 'value',
      splitLine: { lineStyle: { type: 'dashed' } /* 网格线虚线 */ },
    },
    series: [
      {
        data: lineData.value.theVideoData,
        name: props.metricLable,
        ...getSeriesItem(['#f7f9fe', 'rgb(236, 242, 254)']),
      },
      ...(props.isShowAvgMetric ? [
        {
          data: lineData.value.avgMetricData,
          name: props.avgMetricLable,
          ...getSeriesItem(['#fdfcfb', '#f7f1ef']),
        },
      ] : []),
    ],
  }
));

</script>
