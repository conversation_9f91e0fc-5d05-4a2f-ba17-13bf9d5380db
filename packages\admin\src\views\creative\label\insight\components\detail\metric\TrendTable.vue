<template>
  <div ref="tableContainer" class="overflow-y-hidden rounded-default flex-1 border-[1px] border-[#ebecf1] ">
    <t-loading :loading="props.isLoading">
      <t-table
        row-key="dtstatdate"
        :data="tableDataInner"
        :sort="tableSort"
        :columns="(tableClounms as BaseTableCol[])"
        :max-height="tableContainerHeight"
        @sort-change="(val: TableSort) => sortChange(val as SortInfo)"
      />
    </t-loading>
  </div>
</template>
<script lang="ts" setup>
import { ref, PropType, computed } from 'vue';
import { useElementSize } from '@vueuse/core';
import type { BaseTableCol, SortInfo, TableSort } from 'tdesign-vue-next';
import dayjs from 'dayjs';
import { orderBy } from 'lodash-es';

const props = defineProps({
  tableData: {
    type: Array as PropType<Record<string, any>[]>,
    default: () => [],
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  metric: {
    type: String,
    default: '',
  },
  avgMetricLable: {
    type: String,
    default: '',
  },
  isShowAvgMetric: {
    type: Boolean,
    default: true,
  },
  formatValue: {
    type: Function,
    default: () => {},
  },
});

const tableContainer = ref();
const { height: tableContainerHeight } = useElementSize(tableContainer);

const tableSort = ref<SortInfo>();

const tableDataInner = computed(() => {
  if (!tableSort.value) return props.tableData;
  return orderBy(props.tableData, [tableSort.value.sortBy], [tableSort.value.descending ? 'desc' : 'asc']);
});

const avgMetricCol = computed(() => ({
  title: props.avgMetricLable, colKey: `avg_${props.metric}`,
  cell: (_h: Function, { row, col }: { row: Record<string, any>, col: { colKey: string}}) => (
    props.formatValue(row[col.colKey], col.colKey)
  ),
  sorter: true,
}));
const tableClounms = computed(() => (
  [
    {
      title: 'Date', colKey: 'dtstatdate',
      cell: (_h: Function, { row }: { row: Record<string, any>}) => dayjs(String(row.dtstatdate), 'YYYYMMDD').format('YYYY-MM-DD'),
    },
    {
      title: 'This Asset', colKey: props.metric,
      cell: (_h: Function, { row, col }: { row: Record<string, any>, col: { colKey: string}}) => (
        props.formatValue(row[col.colKey], col.colKey)
      ),
      sorter: true,
      // align: 'right',
    },
    ...(props.isShowAvgMetric ? [avgMetricCol.value] : []),
  ]
));

const sortChange = (val: SortInfo) => {
  tableSort.value = val;
};

</script>
