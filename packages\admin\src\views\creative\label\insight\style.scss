.vjs-big-play-centered .vjs-big-play-button {
  margin-top: initial !important;
  margin-left: initial !important;
}

.label-asset-detail {
  .common-info::-webkit-scrollbar {
    width: 4px;
  }
  .common-info::-webkit-scrollbar-thumb {
    background: #CBD0DE;
  }
  .trigger-expand {
    cursor: pointer;
    bottom: 24px;
  }

  .traffic-source-table::-webkit-scrollbar {
    width: 4px;
  }
  .traffic-source-table::-webkit-scrollbar-thumb {
    background: #CBD0DE;
  }
}

.label-time-line {
  position: relative;
  display: flex;
  flex-direction: column;
  flex: 1;
  user-select: none;
  .scroll-content::-webkit-scrollbar {
    width: 4px;
  }
  .scroll-content::-webkit-scrollbar-thumb {
    background: #CBD0DE;
  }
}
