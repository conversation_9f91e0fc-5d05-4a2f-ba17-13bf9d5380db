import { useEventBus } from '@vueuse/core';
import { TXLSXValue } from 'common/utils/template/templateHelper';
import { IMetricItem } from 'common/service/creative/dashboard-af/type';

// 格式化数字
const isPercentCols = [
  'spend_rate',
  'asset_num_rate',
  'installs_rate',
  'avg_view_rate',
  's3_bounce_rate',
  's6_bounce_rate',
];
const isDecimals = ['ipm', 'cpi', 'd7_roas'];

export function formatVal(val: number, col = '', customMetrics: IMetricItem[] = []) {
  if (col.indexOf('avg_') > -1) {
    // eslint-disable-next-line no-param-reassign
    col = col.replace('avg_', '');
  }
  const custom = customMetrics.find(item => item.key === col);
  const isPercent = isPercentCols.includes(col) || custom?.format === 'percent';
  let isDecimal = isDecimals.includes(col) || custom?.format === 'numShort' || custom?.format === 'money';
  let validNum = 0; // 小数点后几位
  if (isDecimal || custom?.format === 'money') {
    validNum = 2;
  }
  if (custom?.opt) {
    validNum = custom?.opt;
  } else {
    isDecimal = false;
  }

  if (val === 0) return isPercent ? '0.00%' : isDecimal ? '0.00' : '0';
  if (!val) return '-';
  if (col === 'spend' || col === 'cost') return Math.round(val).toLocaleString();

  let resVal = 0;

  if (isPercent) {
    if (col === 'avg_view_rate') return `${val.toFixed(validNum)}%`;
    return `${(val * 100).toFixed(validNum || 2)}%`;
  }
  if (isDecimal && validNum !== 0) {
    validNum = validNum || 2;
    resVal = Number(val.toFixed(validNum));
    return resVal.toLocaleString(undefined, {
      minimumFractionDigits: validNum,
      maximumFractionDigits: validNum,
    });
  }
  resVal = Number(val.toFixed(validNum));
  return resVal.toLocaleString();
}

/**
 * 将秒级别时间转换为时间数组。
 * 如果时长超过20秒，时间间隔为5秒；否则为1秒。
 * @param duration - 秒级别时间
 * @returns 包含格式化时间字符串的数组，每个元素表示时间点
 */
export function durationToTimeArr(duration: number) {
  const step = duration > 20 ? 5 : 1; // 判断步长

  const rangeSeconds = Math.ceil(duration / step) * step; // 向上取整，获取完整的秒数，并根据步长调整
  const timeArray = [];

  for (let i = 0; i <= rangeSeconds; i += step) {
    const minutes = Math.floor(i / 60); // 计算当前时间的分钟
    const seconds = i % 60; // 计算当前时间的秒
    const timeString = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`; // 格式化为 "MM:SS"
    timeArray.push({ time: timeString });
  }

  // 确保总时长的最后一个时间点被包括在内（如果不是整步长的倍数）
  if (rangeSeconds % step !== 0) {
    const minutes = Math.floor(rangeSeconds / 60);
    const seconds = rangeSeconds % 60;
    const timeString = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    timeArray.push({ time: timeString });
  }

  return {
    timeArray,
    rangeSeconds,
  };
}

// 将视频播放时间戳，格式化为mm:ss.SSS
export function formatTime(duration: number) {
  const minutes = Math.floor(duration / 60);
  const seconds = Math.floor(duration % 60);
  const milliseconds = Math.floor((duration % 1) * 1000);

  const formattedMinutes = minutes.toString().padStart(2, '0');
  const formattedSeconds = seconds.toString().padStart(2, '0');
  const formattedMilliseconds = milliseconds.toString().padStart(3, '0');

  return `${formattedMinutes}:${formattedSeconds}.${formattedMilliseconds}`;
}

// 将mm:ss.SSS转化为秒
export function timeStrToSec(time: string): number {
  const [minutes, seconds] = time.split(':').map(part => parseFloat(part)); // 将时间字符串按照冒号和小数点分割
  return minutes * 60 + seconds; // 计算总秒数
}

// 下载格式转化
export function excelFormatFn(xlsxObject: Record<string, TXLSXValue>) {
  Object.keys(xlsxObject).forEach((key) => {
    const item = xlsxObject[key];
    if (typeof item !== 'object') return;

    const toNum = Number(item.v);
    if (!Number.isNaN(toNum)) {
      item.v = toNum;
      item.t = 'n';
    } else {
      const numReg = ((item.v ?? '') as string).match(/^(\d{1,3}(,\d{3})*(\.\d{1,2})?)$/);
      if (numReg) {
        item.v = numReg[0].replace(/,/g, ''); // 移除逗号;
        item.t = 'n';
      } else {
        const format = ((item.v ?? '') as string).match(/(\d+(\.\d+)?)%/); // 百分比数值
        if (format) {
          item.v = Number(format[1]) / 100;
          item.z = '0.00%';
          item.t = 'n';
        }
      }
    }
  });
  return xlsxObject;
}

export const videoBus = useEventBus<string>('assetVideo');
