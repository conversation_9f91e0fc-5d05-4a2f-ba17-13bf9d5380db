<template>
  <CommonView
    :need-back="true"
    :router-index="-2"
    :hide-right="true"
  >
    <template #views>
      <DataContainer
        class="pt-[16px] rounded-large mb-[24px]"
        :hide-footer="true"
      >
        <template #attributeSlot>
          <h2 class="font-bold text-xl">Label List</h2>
        </template>
        <template #actionSlot>
          <t-button variant="text" @click="createLabel">
            <template #icon><AddIcon /></template>Create Label
          </t-button>
          <t-button variant="text" @click="downloadFile">
            <template #icon><DownloadIcon /></template>Download
          </t-button>
        </template>
        <t-table
          row-key="name"
          :columns="tableColumns"
          :loading="tableLoading || isDragLoading"
          :data="tableData"
          drag-sort="row-handler"
          @drag-sort="onDragSort"
        >
          <template #drag>
            <MoveIcon />
          </template>
          <template #required="{row}">
            {{ row.required ? 'Required' : 'Optional' }}
          </template>
          <template #multiple="{row}">
            {{ row.multiple ? 'Multiple Choice' : 'Single Choice' }}
          </template>
          <template #options="{row}">
            {{ row.optionList.join('、 ') }}
          </template>
          <template #label_type="{row}">
            {{ row.label_type === 'creative' ? 'Creative Label' : 'Content Label' }}
          </template>
          <template #label_level="{row}">
            {{ row.label_level === 'asset' ? 'Asset Level' : 'Serial Level' }}
          </template>
          <template #label_method="{row}">
            {{ row.label_method === 'manual' ? 'Manual Labeling' : 'Intelligent Labeling' }}
          </template>
          <template #actions="{row}">
            <t-button theme="primary" variant="text" @click="editLabel(row)">Edit</t-button>
            <t-button theme="primary" variant="text" @click="removeLabel(row)">Delete</t-button>
          </template>
        </t-table>
        <t-pagination
          v-model:current="pageIndex"
          v-model:page-size="pageSize"
          class="my-[24px]"
          :total="totalCount"
          size="small"
          :page-size-options="[10, 20, 50]"
          @page-size-change="onPageSizeChange"
          @current-change="onPageIndexChange"
        />
      </DataContainer>
    </template>
  </CommonView>
  <label-config ref="labelDialog" />
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useLabelsSystemStore } from '@/store/creative/labels/labels-systtem.store';
import { useGlobalGameStore } from '@/store/global/game.store';
import LabelConfig from '../components/LabelConfig.vue';
import CommonView from 'common/components/Layout/CommonView.vue';
import DataContainer from 'common/components/Layout/DataContainer.vue';
import { SystemLabelItem } from 'common/service/creative/label/manage/type';
import { useDownloadFile } from 'common/compose/download-file';
import { AddIcon, DownloadIcon, MoveIcon } from 'tdesign-icons-vue-next';
import type { DragSortContext, TableRowData } from 'tdesign-vue-next';
import { orderBy } from 'lodash-es';
import { DialogPlugin } from 'tdesign-vue-next';
import { useLoading } from 'common/compose/loading';
import { useWatchGameChange } from 'common/compose/request/game';

const { isLoading: isDragLoading, hideLoading: hideDragLoading, showLoading: showDragLoading } = useLoading();

const { getTableData, onPageSizeChange, onPageIndexChange, deleteLabel, updateLabelOrder } = useLabelsSystemStore();
const {
  tableColumns, pageIndex, pageSize, tableLoading, tableData, totalCount, totalData,
} = storeToRefs(useLabelsSystemStore());

const gameStore = useGlobalGameStore();
const labelDialog = ref();

const createLabel = () => {
  const randOrder = Math.floor(Math.random() * (100000 - 10000) + 10000);
  let initOrder = totalData.value[0] ? totalData.value[0].label_order : null;
  if (initOrder) initOrder = initOrder + 1;
  else initOrder = randOrder;
  labelDialog.value.show(initOrder);
};

const editLabel = (item: SystemLabelItem) => {
  labelDialog.value.show(item);
};

const removeLabel = (item: SystemLabelItem) => {
  const confirmDia = DialogPlugin({
    header: 'Tips',
    body: 'Are you sure to delete it?',
    confirmBtn: 'Confirm',
    cancelBtn: 'Cancel',
    onConfirm: async () => {
      await deleteLabel(item.id as number);
      getTableData();
      confirmDia.hide();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};


// 下载excel
const downloadFile = () => {
  let data: string[][] = [
    ['First Label', 'Filling Requirement', 'Choice Requirement', 'Label Type', 'Labeling Method', 'Second Label'],
  ];
  data = data.concat(totalData.value.map(item => [
    item.name as string,
    item.required ? 'Required' : 'Optional',
    item.multiple ? 'Multiple Choice' : 'Single Choice',
    item.label_type === 'creative' ? 'Creative Label' : 'Content Label',
    item.label_method === 'manual' ? 'Manual Labeling' : 'Intelligent Labeling',
    item.optionList!.join(';') || '',
  ]));
  useDownloadFile(
    [
      { sheet: 'Sheet1', list: data },
    ],
    `${gameStore.gameCode}-all-label-${new Date().toLocaleDateString()}.xlsx`,
    {
      mode: 'group',
      isSetHeader: true,
    },
  );
};

const dragTableData = ref<SystemLabelItem[] | null>(null);
const onDragSort = async (context: DragSortContext<TableRowData>) => {
  showDragLoading();
  const { data, newData } = context;

  // 得到原来的 修改之前的label_order 数组
  const labelOrderList = orderBy(data.map(item => Number(item.label_order)), undefined, 'desc');
  const afterSortData = newData.map(((item, index) => ({
    id: item.id,
    label_order: labelOrderList[index],
  })));

  // label_order=0的数据处理
  const order0Data = afterSortData.filter(item => item.label_order === 0); // 获取order=0的数据
  const lastNot0Data = afterSortData.filter(item => item.label_order !== 0).pop(); // 获取最后一个order>0的数据
  const startIndex = lastNot0Data ? lastNot0Data.label_order : afterSortData.length;
  // eslint-disable-next-line no-param-reassign
  order0Data.forEach((item, index) => item.label_order = startIndex - index);

  // 请求接口之前 先把拖拽后的内容显示到表格
  dragTableData.value = [...(newData as SystemLabelItem[])];
  await updateLabelOrder(afterSortData);
  await getTableData();
  dragTableData.value = null;
  hideDragLoading();
};

useWatchGameChange(async () => {
  getTableData();
});
</script>
<style scoped lang="scss"></style>
