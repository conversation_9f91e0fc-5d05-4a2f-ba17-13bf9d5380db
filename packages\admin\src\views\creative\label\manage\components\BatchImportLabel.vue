<template>
  <BaseDialog
    ref="dialogRef"
    title="Bulk Edit Label"
    width="30%"
    confirm-text="Confirm Import"
    :confirm-loading="submitLoading"
    @confirm="confirmToUpload"
  >
    <template #default>
      <t-space
        class="w-full"
        direction="vertical"
        size="16px"
      >
        <t-space size="8px">
          <t-dropdown
            :options="options"
            min-column-width="175px"
            @click="onDownload"
          >
            <t-space>
              <t-button theme="default">
                Download Template
                <template #suffix>
                  <t-icon
                    name="chevron-down"
                    size="16"
                  />
                </template>
              </t-button>
            </t-space>
          </t-dropdown>
        </t-space>

        <t-upload
          ref="uploadRef"
          v-model="files"
          theme="file"
          :abridge-name="[10, 8]"
          draggable
          accept=".xlsx"
          :size-limit="{ size: 5, unit: 'MB', message: 'File size should not over 5MB' }"
          :request-method="requestMethod"
        />
      </t-space>
    </template>
  </BaseDialog>
</template>

<script setup lang="ts">
import BaseDialog from 'common/components/Dialog/Base';
import { ref } from 'vue';
import { useDownloadFile } from 'common/compose/download-file';
import { useTips } from 'common/compose/tips';
import { useLoading } from 'common/compose/loading';
import { get } from '@vueuse/core';
import { addLabelRuleTask } from 'common/service/creative/label/manage';
import { useGlobalGameStore } from '@/store/global/game.store';
import type { UploadFile } from 'tdesign-vue-next';
import { v4 as uuidv4 } from 'uuid';
import { uploadToCos } from 'common/components/FileUpload/util';
import { useEnv } from 'common/compose/env';
import { FILE_CDN_COM } from 'common/config';
import { MessagePlugin } from 'tdesign-vue-next';
import { FirstLabelType } from 'common/service/creative/common/type';

const { err: errTips, success: successTips } = useTips();
const { env } = useEnv();
const props = defineProps<{
  labelList: FirstLabelType[],
}>();

const dialogRef = ref();
const uploadRef = ref();
const files = ref([]);
/**
 * donwload file template start
 */
const options = [
  { value: 'default', content: 'General Template' },
];

function onDownload(item: { value: 'default', content: string }) {
  const type = item.value;
  const configs = {
    default: {
      fields: {
        'Serial Name': 'Sample0001',
        'Asset Name': 'Sample0001-V-V1.0-EN-16X9',
      },
      filename: 'Label Import Template',
    },
  } as const;

  const config = configs[type] || configs.default;

  const emptyFields = Object.fromEntries(Object.keys(config.fields).map(key => [key, '']));
  const optionalRow: { [key: string]: string } = { ...emptyFields }; // 描述字段必填/可选
  const optionsRow: { [key: string]: string } = { ...emptyFields }; // 枚举可选项
  const demoRow: { [key: string]: string } = { ...config.fields }; // 示例数据
  const manualList = props.labelList.filter(item => item.label_method === 'manual'); // 过滤机器打标，保留人工打标
  manualList.forEach((item) => {
    const { value, multiple, children = [], required, label_method: labelMethod, label_level: labelLevel = 'serial' } = item;
    const secondLabels = children.map(item => item.value);
    let colName = value;
    if (labelMethod === 'intelligent') colName += '（intelligent）';
    const labelRules = [
      required ? 'Required' : 'Optional',
      multiple ? 'Multiple Choice' : 'Single Choice',
      labelLevel === 'serial' ? 'Serial Level' : 'Asset Level',
    ];
    optionalRow[colName] = labelRules.join('-');
    optionsRow[colName] = secondLabels.join(',');
    const defaultOptions = multiple ? secondLabels : [secondLabels[0]];
    demoRow[colName] = defaultOptions.slice(0, 2).join(',');
  });
  const data = [optionalRow, optionsRow, demoRow];

  useDownloadFile(
    [
      {
        sheet: 'Sheet1',
        list: data,
      },
    ],
    `${config.filename}.xlsx`,
    {
      mode: 'group',
    },
  );
}

/**
 * donwload file template end
 */
// 提交导入内容到后台服务的loading
const { isLoading: submitLoading, showLoading: showSubmitLoading, hideLoading: hideSubmitLoading } = useLoading();

const gameStore = useGlobalGameStore();

const confirmToUpload = async () => {
  const uploadFile = files.value?.[0] as UploadFile;

  const cosFilePath = uploadFile?.response?.key;
  if (!cosFilePath) {
    MessagePlugin.warning('Please upload the file first');
    return;
  }
  showSubmitLoading();

  const res = await addLabelRuleTask({
    gameCode: gameStore.gameCode,
    cosFile: `/${cosFilePath}`,
  });

  if (res.result.error_code === 0) {
    successTips('upload success');
    dialogRef.value.hide();
  } else {
    errTips(res.result.error_message);
  }
  hideSubmitLoading();
};

const requestMethod = (uploadFile: UploadFile) => {
  const rawFile = uploadFile.raw as File;
  const key = generateUploadKey(rawFile.name);
  return new Promise((resolve) => {
    uploadToCos({ file: rawFile, key, type: 'aix' })
      .then((data) => {
        if (data.code) {
          resolve({ status: 'fail', error: 'upload error!', response: {} });
        }
        resolve({
          status: 'success',
          response: {
            key,
            url: `${FILE_CDN_COM}/${encodeURIComponent(key)}`,
          },
        });
      })
      .catch(() => {
        resolve({ status: 'fail', error: 'upload error!', response: {} });
      });
  });
};

const generateUploadKey = (fileName: string): string => {
  const uuid = uuidv4();
  return `creative_label/${gameStore.gameCode}_${env.value}_${uuid}_${Date.now()}_${fileName}`;
};

defineExpose({
  show: () => {
    get(dialogRef).show();
    files.value = [];
  },
});
</script>

<style scoped>
:deep(.t-upload__dragger) {
  width: 100%;
}
</style>
