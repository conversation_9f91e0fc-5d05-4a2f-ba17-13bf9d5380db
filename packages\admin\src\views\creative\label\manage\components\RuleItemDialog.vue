<template>
  <BaseDialog
    v-model:visible="visible"
    :confirm-loading="confirmLoading"
    @confirm="handleConfirm"
  >
    <template #title>
      <template v-if="isCreated">Create Label Rule</template>
      <div v-else class="flex items-center">
        <span class="text-lg mr-[18px]">Edit</span>
        <span
          class="text-center block text-[#fff] w-[22px] h-[22px] line-[22px] rounded-[6px] mr-[6px]"
          :class="labelLevel === 'asset' ? 'bg-[#02B875]' : 'bg-[#F2AA09]'"
        >
          {{ labelLevel[0].toUpperCase() }}
        </span>
        <span>{{ labelLevel === 'asset' ? 'Asset' : 'Serial' }} Level Label</span>
      </div>
    </template>
    <div class="w-[600px] space-y-[8px]">
      <Justify class="w-full">
        <template #left>
          <div />
        </template>
        <template #right>
          <t-button variant="text" theme="primary" @click="gotoLabelSystem">Label System</t-button>
        </template>
      </Justify>
      <t-form
        ref="formRef"
        class="max-h-[450px] overflow-y-scroll px-8"
        :colon="true"
        :rules="rules"
        :label-width="120"
        :data="formData"
        scroll-to-first-error="smooth"
      >
        <t-form-item
          v-if="labelLevel === 'serial'" label="Serial Name" name="rule"
          label-align="right"
        >
          <t-input v-model="formData.rule" />
        </t-form-item>
        <t-form-item
          v-if="labelLevel === 'asset'" label="Asset Name" name="rule"
          label-align="right"
        >
          <t-input v-model="formData.name" disabled />
        </t-form-item>
        <template v-for="(item, index) in actionFirstLabels as FirstLabelType[]" :key="index">
          <t-form-item
            :name="item.value"
            :label="item.value as string"
            label-align="right"
            :title="item.value"
            :required-mark="item.required"
          >
            <template #label>
              <span>
                {{ item.value.length > 10 ? (item.value.slice(0, 10) + '...') : item.value }}
              </span>
              <template v-if="item.label_method === 'intelligent'">
                <t-tooltip :content="disabledTip">
                  <InfoCircleIcon size="14" class="ml-[2px]" />
                </t-tooltip>
              </template>
            </template>
            <SelectInput
              v-model="formData[item.value] as string[]"
              overlay-inner-class-name="label-rule-popup"
              :options="item.children || []"
              :multiple="item.multiple"
              :mutex="item.mutex"
              :disabled="item.label_method === 'intelligent'"
            />
          </t-form-item>
        </template>
        <t-form-item
          v-if="labelLevel === 'serial'" label="Offline Date" name="offline_date"
          label-align="right"
        >
          <t-date-picker
            v-model="formData.offline_date"
            class="w-[220px]"
            :disable-date="{ before: now }"
            placeholder="select date"
          />
        </t-form-item>
      </t-form>
    </div>
  </BaseDialog>
</template>
<script setup lang="ts">
/* eslint-disable  no-restricted-syntax */
import { ref, computed, reactive } from 'vue';
import BaseDialog from 'common/components/Dialog/Base/index';
import { useLabelsManageStore } from '@/store/creative/labels/labels-manage.store';
import { storeToRefs } from 'pinia';
import Justify from 'common/components/Layout/Justify.vue';
import { useGoto } from '@/router/goto';
import { InfoCircleIcon } from 'tdesign-icons-vue-next';
import { SelectInput } from 'common/components/Select';
import { FirstLabelType } from 'common/service/creative/common/type';
import { LabelItem, RuleItem } from 'common/service/creative/label/manage/type';
import { addEditLabelRule, editAssetLabelRule } from 'common/service/creative/label/manage';
import { useGlobalGameStore } from '@/store/global/game.store';
import { MessagePlugin } from 'tdesign-vue-next';
import dayjs from 'dayjs';

const disabledTip = 'The labeling method for this label is intelligent labeling, without the need for manual processing.';

const { gotoLabelSystem } = useGoto();
const { gameCode } = storeToRefs(useGlobalGameStore());
const { firstLabels } = storeToRefs(useLabelsManageStore());

const isCreated = ref(true);
const confirmLoading = ref(false);
const formRef = ref();
const now = dayjs();

const props = defineProps<{
  labelLevel: 'serial' | 'asset';
}>();

const emit = defineEmits(['success']);

const actionFirstLabels = computed(() => firstLabels.value.filter(item => item.label_level === props.labelLevel));

const formData = computed<{[col: string]: string | string[]}>(() => {
  const labelParams: { [col: string]: string[] } = {};
  actionFirstLabels.value.forEach((item) => {
    if (isCreated.value) labelParams[item.label] = [];
    else {
      const validSeconds = item.children!.map(i => i.value);
      const secondLabel = (curItem.value as RuleItem)[item.label];
      let secLabelList: string[] = secondLabel ? secondLabel.split(',') : [];
      secLabelList = secLabelList.filter(s => validSeconds.includes(s));
      labelParams[item.label] = secLabelList;
    }
  });
  let offlineDate = curItem.value?.offline_date || '';
  if (offlineDate === 'NA') offlineDate = '';

  return reactive({
    rule: isCreated.value ? '' : curItem.value?.rule || '',
    name: curItem.value?.name || '',
    ...labelParams,
    offline_date: offlineDate,
  });
});

const rules = computed(() => {
  const ruleParams: { [col: string]: { required: boolean }[] } = {};
  actionFirstLabels.value.forEach((item) => {
    if (item.required) {
      ruleParams[item.label] = [{ required: true }];
    }
  });
  return {
    name: [
      { required: true, message: 'serial name is required' },
      {
        validator: (val: string) => {
          if (val?.length <= 50) return true;
          return {
            message: 'serial name cannot exceed 50 characters', result: false, type: 'error',
          };
        },
      },
    ],
    ...ruleParams,
  };
});

// 创建,编辑规则
const handleConfirm = async () => {
  const validateRes = await formRef.value.validate();
  if (validateRes !== true) return;

  const labels: LabelItem[] = [];
  for (const key in formData.value) {
    if (key === 'rule' || key === 'name' || key === 'offline_date') continue;
    const val = formData.value[key];
    if (val instanceof Array) {
      (formData.value[key] as string[]).forEach((val) => {
        labels.push({ first_label: key, second_label: val });
      });
    } else {
      labels.push({ first_label: key, second_label: val });
    }
  }

  confirmLoading.value = true;

  if (props.labelLevel === 'serial') {
    const res = await addEditLabelRule({
      game_code: gameCode.value, rule: formData.value.rule as string, labels, type: 2,
      id: isCreated.value ? undefined : curItem.value!.id,
      offline_date: (formData.value.offline_date as string).replaceAll('-', ''),
    });
    if (res.result.error_code !== 0) {
      MessagePlugin.error(res.result.error_message);
      return;
    }
  } else {
    const res = await editAssetLabelRule({
      game_code: gameCode.value,
      asset_name: formData.value.name as string,
      labels,
    });
    if (res.result.error_code !== 0) {
      MessagePlugin.error(res.result.error_message);
      return;
    }
  }
  confirmLoading.value = false;

  visible.value = false;
  emit('success');
};

const visible = ref(false);

const curItem = ref<RuleItem>();
const show = (item?: RuleItem) => {
  if (item) {
    curItem.value = item;
    isCreated.value = false;
  } else {
    isCreated.value = true;
  }
  visible.value = true;
};

defineExpose({
  show,
});
</script>
