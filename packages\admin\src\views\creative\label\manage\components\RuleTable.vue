<template>
  <div class="flex flex-col h-[200px] flex-1">
    <t-table
      class="label-manage-label flex flex-col"
      :loading="tableLoading"
      :columns="tableColumns"
      :expanded-row-keys="expandedRowKeys"
      resizable
      row-key="id"
      :data="tableData"
      @expand-change="handleExpandChange"
      @column-resize-change="onColumnSizeChange"
    >
      <!-- 展开行内容 -->
      <template #expand-icon="{ row }">
        <template v-if="row.isAsset" />
        <template v-else>
          <ChevronRightIcon size="16" />
        </template>
      </template>

      <template v-for="item in firstLabelCols" :key="item.colKey" #[`${item.title}`]>
        <div class="flex">
          <span
            v-if="item.label_level"
            class="text-center block text-[#fff] w-[22px] h-[22px] line-[22px] rounded-[6px] mr-[6px] min-w-[22px]"
            :class="item.label_level === 'asset' ? 'bg-[#02B875]' : 'bg-[#F2AA09]'"
          >
            <t-tooltip :content="item.label_level === 'asset' ? 'Asses Labels' : 'Serial Labels'">
              {{ item.label_level[0].toUpperCase() }}
            </t-tooltip>
          </span>
          {{ item.showName }}
        </div>
      </template>

      <template #expanded-row="{ row }">
        <t-loading v-if="row.loading" class="ml-[30px]" size="small" />
        <div
          v-if=" expandDataList[row.id] && expandDataList[row.id].length === 0"
          class="flex items-center justify-start w-full ml-[30px] text-gray-primary"
        >
          Empty Data
        </div>
      </template>

      <template #name="{ row }">
        <Text
          v-if="!row.isAsset"
          :content="splitName(row)"
          :overflow="true"
          :tool-tip="true"
          tips-placement="top-left"
        />
        <template v-else>
          <Text
            v-if="row.source === 1"
            :content="row.name"
            :overflow="true"
            theme="primary"
            :need-cursor="true"
            :tool-tip="true"
            tips-placement="top-left"
            @click="previewAsset(row)"
          />
          <Text
            v-else
            :content="row.name"
            :overflow="true"
            :tool-tip="true"
            tips-placement="top-left"
          />
        </template>
      </template>

      <template #actions="{ row }">
        <t-link theme="primary" class="mr-[12px]" @click="editItem(row)">
          <t-tooltip content="Edit Serial Labels">Edit</t-tooltip>
        </t-link>
        <t-link v-if="!row.isAsset" theme="primary" @click="deleteItem(row)">Delete</t-link>
      </template>
    </t-table>
    <t-pagination
      v-model:current="pageIndex"
      v-model:page-size="pageSize"
      class="my-[24px]"
      :total="totalCount"
      size="small"
      :page-size-options="[10, 20, 50]"
      @page-size-change="onPageSizeChange"
      @current-change="onPageIndexChange"
    />
  </div>
  <rule-item-dialog ref="ruleItemRef" :label-level="labelLevel" @success="editSuccess" />
  <Preview
    ref="previewRef"
    :type="previewItem.type"
    :title="previewItem.name"
    :hide-trigger="true"
    :get-url="getUrl"
  />
</template>
<script setup lang="tsx">
/* eslint-disable @typescript-eslint/no-misused-promises */
/* eslint-disable no-param-reassign */
import { reactive, ref, onMounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { DialogPlugin, MessagePlugin, TableProps } from 'tdesign-vue-next';
import { ChevronRightIcon } from 'tdesign-icons-vue-next';
import { useLabelsManageStore } from '@/store/creative/labels/labels-manage.store';
import RuleItemDialog from './RuleItemDialog.vue';
import { RuleAssetItem, RuleItem } from 'common/service/creative/label/manage/type';
import { deleteLabelRule } from 'common/service/creative/label/manage';
import { cloneDeep } from 'lodash-es';
import Text from 'common/components/Text';
import Preview, { IPreviewProps } from 'common/components/Dialog/Preview';
import { getVideoUrl } from '@/views/creative/label/manage/utils';

const { onPageSizeChange, onPageIndexChange, getTableData, onSizeChange, getAssetTable } = useLabelsManageStore();
const {
  tableData, tableColumns, tableLoading, pageIndex, pageSize, totalCount, expandedRowKeys, firstLabelCols, curTableList,
  expandDataList,
} = storeToRefs(useLabelsManageStore());

const props = defineProps<{
  game: string,
  depotToken: {
    arthubCode: string,
    publicToken: string,
  },
}>();


const isExpanding = computed(() => tableData.value.some(item => item.loading));  // 是否有正在请求的展开行

// 展开收起处理函数
const handleExpandChange = async (values: string[]) => {
  if (isExpanding.value) return;

  const curExpandedIds = cloneDeep(expandedRowKeys.value);
  expandedRowKeys.value = values as string[];

  const newExpandedId = values.filter(id => !curExpandedIds.includes(id as string))[0];
  if (newExpandedId) {
    const ruleItem = tableData.value.find(item => item.id === newExpandedId)!;
    ruleItem.loading = true;
    await getAssetTable(ruleItem);
    ruleItem.loading = false;
  }

  tableData.value = curTableList.value;
  values.forEach((ruleId) => {
    const ruleIndex = tableData.value.findIndex(item => item.id === ruleId);
    const preList = tableData.value.slice(0, ruleIndex + 1);
    const nextList = tableData.value.slice(ruleIndex + 1);

    const toExpandData = expandDataList.value[ruleId] || []; // 获取缓存的展开数据
    tableData.value = preList.concat(toExpandData).concat(nextList);
  });

  setTimeout(() => updateTableExpandRows(), 0);

  // 重新设置expandDataList
  Object.keys(expandDataList.value).forEach((key) => {
    if (!values.includes(key)) delete expandDataList.value[key];
  });
};

// 单独更新展开行数据
const updateExpandRow = async (ruleItem: RuleItem) => {
  expandedRowKeys.value = expandedRowKeys.value.filter(id => id !== ruleItem.id);
  tableData.value = tableData.value.filter(item => !item.id.includes(`${ruleItem.id}-`));  // 移除展开的行数据
  setTimeout(() => {
    const newIds = expandedRowKeys.value.concat(ruleItem.id);
    handleExpandChange(newIds);
  });
};

const ruleItemRef = ref();
const labelLevel = ref<'serial' | 'asset'>('serial');
const createRule = () => {
  ruleItemRef.value.show();
};

const actionRule = ref<RuleItem>();  // 操作的规则
const actionAsset = ref<RuleItem>();  // 操作的素材
const editItem = (item: RuleItem) => {
  if (item.isAsset) {
    labelLevel.value = 'asset';
  } else {
    labelLevel.value = 'serial';
  }
  actionRule.value = curTableList.value.find(ruleItem => item.id.includes(ruleItem.id))!;
  actionAsset.value = item;
  ruleItemRef.value.show(item);
};

// 编辑成功回调
const editSuccess = () => {
  if (actionAsset.value?.isAsset) {
    updateExpandRow(actionRule.value!);
  } else {
    getTableData();
  }
};

const deleteItem = (item: RuleItem) => {
  const confirmDia = DialogPlugin({
    header: 'Tips',
    body: 'Are you sure to delete it?',
    confirmBtn: 'Confirm',
    cancelBtn: 'Cancel',
    onConfirm: async () => {
      const res = await deleteLabelRule({ game_code: props.game, id: item.id });
      if (res.result.error_code !== 0) {
        MessagePlugin.error(res.result.error_message);
        return;
      }
      confirmDia.hide();
      getTableData();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

// 匹配 - _ 空格 进行分割
const splitName = (row: RuleItem) => {
  const regex = /[-_ ]+/;
  return row.name.split(regex).slice(0, 3)
    .join('-');
};

const previewRef = ref();
const previewItem = reactive<{
  asset_id: string,
  name: string,
  type: IPreviewProps['type'],
}>({
  asset_id: '', name: '', type: 'video',
});
const previewAsset = (item: RuleAssetItem) => {
  previewItem.asset_id = item.asset_id;
  previewItem.name = item.name;
  previewItem.type = item.type;
  previewRef.value.show();
};

const getUrl = async () => getVideoUrl(props.game, previewItem.asset_id);

// 表格列宽度变化
const onColumnSizeChange: TableProps['onColumnResizeChange'] = (context: { columnsWidth: { [colKey: string]: number }; }) => {
  onSizeChange(context);
};

// 移除展开行的空白行
function updateTableExpandRows() {
  const tableEle = document.querySelector('.label-manage-label') as HTMLElement;
  if (!tableEle) return;

  const tBodyEle = tableEle.querySelector('.t-table__body') as HTMLElement;
  const expandRows = tBodyEle.querySelectorAll('.t-table__expanded-row');
  expandRows.forEach((row) => {
    if (row.querySelector('.t-table__row-full-element')!.children.length === 0) tBodyEle.removeChild(row);
  });
}

onMounted(() => {
  updateTableExpandRows();
});

defineExpose({
  createRule,
});
</script>
