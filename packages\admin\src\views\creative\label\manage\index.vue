<template>
  <CommonView
    class="creative-labels-insight"
    :hide-right="true"
    :store="store"
    :form-props="{
      formList,
      modelValue: formModelValue,
      onSubmit: () => getTableData(true),
      onReset,
      'onUpdate:modelValue': updateFormModelValue,
    }"
  >
    <template #views>
      <div class="flex flex-col p-[16px] bg-white-primary rounded-extraLarge">
        <div class="flex justify-between mb-[16px]">
          <div>
            <t-button @click="gotoLabelSystem">Label System</t-button>
          </div>
          <div>
            <t-button variant="text" @click="changeMetrics">
              <template #icon><view-column-icon /></template>Label Metrics
            </t-button>
            <t-button variant="text" @click="createRule">
              <template #icon><AddIcon /></template>Add Creative Labels
            </t-button>
            <t-button variant="text" @click="importRule">
              <template #icon><ListIcon /></template>Bulk Add Creative Labels
            </t-button>
            <t-tooltip :content="downloadTip" placement="top-right">
              <t-button variant="text" :loading="downLoadLoading" @click="download">
                <template #icon><DownloadIcon /></template>Download
              </t-button>
            </t-tooltip>
          </div>
        </div>
        <rule-table ref="ruleTableRef" :game="gameCode" :depot-token="depotToken" />
      </div>
    </template>
  </CommonView>
  <batch-import-label
    ref="batchImportLabelRef"
    :label-list="firstLabels as FirstLabelType[]"
  />
  <CustomizeColumnsDialog
    v-model:visible="colDialogVisible"
    type="group"
    :min-count="3"
    :list="metricGroup"
    :selected-list="colCheckedList"
    @confirm="(list) => onColumnChange(list)"
  />
</template>
<script setup lang="ts">
import { ref, reactive } from 'vue';
import { storeToRefs } from 'pinia';
import CommonView from 'common/components/Layout/CommonView.vue';
import RuleTable from './components/RuleTable.vue';
import { AddIcon, DownloadIcon, ListIcon, ViewColumnIcon } from 'tdesign-icons-vue-next';
import { useLabelsManageStore } from '@/store/creative/labels/labels-manage.store';
import { FormModel, DepotToken } from 'common/service/creative/label/manage/type';
import { downloadRules } from 'common/service/creative/label/manage';
import { useGoto } from '@/router/goto';
import { getArthubToken } from 'common/service/creative/common/material';
import { useGlobalGameStore } from '@/store/global/game.store';
import BatchImportLabel from './components/BatchImportLabel.vue';
import { FILE_CDN_COM } from 'common/config';
import { FirstLabelType } from 'common/service/creative/common/type';
import CustomizeColumnsDialog, { IMetric }  from 'common/components/CustomizeColumnsDialog/index';
import { useWatchGameChange } from 'common/compose/request/game';

const { gameCode } = storeToRefs(useGlobalGameStore());
const { gotoLabelSystem } = useGoto();

const store = useLabelsManageStore();
const { getTableData, getParams, changeCheckedColList, onReset, init } = store;
const { formModelValue, formList, firstLabels, metricGroup, colCheckedList, expandedRowKeys } = storeToRefs(store);

const downloadTip = 'If the labeling method is intelligent labeling, the tags do not require manual processing and'
  + ' therefore are not included in the downloaded template.';

useWatchGameChange(async () => {
  // 先清空展开的行， 要不然会报错
  expandedRowKeys.value = [];
  const [data] = await Promise.all([getArthubToken(gameCode.value, true), init()]);
  if (data) {
    depotToken.type = Number(data.type);
    depotToken.arthubCode = data.arthub_code;
    depotToken.publicToken = data.public_token;
  }
  getTableData();
});

// 更新formModelValue数据
const updateFormModelValue = (formModel: FormModel) => {
  formModelValue.value = {
    ...formModelValue.value,
    ...formModel,
  };
};

// 批量导入规则
const batchImportLabelRef = ref();
const importRule = () => {
  batchImportLabelRef.value.show();
};

// 下载规则
const downLoadLoading = ref(false);
const download = async () => {
  downLoadLoading.value = true;
  const params = getParams(true);
  const res = await downloadRules(params);
  downLoadLoading.value = false;
  window.open(`${FILE_CDN_COM}${res.cos_file}`);
};

const ruleTableRef = ref();
const createRule = () => {
  ruleTableRef.value.createRule();
};

// 修改选中metrics
const colDialogVisible = ref(false);
const changeMetrics = () => {
  colDialogVisible.value = true;
};

const onColumnChange = (list: IMetric[]) => {
  expandedRowKeys.value = [];
  changeCheckedColList(list);
};

// 预览请求鉴权token
// 预览请求鉴权token
const depotToken = reactive<DepotToken>({
  arthubCode: '', publicToken: '', type: 1,
});
</script>
<style lang="scss" scoped>
:deep(.t-table__expanded-row) {
}
</style>
