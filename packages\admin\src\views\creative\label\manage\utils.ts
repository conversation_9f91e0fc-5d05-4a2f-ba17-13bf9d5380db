import { useEventBus } from '@vueuse/core';
import { getBaseUrlByEnv } from 'common/utils/baseUrl';

export const videoBus = useEventBus<string>('label-manage');

export const getVideoUrl = (game: string, assetId: string) => {
  const baseUrl = getBaseUrlByEnv();
  const id = encodeURIComponent(`id:${assetId}`);
  return `https:${baseUrl}/api_v2/creative/common/preview?game=${game}&path=${id}&redirect=1`;
};

// https://api.aix.levelinfinite.com/api_v2/creative/common/preview?game=mc_demo&path=id%3AtP0yf0XSxjsAAAAAAAAAJA&redirect=1
// https://api.aix.levelinfinite.com/api_v2/creative/common/preview?game=mc_demo&path=id%3AtP0yf0XSxjsAAAAAAAAAMA&redirect=1
