<template>
  <CommonView
    v-loading="showPageLoading"
    :need-back="true"
    :router-index="-3"
    :hide-right="true"
    :is-hover="true"
  >
    <template #views>
      <template v-if="showErrs">
        <t-card>
          <div class="w-full h-[500px] flex items-center justify-center flex-col space-y-[18px]">
            <Text theme="danger" :content="`Errors: ${showErrs}`" />
            <div class="space-x-[8px]">
              <t-button @click="detailId = 'new'">
                Create New Rule
              </t-button>
              <t-button
                theme="default"
                @click="gotoCreativeAutoRulesList"
              >
                Go Back List Page
              </t-button>
            </div>
          </div>
        </t-card>
      </template>
      <t-form
        v-else
        ref="formRef"
        label-align="top"
        :data="ruleData"
        class="space-y-[20px] pb-[100px]"
        @submit="onSubmit"
      >
        <t-card class="pb-[8px]">
          <t-form-item
            label="Rule Name"
            name="rule_name"
            :rules="[
              { required: true, type: 'error' },
              { max: 50, type: 'error', message: 'The content character length exceeds the limit.' },
              { whitespace: true, type: 'error', message: 'rule name is required' },
              { validator: ruleNameValidator, trigger: 'change' },
            ]"
          >
            <t-input
              v-model="ruleData.rule_name"
              class="w-full"
            >
              <template #suffix>
                <t-loading v-if="checkingRuleName" size="small" />
              </template>
            </t-input>
          </t-form-item>
        </t-card>
        <t-card>
          <t-form-item
            label="Select Folders To Be Synced"
            name="folders"
            :rules="[{
              required: true,
              type: 'error',
              message: 'Please select folders to be synced.'
            }]"
          >
            <folders
              v-model="ruleData.folders"
              :detail-list="ruleData.folderDetails"
            />
          </t-form-item>
          <t-form-item
            label="Select Asset Type"
            name="type"
            :rules="[{required: true, type: 'error'}]"
          >
            <t-radio-group v-model="ruleData.type" default-value="0">
              <t-radio-button value="0">All</t-radio-button>
              <t-radio-button value="1">Video</t-radio-button>
              <t-radio-button value="2">Image</t-radio-button>
              <t-radio-button value="3">HTML</t-radio-button>
            </t-radio-group>
          </t-form-item>
          <t-form-item
            label="Apply to Assets"
            name="start_cloud_upload_time"
            :rules="[{required: true, type: 'error'}, { validator: checkApplyToAsset, type: 'error'}]"
            class="pb-[16px] apply-to-assets"
          >
            <span class="mr-[16px]">Files uploaded to the cloud drive from (Inclusive)</span>
            <!-- enableTimePicker -->
            <t-date-picker
              v-model="ruleData.start_cloud_upload_time"
              :disable-date="{ after: dayjs().format(), before: dayjs().subtract(6, 'month').format() }"
              suffix-icon="(UTC+0)"
              placeholder="Please select"
            />
          </t-form-item>
        </t-card>
        <t-card
          class="pb-[8px]"
        >
          <t-form-item
            label="Automatic Execution Task"
            :rules="[{required: true, type: 'error'}]"
            name="medias"
          >
            <TaskList
              v-model="ruleData.medias"
              :total-media-list="['Google', 'Facebook', 'TikTok', 'Twitter', 'Unity', 'Snapchat']"
              @scroll-to-bottom="scrollToBottom"
            />
          </t-form-item>
        </t-card>
        <div class="fixed z-0 bottom-0 right-0 w-full bg-white h-[50px] flex items-center px-[24px] justify-between">
          <div />
          <t-space>
            <t-button
              theme="default"
              @click="cancel"
            >
              Cancel
            </t-button>
            <t-button
              type="submit"
              theme="default"
              :loading="saveLoading"
              :disabled="!canSave
                || ruleData.status === AutomaticSyncTaskRuleStatus.ENABLE"
            >
              Save
            </t-button>
            <t-button
              theme="primary"
              :disabled="!canSave"
              :loading="saveLoading"
              @click="saveAndActivate"
            >
              Save & Activate
            </t-button>
          </t-space>
        </div>
      </t-form>
    </template>
  </CommonView>
</template>
<script setup lang="ts">
import { reactive, ref, computed, watch } from 'vue';
import CommonView from 'common/components/Layout/CommonView.vue';
import Folders from '@/views/creative/library/components/rules/folders.vue';
import Text from 'common/components/Text';
import { useConfirmDialog } from 'common/compose/useDialog';
import { useGoto } from '@/router/goto';
import TaskList from '@/views/creative/library/components/rules/TaskList.vue';
import { usePageContainer } from '@/compose/usePageContainer';
import { useWatchGameChange } from 'common/compose/request/game';
import { useRouteQuery } from '@vueuse/router';
import { tryOnMounted, useDebounceFn } from '@vueuse/core';
import { useLoading } from 'common/compose/loading';
import {
  addAutomaticSyncTaskRule,
  AutomaticSyncTaskRuleStatus,
  checkAutomaticSyncTaskRuleName,
  getAutomaticSyncTaskRule,
  TAutomaticSyncTaskRule,
} from 'common/service/creative/rules/config';
import { useTips } from 'common/compose/tips';
import { useGlobalGameStore } from '@/store/global/game.store';
import { FormInstanceFunctions, FormProps, MessagePlugin } from 'tdesign-vue-next';
import { getPathItem } from '@/views/creative/library/compose/folder/init-folder';
import { useAuthStageStore } from '@/store/global/auth.store';
import { MediaType, TTaskItem } from '@/views/creative/library/define';
import { MaterialMediaList, MaterialMediaMap } from '@/views/trade/ads_creation/common/template/config';
import { monthOffset } from './utils';
import dayjs from 'dayjs';
import { useStatusSwitchDialog } from './components/dialog/StatusSwitchDialog';

const { gotoCreativeAutoRulesList } = useGoto();
const { scrollToBottom } = usePageContainer();
const { isLoading: showPageLoading, showLoading, hideLoading } = useLoading();
const { success, err } = useTips();
const gameStore = useGlobalGameStore();
const authStore = useAuthStageStore();

const formRef = ref<FormInstanceFunctions>();

const initData: any = {
  folders: [],
  rule_name: '',
  type: '0',
  medias: [{
    id: 0,
    media: '',
    accounts: [],
  }],
  folderDetails: [],
  start_cloud_upload_time: '',
};
let ruleData = reactive<{
  folders: string[],
  folderDetails: any[],
  rule_name: string,
  type: string,
  medias: TTaskItem[]
  status?: AutomaticSyncTaskRuleStatus,
  start_cloud_upload_time: string,
}>({ ...initData });

const canSave = computed(() => ruleData.rule_name.trim().length > 0
  && ruleData.folders.length > 0
  && ruleData.type.length > 0
  && ruleData.medias.length > 0);

// TODO 如果 id 是new, 则直接使用初始化内容，如果id不是new，则等待数据拉取回来。
const detailId = useRouteQuery<string>('id', 'new');
const copyId = useRouteQuery<string | undefined | null>('copy');
const isEdit = computed(() => detailId.value !== 'new');
const showErrs = ref<string>();

const checkApplyToAsset = () => {
  const monthOffNum = monthOffset(ruleData.start_cloud_upload_time, dayjs(new Date()).format('YYYY-MM-DD'));
  if (monthOffNum > 6) {
    return { flag: false, message: 'Only assets uploaded within 6 months are supported. Please update the Setting.', type: 'error' };
  }
  return true;
};

const initDetail = async (copyId?: string) => {
  showLoading();
  const detailInfo = await getAutomaticSyncTaskRule({
    id: copyId || detailId.value,
    game_code: gameStore.gameCode,
  });
  hideLoading();
  if (detailInfo.result.error_code !== 0) {
    showErrs.value = detailInfo.result.error_message;
  } else {
    // detailInfo.task_rules
    const currentRule = detailInfo.task_rules[0] || {};
    ruleData.rule_name = copyId ? `${currentRule.name.slice(0, 45)}_copy` : currentRule.name;
    ruleData.folderDetails = currentRule.dirs;
    ruleData.folders = currentRule.dirs.map(i => i.id);
    ruleData.type = String(currentRule.asset_type);
    ruleData.start_cloud_upload_time = currentRule.start_cloud_upload_time.substring(0, 10);
    ruleData.medias = currentRule.medias.map((i, index) => ({
      id: index,
      media: MaterialMediaList[i.channel],
      accounts: i.accounts.split(','),
      language: i.language,
    }));
    ruleData.status = currentRule.status;
  }
};

tryOnMounted(async () => {
  if (isEdit.value) {
    await initDetail();
  } else {
    if (copyId.value) {
      await initDetail(copyId.value);
    }
  }
});

watch(() => detailId.value, (val) => {
  if (val === 'new') {
    showErrs.value = '';
    ruleData = reactive({ ...initData });
  } else {
    initDetail();
  }
});


useWatchGameChange(async () => {
  ruleData = reactive({ ...initData });
});

const cancel = () => {
  useConfirmDialog({
    theme: 'warning',
    body: 'Your changes will not be saved. Are you sure you want to go back?',
    confirmBtn: 'Go Back',
    onConfirm: () => {
      gotoCreativeAutoRulesList();
    },
  });
};

const checkingRuleName = ref(false);
const checkSameName = useDebounceFn((resolve, value: string) => {
  checkingRuleName.value = true;
  checkAutomaticSyncTaskRuleName({
    game_code: gameStore.gameCode,
    name: value,
    id: detailId.value === 'new' ? '0' : detailId.value,
  })
    .then((res) => {
      if (res.result.error_code !== 0) {
        return resolve({
          result: false,
          type: 'error',
          message: res.result.error_message,
        });
      }
      if (res.duplicate) {
        return resolve({
          result: false,
          type: 'error',
          message: 'The rule name has already been used, please input another one.',
        });
      }
      return resolve({
        result: true,
        type: 'success',
      });
    })
    .catch((errMsg) => {
      err(errMsg.message || errMsg);
    })
    .finally(() => {
      checkingRuleName.value = false;
    });
}, 500);

const ruleNameValidator = (value: string) => new Promise((resolve) => {
  checkSameName(resolve, value);
});

const saveLoading = ref(false);
const save = async (status = AutomaticSyncTaskRuleStatus.DISABLED) => {
  const baseTaskRule: TAutomaticSyncTaskRule = {
    game_code: gameStore.gameCode,
    name: ruleData.rule_name,
    status, // 默认不启用
    asset_type: Number(ruleData.type),
    start_cloud_upload_time: `${ruleData.start_cloud_upload_time} 00:00:00`,
    dirs: ruleData.folders.map((id) => {
      // 从缓存中获取数据
      let origin = getPathItem(id);
      // 如果缓存中数据不存在，则从历史数据中获取
      if (Object.keys(origin).length === 0) {
        origin = ruleData.folderDetails.find(i => i.id === id) || {};
      }
      return {
        id: origin.id,
        name: origin.name,
        full_path_name: origin.full_path_name,
        include_sub_dir: true,
      };
    }),
    medias: ruleData.medias.map(i => ({
      channel: MaterialMediaMap[i.media as MediaType],
      accounts: Array.isArray(i.accounts) ? i.accounts.join(',') : i.accounts,
      language: i.language,
      campaign_id: i.campaign_id,
      creative_set_name: i.creative_set_name,
      countries: i.countries,
    })),
  };
  if (isEdit.value) {
    baseTaskRule.update_user = authStore.currentUser;
    baseTaskRule.id = detailId.value;
  } else {
    baseTaskRule.create_user = authStore.currentUser;
  }
  saveLoading.value = true;
  const res = await addAutomaticSyncTaskRule({
    task_rule: baseTaskRule,
  });
  saveLoading.value = false;
  if (res.result.error_code === 0) {
    // 更新当前url的配置
    detailId.value = res.id;
    copyId.value = null;
    if (status === AutomaticSyncTaskRuleStatus.ENABLE) {
      await success('Save & activate Success');
    } else {
      await success('Save Success');
    }
  } else {
    await err(res.result.error_message || 'NetWork Err, Please try again later');
  }
};

const saveAndActivate = async () => {
  const validateRes = await formRef.value?.validate();
  if (validateRes === true) {
    const bodyText = 'Files uploaded to cloud drive from (inclusive) [bold] in the selected folders will sync automatically. This includes all future uploads while the rule is on.';
    const boldText = [`${ruleData.start_cloud_upload_time} (UTC+0)`];
    useStatusSwitchDialog({
      currStatus: true,
      header: 'Notice',
      bodyText,
      boldText,
      async confirmFunc() {
        await save(AutomaticSyncTaskRuleStatus.ENABLE);
        await gotoCreativeAutoRulesList();
      },
      cancleFunc: () => {},
    }, 6);
  }
};


const onSubmit: FormProps['onSubmit'] = async ({ validateResult, firstError }) => {
  if (validateResult === true) {
    await save();
  } else {
    console.log('Validate Errors: ', firstError, validateResult);
    await MessagePlugin.warning(firstError!);
  }
};

</script>
<style scoped lang="scss">
:deep(.t-form__label--top) {
  color: #000;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  text-transform: capitalize;
}
.apply-to-assets {
  :deep(.t-input__suffix-icon){
    color: rgba(0, 0, 0, 0.4);
  }
}
</style>
