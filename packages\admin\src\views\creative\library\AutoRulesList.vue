<template>
  <CommonView
    :need-back="true"
    :router-index="2"
    :hide-right="true"
  >
    <template #views>
      <DataContainer
        class="pt-[16px] rounded-large mb-[24px]"
        :loading="isLoading"
        :total="pageInfo.total"
        :page-size="pageInfo.pageSize"
        :default-page="pageInfo.page"
        @on-page-change="onPageChange"
      >
        <template #attributeSlot>
          <t-button
            theme="primary"
            @click="()=>gotoCreativeAutoRulesDetail({id: 'new'})"
          >
            <template #icon>
              <AddIcon />
            </template>
            Create Rule
          </t-button>
        </template>
        <template #actionSlot>
          <div />
        </template>
        <Table
          ref="tableRef"
          v-model:display-columns="displayCols"
          :data="dataList"
          row-key="id"
          :columns="cols"
          max-height="1070px"
          class="overflow-auto"
        />
      </DataContainer>
    </template>
  </CommonView>
</template>
<script setup lang="ts">
import CommonView from 'common/components/Layout/CommonView.vue';
import DataContainer from 'common/components/Layout/DataContainer.vue';
import Table from 'common/components/table';
import { AddIcon } from 'tdesign-icons-vue-next';
import { useGoto } from '@/router/goto';
import { useAutoRulesTable } from '@/views/creative/library/compose/auto-rules-list-table';
import { useLoading } from 'common/compose/loading';
import { onMounted, ref, reactive } from 'vue';
import { getAutomaticSyncTaskRule } from 'common/service/creative/rules/config';
import { AssertTypeList } from '@/views/creative/library/define';
import { MaterialMediaList } from '@/views/trade/ads_creation/common/template/config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useWatchGameChange } from 'common/compose/request/game';

const { gotoCreativeAutoRulesDetail } = useGoto();
const { isLoading, showLoading, hideLoading } = useLoading();
const gameStore = useGlobalGameStore();

const pageInfo = reactive({
  total: 0,
  pageSize: 10,
  page: 1,
});

const onPageChange = (current: number, { pageSize }: any) => {
  console.log('on page change', current, pageInfo);
  pageInfo.page = current;
  pageInfo.pageSize = pageSize;
  updateTaskList();
};

const dataList = ref<any[]>([]);
const updateTaskList = async () => {
  showLoading();
  const list = await getAutomaticSyncTaskRule({
    game_code: gameStore.gameCode,
    page: pageInfo.page - 1,
    page_size: pageInfo.pageSize,
  });
  dataList.value = list.task_rules.map(i => ({
    id: i.id,
    switch: i.status,
    rule_name: i.name,
    folders: i.dirs.map(ii => `${ii.full_path_name.replaceAll(',', ' > ')} > ${ii.name}`),
    type: AssertTypeList[i.asset_type],
    media_list: i.medias.map(ii => MaterialMediaList[ii.channel]),
    create_time: i.create_time,
    creator: i.create_user,
    update_time: i.update_time,
    start_cloud_upload_time: i.start_cloud_upload_time.substring(0, 10),
  }));
  pageInfo.total = list.total;
  hideLoading();
};
const { cols, displayCols } = useAutoRulesTable({
  updateList: updateTaskList,
});

useWatchGameChange(async () => {
  await updateTaskList();
});

onMounted(async () => {
  await updateTaskList();
});

</script>
<style scoped lang="scss">

</style>
