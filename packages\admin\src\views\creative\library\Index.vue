<template>
  <common-view
    :store="aixLibraryStore"
    class="h-screen pb-0"
    :form-props="showPortal ? [] : filterConfig"
    :hide-right="showPortal"
    :tab-props="{
      modelValue: tabValue,
      list: list,
      onAddView,
      onUpdateView,
      onDeleteView,
      shareParams,
      isShowViewType: false,
      'onUpdate:modelValue' : (newValue: string) => tabValue = newValue,
    }"
  >
    <template #views>
      <Portal
        v-if="showPortal"
        :status="rootInfo?.cloud_drive_status"
      />
      <ResizeableColumns
        v-else
        class="h-full"
        :width="250"
        :min-width="250"
        :max-width="500"
      >
        <template #left>
          <LeftDictionary
            class="left-nav-height"
            :disable-action="aixLibraryStore.dictionary.type === 'dropbox'"
            :tree-list="aixLibraryStore.dictionary.dictionaryList"
            :default-expanded="aixLibraryStore.dictionary.defaultExpanded"
            :default-actived="aixLibraryStore.dictionary.activeFolderId"
            :loading="aixLibraryStore.isLoading"
            @active-change="activeChange"
            @open-folder="aixLibraryStore.dictionary.changeActiveDictionary"
            @show-more="aixLibraryStore.dictionary.initChildFolder"
          />
        </template>
        <template #right>
          <ContentContainer
            type="aix"
            class="ml-[18px]"
            :list="aixLibraryStore.material.materialList"
            :loading="aixLibraryStore.material.loading"
            :store="aixLibraryStore"
          />
        </template>
      </ResizeableColumns>
    </template>
  </common-view>
</template>

<script setup lang="tsx">
import { onBeforeMount, onMounted, computed, ref, watch, toRefs } from 'vue';
import { get } from '@vueuse/core';
import CommonView from 'common/components/Layout/CommonView.vue';
import type { ITree } from 'common/components/TreeMenu';
import ResizeableColumns from 'common/components/Layout/ResizeableColumns.vue';
import LeftDictionary from '@/views/creative/library/components/LeftDictionary.vue';
import ContentContainer from '@/views/creative/library/components/ContentContainer.vue';
import useAixLibraryStore from '@/store/creative/library/aix-library.store';
import { useGenFormData } from 'common/compose/form/gen-form-data';
import { getAixLibraryFilterConfig } from '@/views/creative/library/config/filter-config';
import { useLabelFilter } from '@/views/creative/library/compose/label-filter';
import { resetTypeMap } from 'common/components/FormContainer/index';
import { useTabView } from '@/views/creative/library/compose/tab-view';
import Portal from '@/views/creative/library/Portal.vue';
import { getRootIdNoCache } from 'common/service/creative/library/get-dictionary-list';

const aixLibraryStore = useAixLibraryStore();
const { submit, reset } = useLabelFilter(aixLibraryStore);

const rootInfo = await getRootIdNoCache();
const showPortal = computed(() => rootInfo?.cloud_drive_status !== 'active');

// 筛选器模块
const filterModelData = ref();
const filterConfig = ref(
  useGenFormData(
    computed(() => getAixLibraryFilterConfig(
      aixLibraryStore.label.labelOptionList,
      'iegg',
      aixLibraryStore.dictionary.type === 'dropbox',
      filterModelData.value === 2,
    ),
    ),
    (formData: any) => submit(formData),
    () => reset(),
    undefined,
    {
      resetType: resetTypeMap.initial,
    },
  ),
);

watch(
  () => filterConfig.value,
  (val) => {
    filterModelData.value = val.modelValue.syncedStatus;
  },
  {
    deep: true,
  },
);

// 视图内容模块
// 不知道为啥，直接传递对象会导致死循环，这里只能作为一个结构来传递
const {
  list,
  modelValue: tabValue,
  shareParam: shareParams,
  onAddView,
  onUpdateView,
  onDeleteView,
} = toRefs(
  useTabView({
    module: 'creative-library-index',
    params: computed(() => filterConfig.value.modelValue), // 需要保存到视图的参数
    needShare: true,
    onTabViewParamChange: (viewTabConfig: any) => {
      // 当视图切换到非default视图的时候
      if (viewTabConfig.value === 'default') {
        filterConfig.value.modelValue = filterConfig.value.defaultValue;
        reset();
      } else {
        // set condition when tabview change
        filterConfig.value.modelValue = viewTabConfig.param || {};
        // update library
        submit(get(viewTabConfig.param || {}));
      }
    },
  }),
);

// 选中目录
function activeChange(treeItem: ITree, deep: number, index: number, activeValue: string) {
  aixLibraryStore.dictionary.changeActiveDictionary(activeValue);
}

onBeforeMount(() => {
  console.log('before  mounted');
  console.time('mounted');
});

onMounted(() => {
  console.timeEnd('mounted');
});
</script>

<style scoped>
.left-nav-height {
  height: calc(100% - 24px);
  display: flex;
  flex-direction: column;
}
</style>
