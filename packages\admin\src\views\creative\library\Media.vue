<template>
  <common-view
    :store="mediaLibraryStore"
    class="h-screen pb-0"
    :form-props="filterInfo"
    hide-right
  >
    <template #views>
      <resizeable-columns
        class="h-full"
        :width="250"
        :min-width="250"
        :max-width="500"
      >
        <template #left>
          <LeftDictionary
            class="left-nav-height"
            library-type="advertise"
            :tree-list="list"
            :loading="mediaLibraryStore.isLoading"
            :disable-action="mediaLibraryStore.dictionary.type === 'dropbox'"
            @active-change="activeChange"
            @open-folder="mediaLibraryStore.dictionary.changeActiveDictionary"
            @show-more="mediaLibraryStore.dictionary.initChildFolder"
          />
        </template>
        <template #right>
          <ContentContainer
            type="media"
            class="ml-[20px]"
            :list="mediaLibraryStore.material.materialList"
            :loading="mediaLibraryStore.material.loading"
            :store="mediaLibraryStore"
          />
        </template>
      </resizeable-columns>
    </template>
  </common-view>
</template>

<script setup lang="tsx">
import CommonView from 'common/components/Layout/CommonView.vue';
import type { ITree } from 'common/components/TreeMenu/type';
import LeftDictionary from '@/views/creative/library/components/LeftDictionary.vue';
import ContentContainer from '@/views/creative/library/components/ContentContainer.vue';
import useMediaLibraryStore from '@/store/creative/library/media-library.store';
import ResizeableColumns from 'common/components/Layout/ResizeableColumns.vue';
import { useGenFormData } from 'common/compose/form/gen-form-data';
import { getAixLibraryFilterConfig } from '@/views/creative/library/config/filter-config';
import { useLabelFilter } from '@/views/creative/library/compose/label-filter';
import { computed, ref } from 'vue';

const mediaLibraryStore = useMediaLibraryStore();
const { submit } = useLabelFilter(mediaLibraryStore);

const filterInfo = ref(useGenFormData(
  computed(() => getAixLibraryFilterConfig(
    mediaLibraryStore.label.labelOptionList,
    'advertise',
    mediaLibraryStore.dictionary.type === 'dropbox'),
  ),
  (formData: any) => submit(formData),
));


const list = computed(() => mediaLibraryStore.dictionary.dictionaryList);

function activeChange(treeItem: ITree, deep: number, index: number, activeValue: string) {
  mediaLibraryStore.dictionary.changeActiveDictionary(activeValue);
}
</script>

<style scoped>
.left-nav-height {
  height: calc(100% - 24px);
}
</style>
