<template>
  <div class="h-[calc(100%-32px)] bg-white rounded-lg flex flex-row items-center">
    <div class="w-[30%] pl-[72px] mx-auto space-y-[34px]">
      <img
        :src="aixLibrary"
        class="h-[50px] w-[262px]"
      >
      <div class="w-[85%] text-left text-[#202A41] text-base font-normal">
        Centralize, tag, and sync assets across media channels—all in one scalable library.
      </div>
      <div class="flex flex-row items-center gap-2">
        <t-button
          class="w-[161px] flex items-center justify-start"
          @click="cloudDriveDialogRef?.show()"
        >
          <Icon
            :name="statusIcon"
            class="w-5 h-5 mr-2"
          />
          {{ statusName }}
        </t-button>
        <t-button
          class="w-[106px] flex items-center justify-start"
          @click="openNewWindow"
        >
          Learn More
        </t-button>
      </div>
      <div>
        <div class="brand-title">Supported Media Channel：</div>
        <div class="brand-container">
          <div
            v-for="item in BRAND_LIST"
            :key="item.id"
            class="brand-item"
          >
            <img
              :src="item.icon"
              class="brand-icon"
            >
            <span class="brand-text">{{ item.title }}</span>
          </div>
          <div
            class="brand-item"
            style="gap: 4px"
          >
            <svg
              v-for="i in ['eclipse_0', 'eclipse_1', 'eclipse_2']"
              :key="i"
              xmlns="http://www.w3.org/2000/svg"
              width="4"
              height="4"
              viewBox="0 0 4 4"
              fill="none"
            >
              <circle
                cx="2"
                cy="2"
                r="2"
                fill="#747D98"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
    <div
      class="w-[70%] flex-1 h-full rounded-tl-[80px] rounded-tr-[8px] rounded-br-[8px] rounded-bl-[80px] bg-gradient-to-br from-[#5086F3_3.95%] to-[#5055F3_128.22%] text-lg"
    >
      <t-swiper
        class="h-full"
        :navigation="{
          showSlideBtn: 'never',
          placement: 'inside',
          type: 'dots',
          spaceBetween: '8px',
          size: 'large',
        }"
      >
        <t-swiper-item
          v-for="item in 3"
          :key="item"
        >
          <img :src="[bg1, bg2, bg3][item - 1]">
        </t-swiper-item>
      </t-swiper>
    </div>
  </div>
  <CloudDriveDialog ref="cloudDriveDialogRef" />
</template>
<script lang="ts" setup>
import { ref, ComponentPublicInstance, computed } from 'vue';
import CloudDriveDialog from '@/views/creative/library/components/dialog/clouddrive.vue';
import bg1 from '@/assets/img/creatives/bg1.png';
import bg2 from '@/assets/img/creatives/bg2.png';
import bg3 from '@/assets/img/creatives/bg3.png';
import aixLibrary from '@/assets/img/creatives/aix_library.png';
import googleIcon from '@/assets/img/creatives/google_ic.png';
import facebookIcon from '@/assets/img/creatives/facebook_ic.png';
import tiktokIcon from '@/assets/img/creatives/tiktok_ic.png';
import twitterIcon from '@/assets/img/creatives/twitter_ic.png';
import unityIcon from '@/assets/img/creatives/unity_ic.png';
import snapchatIcon from '@/assets/img/creatives/snapchat_ic.png';
import applovinIcon from '@/assets/img/creatives/applovin_ic.png';
import { Icon } from 'tdesign-icons-vue-next';
import { useStatusIcon, useStatusName } from './compose/drive-status-const';
import { usePortalStore } from '@/store/creative/library/portal.store';

const cloudDriveDialogRef = ref<ComponentPublicInstance<typeof CloudDriveDialog>>();
const BRAND_LIST = [
  {
    id: 'google_ic',
    title: 'Google',
    icon: googleIcon,
  },
  {
    id: 'facebook_ic',
    title: 'Facebook',
    icon: facebookIcon,
  },
  {
    id: 'tiktok_ic',
    title: 'TikTok',
    icon: tiktokIcon,
  },
  {
    id: 'twitter_ic',
    title: 'Twitter',
    icon: twitterIcon,
  },
  {
    id: 'unity_ic',
    title: 'Unity',
    icon: unityIcon,
  },
  {
    id: 'snapchat_ic',
    title: 'Snapchat',
    icon: snapchatIcon,
  },
  {
    id: 'applovin_ic',
    title: 'Applovin',
    icon: applovinIcon,
  },
];

const props = defineProps({
  status: {
    type: String as () => 'pending' | 'initializing' | 'unauthorized' | 'active',
    default: '',
  },
});

const statusIcon = computed(() => useStatusIcon(props.status || 'pending'));
const statusName = computed(() => useStatusName(props.status || 'pending'));

const portalStore = usePortalStore();
if (props.status === 'initializing') {
  portalStore.driveStatus = props.status;
  portalStore.initStep(3);
}

function openNewWindow() {
  window.open('https://aix.levelinfinite.com/docs/user_guide/creative/aix_library_instructions.html', '_blank');
}
</script>
<style lang="scss" scoped>
.brand-title {
  color: #747d98;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.brand-container {
  /* 容器布局 */
  display: flex;
  flex-wrap: wrap;
  gap: 18px; /* 行间距, 列间距12px */
  max-width: 85%;
  margin-top: 18px;
}
.brand-item {
  /* 单项布局 */
  display: inline-flex;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  transition: transform 0.2s;
}

.brand-item:hover {
  transform: translateY(-2px);
}

.brand-icon {
  /* 图标尺寸控制 */
  width: 24px;
  height: 24px;
  object-fit: contain;
  flex-shrink: 0;
}

.brand-text {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  color: #202a41;
  line-height: 22px;
  white-space: nowrap;
}

:deep(.t-swiper__wrap) {
  height: 100%;
  display: flex;
  flex-direction: column;
  .t-swiper__content {
    flex: 8;
    position: relative;
    .t-swiper__container {
      height: 100%;
      .t-swiper__container__item {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2%; /* 留出安全边距 */
        img {
          max-width: clamp(300px, 90%, 2000px);
          max-height: clamp(200px, 90%, 1600px);
          width: auto;
          height: auto;
          object-fit: contain;
          object-position: center;
          margin: auto;
        }
      }
    }
  }
  .t-swiper__navigation {
    flex: 2; /* 占20%高度 */
    gap: 10px;
    min-height: 75px; /* 导航最小高度 */
    .t-swiper__navigation-item span {
      min-height: 10px;
      min-width: 10px;
    }
  }
}
</style>
