<template>
  <CommonView
    :need-back="true"
    :router-index="-2"
    :hide-right="true"
    :form-props="formInfo"
    :store="store"
  >
    <template #views>
      <t-card class="w-full mb-[20px]">
        <template #title>
          <span class="font-bold"> Time Period</span>
        </template>
        <div class="flex flex-row justify-around">
          <div class="flex flex-row items-center space-x-[24px]">
            <BasicChart
              class="w-[120px] h-[120px] total-chart"
              chart-type="pie"
              :chart-style="{ height: '120px', width: '120px' }"
              :data-mode="DataMode.y"
              detail-type="ring"
              data-value-filed="value"
              data-item-field="name"
              :is-show-legend="false"
              :is-show-bar-label="false"
              :grid="{left: '0', right: '0', top:'0', bottom:'0'}"
              :color="['#69D19C','#F17C86','#6E7794']"
              :radius="['70%', '100%']"
              :data="[
                { value: store.statics.success, name: 'Successful' },
                { value: store.statics.failed, name: 'Failed' },
                { value: store.statics.canceled, name: 'Cancelled' },
              ]"
            />
            <div class="w-full h-[56px] flex flex-col justify-around">
              <p class="font-bold text-xl">{{ store.statics.total }}</p>
              <span class="text-[#747D98]">Total Assets</span>
            </div>
          </div>
          <div class="flex flex-row items-center space-x-[24px]">
            <t-progress
              theme="circle"
              :percentage="+(store.statics.success / store.statics.total * 100).toFixed(2) || 0"
              :size="56"
              :stroke-width="8"
              color="#69D19C"
              track-color="#F0F1F6"
            />
            <div class="w-full h-[56px] flex flex-col justify-around">
              <p class="font-bold text-xl">{{ store.statics.success }}</p>
              <span class="text-[#747D98]">Successful</span>
            </div>
          </div>
          <div class="flex flex-row items-center space-x-[24px]">
            <t-progress
              theme="circle"
              :percentage="+(store.statics.failed / store.statics.total * 100).toFixed(2) || 0"
              :size="56"
              :stroke-width="8"
              color="#F17C86"
              track-color="#F0F1F6"
            />
            <div class="w-full h-[56px] flex flex-col justify-around">
              <p class="font-bold text-xl">{{ store.statics.failed }}</p>
              <span class="text-[#747D98]">Failed</span>
            </div>
          </div>
          <div class="flex flex-row items-center space-x-[24px]">
            <t-progress
              theme="circle"
              :percentage="+(store.statics.canceled / store.statics.total * 100).toFixed(2) || 0"
              :size="56"
              :stroke-width="8"
              color="#6E7794"
              track-color="#F0F1F6"
            />
            <div class="w-full h-[56px] flex flex-col justify-around">
              <p class="font-bold text-xl">{{ store.statics.canceled }}</p>
              <span class="text-[#747D98]">Cancelled</span>
            </div>
          </div>
        </div>
      </t-card>
      <DataContainer
        class="pt-[16px] rounded-large mb-[24px]"
        :total="store.pageTotal"
        :page-size="store.pageSize"
        :data="store.taskList"
        :loading="store.isLoading"
        :default-page="store.pageIndex"
        :page-size-options="[5, 10, 20, 50, 200, 500]"
        @on-page-change="onPageChange"
        @on-download-file="emitDownLoad"
        @on-show-metric-select="()=>tableRef.showMetricsSelect()"
      >
        <template #attributeSlot>
          <div class="flex items-center space-x-[8px]">
            <t-button
              theme="primary"
              :disabled="store.isOperateLoading || store.batchResumeList.length === 0"
              @click="store.resumeUpload(store.batchResumeList)"
            >
              Re-upload
            </t-button>
            <t-button
              theme="default"
              :disabled="store.isOperateLoading || store.batchCancelList.length === 0"
              @click="store.cancelUpload(store.batchCancelList)"
            >
              Cancel upload
            </t-button>
          </div>
        </template>
        <Table
          ref="tableRef"
          v-model:display-columns="displayCols"
          :data="store.taskList"
          row-key="id"
          :columns="cols"
          max-height="1070px"
          :resizable="true"
          :reserve-selected-row-on-paginate="true"
          @select-change="store.onSelectChange"
        />
      </DataContainer>
    </template>
  </CommonView>
</template>

<script setup lang="ts">
import CommonView from 'common/components/Layout/CommonView.vue';
import DataContainer from 'common/components/Layout/DataContainer.vue';
import { ref, toRaw, defineAsyncComponent, watch, computed } from 'vue';
import { useGenFormData } from 'common/compose/form/gen-form-data';
import { getAxiTaskFilterConfig } from '@/views/creative/library/config/task-filter';
import { useCreativeAixTaskStore } from '@/store/creative/library/task.store';
import { DEFAULT_TASK_LIST_CONDITION } from '@/store/creative/library/const';
import { useTaskTable } from '@/views/creative/library/compose/task-table';
import Table from 'common/components/table';
import { tryOnBeforeUnmount, useDocumentVisibility, get } from '@vueuse/core';
import { useDownloadTask } from '@/views/creative/library/compose/use-download-task';
import { useUrlDefault } from 'common/compose/url-default';
import { DataMode } from 'common/components/BasicChart/type.d';
import { useWatchGameChange } from 'common/compose/request/game';
import { resetTypeMap } from 'common/components/FormContainer/index';


const BasicChart = defineAsyncComponent(() => import('common/components/BasicChart'));

const tableRef = ref();

const store = useCreativeAixTaskStore();

const filterModelData = ref();
const dateType = ref();
const formInfo = ref(useGenFormData(
  computed(() => getAxiTaskFilterConfig({
    taskType: filterModelData.value || store.taskType,
    ruleList: store.ruleList,
    text: store.text,
    dateRange: store.dateRange,
    dateType: dateType.value || store.dateType,
    channel: store.channel,
    formatType: store.formatType,
    status: store.status,
    directories: store.directories,
    taskBatchList: store.taskBatchList,
  })),
  (data: Record<string, any>) => submit(data),
  () => reset(),
  undefined,
  {
    resetType: resetTypeMap.propsHandler,
    resetHandler(params) {
      return Object.assign(params, {
        dateInfo: {
          dateType: DEFAULT_TASK_LIST_CONDITION.dateType,
          dateRange: DEFAULT_TASK_LIST_CONDITION.dateRange,
        },
        text: [],
        taskType: DEFAULT_TASK_LIST_CONDITION.taskType,
        ruleId: '',
        channel: '',
        formatType: '',
        status: '',
        directories: [],
      });
    },
  },
));

watch(() => formInfo.value, (val) => {
  filterModelData.value = val.modelValue.taskType;
  dateType.value = val.modelValue.dateInfo.dateType;
}, {
  deep: true,
});


const { emitDownLoad } = useDownloadTask(store);

function submit(data: Record<string, any>) {
  const { channel, status, formatType, directories, text, taskType, ruleId, dateInfo } = toRaw(get(data));
  store.setCondition({
    pageIndex: DEFAULT_TASK_LIST_CONDITION.pageIndex,
    pageSize: DEFAULT_TASK_LIST_CONDITION.pageSize,
    channel,
    status,
    formatType,
    directories,
    text,
    taskType: get(taskType),
    dateType: dateInfo.dateType,
    dateRange: dateInfo.dateRange,
    ruleId,
  });
  store.getMediaTaskStat();
}

function reset() {
  store.setCondition(useUrlDefault(DEFAULT_TASK_LIST_CONDITION));
}

function onPageChange(current: number, pageInfo: any) {
  store.setPages(current, pageInfo.pageSize);
}

const { cols, displayCols } = useTaskTable(
  store.resumeUpload,
  store.cancelUpload,
  store.isDisabledRow,
  store.isOperateLoading,
);

let timer: any = null;

function repeatTimeout() {
  store?.updateTaskList?.();
  store?.getMediaTaskStat?.();
}


const visibility = useDocumentVisibility();
watch(visibility, (value) => {
  if (value === 'hidden') {
    clearTimeout(timer);
    timer = null;
  } else {
    repeatTimeout();
  }
});

tryOnBeforeUnmount(() => {
  reset();
  clearTimeout(timer);
  timer = null;
});
useWatchGameChange(async () => {
  await store.init();
});


</script>

<style lang="scss" scoped>
:deep(.t-input ) {
  @apply rounded-default;
}

.total-chart {
  position: relative;

  &::after {
    position: absolute;
    content: 'Total';
    left: 40%;
    top: 40%;
    font-size: 14px;
    color: #747D98;
  }
}
</style>
