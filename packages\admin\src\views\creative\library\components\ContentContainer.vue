<template>
  <div class="bg-white-primary pt-[16px] list-container">
    <Justify class="px-[16px] mb-[16px] text-sm font-[500]">
      <template #left>
        <p>{{ currentFullPath }}</p>
      </template>
      <template #right>
        <p
          v-if="currentDictionaryItem?.ext"
          class="text-black-placeholder text-xs flex flex-row items-center"
        >
          <SvgIcon
            class="mr-[8px]"
            name="cycle-refresh"
            size="16"
            color="var(--aix-text-color-black-disabled)"
          />
          Last synced at {{ currentDictionaryItem.ext.sync_time || currentDictionaryItem.ext.update_date }}
        </p>
      </template>
    </Justify>
    <DataContainer
      class="h-[calc(100%-42px)]"
      :data="list"
      :loading="loading"
      :arthub-code="store.dictionary.arthubCode"
      :storage-type="store.dictionary.type"
      :public-token="store.dictionary.publicToken"
      :total="store.material.total"
      :page-size="store.material.pageSize"
      :default-page="store.material.pageNum"
      @on-page-change="onPageChange"
    >
      <template #attributeSlot>
        <t-space
          align="center"
          size="8px"
        >
          <div class="flex items-center space-x-[4px]">
            <div v-if="containerView !=='table'" class="flex items-center">
              <t-checkbox
                class="-mr-[8px]"
                :checked="checkAll"
                :indeterminate="indeterminate"
                @change="handleSelectAll"
              />
              <span class="ml-[2px] mr-[6px]">Select All</span>
            </div>
            <hover-select
              :checked-asset-list="simpleCheckedAssets"
              @clear="clearAll"
            />
          </div>
          <t-button
            v-if="type === 'aix'"
            theme="primary"
            :disabled="checkValue.length === 0"
            @click="showMediaSync"
          >
            Media Sync
          </t-button>
          <t-dropdown
            v-if="type === 'aix'"
            :options="actionsList"
            trigger="click"
            :max-column-width="200"
            :min-column-width="146"
            @click="labelEmit"
          >
            <t-button theme="default">
              Actions
              <template #suffix>
                <t-icon
                  name="chevron-down"
                  size="16"
                />
              </template>
            </t-button>
          </t-dropdown>
          <t-dropdown
            v-if="!isDropbox"
            :options="labelManageList"
            trigger="click"
            :max-column-width="200"
            :min-column-width="146"
            @click="labelEmit"
          >
            <t-button theme="default">
              Manage Labels
              <template #suffix>
                <t-icon
                  name="chevron-down"
                  size="16"
                />
              </template>
            </t-button>
          </t-dropdown>
          <SvgBtn
            v-if="type === 'aix' && currentDictionaryItem.id !== '0' && !isDropbox"
            type="button"
            name="refresh"
            :label="store.dictionary.refreshLoading ? 'Updating files...' : 'Refresh'"
            :svg-class="{ 'animate-spin': store.dictionary.refreshLoading }"
            :disabled="store.dictionary.refreshLoading"
            size="14px"
            @click="refresh"
          />
          <!-- {{ currentDictionaryItem }} -->
        </t-space>
      </template>
      <template #actionSlot>
        <SvgBtnList :list="buttons" />
      </template>
      <div
        v-if="list.length"
        class="w-full h-full"
      >
        <Table
          v-if="containerView === 'table'"
          ref="tableRef"
          v-model:display-columns="displayCols"
          :selected-row-keys="checkValue"
          max-height="1028px"
          row-key="id"
          :data="list"
          :columns="cols"
          @select-change="(val) => setSelected(val, false)"
        />
        <Container
          v-else
          :model-value="checkValue"
          :default-value="checkValue"
          :mode="$attrs.mode"
          :list="list"
          :can-edit="canEdit"
          :need-detail-drawer="false"
          :need-preview="false"
          @update:model-value="setSelected"
          @on-rename="rename"
          @on-preview-detail="showPreview"
          @on-view-detail="showDetail"
        />
      </div>
      <data-empty v-else class="w-full h-full" />
    </DataContainer>
  </div>
</template>

<script setup lang="ts">
import DataContainer from 'common/components/Layout/DataContainer.vue';
import { SvgBtn, SvgBtnList, SvgIcon } from 'common/components/SvgIcon';
import Justify from 'common/components/Layout/Justify.vue';
import Table from 'common/components/table';
import { Container, IMaterialItem } from 'common/components/creative/MaterialItem/';
import { computed, PropType, ref, useAttrs, watch } from 'vue';
import { useSeparateCheckAll } from 'common/compose/form/check-all';
import { get, tryOnBeforeUnmount } from '@vueuse/core';
import { useTable } from '@/views/creative/library/compose/library-table';
import { TSimpleAsset } from '@/views/creative/library/define';
import { useCreativeDialog } from '@/views/creative/library/compose/dialog';
import { useDetailDialog } from '@/views/creative/library/compose/detail-dailog';
import { syncArthubNode as syncArthubNodeService } from 'common/service/creative/library/sync-dictionary';
import { useTips } from 'common/compose/tips';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useTimeout } from 'common/compose/useInterval';
import { SyncStatus } from '@/store/creative/library/const';
import DataEmpty from '@/components/nullable/DataEmpty.vue';
import HoverSelect from '@/views/creative/library/components/dialog/hover-select.vue';

const { callTimeout, stopTimeout } = useTimeout();
const props = defineProps({
  list: {
    type: Array as PropType<Array<IMaterialItem>>,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: () => false,
  },
  store: {
    type: Object,
    default: () => {},
  },
  type: {
    type: String as PropType<'aix'|'media'>,
    default: () => 'aix',
  },
});

const isDropbox = computed<boolean>(() => props.store.dictionary.type === 'dropbox');


const containerView = ref<'list' | 'table'>('list');
const gameStore = useGlobalGameStore();

const canEdit = computed(() => !isDropbox.value);

const changeView = () => {
  containerView.value = get(containerView) === 'list' ? 'table' : 'list';
};
const isTableView = computed(() => get(containerView) === 'table');
const currentDictionaryItem = computed(() => props?.store?.dictionary?.activeFolderItem || {});
const currentFullPath = computed(() => {
  const activeItem = currentDictionaryItem.value;
  const { ext } = activeItem;
  if (ext) {
    // eslint-disable-next-line max-len
    return ext.parent_name === activeItem.text ? `${ext.full_path_name?.split(',').join?.(' > ') || ext.full_path_name}` : `${ext.full_path_name?.split(',').join?.(' > ') || ext.full_path_name} > ${activeItem.text}`;
  }
  return activeItem.text || gameStore.gameCode;
});

const {
  selectValue: checkValue,
  checkAll,
  indeterminate,
  setSelected,
  setEmpty,
  clearAll,
} = useSeparateCheckAll<IMaterialItem>(
  // computed(() => {
  //   // 当前的的全部列表值为，当前页的list + 现在已经选中的list,然后再，按照id去重
  //   if (get(checkValue).length) {
  //     return uniqBy(props.list?.concat(props
  //       .store
  //       .material
  //       .currentAllMaterialList
  //       .filter((i: IMaterialItem) => get(checkValue).includes(i.id))), 'id') || [];
  //   }
  //   return props.list || [];
  // }),
  computed(() => props.list || []), // 当前页面的数据
  i => i.id,
);

const handleSelectAll = (checked: boolean) => {
  const currentPageListId = props.list?.map(i => i.id);
  if (currentPageListId.length === 0) return;

  if (checked) {
    setSelected(currentPageListId, true);
  } else {
    setEmpty();
  }
};

const simpleCheckedAssets = computed<TSimpleAsset[]>(() => get(props
  .store
  .material
  .currentAllMaterialList)
  ?.filter((i: any) => checkValue?.value
    ?.includes?.(i.id))
  .map((i: any) => ({
    AssetID: i.id,
    AssetName: i.title,
    originName: i.ext.name,
    type: i.type,
    materialExt: i.ext.material_ext,
  })));

const labelEmit = (data: any) => data.method();


const tableRef = ref();
const {
  actionsList,
  labelManageList,
  buttons,
  rename,
  showMediaSync,
} = useCreativeDialog({
  checkValue,
  simpleCheckedAssets,
  changeView,
  isTableView,
  tableRef,
  containerType: props.type,
  store: props.store,
});
const attr = useAttrs();
const { showDetail, showPreview } = useDetailDialog(attr.mode, props.store);
const { cols, displayCols } = useTable({
  hideRename: location.search.includes('mc_demo'),
  methods: {
    rename,
    showDetail,
    showPreview,
  },
  type: props.type,
});


const refresh = async () => {
  // console.log('store', props.store);
  try {
    await syncArthubNodeService({
      node_id: props.store.dictionary.activeFolderId,
      node_type: 1,
    });
    // 同步到syncNodeList
    props.store.dictionary.setSyncNode([
      {
        nodeId: props.store.dictionary.activeFolderId,
        syncStatus: SyncStatus.SYNCING,
      },
    ]);
    const { success } = useTips({ title: 'Updating directory' });
    success(`Updating directory【${currentFullPath.value}】Update process might take you a few minutes, please wait patiently`);
  } catch (e: any) {
    props.store.dictionary.setSyncNode([
      {
        nodeId: props.store.dictionary.activeFolderId,
        syncStatus: SyncStatus.SYNCED,
      },
    ]);
    console.log('err', e);
  }
};

watch(() => props.store.dictionary.syncNodeList, (val) => {
  callTimeout(() => {
    // 只有在存在没有同步完成的节点的时候，才取拉接口
    if (val.filter((item: any) => item.syncStatus === SyncStatus.SYNCING).length > 0) {
      props.store.dictionary.refreshData(props.store.material.update);
    }
  }, 10000);
}, { deep: true });


tryOnBeforeUnmount(() => {
  stopTimeout();
});

const onPageChange = (currenPage: number, pageInfo: any) => {
  props.store.material.setPageInfo(currenPage, pageInfo.pageSize);
};

// 当游戏发生变化，修改选中状态
watch(() => gameStore.gameCode, () => clearAll());


defineExpose({
  setSelected,
  checkValue,
});
</script>

<style scoped>
.list-container {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.03);
  @apply rounded-large bg-white-primary;
  height: calc(100% - 24px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
