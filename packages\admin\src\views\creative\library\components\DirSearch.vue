<template>
  <t-select-input
    v-model:input-value="inputValue"
    :value="selectValue"
    :popup-visible="popupVisible"
    :popup-props="{ overlayInnerStyle: { width: 'auto', minWidth: '300px', maxWidth: '800px', padding: 0 } }"
    :max-width="20"
    placeholder="Enter to Search Folder"
    class="dir-search"
    clearable
    allow-input
    @popup-visible-change="onPopupVisibleChange"
    @clear="onClear"
    @input-change="onInputChange"
  >
    <template #prefixIcon>
      <SearchIcon />
    </template>
    <template #panel>
      <div v-if="!dirOptions.length || loading" class="text-gray-primary text-center p-[12px]">
        <span v-if="!dirOptions.length && !loading">No Data</span>
        <t-loading v-if="loading" size="small" />
      </div>
      <div v-else class="narrow-scrollbar max-h-[600px] overflow-y-auto">
        <ul class="p-[12px]">
          <li
            v-for="item in dirOptions"
            :key="item.id"
            class="cursor-pointer mb-[6px] px-[12px] py-[6px]"
            @click="() => onOptionClick(item)"
          >
            <div class="text-[14px]">{{ item.name }}</div>
            <div class="text-gray-primary text-[12px]" style="font-family: 'PingFang SC'">
              {{ item.full_path_name.replace(/,/g, ' > ') }}
            </div>
          </li>
        </ul>
      </div>
    </template>
  </t-select-input>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import { SelectInputProps } from 'tdesign-vue-next';
import { SearchIcon } from 'tdesign-icons-vue-next';
import useAixLibraryStore from '@/store/creative/library/aix-library.store';
import { SearchDirResItem } from 'common/service/creative/library/get-dictionary-list';

const store = useAixLibraryStore();

const dirOptions = ref<SearchDirResItem[]>([]);

const inputValue = ref<string>('');
const selectValue = ref<SearchDirResItem>();
const popupVisible = ref(false);
const onOptionClick = async (item: SearchDirResItem) => {
  selectValue.value = item;
  onClear();

  store.dictionary.initFromActiveId(item.id);
  await store.dictionary.initDictionary();
};
const onClear = () => {
  selectValue.value = undefined;
  popupVisible.value = false;  // 选中后立即关闭浮层
  inputValue.value = '';
  dirOptions.value = [];
};
const onPopupVisibleChange: SelectInputProps['onPopupVisibleChange'] = (val, context) => {
  console.log(context);
  popupVisible.value = val;
};

// 300ms防抖延迟
const loading = ref(false);
const searchWithDebounce = useDebounceFn(async (val: string) => {
  loading.value = true;
  dirOptions.value = [];
  const res = await store.dictionary.searchDictionary(val);
  loading.value = false;
  dirOptions.value = res;
}, 300);

const onInputChange: SelectInputProps['onInputChange'] = async (val) => {
  if (!val) {
    dirOptions.value = [];
    return;
  }
  searchWithDebounce(val);
};
</script>
<style lang="scss" scoped>
ul > li:hover {
  background-color: #F0F7FF;
}
</style>
