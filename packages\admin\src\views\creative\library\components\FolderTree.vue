<template>
  <t-loading size="small" :loading="loading" content="loading...">
    <t-tree
      ref="tree"
      v-model="allChecked"
      class="narrow-scrollbar"
      :data="items"
      :load="load"
      :check-strictly="true"
      value-mode="parentFirst"
      max-height="396"
      height="396"
    >
      <template #label="{ node }">
        <div class="flex items-center space-x-[8px] items">
          <t-loading
            v-if="node.data.loading"
            v-element-visibility="(state) => onElementVisibility(state, node)"
            size="small"
          />
          <FolderIcon v-if="node.data.children" />
          <Text
            :content="node.label"
            class="max-w-[460px] inline-block"
            overflow
            tool-tip
            @click="checked(node)"
          />
        </div>
      </template>
      <template v-if="!loading" #empty>
        <div class="w-full h-[400px] flex justify-center items-center">
          this Game didn't get folders, please check auth permission or concat your admin
        </div>
      </template>
      <template #operations="{ node }">
        <div class="opt">
          <t-button
            class="hidden"
            size="small"
            variant="base"
            :disabled="allChecked.includes(node.data.value)"
            @click="checked(node)"
          >
            Select
          </t-button>
        </div>
      </template>
    </t-tree>
  </t-loading>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { TreeInstanceFunctions, TreeProps } from 'tdesign-vue-next';
import { FolderIcon } from 'tdesign-icons-vue-next';
import { useDebounceFn, useVModel } from '@vueuse/core';
import Text from 'common/components/Text';
import { getFolders, initFolder, PAGE_LIMIT } from '@/views/creative/library/compose/folder/init-folder';
import { useWatchGameChange } from 'common/compose/request/game';
import { vElementVisibility } from '@vueuse/components';
import { useTips } from 'common/compose/tips';


// 如果是最后一个，并且，node的parent的所有节点
//

const props = defineProps<{
  modelValue: string[],
  pathName: Array<{ id: string, path: string[] }>,
}>();
const emit = defineEmits(['update:modelValue', 'update:pathName']);
const allChecked = useVModel(props, 'modelValue', emit);
const pathName = useVModel(props, 'pathName', emit);


const checked = (node: any) => {
  if (!allChecked.value.includes(node.data.value)) {
    allChecked.value.push(node.data.value);
  }
};

// 1 -> get root id
// 3 -> folderList

// 2 -> initFolderFromNode


const mapPath = (i: any) => ({ id: i.value, path: i.fullPathName, name: i.label });
// 初始化，获取第一级目录
const tree = ref<TreeInstanceFunctions>();
const items = ref<TreeProps['data']>();
const loading = ref(false);
const rootId = ref();

// 当此列数据没有拉取完整的时候，应该要增加一个loading，用于处理数据的拉取
const getLoadingItem = (offset = 0) => ({
  label: 'loading',
  value: offset,
  loading: true,
  checkable: false,
});

const { err } = useTips();

const foldersOnInit = async () => {
  items.value = [];
  loading.value = true;
  const res = await initFolder();
  if (!res) {
    return err('get folders err, please try again later!');
  }
  rootId.value = res.rootId;
  if (res) {
    // 说明此时还亚欧数据没有拉完
    if (res.total > PAGE_LIMIT) {
      console.log('init list');
      res.list?.push(getLoadingItem());
    }
    items.value = res.list;
    pathName.value = res.list?.map(mapPath) || [];
  }
  loading.value = false;
};

onMounted(async () => {
  await foldersOnInit();
});

useWatchGameChange(async () => {
  // allChecked.value = [];
  await foldersOnInit();
});

// 逐层加载
const load: TreeProps['load'] = async (node) => {
  if (node.value) {
    const { list, total } = await getFolders(String(node.value));
    // 说明此时还亚欧数据没有拉完
    if (total > PAGE_LIMIT) {
      list!.push(getLoadingItem());
    }
    pathName.value = pathName.value.concat(list!.map(mapPath));
    return list!;
  }
  return [];
};

// 增加一个请求锁, 如果已经在请求状态中，重复触发，不做处理
const locks = ref<Record<string, boolean>>({});

const onElementVisibility = useDebounceFn(async (state: boolean, node: any) => {
  if (!state) return; // 不可见的状态不做处理
  if (locks.value[node.data.value]) return;
  locks.value[node.data.value] = true;
  const parentId = node.getParent() ? String(node.getParent().data.value) : rootId.value;
  const offset = node.data.value - -PAGE_LIMIT;
  const newList = await getFolders(parentId, offset);
  newList.list?.map((it: any) => node.insertBefore(it));
  if (newList.total > offset + PAGE_LIMIT) {
    node.insertAfter(getLoadingItem(offset));
  }
  node.remove();
  locks.value[node.data.value] = false;
}, 100);

</script>

<style scoped lang="scss">
:deep(.t-tree__label) {
  padding: 4px;
}

:deep(.t-tree__icon:not(:empty)) {
  &:hover {
    background-color: unset;
    opacity: .8;
  }

  &:active {
    opacity: .5;
  }
}

:deep(.t-tree__item):hover {
  .hidden {
    display: block;
  }
}

</style>
