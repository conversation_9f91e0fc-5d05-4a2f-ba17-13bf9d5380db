<template>
  <div class="left-nav-container narrow-scrollbar relative">
    <div class="px-[16px] pt-[16px]">
      <dir-search />
    </div>
    <TreeMenu
      class="flex-[1] p-[16px] pt-[8px] narrow-scrollbar overflow-y-auto"
      v-bind="$attrs"
    />
    <div v-if="libraryType === LibraryType.Aix && !disableAction" class="border-t border-[#DCDFE8] flex items-center p-[12px]">
      <p
        class="text-brand normal-hover"
        @click="editorRef.show()"
      >
        <add-icon />
        Create Directory
      </p>
    </div>
    <BaseDialog
      ref="editorRef"
      title="Create Directory"
      :confirm-loading="store.dictionary.dicLoading"
      @confirm="createDictionary"
      @close="closeEdit"
    >
      <t-form
        ref="formRef"
        class="w-[500px]"
        :data="form"
        label-align="left"
      >
        <t-form-item
          v-if="store.dictionary.activeFolderId"
          label-width="150px"
          label="Create directory"
        >
          <t-radio-group v-model="form.level">
            <t-radio value="same">Same level class</t-radio>
            <t-radio value="child">Child class</t-radio>
          </t-radio-group>
        </t-form-item>
        <t-form-item
          label-width="150px"
          label="New folder name"
          name="newDicName"
          :rules="[
            {required: true}
          ]"
        >
          <t-input v-model="form.newDicName" type="text" />
        </t-form-item>
      </t-form>
    </BaseDialog>
  </div>
</template>

<script setup lang='ts'>
import TreeMenu from 'common/components/TreeMenu';
import { LibraryType } from '@/views/creative/library/define';
import { PropType, reactive, ref } from 'vue';
import { AddIcon } from 'tdesign-icons-vue-next';
import BaseDialog from 'common/components/Dialog/Base';
import useAixLibraryStore from '@/store/creative/library/aix-library.store';
import DirSearch from './DirSearch.vue';

type TLibrary = (typeof LibraryType)[keyof typeof LibraryType];

defineProps({
  libraryType: {
    type: String as PropType<TLibrary>,
    default: LibraryType.Aix,
  },
  disableAction: { // 禁用操作，素材弹框场景
    type: Boolean,
    default: false,
  },
});

const editorRef = ref();
const formRef = ref();
const store = useAixLibraryStore();

const form = reactive({
  level: 'child', // 同级子级
  newDicName: '',
});

const closeEdit = () => {
  formRef.value.reset();
};
const createDictionary = async () => {
  // 如果是同级就取自己的父亲，如果是子集，就取自己
  const result = await formRef.value.validate();
  if (result === true) {
    await store.dictionary.creatDictionary(form.newDicName.trim(), form.level);
    editorRef.value.hide();
  }
};
</script>

<style scoped>
.left-nav-container{
  overflow-y: auto;
  @apply rounded-large bg-white-primary;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.03);
  width: 100%;
}
</style>
