<template>
  <div class="inline-flex">
    <Label
      v-bind="filteredAttrs"
      v-model="labelListInnter"
    />
    <t-select
      v-model="searchTypeInnter"
      class="w-[70px] ml-[1px]"
    >
      <t-option value="1" label="Union">
        <div>
          <SvgIcon
            name="logic-or"
            size="40px"
            height="18px"
          />
        </div>
      </t-option>
      <t-option value="2" label="Intersection">
        <div>
          <SvgIcon
            name="logic-and"
            size="40px"
            height="18px"
          />
        </div>
      </t-option>
      <template #valueDisplay="{value}">
        <div>
          <SvgIcon
            :name="value === '1' ? 'logic-or' : 'logic-and'"
            size="40px"
            height="18px"
          />
        </div>
      </template>
    </t-select>
  </div>
</template>
<script lang="ts" setup>
import { useAttrs, computed, ref, watch } from 'vue';
import Label from '@/views/creative/dashboard/components/Label.vue';
import SvgIcon from 'common/components/SvgIcon';

const emit = defineEmits(['update:modelValue']);
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
    },
  },
});
const attrs = useAttrs();

const filteredAttrs = computed(() => ({
  ...attrs,
  style: undefined,
  class: undefined,
}));

const labelListInnter = ref<(string | number)[]>([]);

const searchTypeInnter = ref<string>('');

watch(() => props.modelValue, (val) => {
  if (val) {
    labelListInnter.value = val.labelList;
    searchTypeInnter.value = String(val.labelsSearchType) ?? '1';
  }
}, { immediate: true, deep: true });

watch(() => labelListInnter.value, (val) => {
  emit('update:modelValue', {
    labelsSearchType: +searchTypeInnter.value,
    labelList: val,
  });
}, { deep: true });

watch(() => searchTypeInnter.value, (val) => {
  emit('update:modelValue', {
    labelsSearchType: +val,
    labelList: labelListInnter.value,
  });
});

</script>
<style lang="scss" scoped>
:deep(.select-input::after) {
  width: 0;
}

:deep(.common-form__select-rounded) {
  @apply rounded-l-default rounded-r-none;
  border-width: 1px 0 1px 1px;
}

:deep(.t-input) {
  @apply rounded-r-default rounded-l-none;
  cursor: pointer;

  .t-input__prefix {
    margin-right: 0;
  }

  .t-input__suffix {
    margin-left: 0;
  }

  .t-input__inner {
    display: none;
  }
}
</style>
