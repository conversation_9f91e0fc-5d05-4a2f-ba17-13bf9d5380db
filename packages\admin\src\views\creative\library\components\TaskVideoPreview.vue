<template>
  <Preview
    ref="previewRef"
    v-bind="$attrs"
  >
    <template #trigger>
      <div @click="show">
        <slot />
      </div>
    </template>
  </Preview>
</template>
<script lang="ts" setup>
import Preview from 'common/components/Dialog/Preview';
import { ref } from 'vue';
const previewRef = ref();

interface IPreviewProps {
  title: string;
  type: 'video' | 'image' | 'text';
  url?: string;
  poster?: string;
  getUrl?: () => Promise<string>;
  mode?: unknown, // 对话框类型，默认modeless
  hideTrigger?: boolean, // 隐藏默认的trigger
}


defineProps<IPreviewProps>();

function show() {
  previewRef.value.show();
}

</script>
