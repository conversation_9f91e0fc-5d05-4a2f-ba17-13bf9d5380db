<template>
  <div class="inline-flex items-center gap-x-[5px]">
    <t-tooltip v-if="props.statusInfo.showToolTip" v-bind="props.tooltip">
      <template #content>
        <slot name="content" :status-info="props.statusInfo" />
      </template>
      <div
        v-if="props.statusInfo.showToolTip"
        class="flex items-center gap-x-[5px] cursor-pointer"
      >
        <component :is="STATUS_ICON[props.statusInfo.statusKey]" />
        <span>{{ props.statusInfo.statusText }}</span>
      </div>
    </t-tooltip>

    <template v-else>
      <component :is="STATUS_ICON[props.statusInfo.statusKey]" />
      <span>{{ props.statusInfo.statusText }}</span>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { PropType } from 'vue';
import type { StringKeyAnyValueObject } from 'common/types/report';
import { TooltipProps } from 'tdesign-vue-next';
import { STATUS_ICON } from './statusIcon';

interface IStatus extends StringKeyAnyValueObject {
  statusKey: number,
  statusText: string,
  showToolTip?: boolean,
}

const props = defineProps({
  statusInfo: {
    type: Object as PropType<IStatus>,
    default: () => {},
  },
  tooltip: {
    type: Object as PropType<TooltipProps>,
    default: () => {},
  },
});

</script>
<style lang="scss" scoped>
</style>
