import {
  ErrorCircleFilledIcon,
  CheckCircleFilledIcon,
  LoadingIcon,
  CloseCircleFilledIcon,
} from 'tdesign-icons-vue-next';

export const STATUS_ICON: Record<number, JSX.Element> = {
  1: (<span class={'text-success-secondary'}><LoadingIcon /></span>),
  2: (<span class={'text-success-secondary'}><CheckCircleFilledIcon /></span>),
  3: (<span class={'ml-[4px] text-error-primary'}><ErrorCircleFilledIcon /></span>),
  4: (<span><CloseCircleFilledIcon /></span>),
  5: (<span class={'text-success-secondary'}><LoadingIcon /></span>),
};
