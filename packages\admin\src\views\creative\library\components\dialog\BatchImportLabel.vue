<template>
  <BaseDialog
    ref="dialogRef"
    title="Bulk Edit Label"
    width="30%"
    confirm-text="Confirm Import"
    :confirm-loading="submitLoading"
    :confirm-disabled="toUploadList.length === 0"
    @confirm="confirmToUpload"
  >
    <template #default>
      <t-space direction="vertical" size="10px">
        <t-space size="8px">
          <t-upload
            theme="file"
            accept=".xlsx"
            :size-limit="{size: 5, unit: 'MB', message: 'File size should not over 5MB'}"
            :before-upload="beforeUpload"
          >
            <t-button theme="primary">Choose File</t-button>
          </t-upload>
          <t-dropdown :options="options" min-column-width="175px" @click="onDownload">
            <t-space>
              <t-button theme="default">
                Download Template
                <template #suffix> <t-icon name="chevron-down" size="16" /></template>
              </t-button>
            </t-space>
          </t-dropdown>
        </t-space>
        <!-- 导入模板过大导致渲染有问题，暂时取消 -->
        <!-- <Table
          row-key="id"
          :loading="isLoading"
          class="w-[700px]"
          :columns="cols"
          :data="sortedList"
        /> -->
      </t-space>
    </template>
  </BaseDialog>
</template>

<script setup lang="ts">
import BaseDialog from 'common/components/Dialog/Base';
import { computed, ref } from 'vue';
import { useDownloadFile } from 'common/compose/download-file';
import { useTips } from 'common/compose/tips';
import { formatFileSize } from 'common/utils/format';
import { getFileExtension, parseFile } from 'common/utils/file';
import { useLoading } from 'common/compose/loading';
// import { Table } from 'tdesign-vue-next/es/table';
// import { useImportTable } from '@/views/creative/library/compose/import-lable-table';
import { importLabels } from 'common/service/creative/library/manage-labels';
import { TLabel, TKeyAttribute } from '@/views/creative/library/define';
// import { TLabel, TKeyAttribute, TLabelStatus } from '@/views/creative/library/define';
import { get } from '@vueuse/core';
const dialogRef = ref();
const { err, success } = useTips();


const props = defineProps({
  manualLabelList: {
    type: Array,
    default: () => [],
  },
});
const MAX_SIZE = 5 * 1024 * 1024; // 5MB

const keyAttribute = ref<TKeyAttribute>({
  key: 'name',
  title: 'Asset Name',
});
const firstLabels = ref<string[]>([]);
const label = ref<TLabel[]>([]);
// const sortedList = computed(() => sortLabels(get(label)));
const toUploadList = computed(() => get(label).filter(n => ['pending'].includes(n.status)));

// 排序权重，数字越大越靠前显示
// const qualityMap: Record<TLabelStatus, number> = {
//   failed: 5,
//   uploading: 4,
//   parse_failed: 3,
//   success: 2,
//   pending: 1,
// };
// function calcQuality(item: TLabel) {
//   return qualityMap[item.status] || 0;
// }
// function sortLabels(labels: TLabel[]) {
//   return [...labels].sort((a, b) => {
//     const qualityDelta = calcQuality(b) - calcQuality(a);
//     if (qualityDelta !== 0) {
//       return qualityDelta;
//     }
//     return (a.id as number) - (b.id as number);
//   });
// }

// function removeLabels(id: string) {
//   label.value = get(label).filter(i => i.id !== id);
// }

/**
 * donwload file template start
 */
const options = [
  { value: 'default', content: '通用模板' },
  { value: 'youtube-link', content: 'YouTube Link模板' },
];
function onDownload(item: {value: string, content: string}) {
  const type = item.value;
  const configs: any = {
    default: {
      fields: {
        素材名称: 'UA31276%',
      },
      filename: '素材标签导入模板',
    },
    'youtube-link': {
      fields: {
        'Assets Link': 'https://www.youtube.com/shorts/SnPGFplm9sU',
        素材编号: 'UA31276',
      },
      filename: 'YouTube Link标签导入模板',
    },
  } as const;
  const config = configs[type] || configs.default;

  const emptyFields = Object.fromEntries(Object.keys(config.fields).map(key => [key, '']));
  const optionalRow: any = { ...emptyFields, 标签名: '' }; // 描述字段必填/可选
  const optionsRow: any = { ...emptyFields, 标签名: '可选项' }; // 枚举可选项
  const demoRow = { ...config.fields, 标签名: '设计标签' }; // 示例数据
  props.manualLabelList.forEach((item: any) => {
    const { name, multiple, options, required } = item;
    const requireText = required ? '必填' : '可选';
    optionalRow[name] = multiple
      ? `${requireText}-多选`
      : `${requireText}-单选`;
    optionsRow[name] = options.join('、');
    const defaultOptions = multiple ? options : [options[0]];
    demoRow[name] = defaultOptions.slice(0, 2).join('、');
  });
  const data = [optionalRow, optionsRow, demoRow];
  useDownloadFile([{
    sheet: 'Sheet1',
    list: data,
  }], `${config.filename}.xlsx`, {
    mode: 'group',
  });
}
/**
 * donwload file template end
 */

// 导入文件和解析文件的loading
const {  showLoading, hideLoading } = useLoading();
// const { isLoading, showLoading, hideLoading } = useLoading();
// 提交导入内容到后台服务的loading
const {
  isLoading: submitLoading,
  showLoading: showSubmitLoading,
  hideLoading: hideSubmitLoading,
} = useLoading();

// const { cols } = useImportTable(keyAttribute, submitLoading, firstLabels, removeLabels);

const beforeUpload = (file: any) => {
  if (getFileExtension(file) !== 'xlsx') {
    err('File format error, please upload files in xlsx format');
    return false;
  }
  if (file.size > MAX_SIZE) {
    err(`File too large, please upload files smaller than ${formatFileSize(MAX_SIZE)}`);
    return false;
  }


  showLoading();
  parseFile(file.raw)
    .then((res: any) => {
      firstLabels.value = res.firstLabels;
      label.value = res.labels;
      keyAttribute.value = res.keyAttribute;
    })
    .catch((e) => {
      err(e.message);
    })
    .finally(() => {
      hideLoading();
    });
  return false; // 不通过upload组件上传文件
};

const confirmToUpload = async () => {
  showSubmitLoading();

  importLabels(get(toUploadList)).then((res) => {
    if ('data' in res) {
      const newLabels = get(label).map((item) => {
        const { id } = item;
        const result = res.data?.[id];
        if (result) {
          return { ...item, ...result };
        }
        return item;
      });
      label.value = newLabels;
    }
    success('Import Success!');
  })
    .finally(() => {
      hideSubmitLoading();
    });
};

defineExpose({
  show: () => get(dialogRef).show(),
});
</script>

<style scoped></style>
