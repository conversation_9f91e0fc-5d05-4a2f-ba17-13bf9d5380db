<template>
  <BaseDialog
    ref="dialogRef"
    title="Bulk Rename"
    :confirm-text="t('apply')"
    :confirm-disabled="selectedRowKeys.length === 0 || isTableLoaing || assetList.length === 0"
    :confirm-loading="isLoading"
    @confirm="updateName"
    @close="onClose"
  >
    <div class="w-[800px] space-y-[16px]">
      <div class="flex items-center space-x-[8px]">
        <SearchBox v-model="serachList" />
        <t-button
          size="medium"
          :disabled="isTableLoaing"
          @click="onSearch"
        >
          Search
        </t-button>
        <t-button
          size="medium"
          :theme="showReplaceFileName? 'primary' : 'default'"
          @click="onShowPlaceTool"
        >
          Replace Tool
        </t-button>
        <!--        <t-button-->
        <!--          size="medium"-->
        <!--          class="ml-[10px]"-->
        <!--          theme="default"-->
        <!--          @click="importFromCsv"-->
        <!--        >-->
        <!--          Import From CSV-->
        <!--        </t-button>-->
      </div>
      <p>
        <span class="font-[500]">Synced To Media: </span>
        <t-switch v-model="syncToMedia" class="ml-[8px]" />
      </p>
      <div
        v-if="showReplaceFileName"
        class="bg-white-primary p-[16px] rounded-small"
      >
        <t-form
          ref="replaceRef"
          layout="inline"
          :data="replaceData"
          :rules="FORM_RULES"
          :colon="true"
          @submit="onReplace"
        >
          <t-form-item
            label="Origin"
            name="origin"
          >
            <t-input
              v-model="replaceData.origin"
              placeholder="Please enter"
            />
          </t-form-item>
          <t-form-item
            label="Change to"
            name="replace"
          >
            <t-input
              v-model="replaceData.replace"
              placeholder="Please enter"
            />
          </t-form-item>
          <t-button
            type="submit"
            :disabled="isTableLoaing || replaceData.origin.length === 0"
          >
            Apply
          </t-button>
        </t-form>
      </div>
      <Table
        :key="count"
        ref="tableRef"
        :loading="isTableLoaing"
        row-key="id"
        :columns="cols"
        :data="assetList"
        :selected-row-keys="selectedRowKeys"
        @select-change="rehandleSelectChange"
      />
      <Pagination
        class="pagination"
        size="small"
        :disabled="isTableLoaing"
        :total="pageInfo.pageTotal"
        :current="pageInfo.pageIndex"
        :page-size="pageInfo.pageSize"
        @change="pageInfoChange"
      />
    </div>
  </BaseDialog>
</template>

<script setup lang="ts">
import BaseDialog from 'common/components/Dialog/Base';
import { Table, Pagination } from 'tdesign-vue-next';
import type { PageInfo as IPageInfo } from 'tdesign-vue-next';
import { reactive, ref } from 'vue';
import { useRenameTable } from '@/views/creative/library/compose/rename-table';
import SearchBox from 'common/components/SearchBox/index';
import { Status, TNameItem, TSimpleAsset } from '@/views/creative/library/define';
import { useTips } from 'common/compose/tips';
import { get, useCounter } from '@vueuse/core';
import { useI18n } from 'common/compose/i18n';
import { I18N_BASE } from 'common/const/i18n';
import { useLoading } from 'common/compose/loading';
import { editpalette } from 'common/service/creative/library/manage-assets';
import { getMaterialList } from 'common/service/creative/library/get-material-list';
import { getSeraChInfo } from '@/views/creative/library/utils';

const dialogRef = ref();
const tableRef = ref();
const { count, inc } = useCounter();

const { t } = useI18n(I18N_BASE);

const props = defineProps({
  store: {
    type: Object,
    default: () => {},
  },
});

// 初始化时候的数据
const originAssetList = ref<TSimpleAsset[]>([]);

const assetList = ref<TNameItem[]>([]);

const requestCount = ref<number>(0);

const pageInfo = reactive({
  pageIndex: 1,
  pageSize: 10,
  pageTotal: 0,
});

const { isLoading: isTableLoaing,
  showLoading: showTableLoading,
  hideLoading: hideTableLoading } = useLoading();

const { warn, info } = useTips();

const FORM_RULES = {
  origin: [{ required: true, message: 'Origin can\'t be empty ' }],
};
const showReplaceFileName = ref(false);
const replaceRef = ref();
const replaceData = reactive({
  origin: '',
  replace: '',
});
const onReplace = ({ validateResult, firstError }: any) => {
  if (validateResult === true) {
    //  进行命名替换
    const list = get(assetList).filter(i => i.originName.includes(replaceData.origin));
    if (!list.length) {
      info(`not found any item with ${replaceData.origin}`);
    } else {
      assetList.value = list.map(item => ({
        ...item,
        newName: item.originName.includes(replaceData.origin)
          ? item.originName.replaceAll(replaceData.origin, replaceData.replace)
          : item.newName,
      }));
    }
    replaceRef.value.reset();
  } else {
    warn(firstError);
  }
};

// const importFromCsv = () => {};

const serachList = ref([
  {
    text: 'Asset Name',
    field: 'asset_name',
    condition: [],
    searchType: 1,
  },
]);

const { cols } = useRenameTable({
  remove: (index: number) => {
    assetList.value.splice(index, 1);
    inc();
  },
  updateItem(index: number, newData: TNameItem) {
    assetList.value.splice(index, 1, newData);
    inc(); // 强制刷新一下table
  },
});

const selectedRowKeys = ref<string[]>([]);

// 分页时调用
function setAssetList(data: TSimpleAsset[]) {
  const sliceEnd = pageInfo.pageIndex * pageInfo.pageSize;
  // 加个分页的逻辑
  const pagingList = data.slice(
    (pageInfo.pageIndex - 1) * pageInfo.pageSize,
    sliceEnd > originAssetList.value.length ? originAssetList.value.length : sliceEnd,
  );

  const list = requestCount.value === 0 ? pagingList : originAssetList.value;
  assetList.value = list.map((item: TSimpleAsset, index: number) => ({
    id: item.AssetID,
    index,
    originName: item.originName!,
    newName: '',
    status: Status.UnChanged,
  }));
  selectedRowKeys.value = assetList.value.map(i => i.id);
}

function initFileList(list: TSimpleAsset[]) {
  originAssetList.value = list;
  if (requestCount.value === 0) {
    pageInfo.pageTotal = list.length;
  }
  setAssetList(list);
}

const rehandleSelectChange = (value: (string| number)[]) => {
  selectedRowKeys.value = value.map?.(String);
};

/**
 * 检查当前表格的输入框是否符合预期
 * @returns true 表示符合  false 表示 存在错误
 */
function onValidateTableData() {
  // 执行结束后触发事件 validate
  return tableRef.value.validateTableData().then((params: any) => {
    console.log('Promise Table Data Validate:', params);
    const cellKeys = Object.keys(params.result);
    if (cellKeys.length === 0) return true;
    const firstError = params.result[cellKeys[0]];
    if (firstError) {
      warn(firstError[0].message);
    }
    return false;
  });
}

// 标识是否需要同步修改到渠道，这个玩意，比较挫，只能一个一个改
// 所以要批量修改后，根据判断，一个一个的修改。
const syncToMedia = ref(false);
const { isLoading, showLoading, hideLoading } = useLoading();
async function updateName() {
  const checkRes = await onValidateTableData();
  // 如果
  if (!checkRes) {
    return;
  }
  showLoading();
  // 修改状态
  assetList.value = get(assetList).map(i => ({
    ...i,
    status: i.newName ? Status.Changing : i.status,
  }));
  const list = get(assetList)
    .filter(asset => get(selectedRowKeys).includes(asset.id) && asset.newName)
    .map(asset => ({
      asset_id: asset.id,
      name: asset.newName,
    }));
  const res = await editpalette(list);
  hideLoading();
  assetList.value = get(assetList).map((it) => {
    const changeRes = res.find((i: any) => i.asset_id === it.id);
    if (changeRes) {
      // 根据返回的结果做数据状态的修正，如果返回成功，并且不同同步到媒体，展示成功
      // 否则展示为aix侧成功
      props.store.material.update();
      return {
        ...it,
        status: changeRes.set_res === 0 ? Status.Failed : syncToMedia.value ? Status.AixSuccess : Status.Success,
      };
    }
    return it;
  });
  if (syncToMedia.value) debugger;
  // TODO 根据res的返回值，更新修改结果
}

// 从接口拉
async function  getRemoteMaterialList()  {
  const { text, names, searchType } = getSeraChInfo(serachList.value);
  showTableLoading();
  const { list = [], total = 0 } = await getMaterialList({
    count: pageInfo.pageSize,
    offset: pageInfo.pageSize * (pageInfo.pageIndex - 1),
    page: pageInfo.pageIndex - 1,
    text,
    directory_id: props.store.material.params.directory_id,
    list_type: props.store.material.params.list_type,
    with_detail: props.store.material.params.with_detail,
    filter_online_status: 0,
    online_status: null,
    search_type: searchType,
    labels: [],
    labels_search_type: 1,
    names,
  });
  hideTableLoading();
  requestCount.value += 1;
  pageInfo.pageTotal = total;
  initFileList(list.map((item: any, index: number) => ({
    id: item.ext.asset_id,
    index,
    originName: item.ext.name,
    status: Status.UnChanged,
  })));
}

// 点搜索的时候 拉接口数据
function onSearch() {
  pageInfo.pageIndex = 1;
  pageInfo.pageSize = 10;
  getRemoteMaterialList();
}

function onShowPlaceTool() {
  showReplaceFileName.value = !showReplaceFileName.value;
}

function pageInfoChange(newPageInfo: IPageInfo) {
  pageInfo.pageIndex = newPageInfo.pageSize === pageInfo.pageSize ? newPageInfo.current : 1;
  pageInfo.pageSize = newPageInfo.pageSize;
  if (requestCount.value === 0) {
    setAssetList(originAssetList.value);
  } else {
    getRemoteMaterialList();
  }
}

function onClose() {
  pageInfo.pageIndex = 1;
  pageInfo.pageSize = 10;
  pageInfo.pageTotal = 0;
  requestCount.value = 0;
}
defineExpose({
  show: (params?: TSimpleAsset[]) => {
    if (!params) return;
    initFileList(params);
    dialogRef.value.show();
  },
});
</script>

<style scoped>
:deep(.pagination .t-input__inner) {
  width: 60px !important;
}
</style>
