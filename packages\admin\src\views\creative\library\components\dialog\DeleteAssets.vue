<template>
  <BaseDialog
    ref="dialogRef"
    title="Delete Creative"
    theme="info"
    :confirm-disabled="checkedValue.length === 0"
    :confirm-loading="isLoading"
    @confirm="deleteAsset"
  >
    <p>Are you sure to delete these assets</p>
    <t-checkbox-group
      v-model="checkedValue"
      class="flex flex-col mt-[16px] min-w-[300px] max-w-[1000px]"
    >
      <t-checkbox :check-all="true" label="All" />
      <t-checkbox
        v-for="i in deleteIds"
        :key="i.AssetID"
        :value="i.AssetID"
      >
        <t-tooltip :content="i.AssetName" placement="mouse">
          <span class="truncate">
            {{ i.AssetName }}
          </span>
        </t-tooltip>
      </t-checkbox>
    </t-checkbox-group>
  </BaseDialog>
</template>

<script setup lang="ts">
import BaseDialog from 'common/components/Dialog/Base';
import { ref } from 'vue';
import { deleteAssets } from 'common/service/creative/library/manage-assets';
import { useLoading } from 'common/compose/loading';
import { get } from '@vueuse/core';
import { useTips } from 'common/compose/tips';

const props = defineProps({
  store: {
    type: Object,
    default: () => {},
  },
  deleteCallback: {
    type: Function,
    default: () => {},
  },
});

const deleteIds = ref();
const checkedValue = ref<string[]>([]);

const { isLoading, showLoading, hideLoading } = useLoading();
const { success } = useTips();
const deleteAsset = () => {
  if (checkedValue.value.length === 0) return;
  showLoading();
  deleteAssets(
    checkedValue.value,
    props.store.dictionary.type,
    props.store.dictionary.arthubCode,
    props.store.dictionary.publicToken,
  ).then((res) => {
    if (checkedValue.value.length === res.data.length) {
      success('Delete success!');
      props.deleteCallback?.();
      hideLoading();
      props.store.material.update();
      get(dialogRef).hide();
    }
  })
    .catch(() => {
      hideLoading();
    });
};

const dialogRef = ref();
defineExpose({
  show: (params?: any) => {
    deleteIds.value = params;
    checkedValue.value = params?.map?.((i: any) => i.AssetID);
    get(dialogRef).show();
  },
});
</script>

<style scoped></style>
