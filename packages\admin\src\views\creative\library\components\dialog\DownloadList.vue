<template>
  <BaseDialog
    ref="dialogRef"
    title="Download Creative"
    :confirm-disabled="downloadList.length === 0"
    :confirm-loading="isDownloadLoading"
    @confirm="onConfirm"
    @close="onClose"
  >
    <div class="scroller max-h-[600px] container-layout my-[10px] flex flex-col">
      <div class="head fileItem flex justify-between border-[1px] border-gray-primary py-[15px] px-[30px]">
        <div class="left font-bold">Asset Name</div>
        <div class="right font-bold ml-[150px]">Action</div>
      </div>
      <div class="flex-1 h-full  overflow-y-auto">
        <div
          v-for="(item) in renderDownloadList"
          :key="item.AssetID"
          class="head fileItem flex justify-between border-[1px] border-t-0 border-gray-primary py-[15px] px-[30px]"
        >
          <div class="left flex items-center bolder-font gap-x-2">
            <div class="mr-[15px]">{{ item.AssetName }}</div>
            <DownloadIcon v-if="item.status === DownLoadStatus.UnStart" />
            <LoadingIcon
              v-else-if="item.status === DownLoadStatus.StartDownloading"
              name="loading"
            />
            <CheckCircleFilledIcon
              v-else-if="item.status === DownLoadStatus.Success"
              class="text-success-secondary"
            />
            <ErrorCircleFilledIcon
              v-else-if="item.status === DownLoadStatus.Failed"
              class="text-error-secondary"
            />
          </div>
          <div
            class="right bolder-font ml-[150px] text-link cursor-pointer normal-hover"
            @click="handleDeleteFileItem(item)"
          >
            Remove
          </div>
        </div>
      </div>
    </div>
    <div class="h-[20px]" />
  </BaseDialog>
</template>

<script setup lang="ts">
import BaseDialog from 'common/components/Dialog/Base/Index.vue';
import { CheckCircleFilledIcon, DownloadIcon, LoadingIcon, ErrorCircleFilledIcon } from 'tdesign-icons-vue-next';
import { computed, ref, watch } from 'vue';
import { getDownloadUrl } from 'common/service/creative/library/manage-assets';
import { useDownloadFile, DownloadFileTaskQueue } from 'common/compose/download-file';
import { useGlobalGameStore } from '@/store/global/game.store';
import { toFileCdnUrl } from 'common/utils/url';
// import { usePagination } from 'common/compose/pagination';
import { TSimpleAsset } from '@/views/creative/library/define';
import { useLoading }  from 'common/compose/loading';
import { isUndefined } from 'lodash-es';

enum DownLoadStatus {
  UnStart, // 未下载
  StartDownloading, // 下载中
  Failed, // 下载失败;
  Success, // 下载成功
}

type TFile = TSimpleAsset & {
  status: DownLoadStatus,
};
type TFileUrlListParam = {
  items: Array<Object>,
  __game: string,
};
type TSuccessItem = {
  asset_id: string,
  download_url: string,
  signed_url: string
};
type TFailItem = {
  asset_id: string,
};


const props = defineProps({
  store: {
    type: Object,
    default: () => {},
  },
});
const gameStore = useGlobalGameStore();
const downloadList = ref<TFile[]>([]);
// 已经取消的队列
const cancelledAssetIdList = ref<string[]>([]);
const renderDownloadList = computed(() => downloadList.value.filter(item => !cancelledAssetIdList.value.includes(item.AssetID)));

const storageType = computed(() => props.store.dictionary.type);
console.log(storageType, getDownloadUrl, useDownloadFile);
// 从接口获取下载的连接地址
// getDownloadUrl()
// 下载内容
// 多条下载内容，需要分多次调用 useDownloadFile
// useDownloadFile(url, fileName, { isUrl: true });
const dialogRef = ref();
defineExpose({
  show: async (params: TSimpleAsset[]) => {
    downloadList.value = params.map(item => ({
      ...item,
      status: DownLoadStatus.UnStart,
    }));
    dialogRef.value.show();
  },
});


const { isLoading: isDownloadLoading, showLoading: showDownloadLoading, hideLoading: hideDownloadLoading } = useLoading();
const downloadFileTaskQueueInstance = ref<DownloadFileTaskQueue>();

// const onConfirm = async () => {
//   const { data, nextPage, isLast } = usePagination(downloadList.value, { pageSize: 1, startPage: 1 });
//   walkDownloadList();
//   function walkDownloadList() {
//     const params = convertRequestParams(data.value);
//     getDownloadUrl(storageType.value, gameStore.gameCode, params).then((res) => {
//       const { succeed_items: succeedItems = [], failed_items: failedItems = [] } = res || {};
//       executeDownloadList(succeedItems);
//       executeFailList(failedItems);
//     })
//       .catch(() => {
//         executeFailList(data.value.map((item) => {
//           const temp: TFailItem = { asset_id: item.AssetID };
//           return temp;
//         }));
//       })
//       .finally(() => {
//         if (!isLast.value) {
//           nextPage();
//           walkDownloadList();
//         }
//       });
//   }
// };

const onClose = () => {
  if (downloadFileTaskQueueInstance.value) {
    downloadFileTaskQueueInstance.value.clear();
  }
  hideDownloadLoading();
  cancelledAssetIdList.value = [];
};

const onConfirm = async () => {
  showDownloadLoading();
  // 如果只有一个文件，直接开始下载
  const intervalTime = renderDownloadList.value.length < 2 ? 0 : 5000;
  downloadFileTaskQueueInstance.value  = new DownloadFileTaskQueue([], 1, intervalTime);
  // 先把状态改成loaing中
  downloadList.value = downloadList.value.map(item => ({ ...item, status: DownLoadStatus.StartDownloading }));
  function walkDownloadList(data: TFile) {
    // 防止下载的过程中， 被remove掉
    if (cancelledAssetIdList.value.includes(data.AssetID)) return;
    // 取最后一条
    const lastTask = renderDownloadList.value[renderDownloadList.value.length - 1];
    // 如果没有最后一条， 或者最后一条的id 等于当前id, 就说明任务执行完了
    const isTaskDone = isUndefined(lastTask) || lastTask.AssetID === data.AssetID;
    const params = convertRequestParams([data]);
    getDownloadUrl(storageType.value, gameStore.gameCode, params).then((res) => {
      const { succeed_items: succeedItems = [], failed_items: failedItems = [] } = res || {};
      executeDownloadList(succeedItems);
      executeFailList(failedItems);
    })
      .catch(() => {
        executeFailList([data].map((item) => {
          const temp: TFailItem = { asset_id: item.AssetID };
          return temp;
        }));
      })
      .finally(() => {
        isTaskDone && hideDownloadLoading();
      });
  }
  downloadList.value.forEach((item) => {
    if (!cancelledAssetIdList.value.includes(item.AssetID)) {
      downloadFileTaskQueueInstance.value!.add(() => walkDownloadList(item));
    }
  });
  downloadFileTaskQueueInstance.value.execute();
};

const handleDeleteFileItem = (item: TFile) => {
  cancelledAssetIdList.value.push(item.AssetID);
};
const convertRequestParams = (list: Array<TFile>): TFileUrlListParam => {
  const params: {items: Array<Object>, __game: string } = { items: [], __game: ''  };
  list.forEach((item) => {
    params.items.push({
      asset_id: item.AssetID,
      download_name: item.originName,
      content_disposition: 'attachment',
    });
    params.__game = gameStore.gameCode;
  });
  return params;
};
const executeDownloadList = (arr: Array<TSuccessItem>) => {
  arr.forEach((item, index) => {
    setTimeout(() => {
      const position = findIndexByAssetId(item.asset_id);
      const url = item.download_url || `https:${item.signed_url}`;
      downloadList.value[position].status = DownLoadStatus.StartDownloading;
      const { isLoading } = useDownloadFile(
        toFileCdnUrl(url),
        downloadList.value[position].AssetName,
        { isUrl: true },
      );
      watch(() => isLoading.value, (val) => {
        !val && (downloadList.value[position].status = DownLoadStatus.Success);
      });
    }, 500 * index); // 延迟一下
  });
};
const executeFailList = (arr: Array<TFailItem>) => {
  arr.forEach((item: {asset_id: string}) => {
    const position = findIndexByAssetId(item.asset_id);
    downloadList.value[position].status = DownLoadStatus.Failed;
  });
};
const findIndexByAssetId = (id: string) => downloadList.value.findIndex(item => item.AssetID === id);
</script>

<style scoped></style>
