<template>
  <BaseDialog
    ref="dialogRef"
    title="Edit Creative Cover"
    :confirm-loading="isLoading"
    :confirm-disabled="!tempFile"
    @confirm="startUpload"
    @close="onClose"
  >
    <template #title>
      <div>
        Edit Creative Cover
        <hover-select :checked-asset-list="assetsList" />
      </div>
    </template>
    <t-upload
      v-show="!isExistsFile"
      ref="uploadRef"
      theme="custom"
      accept="image/*"
      draggable
      :size-limit="{size: 5, unit: 'MB', message: 'Please upload the file, within 5M in size'}"
      :auto-upload="false"
      :before-upload="beforeUpload"
    >
      <template #dragContent="params">
        <div>
          <p v-if="params && params.dragActive">release the mouse</p>
          <p v-else>Click to upload/drag to this area</p>
        </div>
      </template>
    </t-upload>
    <div
      v-if="isExistsFile"
      class="t-upload__dragger flex w-[460px] p-[10px] h-[122px]"
    >
      <t-image-viewer
        :images="[tempFile.url]"
        :z-index="5000"
      >
        <template #trigger="{ open }">
          <t-image
            :src="tempFile.url"
            class="w-[133px] h-[100px] cursor-pointer"
            fit="scale-down"
            title="Click for larger image"
            @click="open"
          />
        </template>
      </t-image-viewer>
      <div
        class="flex flex-1  flex-col justify-between ml-[10px] text-xs"
      >
        <div class="text-black-primary">{{ tempFile.name }}</div>
        <div>
          <div class="text-black-disabled">File size: {{ formatFileSize(tempFile.size) }}</div>
          <div class="text-black-disabled">Upload date: {{ tempFile.uploadTime }}</div>
        </div>
        <div>
          <a href="javascript:;" class="text-brand" @click="reUpload">Re-upload</a>
          <a
            href="javascript:;"
            class="text-brand ml-[8px]"
            @click="deleteFile"
          >
            Delete
          </a>
        </div>
      </div>
    </div>
    <p class="text-xs text-black-placeholder mt-[8px]">
      Please upload the file, within 5M in size
    </p>
  </BaseDialog>
</template>

<script setup lang="ts">
import BaseDialog from 'common/components/Dialog/Base';
import { ref, PropType, watch, computed } from 'vue';
import { v4 as uuidv4 } from 'uuid';
import type { UploadFile } from 'tdesign-vue-next';
import { upload } from 'common/components/FileUpload/util';
import  type { UploadReq } from 'common/components/FileUpload/type';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useTips } from 'common/compose/tips';
import { useLoading } from 'common/compose/loading';
import { editpalette } from 'common/service/creative/library/manage-assets';
import { formatFileSize, previewType } from 'common/utils/format';
import dayjs from 'dayjs';
import { getFileExtension } from 'common/utils/file';
import HoverSelect from '@/views/creative/library/components/dialog/hover-select.vue';
import { TSimpleAsset } from '@/views/creative/library/define';
const MAX_SIZE = 5 * 1024 * 1024; // 5MB

const dialogRef = ref();
const uploadRef = ref();
const { warn } = useTips();
const { isLoading, showLoading, hideLoading } = useLoading();
defineExpose({
  show: () => dialogRef.value.show(),
  hide: () => dialogRef.value.hide(),
});

// interface IVideo {
//   AssetID: string,
//   AssetName: string,
//   originName: string,
//   type: string,
//   materialExt: any,
// }

const props = defineProps({
  assetsList: {
    type: Array as  PropType<TSimpleAsset[]>,
    default: () => [],
  },
  store: {
    type: Object,
    default: () => {},
  },
});

const tempFile = ref<UploadFile>({});
const { err } = useTips();

const isExistsFile = computed(() => Object.keys(tempFile.value).length > 0 && tempFile.value.url);

const startUpload = async () => {
  const list = props.assetsList.map(item => Object.assign({}, {
    asset_id: item.AssetID,
    label: item.materialExt.label,
    cover: tempFile.value?.url,
  }));
  await editpalette(list);
  // 重新拉数据 关闭弹窗
  props.store.material.update();
  await useTips().success('Update cover success');
  dialogRef.value.hide();
};

watch(() => props.assetsList, ((val) => {
  if (val.length === 1) {
    tempFile.value = {
      name: val[0].AssetName,
      url: val[0].materialExt?.universal?.cover,
      size: val[0].materialExt?.universal?.size,
      uploadTime: val[0].materialExt?.universal?.update_time,
    };
  }
  if (val.length > 1) {
    tempFile.value = {};
  }
}), { immediate: true });

async function beforeUpload(file: UploadFile) {
  if (previewType(getFileExtension(file as File)!) !== 'image') {
    err('File format error, please upload files in image format');
    return false;
  }
  if (file.size! > MAX_SIZE) {
    err(`File too large, please upload files smaller than ${formatFileSize(MAX_SIZE)}`);
    return false;
  }
  showLoading();
  deleteFile(); // 删掉上一个
  const { url, message }: UploadReq = await upload({
    file: Object.assign(file.raw as File, { id: uuidv4() }),
    type: 'thumbnail',
    game_code: useGlobalGameStore().gameCode,
  });
  if (url) {
    tempFile.value = file;
    tempFile.value.url = url;
    tempFile.value.uploadTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
  } else {
    warn(message as string);
  }
  hideLoading();
  return false;
}

// 删除
function deleteFile() {
  if (!isLoading.value) {
    tempFile.value = {};
  }
}

// 重新上传
function reUpload() {
  if (!isLoading.value) {
    uploadRef.value.triggerUpload();
  }
}

function onClose() {
  tempFile.value = {};
}
</script>

<style lang="scss" scoped>
:deep(.t-upload__dragger) {
  width: 460px;
  height: 122px;
}
</style>
