<template>
  <BaseDialog
    ref="dialogRef"
    :confirm-loading="isLoading"
    mode="modeless"
    :draggable="true"
    @confirm="submit"
    @close="clearSelectList"
  >
    <template #title>
      <span class="text-xl">Edit Label</span>
      <hover-select :checked-asset-list="checkedAssetList" />
    </template>
    <div class="w-[600px]">
      <!-- <t-tabs
        :value="tab"
        :list="tabList"
        @change="setTab"
      /> -->
      <t-form
        v-loading="dataLoading"
        class="max-h-[450px] mt-[18px]"
        :class="dataLoading ? 'overflow-y-hidden' : 'overflow-y-scroll'"
      >
        <t-form-item
          v-for="(it, idx) in tabList"
          :key="idx"
          :label="it.label + ':'"
          label-width="150px"
        >
          <SelectInput
            v-model="selectedLabel[it.value]"
            v-model:options="itemOption[it.value]"
            :placeholder="tagPlaceholder[it.value]"
            :multiple="LabelConfig[it.value].multiple"
            :mutex="LabelConfig[it.value]?.mutex"
            :creatable="creatable"
          />
        </t-form-item>
      </t-form>
      <t-alert
        v-if="errTips"
        class="mt-[18px]"
        theme="error"
      >
        {{ errTips }}
      </t-alert>
    </div>
  </BaseDialog>
</template>

<script setup lang="ts">
import BaseDialog from 'common/components/Dialog/Base';
import { computed, PropType, reactive, ref, toRaw, watch } from 'vue';
import { isEmpty, union } from 'lodash-es';
import { useLoading } from 'common/compose/loading';
import { SelectInput } from 'common/components/Select';
// import { useGlobalGameStore } from '@/store/global/game.store';
import { get } from '@vueuse/core';
import { editLabels } from 'common/service/creative/library/manage-labels';
import { TSimpleAsset } from '@/views/creative/library/define';
import { useTips } from 'common/compose/tips';
import HoverSelect from '@/views/creative/library/components/dialog/hover-select.vue';
import { useAuthStageStore } from '@/store/global/auth.store';
import { getCreativeDetail } from 'common/service/creative/library/get-material-detail';

type TDialogType = 'aix' | 'content';
const LABEL_KEY = {
  content: '设计',
  aix: 'AiX',
};

const props = defineProps({
  type: {
    type: String as PropType<TDialogType>,
    default: () => 'aix',
  },
  store: {
    type: Object,
    default: () => {},
  },
});
const dialogRef = ref();
const tab = ref();
const selectedLabel = reactive<Record<string, any>>({});
const itemOption = reactive<Record<string, any>>({});
const tagPlaceholder = reactive<Record<string, any>>({});
const LabelConfig = reactive<Record<string, any>>({});
const innerType = ref<TDialogType>(props.type);
const innerTypeKey = computed<string>(() => LABEL_KEY[get(innerType)]);
const checkedAssetList = ref<TSimpleAsset[]>([]);
// const gameStore = useGlobalGameStore();
const options = ref<{ label: string; value?: string; checkAll?: boolean }[]>([]);

const { isLoading: dataLoading, showLoading: showDataLoading, hideLoading: hideDataLoading } = useLoading();
const defaultLabels = reactive(props.store?.label?.manualLabel);
const firstLabels = computed(() => (get(innerType) === 'aix' ? props?.store?.label?.robotLabel : props.store?.label?.manualLabel) || []);
const lastLabels = ref<{ [str: string]: any[] } >({});

const currentLabelConfig = computed(() => get(firstLabels).find((i: any) => i.name === get(tab)));
function setTab(item: string) {
  tab.value = item;
  options.value = get(currentLabelConfig)
    ?.options
    ?.map?.((i: string) => ({ label: i, value: i }));
}

function initSelectedLabel(labels: any[], ll: any) {
  labels.forEach((label) => {
    if (label.multiple) {
      if (ll[label.name]?.length > 1 && ll[label.name].includes(label.default[0])) {
        selectedLabel[label.name] = ll[label.name].slice(0, ll[label.name].indexOf(label.default[0]));
      } else {
        selectedLabel[label.name] = ll[label.name] || label.default || [];
      }
    } else {
      selectedLabel[label.name] = (ll[label.name]?.length > 1 ? label.default : ll[label.name] || label.default) || [];
    }

    LabelConfig[label.name] = label;

    itemOption[label.name] = label.options.map((item: object) => ({ label: item, value: item }));

    const result: string[] = [];
    if (label.required) {
      result.push('Required');
    }
    result.push(label.multiple ? 'Multiple' : 'Single');
    if (label.mutex) {
      result.push('Null and other options are mutually exclusive');
    }
    tagPlaceholder[label.name] = result.join('，');
  });
}

watch([firstLabels, lastLabels], ([val, ll]) => {
  if (val.length) {
    setTab(val[0].name);
    initSelectedLabel(val, ll);
  }
});

const tabList = computed(() => get(firstLabels)?.map?.((i: any) => ({
  label: i?.required ? `*${i?.name}` : i?.name,
  value: i?.name,
})) || []);

// pubgm ， dialogType 为 content 是，需要允许用户将输入的内容，加入到options中。
// 在提交的时候，把这个options写回后台去 这里有一个权限字段用来控制 isManualEditing
// 同时配置里有一个optionsOnly的字段，用来控制
const authStore = useAuthStageStore();
const isManualEditing = computed(() => authStore.hasPrivilege('manual_editing'));
const creatable = computed(() => !currentLabelConfig.value.optionsOnly && !isManualEditing.value);


// const tagPlaceholder = computed(() => {
//   const result: string[] = [];
//   const { required, multiple, mutex  } = get(currentLabelConfig);
//   if (required) {
//     result.push('必填');
//   }
//   result.push(multiple ? '可多选' : '单选');
//   if (mutex) {
//     result.push('无和其他选项互斥');
//   }
//   return result.join('，');
// });

const requiredLabel = computed(() => get(firstLabels)
  .filter((i: any) => i.required)
  .map((i: any) => i.name));

const errTips = ref('');

const clearSelectList = () => {
  tab.value = get(firstLabels)[0].name;
  options.value = [];
  // 遍历 selectedLabel 的每个值，然后置为 []
  Object.keys(selectedLabel).forEach((key) => {
    selectedLabel[key] = [];
  });
  errTips.value = '';
};

const validateSelectedLabel = () => {
  // 需要判断 二级标签没有默认值的填写
  const noDefaultTab: string[] = get(requiredLabel).filter((label: string) => isEmpty(selectedLabel[label]));
  if (get(innerType) !== 'aix' && noDefaultTab.length !== 0) {
    errTips.value = `${noDefaultTab.join(',')} need add second level label`;
  }
  return get(innerType) === 'aix' || noDefaultTab.length === 0;
};

const { err, success } = useTips();
const { isLoading, showLoading, hideLoading } = useLoading();
const submit = async () => {
  showLoading();
  // 检查数据格式
  if (!validateSelectedLabel()) {
    hideLoading();
    return;
  }
  errTips.value = '';
  // 提交标签修改数据
  const data = (await editLabels(get(checkedAssetList).map(asset => ({
    id: asset.AssetID,
    assetId: asset.AssetID,
    labelName: get(innerTypeKey),
    labels: toRaw(selectedLabel),
  })))) as any;
  const errorAssetList = Object.keys(data).filter(assetId => data[assetId].status === 'failed');
  const errorAssetNameList = errorAssetList
    .map((assetId) => {
      const asset = get(checkedAssetList).find(item => item.AssetID === assetId);
      return asset?.AssetName;
    })
    .filter(Boolean);

  if (errorAssetNameList.length === 0) {
    if (get(creatable)) {
      // TODO 更新pubgm options
    }
    success('label edit success!');
    get(dialogRef).hide();
  } else {
    err(`${errorAssetNameList.join(',')} edit label failed!`);
  }
  hideLoading();
};

defineExpose({
  show: async (type: TDialogType, simpleCheckedAssets: TSimpleAsset[]) => {
    innerType.value = type;
    checkedAssetList.value = simpleCheckedAssets;
    get(dialogRef).show();
    showDataLoading();

    let results: any[] = [];
    try {
      results = await Promise
        .all(simpleCheckedAssets
          .map((item: TSimpleAsset) => getCreativeDetail({ asset_id: item.AssetID })));
    } catch (err) {};

    const unionArray: Record<string, string[]> = {};

    if (results.length > 1) {
      results.forEach((item: Record<string, Record<string, Record<string, string[]>>>) => {
        if (item.newLabels['设计']) {
          for (const [key, value] of Object.entries(item.newLabels['设计'])) {
            unionArray[key] = unionArray[key] ? union(unionArray[key], value) : value;
          }
        } else {
          defaultLabels.forEach((item: Record<string, any>) => {
            unionArray[item.name] = unionArray[item.name] ? union(unionArray[item.name], item.default) : item.default;
          });
        }
      });
    }

    lastLabels.value = isEmpty(Object.keys(unionArray)) ? results[0]?.newLabels?.['设计'] || {} : unionArray;
    hideDataLoading();
  },
});
</script>

<style scoped></style>
