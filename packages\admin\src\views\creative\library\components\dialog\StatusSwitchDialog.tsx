import { DialogPlugin, Button } from 'tdesign-vue-next';
type StatusDialogParams = {
  bodyText: string;
  currStatus?: boolean,
  confirmFunc: Function,
  cancleFunc?: Function,
  editFunc?: Function,
  boldText: string[],
  header?: string,
};
export const useStatusSwitchDialog = (params: StatusDialogParams, timeOffset: number) => {
  const dialog = DialogPlugin.confirm({
    theme: 'info',
    header: params.header || 'Change Rule Status',
    body: () => {
      const result: JSX.Element[] = [];
      let lastIndex = 0;
      let index = 0;
      params.bodyText.replace(/\[bold\]/g, (match: string, offset: number) => {
        const subStr = params.bodyText.substring(lastIndex, offset);
        result.push(<span key={subStr}>{subStr}</span>);
        result.push(
          <span key={`bold-${offset}`} style={{ fontWeight: 'bold' }}>
            {params.boldText[index] || ''}
          </span>,
        );
        lastIndex = offset + match.length;
        index += 1;
        return match;
      });
      // 添加最后一段普通文本（如果有的话）
      if (lastIndex < params.bodyText.length) {
        result.push(
          <span key={`text-${lastIndex}`}>
            {params.bodyText.substring(lastIndex)}
          </span>,
        );
      }
      return result;
    },
    footer: () => (
      <span>
        <Button theme="default" onClick={() => dialog.hide()}>Cancel</Button>
        {!params.currStatus && <Button theme="primary" onClick={() => {
          params.editFunc?.(); dialog.hide();
        }}>Edit</Button>}
        { timeOffset <= 6 || params.currStatus ? <Button theme="primary" onClick={() => {
          params.confirmFunc(); dialog.hide();
        }}>Ok</Button> : null }
      </span>
    ),
  });
};
