<template>
  <BaseDialog
    ref="dialogRef"
    title="Total summary of current selected tasks' status"
    :is-show-footer="false"
  >
    <div class="w-[460px] text-center">
      <Table
        :columns="columns"
        :data="data"
      />
      <t-button
        class="mt-[20px]"
        @click="dialogRef.hide()"
      >
        OK
      </t-button>
    </div>
  </BaseDialog>
</template>
<script lang="ts" setup>
import BaseDialog from 'common/components/Dialog/Base';
import type { ITaskStat } from '@/store/creative/library/type';
import Table from 'common/components/table';
import { ref, PropType, computed } from 'vue';
import { UPLOAD_STATUS } from '@/views/creative/library/compose/const';

const props = defineProps({
  list: {
    type: Array as PropType<ITaskStat[]>,
    default: () => [],
  },
});

const data = computed(() => setTableData(props.list));

function setTableData(list: ITaskStat[])  {
  const result: any[] = [];
  Object.keys(UPLOAD_STATUS).forEach((key) => {
    if (!UPLOAD_STATUS[Number(key)].noSelect) {
      result.push({
        status: UPLOAD_STATUS[Number(Number(key))].text,
        count: list.find(item => String(item.status) === key)?.count || 0,
      });
    }
  });
  return result;
}

const columns = [
  {
    title: 'Status',
    colKey: 'status',
  },
  {
    title: 'Count',
    colKey: 'count',
  },
];

const dialogRef = ref();
defineExpose({
  show: () => dialogRef.value.show(),
  hide: () => dialogRef.value.hide(),
});
</script>
<style lang="scss" scoped>

</style>
