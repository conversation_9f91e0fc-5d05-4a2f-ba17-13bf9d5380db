<template>
  <BaseDialog
    ref="dialogRef"
    title="Upload files to AiX library"
    :confirm-loading="isLoading"
    :confirm-disabled="remainder === 0"
    @confirm="startUpload"
    @close="closeFileUpload"
  >
    <t-upload
      v-model="files"
      theme="file-flow"
      multiple
      :max="10"
      :auto-upload="false"
      :allow-upload-duplicate-file="true"
      :is-batch-upload="true"
      :upload-all-files-in-one-request="true"
      :disabled="isLoading"
    />
    <div
      v-if="files.length >0"
      class="w-[960px] mt-[16px]"
    >
      <t-table
        row-key="index"
        :data="files"
        :columns="columns"
      >
        <template #operation="{row, rowIndex}">
          <div>
            <t-link
              theme="primary"
              :disabled="isLoading || row.status === 'success' || row.status === 'progress'"
              @click="deleteFile(rowIndex)"
            >
              Delete
            </t-link>
          </div>
        </template>
        <template #size="{row}">
          <div>
            {{ formatFileSize(row.size) }}
          </div>
        </template>
        <template #status="{row}">
          <div>
            <span
              v-if="row.status === 'waiting'"
              class="text-black-placeholder mr-[5px]"
            >
              <IconFont
                name="time"
                size="14px"
              />
            </span>
            <UploadStstus :key="row.status" :status-info="statusMap[row.status as TStatus]" />
            <span v-if="row.status === 'progress'">
              {{ formatPercent(row.percent, 0) }}
            </span>
          </div>
        </template>
      </t-table>
    </div>
  </BaseDialog>
</template>

<script setup lang="ts">
import BaseDialog from 'common/components/Dialog/Base';
import { updateAsset, uploadAndUpdateAsset } from 'common/service/creative/library/manage-assets';
import { formatFileSize, formatPercent } from 'common/utils/format';
import { useLoading } from 'common/compose/loading';
import { IconFont } from 'tdesign-icons-vue-next';
import type { UploadFile } from 'tdesign-vue-next';
import { useTips } from 'common/compose/tips';
import { ref, computed } from 'vue';
import { omit } from 'lodash-es';
import UploadStstus from '@/views/creative/library/components/UploadStstus/Index.vue';
const { isLoading, showLoading, hideLoading } = useLoading();
const { warn } = useTips();
const props = defineProps({
  parentId: {
    type: String,
    default: () => '',
  },
  publicToken: {
    type: String,
    default: () => '',
  },
  arthubCode: {
    type: String,
    default: () => '',
  },
  storageType: {
    type: String,
    default: () => '',
  },
  update: {
    type: Function,
    default: () => {},
  },
});

const columns = [
  {
    colKey: 'name',
    title: 'FileName',
  },
  {
    colKey: 'size',
    title: 'Size',
  },
  {
    colKey: 'status',
    title: 'Status',
  },
  {
    colKey: 'operation',
    title: 'Operation',
  },
];

type TStatus = 'success' | 'fail' | 'progress' | 'waiting';

const statusMap = {
  progress: {
    statusKey: 1,
    statusText: 'Progress',
  },
  success: {
    statusKey: 2,
    statusText: 'Success',
  },
  fail: {
    statusKey: 3,
    statusText: 'Fail',
  },
  waiting: {
    statusKey: 0,
    statusText: 'Waiting',
  },
};

const uploadAction = props.storageType === 'google-drive' ? updateAsset : uploadAndUpdateAsset;

const dialogRef = ref();
defineExpose({
  show: () => dialogRef.value.show(),
  hide: () => dialogRef.value.hide(),
});

const files = ref<UploadFile[]>([]);

const fileMap = computed(() => new Map(files.value.map((item, index) => [index, item])));

// 有多少个可以上传的文件
const remainder = computed(() => getRemainder(files.value));

// 可上传的文件状态
const uploadableStatus = ['fail', 'waiting'];

function getRemainder(fileList: UploadFile[]) {
  return fileList.filter(item => uploadableStatus.includes(item.status as string)).length;
}

const startUpload = async () => {
  if (props.parentId.trim().length  === 0) {
    return warn('please choose your dictionary!');
  }
  showLoading();
  for (const [index, item] of fileMap.value) {
    if (uploadableStatus.includes(item.status as string)) {
      const fileItem = {
        ...item,
        file: item.raw,
      };
      const res: any = await uploadAction(
        omit(fileItem, 'raw'),
        {
          parent_id: props.parentId,
          public_token: props.publicToken,
          arthub_code: props.arthubCode,
          onUploadProgress: (percent: number) => onUploadProgress(percent, 'progress', index),
        },
      );
      console.log('res', res);
      const resStatus = res.status === 'upload_success' ? 'success' : 'fail';
      onUploadProgress(res?.cvm?.percent || 0, resStatus, index);
    }
  }
  hideLoading();
  // 重新拉取列表数据
  props.update();
};

function onUploadProgress(percent: number, status: TStatus, index: number) {
  if (files.value[index]) {
    files.value[index].percent = percent;
    files.value[index].status = status;
  }
}


function deleteFile(index: number) {
  files.value.splice(index, 1);
}

function closeFileUpload() {
  files.value = [];
  hideLoading();
}

</script>

<style lang="scss" scoped>
:deep(.t-upload__flow-bottom) {
  display: none;
}
:deep(.t-upload__flow-table) {
  display: none;
}
:deep(.t-upload__flow-card-area) {
  margin-top: 16px;
}
</style>
