<template>
  <BaseDialog
    ref="dialogRef"
    title="Assets Validate"
    theme="warning"
    :confirm-text="confirmText"
    :confirm-loading="isLoading"
    :confirm-disabled="confirmDisabled || selectRowKeys?.length === 0"
    @confirm="confirmHandle"
  >
    <div class="space-y-[8px]">
      <t-form :data="validateCondition">
        <t-form-item
          v-if="mediaList.length"
          label="Validate Media"
          name="media"
          label-width="150px"
        >
          <t-radio-group v-model="validateCondition.media">
            <t-radio
              v-for="item in mediaList"
              :key="item"
              :value="item"
            >
              {{ item }}
            </t-radio>
          </t-radio-group>
        </t-form-item>
        <t-form-item
          v-if="namingTypeList.length > 1"
          label="Validate Naming"
          name="naming"
          label-width="150px"
        >
          <t-radio-group v-model="validateCondition.naming">
            <t-radio
              v-for="namingItem in namingTypeList"
              :key="namingItem"
              :value="namingItem"
            >
              {{ namingItem }}
            </t-radio>
          </t-radio-group>
        </t-form-item>
      </t-form>
      <p v-if="metaNeedChange || namingNeedChange">
        Total {{ errItemLen }} creatives failed in verification, please fix it by
        <template v-if="metaNeedChange">
          <t-popup
            destroy-on-close
            show-arrow
            overlay-style
            :content="getMetaLimitTips(validateCondition.media as any) as any"
            placement="right"
            trigger="hover"
          >
            <span class="text-warning-primary cursor-pointer"> {{ validateCondition.media }} Require </span>
          </t-popup>
        </template>
        <template v-if="metaNeedChange && namingNeedChange">
          and
        </template>
        <template v-if="namingNeedChange">
          <t-popup
            destroy-on-close
            show-arrow
            overlay-style
            :content="getNamingLimitTips(validateCondition.naming as any) as any"
            placement="right"
            trigger="hover"
          >
            <span class="text-warning-primary cursor-pointer">Naming standard</span>
          </t-popup>
        </template>
      </p>
      <Table
        class="w-[700px]"
        row-key="AssetID"
        :columns="(tableCols as any)"
        :pagination="(pagination as any)"
        :data="list"
        :selected-row-keys="selectRowKeys"
        @select-change="selectChange"
      />
    </div>
  </BaseDialog>
</template>

<script setup lang="ts">
import BaseDialog from 'common/components/Dialog/Base';
import { computed, PropType, reactive, ref, watch } from 'vue';
import { useLoading } from 'common/compose/loading';
import { get } from '@vueuse/core';
import { NamingType, TValidateTableItem } from '@/views/creative/library/define';
import { Table } from 'tdesign-vue-next/es/table';
import { useValidateDialogTable } from '@/views/creative/library/compose/validate-dialog-table';
import {
  checkNamingValidInfo,
  getLimitTips as getNamingLimitTips,
} from '@/views/creative/library/components/dialog/media/namings';
import {
  checkMediaValidInfo,
  getLimitTips as getMetaLimitTips,
} from '@/views/creative/library/components/dialog/media/medias';
import { transformItem } from '@/views/creative/library/components/dialog/media/checkAssetInfo';
import { isEmpty } from 'lodash-es';
import { ArrayJoin } from 'common/utils/array';
import { MediaType } from '@/views/trade/ads_creation/common/template/type';

const props = defineProps({
  // 展示渠道选择,如果数组为空，则不展示
  mediaList: {
    type: Array as PropType<MediaType[]>,
    default: () => [],
  },
  namingTypeList: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  beforeClose: {
    type: Function as PropType<(list: any[]) => Promise<any>>,
    default: () => Promise.resolve(),
  },
  confirmText: {
    type: String,
    default: '',
  },
  confirmDisabled: {
    type: Boolean,
    default: () => false,
  },
  assetList: {
    type: Array,
    default: () => [],
  },
  // 如果沒有展示渠道，需要傳入指定的渠道
  media: {
    type: String as PropType<MediaType>,
    default: () => 'Google',
  },
  // 如果没有展示命名规范，需要传入需要校验的命名规范
  namingType: {
    type: String as PropType<NamingType>,
    default: () => 'gpp',
  },
});
const dialogRef = ref();
const assetsList = ref<any[]>(props.assetList);
const { isLoading, showLoading, hideLoading } = useLoading();
const validateCondition = reactive({
  media: props.media,
  naming: props.namingType,
});
const { cols: tableCols } = useValidateDialogTable();

const pagination = reactive({
  size: 'small',
  defaultCurrent: 1,
  defaultPageSize: 5,
  total: computed(() => get(assetsList).length),
});

const selectRowKeys = ref<string[]>([]);
const namingNeedChange = computed(() => list.value.some(i => i.hasNamingErr));
const metaNeedChange = computed(() => list.value.some(i => i.hasMetaErr || i.hasMetaWarn));
const errItemLen = computed(() => list.value.filter(i => i.hasNamingErr || i.hasMetaErr || i.hasMetaErr).length);
const list = computed<TValidateTableItem[]>(() => {
  if (!validateCondition.media) return [];
  return assetsList.value.map((item) => {
    const namingCheck = checkNamingValidInfo(validateCondition.naming!, transformItem(item));
    const metaCheck = checkMediaValidInfo(validateCondition.media!, transformItem(item));
    let namingCheckTips = '';
    let metaErrTips = '';
    let metaWarning = '';
    const hasNamingTips = !isEmpty(namingCheck.namingWarnings);
    const hasMetaTips = !isEmpty(metaCheck?.metaErrors);
    const hasMetaWarn = !isEmpty(metaCheck?.metaWarnings);
    if (hasNamingTips) {
      namingCheckTips = `Naming standard: ${ArrayJoin(namingCheck.namingWarnings, ',')}`;
    }
    if (hasMetaTips) {
      metaErrTips = `Creative Meta err: ${ArrayJoin(metaCheck?.metaErrors || [], ',')}`;
    }
    if (hasMetaWarn) {
      metaWarning = `Creative Meta warn: ${ArrayJoin(metaCheck?.metaWarnings || [], ',')}`;
    }
    const tips = [namingCheckTips, metaErrTips, metaWarning];
    return {
      AssetID: item.AssetID,
      AssetName: item.AssetName,
      hasNamingErr: hasNamingTips,
      hasMetaErr: hasMetaTips,
      hasMetaWarn,
      tips: isEmpty(tips.filter(Boolean)) ? 'Check passed' : tips,
    };
  });
});
watch(
  () => list.value,
  (val: TValidateTableItem[]) => {
    selectRowKeys.value = val.filter(i => !i.hasMetaErr).map(i => i.AssetID);
  },
);

const selectChange = (value: (string | number)[]) => {
  selectRowKeys.value = value.map(String);
};

const confirmHandle = () => {
  if (!props.beforeClose) get(dialogRef).hide();
  showLoading();
  props.beforeClose(get(assetsList)
    .filter(i => get(selectRowKeys).includes(i.AssetID))).then(() => {
    hideLoading();
    get(dialogRef).hide();
  });
};

defineExpose({
  show(list?: any[]) {
    if (list) {
      assetsList.value = list;
    }
    get(dialogRef).show();
  },
});
</script>

<style scoped>
:deep(.t-pagination .t-input__inner) {
  width: 60px !important;
}
</style>
