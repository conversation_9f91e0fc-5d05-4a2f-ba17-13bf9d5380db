<template>
  <BaseDialog
    ref="dialogInstance"
    width="890"
    class="cloud-drive-dialog"
    :close-btn="false"
    :is-show-footer="false"
    @close="onClosed"
  >
    <div class="h-[380px] bg-[#F3F4F4] flex flex-row">
      <div class="h-full w-[300px] bg-white p-[20px] space-y-[30px]">
        <h2 class="text-lg font-bold text-black">Cloud drive</h2>
        <t-steps :current="portalStore.step" layout="vertical">
          <t-step-item title="Select Cloud Drive" />
          <t-step-item title="Connect to Cloud Drive" />
          <t-step-item title="Select Target Folder" />
          <t-step-item title="AiX Processing" />
        </t-steps>
      </div>
      <div class="flex-1 h-full p-[20px]">
        <div class="w-full space-y-[10px] h-full">
          <div class="h-[16px] pt-[4px]">
            <svg-icon
              class="w-[12px] h-[12px] float-right cursor-pointer
              active:opacity-30 hover:opacity-50"
              name="close"
              @click="() => dialogInstance?.hide()"
            />
          </div>
          <keep-alive>
            <Suspense>
              <component
                :is="stepComponentMap[portalStore.step]"
                :key="portalStore.step"
                @cancel="dialogInstance?.hide()"
              />
              <template #fallback>
                <div class="w-full h-[300px] flex items-center justify-center">
                  <t-loading size="small" />
                </div>
              </template>
            </Suspense>
          </keep-alive>
        </div>
      </div>
    </div>
  </BaseDialog>
</template>
<script lang="ts" setup>
import { ref, ComponentPublicInstance, defineAsyncComponent } from 'vue';
import BaseDialog from 'common/components/Dialog/Base';
import { SvgIcon } from 'common/components/SvgIcon';
import { usePortalStore } from '@/store/creative/library/portal.store';


const dialogInstance = ref<ComponentPublicInstance<typeof BaseDialog>>();

const stepComponentMap = {
  0: defineAsyncComponent(() => import('./driverconfig/Step1.vue')),
  1: defineAsyncComponent(() => import('./driverconfig/Step2.vue')),
  2: defineAsyncComponent(() => import('./driverconfig/Step3.vue')),
  3: defineAsyncComponent(() => import('./driverconfig/Step4.vue')),
};

const portalStore = usePortalStore();

const onClosed = () => {
  portalStore.initStep();
};


defineExpose({
  show: () => {
    dialogInstance.value?.show();
  },
});

</script>
<style lang="scss">
.cloud-drive-dialog {
  .t-dialog--default {
    padding: 0;
    overflow: hidden;

    .t-dialog__body {
      padding: 0;
    }
  }
}
</style>
