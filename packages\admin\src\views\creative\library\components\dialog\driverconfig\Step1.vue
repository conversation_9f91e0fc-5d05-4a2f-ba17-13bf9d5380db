<template>
  <div class="w-full h-[calc(100%-60px)]">
    <div class="w-full flex flex-row justify-between items-center h-full">
      <div
        v-for="i in portalStore.supportDriver"
        :key="i"
        class="w-[180px] h-[130px] rounded-default bg-white flex flex-col border-solid border hover:border-brand
      border-2 cursor-pointer hover:opacity-50 active:opacity-30 justify-center items-center space-y-[8px]"
        :class="{'border-brand': i === portalStore.currentDriver}"
        @click="portalStore.currentDriver = i"
      >
        <img :src="useDriverImage(i)" :alt="useDriverName(i)" class="w-[60px] h-[60px]">
        <p>{{ useDriverName(i) }}</p>
      </div>
    </div>
    <div class="space-x-[8px] float-right">
      <t-button
        theme="default"
        @click="()=> emit('cancel')"
      >
        Cancel
      </t-button>
      <t-button
        theme="primary"
        @click="portalStore.nextStep"
      >
        Continue
      </t-button>
    </div>
  </div>
</template>
<script setup lang="ts">

import { usePortalStore } from '@/store/creative/library/portal.store';
import { useDriverImage, useDriverName } from '@/views/creative/library/compose/driver-const';

const emit = defineEmits(['cancel']);

const portalStore = usePortalStore();


</script>
<style scoped lang="scss">

</style>
