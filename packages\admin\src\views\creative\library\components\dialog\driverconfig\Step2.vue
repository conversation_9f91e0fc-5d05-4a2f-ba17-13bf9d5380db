<template>
  <div class="w-full h-[calc(100%-60px)]">
    <div class="w-full flex flex-col h-full space-y-[12px]">
      <div class="text-brand fill-brand flex flex-row items-center space-x-[8px]">
        <SvgIcon
          size="16"
          name="question"
        />
        <a
          :href="useDriverGuide(portalStore.currentDriver)"
          target="_blank"
        ><span class="text-base">See instructions</span></a>
      </div>
      <template v-if="portalStore.currentDriver === 'arthub'">
        <div class="space-y-[8px] rounded-default bg-white py-[14px] px-[16px]">
          <p>Arthub Token</p>
          <t-input
            v-model="portalStore.arthubToken"
            :title="portalStore.arthubToken"
            placeholder="Please enter the arthub token"
            class="w-[378px]"
          />
          <p>Authorized Folder Path</p>
          <t-input
            v-model="portalStore.arthubCode"
            :title="portalStore.arthubCode"
            placeholder="Please enter the folder path consistent with the token."
            class="w-[378px]"
          />
        </div>
      </template>
      <div
        v-else
        v-loading="isLoading"
        class="w-full h-[204px] rounded-default bg-white py-[14px] px-[16px] space-y-[18px]"
      >
        <div class="space-y-[8px]">
          <p>Sign Into To {{ driverName }} And Authorize</p>
          <t-button
            class="w-[200px]"
            theme="default"
            @click="signToDriver"
          >
            <template #icon>
              <img
                class="w-[20px] h-[20px]"
                :src="useDriverImage(portalStore.currentDriver)"
                :alt="driverName"
              >
            </template>
            Sign In With {{ driverName }}
          </t-button>
        </div>
        <div class="space-y-[8px]">
          <p>{{ driverName }} Accounts Name</p>
          <t-input
            disabled
            auto-width
            class="min-w-[200px]"
            placeholder="please sign in first"
            :status="accountsErr ? 'error' : 'default'"
            :tips="accountsErr"
            :value="accountName"
          />
        </div>
      </div>
    </div>
    <div class="space-x-[8px] float-right">
      <t-button
        theme="danger"
        @click="emit('cancel')"
      >
        Cancel
      </t-button>
      <t-button
        theme="default"
        @click="onPrevious"
      >
        Previous
      </t-button>
      <t-button
        theme="primary"
        :disabled="continueDisabled"
        @click="portalStore.nextStep"
      >
        Continue
      </t-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { usePortalStore } from '@/store/creative/library/portal.store';
import { SvgIcon } from 'common/components/SvgIcon';
import { useDriverImage, useDriverName, useDriverGuide } from '@/views/creative/library/compose/driver-const';
import { computed, ref } from 'vue';
import getDropboxAuthUrl from 'common/service/creative/auth/dropbox';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useLoading } from 'common/compose/loading';
import { Dropbox } from 'dropbox';

const portalStore = usePortalStore();

const driverName = computed(() => useDriverName(portalStore.currentDriver));
const emit = defineEmits(['cancel']);

const accountName = ref('');
const accountsErr = ref();

const onPrevious = () => {
  portalStore.initStep(0);
  accountName.value = '';
  accountsErr.value = '';
};

const popWindowRef = ref();
let checkTimer: any = null;
const checkPopWindowCloseCallback = (callback: () => void, timer = 500) => {
  clearTimeout(checkTimer);
  checkTimer = setTimeout(() => {
    if (popWindowRef.value && !popWindowRef.value.closed) {
      checkPopWindowCloseCallback(callback, timer);
    } else {
      callback?.();
    }
  }, timer);
};

const { isLoading, showLoading, hideLoading } = useLoading();

const updateAccountsInfo = () => {
  // if (isLoading.value) return;
  console.log('start get accounts info', portalStore.currentDriver);
  accountsErr.value = '';
  accountName.value = '';
  if (portalStore.currentDriver === 'dropbox') {
    portalStore
      .updateDropboxToken()
      .then(() => portalStore.getDropbox())
      .then((dbx: Dropbox) => dbx.usersGetCurrentAccount())
      .then((res) => {
        accountName.value = res?.result?.name?.display_name;
        return;
      })
      .catch((e) => {
        accountName.value = '';
        accountsErr.value = e.message;
        return;
      })
      .finally(() => hideLoading());
  } else if (portalStore.currentDriver === 'googledriver') {
    portalStore
      .updateGoogleToken()
      .then((res) => {
        accountName.value = res?.authUser;
        return;
      })
      .catch((e) => {
        accountName.value = '';
        accountsErr.value = e.message;
        return;
      })
      .finally(() => hideLoading());
  }
};

const signToDriver = async () => {
  showLoading();
  const { gameCode } = useGlobalGameStore();
  // TODO 根据不通的driver，拉取不通的授权url
  let authUrl: string;
  if (portalStore.currentDriver === 'dropbox') {
    authUrl = await getDropboxAuthUrl(gameCode);
  } else if (portalStore.currentDriver === 'googledriver') {
    authUrl = `https://api.aix.levelinfinite.com/api_v2/oauth/google?game=${gameCode}&scope=drive`;
  } else {
    return '';
  }
  const { width, height } = window.screen;
  const [windowWidth, windowHeight] = [600, 800];
  const left = (width - windowWidth) / 2;
  const top = (height - windowHeight) / 2;

  //  检查是否已经关闭，如果已经关闭，那么检查登录情况
  //  拉取账号信息
  //  监听返回页面，是否有postMessage告知登录情况
  window.addEventListener('message', (event) => {
    if (event.origin === 'https://static.aix.levelinfinite.com') {
      clearTimeout(checkTimer);
      updateAccountsInfo();
      if (popWindowRef.value && !popWindowRef.value.closed) {
        popWindowRef.value.close();
      }
    }
  });
  checkPopWindowCloseCallback(() => {
    updateAccountsInfo();
  });
  popWindowRef.value = window.open(
    authUrl,
    'OAuth Login',
    `width=${windowWidth},height=${windowHeight},left=${left},top=${top},toolbar=no,menubar=no,scrollbars=yes`,
  );
};

const ARTHUB_URL_REGEX = /^https:\/\/arthub\.(?:woa|qq)\.com\/([^/?#]+)\/pan(?:\?(?:[^&=]*&)*node=(\d+)(?:&|$))/i;
const continueDisabled = computed(() => (portalStore.currentDriver === 'arthub'
  ? !portalStore.arthubToken || !ARTHUB_URL_REGEX.test(portalStore.arthubCode)
  : !accountName.value),
);
</script>
<style scoped lang="scss"></style>
