<template>
  <div class="w-full h-[calc(100%-60px)]">
    <div class="w-full flex flex-col h-full space-y-[12px]">
      <div class="text-brand fill-brand flex flex-row items-center space-x-[8px]">
        <SvgIcon
          size="16"
          name="question"
        />
        <a :href="useDriverGuide(portalStore.currentDriver)" target="_blank"><span class="text-base">See instructions</span></a>
      </div>
      <div class="w-full h-[204px] rounded-default bg-white px-[16px] py-[8px] space-y-[8px]">
        <p class="flex flex-row items-center space-x-[8px]">
          <span>{{ useDriverName(portalStore.currentDriver) }} Folder Path</span>
          <t-tooltip
            content="Please select the root-level folder containing your final assets for syncing. Avoid selecting the entire cloud drive."
          >
            <SvgIcon
              class="hover:opacity-80 cursor-pointer"
              name="error"
            />
          </t-tooltip>
        </p>
        <t-tree-select
          v-model="directoryId"
          placeholder="Please select the root-level folder to be synced."
          :data="options"
          clearable
          filterable
          value-type="value"
          :min-collapsed-num="5"
          :tree-props="{
            valueMode: 'parentFirst', // 选中值模式 all表示父节点和子节点全部会出现在选中值里面
            load: loadWhenExpand,
          }"
        />
      </div>
    </div>
    <div class="space-x-[8px] float-right">
      <t-button
        theme="danger"
        @click="emit('cancel')"
      >
        Cancel
      </t-button>
      <t-button
        theme="default"
        @click="() => portalStore.initStep(1)"
      >
        Previous
      </t-button>
      <t-tooltip :content="directoryId ? `` : `Please select at lease one item`">
        <t-button
          theme="primary"
          :disabled="!directoryId || directoryId.length === 0"
          @click="portalStore.nextStep"
        >
          Continue
        </t-button>
      </t-tooltip>
    </div>
  </div>
</template>
<script setup lang="ts">
import { usePortalStore } from '@/store/creative/library/portal.store';
import { SvgIcon } from 'common/components/SvgIcon';
import { useDriverGuide, useDriverName } from '../../../compose/driver-const';
import { useTips } from 'common/compose/tips';
import { ref, watch } from 'vue';
import { Dropbox, files } from 'dropbox';
import { storeToRefs } from 'pinia';
import { drive_v3 } from 'googleapis';
import { getGoogleDriveDirectory, getArthubDirectory } from 'common/service/creative/library/get-dictionary-directly';
import { tryOnMounted } from '@vueuse/core';

interface TreeNode {
  label: string;
  value: string;
  type: string; // 文件夹类型 file|folder
  path: string; // Dropbox 路径（如 "/Documents"）
  parent: string; // 父层级id
  children?: boolean | TreeNode[];
}

type TEntries = (files.FileMetadataReference | files.FolderMetadataReference | files.DeletedMetadataReference)[];

const emit = defineEmits(['cancel', 'update:directoryId']);
const { err: errTips } = useTips();
const portalStore = usePortalStore();

const { directoryId } = storeToRefs(portalStore);

const options = ref<TreeNode[]>([]); // 当前tree options数据，当文件夹收缩时，只移除这里的子层级数据

// Dropbox递归加载所有分页数据
const loadDBXAllEntries = async (dbx: Dropbox, path: string): Promise<TEntries> => {
  let allEntries: TEntries = [];
  let cursor: string | null = null;

  do {
    const res: { result: { entries: TEntries; cursor: string; has_more: boolean } } = cursor
      ? await dbx.filesListFolderContinue({ cursor })
      : await dbx.filesListFolder({ path });

    allEntries = [...allEntries, ...res.result.entries];
    cursor = res.result.has_more ? res.result.cursor : null;
  } while (cursor);

  return allEntries;
};

const transformRes = (type: string, src: TEntries | drive_v3.Schema$File[], parent: string) => {
  switch (type) {
    case 'dropbox':
      return (src as TEntries).map(folder => ({
        label: folder.name,
        value: (folder as files.FileMetadata).id,
        type: folder['.tag'],
        path: folder.path_lower!,
        children: folder['.tag'] === 'folder',
        parent,
      }));
    case 'googledriver':
      return (src as drive_v3.Schema$File[]).map(folder => ({
        label: folder.name || '',
        value: folder.id || '',
        type: 'folder',
        path: folder.name || '',
        children: true,
        parent,
      }));
    case 'arthub':
      return (src as drive_v3.Schema$File[]).map(folder => ({
        label: folder.name || '',
        value: folder.id || '',
        type: 'folder',
        path: folder.name || '',
        children: true,
        parent,
      }));
    default:
      return [];
  }
};
tryOnMounted(async () => {
  await initData();
});
watch(
  () => portalStore.step,
  async (newStep) => {
    if (newStep === 2) {
      await initData();
    }
  },
  { deep: true },
);

const initData = async () => {
  portalStore.directoryId = '';
  options.value = [];
  if (portalStore.currentDriver === 'dropbox') {
    const dbx = await portalStore.getDropbox();
    /** -- /files/list_folder &&  /files/list_folder/continute
     * eg. https://www.dropbox.com/developers/documentation/http/documentation#files-list_folder-continue
     * desc: The empty string ("") represents the root folder.
     */
    try {
      const parent = '';
      const res = await loadDBXAllEntries(dbx, parent);
      options.value = transformRes('dropbox', res, parent);
    } catch (err) {
      console.error('get dropbox fileslistFolder', err);
      errTips('get dropbox filesListFolder err, please try again later!');
    }
  } else if (portalStore.currentDriver === 'googledriver') {
    try {
      const parent = 'root';
      const res = await getGoogleDriveDirectory(parent, portalStore.googleDriverToken?.authUser);
      options.value = transformRes('googledriver', res, parent);
    } catch (err) {
      console.error('get google driver folder', err);
      errTips('get google driver folder err, please try again later!');
    }
  } else if (portalStore.currentDriver === 'arthub') {
    try {
      const parent = '';
      const res = await getArthubDirectory(portalStore.arthubToken, portalStore.arthubCode, parent);
      options.value = transformRes('arthub', res, parent);
    } catch (err) {
      console.error('get arthub folder', err);
      errTips('get arthub folder err, please try again later!');
    }
  }
};

const updateTreeChildren = (
  nodes: TreeNode[],
  parent: { level: number; value: string },
  childList: TreeNode[],
  curLevel = 1,
): TreeNode[] => {
  // 层级超过时提前返回
  if (curLevel > parent.level || !Array.isArray(nodes)) return nodes;

  return nodes?.map((node) => {
    // 匹配条件：当前层级与value都符合
    if (curLevel === parent.level && node.value === parent.value) {
      return {
        ...node,
        children: childList,
      };
    }
    // 递归处理子节点
    if (node.children) {
      return {
        ...node,
        children: updateTreeChildren(
          node.children as TreeNode[],
          parent,
          childList,
          curLevel + 1, // 层级递增
        ),
      };
    }
    return node;
  });
};

// 加载子数据的方法，在展开节点时调用（仅当节点 children 为 true 时生效）
const loadWhenExpand = async (node: {
  label: string;
  value: string;
  type: string;
  path: string;
  level: number;
}): Promise<TreeNode[]> => {
  if (node.type === 'file') return [];

  const parent = node.value || ''; // 获取当前路径id
  if (portalStore.currentDriver === 'dropbox') {
    if (!portalStore.dropboxToken?.accessToken) return [];
    try {
      // 递归加载所有分页数据
      const dbx = await portalStore.getDropbox();
      const res = await loadDBXAllEntries(dbx, parent);
      const childList = transformRes('dropbox', res, parent);
      // 更新父节点 children，触发响应式
      options.value = updateTreeChildren(options.value, node, childList, 0);
      return childList;
    } catch (err) {
      console.error('get dropbox fileslistFolder', err);
      return [];
    }
  } else if (portalStore.currentDriver === 'googledriver') {
    if (!portalStore.googleDriverToken?.accessToken) return [];
    try {
      const res = await getGoogleDriveDirectory(parent, portalStore.googleDriverToken?.authUser);
      const childList = transformRes('googledriver', res, parent);
      options.value = updateTreeChildren(options.value, node, childList, 0);
      return childList;
    } catch (err) {
      console.error('get googledriver fileslistFolder', err);
      return [];
    }
  } else if (portalStore.currentDriver === 'arthub') {
    try {
      const res = await getArthubDirectory(portalStore.arthubToken, portalStore.arthubCode, parent);
      const childList = transformRes('arthub', res, parent);
      options.value = updateTreeChildren(options.value, node, childList, 0);
      return childList;
    } catch (err) {
      console.error('get arthub fileslistFolder', err);
      return [];
    }
  }
  return [];
};
</script>
<style scoped lang="scss"></style>
