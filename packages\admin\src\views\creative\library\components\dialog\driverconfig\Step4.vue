<template>
  <div class="w-full h-[calc(100%-60px)] space-y-[8px]">
    <div class="w-full flex flex-col space-y-[16px] h-full bg-white rounded-default py-[24px] px-[16px]">
      <p>
        <t-loading
          size="small"
          text="The initial Sync might take Sometime. Please check back later."
        />
      </p>
      <div class="flex flex-row space-x-[8px]">
        <div class="space-y-[8px]">
          <t-popconfirm
            content="Confirm to cancel notice?  You will not be notified when status changed, and need to actively refresh the page/go back to AiX."
            :visible="showCloseEmailConfirm"
            @confirm="confirmRemove"
            @cancel="showCloseEmailConfirm = false"
          >
            <t-checkbox
              :checked="showEmailInput"
              label="Notify People"
              @change="changEmailValue"
            />
          </t-popconfirm>
          <div
            v-if="showEmailInput"
            class="flex flex-row items-center space-x-[8px]"
          >
            <t-tagInput
              v-model="portalStore.notifyEmail"
              placeholder="Separate multiple emails with enter."
            />
          </div>
          <p class="text-gray-primary">
            Enter your email address to be notified upon completion or if an error occurs. You don't need to stay on
            this page.
          </p>
        </div>
      </div>
    </div>
    <div class="space-x-[8px] float-right">
      <t-tooltip :content="isAllEmails ? '' : `Please enter the correct email address`">
        <t-button
          theme="primary"
          :disabled="(showEmailInput && !portalStore.notifyEmail?.length) || !isAllEmails"
          @click="closeDialog"
        >
          Save & Close
        </t-button>
      </t-tooltip>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, defineEmits, watch, computed } from 'vue';
import { usePortalStore } from '@/store/creative/library/portal.store';
import { useRemoteConfigStore } from '@/store/global/configRemote.store';
import { tryOnMounted } from '@vueuse/core';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useAuthStageStore } from '@/store/global/auth.store';
import { useTips } from 'common/compose/tips';
import { emitSyncHubDriveState } from 'common/service/creative/library/sync-hub-drive-state';

const showCloseEmailConfirm = ref(false);
const showEmailInput = ref(false);

const portalStore = usePortalStore();
const { currentUser } = useAuthStageStore();

const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
const isAllEmails = computed(() => portalStore.notifyEmail.every(email => EMAIL_REGEX.test(email)));

const emit = defineEmits(['cancel']);

const { initConfigTable } = useRemoteConfigStore();
const { getConfig, createConfig, updateConfig, removeConfig } = initConfigTable({
  tableName: 'aix_web.creative_driver_notify_email',
  dataSource: 'common_config',
  env: 'prod',
});

const emailRecord = ref<any>();
const emailRecordList = ref<any[]>([]);

const updateEmailList = async () => {
  const configRes = await getConfig({
    condition: {
      game: gameCode,
    },
  });
  emailRecord.value = configRes.list?.[0];
  emailRecordList.value = configRes.list;
};

const changEmailValue = () => {
  if (showEmailInput.value) {
    showCloseEmailConfirm.value = true;
  } else {
    showCloseEmailConfirm.value = false;
    showEmailInput.value = true;
  }
};

const confirmRemove = () => {
  showEmailInput.value = false;
  showCloseEmailConfirm.value = false;
};

// TODO 检查是否已经有配置
const { gameCode } = useGlobalGameStore();

tryOnMounted(async () => {
  // 先进行emailInput 的显隐处理，再触发june哥的接口；否则会因june哥接口耗时影响了emailInput的及时展示
  // TODO emitDriveProcess
  await updateEmailList();
  // 如果已经有了，则根据以有的值进行展示
  if (emailRecord.value?.id) {
    showEmailInput.value = emailRecord.value.status === 'true';
    portalStore.notifyEmail = JSON.parse(emailRecord.value.emails);
  } else {
    if (portalStore.driveStatus === 'initializing') {
      // 正在初始化中，但没有配置email, 则不展示email
      showEmailInput.value = false;
    } else {
      showEmailInput.value = true;
      const [res] = await createConfig([
        {
          game: gameCode,
          emails: JSON.stringify(portalStore.notifyEmail),
          status: true,
        },
      ]);
      emailRecord.value = res;
    }
  }

  // TODO 触发june哥的接口，这个接口要用nest代理，应该不能把refresh token通过前端来传递
  // 若 不是在进行初始化，才需要触发此接口
  if (portalStore.driveStatus !== 'initializing') {
    const { result } = await emitSyncHubDriveState({
      game: gameCode,
      channel: portalStore.currentDriver,
      rootId: portalStore.directoryId!,
      arthubToken: portalStore.arthubToken,
      arthubCode: portalStore.arthubCode,
      authUser: portalStore.googleDriverToken?.authUser,
    });
    if (result?.error_code) {
      errTips(result?.error_message);
    }
  }
});

watch(showEmailInput, async (value) => {
  if (!value) {
    // TODO 如果关闭了，则要移除这条记录
    if (emailRecord.value.id) {
      await removeConfig(emailRecordList.value.map(({ id }) => id));
    }
  } else {
    await createConfig([
      {
        game: gameCode,
        emails: JSON.stringify([currentUser]),
        status: true,
      },
    ]);
  }
  await updateEmailList();
});

const { success, err: errTips } = useTips();
const closeDialog = async () => {
  // save
  if (showEmailInput.value && emailRecord.value?.id) {
    const res = await updateConfig([
      {
        ...emailRecord.value,
        emails: JSON.stringify(portalStore.notifyEmail),
      },
    ]);
    if (!res?.ret) {
      success('Update Email Success');
    }
  }
  emit('cancel');
  window.location.reload();
};
</script>
<style scoped lang="scss"></style>
