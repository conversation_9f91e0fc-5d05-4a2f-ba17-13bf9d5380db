<template>
  <div
    v-if="selectedNum"
    class="text-black-secondary text-sm ml-[8px] cursor-pointer bg-[#F0F1F6] rounded-[14px] px-[12px] py-[2px]"
  >
    <div class="flex items-center">
      <t-popup placement="bottom">
        <template #content>
          <ul class="p-[8px] list-disc list-inside max-h-[40vh] overflow-y-scroll narrow-scrollbar">
            <li
              v-for="asset in checkedAssetList"
              :key="asset.AssetID"
            >
              {{ asset.AssetName }}
            </li>
          </ul>
        </template>
        <span>{{ selectedNum }} Selected</span>
      </t-popup>
      <CloseCircleFilledIcon
        color="#929CB8"
        class="ml-[6px]"
        size="18"
        @click="emit('clear')"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, PropType } from 'vue';
import { TSimpleAsset } from '@/views/creative/library/define';
import { CloseCircleFilledIcon } from 'tdesign-icons-vue-next';

const props = defineProps({
  checkedAssetList: {
    type: Array as PropType<TSimpleAsset[]>,
    default: () => [],
  },
});

const emit = defineEmits(['clear']);

const selectedNum = computed(() => props.checkedAssetList?.length || 0);
</script>

<style scoped>

</style>
