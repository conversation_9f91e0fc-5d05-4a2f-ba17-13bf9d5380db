<template>
  <BaseDialog
    ref="dialogRef"
    :confirm-loading="confirmLoading"
    @confirm="confirmHandle"
    @close="cancelHandle"
  >
    <template #title>
      <span class="text-base leading-[24px]">Media Sync</span>
      <hover-select :checked-asset-list="selectAssets" />
    </template>
    <template #default>
      <t-form
        ref="formRef"
        :data="formData"
        :rules="rules"
      >
        <t-form-item label="Media" name="media">
          <div class="w-full flex items-center space-x-[10px]">
            <t-select
              v-model="formData.media"
              :options="MEDIA_LIST.map(i=>({label:i, value: i}))"
            />
            <t-popup
              v-if="formData.media"
              destroy-on-close
              show-arrow
              overlay-style
              :content="getLimitTips(formData.media)"
              placement="right"
              trigger="hover"
            >
              <ErrorCircleIcon class="cursor-pointer hover:opacity-50" />
            </t-popup>
          </div>
        </t-form-item>
        <t-form-item label="Account" name="account">
          <t-select
            v-model="formData.account"
            class="w-[470px]"
            placeholder="Please select account"
            :loading="isLoading"
            :options="accountsList"
            :min-collapsed-num="3"
            multiple
          />
        </t-form-item>
        <template v-if="formData.media === 'Unity'">
          <t-form-item label="Creative Type" name="creativeType">
            <t-select
              :value="1"
              :options="creativeTypeList"
            />
          </t-form-item>
          <t-form-item label="Language" name="language" required-mark>
            <t-select
              v-model="formData.language"
              :options="languageList"
              clearable
              filterable
            />
          </t-form-item>
        </template>
      </t-form>
      <Table
        class="w-[570px] mt-[24px] mx-auto"
        :columns="tableColumns"
        :data="taskList"
        row-key="index"
      />
    </template>
  </BaseDialog>
</template>

<script setup lang="ts">
import BaseDialog from 'common/components/Dialog/Base/Index.vue';
import Table from 'tdesign-vue-next/es/table';
import { computed, reactive, ref, watch } from 'vue';
import { useLoading } from 'common/compose/loading';
import { useTips } from 'common/compose/tips';
import { getAccountList } from 'common/service/media_account/get';
import { useGlobalGameStore } from '@/store/global/game.store';
import { get, tryOnBeforeMount } from '@vueuse/core';
import { ErrorCircleIcon } from 'tdesign-icons-vue-next';
import { useFetchWrapper } from 'common/compose/request/request';
import { getUploadMediaOption } from 'common/service/creative/library/manage-assets';
import { getLimitTips } from '@/views/creative/library/components/dialog/media/medias';
import { TSelectItem, TSimpleAsset } from '@/views/creative/library/define';
import dayjs from 'dayjs';
import {
  creativeTypeList,
  genTaskName, languageList,
  MEDIA_LIST,
  rules,
  useMediaTableColumns,
} from '@/views/creative/library/components/dialog/media/config';
import { TTaskItem } from '@/views/creative/library/components/dialog/media/interface';
import { isEmpty } from 'lodash-es';
import { addUpload } from 'common/service/creative/library/get-aix-task';
import { MediaType } from '@/views/trade/ads_creation/common/template/type';
import { useCheckAssetNameAndSize } from '@/views/creative/library/components/dialog/media/checkAssetInfo';
import { syncedSuccess, useValidateDialog } from '@/views/creative/library/compose/validate-dialog';
import { useGoto } from '@/router/goto';
import HoverSelect from '@/views/creative/library/components/dialog/hover-select.vue';


const dialogRef = ref();
const selectAssets = ref<TSimpleAsset[]>([]);
defineExpose({
  show: (assets: TSimpleAsset[]) => {
    selectAssets.value = assets;
    get(dialogRef)
      .show();
  },
});
const gameStore = useGlobalGameStore();

const formData = reactive({
  media: '' as MediaType,
  account: [] as string[],
  reminder: false,
  timePeriod: 7,
  creativeType: '',
  language: 'en',
});

const numberCheckErr = ref(undefined);


const taskList = ref<TTaskItem[]>([]);
const formRef = ref();

const updateItem = (index: number, data: TTaskItem) => {
  taskList.value.splice(index, 1, {
    ...data,
    taskName: genTaskName(data),
    field: data.field,
  });
};

function updateTaskNameList(media: string) {
  if (taskList.value.length === 0) {
    // 为空的时候，初始化一个数据内容
    const middleText = dayjs()
      .format('YYYY-MM-DD');
    const base = {
      index: 0,
      taskName: '',
      field: '',
      media,
      middleText,
    };
    taskList.value = [
      {
        ...base,
        taskName: genTaskName(base),
      },
    ];
  } else {
    // 如果已经有内容，只是对media内容做修改
    taskList.value = taskList.value.map(it => ({
      ...it,
      media,
      taskName: genTaskName({
        ...it,
        media,
      }),
    }));
  }
}

const tableColumns = useMediaTableColumns(updateItem);

const { err: errTips } = useTips();

const { data: mediaOptions, emit: getUploadOptions } = useFetchWrapper(
  getUploadMediaOption,
  {},
  { storage: 'getUploadMediaOption' },
);
const checkNamingOpt = computed(() => (mediaOptions as any)?.game_naming?.[gameStore.gameCode]);
// TODO 补齐权限校验的逻辑，从rabc上拿，这个人是否有配置media的权限、

const accountsList = ref<TSelectItem[]>([]);
const { isLoading, showLoading, hideLoading } = useLoading();

function cancelHandle() {
  hideLoading();
}

function updateAccountsList(media: string) {
  showLoading();
  getAccountList({
    gameCode: gameStore.gameCode,
    media: media as MediaType,
  })
    .then((res) => {
      if (res.length > 0) {
        const base: TSelectItem[] = [{
          label: 'All Accounts',
          checkAll: true,
        }];
        accountsList.value = base.concat(res.map((item: any) => ({
          label: item.label,
          value: item.value,
          disabled: item.isSuspend,
        })));
        // 默认使用全部帐号
        formData.account = accountsList.value
          .filter(i => !i.disabled && i.value)
          .map(i => i.value!) || [];
      } else {
        accountsList.value = [];
        formData.account = [];
      }
      hideLoading();
    })
    .catch((err) => {
      console.error('get Media accounts', err);
      errTips('get media accounts list err, please try again later!');
      hideLoading();
    });
}

tryOnBeforeMount(() => {
  getUploadOptions();
});

watch(
  () => formData.media,
  (value) => {
    updateAccountsList(value);
    updateTaskNameList(value);
  },
);

const {
  isLoading: confirmLoading,
  showLoading: showConfirmLoading,
  hideLoading: hideConfirmLoading,
} = useLoading();

/**
 * 开始执行传送到媒体的任务
 * @constructor
 */
async function syncedToMedia(
  selectAssets: TSimpleAsset[],
  accounts: string[],
  media: MediaType,
  mediaPath: string,
) {
  const res = await addUpload({
    assetIDs: selectAssets.map(i => i.AssetID),
    language: formData.language,
    toChannels: [
      {
        media,
        path: mediaPath,
        accounts: formData.account.join(','),
      },
    ],
    notifyDays: 0,
  }) as any;
  if (res.error_code === 0) return res;
  errTips(`Synced to Media err ,${res.error_msg}`);
}

const ValidateDialog = useValidateDialog();
const { gotoCreativeTask } = useGoto();

function confirmHandle() {
  if (numberCheckErr.value) return;
  showConfirmLoading();
  formRef.value.validate()
    .then((res: any) => {
      // 如果没有错误了
      if (isEmpty(res) || res === true) {
        //  检查命名和格式
        //  如果检查通过，提交任务
        const checkResult = useCheckAssetNameAndSize(
          formData.media,
          get(checkNamingOpt),
          get(selectAssets),
        );
        // 如果检查有校验失败，提交给校验弹窗 dialog/validate.vue
        if (!checkResult) {
          ValidateDialog.show({
            showMediaSelect: false,
            assetList: get(selectAssets),
            confirmText: 'Continue Sync',
            media: formData.media,
            namingType: get(checkNamingOpt),
            beforeClose: (assets: TSimpleAsset[]) => syncedToMedia(
              assets,
              formData.account,
              formData.media,
              taskList.value[0].taskName,
            )
              .then(() => syncedSuccess(gotoCreativeTask)),
          });
          hideConfirmLoading();
          get(dialogRef)
            .hide();
        } else {
          // 如果检查校验全部成功，直接同步到媒体
          syncedToMedia(
            get(selectAssets),
            formData.account,
            formData.media,
            taskList.value[0].taskName,
          )
            .then(() => {
              hideConfirmLoading();
              syncedSuccess(gotoCreativeTask);
              get(dialogRef)
                .hide();
            })
            .catch(() => {
              hideConfirmLoading();
            });
        }
      }
      hideConfirmLoading();
    });
}

</script>

<style scoped></style>
