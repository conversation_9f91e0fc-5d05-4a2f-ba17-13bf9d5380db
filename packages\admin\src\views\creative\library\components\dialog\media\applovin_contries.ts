const countries = [
  {
    id: '243',
    parent: '#',
    text: 'North America',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '248',
    parent: '#',
    text: 'Latin America and the Caribbean',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '274',
    parent: '#',
    text: 'Western Europe',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '296',
    parent: '#',
    text: 'Eastern Europe',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '317',
    parent: '#',
    text: 'Africa',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '365',
    parent: '#',
    text: 'Asia',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '408',
    parent: '#',
    text: 'Oceania (AU, NZ, Pacific Islands)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12163',
    parent: '274',
    text: 'Andorra (AD)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '404',
    parent: '365',
    text: 'United Arab Emirates (AE)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '366',
    parent: '365',
    text: 'Afghanistan (AF)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12160',
    parent: '248',
    text: 'Antigua and Barbuda (AG)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12168',
    parent: '248',
    text: 'Anguilla (AI)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '297',
    parent: '296',
    text: 'Albania (AL)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '367',
    parent: '365',
    text: 'Armenia (AM)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '319',
    parent: '317',
    text: 'Angola (AO)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '249',
    parent: '248',
    text: 'Argentina (AR)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17334',
    parent: '408',
    text: 'American Samoa (AS)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '275',
    parent: '274',
    text: 'Austria (AT)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '409',
    parent: '408',
    text: 'Australia (AU)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '250',
    parent: '248',
    text: 'Aruba (AW)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17351',
    parent: '296',
    text: '\u00c5land Islands (AX)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '368',
    parent: '365',
    text: 'Azerbaijan (AZ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '299',
    parent: '296',
    text: 'Bosnia and herzegovina (BA)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '251',
    parent: '248',
    text: 'Barbados (BB)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '370',
    parent: '365',
    text: 'Bangladesh (BD)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '276',
    parent: '274',
    text: 'Belgium (BE)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '322',
    parent: '317',
    text: 'Burkina Faso (BF)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '300',
    parent: '296',
    text: 'Bulgaria (BG)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '369',
    parent: '365',
    text: 'Bahrain (BH)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17321',
    parent: '317',
    text: 'Burundi (BI)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '320',
    parent: '317',
    text: 'Benin (BJ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17312',
    parent: '248',
    text: 'Saint Barth\u00e9lemy (BL)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '244',
    parent: '243',
    text: 'Bermuda (BM)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '372',
    parent: '365',
    text: 'Brunei Darussalam (BN)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '253',
    parent: '248',
    text: 'Bolivia (BO)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17307',
    parent: '248',
    text: 'Bonaire, Sint Eustatius and Saba (BQ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '254',
    parent: '248',
    text: 'Brazil (BR)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12152',
    parent: '248',
    text: 'Bahamas (BS)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '371',
    parent: '365',
    text: 'Bhutan (BT)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '321',
    parent: '317',
    text: 'Botswana (BW)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '298',
    parent: '296',
    text: 'Belarus (BY)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '252',
    parent: '248',
    text: 'Belize (BZ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '245',
    parent: '243',
    text: 'Canada (CA)',
    icon: false,
    state: { opened: false, disabled: false, selected: true },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '328',
    parent: '317',
    text: 'Congo, the Democratic Republic of the (CD)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '325',
    parent: '317',
    text: 'Central African Republic (CF)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '327',
    parent: '317',
    text: 'Congo (CG)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '294',
    parent: '274',
    text: 'Switzerland (CH)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '329',
    parent: '317',
    text: 'Cote D\'Ivoire (CI)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12166',
    parent: '408',
    text: 'Cook Islands (CK)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '255',
    parent: '248',
    text: 'Chile (CL)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '323',
    parent: '317',
    text: 'Cameroon (CM)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '374',
    parent: '365',
    text: 'China (CN)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '256',
    parent: '248',
    text: 'Colombia (CO)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '257',
    parent: '248',
    text: 'Costa Rica (CR)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17308',
    parent: '248',
    text: 'Cuba (CU)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '324',
    parent: '317',
    text: 'Cape Verde (CV)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12159',
    parent: '248',
    text: 'Curacao (CW)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '375',
    parent: '365',
    text: 'Cyprus (CY)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '302',
    parent: '296',
    text: 'Czech Republic (CZ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '280',
    parent: '274',
    text: 'Germany (DE)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '330',
    parent: '317',
    text: 'Djibouti (DJ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '277',
    parent: '274',
    text: 'Denmark (DK)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12170',
    parent: '248',
    text: 'Dominica (DM)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '258',
    parent: '248',
    text: 'Dominican Republic (DO)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '318',
    parent: '317',
    text: 'Algeria (DZ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '259',
    parent: '248',
    text: 'Ecuador (EC)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '303',
    parent: '296',
    text: 'Estonia (EE)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '331',
    parent: '317',
    text: 'Egypt (EG)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17329',
    parent: '317',
    text: 'Western Sahara (EH)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17323',
    parent: '317',
    text: 'Eritrea (ER)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '292',
    parent: '274',
    text: 'Spain (ES)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '333',
    parent: '317',
    text: 'Ethiopia (ET)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '278',
    parent: '274',
    text: 'Finland (FI)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12165',
    parent: '408',
    text: 'Fiji (FJ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17309',
    parent: '248',
    text: 'Falkland Islands (Malvinas) (FK)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17337',
    parent: '408',
    text: 'Micronesia, Federated States of (FM)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12162',
    parent: '274',
    text: 'Faroe Islands (FO)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '279',
    parent: '274',
    text: 'France (FR)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '334',
    parent: '317',
    text: 'Gabon (GA)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '295',
    parent: '274',
    text: 'United Kingdom (GB)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17310',
    parent: '248',
    text: 'Grenada (GD)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '376',
    parent: '365',
    text: 'Georgia (GE)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12161',
    parent: '248',
    text: 'French Guiana (GF)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '283',
    parent: '274',
    text: 'Guernsey (GG)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '336',
    parent: '317',
    text: 'Ghana (GH)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '281',
    parent: '274',
    text: 'Gibraltar (GI)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '246',
    parent: '243',
    text: 'Greenland (GL)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '335',
    parent: '317',
    text: 'Gambia (GM)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '337',
    parent: '317',
    text: 'Guinea (GN)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '261',
    parent: '248',
    text: 'Guadeloupe (GP)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '332',
    parent: '317',
    text: 'Equatorial Guinea (GQ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '282',
    parent: '274',
    text: 'Greece (GR)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '262',
    parent: '248',
    text: 'Guatemala (GT)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12154',
    parent: '408',
    text: 'Guam (GU)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '338',
    parent: '317',
    text: 'Guinea-Bissau (GW)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '263',
    parent: '248',
    text: 'Guyana (GY)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '377',
    parent: '365',
    text: 'Hong Kong (HK)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12153',
    parent: '248',
    text: 'Honduras (HN)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '301',
    parent: '296',
    text: 'Croatia (HR)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '264',
    parent: '248',
    text: 'Haiti (HT)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '304',
    parent: '296',
    text: 'Hungary (HU)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '379',
    parent: '365',
    text: 'Indonesia (ID)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '285',
    parent: '274',
    text: 'Ireland (IE)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '381',
    parent: '365',
    text: 'Israel (IL)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17353',
    parent: '274',
    text: 'Isle of Man (IM)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '378',
    parent: '365',
    text: 'India (IN)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '380',
    parent: '365',
    text: 'Iraq (IQ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '4780',
    parent: '365',
    text: 'Iran (IR)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '284',
    parent: '274',
    text: 'Iceland (IS)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '286',
    parent: '274',
    text: 'Italy (IT)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12167',
    parent: '274',
    text: 'Jersey (JE)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '265',
    parent: '248',
    text: 'Jamaica (JM)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '383',
    parent: '365',
    text: 'Jordan (JO)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '382',
    parent: '365',
    text: 'Japan (JP)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '339',
    parent: '317',
    text: 'Kenya (KE)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '387',
    parent: '365',
    text: 'Kyrgyzstan (KG)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '373',
    parent: '365',
    text: 'Cambodia (KH)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17335',
    parent: '408',
    text: 'Kiribati (KI)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17322',
    parent: '317',
    text: 'Comoros (KM)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17313',
    parent: '248',
    text: 'Saint Kitts and Nevis (KN)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17331',
    parent: '365',
    text: 'Korea, Democratic People\'s Republic of (KP)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '385',
    parent: '365',
    text: 'Korea, Republic of (KR)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '386',
    parent: '365',
    text: 'Kuwait (KW)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '13115',
    parent: '248',
    text: 'Cayman Islands (KY)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '384',
    parent: '365',
    text: 'Kazakhstan (KZ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '388',
    parent: '365',
    text: 'Lao People\'s Democractic Republic (LA)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '4781',
    parent: '365',
    text: 'Lebanon (LB)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17314',
    parent: '248',
    text: 'Saint Lucia (LC)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17354',
    parent: '274',
    text: 'Liechtenstein (LI)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '399',
    parent: '365',
    text: 'Sri Lanka (LK)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '341',
    parent: '317',
    text: 'Liberia (LR)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '340',
    parent: '317',
    text: 'Lesotho (LS)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '306',
    parent: '296',
    text: 'Lithuania (LT)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '287',
    parent: '274',
    text: 'Luxembourg (LU)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '305',
    parent: '296',
    text: 'Latvia (LV)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '342',
    parent: '317',
    text: 'Libyan Arab Jamahiriya (LY)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '348',
    parent: '317',
    text: 'Morocco (MA)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17355',
    parent: '274',
    text: 'Monaco (MC)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '308',
    parent: '296',
    text: 'Moldova, Republic of (MD)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '309',
    parent: '296',
    text: 'Montenegro (ME)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17315',
    parent: '248',
    text: 'Saint Martin (French part) (MF)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '343',
    parent: '317',
    text: 'Madagascar (MG)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17336',
    parent: '408',
    text: 'Marshall Islands (MH)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '307',
    parent: '296',
    text: 'Macedonia, the Former Yugoslav Republic of (MK)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '345',
    parent: '317',
    text: 'Mali (ML)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12149',
    parent: '365',
    text: 'Myanmar (MM)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12151',
    parent: '365',
    text: 'Mongolia (MN)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12150',
    parent: '365',
    text: 'Macao (MO)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '411',
    parent: '408',
    text: 'Northern Mariana Islands (MP)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12156',
    parent: '248',
    text: 'Martinique (MQ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '346',
    parent: '317',
    text: 'Mauritania (MR)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17311',
    parent: '248',
    text: 'Montserrat (MS)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '288',
    parent: '274',
    text: 'Malta (MT)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '347',
    parent: '317',
    text: 'Mauritius (MU)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '390',
    parent: '365',
    text: 'Maldives (MV)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '344',
    parent: '317',
    text: 'Malawi (MW)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '266',
    parent: '248',
    text: 'Mexico (MX)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '389',
    parent: '365',
    text: 'Malaysia (MY)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '349',
    parent: '317',
    text: 'Mozambique (MZ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '350',
    parent: '317',
    text: 'Namibia (NA)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17339',
    parent: '408',
    text: 'New Caledonia (NC)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '351',
    parent: '317',
    text: 'Niger (NE)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17341',
    parent: '408',
    text: 'Norfolk Island (NF)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '352',
    parent: '317',
    text: 'Nigeria (NG)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12155',
    parent: '248',
    text: 'Nicaragua (NI)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '289',
    parent: '274',
    text: 'Netherlands (NL)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '290',
    parent: '274',
    text: 'Norway (NO)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '391',
    parent: '365',
    text: 'Nepal (NP)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '4784',
    parent: '408',
    text: 'Dronning Maud Land (NQ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17338',
    parent: '408',
    text: 'Nauru (NR)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17340',
    parent: '408',
    text: 'Niue (NU)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '410',
    parent: '408',
    text: 'New Zealand (NZ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '392',
    parent: '365',
    text: 'Oman (OM)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '267',
    parent: '248',
    text: 'Panama (PA)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '269',
    parent: '248',
    text: 'Peru (PE)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12169',
    parent: '408',
    text: 'French Polynesia (PF)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17343',
    parent: '408',
    text: 'Papua New Guinea (PG)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '395',
    parent: '365',
    text: 'Philippines (PH)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '393',
    parent: '365',
    text: 'Pakistan (PK)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '310',
    parent: '296',
    text: 'Poland (PL)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17320',
    parent: '243',
    text: 'Saint Pierre and Miquelon (PM)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17344',
    parent: '408',
    text: 'Pitcairn (PN)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '270',
    parent: '248',
    text: 'Puerto Rico (PR)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '394',
    parent: '365',
    text: 'Palestinian Territory, Occupied (PS)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '291',
    parent: '274',
    text: 'Portugal (PT)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17342',
    parent: '408',
    text: 'Palau (PW)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '268',
    parent: '248',
    text: 'Paraguay (PY)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '396',
    parent: '365',
    text: 'Qatar (QA)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '353',
    parent: '317',
    text: 'Reunion (RE)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '311',
    parent: '296',
    text: 'Romania (RO)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '313',
    parent: '296',
    text: 'Serbia (RS)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '312',
    parent: '296',
    text: 'Russian Federation (RU)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '354',
    parent: '317',
    text: 'Rwanda (RW)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '397',
    parent: '365',
    text: 'Saudi Arabia (SA)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17345',
    parent: '408',
    text: 'Solomon Islands (SB)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17327',
    parent: '317',
    text: 'Seychelles (SC)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12158',
    parent: '317',
    text: 'Sudan (SD)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '293',
    parent: '274',
    text: 'Sweden (SE)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '398',
    parent: '365',
    text: 'Singapore (SG)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17325',
    parent: '317',
    text: 'Saint Helena, Ascension and Tristan da Cunha (SH)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '315',
    parent: '296',
    text: 'Slovenia (SI)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17357',
    parent: '296',
    text: 'Svalbard and Jan Mayen (SJ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '314',
    parent: '296',
    text: 'Slovakia (SK)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '356',
    parent: '317',
    text: 'Sierra Leone (SL)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17356',
    parent: '274',
    text: 'San Marino (SM)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '355',
    parent: '317',
    text: 'Senegal (SN)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '357',
    parent: '317',
    text: 'Somalia (SO)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12157',
    parent: '248',
    text: 'Suriname (SR)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17328',
    parent: '317',
    text: 'South Sudan (SS)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17326',
    parent: '317',
    text: 'Sao Tome and Principe (ST)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '260',
    parent: '248',
    text: 'El Salvador (SV)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17317',
    parent: '248',
    text: 'Sint Maarten (Dutch part) (SX)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '4783',
    parent: '365',
    text: 'Syria (SY)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '359',
    parent: '317',
    text: 'Swaziland (SZ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17318',
    parent: '248',
    text: 'Turks and Caicos Islands (TC)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '326',
    parent: '317',
    text: 'Chad (TD)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '361',
    parent: '317',
    text: 'Togo (TG)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '402',
    parent: '365',
    text: 'Thailand (TH)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '401',
    parent: '365',
    text: 'Tajikistan (TJ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17346',
    parent: '408',
    text: 'Tokelau (TK)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17332',
    parent: '365',
    text: 'Timor-Leste (TL)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17333',
    parent: '365',
    text: 'Turkmenistan (TM)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '362',
    parent: '317',
    text: 'Tunisia (TN)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17347',
    parent: '408',
    text: 'Tonga (TO)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '403',
    parent: '365',
    text: 'Turkey (TR)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '271',
    parent: '248',
    text: 'Trinidad and Tobago (TT)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17348',
    parent: '408',
    text: 'Tuvalu (TV)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '400',
    parent: '365',
    text: 'Taiwan (TW)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '360',
    parent: '317',
    text: 'Tanzania, United Republic of (TZ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '316',
    parent: '296',
    text: 'Ukraine (UA)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '363',
    parent: '317',
    text: 'Uganda (UG)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '247',
    parent: '243',
    text: 'United States (US)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '272',
    parent: '248',
    text: 'Uruguay (UY)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '405',
    parent: '365',
    text: 'Uzbekistan (UZ)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17352',
    parent: '296',
    text: 'Holy See (Vatican City State) (VA)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17316',
    parent: '248',
    text: 'Saint Vincent and the Grenadines (VC)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '273',
    parent: '248',
    text: 'Venezuela (VE)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17319',
    parent: '248',
    text: 'Virgin Islands, British (VG)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '12164',
    parent: '248',
    text: 'Virgin Islands (VI)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '406',
    parent: '365',
    text: 'Viet Nam (VN)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17349',
    parent: '408',
    text: 'Vanuatu (VU)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17350',
    parent: '408',
    text: 'Wallis and Futuna (WF)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '412',
    parent: '408',
    text: 'Samoa (WS)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '729943',
    parent: '296',
    text: 'Kosovo (XK)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '407',
    parent: '365',
    text: 'Yemen (YE)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17324',
    parent: '317',
    text: 'Mayotte (YT)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '358',
    parent: '317',
    text: 'South Africa (ZA)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '364',
    parent: '317',
    text: 'Zambia (ZM)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }, {
    id: '17330',
    parent: '317',
    text: 'Zimbabwe (ZW)',
    icon: false,
    state: { opened: false, disabled: false, selected: false },
    li_attr: [],
    a_attr: [],
    custom_properties: { type: 'country', true_parent_id: '0' },
  }];

interface TreeNode {
  id: string;
  parent: string;
  text: string;
  [key: string]: any;
}

interface Option {
  label: string;
  value: string;
  children: Option[];
}

function transformTreeData(data: TreeNode[]): Option[] {
  const map = new Map<string, Option>();
  const result: Option[] = [];

  // 创建所有节点并存储到map中
  data.forEach((item) => {
    const textMatch = item.text.match(/\((\w{2})\)/);
    const node: Option = {
      label: item.text,
      value: textMatch ? (textMatch[1] as string).toLowerCase() : item.id,
      children: [],
    };
    map.set(item.id, node);
  });

  // 构建树形结构
  data.forEach((item) => {
    const node = map.get(item.id)!;
    if (item.parent === '#') {
      result.push(node);
    } else {
      const parent = map.get(item.parent);
      parent?.children.push(node);
    }
  });

  // 对根节点和子节点进行排序
  const sortByLabel = (a: Option, b: Option) => a.label.localeCompare(b.label);
  result.sort(sortByLabel);
  result.forEach(root => root.children.sort(sortByLabel));

  return result;
}

export const getAppLovinContries = () => transformTreeData(countries);
