import { MediaType, NamingType, TSimpleAsset } from '@/views/creative/library/define';
import { checkMediaValidInfo } from '@/views/creative/library/components/dialog/media/medias';
import { UploadItem } from '@/views/creative/library/components/dialog/media/interface';
import { checkNamingValidInfo } from '@/views/creative/library/components/dialog/media/namings';
import { isEmpty } from 'lodash-es';

export function transformItem(item: TSimpleAsset): UploadItem {
  return {
    id: item.AssetID,
    name: item.AssetName,
    format: item.materialExt?.universal?.format,
    size: item.materialExt?.universal.size,
    duration: item.materialExt.video.duration,
    width: item.materialExt.video.width,
    height: item.materialExt.video.high,
    mediaType: item.type!,
  };
}

/**
 * 检查素材的命名和格式是否符合要求，如果返回true表示符合，如果为false，表示存在不符合的内容
 * @param media
 * @param namingType
 * @param assetsList
 */
export function useCheckAssetNameAndSize(
  media: MediaType,
  namingType: NamingType,
  assetsList: TSimpleAsset[],
): boolean {
  return !assetsList
    .map((asset: TSimpleAsset) => {
      const checkItem = transformItem(asset);
      const nameCheck = isEmpty(checkNamingValidInfo(namingType, checkItem).namingWarnings);
      const metaCheck = checkMediaValidInfo(media,  checkItem);
      return nameCheck && isEmpty(metaCheck?.metaWarnings) && isEmpty(metaCheck?.metaErrors);
    })
    .some(i => !i);
}
