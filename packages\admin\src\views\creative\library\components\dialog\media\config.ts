import { Input } from 'tdesign-vue-next';
import { TTaskItem } from '@/views/creative/library/components/dialog/media/interface';
import { computed } from 'vue';
import { useAuthStageStore } from '@/store/global/auth.store';
import { isEmpty } from 'lodash-es';


const baseMediaList = [
  { name: 'Google', authId: 1 },
  { name: 'Facebook', authId: 2 },
  { name: 'TikTok', authId: 3 },
  { name: 'Twitter', authId: 4 },
  // { name: 'ASA', authId: 5 },
  // { name: 'Reddit', authId: 6 },
  { name: 'Unity', authId: 7 },
  { name: 'Snapchat', authId: 8 },
  { name: 'AppLovin', authId: 9 },
];
export const MEDIA_LIST = computed(() => {
  // 根据权限配置，来设定渠道拉取的范围
  // 只有在调用发生的时候，才去获取store，进行计算。
  const authStore = useAuthStageStore();
  const mediaChannel = authStore.getDimension('media_channel');
  return (isEmpty(mediaChannel) ? baseMediaList : baseMediaList.filter(i => mediaChannel.includes(i.authId)))
    .map(i => i.name);
});

export const rules = {
  media: [{ required: true, message: 'Media should not be empty' }],
  account: [{ required: true, message: 'Account should not be empty', trigger: 'change' }],
};

// eslint-disable-next-line @typescript-eslint/naming-convention
export const creativeTypeList = [
  { label: 'Video, Square End Card, Responsive Playable', value: 1 },
  { label: 'End Card Pair', value: 2, disabled: true },
  { label: 'Portrait Playable', value: 3, disabled: true },
  { label: 'Landcape Playable', value: 4, disabled: true },
];

// eslint-disable-next-line @typescript-eslint/naming-convention
export const languageList = [
  { label: 'No linguistic content', value: 'zxx' },
  { label: 'Undetermined', value: 'und' },
  { label: 'Afrikaans', value: 'af' },
  { label: 'Arabic', value: 'ar' },
  { label: 'Assamese', value: 'as' },
  { label: 'Azerbaijani', value: 'az' },
  { label: 'Belarusian', value: 'be' },
  { label: 'Bengali(Bangla)', value: 'bn' },
  { label: 'Bulgarian', value: 'bg' },
  { label: 'Catalan(Valencian)', value: 'ca' },
  { label: 'Chinese', value: 'zh' },
  { label: 'Croatian', value: 'hr' },
  { label: 'Czech', value: 'cs' },
  { label: 'Danish', value: 'da' },
  { label: 'Dutch(Flemish)', value: 'nl' },
  { label: 'English', value: 'en' },
  { label: 'Estonian', value: 'et' },
  { label: 'Filipino(Pilipino)', value: 'fil' },
  { label: 'Finnish', value: 'fi' },
  { label: 'French', value: 'fr' },
  { label: 'German', value: 'de' },
  { label: 'Modern Greek', value: 'el' },
  { label: 'Hebrew', value: 'he' },
  { label: 'Hindi', value: 'hi' },
  { label: 'Hungarian', value: 'hu' },
  { label: 'Icelandic', value: 'is' },
  { label: 'Indonesian', value: 'id' },
  { label: 'Italian', value: 'it' },
  { label: 'Japanese', value: 'ja' },
  { label: 'Kazakh', value: 'kk' },
  { label: 'Korean', value: 'ko' },
  { label: 'Kirghiz(Kyrgyz)', value: 'ky' },
  { label: 'Latvian', value: 'lv' },
  { label: 'Lithuanian', value: 'lt' },
  { label: 'Macedonian', value: 'mk' },
  { label: 'Malay', value: 'ms' },
  { label: 'Marathi', value: 'mr' },
  { label: 'Mongolian', value: 'mn' },
  { label: 'Nepali', value: 'ne' },
  { label: 'Norwegian', value: 'no' },
  { label: 'Pushto(Pashto)', value: 'ps' },
  { label: 'Persian', value: 'fa' },
  { label: 'Polish', value: 'pl' },
  { label: 'Portuguese', value: 'pt' },
  { label: 'Romanian(Moldavian)', value: 'ro' },
  { label: 'Russian', value: 'ru' },
  { label: 'Sanskrit', value: 'sa' },
  { label: 'Serbian', value: 'sr' },
  { label: 'Slovak', value: 'sk' },
  { label: 'Slovenian', value: 'sl' },
  { label: 'Spanish(Castilian)', value: 'es' },
  { label: 'Swedish', value: 'sv' },
  { label: 'Tamil', value: 'ta' },
  { label: 'Thai', value: 'th' },
  { label: 'Turkish', value: 'tr' },
  { label: 'Ukrainian', value: 'uk' },
  { label: 'Urdu', value: 'ur' },
  { label: 'Uzbek', value: 'uz' },
  { label: 'Vietnamese', value: 'vi' },
];

export const genTaskName = (item: TTaskItem) => {
  if (item.media !== 'AppLovin') {
    return [
      item.media,
      item.middleText,
      item.field,
    ].filter(Boolean).join('_');
  }
  return [
    item.media,
    item.middleText,
    item.creative_set_name,
  ].filter(Boolean).join('_');
};

export const useMediaTableColumns = (updateItem: (index: number, content: TTaskItem) => void) => [
  {
    title: 'Task Name',
    colKey: 'taskName',
    ellipsis: true,
  },
  {
    title: 'Customize Field',
    colKey: 'field',
    ellipsis: true,
    edit: {
      component: Input,
      defaultEditable: true,
      props: {
        clearable: true,
        autofocus: true,
      },
      abortEditOnEvent: ['onEnter'],
      on: () => ({
        onChange(context: any) {
          updateItem(context.rowIndex, context.editedRow);
        },
      }),
      onEdited: (context: any) => {
        updateItem(context.rowIndex, context.newRowData);
      },
    },
  },
];
