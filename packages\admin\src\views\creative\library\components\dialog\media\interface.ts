export type UploadItem = {
  id: string;
  name: string;
  format: string;
  size: number;
  duration: number;
  width: number;
  height: number;
  mediaType: 'image' | 'video' | 'other' | 'text';
};

export type UploadItemWithValidInfo = UploadItem & UploadMetaValidInfo & Partial<UploadNamingValidInfo>;

export type UploadMetaValidInfo = {
  /** 文件meta不满足推荐要求 */
  metaWarnings: string[];
  /** 文件meta不满足基本要求 */
  metaErrors: string[];
};

export type UploadNamingValidInfo = {
  /** 命名不规范信息 */
  namingWarnings: string[];
};


export type TTaskItem = {
  index?: number;
  taskName?: string;
  field: string;
  media?: string;
  middleText?: string;
  creative_set_name?: string
};
