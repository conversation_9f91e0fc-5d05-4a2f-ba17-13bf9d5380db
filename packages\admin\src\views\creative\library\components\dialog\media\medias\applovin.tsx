// see https://support.google.com/google-ads/answer/10012391
import { UploadItem, UploadMetaValidInfo } from '../interface';
import { Table } from 'tdesign-vue-next';
import List from './list.vue';


const imageDatas = [
  {
    asset_type: 'Portrait Interstitial',
    dimension: '768x1024\n320x512\n320x480',
    file_size: '<3MB\n(Animation loops <60s)',
    file_type: 'JPG, PNG, GIF',
  },
  {
    asset_type: 'Landscape Interstitial',
    dimension: '1024x768\n512x320\n480x320',
    file_size: '<3MB\n(Animation loops <60s)',
    file_type: 'JPG, PNG, GIF',
  },
  {
    asset_type: 'Banner',
    dimension: '320x50',
    file_size: '<3MB\n(Animation loops <60s)',
    file_type: 'JPG, PNG, GIF',
  },
  {
    asset_type: 'Mrec',
    dimension: '300x250',
    file_size: '<3MB\n(Animation loops <60s)',
    file_type: 'JPG, PNG, GIF',
  },
  {
    asset_type: 'Leader',
    dimension: '728x90',
    file_size: '<3MB\n(Animation loops <60s)',
    file_type: 'JPG, PNG, GIF',
  },
  {
    asset_type: 'Native',
    dimension: '-',
    file_size: '<3MB\n(Animation loops <60s)',
    file_type: 'JPG, PNG, GIF',
  },
  {
    asset_type: 'App Icon',
    dimension: 'Min 300x300\nMax 600x600',
    file_size: '<3MB\n(Animation loops <60s)',
    file_type: 'JPG, PNG, GIF',
  },
  {
    asset_type: 'Foreground Object',
    dimension: '192x192',
    file_size: '<3MB\n(Animation loops <60s)',
    file_type: 'JPG, PNG, GIF',
  },
];

const videoDatas = [
  {
    asset_type: 'Portrait Short',
    dimension: 'Suggested 9:16',
    file_size: '<1GB\nShort Video < 15s\nLong Video 15-60s\n',
    file_type: 'MP4, MOV',
  },
  {
    asset_type: 'Portrait Long',
    dimension: 'Suggested 9:16',
    file_size: '<1GB\nShort Video < 15s\nLong Video 15-60s\n',
    file_type: 'MP4, MOV',
  },
  {
    asset_type: 'Landscape Short',
    dimension: 'Suggested 16:9',
    file_size: '<1GB\nShort Video < 15s\nLong Video 15-60s\n',
    file_type: 'MP4, MOV',
  },
  {
    asset_type: 'Landscape Long',
    dimension: 'Suggested 16:9',
    file_size: '<1GB\nShort Video < 15s\nLong Video 15-60s\n',
    file_type: 'MP4, MOV',
  },
];

const htmlDatas = [
  {
    asset_type: 'HTML File',
    dimension: 'Responsive to\nvarying screen\nsizes',
    file_size: '<5MB',
    file_type: 'Single in-line\nHTML file',
  },
];

const commonAttrs = ({ rowIndex }: { rowIndex: number}) => {
  // console.log(params);
  if (rowIndex === -1) {
    return {
      style: {
        borderRadius: 0,
      },
    };
  }
  return {};
};

const columns = [
  {
    colKey: 'asset_type',
    title: 'Asset type',
    width: 120,
    attrs: commonAttrs,
  },
  {
    colKey: 'dimension',
    title: 'Dimension',
    width: 100,
    attrs: commonAttrs,
    cell: (h, { row }) => <pre>{row.dimension}</pre>,
  },
  {
    colKey: 'file_size',
    title: 'File Size',
    attrs: commonAttrs,
    width: 150,
    cell: (h, { row }) => <pre>{row.file_size}</pre>,
  },
  {
    colKey: 'file_type',
    title: 'File Type',
    attrs: commonAttrs,
    width: 100,
    cell: (h, { row }) => <pre>{row.file_type}</pre>,
  },
];


export const LIMIT_TIPS = (
  <List
    title={'Applovin  Upload Requirements：'}
    disc= {true}
    list={[
      {
        title: 'Creative Set Rules',
        content: (
          <div>
          <p>Each creative set supports 13 different asset types, with only 1 asset allowed per type.</p>
          <p>Note: Aix will upload the selected assets as a new creative set.</p>
          </div>
        ),
      },
      {
        title: 'Image Specifications',
        content: (
          <Table
            class={'w-[640px]'}
            bordered
            rowKey={'asset_type'}
            data={imageDatas}
            rowspanAndColspan={
              ({ colIndex }) => (colIndex > 1 ? { rowspan: 8, colspan: 1 } : { colspan: 1, rowspan: 1 })
            }
            columns={columns}
          />
        ),
      },
      {
        title: 'Video',
        content: (
          <Table
            class={'w-[640px]'}
            bordered
            rowKey={'asset_type'}
            data={videoDatas}
            rowspanAndColspan={
              ({ colIndex }) => (colIndex > 1 ? { rowspan: 4, colspan: 1 } : { colspan: 1, rowspan: 1 })
            }
            columns={columns}
          />
        ),
      },
      {
        title: 'Html',
        content: (
          <Table
            class={'w-[640px]'}
            bordered
            rowKey={'asset_type'}
            data={htmlDatas}
            columns={columns}
          />
        ),
      },
    ]}
  />
);

export function checkValid(record: UploadItem) {
  const validInfo: UploadMetaValidInfo = {
    metaWarnings: [],
    metaErrors: [],
  };
  console.log('upload snapchat', record);
  return validInfo;
}
