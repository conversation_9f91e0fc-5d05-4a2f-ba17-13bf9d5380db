// see https://www.facebook.com/business/ads-guide/video
// see https://www.facebook.com/business/ads-guide/image
import { formatFileSize } from 'common/utils/format';
import List from './list.vue';
import { UploadItem, UploadMetaValidInfo } from '../interface';
import { joinSize } from '@/views/creative/library/components/dialog/media/medias/utils';

const IMAGE_RATIO_RANGE = [1.919 / 1, 1 / 1, 9 / 16]; // 1.91~4:5
const VIDEO_RATIO_RANGE = [16 / 9, 9 / 16, 1 / 1];
const VIDEO_MAX_SIZE = 4 * 1024 * 1024 * 1024; // 4G
const VIDEO_MAX_SIZE_TEXT = formatFileSize(VIDEO_MAX_SIZE);
const VIDEO_MIN_SIZE = { width: 120, height: 120 };
const IMAGE_MIN_SIZE = { width: 600, height: 600 };
const IMAGE_MAX_SIZE = 30 * 1024 * 1024; // 30M
const IMAGE_MAX_SIZE_TEXT = formatFileSize(IMAGE_MAX_SIZE);
const VIDEO_MAX_DURATION_MINUTES = 240; // 240分钟
const VIDEO_MAX_DURATION = 60 * VIDEO_MAX_DURATION_MINUTES;
const VIDEO_MAX_DURATION_TEXT = `${VIDEO_MAX_DURATION_MINUTES}minutes`;

export const LIMIT_TIPS = (
  <List
    title={'Facebook Channel Upload Requirements'}
    list={[
      'Image format not restricted, recommended: JPG or PNG',
      'Supported Image Aspect Ratios：1.91:1 、 4:5 、 1:1',
      'Min image dimensions: 600x600, not lower than 1080x1080',
      'Maximum Image File Size：30 MB',
      'Video format not restricted, recommended: MP4 or MOV',
      'Supported Video Aspect Ratios：16:9 、 9:16 、 1:1',
      'Min video resolution: 120x120,not lower than 1080x1080',
      'Maximum video duration: 240 minutes',
      'Maximum video file size: 4 GB',
    ]}
  />
);

export function checkValid(record: UploadItem) {
  const validInfo: UploadMetaValidInfo = {
    metaWarnings: [],
    metaErrors: [],
  };

  const { size, duration, width, height, mediaType } = record;
  if (mediaType === 'image') {
    if (size > IMAGE_MAX_SIZE) {
      validInfo.metaWarnings.push(`The image size (${formatFileSize(size)}) exceeds the upper limit (${IMAGE_MAX_SIZE_TEXT})`);
    } else if (width === 0 || height === 0) {
      validInfo.metaWarnings.push('Image dimension information not parsed');
      return validInfo;
    } else if (width < IMAGE_MIN_SIZE.width || height < IMAGE_MIN_SIZE.height) {
      validInfo.metaWarnings.push(`The image size (${joinSize({ width, height })}) cannot be less than ${joinSize(IMAGE_MIN_SIZE)}`);
    } else {
      const ratio = width / height;
      if (ratio > IMAGE_RATIO_RANGE[0] || ratio < IMAGE_RATIO_RANGE[1]) {
        validInfo.metaWarnings.push(`The image size (${joinSize({ width, height })}) ratio does not meet the requirements`);
      }
    }
  } else if (mediaType === 'video') {
    if (size > VIDEO_MAX_SIZE) {
      validInfo.metaWarnings.push(`The video size (${formatFileSize(size)}) exceeds the upper limit (${VIDEO_MAX_SIZE_TEXT})`);
    } else if (width === 0 || height === 0) {
      validInfo.metaWarnings.push('Video dimension information not parsed');
      return validInfo;
    } else if (duration > VIDEO_MAX_DURATION) {
      validInfo.metaWarnings.push(`The video duration exceeds the upper limit (${VIDEO_MAX_DURATION_TEXT})`);
    } else if (width < VIDEO_MIN_SIZE.width || height < VIDEO_MIN_SIZE.height) {
      validInfo.metaWarnings.push(`The video size (${joinSize({ width, height })}) cannot be less than ${joinSize(VIDEO_MIN_SIZE)}`);
    } else {
      const ratio = width / height;
      if (ratio > VIDEO_RATIO_RANGE[0] || ratio < VIDEO_RATIO_RANGE[1]) {
        validInfo.metaWarnings.push(`The video size (${joinSize({ width, height })}) ratio does not meet the requirements`);
      }
    }
  } else {
    validInfo.metaErrors.push('Only videos or pictures can be uploaded');
  }

  return validInfo;
}
