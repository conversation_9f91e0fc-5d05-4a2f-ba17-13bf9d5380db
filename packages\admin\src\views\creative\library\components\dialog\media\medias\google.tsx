// see https://support.google.com/google-ads/answer/10012391
import { UploadItem, UploadMetaValidInfo } from '../interface';
import { formatFileSize } from 'common/utils/format';
import { joinSize } from './utils';
import { Table } from 'tdesign-vue-next';
import List from './list.vue';

const IMAGE_TYPE = ['jpg', 'png', 'gif'];
const IMAGE_TYPE_TEXT = IMAGE_TYPE.join('、');
const VIDEO_TYPE = ['avi', 'mov', 'mp4', 'wmv'];
const VIDEO_TYPE_TEXT = VIDEO_TYPE.join('、');
const IMAGE_MAX_SIZE = 5 * 1024 * 1024; // 5M
const IMAGE_MAX_SIZE_TEXT = formatFileSize(IMAGE_MAX_SIZE);

type SizeInfo = {
  width: number;
  height: number;
};
type SizeRatioInfo = {
  ratio: number;
  minimumSize: SizeInfo;
  recommendedSize: SizeInfo;
};
const IMAGE_RATIO: Record<string, SizeRatioInfo> = {
  '1:1 (Square)': {
    ratio: 1,
    minimumSize: { width: 200, height: 200 },
    recommendedSize: { width: 1200, height: 1200 },
  },
  '1.91:1 (Landscape)': {
    ratio: 1.91 / 1,
    minimumSize: { width: 600, height: 314 },
    recommendedSize: { width: 1200, height: 628 },
  },
  '4:5 (Portrait)': {
    ratio: 4 / 5,
    minimumSize: { width: 320, height: 400 },
    recommendedSize: { width: 1200, height: 1500 },
  },
  '4:1 (Logo)': {
    ratio: 4 / 1,
    minimumSize: { width: 512, height: 128 },
    recommendedSize: { width: 1200, height: 300 },
  },
};

function isValidImageSize(width: number, height: number, sizeRatioInfo: SizeRatioInfo) {
  const { ratio, minimumSize } = sizeRatioInfo;
  const imageRatio = width / height;
  const isValidRatio = imageRatio >= ratio - 0.009 && imageRatio <= ratio + 0.009;
  if (!isValidRatio) return false;
  return width >= minimumSize.width && height >= minimumSize.height;
}

function checkImage(width: number, height: number) {
  return (
    Object.values(IMAGE_RATIO)
      .map(sizeRatioInfo => isValidImageSize(width, height, sizeRatioInfo))
      .filter(Boolean).length > 0
  );
}

const imageRatioArr = Object.keys(IMAGE_RATIO).map((key) => {
  const sizeInfo = IMAGE_RATIO[key];
  return {
    aspectRatio: key,
    minimumSize: joinSize(sizeInfo.minimumSize),
    recommendedSize: joinSize(sizeInfo.recommendedSize),
  };
});

export const LIMIT_TIPS = (
  <List
    title={'Google Channel Upload Requirements'}
    list={[
      'Image format restrictions: JPG, PNG, GIF',
      'Image file size limit: 5 MB',
      {
        title: 'Image Size Restrictions：',
        content: (
          <Table
            class={'w-[500px]'}
            bordered
            rowKey={'aspectRatio'}
            data={imageRatioArr}
            columns={[
              {
                colKey: 'aspectRatio',
                title: 'Aspect ratio',
              },
              {
                colKey: 'minimumSize',
                title: 'Minimum size',
              },
              {
                colKey: 'recommendedSize',
                title: 'Recommended size',
              },
            ]}
          />
        ),
      },
      'Video Format Restrictions: AVI, MOV, MP4, WMV',
    ]}
  />
);

export function checkValid(record: UploadItem) {
  const validInfo: UploadMetaValidInfo = {
    metaWarnings: [],
    metaErrors: [],
  };

  const { format, size, width, height, mediaType } = record;
  const lowerCaseFormat = (format || '').toLowerCase();
  if (mediaType === 'image') {
    if (!IMAGE_TYPE.includes(lowerCaseFormat)) {
      validInfo.metaErrors.push(`Image Formats Allowed: ${IMAGE_TYPE_TEXT}`);
    } else if (size > IMAGE_MAX_SIZE) {
      validInfo.metaErrors.push(`Image size(${formatFileSize(size)})exceeds the maximum limit(${IMAGE_MAX_SIZE_TEXT})`);
    } else if (width === 0 || height === 0) {
      validInfo.metaWarnings.push('Image size information not detected');
      return validInfo;
    } else if (!checkImage(width, height)) {
      validInfo.metaWarnings.push(`Image dimensions(${joinSize({ width, height })})do not meet the requirements`);
    }
  } else if (mediaType === 'video') {
    if (!VIDEO_TYPE.includes(lowerCaseFormat)) {
      validInfo.metaErrors.push(`Allowed Video Formats: ${VIDEO_TYPE_TEXT}`);
    }
  } else {
    validInfo.metaErrors.push('Only videos or images can be uploaded');
  }
  return validInfo;
}
