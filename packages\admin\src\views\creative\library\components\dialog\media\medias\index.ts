import { UploadItem, UploadMetaValidInfo } from '../interface';
import * as google from './google';
import * as facebook from './facebook';
import * as tiktok from './tiktok';
import * as twitter from './twitter';
import * as unity from './unity';
import * as snapchat from './snapchat';
import * as applovin from './applovin';
import { MediaType } from '@/views/creative/library/define';
import { VNode } from 'vue';

type MediaValidator = {
  LIMIT_TIPS: VNode | string;
  checkValid: (record: UploadItem) => UploadMetaValidInfo;
};

const instanceMap: Partial<Record<MediaType, MediaValidator>> = {
  Google: google,
  Facebook: facebook,
  TikTok: tiktok,
  Twitter: twitter,
  Unity: unity,
  Snapchat: snapchat,
  AppLovin: applovin,
};

function getInstance(media: MediaType) {
  return instanceMap[media];
}

export function getLimitTips(media: String) {
  const instance = getInstance(media as MediaType);
  return () => instance?.LIMIT_TIPS;
}

// 返回true表示检查无问题，返回 namingWarnings: [] 表示存在错误内容

export function checkMediaValidInfo(media: MediaType, record: UploadItem) {
  const instance = getInstance(media);
  return instance?.checkValid?.(record);
}
