<template>
  <div class="my-[10px] space-y-[10px] max-h-screen overflow-auto p-[16px]">
    <h3 class="font-bold">{{ title }}</h3>
    <ul class="space-y-[5px] list-inside" :class="{'list-disc':disc}">
      <li
        v-for="(item, i) in list"
        :key="i"
      >
        <template v-if="isString(item)">{{ item }}</template>
        <template v-else>
          <template v-if="item?.title">
            <span>{{ item.title }}</span><br>
          </template>
          <template v-if="isVNode(item?.content)">
            <component :is="item.content" />
          </template>
          <template v-else>
            {{ item.content }}
          </template>
        </template>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { PropType, isVNode } from 'vue';
import { isString } from 'lodash-es';

defineProps({
  title: {
    type: String,
    default: '',
  },
  list: {
    type: Array as PropType<Array<{ title?: string; content: any } | string>>,
    default: () => [],
  },
  disc: {
    type: Boolean,
    default: true,
  },
});
</script>

<style scoped></style>
