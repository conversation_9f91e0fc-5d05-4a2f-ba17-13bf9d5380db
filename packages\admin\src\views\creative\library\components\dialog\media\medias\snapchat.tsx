// see https://support.google.com/google-ads/answer/10012391
import { UploadItem, UploadMetaValidInfo } from '../interface';
import { Table } from 'tdesign-vue-next';
import List from './list.vue';

// const IMAGE_TYPE = ['jpg', 'png', 'gif'];
// const IMAGE_TYPE_TEXT = IMAGE_TYPE.join('、');
// const VIDEO_TYPE = ['avi', 'mov', 'mp4', 'wmv'];
// const VIDEO_TYPE_TEXT = VIDEO_TYPE.join('、');
// const IMAGE_MAX_SIZE = 5 * 1024 * 1024; // 5M
// const IMAGE_MAX_SIZE_TEXT = formatFileSize(IMAGE_MAX_SIZE);


// function isValidImageSize(width: number, height: number, sizeRatioInfo: SizeRatioInfo) {
//   const { ratio, minimumSize } = sizeRatioInfo;
//   const imageRatio = width / height;
//   const isValidRatio = imageRatio >= ratio - 0.009 && imageRatio <= ratio + 0.009;
//   if (!isValidRatio) return false;
//   return width >= minimumSize.width && height >= minimumSize.height;
// }

// function checkImage(width: number, height: number) {
//   return (
//     Object.values(IMAGE_RATIO)
//       .map(sizeRatioInfo => isValidImageSize(width, height, sizeRatioInfo))
//       .filter(Boolean).length > 0
//   );
// }

const imageDatas = [
  {
    creative_type: 'Top Snap',
    ratio: '9:16',
    spec: 'Min 1080 x 1920',
    file_size: 'Max 5MB',
    file_type: 'PNG, JPG',
  },
  {
    creative_type: 'App Icon (Snap Ads)',
    ratio: '1:1',
    spec: 'Min 200 x 200, Max 2000 x 2000',
    file_size: '-',
    file_type: 'PNG',
  },
  {
    creative_type: 'App Icon (Lens)',
    ratio: '1:1',
    spec: '256 x 256',
    file_size: '-',
    file_type: 'PNG',
  },
  {
    creative_type: 'Preview Image (Story Ad)',
    ratio: '3:5',
    spec: 'Min 360 x 600	',
    file_size: 'Max 2MB',
    file_type: 'PNG',
  },
  {
    creative_type: 'Audience Filter	',
    ratio: '-',
    spec: '1080 x 2340	',
    file_size: 'Max 2MB',
    file_type: 'PNG',
  },
  {
    creative_type: 'Top Snap',
    ratio: '9:16',
    spec: 'Min 1080 x 1920',
    file_size: 'Max 300KB',
    file_type: 'PNG',
  },
];

const videoDatas = [
  {
    type: 'Top Snap',
    ratio: '9:16',
    spec: '1080 x 1920',
    file_size: 'Max 1GB',
    file_type: 'MP4,MOV',
    length: '3 - 180 Seconds',
  },
  {
    type: 'Long Form',
    ratio: '-',
    spec: 'Min width 1080	',
    file_size: 'Max 1GB',
    file_type: 'MP4,MOV',
    length: 'Min 15 Seconds',
  },
];

const commonAttrs = ({ rowIndex }: { rowIndex: number}) => {
  // console.log(params);
  if (rowIndex === -1) {
    return {
      style: {
        borderRadius: 0,
      },
    };
  }
  return {};
};

export const LIMIT_TIPS = (
  <List
    title={'SnapChat Upload Requirements：'}
    disc= {true}
    list={[
      {
        title: 'Image',
        content: (
          <Table
            class={'w-[640px]'}
            bordered
            rowKey={'creative_type'}
            data={imageDatas}
            columns={[
              {
                colKey: 'creative_type',
                title: 'Creative Type',
                attrs: commonAttrs,
              },
              {
                colKey: 'ratio',
                title: 'Ratio',
                width: 60,
                attrs: commonAttrs,
              },
              {
                colKey: 'spec',
                title: 'Spec',
                width: 150,
                attrs: () => ({
                  style: {
                    wordBreak: 'break-word',
                    whiteSpace: 'break-spaces',
                  },
                }),
              },
              {
                colKey: 'file_size',
                title: 'File Size',
                attrs: commonAttrs,
                width: 100,
              },
              {
                colKey: 'file_type',
                title: 'File Type',
                attrs: commonAttrs,
                width: 100,
              },
            ]}
          />
        ),
      },
      {
        title: 'Video',
        content: (
          <Table
            class={'w-[640px]'}
            bordered
            rowKey={'type'}
            data={videoDatas}
            columns={[
              {
                colKey: 'type',
                title: 'Type',
                attrs: commonAttrs,
                width: 100,
              },
              {
                colKey: 'ratio',
                title: 'Ratio',
                width: 60,
                attrs: commonAttrs,
              },
              {
                colKey: 'spec',
                title: 'Spec',
                attrs: commonAttrs,
              },
              {
                colKey: 'file_size',
                title: 'File Size',
                width: 100,
                attrs: commonAttrs,
              },
              {
                colKey: 'file_type',
                title: 'File Type',
                width: 100,
                attrs: commonAttrs,
              },
              {
                colKey: 'length',
                title: 'Length',
                attrs: commonAttrs,
              },
            ]}
          />
        ),
      },
    ]}
  />
);

export function checkValid(record: UploadItem) {
  const validInfo: UploadMetaValidInfo = {
    metaWarnings: [],
    metaErrors: [],
  };
  console.log('upload snapchat', record);
  return validInfo;
}
