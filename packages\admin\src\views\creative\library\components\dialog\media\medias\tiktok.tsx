// see https://ads.tiktok.com/help/article?aid=9629
// see https://ads.tiktok.com/help/article?aid=9626
import { UploadItem, UploadMetaValidInfo } from '../interface';
import { formatFileSize } from 'common/utils/format';
import { joinSize } from '@/views/creative/library/components/dialog/media/medias/utils';
import List from './list.vue';

const IMAGE_TYPE = ['jpg', 'png'];
const IMAGE_TYPE_TEXT = IMAGE_TYPE.join('、');
const VIDEO_TYPE = ['mp4', 'mov', 'avi'];
const VIDEO_TYPE_TEXT = VIDEO_TYPE.join('、');
const VIDEO_RATIO = [9 / 16, 1, 16 / 9];
const VIDEO_MAX_SIZE = 500 * 1024 * 1024; // 500MB
const VIDEO_MAX_SIZE_TEXT = formatFileSize(VIDEO_MAX_SIZE);
const IMAGE_MAX_RECOMMEND_SIZE = 500 * 1024; // Recommended 500KB
const IMAGE_MAX_RECOMMEND_SIZE_TEXT = formatFileSize(IMAGE_MAX_RECOMMEND_SIZE);

function isValidVideoSize(width: number, height: number, ratio: number) {
  const imageRatio = width / height;
  return imageRatio >= ratio - 0.009 && imageRatio <= ratio + 0.009;
}

function checkVideo(width: number, height: number) {
  return (
    Object.values(VIDEO_RATIO)
      .map(ratio => isValidVideoSize(width, height, ratio))
      .filter(Boolean).length > 0
  );
}

export const LIMIT_TIPS = (
// @ts-ignore
  <List
    title={'Upload Conditions for TikTok Channel'}
    list={[
      'Image format limit: JPG, PNG',
      'Recommended image size limit: 500 KB',
      'Video format limit: MP4, MOV, AVI',
      'Video file size limit: 500 MB',
      'Video ratio: 9:16, 1:1, 16:9',
    ]}
  />
);

export function checkValid(record: UploadItem) {
  const validInfo: UploadMetaValidInfo = {
    metaWarnings: [],
    metaErrors: [],
  };

  const { format, size, width, height, mediaType } = record;
  const lowerCaseFormat = (format || '').toLowerCase();
  if (mediaType === 'image') {
    if (!IMAGE_TYPE.includes(lowerCaseFormat)) {
      validInfo.metaErrors.push(`Image format limited to: ${IMAGE_TYPE_TEXT}`);
    } else if (size > IMAGE_MAX_RECOMMEND_SIZE) {
      validInfo.metaWarnings.push(`Recommended image size limit is ${IMAGE_MAX_RECOMMEND_SIZE_TEXT}`);
    } else if (width === 0 || height === 0) {
      validInfo.metaWarnings.push('Image dimension information not parsed');
      return validInfo;
    }
  } else if (mediaType === 'video') {
    if (!VIDEO_TYPE.includes(lowerCaseFormat)) {
      validInfo.metaErrors.push(`Video format limited to: ${VIDEO_TYPE_TEXT}`);
    } else if (size > VIDEO_MAX_SIZE) {
      validInfo.metaErrors.push(`Video size (${formatFileSize(size)}) exceeds limit (${VIDEO_MAX_SIZE_TEXT})`);
    } else if (width === 0 || height === 0) {
      validInfo.metaWarnings.push('Video dimension information not parsed');
      return validInfo;
    } else if (!checkVideo(width, height)) {
      validInfo.metaWarnings.push(`Video dimensions (${joinSize({ width, height })}) do not meet the requirements`);
    }
  } else {
    validInfo.metaErrors.push('Only video or image can be uploaded');
  }
  return validInfo;
}
