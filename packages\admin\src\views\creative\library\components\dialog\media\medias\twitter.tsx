// see https://www.facebook.com/business/ads-guide/video
// see https://www.facebook.com/business/ads-guide/image
import { formatFileSize } from 'common/utils/format';
import { UploadItem, UploadMetaValidInfo } from '../interface';
import { joinSize } from '@/views/creative/library/components/dialog/media/medias/utils';
import List from './list.vue';

const IMAGE_TYPE = ['bmp', 'jpeg', 'jpg', 'png'];
const IMAGE_TYPE_TEXT = IMAGE_TYPE.join(', ');
const IMAGE_RATIO_RANGE = [1.91, 1]; // [1.91:1, 1:1]
const VIDEO_TYPE = ['mov', 'mp4'];
const VIDEO_TYPE_TEXT = VIDEO_TYPE.join(', ');
const VIDEO_RATIO_RANGE = [16 / 9, 1 / 1, 9 / 16].map(i => i.toFixed(2));
const VIDEO_MAX_SIZE = 512 * 1024 * 1024; // 4G
const VIDEO_MAX_SIZE_TEXT = formatFileSize(VIDEO_MAX_SIZE);
const VIDEO_SIZE = [720, 1280];
const IMAGE_MAX_SIZE = 5 * 1024 * 1024; // 3M
const IMAGE_MAX_SIZE_TEXT = formatFileSize(IMAGE_MAX_SIZE);
const VIDEO_MAX_DURATION = 140;
const VIDEO_MAX_DURATION_TEXT = '2m 20s (140s)';

export const LIMIT_TIPS = (
  <List
    title={'Twitter Upload Guidelines'}
    list={[
      'Image formats: bmp, jpeg, png',
      'Image aspect ratios: 1.91:1 or 1:1',
      'Recommended sizes: 800 * 418 px (1.91:1), 800 * 800 px (1:1)',
      'Minimum image width: 800px',
      'Max image size: 3MB',
      'Video formats: MP4, MOV',
      'Video aspect ratios: 16:9, 9:16, 1:1',
      'Supported sizes: 720*720 (1:1), 1280*720 (16:9), 720*1280 (9:16)',
      'Max video size: 512MB, Max duration: 2m 20s (140s)',
    ]}
  />
);

export function checkValid(record: UploadItem) {
  const validInfo: UploadMetaValidInfo = {
    metaWarnings: [],
    metaErrors: [],
  };

  const { size, duration, width, height, mediaType, format } = record;
  if (mediaType === 'image') {
    if (!IMAGE_TYPE.includes(format.toLowerCase())) {
      validInfo.metaErrors.push(`Supported image formats: ${IMAGE_TYPE_TEXT}`);
    } else if (size > IMAGE_MAX_SIZE) {
      validInfo.metaErrors.push(`Image size (${formatFileSize(size)}) exceeds limit (${IMAGE_MAX_SIZE_TEXT})`);
    } else if (width === 0 || height === 0) {
      validInfo.metaWarnings.push('Failed to parse image dimensions');
      return validInfo;
    } else if (width < 800) {
      validInfo.metaWarnings.push('Minimum image width: 800px');
    } else {
      const ratio = +(width / height).toFixed(2);
      if (ratio - IMAGE_RATIO_RANGE[0] !== 0 && ratio - IMAGE_RATIO_RANGE[1] !== 0) {
        validInfo.metaWarnings.push(`Image aspect ratio (${joinSize({ width, height })}) not supported`);
      }
    }
  } else if (mediaType === 'video') {
    if (!VIDEO_TYPE.includes(format.toLowerCase())) {
      validInfo.metaErrors.push(`Supported video formats: ${VIDEO_TYPE_TEXT}`);
    }
    if (size > VIDEO_MAX_SIZE) {
      validInfo.metaErrors.push(`Video size (${formatFileSize(size)}) exceeds limit (${VIDEO_MAX_SIZE_TEXT})`);
    } else if (width === 0 || height === 0) {
      validInfo.metaWarnings.push('Failed to parse video dimensions');
      return validInfo;
    } else if (duration > VIDEO_MAX_DURATION) {
      validInfo.metaErrors.push(`Video duration exceeds limit (${VIDEO_MAX_DURATION_TEXT})`);
    } else if (!VIDEO_SIZE.includes(width)) {
      validInfo.metaWarnings.push(`Unsupported video size (${joinSize({ width, height })})`);
    } else {
      const ratio = (width / height).toFixed(2);
      if (!VIDEO_RATIO_RANGE.includes(ratio)) {
        validInfo.metaErrors.push(`Video aspect ratio (${joinSize({ width, height })}) not supported`);
      }
      if (ratio === '1.00' && width !== 720) {
        validInfo.metaWarnings.push('Supported sizes: 1280*720, 720*1280, 720*720');
      }
    }
  } else {
    validInfo.metaErrors.push('Only image or video allowed');
  }

  return validInfo;
}
