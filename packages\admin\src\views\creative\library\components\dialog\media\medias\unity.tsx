// see https://ads.tiktok.com/help/article?aid=9629
// see https://ads.tiktok.com/help/article?aid=9626
import { UploadItem, UploadMetaValidInfo } from '../interface';
import { formatFileSize } from 'common/utils/format';
import { joinSize } from '@/views/creative/library/components/dialog/media/medias/utils';
import List from './list.vue';

const IMAGE_TYPE = ['jpg', 'png', 'gif'];
const IMAGE_TYPE_TEXT = IMAGE_TYPE.join('、');
const VIDEO_TYPE = ['mp4', 'mov', 'avi'];
const VIDEO_TYPE_TEXT = VIDEO_TYPE.join('、');
const VIDEO_RATIO = [9 / 16, 1, 16 / 9];
const VIDEO_MAX_SIZE = 100 * 1024 * 1024; // 500MB
const VIDEO_MAX_SIZE_TEXT = formatFileSize(VIDEO_MAX_SIZE);
const IMAGE_MAX_RECOMMEND_SIZE = 5 * 1024 * 1024; // Recommended 5M
const IMAGE_MAX_RECOMMEND_SIZE_TEXT = formatFileSize(IMAGE_MAX_RECOMMEND_SIZE);

function isValidVideoSize(width: number, height: number, ratio: number) {
  const imageRatio = width / height;
  return imageRatio >= ratio - 0.009 && imageRatio <= ratio + 0.009;
}

function checkVideo(width: number, height: number) {
  return (
    Object.values(VIDEO_RATIO)
      .map(ratio => isValidVideoSize(width, height, ratio))
      .filter(Boolean).length > 0
  );
}

export const LIMIT_TIPS = (
// @ts-ignore
  <List
    title={'Unity Upload Requirements'}
    disc={false}
    list={[
      {
        content: (
          <List
            title={'Video'}
            list={[
              'Duration: 30 seconds or less',
              'Format: H.264-encoded MP4',
              'Recommended File Size: 10MB',
              'Maximum File Size: 100MB',
              'Aspect Ratios: 9:16 (Portrait), 16:9 (Landscape)',
            ]}
          />
        ),
      },
      {
        content:
          (
            <List
              title={'Square End Card'}
              list={[
                'Dimensions: 800 × 800 (Square)',
                'Formats: PNG, JPEG, GIF',
                'Maximum File Size: 5MB',
              ]}
            />
          ),
      },
      {
        content: (
          <List
            title={'Playable'}
            list={[
              'Requirements: The store URL in the HTML must match the account it syncs with',
              'Maximum File Size: 5MB',
            ]}
          />
        ),
      },
      {
        content: (
          <List
            title={'End Card Pair (Currently not supported)'}
            list={[
              'Composition: Contains a portrait and a landscape end card image',
              'Dimensions: 600 × 800 (Portrait) + 800 × 600 (Landscape)',
              'Formats: PNG, JPEG, GIF',
              'Maximum File Size: 5MB',
            ]}
          />
        ),
      },
    ]}
  />
);

export function checkValid(record: UploadItem) {
  const validInfo: UploadMetaValidInfo = {
    metaWarnings: [],
    metaErrors: [],
  };

  const { format, size, width, height, mediaType } = record;
  const lowerCaseFormat = (format || '').toLowerCase();
  if (mediaType === 'image') {
    if (!IMAGE_TYPE.includes(lowerCaseFormat)) {
      validInfo.metaErrors.push(`Image format limited to: ${IMAGE_TYPE_TEXT}`);
    } else if (size > IMAGE_MAX_RECOMMEND_SIZE) {
      validInfo.metaWarnings.push(`Recommended image size limit is ${IMAGE_MAX_RECOMMEND_SIZE_TEXT}`);
    } else if (width === 0 || height === 0) {
      validInfo.metaWarnings.push('Image dimension information not parsed');
      return validInfo;
    }
  } else if (mediaType === 'video') {
    if (!VIDEO_TYPE.includes(lowerCaseFormat)) {
      validInfo.metaErrors.push(`Video format limited to: ${VIDEO_TYPE_TEXT}`);
    } else if (size > VIDEO_MAX_SIZE) {
      validInfo.metaErrors.push(`Video size (${formatFileSize(size)}) exceeds limit (${VIDEO_MAX_SIZE_TEXT})`);
    } else if (width === 0 || height === 0) {
      validInfo.metaWarnings.push('Video dimension information not parsed');
      return validInfo;
    } else if (!checkVideo(width, height)) {
      validInfo.metaWarnings.push(`Video dimensions (${joinSize({ width, height })}) do not meet the requirements`);
    }
  } else if (mediaType === 'other' && format === 'html') {
    return validInfo;
  } else {
    validInfo.metaErrors.push('Only video or image can be uploaded');
  }
  return validInfo;
}
