/* eslint-disable @typescript-eslint/naming-convention */

// 将后台返回的素材信息转换为UploadItem
import { UploadItem } from '@/views/creative/library/components/dialog/media/interface';
import { previewType } from 'common/utils/format';

export function parseUploadItem(record: any): UploadItem {
  const { material_ext = { video: {}, universal: {} }, asset_id, name } = record;
  const {
    video: { width, high, duration },
    universal: { size, format },
  } = material_ext;

  return {
    id: asset_id,
    name,
    format,
    size,
    duration,
    width,
    height: high,
    mediaType: previewType(format),
  };
}

export function joinSize({ width, height }: { width: string | number; height: string | number }) {
  return `${width}x${height}`;
}
