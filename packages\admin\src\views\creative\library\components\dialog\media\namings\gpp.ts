// see https://doc.weixin.qq.com/sheet/e3_AIQARgaEACcS3njMbkORk0wA9zypC?scode=AJEAIQdfAAo7ZelvYbAKIATQbdAFw

import { UploadItem, UploadNamingValidInfo } from '@/views/creative/library/components/dialog/media/interface';

const TYPES = ['V', 'P', 'U', 'S', 'Z'];
const TYPES_TEXT = TYPES.join(', ');
const SIZES = ['H', 'S', 'F'];
const SIZES_TEXT = SIZES.join(', ');
const FIELD_LEN = 8;

export const LIMIT_TABLE = [
  {
    type: 'Type',
    example: 'V',
    desc: 'V, P, U, S, Z represent video, image, UE4, store image, and real person respectively',
  },
  {
    type: 'Custom',
    example: 'Select Fashion Battle',
    desc: 'Material name',
  },
  {
    type: 'Gameplay',
    example: 'Fashion',
    desc: 'Gameplay represented by the material',
  },
  {
    type: 'Presentation',
    example: 'Packaging Editing',
    desc: 'Packaging editing, UE4 theater, UE4 gameplay, real person, KOL, etc.',
  },
  {
    type: 'Language',
    example: 'UN',
    desc: 'Language, use JP for Japanese, UN for universal',
  },
  {
    type: 'Size',
    example: 'H',
    desc: 'Material size, H, S, F represent horizontal, vertical, and square respectively',
  },
  {
    type: 'Phase',
    example: 'CBT',
    desc: 'Testing or promotion phase corresponding to material production',
  },
  {
    type: 'Date',
    example: '211116',
    desc: 'Delivery date',
  },
];

// e.g. V-Select Fashion Battle-Fashion-Packaging Editing-UN-H-CBT-211116
export function checkValid(record: UploadItem) {
  const validInfo: UploadNamingValidInfo = {
    namingWarnings: [],
  };

  const { name, width, height, mediaType } = record;
  const nameFields = name.split('.')[0].split('-');
  if (nameFields.length < FIELD_LEN) {
    validInfo.namingWarnings.push(`Should not have less than ${FIELD_LEN} fields`);
    return validInfo;
  }

  const [type, size, date] = nameFields;

  if (!TYPES.includes(type)) {
    validInfo.namingWarnings.push(`Type naming limited to: ${TYPES_TEXT}`);
  } else if (type === 'V' && mediaType !== 'video') {
    validInfo.namingWarnings.push('Type V should represent video');
  } else if (type === 'P' && mediaType !== 'image') {
    validInfo.namingWarnings.push('Type P should represent image');
  }

  if (!SIZES.includes(size)) {
    validInfo.namingWarnings.push(`Size naming limited to: ${SIZES_TEXT}`);
  } else if (size === 'H' && width < height) {
    validInfo.namingWarnings.push('Size H should represent horizontal');
  } else if (size === 'S' && width > height) {
    validInfo.namingWarnings.push('Size S should represent vertical');
  } else if (size === 'F' && width !== height) {
    validInfo.namingWarnings.push('Size F should represent square');
  }

  if (!/^(20)?\d{4,6}$/.test(date)) {
    validInfo.namingWarnings.push('Date is not standard');
  }

  return validInfo;
}
