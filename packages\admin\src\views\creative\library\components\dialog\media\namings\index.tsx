import { NamingType } from '../../../../define';
import * as gpp from './gpp';
import * as pubgm from './pubgm';
import { UploadItem } from '@/views/creative/library/components/dialog/media/interface';
import Table from 'tdesign-vue-next/es/table';
import ToolTips from 'tdesign-vue-next/es/tooltip';
import { upperFirst } from 'lodash-es';

const instanceMap = {
  gpp,
  pubgm,
};

function getInstance(media: NamingType) {
  return instanceMap[media];
}

// 返回true表示检查无问题，返回 namingWarnings: [] 表示存在错误内容
export function checkNamingValidInfo(type: NamingType, record: UploadItem) {
  console.log(type, record);
  // const instance = getInstance(type);
  return {
    namingWarnings: [],
  };
  // return instance.checkValid(record);
}

export function getLimitTips(type: NamingType) {
  const instance = getInstance(type);
  if (!instance) return null;
  return (
    <div class={'m-[10px]'}>
      <h3 class={'my-[10px] font-bold'}> { upperFirst(type)} Creative naming standard</h3>
      <Table
        class={'w-[600px]'}
        bordered
        data={instance.LIMIT_TABLE}
        rowKey={'type'}
        columns={[
          {
            colKey: 'type',
            title: 'Filed',
            width: '100px',
          },
          {
            colKey: 'example',
            title: 'Example',
            width: '100px',
          },
          {
            colKey: 'desc',
            title: 'Description',
            cell: (h: any, { row }: any) => row.desc
              .split('\n')
              .map((s: string, index: number) => (
                <ToolTips content={s} placement={'mouse'}>
                  <p class={'truncate'} key={index}>{s}</p>
                </ToolTips>
              )),
          },
        ]}
      />
    </div>
  );
}
