// see https://doc.weixin.qq.com/sheet/e3_AIQARgaEACcS3njMbkORk0wA9zypC?scode=AJEAIQdfAAo7ZelvYbAKIATQbdAFw

import { UploadItem, UploadNamingValidInfo } from '@/views/creative/library/components/dialog/media/interface';

const TYPES = ['V', 'P']; // P=Picture, V=Video
const TYPES_TEXT = TYPES.join(', ');
const SIZES = ['1', '2', '3']; // 1=Horizontal, 2=Vertical, 3=Square
const SIZES_TEXT = SIZES.join(', ');
const FIELD_LEN = 5;

export const LIMIT_TABLE = [
  {
    type: 'Sequence Number',
    example: 'UA00012',
    desc: `1. Sequentially arranged from 00001-99999
2. The same creative uses the same sequence number
3. RG for HQ version material, UA for UA output material, IF for regional influencer material
*If unsure about the sequence number of the project you are responsible for, follow the sequence of numbers not appearing in the material library, this number should be automatically generated by the system, supplier output can correspond to the language, other common errors are often inconsistent`,
  },
  {
    type: 'Project Name',
    example: 'PUBGM',
    desc: `1. Uppercase
2. Spaces allowed
3. The name of each project is aligned at the Kickoff and confirmed via email, any changes must be communicated via email
*If unsure about the project name you are responsible for, contact lucianali`,
  },
  {
    type: 'Material Format',
    example: 'P',
    desc: `1. If the material is a video, please fill in: V
2. If the material is an image, please fill in: P`,
  },
  {
    type: 'Size',
    example: '2',
    desc: `1. For horizontal material, 16:9, please fill in: 1
2. For vertical material, 9:16, please fill in: 2
3. For square material, 1:1, please fill in: 3
For non-standard sizes, please specify the actual size (e.g., 600x300)`,
  },
  {
    type: 'Language',
    example: 'EG',
    desc: 'Use uppercase language code',
  },
];

// e.g. UA00012-PUBGM-P-2-EG-TENCENT
export function checkValid(record: UploadItem) {
  const validInfo: UploadNamingValidInfo = {
    namingWarnings: [],
  };

  const { name, width, height, mediaType } = record;
  const nameFields = name.split('.')[0].split('-');
  if (nameFields.length < FIELD_LEN) {
    validInfo.namingWarnings.push(`Should not have less than ${FIELD_LEN} fields`);
    return validInfo;
  }

  const [seq, project, type, size] = nameFields;

  if (!/^[A-Z]{2}\d{5}$/.test(seq)) {
    validInfo.namingWarnings.push('Sequence number is not standard');
  }

  if (!/^[A-Z0-9 ]+$/.test(project)) {
    validInfo.namingWarnings.push('Project name should be uppercase letters');
  }

  if (!TYPES.includes(type)) {
    validInfo.namingWarnings.push(`Type naming limited to: ${TYPES_TEXT}`);
  } else if (type === 'V' && mediaType !== 'video') {
    validInfo.namingWarnings.push('Type V should represent video');
  } else if (type === 'P' && mediaType !== 'image') {
    validInfo.namingWarnings.push('Type P should represent image');
  }

  if (!SIZES.includes(size) && !/^\d+x\d+$/.test(size)) {
    validInfo.namingWarnings.push(`Size naming limited to ${SIZES_TEXT} or format as actual size (e.g., 600x300)`);
  } else if (size === '1' && width < height) {
    validInfo.namingWarnings.push('Size 1 should represent horizontal');
  } else if (size === '2' && width > height) {
    validInfo.namingWarnings.push('Size 2 should represent vertical');
  } else if (size === '3' && width !== height) {
    validInfo.namingWarnings.push('Size 3 should represent square');
  }

  return validInfo;
}
