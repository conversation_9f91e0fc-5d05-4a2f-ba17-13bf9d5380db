<template>
  <t-tooltip :content="paths.join(' > ')">
    <div class="flex items-center cursor-pointer">
      <div
        v-for="(path, index) in paths"
        :key="index"
        class="flex items-center mr-2"
      >
        <template v-if="index < 1 || index >= lastIndex - 1">
          <FolderIcon class="mr-[4px]" />
          <Text
            :content="path"
            overflow
            class="max-w-[80px] inline-block"
          />
          <i v-if="index !== lastIndex" class="mx-[4px]"> > </i>
        </template>
        <template v-else-if="index === 1 && index !== lastIndex">
          <Text
            class="font-bold"
            content="···"
          />
          <i class="mx-[4px]"> > </i>
        </template>
      </div>
    </div>
  </t-tooltip>
</template>
<script setup lang="ts">
import { computed } from 'vue';
import { FolderIcon } from 'tdesign-icons-vue-next';
import Text from 'common/components/Text';

const props = defineProps({
  data: {
    type: Object,
    default: () => {
    },
  },
});

const paths = computed<string[]>(() => {
  if (props.data.name) {
    return props.data.full_path_name?.split(',').concat([props.data.name]);
  }
  return [];
});

const lastIndex = computed(() => paths.value.length - 1);

</script>
<style scoped lang="scss">

</style>
