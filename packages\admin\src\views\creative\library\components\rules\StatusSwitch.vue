<script setup lang="ts">
import { useLoading } from 'common/compose/loading';
import { ref, watch } from 'vue';
import { AutomaticSyncTaskRuleStatus, changeAutomaticSyncTaskRuleStatus } from 'common/service/creative/rules/config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useTips } from 'common/compose/tips';
import { useStatusSwitchDialog } from '../dialog/StatusSwitchDialog';

const props = defineProps({
  value: {
    type: Boolean,
    default: () => false,
  },
  id: {
    type: String,
    default: () => '0',
  },
  applyTimeOffset: {
    type: Number,
    default: () => 0,
  },
  lastEditTime: {
    type: String,
    default: () => '',
  },
  applyTime: {
    type: String,
    default: () => '',
  },
});

const emits = defineEmits(['gotoEditRule', 'changeStatusSuccess']);

const switchValue = ref(props.value);
const { success, err } = useTips();
watch(() => props.value, (value) => {
  switchValue.value = value;
});

const confirmFunc = async () => {
  showLoading();
  const res = await changeAutomaticSyncTaskRuleStatus({
    game_code: gameStore.gameCode,
    id: props.id,
    status: switchValue.value ? AutomaticSyncTaskRuleStatus.DISABLED : AutomaticSyncTaskRuleStatus.ENABLE,
  });
  if (res.result.error_code === 0) {
    switchValue.value = !switchValue.value;
    await success('Change Success');
    emits('changeStatusSuccess');
  } else {
    await err(res.result.error_message);
  }
  hideLoading();
};

const { isLoading, showLoading, hideLoading } = useLoading();
const gameStore = useGlobalGameStore();
const emitChange = () => {
  let onText = 'New files in the selected folders will sync automatically while this rule is on. Enable this rule?';
  const offText = 'Turning off this rule will stop new files form syncing. Stop this rule?';
  const boldText: string[] = [];
  if (!switchValue.value && props.applyTimeOffset <= 6) {
    boldText.push(...[`${props.lastEditTime} (UTC+0)`, `${props.applyTime} (UTC+0)`]);
    onText = 'This rule was paused on [bold]. \nFiles uploaded to cloud drive from (inclusive) [bold] in the selected folders will sync automatically.\nThis includes all future uploads while the rule is on.';
  }
  if (!switchValue.value && props.applyTimeOffset > 6) {
    boldText.push(...[`${props.lastEditTime} (UTC+0)`]);
    onText = 'This rule was paused on [bold]. \nOnly assets uploaded to cloud drive within 6 months are supported.Please update [Apply to Assets] setting to continue.';
  }
  useStatusSwitchDialog({
    currStatus: switchValue.value,
    bodyText: switchValue.value ? offText : onText,
    boldText,
    confirmFunc,
    editFunc: () => {
      emits('gotoEditRule');
    },
    cancleFunc: () => {},
  }, props.applyTimeOffset);
};

</script>

<template>
  <t-switch :loading="isLoading" :value="switchValue" @change="emitChange" />
</template>

<style scoped lang="scss">

</style>
