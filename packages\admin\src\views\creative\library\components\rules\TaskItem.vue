<template>
  <div class="w-full px-[16px] py-[24px] bg-[#F4F5F6] rounded-default space-y-[16px]">
    <h4 class="flex items-center justify-between">
      <span>Media {{ index + 1 }}</span>
      <span
        class="text-brand ml-[10px] cursor-pointer hover:opacity-80 active:opacity-50"
        @click="deleteHandle"
      >
        Delete
      </span>
    </h4>
    <t-space direction="vertical" class="w-full">
      <t-form-item
        label="Media"
        :name="`medias[${index}].media`"
        label-align="top"
        :rules="[{ required: true, type: 'error' }]"
      >
        <div class="w-full flex items-center space-x-[10px]">
          <t-select
            v-model="data.media"
            class="w-[200px]"
            :options="optionsMediaList"
          />
          <t-popup
            v-if="data.media"
            destroy-on-close
            show-arrow
            overlay-style
            :content="getLimitTips(data.media)"
            placement="right"
            trigger="hover"
          >
            <ErrorCircleIcon class="cursor-pointer hover:opacity-50" />
          </t-popup>
        </div>
      </t-form-item>
      <t-form-item
        label="Account"
        :name="`medias[${index}].accounts`"
        class="w-full accounts-validate"
        label-align="top"
        :help="disabledAccountsText"
        :rules="[
          { validator: checkAccountsValid },
          { required: true, type: 'error' },
        ]"
      >
        <t-select
          v-model="data.accounts"
          class="w-[100%]"
          placeholder="Please select account"
          :loading="isLoading"
          :options="accountsList"
          :min-collapsed-num="3"
          :multiple="data.media !== 'AppLovin'"
        />
      </t-form-item>
      <template v-if="data.media === 'Unity'">
        <t-form-item
          label="Creative Type"
          :name="`medias[${index}].creativeType`"
          label-align="top"
          required-mark
        >
          <t-select
            :value="1"
            :options="creativeTypeList"
          />
        </t-form-item>
        <t-form-item
          label="Language"
          :name="`medias[${index}].language`"
          :rules="[{ required: true, type: 'error' }]"
          label-align="top"
        >
          <t-select
            v-model="data.language"
            :options="languageList"
            clearable
            filterable
          />
        </t-form-item>
      </template>
      <template v-if="data.media === 'AppLovin'">
        <t-form-item
          label="Campaign"
          :name="`medias[${index}].campaign_id`"
          class="w-full"
          label-align="top"
          :show-error-message="false"
          :rules="[{ required: true, type: 'error' }]"
          :status="typeof currentCampaign.status === 'undefined'? 'error': ''"
          :tips="typeof currentCampaign.status === 'undefined'?'Please contact AiX to configure draft campaign.':''"
        >
          <div class="w-full flex items-center space-x-[10px]">
            <p
              class="h-[36px] border-solid border-[1px] border-[#DCDFE8] text-black-disabled leading-[36px]
            bg-[#F0F1F6] rounded-default py-[7px] px-[12px] flex items-center w-[calc(100%-24px)]"
            >
              <template v-if="typeof currentCampaign.status !== 'undefined'">
                <t-tooltip
                  :content="`[Status: ${currentCampaign.status?'Active':'Pause'}] ${currentCampaign.campaign_name}|${currentCampaign.campaign_id}`"
                >
                  <p class="w-full flex flex-row items-center">
                    <span
                      class="w-[6px] h-[6px] rounded mr-[4px] inline-block"
                      :class="[!currentCampaign.status?'bg-[#FF9824]': 'bg-[#4CAF50]']"
                    />
                    <span class="cursor-not-allowed truncate flex-1">
                      {{ currentCampaign.campaign_name }} | {{ currentCampaign.campaign_id }}
                    </span>
                  </p>
                </t-tooltip>
              </template>
            </p>
            <t-popup
              destroy-on-close
              show-arrow
              overlay-style
              content="Create a new creative set with the selected assets."
              trigger="hover"
            >
              <ErrorCircleIcon class="cursor-pointer hover:opacity-50" />
            </t-popup>
          </div>
        </t-form-item>
        <t-form-item
          label="Creative Set Name"
          :name="`medias[${index}].creative_set_name`"
          class="w-full"
          label-align="top"
          :rules="[{ required: true, type: 'error' }]"
        >
          <t-input v-model="data.creative_set_name" />
        </t-form-item>
        <t-form-item
          label="Language"
          :name="`medias[${index}].language`"
          class="w-full"
          label-align="top"
          :rules="[{ required: true, type: 'error' }]"
        >
          <t-select
            v-model="data.language"
            :options="appLovinLanguages"
            filterable
            placeholder="Please select language"
          />
        </t-form-item>
        <t-form-item
          :name="`medias[${index}].countries`"
          class="w-full countries-container"
          :class="{'hide-content-container': !isContriesShow}"
          label-align="top"
          required-mark
          :rules="[{ validator: checkCountriesValid, trigger: 'change' }]"
        >
          <template #label>
            <div
              class="cursor-pointer hover:opacity-50 inline-block contries-header"
              @click="isContriesShow = !isContriesShow"
            >
              <span>
                Countries
              </span>
              <svg-icon
                name="arrow"
                size="12"
                class="transition inline-block ml-[8px] arrow-icon"
                :class="isContriesShow ? 'rotate-[180deg]' : 'rotate-[0deg]'"
              />
            </div>
          </template>
          <div v-if="isContriesShow" class="flex flex-col space-y-[8px] w-full">
            <t-radio-group v-model="isApplovinSelectAllContries" class="flex flex-col space-x-[8px]">
              <t-radio :value="true">All Countries</t-radio>
              <t-radio :value="false">Specific Countries</t-radio>
            </t-radio-group>
            <t-tree-select
              v-if="!isApplovinSelectAllContries"
              v-model="data.countries"
              class="w-full"
              :data="appLovinContries"
              filterable
              multiple
              clearable
              placeholder="Please select countries"
            />
          </div>
        </t-form-item>
      </template>
    </t-space>
  </div>
</template>

<script setup lang="ts">
import { tryOnMounted, useVModel } from '@vueuse/core';
import { MediaType, TSelectItem, TTaskItem } from '@/views/creative/library/define';
import {
  creativeTypeList,
  languageList,
  MEDIA_LIST,
} from '@/views/creative/library/components/dialog/media/config';
import { getLimitTips } from '@/views/creative/library/components/dialog/media/medias';
import { ErrorCircleIcon } from 'tdesign-icons-vue-next';
import { computed, ref, watch } from 'vue';
import { useLoading } from 'common/compose/loading';
import { getAccountList } from 'common/service/media_account/get';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useTips } from 'common/compose/tips';
import { getAppLovinContries } from '@/views/creative/library/components/dialog/media/applovin_contries';
import { getAppLovinLanguages } from '@/views/creative/library/components/dialog/media/applovin_languages';
import { SvgIcon } from 'common/components/SvgIcon';

const props = defineProps<{
  modelValue: TTaskItem;
  checkableMedia: string[];
  index: number;
}>();

const emit = defineEmits(['update:modelValue', 'delete']);

const optionsMediaList = computed(() => {
  const baseMediaList: { label: string, value: string, disabled?: boolean, title?: string }[] = MEDIA_LIST.value
    .filter(i => props.checkableMedia.includes(i))
    .map(i => ({ label: i, value: i }));
  if (!baseMediaList.some(i => i.label === 'AppLovin')) {
    baseMediaList.push({
      label: 'AppLovin',
      value: 'AppLovin',
      disabled: true,
      title: 'Media selected, Applovin not applicable.',
    });
  }
  return baseMediaList;
});

const deleteHandle = () => {
  emit('delete', props.modelValue);
};
type TCampaign = {
  campaign_id?: string;
  campaign_name?: string;
  status?: boolean;
};
const accountsList = ref<TSelectItem[]>([]);
const campaignList = ref<Record<string, TCampaign>>({});
const currentCampaign = computed<TCampaign>(() => {
  if (data.value.media === 'AppLovin' && !Array.isArray(data.value.accounts)) {
    return campaignList.value[data.value.accounts as string] || {};
  }
  return {};
});
const { isLoading, showLoading, hideLoading } = useLoading();
const isApplovinSelectAllContries = ref(true);

const data = useVModel(props, 'modelValue', emit);
const gameStore = useGlobalGameStore();
const { err: errTips } = useTips();
const disabledAccountsList = ref<string[]>([]);
const disabledAccountsText = computed(() => {
  if (disabledAccountsList.value.length > 0) {
    return `${disabledAccountsList.value.join(',')} These accounts have expired. Please select other accounts.`;
  }
  return '';
});

const checkAccountsValid = () => {
  if (disabledAccountsList.value.length > 0) {
    return {
      result: true,
      message: disabledAccountsText.value,
      type: 'warning',
    };
  }
  return {
    result: true,
    message: '',
  };
};


function setDataAccounts(accounts: string[]) {
  if (data.value.media === 'AppLovin') {
    // applovin 是单选
    [data.value.accounts] = accounts;
  } else {
    data.value.accounts = accounts;
  }
}


function updateAccountsList(media: string) {
  showLoading();
  if (!media) {
    accountsList.value = [];
    setDataAccounts([]);
    hideLoading();
    return;
  }
  getAccountList({
    gameCode: gameStore.gameCode,
    media: media as MediaType,
  })
    .then((res) => {
      if (res.length > 0) {
        const base: TSelectItem[] = res.map((item: any) => ({
          label: item.label,
          value: item.value,
          disabled: item.isSuspend,
        }));
        // applovin 是单选
        if (data.value.media === 'AppLovin') {
          accountsList.value = base;
          // 如果已经有账号，如果当前账号存在，那么就用当前账号，如果当前账号已经不存在了，则使用默认账号
          const defaultAccount = base
            .some(item => item.value === data.value.accounts) ? data.value.accounts as string : res[0].value;
          setDataAccounts([defaultAccount]);
          const temp: any = {};
          res.forEach((item: any) => {
            temp[item.value] = item.upload_asset_campaign;
          });
          campaignList.value = temp;
        } else {
          // 可用的账号
          accountsList.value = base.concat([
            {
              label: 'All Accounts',
              checkAll: true,
            },
          ]);
          const newAccounts: string[] = [];
          // 不可用的账号
          const newDisabledAccounts: string[] = [];

          // 遍历当前已有的账号，区分账号类型
          (data.value.accounts as string[]).forEach((account) => {
            const item = accountsList.value.find(i => i.value === account);
            if (item?.disabled) {
              newDisabledAccounts.push(item.label);
            }
            if (item && !item.disabled && item.value) {
              newAccounts.push(item.value);
            }
          });

          // 全部可用的帐号
          const validAccountsList = accountsList.value.filter(i => !i.disabled && i.value)
            .map(i => i.value!,
            ) || [];
          setDataAccounts(newAccounts);
          disabledAccountsList.value = newDisabledAccounts;
          // 如果当前选中列表中已经没有一个可用的账号，那就选中所有可以选择的列表
          if (data.value.accounts.length === 0) {
            setDataAccounts(validAccountsList);
          }
        }
        if (['Unity', 'AppLovin'].includes(data.value.media)) {
          data.value.language = 'en';
        } else {
          data.value.language = undefined;
        }
      } else {
        accountsList.value = [];
        setDataAccounts([]);
      }
      hideLoading();
    })
    .catch((err) => {
      console.error('get Media accounts', err);
      errTips('get media accounts list err, please try again later!');
      hideLoading();
    });
}

const appLovinContries = getAppLovinContries();
const appLovinLanguages = getAppLovinLanguages();

tryOnMounted(() => {
  if (data.value.media) {
    updateAccountsList(data.value.media);
  }
});

watch(currentCampaign, (value) => {
  if (value) {
    data.value.campaign_id = value.campaign_id;
  }
});

watch(
  () => isApplovinSelectAllContries.value,
  (value) => {
    if (!value) {
      data.value.countries = [];
    }
  },
);

const isContriesShow = ref(false);

const checkCountriesValid = (val) => {
  if (val?.length <= 0 && !isApplovinSelectAllContries.value) {
    return { result: false, message: 'Please select at least one country' };
  }
  return { result: true, message: '' };
};

watch(
  () => data.value.media,
  (value) => {
    updateAccountsList(value);
  },
);
</script>
<style scoped lang="scss">
h4 {
  color: #000;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

:deep(.t-form__label--left) {
  color: rgba(0, 0, 0, 0.4);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
}

:deep(.t-is-error) {
  .t-input__extra {
    color: var(--td-error-color) !important;
  }
}

.accounts-validate :deep(.t-input__help) {
  color: var(--td-error-color) !important;
}

.countries-container.hide-content-container {
  :deep(.t-form__controls) {
    height: 0;
    min-height: 0;
  }
}
</style>
