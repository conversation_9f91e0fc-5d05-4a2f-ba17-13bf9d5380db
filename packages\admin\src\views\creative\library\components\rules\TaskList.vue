<template>
  <div
    ref="scrollContainer"
    v-auto-animate
    class="w-full space-y-[16px]"
  >
    <TaskItem
      v-for=" (item, index) in taskList"
      :key="item.id"
      v-model="taskList[index]"
      :index="index"
      :checkable-media="checkableMedia"
      @delete="deleteItem"
    />
    <template v-if="isAppLovinSelected">
      <t-tooltip content="AppLovin selected, muti-media sync not applicable.">
        <t-button
          :disabled="disabledAdd"
          @click="addTask"
        >
          Add Media
        </t-button>
      </t-tooltip>
    </template>
    <template v-else>
      <t-button
        :disabled="disabledAdd"
        @click="addTask"
      >
        Add Media
      </t-button>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue';
import TaskItem from './TaskItem.vue';
import { MediaType } from '@/views/trade/ads_creation/common/template/type';
import { TTaskItem } from '@/views/creative/library/define';
import { useId } from '@/views/creative/library/compose/useId';
import { difference } from 'lodash-es';
import { useVModel } from '@vueuse/core';

const props = defineProps<{
  modelValue: TTaskItem[],
  totalMediaList?: MediaType[]
}>();
const emit = defineEmits(['scrollToBottom', 'update:modelValue']);

const taskList = useVModel(props, 'modelValue', emit);
const totalMediaList: MediaType[] = props.totalMediaList || ['Google', 'Facebook', 'TikTok', 'Twitter', 'Unity', 'Snapchat', 'AppLovin'];
const checkableMedia = ref<MediaType[]>(totalMediaList);

const scrollContainer = ref();

const isAppLovinSelected = computed(() => taskList.value.some(item => item.media === 'AppLovin'));

const disabledAdd = computed(() => taskList.value.length >= totalMediaList.length || isAppLovinSelected.value);

const addTask = () => {
  taskList.value.push({
    id: Date.now() + useId(taskList.value?.length || 0),
    media: '',
    accounts: [],
  });
  nextTick()
    .then(() => {
      setTimeout(() => {
        emit('scrollToBottom');
      }, 300);
    });
};

const deleteItem = (data: TTaskItem) => {
  taskList.value.splice(taskList.value.indexOf(data), 1);
};

watch(() => taskList.value, (val) => {
  const selectedMedia = val.map(i => i.media);
  let unSelectedArr = [''];
  if (selectedMedia.some(i => i !== 'AppLovin' && i !== '') && selectedMedia.length > 1) {
    unSelectedArr = ['AppLovin'];
  }
  checkableMedia.value = difference(totalMediaList, selectedMedia, unSelectedArr) as MediaType[];
}, { deep: true });

</script>


<style scoped lang="scss">

</style>
