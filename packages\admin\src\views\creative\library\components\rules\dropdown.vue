<script setup lang="ts">
import { deleteAutomaticSyncTaskRule } from 'common/service/creative/rules/config';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useTips } from 'common/compose/tips';
import { useConfirmDialog } from 'common/compose/useDialog';
import { useGoto } from '@/router/goto';

const gameStore = useGlobalGameStore();
const { success, err } = useTips();
const { gotoCreativeAutoRulesDetail, gotoCreativeTask } = useGoto();
const emits = defineEmits(['delete']);

const props = defineProps({
  name: {
    type: String,
    default: () => '',
  },
  id: {
    type: String,
    default: () => '0',
  },
});

const options = [
  { content: 'Duplicate', value: 1 },
  { content: 'Delete', value: 2 },
  { content: 'Task History', value: 3 },
];

const duplicate = async () => {
  await gotoCreativeAutoRulesDetail({
    id: 'new',
    copy: props.id,
  });
};

const deleteRule = () => {
  useConfirmDialog({
    theme: 'warning',
    header: () => 'Please Confirm',
    body: `Do you want to delete ${props.name}(#${props.id}) rule ?`,
    onConfirm: async () => {
      const res = await deleteAutomaticSyncTaskRule({
        game_code: gameStore.gameCode,
        id: props.id,
      });
      if (res.result.error_code !== 0) {
        return err(res.result.error_message);
      }
      emits('delete');
      return success('Delete Success');
    },
  });
};

const gotoTaskHistory = () => {
  gotoCreativeTask(false, {
    ruleId: props.id,
    taskType: 'auto',
  });
};

const opMap: Record<number, () => void | Promise<void>> = {
  1: duplicate,
  2: deleteRule,
  3: gotoTaskHistory,
};

const clickHandler = (data: { content: string; value: number }) => {
  opMap[data.value]();
};
</script>

<template>
  <t-dropdown
    :options="options"
    trigger="click"
    @click="clickHandler"
  >
    <t-button
      variant="text"
      theme="primary"
    >
      More
      <template #suffix>
        <t-icon
          name="chevron-down"
          size="16"
        />
      </template>
    </t-button>
  </t-dropdown>
</template>

<style scoped lang="scss"></style>
