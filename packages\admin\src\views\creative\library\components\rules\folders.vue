<template>
  <div class="h-[466px] w-full">
    <ResizeableColumns
      class="border-solid border-[#DEDEDE] rounded-small border-[1px] overflow-hidden"
      :width="560"
      :min-width="250"
      :max-width="700"
    >
      <template #left>
        <div class="h-full px-[16px] pt-[16px]">
          <t-select-input
            :value="selectValue"
            :popup-visible="popupVisible"
            :popup-props="{ overlayInnerStyle: { padding: '6px' } }"
            placeholder="Search"
            allow-input
            clearable
            @input-change="onInputChange"
            @popup-visible-change="onPopupVisibleChange"
          >
            <template #panel>
              <div class="flex w-full items-center justify-center h-[300px]">
                <t-loading v-if="isLoading" size="small" text="Loading folders" />
                <template v-else>
                  <t-list
                    v-if="options.length"
                    :async-loading="searchNextLoading"
                    class="w-full h-[300px]"
                    @scroll="scrollHandler"
                  >
                    <t-list-item
                      v-for="i in options"
                      :key="i"
                      class="hover:bg-gray-200 cursor-pointer rounded-default"
                      @click="() => onOptionClick(String(i.value))"
                    >
                      {{ i.name }}
                    </t-list-item>
                  </t-list>
                  <div v-else>
                    <DataEmpty>
                      <template #content>
                        {{
                          selectValue?.length && selectValue?.length > 0
                            ? 'Data Empty'
                            : 'Please input some for search'
                        }}
                      </template>
                    </DataEmpty>
                  </div>
                </template>
              </div>
            </template>
            <template #suffixIcon>
              <search-icon :style="{ cursor: 'pointer' }" />
            </template>
          </t-select-input>
          <FolderTree
            v-model="allChecked"
            v-model:path-name="checkedPath"
          />
        </div>
      </template>
      <template #right>
        <div
          v-auto-animate
          class="bg-[#F4F5F6] h-full border-solid border-[#DDD] border-[1px] px-[23px] pt-[16px]"
        >
          <div class="h-[22px] flex items-center justify-between">
            <div class="flex items-center space-x-[8px]">
              <span> Selected: {{ allChecked?.length || 0 }}</span>
              <t-tooltip
                content="Sync files in selected folders and all subfolders( including future subfolders )"
              >
                <HelpCircleIcon class="cursor-pointer" />
              </t-tooltip>
            </div>
            <t-button variant="text" theme="primary" @click="reset">
              Clear All
            </t-button>
          </div>
          <div
            v-if="checkedDetailList.length > 0"
            v-auto-animate
            class="space-y-[8px] overflow-y-auto py-[16px] h-[calc(100%-22px)] narrow-scrollbar"
          >
            <div
              v-for="(item, index) in checkedDetailList"
              :key="index"
              class="bg-white rounded-default px-[15px] py-[11px] flex justify-between items-center
                     hover:bg-gray-200"
            >
              <PathDisplay :data="item" />
              <span
                class="cursor-pointer text-brand hover:opacity-75 active:opacity-50"
                @click="()=> allChecked.splice(allChecked.indexOf(item.id), 1)"
              >
                Delete
              </span>
            </div>
          </div>
          <div v-else class="w-full h-[calc(100%-22px)] flex items-center justify-center">
            <div>
              <p class="text-[100px] text-center leading-normal">👈</p>
              <p class="empty-tips-text">Please select folders on the left</p>
            </div>
          </div>
        </div>
      </template>
    </ResizeableColumns>
  </div>
</template>

<script setup lang="tsx">
import ResizeableColumns from 'common/components/Layout/ResizeableColumns.vue';
import { HelpCircleIcon, SearchIcon } from 'tdesign-icons-vue-next';
import { ref, computed } from 'vue';
import { useVModel } from '@vueuse/core';
import FolderTree from '@/views/creative/library/components/FolderTree.vue';
import PathDisplay from '@/views/creative/library/components/rules/PathDisplay.vue';
import { SelectInputProps } from 'tdesign-vue-next';
import DataEmpty from '@/components/nullable/DataEmpty.vue';
import { useLoading } from 'common/compose/loading';
import { directorySearch } from 'common/service/creative/rules/config';
import { getPathItem, setPathMap } from '@/views/creative/library/compose/folder/init-folder';

const props = defineProps<{
  modelValue: string[]
  detailList: any[]
}>();
const emit = defineEmits(['update:modelValue']);

const allChecked = useVModel(props, 'modelValue', emit);
const reset = () => allChecked.value = [];
const checkedPath = ref<{ id: string, path: string[] }[]>([]);

const popupVisible = ref(false);
const selectValue = ref<string>();
const options = ref<{ name: string, value: string }[]>([]);

const pageSize = 30;
const pageNum = ref(0);
const isEnd = ref(false);

const onOptionClick = (item: string) => {
  selectValue.value = '';
  popupVisible.value = false;
  if (!allChecked.value.includes(item)) {
    allChecked.value.push(item);
  }
};
const { isLoading, showLoading, hideLoading } = useLoading();

const updateDirectoryList = async (keyword?: string, showLoadingTag = true) => {
  if (!keyword) return { dirs: [], total: 0 };
  showLoadingTag && showLoading();
  const list = await directorySearch({
    name: keyword,
    page: pageNum.value,
    page_size: pageSize,
  });
  pageNum.value += 1;
  setPathMap(list.dirs);
  showLoadingTag && hideLoading();
  return list;
};

const mapDir = (i: any) => ({
  name: `${i.full_path_name.replaceAll(',', ' > ')} > ${i.name}`,
  value: i.id,
});

const onInputChange: SelectInputProps['onInputChange'] = async (keyword) => {
  selectValue.value = keyword;
  pageNum.value = 0;
  isEnd.value = false;
  const { dirs } = await updateDirectoryList(keyword);
  options.value = dirs.map(mapDir);
};

const searchNextLoading = ref<'loading' | ''>('');

const onPopupVisibleChange: SelectInputProps['onPopupVisibleChange'] = (val) => {
  popupVisible.value = val;
};

let requestLock = false;

const scrollHandler = ({ scrollBottom }: { scrollBottom: number }) => {
  if (requestLock) {
    return;
  }
  if (scrollBottom < 100 && scrollBottom > 50 && !isEnd.value) {
    searchNextLoading.value = 'loading';
    requestLock = true;
    updateDirectoryList(String(selectValue.value), false)
      .then(({ dirs, total }) => {
        options.value = options.value.concat(dirs.map(mapDir));
        searchNextLoading.value = '';
        if (dirs.length + (pageNum.value - 1) * pageSize > total) {
          isEnd.value = true;
        }
      })
      .finally(() => {
        requestLock = false;
      });
  }
};


const getItemByDetail = (id: string) => {
  const map = getPathItem(id);
  if (Object.keys(map).length > 0) return map;
  const listItem = props.detailList.find(i => i.id === id);
  if (listItem) return listItem;
  return {};
};

const checkedDetailList = computed(() => allChecked.value.map(i => getItemByDetail(i)));

</script>

<style scoped lang="scss">
.empty-tips-text {
  color: #000;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  opacity: .4;
}


</style>
