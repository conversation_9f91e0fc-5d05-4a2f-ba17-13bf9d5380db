import { TreeProps } from 'tdesign-vue-next';
import { Icon } from 'tdesign-icons-vue-next';

export const icon: TreeProps['icon'] = (h, node) => {
  let name = 'arrow-left';
  // node.children is undefined on some cases
  if (node.getChildren?.(false)) {
    if (node.expanded) {
      name = 'arrow-down';
      if (node.loading) {
        name = 'loading';
      }
    } else {
      name = 'folder';
    }
  }
  return <Icon name={name} />;
};
