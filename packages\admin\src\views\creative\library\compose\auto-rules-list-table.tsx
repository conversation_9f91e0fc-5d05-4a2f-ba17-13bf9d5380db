import { ITableCols } from 'common/components/table/type';
import { ref } from 'vue';
import { Button, Tooltip, Space } from 'tdesign-vue-next';
import { SvgIcon } from 'common/components/SvgIcon';
import { useGoto } from '@/router/goto';
import DropDownList from '../components/rules/dropdown.vue';
import StatusSwitch from '@/views/creative/library/components/rules/StatusSwitch.vue';
import { InfoCircleIcon } from 'tdesign-icons-vue-next';
import { monthOffset } from '../utils';
import dayjs from 'dayjs';

export function useAutoRulesTable({ updateList }: any) {
  const { gotoCreativeAutoRulesDetail } = useGoto();
  const cols: ITableCols[] = [
    {
      colKey: 'switch',
      title: 'On/Off',
      cell: (h, { row }) => (
        <StatusSwitch
          value={Boolean(row.switch)}
          id={row.id}
          applyTimeOffset={monthOffset(row.start_cloud_upload_time, dayjs(new Date()).format('YYYY-MM-DD'))}
          lastEditTime={row.update_time}
          applyTime={row.start_cloud_upload_time}
          onGotoEditRule={() => gotoCreativeAutoRulesDetail({ id: row.id })}
          onChangeStatusSuccess={() => updateList()}
        />
      ),
    },
    {
      title: 'Rule Name',
      colKey: 'rule_name',
      ellipsis: {
        theme: 'light',
        placement: 'bottom',
      },
    },
    {
      title: 'Folder',
      colKey: 'folder',
      cell: (h, { row }) => {
        const list = () => (<ul>
          {row.folders.slice(0, 5).map((i: string) => (<li>{i}</li>))
            .concat([row.folders.length > 6 ? (<li>...</li>) : null])}
        </ul>);
        return (
        <Tooltip content={list}>
          <div
            class="w-[24px] h-[24px] rounded-small bg-[#E5EDFD] text-[#5086F3] text-center cursor-pointer"
          >
            {row.folders.length}
          </div>
        </Tooltip>
        );
      },
    },
    {
      title: 'Asset Type',
      colKey: 'type',
    },
    {
      title: () => <Tooltip content="Apply to assets uploaded to the cloud drive from shown date (inclusive).">
        <span class={'mr-[8px]'}>Apply to Assets</span>
        <InfoCircleIcon />
      </Tooltip>,
      colKey: 'start_cloud_upload_time',
      width: 150,
      cell(h, props) {
        return (
          <Tooltip content={`${props.row.start_cloud_upload_time} (UTC+0)`}>
            <span>
              {props.row.start_cloud_upload_time} (UTC+0)
            </span>
          </Tooltip>
        );
      },
    },
    {
      title: 'Sync Media',
      colKey: 'media_list',
      cell(h, props) {
        return (
          <Space size={'small'}>{
            props.row.media_list.map((media: string) => (
              <Tooltip content={media}>
                <SvgIcon class={'cursor-pointer w-[18px] h-[18px]'}
                         name={ media === 'AppLovin' ? 'icon-applovin' : media.toLowerCase()} />
              </Tooltip>
            ))
          }</Space>
        );
      },
    },
    {
      title: 'Create Time',
      colKey: 'create_time',
    },
    {
      title: 'Creator',
      colKey: 'creator',
    },
    {
      title: 'Edit Time',
      colKey: 'update_time',
    },
    {
      title: 'Actions',
      colKey: 'opt',
      width: 150,
      cell: (h, { row }) => (<>
        <Button
          theme={'primary'}
          variant={'text'}
          onClick={() => gotoCreativeAutoRulesDetail({ id: row.id })}
        >Edit</Button>
        <DropDownList onDelete={() => updateList()} id={row.id} name={row.rule_name} />
      </>),
    },
  ];
  return {
    cols,
    displayCols: ref([
      'switch',
      'rule_name',
      'folder',
      'type',
      'start_cloud_upload_time',
      'update_time',
      'media_list',
      'create_time',
      'creator',
      'opt',
    ]),
  };
}
