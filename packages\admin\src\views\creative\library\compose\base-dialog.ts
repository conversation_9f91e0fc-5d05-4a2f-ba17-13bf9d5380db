import { Component, getCurrentInstance } from 'vue';
import { tryOnUnmounted } from '@vueuse/core';
import { useClearInstance, useDialogInstance } from 'common/compose/dialog-instance';

export function useCreativeDialogInstance() {
  const parentInstance = getCurrentInstance() || {};
  const baseDialogInstance = useDialogInstance(parentInstance);
  const clearInstance = useClearInstance();
  const collectInstance = (instance: any) => {
    clearInstance.set(instance);
    return instance;
  };

  tryOnUnmounted(() => {
    clearInstance.clear();
  });
  return (
    dialogComponent: Component,
    props?: Record<string, any>,
    cacheKey?: string,
  ) => collectInstance(baseDialogInstance(dialogComponent, props, cacheKey));
}
