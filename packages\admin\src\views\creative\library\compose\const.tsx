import {
  ErrorCircleFilledIcon,
  CheckCircleFilledIcon,
  LoadingIcon,
  CloseCircleFilledIcon,
} from 'tdesign-icons-vue-next';

type StatusType = {
  icon?: JSX.Element;
  iconType?: string;
  text: string;
  operations?: string[];
  error?: boolean;
  tooltip?: string;
  noSelect?: boolean;
};

export const UPLOAD_STATUS: Record<number, StatusType> = {
  1: {
    icon: <span class={'text-success-secondary'}><LoadingIcon /></span>,
    text: 'In Progress',
    operations: ['cancel'],
  },
  2: {
    icon: <span class={'text-success-secondary'}><CheckCircleFilledIcon /></span>,
    text: 'Successful',
  },
  3: {
    icon: <span class={'ml-[4px] text-error-primary'}><ErrorCircleFilledIcon /></span>,
    text: 'Failed',
    error: true,
    operations: ['resume'],
  },
  4: {
    icon: <span><CloseCircleFilledIcon /></span>,
    text: 'Cancelled',
    operations: ['resume'],
  },
  5: {
    icon: <span class={'text-success-secondary'}><LoadingIcon /></span>,
    text: 'Pending',
    tooltip:
      'Reached the limitation of Youtube API capability\nWill restart task in 3pm everyday ',
    noSelect: true,
    operations: ['cancel'],
  },
};

export const FORMAT_TYPE_MAP: Record<number, any> = {
  1: { text: 'Video' },
  2: { text: 'Image' },
  3: { text: 'HTML' },
  0: { text: 'Other' },
};
