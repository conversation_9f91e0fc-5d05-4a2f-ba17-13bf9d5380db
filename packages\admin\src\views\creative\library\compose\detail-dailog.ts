import { useCreativeDialogInstance } from '@/views/creative/library/compose/base-dialog';
import { DetailDrawer } from 'common/components/creative/Drawer';
import Preview from 'common/components/Dialog/Preview/Preview.vue';
import { useCreativeDetail } from 'common/compose/creative/detail';

export function useDetailDialog(mode: any, store: any) {
  const dialogInstance = useCreativeDialogInstance();
  const showDetail = (data: any) => {
    dialogInstance(DetailDrawer, { data }, `drawer-${data.base.id}`).show();
  };

  const showPreview = (data: any) => {
    const { getUrl } = useCreativeDetail(data.base, store);
    dialogInstance(Preview, {
      title: data.base.title,
      type: data.base.type,
      url: data.base.url,
      poster: data.base.poster,
      getUrl,
      mode,
      hideTrigger: true,
    }).show();
  };

  return {
    showDetail,
    showPreview,
  };
}
