import { computed } from 'vue';
import { useDownloadFile } from 'common/compose/download-file';
import dayjs from 'dayjs';
import { useGlobalGameStore } from '@/store/global/game.store';
import { get } from '@vueuse/core';
import { useGoto } from '@/router/goto';

import UploadFile from '@/views/creative/library/components/dialog/UploadFile.vue';
// import MediaSync from '@/views/creative/library/components/dialog/media/MediaSync.vue';
import BatchImportLabel from '@/views/creative/library/components/dialog/BatchImportLabel.vue';
import Label from '@/views/creative/library/components/dialog/Label.vue';
import BulkRename from '@/views/creative/library/components/dialog/BulkRename.vue';
import DeleteAssets from '@/views/creative/library/components/dialog/DeleteAssets.vue';
import EditContentCover from '@/views/creative/library/components/dialog/EditContentCover.vue';
import DownloadList from '@/views/creative/library/components/dialog/DownloadList.vue';
import { useCreativeDialogInstance } from '@/views/creative/library/compose/base-dialog';
import { useValidateDialog } from '@/views/creative/library/compose/validate-dialog';
import { MEDIA_LIST } from '../components/dialog/media/config';
import { genDropOpts } from '@/views/creative/library/compose/gen-drop-opts';
import { useAuthStageStore } from '@/store/global/auth.store';
import { ChannelDrawer } from 'common/components/creative/Drawer';

export function useCreativeDialog({
  checkValue,
  simpleCheckedAssets,
  isTableView,
  changeView,
  tableRef,
  containerType,
  store,
}: any) {
  const { gotoCreativeTask, gotoCreativeAutoRulesList } = useGoto();
  const gameStore = useGlobalGameStore();
  const dialogInstance = useCreativeDialogInstance();
  const validateDialog = useValidateDialog();

  const rename = (params: any) => {
    const instance = dialogInstance(BulkRename, { store });
    instance.show(Array.isArray(params) ? params : [params]);
  };

  const hasCheckedValue = computed(() => get(checkValue).length === 0);
  const videoChecked = computed(() => get(simpleCheckedAssets).filter((item: any) => item.type === 'video'));

  const authStore = useAuthStageStore();
  const canEdit = computed(() => authStore.hasPrivilege('edit_name'));

  const actionsList = computed(() => [
    {
      content: 'Bulk Rename',
      value: 'edit_batch_change_name',
      disabled: get(hasCheckedValue) && !canEdit.value,
      hidden: store.dictionary.type === 'dropbox',
      disabledText: get(canEdit) ? 'Please select creative' : 'Insufficient authority',
      method: () => rename(get(simpleCheckedAssets)),
    },
    {
      content: 'Bulk Export Asset ID',
      value: 'export_asset',
      disabled: get(hasCheckedValue),
      divider: true,
      disabledText: 'Please select creative',
      method: () => useDownloadFile(simpleCheckedAssets, `${gameStore.gameCode}_AssetIds_${dayjs().format('YYYYMMDDHHmmss')}.csv`),
    },
    {
      content: 'Edit Creative Cover',
      value: 'edit_cover',
      disabled: get(hasCheckedValue) || get(videoChecked).length === 0,
      hidden: store.dictionary.type === 'dropbox',
      disabledText: get(hasCheckedValue) ? 'Please select creative' : 'Please select video creative',
      divider: true,
      method: () => dialogInstance(EditContentCover, {
        store,
        assetsList: videoChecked.value,
      }).show(get(simpleCheckedAssets)),
    },
    {
      content: 'Delete Creative',
      value: 'delete_asset',
      disabled: get(hasCheckedValue),
      hidden: store.dictionary.type === 'dropbox',
      disabledText: 'Please select creative',
      method: () => dialogInstance(DeleteAssets, {
        store,
        deleteCallback: () => {
          // eslint-disable-next-line no-param-reassign
          checkValue.value = [];
        },
      }).show(get(simpleCheckedAssets)),
    },
    {
      content: 'Download Creative',
      value: 'download_asset',
      disabled: get(hasCheckedValue),
      disabledText: 'Please select creative',
      method: () => dialogInstance(DownloadList, { store })
        .show(get(simpleCheckedAssets)),
    },
    {
      content: 'Validate Assets',
      value: 'validateAsstes',
      disabled: get(hasCheckedValue),
      hidden: store.dictionary.type === 'dropbox',
      disabledText: 'Please select creative',
      method: () => {
        validateDialog.show({
          mediaList: MEDIA_LIST,
          namingTypeList: gameStore.gameCode === 'pubgm' ? ['pubgm'] : ['gpp'],
        }, get(simpleCheckedAssets));
      },
    },
  ]
    .map(genDropOpts)
    .filter(i => !i.hidden),
  );

  const labelManageList = computed(() => [
    genDropOpts({
      content: 'Edit Label',
      value: 'editDesignLabel',
      disabled: get(hasCheckedValue),
      disabledText: 'Please select creative',
      method: () => dialogInstance(Label, { store }).show('content', get(simpleCheckedAssets)),
    }),
    {
      content: 'Bulk Edit Label',
      value: 'batchEditDesignLabel',
      method: () => dialogInstance(BatchImportLabel, {
        manualLabelList: store.label.manualLabel,
      }).show(),
    },
  ]);

  const buttons = computed(() => {
    let base = [
      authStore.hasPrivilege('upload_asset') && {
        name: 'export',
        label: 'Upload Local File',
        type: ['aix'],
        method: () => dialogInstance(UploadFile, {
          arthubCode: store.dictionary.arthubCode,
          publicToken: store.dictionary.publicToken,
          parentId: store.dictionary.activeFolderId,
          storageType: store.dictionary.type,
          update: store.material.update,
        }).show(),
      },
      {
        name: 'auto-rules-list',
        label: 'Automatic Task Rule',
        type: ['aix'],
        method: () => gotoCreativeAutoRulesList(),
      },
      {
        name: 'task-list-2',
        label: 'Task List',
        type: ['aix'],
        method: () => gotoCreativeTask(),
      },
      {
        name: get(isTableView) ? 'group' : 'more-select',
        label: 'Switch View',
        type: ['aix', 'media'],
        method: changeView,
      },
    ].filter(Boolean) as { name: string, label: string, type: string[], method: () => void }[];
    if (isTableView.value) {
      base = [
        {
          name: 'more',
          label: 'Select More Metrics',
          type: ['aix', 'media'],
          method: () => get(tableRef)?.showMetricsSelect(),
        },
      ].concat(base);
    }
    return base.filter(i => (i?.type)?.includes(containerType));
  });

  const showMediaSync = (data: any) => {
    dialogInstance(ChannelDrawer, {
      data,
      deleteCallback: (id: string) => {
        // eslint-disable-next-line no-param-reassign
        checkValue.value = checkValue.value.filter(item => item !== id);
      },
    }, 'channel-drawer-111').show(get(simpleCheckedAssets));
  };

  return {
    actionsList,
    labelManageList,
    buttons,
    rename,
    showMediaSync,
  };
}
