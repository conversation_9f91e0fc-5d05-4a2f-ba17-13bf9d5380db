import dropboxImage from '@/assets/img/creatives/dropbox.png';
import googledriverImage from '@/assets/img/creatives/googledriver.png';
import arthubImage from '@/assets/img/creatives/arthub.png';

export type TDriverType = 'dropbox' | 'arthub' | 'googledriver';

export function useDriverImage(driver: TDriverType) {
  return {
    dropbox: dropboxImage,
    arthub: arthubImage,
    googledriver: googledriverImage,
  }[driver];
}
export function useDriverName(driver: TDriverType) {
  const driverNameMap = {
    dropbox: 'Dropbox',
    googledriver: 'Google Drive',
    arthub: 'Arthub',
  };
  return driverNameMap[driver];
}
export function useDriverGuide(driver: TDriverType) {
  // https://aix.levelinfinite.com/docs/user_guide/creative/google_Drive_setup.html
  const baseUrl = 'https://aix.levelinfinite.com/docs/user_guide/creative';
  return {
    dropbox: `${baseUrl}/dropbox_setup.html`,
    googledriver: `${baseUrl}/google_Drive_setup.html`,
    arthub: `${baseUrl}/arthub_setup.html`,
  }[driver];
}
