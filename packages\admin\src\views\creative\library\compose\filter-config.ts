import { ref } from 'vue';


export function useTabsConfig(tabMap: Record<string, string>) {
  const tabList = Object.keys(tabMap).map(k => ({
    label: tabMap[k],
    value: k,
  }));
  const tabSelectId = ref<string>(tabList[0].value);
  return {
    tabSelectId,
    tabInfo: {
      hideOpt: true,
      hideSaveView: true,
      hideShareView: true,
      modelValue: tabSelectId,
      list: tabList,
      showNum: tabList.length,
      'onUpdate:modelValue': (newValue: string) => {
        tabSelectId.value = newValue;
      },
    },
  };
}
