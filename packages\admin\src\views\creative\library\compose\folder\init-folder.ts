import { getFolderList, getRootId } from 'common/service/creative/library/get-dictionary-list';
import { TreeProps } from 'tdesign-vue-next';

export const PAGE_LIMIT = 1000;

let pathMap: Record<string, any> = {};

export async function initFolder() {
  const rootId = await getRootId();
  if (rootId) {
    return {
      ...await getFolders(rootId.depot_id),
      rootId: rootId.depot_id,
    };
  }
}


export async function getFolders(parentId: string, offset = 0): Promise<{
  list: TreeProps['data'],
  total: number
}> {
  const res = await getFolderList({
    offset,
    limit: PAGE_LIMIT,
    parent_id: parentId,
    list_type: 'iegg',
  }) as any;
  // 将这个对象挂在全局上，方便复用
  setPathMap(res?.data);
  return {
    list: res?.data?.map((item: any) => ({
      label: item.name,
      value: item.id,
      children: item.direct_directory_count > 0,
      fullPathName: item.full_path_name.split(',')
        .concat([item.name]),
    })) || [],
    total: res.total,
  };
}


export function setPathMap(list: any[]) {
  list.map((i: any) => pathMap[i.id] = i);
}

export function getPathItem(id: any) {
  return pathMap?.[id] || {};
}

export function clearMemoryMap() {
  pathMap = {};
}

