import ToolTips from 'tdesign-vue-next/es/tooltip';
import { VNode } from 'vue';

type TGenDropOpts = {
  content: string | Function | VNode; // 展示的文案
  value: string;
  disabled: boolean,
  method: () => void
  disabledText: string; // 禁用时候的弹出文案
  divider?: boolean,
};
export function genDropOpts(params: TGenDropOpts): Omit<TGenDropOpts, 'disabledText'> & { hidden?: boolean } {
  if (!params.disabled) return params;
  return {
    ...params,
    content: <ToolTips content={params.disabledText} placement={'mouse'}>{params.content}</ToolTips>,
  };
}
