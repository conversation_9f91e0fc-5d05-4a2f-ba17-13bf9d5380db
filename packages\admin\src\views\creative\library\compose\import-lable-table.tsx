import { T<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>lStatus, StatusConfigs, TKeyAttribute } from '@/views/creative/library/define';
import { sliceStart } from 'common/utils/string';
import { Button } from 'tdesign-vue-next';
import { computed, Ref } from 'vue';
import { get, resolveUnref } from '@vueuse/core';

const LabelKeyPrefix = 'label-';

function fieldRender(h: any, { row, col }: any) {
  const { colKey } = col;
  const isLabel = colKey.startsWith(LabelKeyPrefix);
  const firstLabel = sliceStart(colKey, LabelKeyPrefix);
  const secondLabel = row.labels[firstLabel]?.join('、') || '-';
  const content = isLabel ? secondLabel : row[colKey as keyof TLabel];
  // const theme = record.status === 'parse_failed' ? 'weak' : 'text';
  return  content;
}


export function useImportTable(
  keyAttribute: Ref<TKeyAttribute>,
  uploading: Ref<boolean>,
  firstLabels: any,
  removeLabels: (id: string) => void,
) {
  const cols = computed(() => [
    {
      colKey: get(keyAttribute).key,
      title: get(keyAttribute).title,
      width: 160,
      fixed: 'left' as const,
      cell: fieldRender,
      ellipsis: true,
    },
    {
      colKey: 'status',
      title: 'Status',
      width: 120,
      cell: (h: any, { row }: any) => {
        const { status, message } = row;
        const showStatus: TLabelStatus = uploading.value && status === 'pending' ? 'uploading' : status; // 使用全局的uploading状态
        const { text, error } = StatusConfigs[showStatus];
        return error ? message : text;
      },
    },
    {
      colKey: 'labelName',
      title: 'Label Name',
      width: 100,
      ellipsis: true,
    },
  ].concat(
    resolveUnref(firstLabels)
      .map((firstLabel: string) => ({
        colKey: `${LabelKeyPrefix}${firstLabel}`,
        title: firstLabel,
        width: 120,
        ellipsis: true,
        cell: fieldRender,
      })),
    {
      colKey: 'opt',
      title: 'Operation',
      width: 80,
      cell: (h: any, { row }: any) => (
        <Button
          variant="text"
          theme={'primary'}
          disabled={['uploading'].includes(row.status)}
          onClick={() => {
            removeLabels(row.id);
          }}
        >
          Delete
        </Button>
      ),
    },
  ));
  return { cols };
}
