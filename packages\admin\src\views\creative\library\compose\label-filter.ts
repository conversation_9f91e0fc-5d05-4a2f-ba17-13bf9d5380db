import { toRaw } from 'vue';
import { getSerachInfo } from '@/views/creative/library/utils';
import { METRIALLIST_CONDITION } from '@/store/creative/library/const';
import { get } from '@vueuse/core';

export function useLabelFilter(store: any) {
  function submit(data: any) {
    console.log('data', toRaw(data));
    // 三个搜索条件  分别是搜索框 标签 状态
    const {
      searchBox,
      onlineStatus = null,
      labelsSearch,
      syncedStatus,
      syncMedia,
      formatTypeList,
      uploadCloudTime,
    } = toRaw(get(data));
    // 标签这里有两个条件 已选择的标签和 类型
    const { labelList = [], labelsSearchType } = labelsSearch || {};
    const { text, names, searchType } = getSerachInfo(searchBox);
    store.material.setCondition({
      ...METRIALLIST_CONDITION,
      searchType,
      labelsSearchType,
      labels: labelList.map((item: string) => {
        const res = item.split('%-%');
        return {
          label_name: res[0],
          first_label: res[1],
          second_label: res[2],
        };
      }),
      syncedStatus,
      syncMedia,
      onlineStatus,
      text,
      names,
      formatTypeList,
      uploadCloudTime,
      filteronlinestatus: onlineStatus === null ? 0 : 1,
    });
  }

  function reset() {
    store.material.setCondition(METRIALLIST_CONDITION);
  }
  return {
    submit,
    reset,
  };
}
