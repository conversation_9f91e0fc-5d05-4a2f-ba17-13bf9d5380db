import TButton from 'tdesign-vue-next/es/button';
// import { ref } from 'vue';
import { ITableCols } from 'common/components/table/type';
import { MediaSyncState } from 'common/components/creative/MaterialItem';
import { useCreativeDetail } from 'common/compose/creative/detail';
import { resolveUnref, useStorage } from '@vueuse/core';
export function useTable({
  hideRename = false,
  methods: {
    rename,
    showDetail,
    showPreview,
  },
  type = 'aix',
}: any) {
  const cols: ITableCols[] = [
    {
      colKey: 'row-select',
      type: 'multiple' as 'multiple',
    },
    {
      title: 'Asset Name',
      colKey: 'title',
      ellipsis: true,
    },
    {
      title: 'Type',
      colKey: 'type',
    },
    {
      title: 'Url',
      colKey: 'poster',
      ellipsis: true,
    },
    {
      title: 'Synced Media',
      colKey: 'syncedMedia',
      cell: (h: any, { row }: any) => (
         <MediaSyncState data={row} />
      ),
    },
    {
      title: 'Data Created',
      colKey: 'createdAt',
    },
    {
      title: 'Actions',
      colKey: 'opt',
      width: 291,
      cell: (h, { row }) => {
        const { loading, detailData, getDetailInfo } = useCreativeDetail(row as any);
        return (
        <div class={'-ml-[20px] space-x-[10px]'}>
          { !hideRename && <TButton
            theme="primary"
            variant="text"
            onClick={() => rename({
              AssetID: row.id,
              AssetName: row.title,
              originName: row.ext.name,
            })}
          >
            Rename
          </TButton>}
          <TButton
            theme="primary"
            variant="text"
            loading={loading.value}
            onClick={() => {
              getDetailInfo(() => {
                showDetail(resolveUnref(detailData));
              });
            }}
          >
            View Detail
          </TButton>
          <TButton
            theme="primary"
            variant="text"
            disabled={!['video', 'image'].includes(row.type)}
            onClick={() => showPreview(resolveUnref(detailData))}
          >
            Preview
          </TButton>
        </div>
        );
      },
    },
  ];

  const displayCols = useStorage(`aix-creative-${type}-library-cols-key`, ['row-select', 'title', 'syncedMedia', 'createdAt', 'opt'], localStorage);

  return {
    cols,
    displayCols,
    // displayCols: ref([
    //   'row-select',
    //   'title',
    //   'syncedMedia',
    //   'createdAt',
    //   'opt']),
  };
}
