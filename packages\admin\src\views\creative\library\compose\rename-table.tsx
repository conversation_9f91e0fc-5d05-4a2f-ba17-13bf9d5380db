import TButton from 'tdesign-vue-next/es/button';
import Space from 'tdesign-vue-next/es/space';
import { Input, Tooltip } from 'tdesign-vue-next';
import { ErrorCircleIcon } from 'tdesign-icons-vue-next';

export function useRenameTable({ remove, updateItem }: any) {
  const cols = [
    {
      colKey: 'row-select',
      type: 'multiple' as 'multiple',
      width: 50,
    },
    {
      colKey: 'originName',
      title: 'Origin Name',
      ellipsis: true,
      width: 250,
    },
    {
      colKey: 'newName',
      title: 'New Name',
      ellipsis: true,
      width: 250,
      edit: {
        component: Input,
        props: {
          clearable: true,
          autofocus: true,
          placeholder: 'Please enter',
        },
        abortEditOnEvent: ['onEnter'],
        rules: [{ required: true, message: 'Cannot be empty' }],
        onEdited: (context: any) => {
          updateItem(context.rowIndex, context.newRowData);
        },
      },
    },
    {
      colKey: 'status',
      title: 'Status',
      width: 100,
      cell: (h: any, { row }: any) => (
        <Space size={'4px'}>
          {row.status}
          {row.status === 'Failed' && (
            <Tooltip content={'Name cannot be repeated'}>
              <ErrorCircleIcon />
            </Tooltip>
          )}
        </Space>
      ),
    },
    {
      colKey: 'opt',
      title: 'Operation',
      width: 100,
      cell: (h: any, { index }: any) => (
        <Space class={'-ml-[20px]'}>
          <TButton
            theme="primary"
            variant="text"
            onClick={() => remove(index)}
          >
            Delete
          </TButton>
        </Space>
      ),
    },
  ];
  return {
    cols,
  };
}
