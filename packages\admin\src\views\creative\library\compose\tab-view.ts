import { computed, nextTick, ref, watch } from 'vue';
import { tryOnBeforeMount, get } from '@vueuse/core';
import { useCustomView } from 'common/compose/useCustomView';
import { getUrlParams } from 'common/utils/baseUrl';
import { useGlobalGameStore } from '@/store/global/game.store';
import type { ViewItem } from 'common/components/ViewTab/type';
import { useTips } from 'common/compose/tips';

type OnTabViewParamsChange = (params: any) => void;
const DefaultItem = {
  value: 'default',
  label: 'Default',
  game: 'allgame',
  type: 'default',
  param: {},
};

type UseTabviewParam = {
  module: string,
  params: any,
  onTabViewParamChange: OnTabViewParamsChange,
  needShare: boolean
};

/**
 * @param module 构成一个唯一的id，用来区分模块
 * @param params 需要保存到视图的参数
 * @param onTabViewParamChange 当视图参数发生变化时候的回调
 */
export function useTabView(
  {
    module,
    params,
    onTabViewParamChange,
    needShare,
  }: UseTabviewParam,
) {
  const modelValue = ref('default');
  /**
   * 通用view params 接口
   */
  const {
    addView,
    updateView,
    deleteView,
    getShare,
    getList,
  } = useCustomView();
  const globalGameStore = useGlobalGameStore();
  const { err } = useTips();

  // TODO 将default合入到数据中，要保证有一个default视图，不然，就无法分享辣！
  const list = ref<ViewItem[]>([DefaultItem]);
  const updateList = async () => {
    const res = await getList({ game: globalGameStore.gameCode, type: 'custom', module }) as any;
    list.value = res.concat([DefaultItem]);
  };

  const currentTabView = computed(() => list.value.find((i: any) => i.value === modelValue.value));

  // 当当前的视图发生切换动作的时候，做出刷新
  watch(currentTabView, (val: any) => {
    if (val) {
      onTabViewParamChange?.(val);
    }
  });

  // 分享逻辑
  // check url params and get params
  tryOnBeforeMount(async () => {
    await updateList();
    const code = getUrlParams('code') as string;
    if (code && needShare) {
      // 获取code进行处理，判断是否有code
      // 如果有，则认为此code可以拉取到分享参数
      const shareParams = await getShare(code);
      if (shareParams) {
        if (!list.value.includes(shareParams)) {
          list.value.unshift(shareParams);
        }
        modelValue.value = shareParams.value;
        await nextTick();
        onTabViewParamChange?.(shareParams);
      }
    }
  });
  const checkSameName = (name: string): boolean => list.value.some((i: any) => i.label === name);
  const onAddView = async (viewConfig: any) => {
    // TODO 检查是否重名
    const name = viewConfig.formData.viewName;
    if (!checkSameName(name)) {
      const activeId =  await addView({
        name,
        param: get(params),
        system: module,
        type: 'custom',
        game: viewConfig.formData.viewType === 'this' ? globalGameStore.gameCode : 'allgame',
      });
      await updateList();
      modelValue.value = activeId;
    } else {
      await err('View Name already exists');
    }
  };
  const onUpdateView = async (viewConfig: any) => {
    // TODO 检查是否重名
    const name = viewConfig.formData.viewName;
    if (!checkSameName(name)) {
      await updateView({
        id: viewConfig.current.value,
        name,
        param: get(params),
        system: module,
        type: 'custom',
        game: viewConfig.formData.viewType === 'this' ? globalGameStore.gameCode : 'allgame',
      });
      await updateList();
    } else {
      await err('View Name already exists');
    }
  };
  const onDeleteView = async (viewConfig: any) => {
    await deleteView({ id: viewConfig.value });
    await updateList();
  };

  return {
    modelValue,
    list,
    shareParam: computed(() => ({
      ...currentTabView.value,
      system: module,
    })),
    onAddView,
    onUpdateView,
    onDeleteView,

  };
}
