import { ITableCols } from 'common/components/table/type';
import { ref } from 'vue';
import TButton from 'tdesign-vue-next/es/button';
import TSpace from 'tdesign-vue-next/es/space';
import { FORMAT_TYPE_MAP, UPLOAD_STATUS } from '@/views/creative/library/compose/const';
import TaskVideoPreview from '@/views/creative/library/components/TaskVideoPreview.vue';
import { isEmpty } from 'lodash-es';
import UploadStstus from '@/views/creative/library/components/UploadStstus/Index.vue';
import { SvgIcon } from 'common/components/SvgIcon';
import { MaterialMediaList } from '@/views/trade/ads_creation/common/template/config';
import Tooltip from 'tdesign-vue-next/es/tooltip';

export function useTaskTable(
  resumeUpload: Function,
  cancelUpload: Function,
  isDisabledRowChecked: Function,
  operateDisabled: boolean,
) {
  const cols: ITableCols[] = [
    {
      colKey: 'row-select',
      type: 'multiple' as 'multiple',
      checkProps: ({ row }) => ({ disabled: isDisabledRowChecked(row) }),
    },
    {
      title: 'Task Name',
      colKey: 'directory_name',
      ellipsis: {
        theme: 'light',
        placement: 'bottom',
      },
    },
    {
      title: 'Task Type',
      colKey: 'automatic_sync_task_rule_id',
      cell: (h, { row }) => (String(row?.automatic_sync_task_rule_id) === '0' ? 'Manual' : 'Automatic'),
    },
    {
      title: 'Asset Name',
      colKey: 'asset_name',
      width: 300,
      ellipsis: {
        theme: 'light',
        placement: 'bottom',
      },
    },
    {
      title: 'Type',
      colKey: 'format_type',
      width: 120,
      cell: (h, { row }) => FORMAT_TYPE_MAP[row.format_type]?.text || row.format || '-',
    },
    {
      title: 'Creator',
      colKey: 'creator',
      ellipsis: {
        theme: 'light',
        placement: 'bottom',
      },
    },
    {
      title: 'Start Date',
      colKey: 'create_time',
      ellipsis: {
        theme: 'light',
        placement: 'bottom',
      },
    },
    {
      title: 'Synced Date',
      colKey: 'update_time',
      ellipsis: {
        theme: 'light',
        placement: 'bottom',
      },
      cell: (h, { row }) => (row.status === 3 ? '-' : row.update_time),
    },
    {
      title: 'Synced Media',
      colKey: 'channel',
      minWidth: '150px',
      cell(h, { row }) {
        const media = MaterialMediaList[row.channel];
        return media ? (
          <Tooltip content={media}>
            <SvgIcon class={'cursor-pointer'}
                     name={ media === 'AppLovin' ? 'icon-applovin' : media.toLowerCase()} />
          </Tooltip>
        ) : '-';
      },
    },
    {
      title: 'YouTube Link',
      colKey: 'youtube_id',
      cell: (h, { row }) => {
        if (row.youtube_id) {
          const youtubeLink = `https://www.youtube.com/watch?v=${row.youtube_id}`;
          return (
            <TaskVideoPreview
              title={'YouTube'}
              type="video"
              url={youtubeLink.replace('watch?v=', 'embed/')}
            >
              {{
                default: () => <a
                    class={'t-text-ellipsis  block'}
                    href="javascript:"
                    title={youtubeLink}
                  >
                  {youtubeLink}
                </a>,
              }}
            </TaskVideoPreview>
          );
        }
        return '-';
      },
    },
    {
      title: 'Storage path',
      colKey: 'full_path_name',
      ellipsis: true,
      cell: (h, { row }) => row.full_path_name.split(',').join(' > '),
    },
    {
      title: 'Creative Set Name (AppLovin)',
      colKey: 'extra_info.creative_set_name',
      ellipsis: {
        theme: 'light',
        placement: 'bottom',
      },
    },
    {
      title: 'Task Status',
      colKey: 'status',
      fixed: 'right',
      width: 150,
      required: true,
      cell: (h, { row }) => {
        const info = UPLOAD_STATUS[row.status];
        if (!info) {
          return null;
        }
        const { text, error, tooltip } = info;
        return (
          <UploadStstus key={row.status} statusInfo={{
            statusKey: row.status,
            statusText: text,
            showToolTip: error || !isEmpty(tooltip),
          }}
            tooltip={{
              theme: error ? 'light' : 'default',
            }}
          >
            {{
              content: () => <div class={'text-xs'}>
                { error ?  <>
                  <div>Error info: <span class={'text-error-primary'}>{ row.err_memo }</span></div>
                  { !isEmpty(row.err_detail) ? <>
                  Error details: <span class={'text-error-primary'}>{ row.err_detail }</span>
                    </> : null
                  }
                  </> : null
                }
                {
                  tooltip ? <div class={' text-white-primary'}>
                    {tooltip.split('\n').map(item => (<p>{ item }</p>))}
                  </div> : null
                }
              </div>,
            }}
          </UploadStstus>
        );
      },
    },
    {
      title: 'Action',
      colKey: 'opt',
      fixed: 'right',
      width: 150,
      required: true,
      cell: (h, { row }) => {
        if (UPLOAD_STATUS[row.status].operations?.includes('resume')) {
          return (
            <TSpace class={'-ml-[18px]'}>
              <TButton onClick={() => resumeUpload([row.id])} theme='primary' variant='text' disabled={operateDisabled}>
                Retry
              </TButton>
            </TSpace>
          );
        } if (UPLOAD_STATUS[row.status].operations?.includes('cancel')) {
          return (
            <TSpace class={'-ml-[18px]'}>
              <TButton onClick={() => cancelUpload([row.id])} theme='primary' variant='text' disabled={operateDisabled}>
                Cancel
              </TButton>
            </TSpace>
          );
        }
        return '-';
      },
    },
  ];

  return {
    cols,
    displayCols: ref([
      'row-select',
      'directory_name',
      'asset_name',
      'automatic_sync_task_rule_id',
      'format_type',
      'channel',
      'creator',
      'create_time',
      'update_time',
      'status',
      'opt',
    ]),
  };
}
