import { getUploadMediaTask } from 'common/service/creative/library/get-aix-task';
import { useDownloadFile } from 'common/compose/download-file';
import dayjs from 'dayjs';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useLoading } from 'common/compose/loading';
import { sleep } from 'common/utils/common';
import { FORMAT_TYPE_MAP, UPLOAD_STATUS } from '@/views/creative/library/compose/const';
import { MaterialMediaList } from '@/views/trade/ads_creation/common/template/config';

export function useDownloadTask(search: any) {
  const { isLoading, showLoading, hideLoading } = useLoading(false);
  const gameStore = useGlobalGameStore();
  const emitDownLoad = async () => {
    showLoading();
    const { list } = await getUploadMediaTask({
      __game: useGlobalGameStore().gameCode,
      channel: search.channel || null,
      text: search.text || null,
      status: search.status || null,
      format_type: search.formatType || null,
      directories: search.directories || [],
      page: search.pageIndex - 1,
      count: search.pageSize,
      isAutoTask: search.taskType !== 'all',
      autoTaskRuleId: search.taskType === 'auto' ? search.ruleId : '0',
      dateType: search.dateType,
      startTime: `${search.dateRange[0]} 00:00:00`,
      endTime: `${search.dateRange[1]} 24:00:00`,
    }) as any;
    const data = list.map((item: any) => {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { asset_name, youtube_id } = item;
      return {
        'Assert Name': asset_name,
        'YouTube Link': youtube_id
          ? `https://www.youtube.com/watch?v=${youtube_id}`
          : '-',
        'Task Type': String(item?.automatic_sync_task_rule_id) === '0' ? 'Manual' : 'Automatic',
        'Task Name': item.directory_name,
        Type: FORMAT_TYPE_MAP[item.format_type]?.text || item.format || '-',
        Creator: item.creator,
        'Start Date': item.create_time,
        'Synced Date': +item.status !== 2 ? '-' : item.update_time,
        'Synced Media': MaterialMediaList[item.channel],
        'Storage path': item.full_path_name,
        'Task Status': UPLOAD_STATUS[item.status].text,
        'Creative set Name': item.extra_info.creative_set_name || '-',
      };
    });
    useDownloadFile([
      {
        list: data,
        sheet: 'Sheet1',
      },
    ], `${gameStore.gameCode || ''}_upload_media_${dayjs().format('YYYYMMDDHHmmss')}.xlsx`, { mode: 'group' });
    await sleep(1500);
    hideLoading();
  };
  return {
    isLoading,
    emitDownLoad,
  };
}
