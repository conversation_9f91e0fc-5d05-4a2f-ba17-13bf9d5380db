import { isString } from 'lodash-es';
import ToolTips from 'tdesign-vue-next/es/tooltip';

export function useValidateDialogTable() {
  const cols = [
    {
      colKey: 'row-select',
      type: 'multiple' as 'multiple',
      checkProps: ({ row }: any) => ({ disabled: row.hasMetaErr }),
    },
    {
      title: 'Asset Name',
      colKey: 'AssetName',
      width: '150px',
      ellipsis: {
        theme: 'light',
        placement: 'bottom',
      },
    },
    {
      title: 'Tips',
      colKey: 'tips',
      cell: (h: any, { row }: any) => {
        if (Array.isArray(row.tips)) {
          return row.tips.map((text: string, index: number) => (
            <ToolTips
              content={text}
              placement={'mouse'}
            >
              <p
                class={'truncate'}
                key={index}
              >
                {text}
              </p>
            </ToolTips>
          ));
        }
        if (isString(row.tips)) {
          return (
            <ToolTips
              content={row.tips}
              placement={'mouse'}
            >
              <p class={'truncate'}>{row.tips}</p>
            </ToolTips>
          );
        }
        return <span>Check passed</span>;
      },
    },
  ];
  return { cols };
}
