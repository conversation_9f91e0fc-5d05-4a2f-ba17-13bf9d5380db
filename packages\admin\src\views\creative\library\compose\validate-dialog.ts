import { useCreativeDialogInstance } from '@/views/creative/library/compose/base-dialog';
import Validate from '@/views/creative/library/components/dialog/Validate.vue';
import { TSimpleAsset } from '@/views/creative/library/define';
import { DialogPlugin } from 'tdesign-vue-next';

export function useValidateDialog() {
  const dialogInstance = useCreativeDialogInstance();

  return {
    show: (props: any, params?: TSimpleAsset[]) => {
      dialogInstance(Validate, props).show(params);
    },
  };
}


export function syncedSuccess(callback: () => void) {
  const confirm = DialogPlugin.confirm({
    theme: 'success',
    header: 'Task submitted successfully',
    body: 'Will sync to media library in the backend, please close this window. Open task list to view sync status.',
    cancelBtn: 'Cancel',
    confirmBtn: 'View Task',
    onConfirm: () => {
      callback?.();
      confirm.hide();
    },
  });
  return Promise.resolve();
}
