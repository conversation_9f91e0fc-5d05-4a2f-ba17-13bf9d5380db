import { IFormDynamicItem } from 'common/components/FormContainer/type';
import components from '@/views/creative/common/components/components';
// import { STATUS_SELECT_OPTIONS } from '@/views/creative/library/define';
import { METRIALLIST_CONDITION, syncedListMap } from '@/store/creative/library/const';
import { LibraryType } from 'common/service/creative/library/type';
import LibraryLabel from '@/views/creative/library/components/LibraryLabel.vue';
import dayjs from 'dayjs';
import DateRangePicker from 'common/components/DateRangePicker';
import { basePresets, FORMAT_TYPE_OPTIONS } from '@/views/creative/library/define';
import { ComposeCommonSearchInput } from 'common/components/CommonSearchInputBox';

export const getAixLibraryFilterConfig = (
  labelList: any[],
  libraryType: LibraryType,
  isDropbox = false,
  showMediaSelect = false,
): Array<IFormDynamicItem> => [
  {
    name: ComposeCommonSearchInput,
    props: {
      label: 'Asset Name',
      typeKey: 'searchType', // 交并集的key定制
      valueKey: 'assetNameList', // 筛选条件的key定制
      unionValue: 2, // 交并集value值定制 并集是2
      intersectionValue: 4, // 交并集value值定制 交集是4
      tagInputProps: {
        class: 'w-[216px]',
      },
    },
    ext: {
      key: 'searchBox',
      default: {
        assetNameList: [],
        searchType: METRIALLIST_CONDITION.searchType,
      },
      label: 'searchBox',
      isAllowClose: false,
    },
  },
  libraryType === 'iegg' && isDropbox && {
    name: DateRangePicker,
    props: {
      label: 'Cloud Upload Period',
      date: ['', ''],
      'disable-date': {
        after: dayjs().add(0, 'days')
          .format(),
      },
      presets: basePresets,
      style: {
        width: '380px',
      },
    },
    ext: {
      key: 'uploadCloudTime',
      label: 'uploadCloudTime',
      isAllowClose: false,
      default: ['', ''],
    },
  },
  libraryType === 'iegg' && {
    name: components.Select,
    props: {
      title: 'Type',
      placeholder: 'select',
      multiple: true,
      list: FORMAT_TYPE_OPTIONS,
      clearable: true,
      searchable: true,
    },
    ext: {
      key: 'formatTypeList',
      label: '',
      title: '',
      isAllowClose: false,
      default: [],
    },
  },
  !isDropbox && {
    name: LibraryLabel,
    props: {
      valueType: 'everyWithLowest',
      options: labelList,
      labels: ['Label Name', 'First Label', 'Second Label'],
    },
    ext: {
      key: 'labelsSearch',
      label: '',
      isAllowClose: false,
      default: {
        labelsSearchType: METRIALLIST_CONDITION.labelsSearchType,
        labelList: METRIALLIST_CONDITION.labels,
      },
    },
  },
  libraryType === 'iegg' && {
    name: components.Select,
    props: {
      title: 'Sync Media Status',
      placeholder: 'Select',
      list: [
        { label: 'ALL', value: 0 },
        { label: 'Not Synced', value: 1 },
        { label: 'Synced', value: 2 },
      ],
      clearable: true,
      searchable: true,
    },
    ext: {
      key: 'syncedStatus',
      label: '',
      title: '',
      isAllowClose: false,
      default: METRIALLIST_CONDITION.syncedStatus,
    },
  },
  libraryType === 'iegg' && showMediaSelect && {
    name: components.Select,
    props: {
      title: 'Synced Media',
      placeholder: 'select',
      list: [
        ...Object.keys(syncedListMap)
          .map(key => ({
            label: syncedListMap[key as unknown as keyof typeof syncedListMap],
            value: key,
          })),
      ],
      clearable: true,
      multiple: true,
      searchable: true,
    },
    ext: {
      key: 'syncMedia',
      label: '',
      title: '',
      isAllowClose: false,
      default: [],
    },
  },
].filter(Boolean) as IFormDynamicItem[];
