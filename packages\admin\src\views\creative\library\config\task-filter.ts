import { IFormDynamicItem } from 'common/components/FormContainer/type';
import components from '@/views/creative/common/components/components';
import { BaseCommonSearchInput } from 'common/components/CommonSearchInputBox';
import {
  MEDIA_SELECT_OPTIONS,
  FORMAT_TYPE_OPTIONS,
  getTaskFilterStatus,
  TASK_TYPE_LIST, basePresets,
} from '@/views/creative/library/define';

import dayjs from 'dayjs';
import SelectDataRangePickerVue from 'common/components/DateRangePicker/SelectDataRangePicker.vue';
// import dayjs from 'dayjs';
import { getUrlParams } from 'common/utils/baseUrl';


export const getAxiTaskFilterConfig = (store: any) => [
  {
    name: SelectDataRangePickerVue,
    props: {
      date: ['', ''],
      options: [
        {
          label: 'Start Date',
          value: 'start_date',
        },
        {
          label: 'Synced Date',
          value: 'synced_date',
        },
      ],
      'disable-date': {
        after: dayjs()
          .add(0, 'days')
          .format(),
      },
      presets: basePresets,
    },
    ext: {
      key: 'dateInfo',
      label: 'dateInfo',
      isAllowClose: false,
      default: {
        dateRange: store.dateRange,
        dateType: store.dateType,
      },
    },
  },
  {
    name: BaseCommonSearchInput,
    props: {
      label: 'Asset Name',
      tagInputProps: {
        class: 'w-[216px]',
      },
    },
    ext: {
      key: 'text',
      label: '',
      default: store.text || '',
      isAllowClose: false,
    },
  },
  {
    name: components.Select,
    props: {
      list: TASK_TYPE_LIST,
      title: 'Task Type',
      clearable: true,
    },
    ext: {
      key: 'taskType',
      label: '',
      default: store.taskType || '',
      isAllowClose: false,
    },
  },
  store.taskType === 'auto' && {
    name: components.Select,
    props: {
      list: store.ruleList,
      title: 'Rule Name',
      clearable: true,
    },
    ext: {
      key: 'ruleId',
      label: '',
      default: getUrlParams('ruleId') || '',
      isAllowClose: false,
    },
  },
  {
    name: components.Select,
    props: {
      list: MEDIA_SELECT_OPTIONS,
      title: 'Media source',
      clearable: true,
    },
    ext: {
      key: 'channel',
      label: '',
      default: store.channel || '',
      isAllowClose: false,
    },
  },
  {
    name: components.Select,
    props: {
      list: FORMAT_TYPE_OPTIONS,
      title: 'Type',
      clearable: true,
    },
    ext: {
      key: 'formatType',
      label: '',
      default: store.formatType || '',
      isAllowClose: false,
    },
  },
  {
    name: components.Select,
    props: {
      list: store?.dateType === 'start_date'
        ? getTaskFilterStatus()
        : [{
          label: 'Successful',
          value: 2,
        }],
      title: 'Status',
      clearable: true,
    },
    ext: {
      key: 'status',
      label: '',
      default: store.status || '',
      isAllowClose: false,
    },
  },
  {
    name: components.Select,
    props: {
      list: store.taskBatchList,
      title: 'Task Name',
      multiple: true,
      button: (textArr: string[] | string) => {
        if (textArr.length === 0) {
          return 'All';
        }
        if (!Array.isArray(textArr)) return textArr;
        return textArr.length > 2 ? textArr.length : textArr.join(',');
      },
    },
    ext: {
      key: 'directories',
      label: '',
      default: store.directories || [],
      isAllowClose: false,
    },
  },
].filter(Boolean) as IFormDynamicItem[];
