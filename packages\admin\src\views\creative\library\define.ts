import type { StatusType } from './type';
import { ImportLabel } from 'common/service/creative/library/manage-labels';
import type { MediaType } from '@/views/trade/ads_creation/common/template/type';
import { FORMAT_TYPE_MAP } from '@/views/creative/library/compose/const';
import dayjs from 'dayjs';


export { MediaType };

export const AixTab = {
  art_hub: 'Art Hub',
  gdrive: 'Google Drive',
};

export const MediaTab = {
  all: 'All',
  google: 'Google',
  facebook: 'Facebook',
  youtube: 'Youtube',
};

export type TTabItem = {
  label: string;
  value: string;
  title?: string;
};

export type TSelectItem = {
  label: string;
  value?: string;
  disabled?: boolean;
  checkAll?: boolean;
};


export const LibraryType = {
  Aix: 'iegg',
  Media: 'advertise',
} as const;

export type TSimpleAsset = {
  AssetID: string;
  AssetName: string;
  // assetName 带了格式，一般用AssetName即可，originName不带格式
  originName?: string;
  type?: 'video' | 'image' | 'other' | 'text',
  materialExt?: any
};

export type MediaOptionItem = { text: MediaType };
export const MEDIA_OPTIONS: Record<string, MediaOptionItem> = {
  1: {
    text: 'Google',
  },
  2: {
    text: 'Facebook',
  },
  3: {
    text: 'TikTok',
  },
  4: {
    text: 'Twitter',
  },
  // 5: {
  //   text: 'ASA',
  // },
  // 6: {
  //   text: 'Reddit',
  // },
  7: {
    text: 'Unity',
  },
  8: {
    text: 'Snapchat',
  },
  9: {
    text: 'AppLovin',
  },
};

export const TASK_TYPE_OPTIONS: Record<string, { text: string }> = {
  all: { text: 'ALL' },
  auto: { text: 'Automatic' },
  manual: { text: 'Manual' },
};

export enum MediaOptionEnum {
  Google = 1,
  Facebook,
  TikTok,
  Twitter,
  ASA,
  Reddit,
  Unity,
  Snapchat = 8,
}

export const STATUS_OPTIONS = {
  0: {
    text: 'ALL',
    type: 'notservering',
    time_text: 'Date of Entry',
    time_key: 'create_date',
  },
  1: {
    text: 'Synced',
    type: 'success',
    time_text: 'Date of Online',
    time_key: 'online_date',
  },
  2: {
    text: 'Not Synced',
    type: 'error',
    time_text: 'Date of Offline',
    time_key: 'offline_date',
  },
} as const;


export const UPLOAD_MEDIA_STATUS: Record<string, StatusType> = {
  1: {
    iconType: 'loading',
    text: 'In Progress',
    operations: ['cancel'],
  },
  2: {
    iconType: 'success',
    text: 'Successful',
  },
  3: {
    iconType: 'error',
    text: 'Failed',
    error: true,
    operations: ['resume'],
  },
  4: {
    iconType: 'warning',
    text: 'Cancelled',
    operations: ['resume'],
  },
  5: {
    iconType: 'pending',
    text: 'Pending',
    tooltip: 'YouTube API quota limit reached\nQuota will be reset and upload task will be restarted at 3pm every day',
    noSelect: true,
    operations: ['cancel'],
  },
};

export enum UploadStatusEnum {
  loading = 1,
  success,
  error,
  warning,
}


export const STATUS_SELECT_OPTIONS = parseSelectOptions<string>(STATUS_OPTIONS);

export const MEDIA_SELECT_OPTIONS = parseSelectOptions<MediaType>(MEDIA_OPTIONS);
export const TASK_TYPE_LIST = parseSelectOptions<'Automatic' | 'Manual' | 'ALL'>(TASK_TYPE_OPTIONS);

export type NamingType = 'gpp' | 'pubgm';

export function parseSelectOptions<T>(options: Record<string, any>) {
  return Object.entries(options)
    .map(([k, v]) => ({
      value: k,
      label: v.text as T,
    }));
}

export type TValidateTableItem = {
  AssetID: string;
  AssetName: string;
  hasNamingErr: boolean;
  hasMetaErr: boolean;
  hasMetaWarn: boolean;
  tips: string | string[]
};

export const FORMAT_TYPE_OPTIONS = parseSelectOptions<string>(FORMAT_TYPE_MAP);

export const getTaskFilterStatus = () => {
  const list: TTabItem[] = [];
  Object.keys(UPLOAD_MEDIA_STATUS)
    .forEach((key) => {
      if (!UPLOAD_MEDIA_STATUS[key].noSelect) {
        list.push({
          label: `${UPLOAD_MEDIA_STATUS[key].text}`,
          value: key,
          title: `${UPLOAD_MEDIA_STATUS[key].text}`,
        });
      }
    });
  return list;
};


export enum Status {
  UnChanged = 'UnApply',
  Changing = 'Changing',
  AixSuccess = 'Change Aix Success',
  ChangingMediaName = 'Changing Media Name',
  Success = 'Success',
  Failed = 'Failed',
}


export type TNameItem = {
  id: string,
  index: number,
  originName: string;
  newName: string;
  status: Status;
};


export type TLabelStatus =
  | 'pending'
  | 'uploading'
  | 'parse_failed'
  | 'failed'
  | 'success';
export type TLabel = ImportLabel & {
  status: TLabelStatus;
  message?: string;
};

type StatusConfig = {
  iconType: string;
  text: string;
  theme: string;
  error?: true;
};

export const StatusConfigs: Record<TLabelStatus, StatusConfig> = {
  pending: {
    iconType: 'pending-gray',
    text: 'To be imported',
    theme: 'text',
  },
  uploading: {
    iconType: 'loading',
    text: 'Importing',
    theme: 'text',
  },
  parse_failed: {
    iconType: 'warning',
    text: 'Parsing failed',
    theme: 'warning',
    error: true,
  },
  failed: {
    iconType: 'error',
    text: 'Import failed',
    theme: 'danger',
    error: true,
  },
  success: {
    iconType: 'success',
    text: 'Import succeeded',
    theme: 'success',
  },
};


export type TKeyAttribute = {
  key: string;
  title: string;
};

// 排序权重，数字越大越靠前显示
export const qualityMap: Record<TLabelStatus, number> = {
  failed: 5,
  uploading: 4,
  parse_failed: 3,
  success: 2,
  pending: 1,
};


export type TTaskItem = {
  id: number;
  media: MediaType | '',
  accounts: string[] | string,
  language?: string,
  campaign_id?: string,
  creative_set_name?: string,
  countries?: string[],
};

export const AssertTypeList: Record<number, string> = {
  0: 'All',
  1: 'Video',
  2: 'Image',
  3: 'HTML',
};

export const basePresets = {
  Today: [dayjs()
    .toDate(), dayjs()
    .toDate()],
  'Last Day': [dayjs()
    .subtract(1, 'day')
    .toDate(), dayjs()
    .subtract(1, 'day')
    .toDate()],
  'Last 7 days': [dayjs()
    .subtract(6, 'day')
    .toDate(), dayjs()
    .toDate()],
  'Last 14 days': [dayjs()
    .subtract(13, 'day')
    .toDate(), dayjs()
    .toDate()],
  'Last 30 days': [dayjs()
    .subtract(29, 'day')
    .toDate(), dayjs()
    .toDate()],
  'This month': [dayjs()
    .startOf('month')
    .toDate(), dayjs()
    .toDate()],
  'Last month': [dayjs()
    .add(-1, 'month')
    .startOf('month')
    .toDate(), dayjs()
    .add(-1, 'month')
    .date(30)
    .toDate()],
  'Year to date': [dayjs()
    .startOf('year')
    .toDate(), dayjs()
    .toDate()],
};
