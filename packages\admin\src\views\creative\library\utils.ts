import type { ISearchValue } from 'common/components/SearchBox/type';
import dayjs from 'dayjs';

export const monthOffset = (startCloudUploadTime: string, time2: string) => {
  const startCloudUploadDate = dayjs(startCloudUploadTime);
  const sixMonthDate = dayjs(time2).add(-6, 'month');
  // 判断当前日期是否超过6个月
  if (startCloudUploadDate.isBefore(sixMonthDate)) {
    return 7;
  }
  return 0;
};
export function getSeraChInfo(searchInfo: ISearchValue[]) {
  // 搜索框 这里只有一个字段, 所以取第一项就可以了
  const { condition = [], searchType = 1 } = searchInfo[0] || {};
  const filterCondition = condition.filter((item: string) => (item ?? '').length > 0);
  let names = undefined;
  let text = '';
  if (filterCondition.length >= 1) {
    names = JSON.stringify(filterCondition);
    if (filterCondition.length === 1) {
      const [newText] = filterCondition;
      text = newText;
    } else {
      text =  filterCondition.join('\n');
    }
  }
  return {
    names,
    text,
    searchType: searchType === 1 ? 2 : searchType === 2 ? 4 : searchType,
  };
}

export function getSerachInfo(searchData: { assetNameList: string[], searchType: number }) {
  const { assetNameList = [], searchType = 2 } = searchData || {};
  let names = undefined;
  let text  = '';
  if (assetNameList.length >= 1) {
    names = JSON.stringify(assetNameList);
    text = assetNameList.length ===  1 ? assetNameList[0] : assetNameList.join('\n');
  }
  return {
    names,
    text,
    searchType,
  };
}


const MAX_SHOW_PATH_LENGTH = 60;
export function transFullPathName(name: string) {
  const pathName = name.replace(/,/g, '>');
  if (pathName.length <= MAX_SHOW_PATH_LENGTH) {
    return pathName;
  }
  // 裁剪路径，尽量保留子目录名
  return `...${pathName.slice(pathName.length - MAX_SHOW_PATH_LENGTH)}`;
}
