<template>
  <CommonView
    class="h-full"
    hide-right
  >
    <template #views>
      <t-loading :loading="isInitLoaing || isFetchUniqueIdLoading" show-overlay class="w-full h-full">
        <div class="w-full h-full bg-white rounded-large">
          <Upload
            v-if="!tableDataList.length"
            @save-to-indexed-d-b="onSaveToIndexedDB"
          />
          <FileListTable
            v-else
            @save-to-indexed-d-b="onSaveToIndexedDB"
          />
          <RealTimeUploadDialog v-if="realTimeUploadDialogVisible" />
          <UnfinishedTaskTipDialog v-if="unfinishedTaskDialogVisible" />
          <CloudDriveAuthExpired v-if="dropboxAuthExpiredDialogVisible" />
          <HistoryFileTipsDialog v-if="historyFileTipsDialogVisible" />
        </div>
      </t-loading>
    </template>
  </CommonView>
</template>
<script lang="ts" setup>
import { storeToRefs  } from 'pinia';
import CommonView from 'common/components/Layout/CommonView.vue';
import Upload from './component/Upload.vue';
import FileListTable from './component/file-list-table/Index.vue';
import RealTimeUploadDialog from './component/dialog/RealTimeUpload.vue';
import UnfinishedTaskTipDialog from './component/dialog/UnfinishedTaskTip.vue';
import HistoryFileTipsDialog from './component/dialog/HistoryFileTips.vue';
import CloudDriveAuthExpired from './component/dialog/CloudDriveAuthExpired.vue';
import { useCreativeNameGeneratorStore } from '@/store/creative/name-generator/index.store';
import { useCreativeNameGeneratorDropboxStore } from '@/store/creative/name-generator/dropbox.store';
import { useWatchGameChange } from 'common/compose/request/game';
import type { SuccessContext } from 'tdesign-vue-next';
import { DEFAULT_CREATIVE_NAME_FULE } from '@/store/creative/name-generator/const';
import { getUniqueIdCodeService } from 'common/service/creative/name-generator';
import { useGlobalGameStore } from '@/store/global/game.store';
import { uniq, difference } from 'lodash-es';
import { getFormatType } from '@/store/creative/name-generator/utils';
import { useCreativeNameGeneratorCacheStore } from '@/store/creative/name-generator/cache.store';
import { useLoading } from 'common/compose/loading';

const {
  isLoading: isFetchUniqueIdLoading,
  showLoading: showFetchUniqueIdLoading,
  hideLoading: hideFetchUniqueIdLoading,
} = useLoading();

const gameStore = useGlobalGameStore();
const { gameCode } = storeToRefs(gameStore);

const creativeNameGeneratorStore = useCreativeNameGeneratorStore();
const { init, addCreativeFile }  = creativeNameGeneratorStore;
const {
  realTimeUploadDialogVisible, tableDataList, isInitLoaing, unfinishedTaskDialogVisible,
  historyFileTipsDialogVisible,
} = storeToRefs(creativeNameGeneratorStore);

const dropboxStore = useCreativeNameGeneratorDropboxStore();
const { dropboxAuthExpiredDialogVisible } = storeToRefs(dropboxStore);

const creativeNameGeneratorCacheStore = useCreativeNameGeneratorCacheStore();
const { appendToCacheUniqueIdMap } = creativeNameGeneratorCacheStore;


// 把文件存到indexdb
const onSaveToIndexedDB = async (ctx: SuccessContext) => {
  showFetchUniqueIdLoading();
  const plianGroupByRowList = tableDataList.value.filter(item => item.isGroupBy);

  // 历史的文件中，有哪些类型（formatType）？
  const historyFormatTypeList = uniq(plianGroupByRowList.map(item => item.format_type));
  // 历史的文件中，每个类型分别对应的唯一id是什么？
  const historyUniqueIdMap: Record<string, number> = {};
  plianGroupByRowList.forEach((item) => {
    historyUniqueIdMap[item.format_type] = item.unique_id;
  });
  // 当前上传的文件中， 总共有哪些formmatType
  const currentFormatTypeList = uniq((ctx.fileList ?? []).map(item => getFormatType(item.raw!)));
  // 当前选择文件列表的formatType 和 历史文件列表中的formatType的差集
  const diffFormatTypeList = difference(currentFormatTypeList, historyFormatTypeList);
  const currentUniqueIdMap: Record<string, number> = {};
  // 每个formatType的UniqueId 不一样， 只有当前选择的文件的formatType 和 历史中的文件的formatType 存在差集的时候，才去拉新的
  if (diffFormatTypeList.length > 0) {
    // 历史的文件中，有哪些UniqueId
    const historyUniqueIdList = Object.values(historyUniqueIdMap);
    // 如果历史的文件中有最新的UniqueId， 就传给接口，取这个最新的UniqueId后面的
    const currentLatestUniqueId = !historyUniqueIdList.length ? 0 : Math.max(...historyUniqueIdList);
    const latsetUniqueIdRes = await getUniqueIdCodeService({
      game_code: gameCode.value,
      count: diffFormatTypeList.length,
      ...(currentLatestUniqueId ? { latest: currentLatestUniqueId } : {}),
    });
    latsetUniqueIdRes.list.forEach(item => appendToCacheUniqueIdMap({
      label: item.unique_id_code, value: item.unique_id,
    }));
    diffFormatTypeList.forEach((item, index) => {
      currentUniqueIdMap[item] = latsetUniqueIdRes.list[index].unique_id;
    });
  }

  const uniqueIdMap = {
    ...historyUniqueIdMap,
    ...currentUniqueIdMap,
  };
  // 先获取唯一id
  const list = (ctx.fileList ?? []).map(item => ({
    ...DEFAULT_CREATIVE_NAME_FULE,
    fileObject: item.raw as File,
    original_name: item.name as string,
    format_type: getFormatType(item.raw as File),
  }));
  hideFetchUniqueIdLoading();
  await addCreativeFile(list as any, uniqueIdMap);
};


useWatchGameChange(async () => {
  init();
});

</script>
<style lang="scss" scoped>

</style>
