<template>
  <CommonView
    :need-back="true"
    :router-index="-2"
    :hide-right="true"
    :form-props="formProps"
  >
    <template #views>
      <DataContainer
        class="pt-[16px] rounded-large mb-[24px]"
        :total="store.pageTotal"
        :page-size="pageInfo.pageSize"
        :data="store.taskList"
        :loading="store.isLoading"
        :default-page="pageInfo.pageIndex"
        :pagination-disabled="store.isLoading"
        :page-size-options="[10, 20, 50, 100]"
        :is-download-loading="isDownloadLoading"
        @on-page-change="onPageChange"
        @on-download-file="downloadTaskData"
        @on-show-metric-select="() => tableRef?.showMetricsSelect?.()"
      >
        <Table
          ref="tableRef"
          v-model:display-columns="displayCols"
          :data="store.taskList"
          row-key="id"
          :columns="cols"
          max-height="1070px"
          :resizable="true"
          :bordered="false"
          :table-layout="'fixed'"
          :cell-empty-content="''"
          :loading="store.isLoading"
          class="border-collapse no-vertical-borders"
        />
      </DataContainer>
    </template>
  </CommonView>
</template>

<script lang="ts" setup>
import {  storeToRefs } from 'pinia';
import { ref, computed, onBeforeUnmount, watch } from 'vue';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import CommonView from 'common/components/Layout/CommonView.vue';
import DataContainer from 'common/components/Layout/DataContainer.vue';
import Table from 'common/components/table';
import { getNameGenTaskFilterConfig, normalizeOptions } from './config/task-filter';
import { resetTypeMap } from 'common/components/FormContainer';
import { useNameGeneratorTaskStore } from '@/store/creative/name-generator/task-list.store';
import { getStatusElement } from './utils';
import * as XLSX from 'xlsx';
import { CREATIVE_UPLOAD_STATUS } from 'common/service/creative/name-generator/const';
import { TASK_LIST_PAGE_INFO, TASK_LIST_DEFAULT_FORM_VALUE } from '@/store/creative/name-generator/const';
import { cloneDeep } from 'lodash-es';
import { useWatchGameChange } from 'common/compose/request/game';
import { getTaskListService } from 'common/service/creative/name-generator';
import { useLoading } from 'common/compose/loading';
import { TASK_LIST_DEFAULT_COLS } from './const';
import { Tooltip } from 'tdesign-vue-next';

// Register dayjs plugins
dayjs.extend(isBetween);

// Table ref
const tableRef = ref();

// Use the store
const store = useNameGeneratorTaskStore();
const { updateFormModelValue, getTaskList } = store;
const { formModelValue, pageInfo, queryTaskListPayload } = storeToRefs(store);

// Define table columns
const cols = [
  {
    colKey: 'asset_name', title: 'Asset Name', ellipsis: true,
    width: 500, required: true, fixed: 'left',
  },
  { colKey: 'asset_type', title: 'Asset Type', width: 120, ellipsis: true },
  {
    colKey: 'start_date',
    title: 'Start Date',
    width: 200,
    ellipsis: true,
    cell: (h: any, { row }: { row: any }) => getFormattedCellValue(row, 'start_date'),
  },
  {
    colKey: 'synced_date',
    title: 'Synced Date',
    width: 200,
    ellipsis: true,
    cell: (h: any, { row }: { row: any }) => getFormattedCellValue(row, 'synced_date'),
  },
  {
    colKey: 'creator',
    title: 'Uploader',
    width: 200,
    ellipsis: true,
    cell: (h: any, { row }: { row: any }) => row.creator || '-',
  },
  {
    colKey: 'storage_folder',
    title: 'Storage Folder',
    width: 200,
    ellipsis: true,
    cell: (h: any, { row }: { row: any }) => getFormattedCellValue(row, 'storage_folder'),
  },
  {
    colKey: 'storage_path',
    title: 'Storage Path',
    ellipsis: true,
    width: 400,
    cell: (h: any, { row }: { row: any }) => getFormattedCellValue(row, 'storage_path'),
  },
  {
    colKey: 'upload_status',
    title: 'Status',
    width: 140,
    fixed: 'right',
    required: true,
    ellipsis: true,
    cell: (h: any, { row }: { row: any }) => {
      if (!row) return '';
      try {
        return getStatusElement(row);
      } catch (error) {
        return row.upload_status || '';
      }
    },
  },
  // Hidden/collapsed fields
  { colKey: 'original_name', title: 'Original Name', width: 200, ellipsis: true },
  {
    colKey: 'dropbox_preview_link', title: 'Dropbox Preview Link', width: 250,
    cell: (h: Function, { row }: { row: any }) => {
      const link = getFormattedCellValue(row, 'dropbox_preview_link');
      if (link && link !== '-') {
        const linkEl = h('a',
          { href: link, class: 't-text-ellipsis  inline-block text-brand hover:text-link w-full', target: '_blank' },
          [link],
        );
        return h(Tooltip, {
          attach: 'body',
          content: getFormattedCellValue(row, 'dropbox_preview_link'),
          default: () => linkEl,
        });
      }
      return link;
    },
    // ellipsis: {
    //   props: { attach: 'body' },
    //   content: (h: Function, { row }: { row: any }) =>  getFormattedCellValue(row, 'dropbox_preview_link'),
    // },
  },
  { colKey: 'game_name', title: 'Game Name', width: 150, ellipsis: true },
  { colKey: 'unique_id_code', title: 'Unique ID', width: 150, ellipsis: true },
  { colKey: 'upload_month', title: 'Upload Month', width: 120, ellipsis: true },
  { colKey: 'concept_name', title: 'Concept Name', width: 150, ellipsis: true },
  { colKey: 'version_number', title: 'Version Number', width: 120, ellipsis: true },
  { colKey: 'version_name', title: 'Version Name', width: 150, ellipsis: true },
  { colKey: 'production_stage', title: 'Production Stage', width: 150, ellipsis: true },
  { colKey: 'creative_type', title: 'Creative Type', width: 150, ellipsis: true },
  { colKey: 'language', title: 'Language', width: 120, ellipsis: true },
  {
    colKey: 'duration_render',
    title: 'Duration',
    width: 120,
    ellipsis: true,
  },
  {
    colKey: 'ratio_dimensions_render',
    title: 'Ratio/Dimensions',
    width: 150,
    ellipsis: true,
  },
  { colKey: 'source', title: 'Source', width: 120, ellipsis: true },
  { colKey: 'target', title: 'Target', width: 120, ellipsis: true },
  { colKey: 'producer', title: 'Producer', width: 120, ellipsis: true },
  { colKey: 'type_specifics', title: 'Type Specifics', width: 150, ellipsis: true },
  { colKey: 'playable_channel', title: 'Playable Channel', width: 150, ellipsis: true },
];

// Default display columns
const displayCols = ref([
  ...TASK_LIST_DEFAULT_COLS,
]);

// Utility function to format cell values consistently
function getFormattedCellValue(row: any, key: string) {
  if (!row) return '';
  let pathParts; let path;
  switch (key) {
    case 'start_date':
      return row.created_at ? dayjs(row.created_at).format('YYYY-MM-DD HH:mm:ss') : '-';
    case 'synced_date':
      return (row.upload_status === 'success' && row.updated_at)
        ? dayjs(row.updated_at).format('YYYY-MM-DD HH:mm:ss')
        : '-';
    case 'storage_folder':
      if (!row.upload_path) return '';
      pathParts = row.upload_path.split('/').filter(Boolean);
      return pathParts.length > 0 ? pathParts[pathParts.length - 1] : '';
    case 'storage_path':
      if (!row.upload_path) return '';
      path = row.upload_path.replace(/\//g, ' > ');
      return path.startsWith(' > ') ? path.substring(3) : path;
    case 'dropbox_preview_link':
      return (
        row.upload_status === CREATIVE_UPLOAD_STATUS.SUCCESS && row.dropbox_preview_link
      ) ? row.dropbox_preview_link : '-';
    // case 'duration':
    //   return renderDuration(row);
    default:
      return row[key] ?? '';
  }
}

// 切换时间类型时， 清空状态
watch(() => formModelValue.value.dateInfo.dateType, () => {
  updateFormModelValue({
    ...formModelValue.value,
    status: [],
  });
});

const formList = computed(() => getNameGenTaskFilterConfig({
  conceptNameOptions: normalizeOptions(store.conceptNameOptions),
  uploaderOptions: normalizeOptions(store.uploaderOptions),
  assetTypeOptions: normalizeOptions(store.assetTypeOptions),
  uploadStatusOptions: normalizeOptions(store.uploadStatusOptions[formModelValue.value.dateInfo.dateType] ?? []),
}));

const formProps = computed(() => ({
  formList: formList.value,
  modelValue: formModelValue.value,
  resetType: resetTypeMap.initial,
  'onUpdate:modelValue': updateFormModelValue,
  initModelValue: cloneDeep(TASK_LIST_DEFAULT_FORM_VALUE),
  onSubmit: () => {
    const { pageIndex } = TASK_LIST_PAGE_INFO;
    store.setPages(pageIndex, pageInfo.value.pageSize);
    getTaskList();
  },
  onReset: () => {
    tableRef.value.setDisplayCols([...TASK_LIST_DEFAULT_COLS]);
    const { pageIndex, pageSize } = TASK_LIST_PAGE_INFO;
    store.setPages(pageIndex, pageSize);
    getTaskList();
  },
}));

const onPageChange = (current: number, pageInfo: any) => {
  store.setPages(current, pageInfo.pageSize);
};


onBeforeUnmount(() => {
  updateFormModelValue(cloneDeep(TASK_LIST_DEFAULT_FORM_VALUE));
  const { pageIndex, pageSize } = TASK_LIST_PAGE_INFO;
  store.setPages(pageIndex, pageSize);
});

useWatchGameChange(async () => {
  updateFormModelValue(cloneDeep(TASK_LIST_DEFAULT_FORM_VALUE));
  const { pageIndex, pageSize } = TASK_LIST_PAGE_INFO;
  store.setPages(pageIndex, pageSize);
  store.init();
});


// Download handler
const STATUS_TEXT_MAP: Record<string, string> = {
  [CREATIVE_UPLOAD_STATUS.WAITING]: 'Waiting',
  [CREATIVE_UPLOAD_STATUS.UPLOADING]: 'In Progress',
  [CREATIVE_UPLOAD_STATUS.SUCCESS]: 'Successful',
  [CREATIVE_UPLOAD_STATUS.ERROR]: 'Failed',
  [CREATIVE_UPLOAD_STATUS.CANCELLED]: 'Cancelled',
};

const { isLoading: isDownloadLoading, showLoading: showDownloadLoading, hideLoading: hideDownloadLoading } = useLoading();
const downloadTaskData = async () => {
  if (!tableRef.value) return;
  showDownloadLoading();
  // Get all data instead of just the current page for download
  // const allData = JSON.parse(JSON.stringify(store.taskList));
  // 这里要拉取全部
  const res = await getTaskListService({
    ...queryTaskListPayload.value,
    page_index: 1,
    page_size: store.pageTotal,
  });
  const allData = res?.list ?? [];
  hideDownloadLoading();

  // Get selected columns and their titles
  const selectedColKeys = displayCols.value;
  const colMap: Record<string, string> = cols.reduce((acc: Record<string, string>, col) => {
    acc[col.colKey] = col.title;
    return acc;
  }, {});

  // Format data for Excel download: only include selected columns
  const formattedData = allData.map((item: any) => {
    const row: Record<string, any> = {};
    selectedColKeys.forEach((key: string) => {
      if (key === 'upload_status') {
        // Export the status text as shown on the page (no icon)
        row[colMap[key] || key] = STATUS_TEXT_MAP[String(item.upload_status)] || item.upload_status || '';
      } else {
        row[colMap[key] || key] = getFormattedCellValue(item, key);
      }
    });
    return row;
  });

  // Create worksheet and workbook
  const worksheet = XLSX.utils.json_to_sheet(formattedData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Tasks');

  // Generate and trigger download
  const filename = `name_generator_task_${dayjs().format('YYYY-MM-DD')}.xlsx`;
  XLSX.writeFile(workbook, filename);
};
</script>
