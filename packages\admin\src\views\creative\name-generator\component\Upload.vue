<template>
  <div class="w-full h-full flex justify-center p-[24px] items-center relative">
    <SvgBtn
      class=" absolute right-[24px] top-[24px]"
      name="task-list"
      label="Task list"
      @click="gotoCreativeNameGeneratorTaskList"
    />
    <t-upload
      class=" max-w-full max-h-full"
      theme="custom"
      multiple
      draggable
      :disabled="isSaveFileToIDBLoaing"
      :request-method="customUploadMethod"
      @success="onSuccess"
    >
      <template #dragContent>
        <div
          v-if="isSaveFileToIDBLoaing"
          class=" flex flex-col gap-y-[8px]"
          :class="{ 'cursor-not-allowed': isSaveFileToIDBLoaing }"
        >
          <t-loading />
          <Text color="#747d98" content="Uploading File" />
        </div>
        <template v-else>
          <div class="inline-block">
            <img :src="UploadFilePng">
          </div>
          <!-- 文本 -->
          <div class=" flex flex-col gap-y-[8px]  items-center mt-[56px]">
            <Text content="Upload / Drag File To This Area To Upload" />
            <Text size="small" color="#747d98" content="Max 512MB/file, Max 10 files" />
          </div>
        </template>
      </template>
    </t-upload>
  </div>
  <!-- 图标 -->
</template>
<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import UploadFilePng from '../asset/upload-file.png';
import Text from 'common/components/Text';
import { useCreativeNameGeneratorStore } from '@/store/creative/name-generator/index.store';
import type { SuccessContext } from 'tdesign-vue-next';
import SvgBtn from 'common/components/SvgIcon/SvgBtn.vue';
import { useGoto } from '@/router/goto';

const emit = defineEmits(['saveToIndexedDB']);

const {  gotoCreativeNameGeneratorTaskList } = useGoto();

const creativeNameGeneratorStore = useCreativeNameGeneratorStore();
const { isSaveFileToIDBLoaing } = storeToRefs(creativeNameGeneratorStore);


// 这里设计的有点蠢，选择多个文件时，形参中不能一次性返回选中的文件， 而且回调会被调用用多次（并行）， 不好控制
// 由于时本地读写indexedDB， 所以直接把逻辑放到onSuccess回调里面，可以一次性拿到选择的文件
const customUploadMethod = () => Promise.resolve({ status: 'success' });

const onSuccess = async (ctx: SuccessContext) => {
  emit('saveToIndexedDB', ctx);
};


</script>
<style lang="scss" scoped>
:deep(.t-upload__dragger) {
  background: #fbfbfb;
  &:hover {
    background: #f0f7ff;
  }
  width: 880px;
  height: 466px;
  max-width: 100%;
  max-height: 100%;
  .t-upload__trigger {
    text-align: center;
  }
}
</style>
