<template>
  <BaseDialog
    :visible="dropboxAuthExpiredDialogVisible"
    theme="warning"
    title="Cloud Drive Auth Expired"
    confirm-text="Go To AiX Library"
    placement="center"
    @confirm="onConfirm"
    @close="onClose"
  >
    <Text
      class="my-[16px]"
      content="Please go to AiX Library to Re-authorize."
      color="#747d98"
    />
  </BaseDialog>
</template>
<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { useCreativeNameGeneratorDropboxStore } from '@/store/creative/name-generator/dropbox.store';
import BaseDialog from 'common/components/Dialog/Base';
import Text from 'common/components/Text';
import { useGoto } from '@/router/goto';

const { gotoCreativeLibrary } = useGoto();

const  dropboxStore = useCreativeNameGeneratorDropboxStore();
const { setDropboxAuthExpiredDialogVisible } = dropboxStore;
const { dropboxAuthExpiredDialogVisible } = storeToRefs(dropboxStore);

const onClose =  () => {
  setDropboxAuthExpiredDialogVisible(false);
};

const onConfirm =  () => {
  setDropboxAuthExpiredDialogVisible(false);
  // 跳转到素材库页面
  gotoCreativeLibrary();
};


</script>
