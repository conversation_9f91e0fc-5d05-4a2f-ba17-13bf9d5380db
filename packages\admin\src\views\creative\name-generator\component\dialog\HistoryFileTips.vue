<template>
  <BaseDialog
    :visible="historyFileTipsDialogVisible"
    theme="warning"
    title="Unsaved Progress Detected"
    confirm-text="Confirm Restore"
    placement="center"
    @confirm="onConfirm"
    @close="onClose"
  >
    <Text
      class="my-[16px]"
      content="We found incomplete work from your last session. Would you like to restore it?"
      color="#747d98"
    />
  </BaseDialog>
</template>
<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { useCreativeNameGeneratorStore } from '@/store/creative/name-generator/index.store';
import BaseDialog from 'common/components/Dialog/Base';
import Text from 'common/components/Text';

const  creativeNameGeneratorStore = useCreativeNameGeneratorStore();
const { setHistoryFileTipsDialogVisible, clearCreativeFileList } = creativeNameGeneratorStore;
const { historyFileTipsDialogVisible } = storeToRefs(creativeNameGeneratorStore);

const onClose = async () => {
  clearCreativeFileList();
  setHistoryFileTipsDialogVisible(false);
};

const onConfirm = async () => {
  setHistoryFileTipsDialogVisible(false);
};

</script>
<style lang="scss" scoped>
</style>
