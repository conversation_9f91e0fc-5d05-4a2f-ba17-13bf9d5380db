<template>
  <div>
    <t-dialog
      header="Real-Time Upload Task"
      :visible="realTimeUploadDialogVisible"
      attach="body"
      placement="center"
      :dialog-style="{
        width: 'auto'
      }"
      @close="onClose"
    >
      <template #closeBtn>
        <SvgIcon
          name="close"
          size="10px"
          color="var(--aix-text-color-gray-primary)"
          :class="{
            'cursor-not-allowed': isReadyRealTimeTaskLoading
          }"
        />
      </template>
      <div class="max-w-[1000px]">
        <t-table
          :columns="columns"
          row-key="id"
          :data="realTimeTaskAssetList"
          attach="body"
          :loading="isReadyRealTimeTaskLoading"
        />
        <p class="mt-[16px] flex justify-center">
          <Text
            content="Please stay on this page until all assets are fully uploaded."
            theme="info"
          />
        </p>
      </div>
      <template #footer>
        <div class=" flex justify-end">
          <t-button
            theme="default"
            :loading="isBreakRealTimeTaskLoading"
            @click="onClose"
          >
            <!-- 任务已经完成 -->
            <template v-if="!taskIsRuning">Close</template>
            <template v-else>Stop & Close</template>
          </t-button>
        </div>
      </template>
    </t-dialog>
    <!-- 弹窗关闭前的提示 -->
    <BaseDialog
      v-model:visible="beforeCloseConfirmDialogVisible"
      theme="warning"
      title="Confirm Close?"
      confirm-text="Confirm"
      placement="center"
      @confirm="beforeCloseConfirmDialogConfirm"
    >
      <Text
        class="my-[16px]"
        content="Exiting now will ​​stop the upload​​ and ​​reset the generator​​."
        color="#747d98"
      />
    </BaseDialog>
  </div>
</template>
<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { h, onBeforeUnmount, onBeforeMount, ref, computed } from 'vue';
import BaseDialog from 'common/components/Dialog/Base';
import Text from 'common/components/Text/Text.vue';
import type { PrimaryTableCol } from 'tdesign-vue-next';
import { useCreativeNameGeneratorStore } from '@/store/creative/name-generator/index.store';
import { useCreativeNameGeneratorDropboxStore } from '@/store/creative/name-generator/dropbox.store';
import { useCreativeNameGeneratorRealTimeUploadStore } from '@/store/creative/name-generator/realtime-upload.store';
import { CREATIVE_UPLOAD_STATUS } from 'common/service/creative/name-generator/const';
import { Link, Loading } from 'tdesign-vue-next';
import { difference } from 'lodash-es';
import { IRealTimeTaskAssetRecord } from '@/store/creative/name-generator/type';
import SvgIcon from 'common/components/SvgIcon';
import { getStatusElement } from '../../utils';

const  creativeNameGeneratorStore = useCreativeNameGeneratorStore();
const { setRealTimeUploadDialogVisible } = creativeNameGeneratorStore;
const { realTimeUploadDialogVisible, isReadyRealTimeTaskLoading } = storeToRefs(creativeNameGeneratorStore);

const  useRealTimeUploadStore = useCreativeNameGeneratorRealTimeUploadStore();
const {  cancelTask, retryTask, breakRealTimeTask, handlePageBeforeClose } = useRealTimeUploadStore;
const { realTimeTaskAssetList, isExecuteRealTimeTaskLoading, isBreakRealTimeTaskLoading } = storeToRefs(useRealTimeUploadStore);

const dropboxStore = useCreativeNameGeneratorDropboxStore();
const { dropboxInstance } = storeToRefs(dropboxStore);

// 实时上传弹窗关闭前的提示
const beforeCloseConfirmDialogVisible = ref(false);

// 正在执行状态变更操作的 任务id数组
const loadingTaskIdList = ref<number[]>([]);

const actionIsLoading = computed(() => loadingTaskIdList.value.length > 0);

const taskIsRuning = computed(() => (
  actionIsLoading.value || isExecuteRealTimeTaskLoading.value || isReadyRealTimeTaskLoading.value
));

const getActionElement = (row: IRealTimeTaskAssetRecord) => {
  const isLoading = loadingTaskIdList.value.includes(row.id);
  const commonProps: Record<string, any> = {
    theme: 'primary',
    hover: 'color',
    ...(isLoading ? {
      prefixIcon: () => h(Loading, { size: 'small' }),
    } : {}),
    disabled: isLoading,
  };

  const retryElement = h(Link, {
    ...commonProps, default: () => 'Retry',
    onClick: async () => {
      console.log('Retry');
      loadingTaskIdList.value.push(row.id);
      await retryTask(dropboxInstance.value!, row);
      loadingTaskIdList.value = [...difference(loadingTaskIdList.value, [row.id])];
    },
  });
  const cancelElement = h(Link, {
    ...commonProps, default: () => 'Cancel',
    onClick: async () => {
      loadingTaskIdList.value.push(row.id);
      await cancelTask(row);
      loadingTaskIdList.value = [...difference(loadingTaskIdList.value, [row.id])];
    },
  });
  const notAllowAction = h(Link, { theme: 'primary',  variant: 'text', disabled: true, default: () => '--' });
  return {
    [CREATIVE_UPLOAD_STATUS.CANCELLED]: retryElement,
    [CREATIVE_UPLOAD_STATUS.UPLOADING]: notAllowAction,
    [CREATIVE_UPLOAD_STATUS.SUCCESS]: notAllowAction,
    [CREATIVE_UPLOAD_STATUS.ERROR]: retryElement,
    [CREATIVE_UPLOAD_STATUS.WAITING]: cancelElement,
  }[row.upload_status!];
};

const columns = ref<PrimaryTableCol<IRealTimeTaskAssetRecord>[]>([
  {
    title: 'Name',
    colKey: 'asset_name',
    cell: (_h: Function, { row }) => row.asset_name,
    ellipsis: {
      attach: 'body',
    },
  },
  {
    title: 'Status',
    colKey: 'upload_status',
    cell: (_h: Function, { row }) => getStatusElement({ upload_status: row.upload_status! }),
    width: 200,
  },
  {
    title: 'Actions',
    colKey: 'actions ',
    cell: (_h: Function, { row }) => getActionElement(row),
    width: 140,
  },
]);


const onClose = () => {
  // 判断是否允许关闭
  if (taskIsRuning.value) {
    beforeCloseConfirmDialogVisible.value = true;
  } else {
    setRealTimeUploadDialogVisible(false);
  }
  console.log('onClose');
};

const beforeCloseConfirmDialogConfirm = async () => {
  // 在这里执行 终止上传的逻辑
  beforeCloseConfirmDialogVisible.value = false;
  await breakRealTimeTask();
  setRealTimeUploadDialogVisible(false);
};

onBeforeMount(() => {
  window.addEventListener('beforeunload', handlePageBeforeClose);
});

onBeforeUnmount(() => {
  window.removeEventListener('beforeunload', handlePageBeforeClose);
});

</script>
<style lang="scss" scoped>
</style>
