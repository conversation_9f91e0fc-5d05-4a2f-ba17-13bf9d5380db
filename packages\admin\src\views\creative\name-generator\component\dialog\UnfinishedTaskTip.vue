<template>
  <BaseDialog
    :visible="unfinishedTaskDialogVisible"
    theme="warning"
    title="Unsaved Progress Detected"
    confirm-text="Confirm Restore"
    placement="center"
    @confirm="onConfirm"
    @close="onClose"
  >
    <Text
      class="my-[16px]"
      content="We found incomplete work from your last session. Would you like to restore it?"
      color="#747d98"
    />
  </BaseDialog>
</template>
<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { useCreativeNameGeneratorStore } from '@/store/creative/name-generator/index.store';
import BaseDialog from 'common/components/Dialog/Base';
import Text from 'common/components/Text';

const  creativeNameGeneratorStore = useCreativeNameGeneratorStore();
const { setUnfinishedTaskDialogVisible,  cancelUnfinishedTaskRealTimeTasks, runUnfinishedTaskRealTimeTasks } = creativeNameGeneratorStore;
const { unfinishedTaskDialogVisible } = storeToRefs(creativeNameGeneratorStore);

const onClose = async () => {
  cancelUnfinishedTaskRealTimeTasks();
  setUnfinishedTaskDialogVisible(false);
};

const onConfirm = async () => {
  setUnfinishedTaskDialogVisible(false);
  runUnfinishedTaskRealTimeTasks();
};

</script>
<style lang="scss" scoped>
</style>
