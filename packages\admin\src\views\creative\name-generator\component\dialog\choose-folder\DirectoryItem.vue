<template>
  <div>
    <span
      v-if="!isEditState"
      class=" inline-block w-full"
      @dblclick="onDoubelClick"
    >
      {{ props.label }}
    </span>
    <DirectoryNameInput
      v-else
      is-edit
      :base-path="parentPath"
      :default-value="newDirName || props.label"
      :old-dir-name="props.label"
      :update-callback="onUpdateDirnName"
      :check-name-error="onCancelEdit"
      @cancel-edit="onCancelEdit"
      @show-check-dir-name-loading="onShowCheckDirNameLoading"
      @hide-check-dir-name-loading="onHideCheckDirNameLoading"
      @input-change="onInputChange"
    />
  </div>
</template>
<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { PropType, computed, unref } from 'vue';
import DirectoryNameInput from './DirectoryNameInput.vue';
import type { files } from 'dropbox/types/index';
import { Tree } from 'tdesign-vue-next';
import { useCreativeNameGeneratorDropboxStore } from '@/store/creative/name-generator/dropbox.store';


const emit = defineEmits([
  'showUpdateloading', 'hideUpdateLoading', 'showCheckDirNameLoading', 'hideCheckDirNameLoading',
  'updateEditState', 'inputChange',
]);
const props = defineProps({
  label: {
    type: String,
    default: '',
  },
  folderInfo: {
    type: Object as PropType<files.FolderMetadataReference>,
    default: () => undefined,
  },
  treeInstance: {
    type: Object as PropType<InstanceType<typeof Tree>>,
    default: () => undefined,
  },
  updateNode: {
    type: Function,
    default: undefined,
  },
  isEditState: {
    type: Boolean,
    default: false,
  },
  newDirName: {
    type: String,
    default: '',
  },
});

const isEditState = computed(() => unref(props.isEditState));
const newDirName = computed(() => unref(props.newDirName));

const dropboxStore = useCreativeNameGeneratorDropboxStore();
const { dropboxInstance } = storeToRefs(dropboxStore);

function getParentPath(path: string) {
  if (path === '/') {
    return '/'; // 根目录没有上层
  }
  // 去掉末尾的斜杠（如果有）
  const trimmedPath = path.endsWith('/') && path.length > 1 ? path.slice(0, -1) : path;
  // 找最后一个斜杠的位置
  const lastSlashIndex = trimmedPath.lastIndexOf('/');
  if (lastSlashIndex === 0) {
    return '/'; // 父目录是根目录
  }
  // 返回父目录路径
  return trimmedPath.substring(0, lastSlashIndex);
}

// eslint-disable-next-line @typescript-eslint/no-non-null-asserted-optional-chain
const pathDisplay = computed(() => props.folderInfo?.path_display!);

const parentPath = computed(() => getParentPath(pathDisplay.value));

const onDoubelClick = () => {
  // isEdit.value = true;
  emit('updateEditState', true);
};


const onCancelEdit = () => {
  // isEdit.value = false;
  emit('updateEditState', false);
};

const onUpdateDirnName = async (dirName: string) => {
  emit('showUpdateloading');
  const newPath = `${parentPath.value}/${dirName}`.replace('//', '/');
  // 先更新
  try {
    const res = await dropboxInstance.value?.filesMoveV2({
      from_path: pathDisplay.value,
      to_path: newPath,
      autorename: false,
    });
      // 通知外部更新
    props?.updateNode?.(res?.result.metadata);
    // 关闭编辑的输入框
    // isEdit.value = false;
    emit('updateEditState', false);
    emit('hideUpdateLoading');
  } catch (e) {
    props?.updateNode?.(props.folderInfo);
    // 关闭编辑的输入框
    // isEdit.value = false;
    emit('updateEditState', false);
    emit('hideUpdateLoading');
  }
};


const onInputChange = (value: any) => {
  emit('inputChange', value);
};


const onShowCheckDirNameLoading = () => emit('showCheckDirNameLoading');
const onHideCheckDirNameLoading = () => emit('hideCheckDirNameLoading');

</script>
<style lang="scss" scoped>
</style>
