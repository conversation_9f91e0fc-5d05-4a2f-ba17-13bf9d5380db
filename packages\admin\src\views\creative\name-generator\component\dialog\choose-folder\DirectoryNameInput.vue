<template>
  <div
    ref="inputContainerRef"
    class="h-[50px] aix-creative-name_generator-dropbox-directory-input"
  >
    <t-input
      v-model.trim="modelValue"
      :status="inputAttr.status"
      :tips="inputAttr.tips"
      autofocus
      clearable
      @change="onChange"
      @enter="onEnter"
    />
  </div>
</template>
<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { ref, unref } from 'vue';
import { isPlainObject  } from 'lodash-es';
import { onClickOutside } from '@vueuse/core';
import { useCreativeNameGeneratorDropboxStore } from '@/store/creative/name-generator/dropbox.store';

const props = defineProps({
  basePath: {
    type: String,
    default: '',
  },
  createCallback: {
    type: Function,
    default: () => undefined,
  },
  updateCallback: {
    type: Function,
    default: () => undefined,
  },
  checkNameError: {
    type: Function,
    default: () => undefined,
  },
  defaultValue: {
    type: String,
    default: '',
  },
  // 编辑还是创建， 默认是创建
  isEdit: {
    type: Boolean,
    default: false,
  },
  // 老的目录名称
  oldDirName: {
    type: String,
    default: '',
  },
});


const emit = defineEmits(['cancelCreate', 'cancelEdit', 'showCheckDirNameLoading', 'hideCheckDirNameLoading', 'inputChange']);

const dropboxStore = useCreativeNameGeneratorDropboxStore();
const { dropboxInstance } = storeToRefs(dropboxStore);

const modelValue = ref<string>(unref(props.defaultValue));
const directoryIsExists = ref(false);

const inputContainerRef = ref<HTMLDivElement>();

const inputAttr = ref<{tips?: string, status?: string}>({});

const createDir = async () => {
  if (!modelValue.value) {
    // 取消创建
    emit('cancelCreate');
    return;
  };
  // 编辑的时候， 如果前后名称没有变化， 就取消编辑
  if (props.isEdit && modelValue.value === props.oldDirName) {
    emit('cancelEdit');
    return;
  }
  if (modelValue.value.replaceAll(' ', '').length < 1) return;
  try {
    const flag = await checkDirectoryExists(`${props.basePath}/${modelValue.value}`.replace('//', '/'));
    directoryIsExists.value = flag;
    if (directoryIsExists.value) {
      inputAttr.value = {
        tips: `The folder "${modelValue.value}" already exists at this location`,
        status: 'error',
      };
      return;
    };
    const callback = props.isEdit ?  props?.updateCallback : props.createCallback;
    await callback?.(modelValue.value);
  } catch (e) {
    // 校验重命名出错
    console.log('e', e);
    props?.checkNameError?.();
  }
};

onClickOutside(inputContainerRef, async () => {
  console.log('点击外层');
  createDir();
});

const checkDirectoryExists = async (path: string) => {
  emit('showCheckDirNameLoading');
  try {
    const res = await dropboxInstance.value?.filesGetMetadata({ path });
    console.log('res', res);
    // 这里要判断是不是编辑
    const isExists = res?.result['.tag'] === 'folder';
    emit('hideCheckDirNameLoading');
    return isExists;
  } catch (e: unknown) {
    const { error } = (e as  any);
    if (isPlainObject(error?.error) && error?.error['.tag'] === 'path' && error?.error?.path['.tag'] === 'not_found') {
      emit('hideCheckDirNameLoading');
      return false;
    }
    console.error('检查目录时发生错误:', error);
    emit('hideCheckDirNameLoading');
    throw e;
  }
};


const onChange = (value: any) => {
  if (!value) {
    inputAttr.value  = {
      tips: 'Folder name is required',
      status: 'error',
    };
  } else {
    inputAttr.value = {};
  }
  emit('inputChange', value);
};

const onEnter = () => {
  createDir();
};

</script>
<style lang="scss" scoped>
:global(.aix-creative-name_generator-dropbox-directory-input input::selection) {
  background-color: Highlight !important;
  color: HighlightText !important;
}
</style>
