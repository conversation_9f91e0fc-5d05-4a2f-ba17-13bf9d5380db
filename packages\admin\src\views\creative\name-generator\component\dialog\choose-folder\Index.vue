<template>
  <BaseDialog
    title="Choose folder"
    confirm-text="Apply"
    :visible="chooseFolderDialogVisible"
    :confirm-loading="treeIsLoading"
    @confirm="onConfirm"
    @update:visible="(val: boolean) => setChooseFolderDialogVisible(val)"
  >
    <div class="flex border-[1px] rounded-game-icon border-black-disabled">
      <div class="w-[400px] h-[430px] p-[16px] flex flex-col gap-y-[8px] border-r-[1px] border-black-disabled">
        <div class="flex flex-col gap-y-[8px]">
          <Search
            :dropbox-instance="dropboxInstance"
            @select-dir="onSelectDir"
          />
          <t-button
            variant="dashed"
            :disabled="!selectedNode || creatingDirState.id.length > 0 || treeIsLoading"
            @click="onCreateDirectory"
          >
            <template #icon><AddIcon /></template>
            Create Directory
          </t-button>
        </div>
        <div class="flex-1 overflow-hidden">
          <t-loading
            size="small"
            :loading="treeIsLoading"
            show-overlay
            :text="loadingText"
            class="w-full h-full overflow-auto"
          >
            <t-tree
              ref="treeRef"
              class="overflow-y-auto h-full"
              :data="folderList"
              :load="loadFunc"
              :expanded="expandedValues"
              activable
              expand-on-click-node
              :scroll="{
                type: 'virtual',
                threshold: 7
              }"
              :actived="modelValue"
              @active="onChange"
              @expand="onExpand"
            />
          </t-loading>
        </div>
      </div>
      <div class="w-[274px] h-[430px] p-[16px] flex flex-col gap-y-[8px] bg-[#f8f8f8]">
        <Text size="title" content="Selected" />
        <div class="flex-1 flex justify-center items-center">
          <div v-if="selctedDirDisplayPath" class="flex flex-col items-center gap-y-[8px]">
            <img :src="SlectedFolderImg">
            <Text align="center" :content="selctedDirDisplayPath" />
          </div>
        </div>
      </div>
    </div>
  </BaseDialog>
</template>
<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { ref, h, computed, nextTick } from 'vue';
import BaseDialog from 'common/components/Dialog/Base';
import Text from 'common/components/Text';
import { useCreativeNameGeneratorStore } from '@/store/creative/name-generator/index.store';
import { useCreativeNameGeneratorDropboxStore } from '@/store/creative/name-generator/dropbox.store';
import type { TreeOptionData, TreeNodeModel, TNode } from 'tdesign-vue-next';
import { AddIcon } from 'tdesign-icons-vue-next';
import SlectedFolderImg from '../../../asset/slected-folder.png';
import { useLoading } from 'common/compose/loading';
import { Tree } from 'tdesign-vue-next';
import { v4 as uuidv4 } from 'uuid';
import DirectoryNameInput from './DirectoryNameInput.vue';
import DirectoryItem from './DirectoryItem.vue';
import type { files } from 'dropbox/types/index';
import Search from './Search.vue';
import { flatMap, isArray } from 'lodash-es';

type TEntrie = files.FolderMetadataReference;

type TTreeOption = {
  label: any, value: string,
  ext: TEntrie, isCreating?: boolean,
  opt?: { expanded?: boolean}
};

type TreeNode = TreeNodeModel<TTreeOption>;

const { isLoading: isFolderLoading, showLoading: showFolderLoading, hideLoading: hideFolderLoading } = useLoading();
const { isLoading: isCreateDirLoading, showLoading: showCreateDirLoading, hideLoading: hideCreateDirLoading } = useLoading();
const { isLoading: isUpdateDirLoading, showLoading: showUpdateDirLoading, hideLoading: hideUpdateDirLoading } = useLoading();
const { isLoading: isCheckDirNameLoading, showLoading: showCheckDirNameLoading, hideLoading: hideCheckDirNameLoading } = useLoading();
const { isLoading: isSearchLoading, showLoading: showSearchLoading, hideLoading: hideSearchLoading } = useLoading();

const treeIsLoading = computed(() => (
  isFolderLoading.value || isCreateDirLoading.value || isUpdateDirLoading.value || isCheckDirNameLoading.value
  || isSearchLoading.value
));

const loadingText = computed(() => {
  if (isFolderLoading.value) return 'loading...';
  if (isCheckDirNameLoading.value) return 'Checking directory names';
  if (isCreateDirLoading.value) return 'Creating...';
  if (isUpdateDirLoading.value) return 'Updating...';
  if (isSearchLoading.value) return 'Search...';
  return undefined;
});

const folderList = ref<TreeOptionData[]>([]);

const treeRef = ref<InstanceType<typeof Tree>>();

const creativeNameGeneratorStore =  useCreativeNameGeneratorStore();
const { setChooseFolderDialogVisible } = creativeNameGeneratorStore;
const { chooseFolderDialogVisible  } = storeToRefs(creativeNameGeneratorStore);

const dropboxStore = useCreativeNameGeneratorDropboxStore();
const {
  getRootFolderInfo, getDropboxChildFolderListByPath,
  setSelectedDropboxFolderId, setSelectedDropboxPathLower,
  setSelectedDropboxPathDisplay, clearPrevSelectedDirInfo,
} = dropboxStore;
const {
  dropboxInstance, selectedDropboxFolderId, selectedDropboxPathDisplay,
  selectedDropboxPathLower, dropboxRootFolderId,
} = storeToRefs(dropboxStore);

// 当前选中的值
const modelValue = ref<string[]>([selectedDropboxFolderId.value].filter(item => item));
// 当前展开的节点的值
const expandedValues = ref<string[]>([]);

const selctedDirDisplayPath = ref<string>(selectedDropboxPathDisplay.value);
const selctedDirLowerPath = ref<string>(selectedDropboxPathLower.value);
const selectedNode = ref<TreeNode>();


// 创建目录时用到的状态
const creatingDirState = ref<{id: string, name: string}>({ id: '', name: 'Untitled Folder' });
const clearCreatingDirState = () => {
  creatingDirState.value.id = '';
  creatingDirState.value.name = 'Untitled Folder';
};

// 编辑目录时用到的状态
const editingDirState = ref<{id: string, name: string}>({ id: '', name: '' });
const clearEditingDirState = () => {
  editingDirState.value.id = '';
  editingDirState.value.name = '';
};

const formatTEntries = (entries: TEntrie[]) => entries.map(item => ({
  label: h(DirectoryItem as any, {
    label: item.name,
    folderInfo: item,
    dropboxInstance: dropboxInstance.value,
    treeInstance: treeRef.value,
    onShowUpdateloading: () => showUpdateDirLoading(),
    onHideUpdateLoading: () => hideUpdateDirLoading(),
    onShowCheckDirNameLoading: () => showCheckDirNameLoading(),
    onHideCheckDirNameLoading: () => hideCheckDirNameLoading(),
    updateNode: (res: TEntrie) => updateCurrentNode(res),
    onUpdateEditState: (flag: boolean) => {
      if (flag) {
        editingDirState.value.id  = item.id;
      } else {
        clearEditingDirState();
      }
    },
    isEditState: computed(() => editingDirState.value.id  === item.id),
    newDirName: computed(() => editingDirState.value.name),
    onInputChange: (val: string) => {
      editingDirState.value.name = val;
    },
  }) as any,
  value: item.id,
  children: true,
  ext: { ...item },
}));


const initFolderList = async () => {
  showFolderLoading();
  const initRoot = async () => {
    const res = await getRootFolderInfo();
    folderList.value = formatTEntries([res] as TEntrie[]);
    // 展开目录
    nextTick(() => {
      if (folderList.value.length > 0) {
        treeRef.value?.setItem(dropboxRootFolderId.value, { expanded: true });
      }
    });
  };
  if (selectedDropboxPathDisplay.value) {
    try {
      await onSelectDir(selectedDropboxPathDisplay.value);
    } catch (e) {
      clearPrevSelectedDirInfo();
      hideSearchLoading();
      await initRoot();
    }
  } else {
    await initRoot();
  }
  hideFolderLoading();
};
initFolderList();

const loadFunc = async (node: TreeNodeModel<{ label: TNode, value: string, ext: TEntrie }>) => {
  const res = await getDropboxChildFolderListByPath(node.data.ext.path_lower!);
  return formatTEntries(res as TEntrie[]);
};


const onChange = (val: string[], { node }: { node: TreeNode }) => {
  console.log('onChange node', node);
  if (node.data.isCreating) return;
  if (creatingDirState.value.id) return;
  selectedNode.value = node;
  modelValue.value = val;
  selctedDirDisplayPath.value = node.data.ext.path_display!;
  selctedDirLowerPath.value = node.data.ext.path_lower!;
};

const onExpand = (values: string[], ctx: { node: TreeNode }) => {
  expandedValues.value = [...values];
  console.log('values', values);
  console.log('ctx', ctx);
};


const onCreateDirectory = async () => {
  // 先展开，再往后追加
  // eslint-disable-next-line @typescript-eslint/no-non-null-asserted-optional-chain
  if (!selectedNode.value?.expanded && selectedNode.value?.value) {
    // eslint-disable-next-line @typescript-eslint/no-non-null-asserted-optional-chain
    treeRef.value?.setItem(selectedNode.value.value, { expanded: true });
  }
  // 获取当前节点下的所有字节点
  const currentChild = selectedNode.value?.getChildren(false);
  if (isArray(currentChild) && currentChild.length > 0) {
    const lastChild = currentChild[currentChild.length - 1];
    // 滚动到指定节点
    treeRef.value?.scrollTo({ key: lastChild.data.value });
  }
  // 再执行创建操作
  await nextTick();
  creatingDirState.value.id = uuidv4();
  const directoryElement = h(DirectoryNameInput, {
    onShowCheckDirNameLoading: () => showCheckDirNameLoading(),
    onHideCheckDirNameLoading: () => hideCheckDirNameLoading(),
    createCallback: async (dirName: string) => {
      showCreateDirLoading();
      // 先获取当前节点
      const currentChild = selectedNode.value?.getChildren(false) || [];
      // 再执行新增
      const createRes =  await dropboxInstance.value?.filesCreateFolderV2({
        path: `${selectedNode.value?.data.ext.path_display}/${dirName}`,
        autorename: false,
      });
      // 新增完成后，再从接口拉最新的
      const newChild = await loadFunc(selectedNode.value!);
      // 把之前的干掉
      (currentChild as TreeNode[]).map(item => selectedNode.value?.remove(item.value));
      // 再把新的append 进去
      selectedNode.value?.appendData(newChild);
      clearCreatingDirState();

      // 创建成功后 同时选中创建的节点
      if (createRes) {
        const returnDirInfo = createRes.result.metadata;
        nextTick(() => {
          modelValue.value = [returnDirInfo.id];
          const nodeItem = treeRef.value?.getItem(returnDirInfo.id);
          selectedNode.value = nodeItem as any;
          // 设置选中的目录
          selctedDirDisplayPath.value = selectedNode.value?.data.ext.path_display || '';
          selctedDirLowerPath.value = selectedNode.value?.data.ext.path_lower || '';
          // 滚动到指定节点
          treeRef.value?.scrollTo({ key: returnDirInfo.id });
        });
      }
      hideCreateDirLoading();
    },
    checkNameError: () => {
      treeRef.value?.remove(creatingDirState.value.id);
      clearCreatingDirState();
    },
    onInputChange: (val: string) => {
      creatingDirState.value.name = val;
    },
    defaultValue: computed(() => creatingDirState.value.name) as any,
    basePath: selectedNode.value?.data.ext.path_display,
    onCancelCreate: () => {
      treeRef.value?.remove(creatingDirState.value.id);
      clearCreatingDirState();
    },
  });

  treeRef.value?.appendTo(selectedNode.value?.value, {
    label: directoryElement as any,
    value: creatingDirState.value.id, isCreating: true,
  });
};

async function onSelectDir(fullPath: string) {
  showSearchLoading();
  // 目录路径一级一级的分割分割成数组
  const parentPaths = [...getParentPaths(fullPath)];
  // 拉去根目录，同时根目录下的所需要展开的节点， 拉回来
  const [folderMap, rootRes] = await Promise.all([
    listParentFolders(parentPaths),
    getRootFolderInfo(),
  ]);
  // 根目录信息
  const rootFolder =  formatTEntries([rootRes] as TEntrie[]);
  // 要展开的节点
  const newExpandedValues = [
    rootFolder[0].value,
    ...getExpandedValues(folderMap, parentPaths),
  ];
  const rootPath = parentPaths[0];
  // 生成新的树
  const newTree = buildTreeNode(folderMap, rootPath);
  // 拼接成新的树形结构
  folderList.value = [
    {
      ...rootFolder[0],
      children: newTree,
    },
  ];
  // 设置展开的节点
  expandedValues.value = [...newExpandedValues];
  // 设置激活状态的节点
  const treeActiveNodeValue = newExpandedValues[newExpandedValues.length - 1];
  modelValue.value = [treeActiveNodeValue];
  // 注意，要等tree渲染完成后， 否则有可能获取不到它的节点
  nextTick(() => {
    const nodeItem = treeRef.value?.getItem(treeActiveNodeValue);
    selectedNode.value = nodeItem as any;
    // 设置选中的目录
    selctedDirDisplayPath.value = selectedNode.value?.data.ext.path_display || '';
    selctedDirLowerPath.value = selectedNode.value?.data.ext.path_lower || '';
    hideSearchLoading();
    // 滚动到指定节点
    treeRef.value?.scrollTo({ key: treeActiveNodeValue });
  });
};

function getParentPaths(fullPath: string) {
  const path = fullPath.endsWith('/') && fullPath.length > 1 ? fullPath.slice(0, -1) : fullPath;
  const parts = path.split('/').filter(Boolean);
  const parentPaths = [];
  for (let i = 1; i <= parts.length; i++) {
    parentPaths.push(`/${parts.slice(0, i).join('/')}`);
  }
  return parentPaths;
}


async function listParentFolders(parentPaths: string[]) {
  const result: Record<string, TTreeOption[]> = {};
  for (const p of parentPaths) {
    try {
      const res = await getDropboxChildFolderListByPath(p);
      result[p] = formatTEntries(res);
    } catch (error) {
      console.error(`拉取目录 ${p} 失败:`, error);
      result[p] = [];
    }
  }
  return result;
}


function buildTreeNode(folderMap: Record<string, TTreeOption[]>, currentPath: string) {
  const childrenFolders = folderMap[currentPath] || [];
  if (childrenFolders.length < 1) return;
  return childrenFolders.map((item: TTreeOption) => {
    const children = buildTreeNode(folderMap, item.ext.path_display!) as (TTreeOption[] | undefined);
    return {
      ...item,
      ...(children ? { children } : {}),
    };
  });
}

// 获取展开的节点
const getExpandedValues = (folderMap: Record<string, TTreeOption[]>, pathList: string[]) => {
  const values: string[] = [];
  flatMap(Object.values(folderMap)).forEach((folderRecord) => {
    const folderpath = folderRecord.ext.path_display ?? '';
    if (pathList.includes(folderpath)) {
      values.push(folderRecord.ext.id);
    }
  });
  return values;
};

const updateCurrentNode = (dirRes: TEntrie) => {
  selectedNode.value?.setData?.({
    label: h(DirectoryItem as any, {
      label: dirRes.name,
      folderInfo: dirRes,
      dropboxInstance: dropboxInstance.value,
      treeInstance: treeRef.value,
      updateNode: (res: TEntrie) => updateCurrentNode(res),
      onShowUpdateloading: () => showUpdateDirLoading(),
      onHideUpdateLoading: () => hideUpdateDirLoading(),
      onShowCheckDirNameLoading: () => showCheckDirNameLoading(),
      onHideCheckDirNameLoading: () => hideCheckDirNameLoading(),
      onUpdateEditState: (flag: boolean) => {
        if (flag) {
          editingDirState.value.id  = dirRes.id;
        } else {
          clearEditingDirState();
        }
      },
      isEditState: computed(() => editingDirState.value.id  === dirRes.id),
      newDirName: computed(() => editingDirState.value.name),
      onInputChange: (val: string) => {
        editingDirState.value.name = val;
      },
    }),
    value: dirRes.id,
    ext: dirRes,
  });
  nextTick(() => {
    const nodeItem = treeRef.value?.getItem(dirRes.id);
    selectedNode.value = nodeItem as any;
    selctedDirDisplayPath.value = dirRes.path_display || '';
    modelValue.value = [dirRes.id];
    setSelectedDropboxPathDisplay(selctedDirDisplayPath.value);
  });
};


const onConfirm = () => {
  if (modelValue.value) {
    setSelectedDropboxFolderId(modelValue.value[0]);
    setSelectedDropboxPathDisplay(selctedDirDisplayPath.value);
    setSelectedDropboxPathLower(selctedDirLowerPath.value);
    setChooseFolderDialogVisible(false);
  }
};


</script>
<style lang="scss" scoped>
</style>
