<template>
  <t-select-input
    v-model:input-value="keyWords"
    :popup-visible="popupVisible"
    clearable
    allow-input
    placeholder="Search"
    @popup-visible-change="onPopupVisibleChange"
    @input-change="onInputChange"
    @clear="onClear"
  >
    <template #prefix-icon>
      <SearchIcon />
    </template>
    <template #panel>
      <div v-if="!pathOptions.length || isLoading" class="text-gray-primary text-center p-[12px]">
        <span v-if="!pathOptions.length && !isLoading">No Data</span>
        <t-loading v-if="isLoading" size="small" />
      </div>
      <div v-else class="narrow-scrollbar max-h-[600px] overflow-y-auto">
        <ul class="p-[12px]">
          <li
            v-for="item in pathOptions"
            :key="item.ext.id"
            class="cursor-pointer mb-[6px] px-[12px] py-[6px]"
            @click="() => onOptionClick(item)"
          >
            <div class="text-[14px]">{{ item.lastName }}</div>
            <div class="text-gray-primary text-[12px]" style="font-family: 'PingFang SC'">
              {{ item.parentPath.replace(/,/g, ' > ') }}
            </div>
          </li>
        </ul>
      </div>
    </template>
  </t-select-input>
</template>
<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { useDebounceFn } from '@vueuse/core';
import {  SearchIcon } from 'tdesign-icons-vue-next';
import { SelectInputProps } from 'tdesign-vue-next';
import { ref } from 'vue';
import type { files  } from 'dropbox/types/index';
import { useLoading } from 'common/compose/loading';
import { TPathOption } from '../../../type';
import { useCreativeNameGeneratorDropboxStore } from '@/store/creative/name-generator/dropbox.store';


const emit = defineEmits(['selectDir']);

const dropboxStore = useCreativeNameGeneratorDropboxStore();
const { dropboxInstance, dropboxRootPathLower } = storeToRefs(dropboxStore);

const { isLoading, showLoading, hideLoading } = useLoading();

const keyWords = ref<string>('');
const popupVisible = ref(false);

const pathOptions = ref<TPathOption[]>([]);


const searchWithDebounce = useDebounceFn(async (val: string) => {
  showLoading();
  const res = await dropboxInstance.value!.filesSearchV2({
    query: val,
    options: {
      max_results: 1000,
      file_categories: [{ '.tag': 'folder' }],
      path: dropboxRootPathLower.value,
    },
  });
  try {
    pathOptions.value = res.result.matches.map((item) => {
      const folderInfo  = ((item.metadata as files.MetadataV2Metadata).metadata) as files.FolderMetadataReference;
      const [parentPath, lastName] = splitDropboxPath(folderInfo.path_display ?? '');
      return {
        parentPath,
        lastName,
        ext: folderInfo,
      };
    });
    hideLoading();
  } catch (e) {
    pathOptions.value = [];
    hideLoading();
  };
}, 300);

const onInputChange = async (val: string) => {
  if (!val) {
    pathOptions.value = [];
    return;
  }
  if (!dropboxInstance.value) return;
  searchWithDebounce(val);
};


const onClear = () => {
  popupVisible.value = false;  // 选中后立即关闭浮层
  keyWords.value = '';
  pathOptions.value = [];
};

const onOptionClick = (record: TPathOption) => {
  emit('selectDir', record.ext.path_display, record);
  onClear();
};


const onPopupVisibleChange: SelectInputProps['onPopupVisibleChange'] = (val, context) => {
  console.log(context);
  popupVisible.value = val;
};

function splitDropboxPath(path: string) {
  if (path === '/') {
    return ['/', '']; // 根目录特殊处理
  }
  // 去掉末尾斜杠
  const trimmedPath = path.endsWith('/') && path.length > 1 ? path.slice(0, -1) : path;
  const lastSlashIndex = trimmedPath.lastIndexOf('/');
  if (lastSlashIndex === 0) {
    // 父路径是根目录
    return ['/', trimmedPath.slice(1)];
  }
  const parentPath = trimmedPath.substring(0, lastSlashIndex);
  const lastName = trimmedPath.substring(lastSlashIndex + 1);
  return [parentPath, lastName];
}
</script>
<style lang="scss" scoped>
</style>
