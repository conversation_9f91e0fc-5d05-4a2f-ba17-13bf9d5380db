<template>
  <t-input v-if="props.isShowInput" :value="props.value" @change="onChange" />
  <span v-else>{{ props.value }}</span>
</template>
<script lang="ts" setup>

const emits = defineEmits(['change']);
const props  = defineProps({
  isShowInput: {
    type: Boolean,
    default: false,
  },
  value: {
    type: String,
    default: '',
  },
});

const onChange = (value: string) => {
  emits('change', value);
};

</script>

<style lang="scss" scoped>

</style>
