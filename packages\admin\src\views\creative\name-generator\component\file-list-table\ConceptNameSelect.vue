<template>
  <t-select
    :value="props.value"
    :options="renderOptions"
    placeholder="Please Enter/Select"
    @change="onChange"
  >
    <template #panelTopContent>
      <div class="p-[8px]">
        <t-input
          v-model.trim="keyWords"
          placeholder="Search/Enter New Concept Name"
          maxlength="20"
          show-limit-number
          clearable
          class="w-[270px]"
          @enter="onEnter"
        />
      </div>
    </template>
    <template #empty>
      <div class="p-[8px] text-center ">
        <Text
          class="whitespace-nowrap"
          content="Press Enter to add new Concept Name"
          color="#929cbb"
        />
      </div>
    </template>
  </t-select>
</template>
<script lang="ts" setup>
import { PropType, ref, computed } from 'vue';
import type { OptionData } from 'tdesign-vue-next';
import Text from 'common/components/Text';

const emits = defineEmits(['change', 'create']);
const props = defineProps({
  options: {
    type: Array as PropType<OptionData[]>,
    default: () => ([]),
  },
  value: {
    type: String,
    default: '',
  },
});

const keyWords = ref('');

const onChange = (val: string) => {
  emits('change', val);
};

const renderOptions = computed(() => {
  if (!keyWords.value) return props.options;
  return props.options.filter(item => `${item.label}`.toLocaleLowerCase().includes(keyWords.value.toLocaleLowerCase()));
});

const onEnter = () => {
  if (!keyWords.value) return;
  emits('create', keyWords.value);
  keyWords.value = '';
};

</script>
