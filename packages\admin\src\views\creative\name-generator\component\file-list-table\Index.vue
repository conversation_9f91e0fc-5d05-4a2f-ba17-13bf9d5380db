<template>
  <div class="w-full py-[16px] flex h-full  flex-col justify-between gap-y-[24px]">
    <div class="flex-1 overflow-hidden flex flex-col">
      <div class="flex items-center justify-between px-[16px]">
        <t-upload
          v-if="isShowUploadBtn"
          :request-method="customUploadMethod"
          :disabled="isDisabledUploadBtn"
          multiple
          @success="onSuccess"
        >
          <t-tooltip
            :disabled="isDisabledUploadBtn"
            content="Max 512MB/file, Max 10 files"
          >
            <t-button
              :disabled="isDisabledUploadBtn"
              theme="primary"
            >
              <template #icon>
                <SvgIcon
                  name="plus"
                  color="#fff"
                  class="mr-[8px]"
                />
              </template>
              Add More File
            </t-button>
          </t-tooltip>
          <template #fileListDisplay><div /></template>
        </t-upload>
        <SvgBtn
          name="task-list"
          label="Task list"
          @click="gotoCreativeNameGeneratorTaskList"
        />
      </div>
      <div
        ref="tableContainerRef"
        class="flex flex-1 overflow-hidden justify-between mt-[24px] relative px-[16px]"
      >
        <div class=" flex-1 overflow-hidden">
          <t-table
            ref="tableRef"
            class="create-name_generator-table t-table__content--scrollable-to-left"
            :columns="columns"
            :data="tableDataList"
            :row-class-name="getRowClassName"
            :loading="isLoading"
            row-key="id"
            :max-height="tableMaxHeight"
            :editable-cell-state="editableCellState"
            :editable-row-keys="editableRowKeys"
          />
        </div>
        <div
          v-if="!isExpandLabelCols"
          class="min-w-[24px] flex items-center cursor-pointer justify-center table-extend"
          :style="{
            height: `${tdesignTableHeight}px`
          }"
        />
        <!-- 展开收起图标 -->
        <div
          class="my-auto shadow-[0_3px_6px_0_rgba(0,0,0,.03)] absolute
            flex justify-center items-center w-5 h-[60px] rounded-default bg-white-primary z-10 cursor-pointer
            border border-black-disabled border-solid box-border
            hover:shadow-[0_3px_6px_0_rgba(0,0,0,.1)] hover:border-brand"
          :class="{
            'right-[8px]': isExpandLabelCols,
            'right-[30px]': !isExpandLabelCols,
          }"
          :style="{
            top: `${(tdesignTableHeight - 60) / 2}px`
          }"
          @click="onToggleTableCols"
        >
          <svg-icon
            name="arrow"
            size="12"
            class="transition"
            :class="isExpandLabelCols ? 'rotate-[90deg]' : 'rotate-[-90deg]'"
          />
        </div>
      </div>
    </div>
    <div class="flex justify-end gap-x-[8px] px-[16px]">
      <t-button
        theme="default"
        @click="onSelectFolder"
      >
        <template #icon>
          <img :src="FolderOpenIcon" class="mr-[6px]">
        </template>
        <template v-if="selectedDropboxPathDisplay">{{ selectedDropboxPathDisplay }}</template>
        <template v-else>Choose folder</template>
      </t-button>
      <t-button
        theme="primary"
        :disabled="isDisabledUploadToCloudBtn"
        @click="onUpload"
      >
        Upload To Cloud Drive
      </t-button>
    </div>
    <Preview
      ref="previewRef"
      :hide-trigger="true"
      :get-url="getPreviewUrl"
      :title="previewItem.title"
      :type="previewItem.type"
      @close="previewClose"
    />
    <ChooseFolderDialog
      v-if="chooseFolderDialogVisible"
    />
    <BaseDialog
      :visible="notChooseFolderTipDialogVisible"
      confirm-text="Choose Folder"
      theme="warning"
      placement="center"
      title="No upload folder selected. Would you like to choose one now?"
      @update:visible="(val: boolean) => setNotChooseFolderTipDialogVisible(val)"
      @confirm="() => [setChooseFolderDialogVisible(true), setNotChooseFolderTipDialogVisible(false)]"
    />
  </div>
</template>
<script lang="ts" setup>
import { computed, ref, nextTick } from 'vue';
import { storeToRefs  } from 'pinia';
import { generateTableColumns } from './columns/index';
import FolderOpenIcon from '../../asset/folder-open.svg';
import { pick }  from 'lodash-es';
import { useCreativeNameGeneratorStore } from '@/store/creative/name-generator/index.store';
import { useCreativeNameGeneratorDropboxStore } from '@/store/creative/name-generator/dropbox.store';
import { useCreativeNameGeneratorCacheStore } from '@/store/creative/name-generator/cache.store';
import { ASSET_LEVEL_FIELDS } from '@/store/creative/name-generator/const';
import Preview from 'common/components/Dialog/Preview';
import type { IRenderAssetNameRecord } from '@/store/creative/name-generator/type';
import type { SuccessContext } from 'tdesign-vue-next';
import ChooseFolderDialog from '../dialog/choose-folder/Index.vue';
import BaseDialog from 'common/components/Dialog/Base';
import SvgBtn from 'common/components/SvgIcon/SvgBtn.vue';
import SvgIcon from 'common/components/SvgIcon/SvgIcon.vue';
import { useGoto } from '@/router/goto';
import { useElementSize } from '@vueuse/core';
import { useGlobalGameStore } from '@/store/global/game.store';

const emit = defineEmits(['saveToIndexedDB']);

const {  gotoCreativeNameGeneratorTaskList } = useGoto();

const gameStore = useGlobalGameStore();
const { gameCode } = storeToRefs(gameStore);


const creativeNameGeneratorStore = useCreativeNameGeneratorStore();
const {
  removeCreativeFile, updateCreativeFile, uploadToCloudDrive,
  setChooseFolderDialogVisible, setNotChooseFolderTipDialogVisible,
} = creativeNameGeneratorStore;
const {
  isInitLoaing, isRemoveIDBFileLoaing,
  isSaveFileToIDBLoaing, isUpdateIDBFileLoaing,
  tableDropdownOptions, tableDataList,
  chooseFolderDialogVisible, notChooseFolderTipDialogVisible,
} = storeToRefs(creativeNameGeneratorStore);

const dropboxStore = useCreativeNameGeneratorDropboxStore();
const { setDropboxAuthExpiredDialogVisible } = dropboxStore;
const { selectedDropboxPathDisplay, isDropboxAuthorized } = storeToRefs(dropboxStore);

const creativeNameGeneratorCacheStore = useCreativeNameGeneratorCacheStore();
const { appendConceptNameMap } = creativeNameGeneratorCacheStore;
const { cacheUniqueIdMap, cacheConceptNameMap } = storeToRefs(creativeNameGeneratorCacheStore);

const isLoading = computed(() => [
  isInitLoaing.value,
  isRemoveIDBFileLoaing.value,
  isSaveFileToIDBLoaing.value,
  isUpdateIDBFileLoaing.value,
].some(item => item));

/** -----------------------------------------  表格用到的数据 开始 ------------------------------------ */

const tableContainerRef = ref();
const { height: tableMaxHeight } = useElementSize(tableContainerRef);
const tableRef = ref();
// const { width: tableDomWidth } = useElementBounding(computed(() => tableRef.value?.baseTableRef.tableElmRef))
const { height: tdesignTableHeight } = useElementSize(tableRef);

const isExpandLabelCols = ref(false);
// 表格列
const columns = computed(() => generateTableColumns(
  {
    editedCallback, previewClickCallback, deleteCallback,
    tableDropOptions: tableDropdownOptions.value,
    createConceptName: appendConceptNameMap,
    cacheUniqueIdList: cacheUniqueIdMap.value[gameCode.value] ?? [],
    cacheConceptNameList: cacheConceptNameMap.value[gameCode.value] ?? [],
    isExpandLabelCols: isExpandLabelCols.value,
    plainFileIdList: plianFileList.value.map(item => item.id),
  },
));
// 可以编辑的列的id
const editableRowKeys = computed(() => tableDataList.value.map(item => item.id));

const getRowClassName = ({ row }: { row: IRenderAssetNameRecord, colKey: string}) => {
  const fileGroupByType = row.format_type.toLocaleLowerCase();
  if (row.isGroupBy) return `${fileGroupByType}-file-group-tr`;
  return `${fileGroupByType}-file-item-tr file-item-tr`;
};

// 上传按钮的disabled
const plianFileList = computed(() => tableDataList.value.filter(item => !item.isGroupBy));
const isDisabledUploadBtn = computed(() => plianFileList.value.length >= 10);
const isDisabledUploadToCloudBtn = computed(() => {
  if (plianFileList.value.length > 10) return true;
  return plianFileList.value.some(item => (item.fileObject?.size || 0) > 512 * 1024 * 1024);
});


// 判断是否能编辑
const editableCellState = ({ row, col }: { row: IRenderAssetNameRecord, col: { colKey: string }}) =>  {
  if (row.isGroupBy) {
    return !ASSET_LEVEL_FIELDS.includes(col.colKey);
  }
  return ASSET_LEVEL_FIELDS.includes(col.colKey);
};

// 修改
const editedCallback = (data: { colKey: string, newValue: string, id: number }) => {
  const { colKey, newValue, id } = data;
  updateCreativeFile(id, { key: colKey, value: newValue });
};

// 删除
const deleteCallback = async (data: { id: number, parentId: number}) => {
  await removeCreativeFile(data);
};

const onToggleTableCols = () => {
  isExpandLabelCols.value = !isExpandLabelCols.value;
  const lastCol = columns.value[columns.value.length - 1];
  const firstCol = columns.value[0];
  const scrollToCol = isExpandLabelCols.value ? lastCol : firstCol;
  // 滚动到指定列
  nextTick(() => {
    tableRef.value.scrollColumnIntoView(scrollToCol.colKey);
  });
};
/** -----------------------------------------  表格用到的数据 结束 ------------------------------------ */

/** -----------------------------------------  预览 开始 ------------------------------------ */
const previewRef = ref<InstanceType<typeof Preview> >();
const previewItem = ref<{
  url: string,
  title: string,
  type: 'video' | 'image'
}>({ url: '', title: '', type: 'image' });
const getPreviewUrl = () => Promise.resolve(previewItem.value.url);
// 打开预览弹窗
const previewClickCallback = (previewData: Record<string, any>) => {
  previewItem.value = pick(previewData, ['url', 'title', 'type']);
  previewRef.value?.show();
};
// 预览弹窗的关闭
const  previewClose = () => {
  // 释放内存
  if (previewItem.value.url) {
    URL.revokeObjectURL(previewItem.value.url);
  }
};
/** -----------------------------------------  预览 结束 ------------------------------------ */


/** -----------------------------------------  上传相关 开始 ------------------------------------ */
const customUploadMethod = () => Promise.resolve({ status: 'success' });

const isShowUploadBtn = ref(true);

const onSuccess = async (ctx: SuccessContext) => {
  emit('saveToIndexedDB', ctx);
  // 这个上传组件有坑， ctx中的fileList会累加上一次选择的
  isShowUploadBtn.value = false;
  nextTick(() => {
    isShowUploadBtn.value = true;
  });
};

const onUpload = async () => {
  if (!isDropboxAuthorized.value) {
    return setDropboxAuthExpiredDialogVisible(true);
  }
  const validateRes = await tableRef.value.validateTableData();
  console.log('validateRes.result', validateRes.result);
  const validateResKeys = Object.keys(validateRes.result);
  // 校验不通过， 不能提交
  if (validateResKeys.length > 0) {
    // 滚到到没有通过验证的列中的 第一列
    const colKey = validateResKeys[0].replace(/^\d+__/, '');
    tableRef.value.scrollColumnIntoView(colKey);
    return;
  };
  if (!selectedDropboxPathDisplay.value) {
    return setNotChooseFolderTipDialogVisible(true);
  }
  uploadToCloudDrive(selectedDropboxPathDisplay.value);
};

const onSelectFolder = () => {
  if (isDropboxAuthorized.value) {
    setChooseFolderDialogVisible(true);
  } else {
    setDropboxAuthExpiredDialogVisible(true);
  }
};
/** -----------------------------------------  上传相关 结束 ------------------------------------ */

</script>
<style lang="scss" scoped>
.table-extend {
  background: linear-gradient(
    to right,
    #d7dae5 0,
    #d7dae5 60%,
    #babcc5 75%,
    #babcc5 100%
  );
}
:deep(.t-table__header) {
  // 最右边的
  tr th:last-child {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }
  // 最左边的
  tr th:first-child {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
}
</style>
