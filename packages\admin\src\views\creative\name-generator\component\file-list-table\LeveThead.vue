<template>
  <div class="flex items-center gap-x-[8px]">
    <img :src="props.levelType === 'asset' ? AssetLevelIcon : SerialLevelIcon">
    {{ props.title }}
  </div>
</template>
<script lang="ts" setup>
import { PropType } from 'vue';
import SerialLevelIcon from '../../asset/serial-level-icon.png';
import AssetLevelIcon from '../../asset/asset-level-icon.png';

const props = defineProps({
  levelType: {
    type: String as PropType<'serial' | 'asset'>,
    default: 'serial',
  },
  title: {
    type: String,
    default: '',
  },
});
</script>
