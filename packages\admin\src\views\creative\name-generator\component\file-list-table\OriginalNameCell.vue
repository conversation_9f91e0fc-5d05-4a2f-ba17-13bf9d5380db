<template>
  <div class="inline-flex items-center gap-x-[16px] justify-start w-full overflow-hidden">
    <!-- 删除按钮 -->
    <SvgIcon
      v-if="!isGroupBy"
      name="delete-2"
      class="hover:text-link text-brand cursor-pointer"
      @click="onDelete"
    />
    <div class="inline-flex justify-between items-center gap-x-[16px] flex-1 overflow-hidden">
      <!-- 文件名和分组头 -->
      <div class="flex-1 overflow-hidden h-[24px] leading-[24px]">
        <div class="flex items-center gap-x-[8px]">
          <!-- 文件名 -->
          <div class="flex-1 truncate">
            <!-- 分组头 -->
            <template v-if="isGroupBy">
              <component
                :is="getGroupByText(props.row.format_type)"
              />
            </template>
            <!-- 文件名 -->
            <t-tooltip v-else :content="fileName">
              <!--  视频， 图片， 可以预览 -->
              <a
                v-if="isSupportPreview"
                href="javascript:;"
                class=" truncate text-brand inline-block w-full  hover:[text-link] cursor-pointer"
                @click="onPreviewClick"
              >
                {{ fileName }}
              </a>
              <!-- 不能预览 -->
              <Text v-else :content="fileName" class=" inline-block w-full truncate cursor-pointer" />
            </t-tooltip>
          </div>
          <!-- 报错的图标 -->
          <t-tooltip
            v-if="isShowFileErrorTips"
            content="Exceeds the size/count limit. Please remove it to proceed."
          >
            <ErrorCircleFilledIcon class="text-error-primary cursor-pointer" />
          </t-tooltip>
        </div>
      </div>
      <!-- 右边的箭头 -->
      <ArrowRightIcon v-if="!isGroupBy" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { defineProps, PropType, computed, defineEmits, h } from 'vue';
import { ArrowRightIcon, ErrorCircleFilledIcon } from 'tdesign-icons-vue-next';
import type { IRenderAssetNameRecord } from '@/store/creative/name-generator/type';
import { CREATIVE_FORMAT_TYPE } from 'common/service/creative/name-generator/const';
import type { TCreativeFormatType } from 'common/service/creative/name-generator/type';
import Text from 'common/components/Text';
import { pick } from 'lodash-es';
import SvgIcon from 'common/components/SvgIcon/SvgIcon.vue';

const emit = defineEmits(['delete', 'previewClickCallback']);

const props = defineProps({
  row: {
    type: Object as PropType<IRenderAssetNameRecord>,
    default: () => ({}),
  },
  plainFileIdList: {
    type: Array as PropType<number[]>,
    default: () => ([]),
  },
});

// 是不是groupby 分组行
const isGroupBy = computed(() => props.row.isGroupBy);

const fileName = computed(() => props.row.original_name);

const isShowFileErrorTips = computed(() => {
  const index = props.plainFileIdList.findIndex(item => item === props.row.id);
  const size = props.row.fileObject?.size || 0;
  return index > 9 || size > 512 * 1024 * 1024;
});

// 是否允许预览
const isSupportPreview = computed(() => {
  const { format_type: formatType } = props.row;
  const supportPreviewTypes: string[] = [CREATIVE_FORMAT_TYPE.VIDEO, CREATIVE_FORMAT_TYPE.IMAGE];
  return supportPreviewTypes.includes(formatType) && !isGroupBy.value;
});

// 预览
const onPreviewClick = () => {
  const { fileObject, format_type: formatType } = props.row as IRenderAssetNameRecord;
  emit('previewClickCallback', {
    url: URL.createObjectURL(fileObject!),
    title: fileObject!.name,
    type: formatType.toLocaleLowerCase(),
  });
};

const onDelete = () => {
  emit('delete', pick(props.row, ['id', 'parentId']));
};

const getGroupByText = (formatType: TCreativeFormatType) => {
  const textMap = {
    [CREATIVE_FORMAT_TYPE.VIDEO]: [h(SvgIcon, { name: 'movie' }), h(Text, { content: 'All Video' })],
    [CREATIVE_FORMAT_TYPE.IMAGE]: [h(SvgIcon, { name: 'picture' }), h(Text, { content: 'All Banner' })],
    [CREATIVE_FORMAT_TYPE.HTML]: [h(SvgIcon, { name: 'html' }), h(Text, { content: 'All Playable' })],
    [CREATIVE_FORMAT_TYPE.OTHER]: [h(SvgIcon, { name: 'other' }), h(Text, { content: 'All Others' })],
  };
  return h('span', { class: 'flex items-center gap-x-[16px]' }, textMap[formatType]);
};


</script>

<style lang="scss" scoped>
</style>
