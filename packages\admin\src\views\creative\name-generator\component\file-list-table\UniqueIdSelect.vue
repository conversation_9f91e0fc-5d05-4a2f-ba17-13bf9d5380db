<template>
  <t-select v-bind="attr">
    <template v-if="props.isShowResetIcon" #suffixIcon>
      <div class="min-w-[16px] cursor-pointer min-h-[16px]" @click.stop>
        <t-tooltip content="Restore Default">
          <SvgIcon
            name="history"
            class="hover:text-[#5086f3] p-[2px] cursor-pointer"
            @click="onSuffixIconClick"
          />
        </t-tooltip>
      </div>
    </template>
  </t-select>
</template>
<script lang="ts" setup>
import { useAttrs, computed } from 'vue';
import SvgIcon from 'common/components/SvgIcon/SvgIcon.vue';


const emit = defineEmits(['suffixIconClick']);

const props = defineProps({
  isShowResetIcon: {
    type: Boolean,
    default: false,
  },
});

const attr = computed(() => useAttrs());

const onSuffixIconClick = () => {
  emit('suffixIconClick');
};

</script>
