import ISO6391 from 'iso-639-1';
import type { INameRefactorOptions } from 'common/service/creative/name-generator/type';
import type { OptionData, PrimaryTableRowEditContext, TableEditableCellPropsParams, PrimaryTableCol } from 'tdesign-vue-next';
import type { IRenderAssetNameRecord } from '@/store/creative/name-generator/type';
import {
  originalNameCol,
  gameNameCol,
  uqiqueIdCol,
  uploadMonthCol,
  assetTypeCol,
  conceptNameCol,
  versionNumberCol,
  versionNameCol,
  productionStageCol,
  creativeTypeCol,
  languageCol,
  durationCol,
  ratioDimensionsCol,
  nameColAttr,
} from './name-cols';
import {
  sourceCol,
  targetCol,
  producerCol,
  uploaderCol,
  typeSpecificsCol,
  playableChannelCol,
  labelColAttr,
} from './label-cols';
import { groupBy, uniq, uniqBy } from 'lodash-es';


const separatorCol = {
  title: '',
  colKey: 'separatorCol',
  cell: () => '-',
  width: 30,
};

const languageCodeList = uniq(['en', 'zh', ...ISO6391.getAllCodes()]).map((item) => {
  const upperCode = item.toLocaleUpperCase();
  return { label: ISO6391.getName(item), value: upperCode };
});

type TGenerateTableColumns =  {
  deleteCallback: (params: { id: number, parentId: number}) => void,
  previewClickCallback: (params: { url: string, title: string, type: string }) => void,
  plainFileIdList: number[],
  tableDropOptions: INameRefactorOptions,
  cacheUniqueIdList: OptionData[],
  cacheConceptNameList: OptionData[],
  editedCallback: (params: { newValue: any, colKey: string, id: number }) => void,
  createConceptName: (record: OptionData) => void,
  isExpandLabelCols: boolean,
};

export const generateTableColumns = (params: TGenerateTableColumns) => {
  const {
    deleteCallback, previewClickCallback, plainFileIdList,
    tableDropOptions, cacheUniqueIdList, cacheConceptNameList,
    editedCallback, createConceptName, isExpandLabelCols,
  } = params;

  // 要用到的数据
  const fullUniqueIdList = uniqBy([...cacheUniqueIdList, ...tableDropOptions.unique_id], 'value');
  const fullConceptNameList = uniqBy([...cacheConceptNameList, ...tableDropOptions.concept_name], 'value');
  const groupByUniqueId = groupBy(fullUniqueIdList, 'value');

  const onEdit = (editParams: PrimaryTableRowEditContext<IRenderAssetNameRecord>) => {
    const { row, value, col } = editParams;
    editedCallback({ newValue: value, colKey: col.colKey!, id: row.id });
  };

  // 失去焦点
  const onBlur = (val: string, ctx: TableEditableCellPropsParams<IRenderAssetNameRecord>) => {
    // 判断更新前后的值是不是一样
    const { row, col } = ctx;
    const oldValue = row[col.colKey! as keyof typeof row];
    if (oldValue === val) return;
    onEdit({
      ...ctx,
      value: val,
    });
  };

  // 分隔符的列
  const nameSeparatorCol = { ...separatorCol, attrs: nameColAttr };
  const labelSeparatorCol = { ...separatorCol,  attrs: labelColAttr };

  // name列
  const nameCols: PrimaryTableCol<IRenderAssetNameRecord>[]  = [
    // 表格列originalName
    originalNameCol({
      deleteCallback,
      previewClickCallback,
      plainFileIdList,
    }),
    // Game Name
    gameNameCol({
      options: tableDropOptions.game_name || [],
    }),
    nameSeparatorCol,
    // Unique ID
    uqiqueIdCol({
      options: fullUniqueIdList,
      onChange: onEdit,
      resetUqiqueIdCallback: editedCallback,
      groupByUniqueId,
    }),
    nameSeparatorCol,
    // Upload Month
    uploadMonthCol(),
    nameSeparatorCol,
    // Asset Type
    assetTypeCol({
      options: tableDropOptions?.asset_type || [],
      onChange: onEdit,
    }),
    nameSeparatorCol,
    // Concept Name
    conceptNameCol({
      options: fullConceptNameList,
      onChange: onEdit,
      changeConceptNameCallback: editedCallback,
      createConceptNameCallback: createConceptName,
    }),
    nameSeparatorCol,
    // Version Number
    versionNumberCol({
      onBlur,
    }),
    nameSeparatorCol,
    // Version Name
    versionNameCol({
      onBlur,
    }),
    nameSeparatorCol,
    // ​Production Stage
    productionStageCol({
      onChange: onEdit,
      options: tableDropOptions?.production_stage || [],
    }),
    nameSeparatorCol,
    // Creative Type
    creativeTypeCol({
      options: tableDropOptions?.creative_type || [],
      onChange: onEdit,
    }),
    nameSeparatorCol,
    // Language
    languageCol({
      onChange: onEdit,
      options: languageCodeList,
    }),
    nameSeparatorCol,
    // Duration
    durationCol({ onBlur }),
    nameSeparatorCol,
    // Ratio/Dimensions
    ratioDimensionsCol({ onBlur }),
  ];
  // label 列
  const labelCols: PrimaryTableCol<IRenderAssetNameRecord>[] = [
    // ​Source
    sourceCol({
      options: tableDropOptions?.source || [],
      onChange: onEdit,
    }),
    labelSeparatorCol,
    // ​Target
    targetCol({
      options: tableDropOptions?.target || [],
      onChange: onEdit,
    }),
    labelSeparatorCol,
    // Producer
    producerCol({
      options: tableDropOptions?.producer || [],
      onChange: onEdit,
    }),
    labelSeparatorCol,
    // Uploader
    uploaderCol(),
    labelSeparatorCol,
    // Type Specifics
    typeSpecificsCol({
      options: tableDropOptions?.type_specifics || [],
      onChange: onEdit,
    }),
    labelSeparatorCol,
    // Playable Channel
    playableChannelCol({
      options: tableDropOptions?.playable_channel || [],
      onChange: onEdit,
    }),
  ];
  if (!isExpandLabelCols) return nameCols;
  return [
    ...nameCols,
    ...labelCols,
  ];
};
