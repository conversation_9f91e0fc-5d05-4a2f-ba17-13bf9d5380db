
import type { PrimaryTableCol, OptionData } from 'tdesign-vue-next';
import type { IRenderAssetNameRecord } from '@/store/creative/name-generator/type';
import { Select, Input } from 'tdesign-vue-next';
import LeveThead from '../LeveThead.vue';
import type { TCellSelectChangeCallback } from '../../../type';


export const labelColAttr: PrimaryTableCol<IRenderAssetNameRecord>['attrs'] = ({ type, row }) => {
  const isTh = type === 'th';
  const backgroundColor = !isTh ? '#edf0ff' : '#dae0ff';
  // 分组行的间距调大一点
  const paddingY = row.isGroupBy ? {
    paddingTop: '24px',
    paddingBottom: '24px',
  } : {};
    // 表头的下边宽颜色
  const thBorder = {
    borderBottom: 'solid 1px #dddcdf',
  };
  const style = {
    backgroundColor,
    ...(!isTh ? paddingY : {}),
    ...(isTh ? thBorder : {}),
  };
  return {
    style,
  };
};

// 表格列​Source
export const sourceCol = (
  { options, onChange }: {options: OptionData[], onChange: TCellSelectChangeCallback },
): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'serial', title: 'Source' }),
  colKey: 'source',
  width: '160px',
  edit: {
    validateTrigger: 'change',
    component: Select,
    props: {
      options,
      clearable: true,
    },
    on: () => ({
      onChange,
    }),
  },
  attrs: labelColAttr,
});

// 表格列​Target
export const targetCol = (
  { options, onChange }: {options: OptionData[], onChange: TCellSelectChangeCallback },
): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'serial', title: 'Target' }),
  colKey: 'target',
  width: '160px',
  edit: {
    validateTrigger: 'change',
    component: Select,
    props: {
      options,
      clearable: true,
    },
    on: () => ({
      onChange,
    }),
  },
  attrs: labelColAttr,
});

// 表格列Producer
export const producerCol = (
  { options, onChange }: {options: OptionData[], onChange: TCellSelectChangeCallback },
): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'serial', title: 'Producer' }),
  colKey: 'producer',
  width: '160px',
  edit: {
    keepEditMode: false,
    component: Select,
    defaultEditable: false,
    props: {
      options,
      clearable: true,
    },
    on: () => ({
      onChange,
    }),
  },
  attrs: labelColAttr,
});


// 表格列Uploader
export const uploaderCol = (): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'serial', title: 'Uploader' }),
  colKey: 'uploader',
  width: '160px',
  ellipsis: true,
  edit: {
    component: Input,
    validateTrigger: 'change',
    props: {
      disabled: true,
    },
  },
  attrs: labelColAttr,
});

// 表格列 Type Specifics
export const typeSpecificsCol = (
  { options, onChange }: {options: OptionData[], onChange: TCellSelectChangeCallback },
): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'serial', title: 'Type Specifics' }),
  colKey: 'type_specifics',
  width: '180px',
  edit: {
    component: Select,
    validateTrigger: 'change',
    props: {
      options,
      clearable: true,
    },
    on: () => ({
      onChange,
    }),
  },
  attrs: labelColAttr,
});

// 表格列Playable Channel
export const playableChannelCol = (
  { options, onChange }: {options: OptionData[], onChange: TCellSelectChangeCallback },
): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'serial', title: 'Playable Channel' }),
  colKey: 'playable_channel',
  width: '200px',
  edit: {
    validateTrigger: 'change',
    component: Select,
    props: {
      options,
      clearable: true,
    },
    on: () => ({
      onChange,
    }),
  },
  attrs: labelColAttr,
});
