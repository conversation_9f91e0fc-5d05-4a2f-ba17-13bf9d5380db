import type { PrimaryTableCol, OptionData } from 'tdesign-vue-next';
import type { IRenderAssetNameRecord } from '@/store/creative/name-generator/type';
import { Select, Input, InputNumber } from 'tdesign-vue-next';
import OriginalNameCell from '../OriginalNameCell.vue';
import OriginalNameTitle from '../OriginalNameTitle.vue';
import LeveThead from '../LeveThead.vue';
import  UniqueIdSelect from '../UniqueIdSelect.vue';
import ConceptNameSelect from '../ConceptNameSelect.vue';
import AssetPropertyInput  from '../AssetPropertyInput.vue';
import type { TCellSelectChangeCallback, TCellInputBlurCallback } from '../../../type';
import { formRule } from '../../../const';


export const nameColAttr: PrimaryTableCol<IRenderAssetNameRecord>['attrs'] = (params) => {
  const { row, type } = params;
  const isTh = type === 'th';
  // const backgroundColor = row.isGroupBy ? '#f0f7ff' : '#f8f8fb';
  const backgroundColor =  row.isGroupBy ? {
    backgroundColor: '#f0f7ff',
  } : {};
  // 分组行的间距调大一点
  const paddingY = row.isGroupBy ? {
    paddingTop: '24px',
    paddingBottom: '24px',
  } : {};
  // 表头的下边宽颜色
  const thBorder = {
    borderBottom: 'solid 1px #dddcdf',
  };
  const style = {
    ...(!isTh ? backgroundColor : {}),
    ...(!isTh ? paddingY : {}),
    ...(isTh ? thBorder : {}),
  };
  return {
    style,
  };
};


// 表格列 Original name
export const originalNameCol = (
  {
    deleteCallback, previewClickCallback,
    plainFileIdList,
  }: {
    deleteCallback: (params: { id: number, parentId: number}) => void,
    previewClickCallback: (params: { url: string, title: string, type: string }) => void,
    plainFileIdList: number[]
  },
): PrimaryTableCol<IRenderAssetNameRecord> => ({
  colKey: 'original_name',
  fixed: 'left',
  width: 300,
  cell: (_h: Function, { row }: {row: any}) => _h(OriginalNameCell, {
    row, onPreviewClickCallback: previewClickCallback,
    onDelete: deleteCallback,
    plainFileIdList,
  }),
  title: (_h: Function) => _h(OriginalNameTitle),
  align: 'left',
  attrs: nameColAttr,
});


// 表格列 GameName
export const gameNameCol = ({ options }: { options: OptionData[] }): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'serial', title: 'Game Name' }),
  colKey: 'game_name',
  width: '160px',
  ellipsis: true,
  edit: {
    component: Select,
    keepEditMode: false,
    defaultEditable: false,
    props: {
      options,
      disabled: true,
    },
  },
  attrs: nameColAttr,
});


// 表格列 Uqique ID
export const uqiqueIdCol = (
  {
    options,
    onChange,
    resetUqiqueIdCallback,
    groupByUniqueId,
  }:
  {
    options: OptionData[],
    onChange: TCellSelectChangeCallback,
    resetUqiqueIdCallback: (params: { newValue: any, colKey: string, id: number}) => void,
    groupByUniqueId: Record<string, OptionData[]>
  },
): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'serial', title: 'Unique ID' }),
  colKey: 'unique_id',
  width: '160px',
  ellipsis: true,
  edit: {
    component: UniqueIdSelect,
    keepEditMode: false,
    defaultEditable: false,
    props: ({ row }) => {
      const list = options.map(item => ({ ...item, label: row.game_name + item.label }));
      return {
        options: list,
        filterable: true,
        isShowResetIcon: row.unique_id !== row.latest_unique_id,
      };
    },
    on: ctx => ({
      onChange,
      onSuffixIconClick: () => {
        const { row, col } = ctx;
        resetUqiqueIdCallback({ newValue: row.latest_unique_id, colKey: col.colKey!, id: row.id });
      },
    }),
    rules: [
      { ...formRule.required, trigger: 'change' },
    ],
  },
  cell: (_h: Function, { row }) => {
    const uniqueIdCode = `${groupByUniqueId[row.unique_id]?.[0]?.label ?? row.unique_id}`;
    return row.game_name + uniqueIdCode;
  },
  attrs: nameColAttr,
});

// 表格列 Upload Month
export const uploadMonthCol = (): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'serial', title: 'Upload Month' }),
  colKey: 'upload_month',
  width: '180px',
  edit: {
    component: Input,
    validateTrigger: 'change',
    props: {
      disabled: true,
    },
    rules: [
      { ...formRule.required },
    ],
  },
  attrs: nameColAttr,
});


// 表格列Asset Type
export const assetTypeCol = (
  { options, onChange }: {options: OptionData[], onChange: TCellSelectChangeCallback },
): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'serial', title: 'Asset Type' }),
  colKey: 'asset_type',
  width: '140px',
  edit: {
    validateTrigger: 'change',
    component: Select,
    props: {
      options,
      disabled: true,
    },
    on: () => ({
      onChange,
    }),
    rules: [
      { ...formRule.required },
    ],
  },
  attrs: nameColAttr,
});


// 表格列 Concept Name
export const conceptNameCol = (
  { options,  onChange, createConceptNameCallback, changeConceptNameCallback }:
  {
    options: OptionData[],
    onChange: TCellSelectChangeCallback,
    createConceptNameCallback: (record: OptionData) => void,
    changeConceptNameCallback: (params: { newValue: any, colKey: string, id: number}) => void,
  },
): PrimaryTableCol<IRenderAssetNameRecord>  => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'serial', title: 'Concept Name' }),
  colKey: 'concept_name',
  width: '200px',
  ellipsis: true,
  edit: {
    component: ConceptNameSelect,
    validateTrigger: 'change',
    props: {
      options,
    },
    on: ctx => ({
      onChange,
      onCreate: (val: string) => {
        const trimValue = val.trim();
        if (!trimValue) return;
        if (options.some(item => item.label === trimValue)) return;
        // 新创建
        createConceptNameCallback({ label: trimValue, value: trimValue });
        // 后选中
        changeConceptNameCallback({ newValue: trimValue, colKey: ctx.col.colKey!, id: ctx.row.id });
      },
    }),
    rules: [
      { ...formRule.required },
    ],
  },
  attrs: nameColAttr,
});

// 表格列 Version Number
export const versionNumberCol = ({ onBlur }: { onBlur: TCellInputBlurCallback }): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'serial', title: 'Version Number' }),
  colKey: 'version_number',
  width: '220px',
  edit: {
    component: InputNumber,
    validateTrigger: 'change',
    props: {
      theme: 'normal',
      decimalPlaces: 0,
      max: Number.MAX_VALUE,
      inputProps: {
        maxlength: 20,
        showLimitNumber: true,
      },
    },
    on: ctx => ({
      onBlur: (val: string) => onBlur(val, ctx as any),
    }),
    rules: [
      { ...formRule.required },
    ],
  },
  attrs: nameColAttr,
});

// 表格列 Version Name
export const versionNameCol = ({ onBlur }: { onBlur: TCellInputBlurCallback }): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'serial', title: 'Version Name' }),
  colKey: 'version_name',
  width: '220px',
  ellipsis: true,
  edit: {
    component: Input,
    validateTrigger: 'change',
    props: {
      showLimitNumber: true,
      maxlength: 20,
    },
    on: ctx => ({
      onBlur: (val: string) => onBlur(val, ctx as any),
    }),
    rules: [
      { ...formRule.required },
    ],
  },
  attrs: nameColAttr,
});

// 表格列 ​Production Stage
export const productionStageCol = (
  { options, onChange }: {options: OptionData[], onChange: TCellSelectChangeCallback },
): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'serial', title: '​Production Stage' }),
  colKey: 'production_stage',
  width: '200px',
  edit: {
    validateTrigger: 'change',
    component: Select,
    props: {
      options,
    },
    on: () => ({
      onChange,
    }),
    rules: [
      { ...formRule.required },
    ],
  },
  attrs: nameColAttr,
});

// 表格列 Creative Type
export const creativeTypeCol = (
  { options, onChange }: {options: OptionData[], onChange: TCellSelectChangeCallback },
): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'serial', title: 'Creative Type' }),
  colKey: 'creative_type',
  width: '180px',
  edit: {
    validateTrigger: 'change',
    component: Select,
    props: {
      options,
    },
    on: () => ({
      onChange,
    }),
    rules: [
      { ...formRule.required },
    ],
  },
  attrs: nameColAttr,
});

// 表格列Language
export const languageCol = (
  { options, onChange }: {options: OptionData[], onChange: TCellSelectChangeCallback },
): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'asset', title: 'Language' }),
  colKey: 'language',
  width: '160px',
  edit: {
    component: Select,
    validateTrigger: 'change',
    props: ({ row }) => ({
      options,
      filterable: true,
      valueDisplay: () => row.language,
    }),
    on: () => ({
      onChange,
    }),
    rules: ({ row }) => {
      if (row.isGroupBy) return [];
      return [
        { ...formRule.required },
      ];
    },
  },
  attrs: nameColAttr,
});

// 表格列Duration
export const durationCol = (
  { onBlur }: { onBlur: TCellInputBlurCallback },
): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'asset', title: 'Duration' }),
  colKey: 'duration_render',
  width: '160px',
  ellipsis: true,
  edit: {
    component: AssetPropertyInput,
    validateTrigger: 'change',
    props: ({ row }) => ({
      isShowInput: row.isExtractDurationFail,
    }),
    on: ctx => ({
      onBlur: (val: string) => onBlur(val, ctx as any),
    }),
    rules: ({ row }) => (row.isExtractDurationFail ?  [{ ...formRule.required }] : []),
  },
  attrs: nameColAttr,
});

// 表格列Ratio/Dimensions
export const ratioDimensionsCol = (
  { onBlur }: { onBlur: TCellInputBlurCallback },
): PrimaryTableCol<IRenderAssetNameRecord> => ({
  title: (_h: Function) => _h(LeveThead, { levelType: 'asset', title: 'Ratio/Dimensions' }),
  colKey: 'ratio_dimensions_render',
  width: '180px',
  ellipsis: true,
  edit: {
    component: AssetPropertyInput,
    validateTrigger: 'change',
    props: ({ row }) => ({
      isShowInput: row.isExtractRatioDimensionsFail,
    }),
    on: ctx => ({
      onBlur: (val: string) => onBlur(val, ctx as any),
    }),
    rules: ({ row }) => (row.isExtractRatioDimensionsFail ? [{ ...formRule.required }] : []),
  },
  attrs: nameColAttr,
});
