import { IFormDynamicItem } from 'common/components/FormContainer/type';
import { BaseCommonSearchInput } from 'common/components/CommonSearchInputBox';
import SelectDataRangePickerVue from 'common/components/DateRangePicker/SelectDataRangePicker.vue';
import dayjs from 'dayjs';
import { COMPONENTS_MAP } from 'common/const/components';
import { TASK_LIST_DEFAULT_FORM_VALUE } from '@/store/creative/name-generator/const';

/**
 * Normalize options array to standard label-value format
 * @param options - Array of option objects
 * @returns Array of normalized options with string labels and values
 */
export function normalizeOptions<T extends { label?: string; value?: any }>(options: T[] = [] as T[]) {
  return options.map(opt => ({
    label: opt.label ?? '',
    value: String(opt.value ?? ''),
  }));
}

// Define presets for date picker
const basePresets = {
  Today: [dayjs().startOf('day')
    .format('YYYY-MM-DD'), dayjs().endOf('day')
    .format('YYYY-MM-DD')],
  Yesterday: [
    dayjs().subtract(1, 'day')
      .startOf('day')
      .format('YYYY-MM-DD'),
    dayjs().subtract(1, 'day')
      .endOf('day')
      .format('YYYY-MM-DD'),
  ],
  'Last 7 Days': [
    dayjs().subtract(6, 'day')
      .startOf('day')
      .format('YYYY-MM-DD'),
    dayjs().endOf('day')
      .format('YYYY-MM-DD'),
  ],
  'Last 30 Days': [
    dayjs().subtract(29, 'day')
      .startOf('day')
      .format('YYYY-MM-DD'),
    dayjs().endOf('day')
      .format('YYYY-MM-DD'),
  ],
  'This Month': [
    dayjs().startOf('month')
      .format('YYYY-MM-DD'),
    dayjs().endOf('month')
      .format('YYYY-MM-DD'),
  ],
  'Last Month': [
    dayjs().subtract(1, 'month')
      .startOf('month')
      .format('YYYY-MM-DD'),
    dayjs().subtract(1, 'month')
      .endOf('month')
      .format('YYYY-MM-DD'),
  ],
};

export const getNameGenTaskFilterConfig = (store: {
  // dateRange?: string[];
  // dateType?: string;
  // assetName?: string[];
  // conceptNames?: string[];
  // assetTypes?: string[];
  // uploaders?: string[];
  // status?: string[];
  // originalName?: string[];
  conceptNameOptions?: Array<{ label: string, value: string }>;
  uploaderOptions?: Array<{ label: string, value: string }>;
  assetTypeOptions?: Array<{ label: string, value: string }>;
  uploadStatusOptions?: Array<{ label: string, value: string }>;
}) => [
  // Date Range Filter (start_date/synced_date) - Visible
  {
    name: SelectDataRangePickerVue,
    props: {
      date: ['', ''],
      options: [
        {
          label: 'Start Date',
          value: 'start_date',
        },
        {
          label: 'Synced Date',
          value: 'synced_date',
        },
      ],
      'disable-date': {
        after: dayjs()
          .add(0, 'days')
          .format(),
      },
      presets: basePresets,
    },
    ext: {
      key: 'dateInfo',
      label: 'Date Range',
      isAllowClose: false,
      default: {
        ...TASK_LIST_DEFAULT_FORM_VALUE.dateInfo,
      },
    },
  },
  // Asset Name (search box) - Visible
  {
    name: BaseCommonSearchInput,
    props: {
      label: 'Asset Name',
      tagInputProps: {
        class: 'w-[216px]',
      },
    },
    ext: {
      key: 'assetName',
      label: 'Asset Name',
      isAllowClose: false,
    },
  },
  // Concept Name (search with multi-select) - Visible
  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      list: store.conceptNameOptions || [],
      // placeholder: 'Select Concept Name',
      clearable: true,
      multiple: true,
      filterable: true,
      title: 'Concept Name',
    },
    ext: {
      key: 'conceptNames',
      label: 'Concept Name',
      isAllowClose: false,
    },
  },
  // Asset Type (search with multi-select) - Visible
  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      list: store.assetTypeOptions || [],
      placeholder: 'Select Asset Type',
      clearable: true,
      multiple: true,
      filterable: true,
      title: 'Type',
    },
    ext: {
      key: 'assetTypes',
      label: 'Asset Type',
      isAllowClose: false,
    },
  },
  // Uploader (search with multi-select) - Visible
  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      list: store.uploaderOptions || [],
      placeholder: 'Select Uploader',
      clearable: true,
      multiple: true,
      filterable: true,
      title: 'Creator',
    },
    ext: {
      key: 'uploaders',
      label: 'Uploader',
      isAllowClose: false,
    },
  },
  // Status (multi-select) - Visible
  {
    name: COMPONENTS_MAP['a-select'],
    props: {
      list: store.uploadStatusOptions || [],
      placeholder: 'Select Status',
      clearable: true,
      multiple: true,
      title: 'Status',
    },
    ext: {
      key: 'status',
      label: 'Status',
      isAllowClose: false,
    },
  },
  // Original Name (search box) - Collapsed
  {
    name: BaseCommonSearchInput,
    props: {
      label: 'Original Name',
      tagInputProps: {
        class: 'w-[216px]',
      },
    },
    ext: {
      key: 'originalName',
      label: 'Original Name',
      isAllowClose: false,
      isHide: true, // This makes it collapsed/hidden by default
    },
  },
] as IFormDynamicItem[];
