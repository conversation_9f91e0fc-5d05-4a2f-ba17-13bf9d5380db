import type { IRenderAssetNameRecord } from '@/store/creative/name-generator/type';
import type { files  } from 'dropbox/types/index';
import type { PrimaryTableRowEditContext } from 'tdesign-vue-next'


export type TPathOption = {
  lastName: string, // 目录名称
  parentPath: string, // 所在的目录的路径
  ext: files.FolderMetadataReference,
};


export type TCellSelectChangeCallback = (params: PrimaryTableRowEditContext<IRenderAssetNameRecord>) => void;

export type TCellInputBlurCallback = (val: string, ctx: TableEditableCellPropsParams<IRenderAssetNameRecord>) => void;
