import { h } from 'vue';
import Text from 'common/components/Text/Text.vue';

import { CREATIVE_UPLOAD_STATUS } from 'common/service/creative/name-generator/const';
import type { TCreativeUploadStatus } from 'common/service/creative/name-generator/type';
import { RollbackIcon, CheckCircleFilledIcon, ErrorCircleFilledIcon, TimeIcon } from 'tdesign-icons-vue-next';
import { Loading } from 'tdesign-vue-next';


export const getStatusElement = (row: { upload_status: TCreativeUploadStatus } & Record<string, any>) => {
  const statusElement = {
    [CREATIVE_UPLOAD_STATUS.CANCELLED]: [h(RollbackIcon), h(Text, { content: 'Cancelled' })],
    [CREATIVE_UPLOAD_STATUS.UPLOADING]: [h(Loading, { size: 'small' }), h(Text, { content: 'In Progress' })],
    [CREATIVE_UPLOAD_STATUS.SUCCESS]: [h(CheckCircleFilledIcon, { class: 'text-success-secondary min-w-[16px] min-h-[16px]' }), h(Text, { content: 'Successful' })],
    [CREATIVE_UPLOAD_STATUS.ERROR]: [h(ErrorCircleFilledIcon, { class: 'text-error-primary min-w-[16px] min-h-[16px]' }), h(Text, { content: 'Failed' })],
    [CREATIVE_UPLOAD_STATUS.WAITING]: [h(TimeIcon), h(Text, { content: 'Waiting' })],
  };
  return h('span', { class: 'flex items-center gap-x-[8px]' }, statusElement[row.upload_status]);
};
