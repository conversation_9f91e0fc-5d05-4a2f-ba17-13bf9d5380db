<template>
  <div class="flex items-center">
    <div class="flex-[1] h-full relative bg-[--td-bg-color-component] rounded-[4px] overflow-hidden">
      <div
        class="absolute h-full rounded-r-[4px]"
        :style="{
          backgroundColor: color,
          width: `${rate * 100}%`,
        }"
      />
      <div class="absolute h-full flex flex-center right-[8px] text-white-primary">{{ leftLabel }}</div>
    </div>
    <div class="ml-[6px]" :style="{ color }">
      {{ rightLabel }}
    </div>
  </div>
</template>
<script setup lang="ts">
defineProps<{
  color: string,
  rate: number,
  rightLabel: string,
  leftLabel: string,
}>();

</script>
