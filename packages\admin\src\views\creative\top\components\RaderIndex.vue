<template>
  <div class="h-[380px] p-[16px] flex flex-col border-[1px] rounded-large">
    <div class="font-bold text-large">{{ firstLabel }} {{ remark }}</div>
    <div
      ref="radarChart"
      class="flex-[1] h-full"
    />
  </div>
  <teleport to="body">
    <div
      v-if="showHoverIndex"
      class="hover-index absolute z-[10] p-[18px] bg-[#000] opacity-[0.85] rounded-large text-white"
      :style="{
        left: `${hoverLeft}px`,
        top: `${hoverTop}px`,
      }"
    >
      <div class="mb-[6px]">{{ curIndex.secondLabel }}</div>
      <div class="flex items-center">
        <div
          class="rounded-[4px] w-[8px] h-[8px] mr-[4px]"
          :style="{ backgroundColor: color }"
        />
        <div class="mr-[12px]">{{ indexName }}</div>
        <div>{{ formatVal(curIndex.value) }}</div>
      </div>
      <div v-if="indexName !== 'Creatives'" class="flex items-center">
        <div
          class="rounded-[4px] w-[8px] h-[8px] mr-[4px]"
          :style="{ backgroundColor: color }"
        />
        <div class="mr-[12px]">Creatives</div>
        <div>{{ curIndex.asset_num }}</div>
      </div>
    </div>
  </teleport>
</template>
<script setup lang="ts">
import { reactive, computed, onMounted, ref, nextTick, onUnmounted } from 'vue';
import * as echarts from 'echarts/core';
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { RadarChart } from 'echarts/charts';
import { TopLabelRes } from 'common/service/creative/top/type';

use([CanvasRenderer, RadarChart, TitleComponent, TooltipComponent, LegendComponent]);

const props = defineProps<{
  firstLabel: string;
  remark?: string;
  metric: string;
  indexName: string;
  checkedLabels: string[];
  color: string;
  maxVal: number;
  list: TopLabelRes['list'];
  formatVal: Function;
}>();

const emit = defineEmits(['change']);

// 鼠标hover展示的数值
const showVal = computed(() => props.formatVal(curIndex.value));

// 是否是百分比指标
const isPercent = computed(() => showVal.value.slice(-1) === '%');

const activeColor = '#5086F3';

const showHoverIndex = ref(false);
const curIndex = reactive({
  secondLabel: '',
  value: 0,
  asset_num: '0',
});
const hoverLabels = ref<string[]>([]); // 鼠标移动上去的标签

// 雷达图配置
const options = computed(() => {
  const values = props.list.map(item => item.value);
  const radarIndicator = props.list.map((item, index) => {
    const { second_label: secondLabel } = item;
    const showName = secondLabel;
    if (index === 0) return { name: showName, max: props.maxVal };
    return {
      name: showName,
      max: props.maxVal,
      axisLabel: { show: false },
      axisTick: {
        alignTicks: false, // 禁用刻度对齐
      },
    };
  });

  return {
    title: {
      show: false,
    },
    legend: {
      data: [props.indexName],
      show: false,
    },
    radar: {
      triggerEvent: true,
      indicator: radarIndicator,
      axisName: {
        color: '#333',
        formatter(value: string) {
          const fsLabel = `${props.firstLabel}---${value}`;
          const active = props.checkedLabels.includes(fsLabel);
          const hovered = hoverLabels.value.includes(fsLabel);
          if (active || hovered) return `{active|${value}}`;
          return value;
        },
        rich: {
          active: {
            color: activeColor,
            fontWeight: 'bold',
          },
        },
      },
      axisLabel: {
        show: true,
        color: '#333',
        formatter(value: number) {
          return isPercent.value ? `${(value * 100).toFixed(2)}%` : value; // 百分比指标，为每个刻度值添加百分号
        },
      },
    },
    series: [
      {
        type: 'radar',
        color: props.color,
        data: [
          {
            value: values,
            name: props.indexName,
            areaStyle: { color: props.color },
          },
        ],
      },
    ],
  };
});

const hoverLeft = ref(200); // 浮框x轴坐标
const hoverTop = ref(200); // 浮框y轴坐标

const radarChart = ref();
const curChart = ref();
const renderChart = () => {
  const myChart = echarts.init(radarChart.value);

  // 鼠标移动到标签
  myChart.on('mouseover', (params: any) => {
    if (params.componentType !== 'radar' || params.targetType !== 'axisName') return;

    const secondLabel = getSecondLabel(params.name);
    const fsLabel = `${props.firstLabel}---${secondLabel}`;
    if (!hoverLabels.value.includes(fsLabel)) {
      hoverLabels.value.push(fsLabel);
    }

    showHoverIndex.value = true;
    curIndex.secondLabel = secondLabel;
    const target = props.list.find(item => item.second_label === secondLabel)!;
    if (!secondLabel) return;

    curIndex.value = target.value as number;
    curIndex.asset_num = target.asset_num || '0';
    hoverLeft.value = params.event.event.clientX;
    hoverTop.value = params.event.event.clientY;

    nextTick(() => myChart.setOption(options.value));
  });

  // 鼠标移出标签
  myChart.on('mouseout', (params) => {
    if (params.componentType !== 'radar') return;

    const secondLabel = getSecondLabel(params.name);
    const fsLabel = `${props.firstLabel}---${secondLabel}`;
    if (hoverLabels.value.includes(fsLabel)) {
      hoverLabels.value = hoverLabels.value.filter(item => item !== fsLabel);
    }

    showHoverIndex.value = false; // 隐藏浮框

    nextTick(() => myChart.setOption(options.value));
  });

  // 点击指标
  myChart.on('click', (params) => {
    if (params.componentType !== 'radar') return;

    const secondLabel = getSecondLabel(params.name);
    emit('change', `${props.firstLabel}---${secondLabel}`);
  });

  myChart.setOption(options.value, true); // 不做合并，直接替换

  curChart.value = myChart;
};

function getSecondLabel(name: string) {
  const activeReg = /{(.*?)}/.exec(name);
  if (activeReg) {
    const [, secondLabel] = activeReg[1].split('|');
    return secondLabel;
  }
  return name;
}

const resize = () => {
  curChart.value.resize({ width: 'auto', height: 'auto' });
};

onMounted(() => {
  renderChart();
});

onUnmounted(() => {
  curChart.value?.dispose();
});

defineExpose({
  resize,
});
</script>
<style lang="scss">
.labels-radar-name {
  color: red;
}
</style>
