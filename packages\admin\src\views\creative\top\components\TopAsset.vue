<template>
  <div class="top-asset relative flex border-[1px] rounded-large p-[12px]">
    <t-tooltip
      v-if="data.is_new"
      content="New in top"
    >
      <div
        v-if="data.is_new"
        class="absolute left-0 top-0 w-[44px] h-[44px]"
      >
        <img
          class="w-full h-full"
          src="@/assets/img/creatives/top_new.png"
          alt=""
        >
      </div>
    </t-tooltip>
    <div class="flex flex-col w-[42px] pt-[12px]">
      <div class="absolute flex flex-col w-[36px] items-center top-[40%] left-[6px]">
        <div
          v-if="scoreStyle"
          class="w-full flex justify-center font-bold text-xl"
          :style="{
            color: scoreStyle.color,
            backgroundColor: scoreStyle.bgColor,
          }"
        >
          <t-tooltip
            v-if="scoreStyle.text === '--'"
            content="Insufficient quantity, no rating yet"
          >
            {{ scoreStyle.text }}
          </t-tooltip>
          <template v-else>
            {{ scoreStyle.text }}
          </template>
        </div>
        <div class="text-lg font-bold">{{ top < 10 ? '0' : '' }}{{ top }}</div>
        <div
          v-if="data.rank !== 0"
          class="pr-[2px] w-full flex justify-center items-center"
          :style="{
            color: COLORS[colorIndex],
            backgroundColor: BG_COLORS[colorIndex],
          }"
        >
          <caret-up-small-icon
            v-if="data.rank_type == 1"
            :color="COLORS[colorIndex]"
            size="12"
          />
          <caret-down-small-icon
            v-if="data.rank_type == 2"
            :color="COLORS[colorIndex]"
            size="12"
          />
          <span>{{ data.rank }}</span>
        </div>
      </div>
    </div>
    <div class="flex flex-[1] w-[1px] flex-col mt-[12px]">
      <div class="flex items-center">
        <Text
          style="font-size: 16px"
          class="inline-block hover-active flex-[1]"
          :content="data.asset_name"
          :overflow="true"
          :need-cursor="true"
          :tool-tip="true"
          type="subTitle"
          tips-placement="top-left"
          @click="goToDetail"
        />
        <div class="ml-[24px] flex items-center">
          <TimeIcon
            class="text-black-secondary mr-[6px]"
            size="16"
          />
          <span class="text-black-secondary">{{ data.impression_date }}</span>
        </div>
      </div>
      <div ref="rightContentEle" class="flex pt-[12px]">
        <div>
          <t-image
            :src="data.preview_url"
            fit="cover"
            class="w-[142px] h-[80px] rounded-large bg-[#eee] cursor-pointer"
            @click="previewAsset"
          >
            <template #overlayContent>
              <div
                v-if="isVideo && data.preview_url"
                class="flex h-full flex-center"
              >
                <PlayCircleIcon
                  color="white"
                  class="bg-[#999] rounded-[50%]"
                  size="22"
                />
              </div>
            </template>
            <template #error>
              <image-error-icon size="24" />
            </template>
            <template #loading>
              <t-loading size="small" />
            </template>
          </t-image>
        </div>
        <div class="flex flex-[1] flex-col">
          <div class="flex">
            <div class="flex-[1] mx-[12px]">
              <div class="text-gray-primary mb-[6px]">{{ spendMetric.title }}</div>
              <progress-bar
                class="h-[18px] mb-[18px]"
                :color="COLORS[0]"
                :rate="data.spend_rate"
                :left-label="data.spend__formatted"
                :right-label="data.spend_rate__formatted"
              />
            </div>
            <div class="flex-[1] mx-[12px]">
              <div class="text-gray-primary mb-[6px]">{{ installsMetric.title }}</div>
              <progress-bar
                class="h-[18px] mb-[18px]"
                :color="COLORS[1]"
                :rate="data.installs_rate"
                :left-label="data.installs__formatted"
                :right-label="data.installs_rate__formatted"
              />
            </div>
          </div>
          <div
            class="flex text-gray-primary mx-[12px]"
          >
            <template
              v-for="(item, index) in BOTTOM_METRICS.slice(0, visibleCount)"
              :key="index"
            >
              <div class="flex items-center">
                <span class="mr-[4px]">{{ item.label }}</span>
                <span>{{ data[`${item.value}__formatted` as keyof TFormattedFields] }}</span>
                <span
                  v-if="index < visibleCount - 1"
                  class="inline-block divider w-[1px] h-[18px] bg-[#DCDFE8] mx-[12px]"
                />
              </div>
            </template>
            <t-tooltip v-if="overflowMetrics.length > 0">
              <template #content>
                <div class="flex flex-center">
                  <template
                    v-for="(item, index) in overflowMetrics"
                    :key="index"
                  >
                    <div class="flex items-center">
                      <span class="mr-[4px]">{{ item.label }}</span>
                      <span>{{ data[`${item.value}__formatted` as keyof TFormattedFields] }}</span>
                      <span
                        v-if="index < overflowMetrics.length - 1"
                        class="inline-block divider w-[1px] h-[14px] bg-[#DCDFE8] mx-[8px]"
                      />
                    </div>
                  </template>
                </div>
              </template>
              <div class="flex items-center cursor-pointer select-none">
                <span class="bg-[#E0E1E6] px-[12px] rounded-[12px] ml-[12px]">+{{ overflowMetrics.length }}</span>
              </div>
            </t-tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useElementSize } from '@vueuse/core';
import Text from 'common/components/Text';
import { TFormattedFields, TopAssetItemFormatted } from '@/store/creative/top/type';
import ProgressBar from './ProgressBar.vue';
import { ImageErrorIcon, CaretDownSmallIcon, CaretUpSmallIcon, PlayCircleIcon, TimeIcon } from 'tdesign-icons-vue-next';
import { COLORS, BG_COLORS, SCORE_LIST } from '../const';
import { BOTTOM_METRICS } from '@/store/creative/top/const';
import { IMetricItem } from 'common/service/creative/dashboard-af/type';

const props = defineProps<{
  top: number;
  data: TopAssetItemFormatted;
  allMetrics: IMetricItem[];
}>();

const isVideo = computed(() => props.data.extra_asset_type === 'VIDEO');

const emit = defineEmits(['detail', 'preview']);

const colorIndex = computed(() => {
  if (props.data.rank_type === 1) return 1;
  if (props.data.rank_type === 2) return 2;
  return 0;
});

const scoreStyle = computed(() => {
  const target = SCORE_LIST.find(item => item.value === props.data.rate_level);
  if (!target) return null;

  return {
    text: target.label,
    color: target.color,
    bgColor: target.bgColor,
  };
});

const goToDetail = () => {
  emit('detail', props.data);
};

const previewAsset = () => {
  emit('preview', props.data);
};

const rightContentEle = ref(null);
const { width } = useElementSize(rightContentEle);

const visibleCount = ref(BOTTOM_METRICS.length);
const overflowMetrics = ref<typeof BOTTOM_METRICS>([]);

const ITEM_MIN_WIDTH = 120;

watch(width, (newWidth) => {
  if (!newWidth) return;
  const validwidth = newWidth - 142;
  const maxCount = Math.floor(validwidth / ITEM_MIN_WIDTH);
  if (maxCount >= BOTTOM_METRICS.length) {
    visibleCount.value = BOTTOM_METRICS.length;
    overflowMetrics.value = [];
  } else if (maxCount > 0) {
    visibleCount.value = maxCount;
    overflowMetrics.value = BOTTOM_METRICS.slice(visibleCount.value);
  } else {
    visibleCount.value = 0;
    overflowMetrics.value = BOTTOM_METRICS;
  }
});

const spendMetric = computed(() => props.allMetrics!.find(m => m.key === 'spend')!);
const installsMetric = computed(() => props.allMetrics!.find(m => m.key === 'installs')!);
</script>
<style lang="scss">
.hover-active:hover {
  color: var(--aix-text-color-brand) !important;
}

.data-item {
  position: relative;
  flex: 1;
  justify-content: center;
}
</style>
