<template>
  <div class="top-asset relative flex border-[1px] rounded-large p-[12px]">
    <div class="flex flex-col w-[42px] pt-[12px]">
      <div>
        <div v-if="top > 3" class="absolute top-[22px] text-lg font-bold mr-[12px]">
          {{ top < 10 ? '0' : '' }}{{ top }}
        </div>
        <div v-else class="absolute left-[-6px] top-[-1px]">
          <svg-icon :name="'rank' + top" size="48" />
        </div>
      </div>
      <div
        v-if="data.rank !== 0"
        class="absolute flex items-center top-[50%] pr-[2px]"
        :style="{
          color: COLORS[colorIndex],
          backgroundColor: BG_COLORS[colorIndex],
        }"
      >
        <caret-up-small-icon v-if="data.rank_type == 1" :color="COLORS[colorIndex]" size="12" />
        <caret-down-small-icon v-if="data.rank_type == 2" :color="COLORS[colorIndex]" size="12" />
        <span>{{ data.rank }}</span>
      </div>
    </div>
    <div class="flex flex-[1] w-[1px] flex-col mt-[12px]">
      <div class="flex items-center gap-x-[16px] ml-[12px]">
        <Text
          :content="data.second_label"
          weight="bold"
          type="navTitle"
          need-cursor
          @click="() => emit('detail')"
        />
        <Text
          :content="data.first_label"
          color="var(--aix-text-color-gray-primary)"
          type="navTitle"
        />
      </div>
      <div class="flex h-full pt-[12px]">
        <div class="flex-[1] mx-[12px]">
          <div class="text-gray-primary mb-[6px]">Spend</div>
          <progress-bar
            class="h-[18px] mb-[18px]"
            :color="COLORS[0]"
            :rate="data.spend_rate"
            :left-label="data.spend__formatted"
            :right-label="data.spend_rate__formatted"
          />
          <div class="flex text-gray-primary whitespace-nowrap">
            <div class="mr-[24px]">CTR {{ data.ctr__formatted }}</div>
            <div>CVR {{ data.cvr__formatted }}</div>
          </div>
        </div>
        <div class="flex-[1] mx-[12px]">
          <div class="text-gray-primary mb-[6px]">Installs</div>
          <progress-bar
            class="h-[18px] mb-[18px]"
            :color="COLORS[1]"
            :rate="data.installs_rate"
            :left-label="data.installs__formatted"
            :right-label="data.installs_rate__formatted"
          />
          <div class="flex text-gray-primary whitespace-nowrap">
            <div class="mr-[24px]">IPM {{ data.ipm__formatted }}</div>
            <div>CPI {{ data.cpi__formatted }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue';
import Text from 'common/components/Text';
import type { TTopLabelItemFormatted } from '@/store/creative/top/type';
import { SvgIcon } from 'common/components/SvgIcon';
import ProgressBar from './ProgressBar.vue';
import { CaretDownSmallIcon, CaretUpSmallIcon } from 'tdesign-icons-vue-next';
import { COLORS, BG_COLORS } from '../const';

const props = defineProps<{
  top: number,
  data: TTopLabelItemFormatted,
}>();


const emit = defineEmits(['detail']);

const colorIndex = computed(() => {
  if (props.data.rank_type === 1) return 1;
  if (props.data.rank_type === 2) return 2;
  return 0;
});


</script>
<style lang="scss">
.hover-active:hover {
  color: var(--aix-text-color-brand) !important;
}
</style>
