<template>
  <t-popup trigger="click" @visible-change="onTopVisible">
    <t-button
      variant="text"
      :loading="props.loading"
    >
      <template #icon><arrow-up-down1-icon /></template>Top Setting
    </t-button>
    <template #content>
      <t-radio-group
        :model-value="modelValue"
        class="flex flex-col top-asset-setting"
        @change="onTopSelect"
      >
        <t-radio
          v-for="item in topList" :key="item.value" class="my-[6px]"
          :value="item.value"
        >
          <template v-if="item.value > 0">{{ item.label }}</template>
          <div v-else>
            <span class="mr-[4px]">Top</span>
            <t-input-number
              v-model="customTop" style="width: 60px" theme="normal"
              :status="validTopVal ? '' : 'error'"
              :tips="validTopVal ? '' : 'number is invalid'"
              placeholder=""
              size="small" :min="1" :max="maxTop"
            />
          </div>
        </t-radio>
      </t-radio-group>
    </template>
  </t-popup>
</template>
<script setup lang="ts">
import { ArrowUpDown1Icon } from 'tdesign-icons-vue-next';
import { computed, ref, watchEffect } from 'vue';

const props = defineProps({
  value: {
    type: Number,
    default: 20,
  },
  loading: {
    type: Boolean,
    default: false,
  },
});
const modelValue = ref(props.value);

const emit = defineEmits(['change']);

const maxTop = ref(50);

const topList = ref([
  { label: 'Top5', value: 5 },
  { label: 'Top10', value: 10 },
  { label: 'Top15', value: 15 },
  { label: 'Top20', value: 20 },
  { label: 'Top30', value: 30 },
  { label: 'Top', value: -1 },
]);
const customTop = ref(props.value);
const onTopSelect = (val: number) => {
  modelValue.value = val;
  if (val === -1) return;
  customTop.value = val;
};
const validTopVal = computed(() => customTop.value >= 1
  && customTop.value <= maxTop.value
  && Number.isInteger(customTop.value),
);
const onTopVisible = (visible: Boolean) => {
  if (!visible && validTopVal.value) {
    const newTopVal = modelValue.value === -1 ? customTop.value :  modelValue.value;
    emit('change', newTopVal);
  }
};

watchEffect(() => {
  if ([5, 10, 15, 20, 30].includes(props.value)) {
    modelValue.value = props.value;
  } else {
    modelValue.value = -1;
  }
  customTop.value = props.value;
});
</script>
<style lang="scss">
.top-asset-setting {
  align-items: flex-start;
  padding: 8px 12px 12px 8px;
  label {
    margin-right: 0;
  }
}
</style>
