<template>
  <div class="h-[98px] flex border-[1px] rounded-large overflow-hidden">
    <div
      class="w-[120px] flex flex-center text-lg"
      :style="{ backgroundColor: bgColor, color: textColor }"
    >
      {{ data.name }}
    </div>
    <div class="index flex-[1] flex-center">
      <div>
        <div class="text-lg text-gray-primary">Total</div>
        <div class="text-2xl">{{ formatVal(data.total, data.index) }}</div>
      </div>
    </div>
    <div v-if="!hideTopTotal" class="index flex-[1] flex-center">
      <div>
        <div class="text-lg text-gray-primary">Top Total</div>
        <div class="text-2xl">{{ formatVal(data.top_total, data.index) }}</div>
      </div>
    </div>
    <div v-if="!hideProgress" class="flex flex-[1] flex-center">
      <t-progress
        theme="circle" :percentage="data.percent" :size="70"
        :color="textColor"
        :stroke-width="10"
      >
        <template #label>
          <span :style="{ color: textColor }">
            {{ Number.isNaN(data.percent) ? '-' : `${data.percent}%` }}</span>
        </template>
      </t-progress>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import { TopData } from 'common/service/creative/top/type';
import { BG_COLORS, COLORS } from '../const';
import { formatVal } from '@/views/creative/label/insight/utils';

const props = defineProps<{
  data: TopData,
  hideTopTotal?: boolean, // 隐藏top total
  hideProgress?: boolean, // 隐藏进度条
}>();

const bgColor = computed(() => {
  if (props.data.index === 'spend') return BG_COLORS[0];
  if (props.data.index === 'installs') return BG_COLORS[1];
  return '';
});


const textColor = computed(() => {
  if (props.data.index === 'spend') return COLORS[0];
  if (props.data.index === 'installs') return COLORS[1];
  return '';
});
</script>
