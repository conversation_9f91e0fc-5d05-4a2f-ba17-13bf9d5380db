<template>
  <CommonView :hide-right="true">
    <template #views>
      <div class="flex flex-col flex-[1] p-[16px] bg-white-primary rounded-extraLarge">
        <div class="flex mb-[16px]">
          <t-button
            style="padding: 0 12px" class="mr-[12px]" variant="outline"
          >
            <div class="flex items-center" @click="showMetric = true">
              <span class="mr-[6px]">{{ orderLabel }}</span>
              <chevron-down-icon />
            </div>
          </t-button>
          <div class="tags">
            <t-tag
              v-for="second in firstSecondLabels" :key="second" theme="primary"
              variant="light"
              size="large"
              closable
              class="mr-[12px]"
              @close="() => labelChange(second)"
            >
              {{ second }}
            </t-tag>
          </div>
        </div>
        <div v-if="loading || labelData.length === 0" class="text-gray-primary flex flex-center py-[100px]">
          <span v-if="loaded && !loading && labelData.length === 0">No Data</span>
          <t-loading v-if="loading" />
        </div>
        <div v-else class="grid grid-cols-3 gap-6 mb-[24px]">
          <template v-for="(item, index) in renderLabelData" :key="index">
            <radar-index
              v-if="!loading"
              ref="radarsRef"
              :metric="metricFilter.metric"
              :index-name="orderLabel"
              :checked-labels="firstSecondLabels"
              :color="RADAR_COLORS[index % RADAR_COLORS.length]"
              :first-label="item.first_label"
              :remark="item.remark"
              :max-val="maxVal"
              :list="item.list"
              :format-val="radarValFormat"
              @change="labelChange"
            />
          </template>
        </div>
      </div>
    </template>
  </CommonView>
  <MetricFilterDialog
    v-model:visible="showMetric"
    :model-value="metricFilter"
    :options="metricFilterOptions"
    title="Please Select"
    @update:model-value="updateMetricFilter"
    @confirm="() => getData()"
  />
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import { storeToRefs } from 'pinia';
import { ChevronDownIcon } from 'tdesign-icons-vue-next';
import CommonView from 'common/components/Layout/CommonView.vue';
import RadarIndex from './components/RaderIndex.vue';
import { useRoute } from 'vue-router';
import { useCustomView } from 'common/compose/useCustomView';
import { useTopCreativesLabelsStore } from '@/store/creative/top/top-creatives-labels.store';
import { calculateMaxValue, isIgnoreMetrics } from '@/store/creative/top/utils';
import MetricFilterDialog from 'common/components/MetricFilterDialog';
import { RADAR_COLORS } from '@/views/creative/common/const';
import { formatVal } from '@/views/creative/label/insight/utils';
import { TopLabelRes } from 'common/service/creative/top/type';

const { query } = useRoute();
const { setParams, getData, labelChange, updateMetricFilter, initOptions } = useTopCreativesLabelsStore();
const {
  labelData, firstSecondLabels, loading, loaded, metricFilter, orderLabel, metricFilterOptions, allMetrics,
  firstLabelList,
} = storeToRefs(useTopCreativesLabelsStore());

const maxVal = computed(() => {
  const allList = labelData.value.map(item => item.list);
  const values = allList.reduce((total, cur) => total.concat(cur), []).map(item => item.value) as number[];
  return calculateMaxValue(values);
});

const renderLabelData = computed(() => {
  // 在一级标签中的
  const labelDataInFirstLabel: TopLabelRes[] = [];
  // 按照一级标签进行排序
  for (const firstLabelItem of firstLabelList.value) {
    const labelDataItem = labelData.value.find(item => item.first_label === firstLabelItem.value);
    if (labelDataItem) labelDataInFirstLabel.push(labelDataItem);
  }

  const firstLabels = labelDataInFirstLabel.map(item => item.first_label);
  // 不在一级标签中的部分
  const labelDataNotInFirstLabel = labelData.value.filter(item => !firstLabels.includes(item.first_label));
  return [
    ...labelDataInFirstLabel,
    ...labelDataNotInFirstLabel,
  ];
});

const { getShare } = useCustomView(); // 获取assetNames参数

const codeToAssets = async (code: string, sDate: string, eDate: string) => {
  if (!code) return;

  const res = await getShare(code);
  const assetNames = res.param as string[];
  setParams(sDate, eDate, assetNames);
  getData();
};

const showMetric = ref(false);

const radarsRef = ref([]);
const resizeChart = () => {
  radarsRef.value.forEach((item: InstanceType<typeof RadarIndex>) => {
    item.resize();
  });
};

const radarValFormat = (val: number) => formatVal(val, metricFilter.value.metric, allMetrics.value);

onMounted(async () => {
  const { code, sDate, eDate, index } = query;

  // 忽略部分指标
  if (!isIgnoreMetrics(index as string)) {
    metricFilter.value.metric = index as string;
  }

  await initOptions(); // 等待配置数据加载完成
  codeToAssets(code as string, sDate as string, eDate as string);

  // 窗口大小变化，调整图的宽高
  window.onresize = useDebounceFn(resizeChart, 500);
});

onUnmounted(() => {
  window.onresize = null;
});
</script>
<style scoped lang="scss"></style>
