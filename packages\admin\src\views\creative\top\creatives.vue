<template>
  <CommonView
    :form-props="{
      formList,
      modelValue: formModelValue,
      onSubmit: getTableData,
      foldList,
      onReset,
      'onUpdate:modelValue': updateFormModelValue,
      'onUpdate:foldList': setFoldList,
      isShowGlobalLoading: isViewLoading,
      confirmDisabled: isViewLoading || isFetchDataLoading,
      resetDisabled: isViewLoading || isFetchDataLoading,
    }"
    :tab-props="{
      list: viewList,
      modelValue: viewId,
      shareParams: shareViewParams,
      'onUpdate:modelValue': updateViewId,
      isShowViewType: false,
      onAddView: addView,
      onUpdateView: updateView,
      onDeleteView: deleteView
    }"
  >
    <template #views>
      <div class="flex flex-col flex-[1] p-[16px] bg-white-primary rounded-extraLarge">
        <div class="flex flex-col overflow-hidden">
          <div class="flex justify-between mb-[12px]">
            <div>
              <t-button
                style="padding: 0 12px" class="mr-[12px]" variant="outline"
                :data-value="JSON.stringify(metricFilterModelValue)"
                :loading="isFetchDataLoading || isCustomRulesLoading"
                @click="() => metricFilterDialogVisible = true"
              >
                <span class="text-gray-primary mr-[12px]">Standard Rule</span>

                <t-tooltip :content="orderbyMetricLabel">
                  <span class="bg-brand text-white-primary px-[12px] rounded-[12px] max-w-[250px] truncate">
                    {{ orderbyMetricLabel }}
                  </span>
                </t-tooltip>
              </t-button>
              <t-button variant="outline" @click="toLabels">
                <svg-icon name="radar" size="24" color="#5086F3" />
                <span class="text-brand ml-[8px]">Labels</span>
              </t-button>
            </div>
            <div>
              <top-setting :value="topVal" :loading="isFetchDataLoading" @change="onTopChange" />
              <t-button
                variant="text" :loading="downloading"
                @click="downloadAll"
              >
                <template #icon><download-icon /></template>Download
              </t-button>
            </div>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-6 mb-[24px]">
          <top-index v-for="(item, index) in topData" :key="index" :data="item as TopData" />
        </div>
        <div class="grid grid-cols-2 gap-6">
          <top-asset
            v-for="(item, index) in assetList" :key="index" :top="index + 1"
            :data="item as TopAssetItemFormatted"
            :all-metrics="allMetrics"
            @detail="goToDetail"
            @preview="previewAsset"
          />
        </div>
        <TopCreativesStandardRuleDialog
          v-model:visible="metricFilterDialogVisible"
          :model-value="metricFilterModelValue"
          :options="metricFilterOptions"
          :is-loading="isCustomRulesLoading"
          :rule-options-obj="{
            asset_score: SCORE_LIST,
          }"
          :excluded-metrics="['asset_score']"
          title="Please Select"
          @update:model-value="(val: IMetricFilterModelValue) => updateMetricFilterModelValue(val, true)"
        />
        <Preview
          ref="previewRef"
          :type="previewItem?.extra_asset_type === 'VIDEO' ? 'video' : 'image'"
          :title="previewItem?.asset_name"
          :hide-trigger="true"
          :get-url="getPreviewUrl"
        >
          <template #trigger><div class="none" /></template>
        </Preview>
      </div>
    </template>
  </CommonView>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { SvgIcon } from 'common/components/SvgIcon';
import CommonView from 'common/components/Layout/CommonView.vue';
import TopIndex from './components/TotalIndex.vue';
import TopAsset from './components/TopAsset.vue';
import TopSetting from './components/TopSetting.vue';
import { useTopCreativesStore } from '@/store/creative/top/top-creatives.store';
import { useGoto } from '@/router/goto';
import { FormModel } from 'common/service/creative/label/insight/type';
import { useWatchGameChange } from 'common/compose/request/game';
import { DownloadIcon } from 'tdesign-icons-vue-next';
import { useCustomView } from 'common/compose/useCustomView';
import { TopAssetItem, TopData } from 'common/service/creative/top/type';
import { useGlobalGameStore } from '@/store/global/game.store';
import Preview from 'common/components/Dialog/Preview';
import { TopAssetItemFormatted } from '@/store/creative/top/type';
import TopCreativesStandardRuleDialog from '@/store/creative/top/top-creatives-standard-rule-dialog';
import { useCreativesStandardRuleStore } from '@/store/creative/top/top-creatives-standard-rule.store';
import type { IModelValue as IMetricFilterModelValue } from 'common/components/MetricFilterDialog';
import { SCORE_LIST } from './const';

const { addShare } = useCustomView();
const { gotoTopCreativesLabels, gotoTopCreativeAssetDetail } = useGoto();
const { gameCode } = storeToRefs(useGlobalGameStore());

const store = useTopCreativesStore();
const {
  formList, formModelValue, downloading, foldList, topData, assetList, isViewLoading,
  metricFilterOptions, metricFilterModelValue, viewId, viewList, shareViewParams,
  orderbyMetricLabel, topVal, isFetchDataLoading, allMetrics,
} = storeToRefs(store);

const {
  getTableData, onReset, downloadAll, init, setFoldList, getViewItem,
  updateMetricFilterModelValue, updateView, setViewId, addView, deleteView,
  updatePageFilters, getParams,
} = store;
const { isCustomRulesLoading } = storeToRefs(useCreativesStandardRuleStore());

const metricFilterDialogVisible = ref(false);

const onTopChange = (val: number) => {
  if (val === topVal.value) return;
  topVal.value = val;
  getTableData();
};

// assetName列表换code
const getAssetsCode = async () => {
  const assetNames = assetList.value.map(item => item.asset_name);
  return addShare({
    type: 'custom', label: 'top-asset-names', value: 'assetNames', game: 'all', system: 'top-asset',
    param: assetNames,
  });
};

// 更新formModelValue数据
const updateFormModelValue = (formModel: FormModel) => {
  formModelValue.value = {
    ...formModelValue.value,
    ...formModel,
  };
};

const updateViewId = (id: string) => {
  setViewId(id);
  const viewItem = getViewItem(id);
  updatePageFilters(viewItem);
  getTableData();
};

const goToDetail = async (assetItem: TopAssetItem) => {
  const code = await getAssetsCode();
  const { date } = formModelValue.value;
  const params = getParams();
  const filter = {
    keywords: params.keywords,
    campaign_type: params.campaign_type,
    country_code: params.country_code,
    impression_date: params.impression_date,
    network: params.network,
    platform: params.platform,
    asset_type: params.asset_type,
  };
  gotoTopCreativeAssetDetail({
    game: gameCode.value,
    sDate: date[0].replaceAll('-', ''),
    eDate: date[1].replaceAll('-', ''),
    asset_name: assetItem.asset_name,
    asset_serial_id: assetItem.extra_asset_serial_id,
    asset_type: assetItem.extra_asset_type,
    url: assetItem.extra_asset_url,
    asset_id: assetItem.asset_id,
    youtube_id: assetItem.extra_youtube_id,
    index: metricFilterModelValue.value.metric,
    filter: JSON.stringify(filter), // 查询条件
    code,
  });
};

const toLabels = async () => {
  const { date } = formModelValue.value;
  const code = await getAssetsCode();
  gotoTopCreativesLabels({
    game: gameCode.value,
    sDate: date[0].replaceAll('-', ''),
    eDate: date[1].replaceAll('-', ''),
    index: metricFilterModelValue.value.metric,
    code,
  });
};

// 预览的图片或者视频
const previewItem = ref<TopAssetItem>();

const getPreviewUrl = () => Promise.resolve(previewItem.value?.extra_asset_url || '');

const previewRef = ref(); // 预览实例
const previewAsset = (data: TopAssetItem) => {
  previewItem.value = data;
  previewRef.value.show();
};

useWatchGameChange(async () => {
  init();
});
</script>
