<template>
  <BaseDialog
    ref="dialogRef"
    :title="title"
    :width="`800px`"
    placement="center"
    :is-show-footer="false"
  >
    <div>
      <t-form
        ref="form"
        :data="formData"
        :rules="formRules"
        label-align="top"
        @submit="onSubmit"
      >
        <t-form-item
          name="Campaign Name"
          :required-mark="true"
        >
          <template #label>
            <span>
              <span class="mr-2">Campaign Name</span>
              <!-- <Tooltip
                  :content="findFeildTips('Campaign Name')"
                >
                  <t-icon name="help-circle" size="16px" color="rgba(0, 0, 0, 0.4)" />
                </Tooltip> -->
            </span>
          </template>
          <t-input
            v-model="formData['Campaign Name']"
            :maxlength="100"
            show-limit-number
            clearable
          />
        </t-form-item>
        <t-form-item
          name="Region"
          :required-mark="true"
        >
          <template #label>
            <span>
              <span class="mr-2">Campaign Owner_Region/Country</span>
              <Tooltip
                :content="findFeildTips('Region')"
              >
                <t-icon name="help-circle" size="16px" color="rgba(0, 0, 0, 0.4)" />
              </Tooltip>
            </span>
          </template>
          <t-cascader
            v-model="formData.Region"
            clearable
            filterable
            :show-all-levels="false"
            :options="countryFlatOptions"
          />
        </t-form-item>
        <t-form-item
          name="Channel Link"
          :required-mark="true"
        >
          <template #label>
            <span>
              <span class="mr-2">Channel Link</span>
              <Tooltip
                :content="findFeildTips('Channel Link')"
              >
                <t-icon name="help-circle" size="16px" color="rgba(0, 0, 0, 0.4)" />
              </Tooltip>
            </span>
          </template>
          <t-input
            v-model="formData['Channel Link']"
            show-limit-number
            clearable
          />
        </t-form-item>
        <!-- 两列布局的表单item 01 -->
        <div class="flex gap-x-4">
          <div class="flex-1 h-[83px]">
            <t-form-item
              name="Content"
              :required-mark="true"
            >
              <template #label>
                <span>
                  <span class="mr-2">Content</span>
                  <Tooltip
                    :content="findFeildTips('Content')"
                  >
                    <t-icon name="help-circle" size="16px" color="rgba(0, 0, 0, 0.4)" />
                  </Tooltip>
                </span>
              </template>
              <t-cascader
                v-model="formData.Content"
                clearable
                filterable
                :show-all-levels="false"
                :options="contentOptions"
              />
            </t-form-item>
          </div>
          <div class="flex-1 h-[83px]">
            <t-form-item
              name="Additional Rights"
            >
              <template #label>
                <span>
                  <span class="mr-2">Additional Rights</span>
                  <Tooltip
                    :content="findFeildTips('Additional Rights')"
                  >
                    <t-icon name="help-circle" size="16px" color="rgba(0, 0, 0, 0.4)" />
                  </Tooltip>
                </span>
              </template>
              <t-cascader
                v-model="formData['Additional Rights']"
                clearable
                filterable
                :show-all-levels="false"
                :options="ADDITIONAL_REGHTS_LV_OPTIONS"
              />
            </t-form-item>
          </div>
        </div>
        <!-- 两列布局的表单item 02 -->
        <div class="flex gap-4">
          <div class="flex-1 h-[83px]">
            <t-form-item
              name="Format"
              :required-mark="true"
            >
              <template #label>
                <span>
                  <span class="mr-2">Format</span>
                  <Tooltip
                    :content="findFeildTips('Format')"
                  >
                    <t-icon name="help-circle" size="16px" color="rgba(0, 0, 0, 0.4)" />
                  </Tooltip>
                </span>
              </template>
              <t-radio-group
                v-model="formData.Format"
                default-value="1"
                class="w-full"
              >
                <t-radio-button v-for="(item, index) in KOL_FORMAT_OPTIONS" :key="index" :value="item.value">{{ item.label }}</t-radio-button>
              </t-radio-group>
            </t-form-item>
          </div>
          <div class="flex-1 h-[83px]">
            <t-form-item
              :required-mark="true"
              name="Publish Date"
            >
              <template #label>
                <span>
                  <span class="mr-2">Publish Date</span>
                  <Tooltip
                    :content="findFeildTips('Publish Date')"
                  >
                    <t-icon name="help-circle" size="16px" color="rgba(0, 0, 0, 0.4)" />
                  </Tooltip>
                </span>
              </template>
              <!-- :presets="presets" -->
              <!-- :max-date="dayjs().format(FORMAT)" -->
              <DateRangePicker
                v-model="formData['Publish Date']"
                value-type=""
                :clearable="true"
              />
            </t-form-item>
          </div>
        </div>
        <!-- 两列布局的表单item 03 -->
        <div class="flex gap-4">
          <div class="flex-1 h-[83px]">
            <t-form-item
              name="Cost"
              :required-mark="true"
            >
              <template #label>
                <span>
                  <span class="mr-2">Cost($)</span>
                  <Tooltip
                    :content="findFeildTips('Cost')"
                  >
                    <t-icon name="help-circle" size="16px" color="rgba(0, 0, 0, 0.4)" />
                  </Tooltip>
                </span>
              </template>
              <t-input-number
                v-model="formData.Cost"
                class="pzh-custom-input-number"
                :max="10000000000"
                theme="column"
              />
            </t-form-item>
          </div>
          <div class="flex-1 h-[83px]">
            <t-form-item
              name="Quotation"
              :required-mark="false"
            >
              <template #label>
                <span>
                  <span class="mr-2">Quotation($)</span>
                  <!-- :content="findFeildTips('Quotation')" -->
                  <Tooltip
                    :content="findFeildTips('Quotation')"
                  >
                    <t-icon name="help-circle" size="16px" color="rgba(0, 0, 0, 0.4)" />
                  </Tooltip>
                </span>
              </template>
              <t-input-number
                v-model="formData.Quotation"
                class="pzh-custom-input-number"
                :min="0"
                :max="10000000000"
                theme="column"
              />
            </t-form-item>
          </div>
        </div>
        <t-form-item
          v-if="isShowTargetStreamHrs"
          name="Target Stream Hrs"
          :required-mark="true"
        >
          <template #label>
            <span>
              <span class="mr-2">Target Stream Hrs</span>
              <Tooltip
                :content="findFeildTips('Target Stream Hrs')"
              >
                <t-icon name="help-circle" size="16px" color="rgba(0, 0, 0, 0.4)" />
              </Tooltip>
            </span>
          </template>
          <t-input
            v-model="formData['Target Stream Hrs']"
            :maxlength="100"
            show-limit-number
            clearable
          />
        </t-form-item>
        <t-form-item
          v-if="isShowDeliverableLink"
          name="Deliverable Link"
        >
          <template #label>
            <span>
              <span class="mr-2">Deliverable Link</span>
              <Tooltip
                :content="findFeildTips('Deliverable Link')"
              >
                <t-icon name="help-circle" size="16px" color="rgba(0, 0, 0, 0.4)" />
              </Tooltip>
            </span>
          </template>
          <t-input
            v-model="formData['Deliverable Link']"
            :maxlength="1000"
            show-limit-number
            clearable
          />
        </t-form-item>
        <t-form-item
          name="Destination Link"
          :required-mark="false"
        >
          <template #label>
            <span>
              <span class="mr-2">Destination Link</span>
              <Tooltip
                :content="findFeildTips('Destination Link')"
              >
                <t-icon name="help-circle" size="16px" color="rgba(0, 0, 0, 0.4)" />
              </Tooltip>
            </span>
          </template>
          <t-input
            v-model="formData['Destination Link']"
            :maxlength="1000"
            show-limit-number
            clearable
          />
        </t-form-item>
        <t-form-item
          name="Tracking Link"
          :required-mark="false"
        >
          <template #label>
            <span>
              <span class="mr-2">Tracking Link</span>
              <Tooltip
                :content="findFeildTips('Tracking Link')"
              >
                <t-icon name="help-circle" size="16px" color="rgba(0, 0, 0, 0.4)" />
              </Tooltip>
            </span>
          </template>
          <t-input
            v-model="formData['Tracking Link']"
            :maxlength="1000"
            show-limit-number
            clearable
          />
        </t-form-item>
        <t-form-item
          name="Custom Tags"
          :required-mark="false"
        >
          <template #label>
            <span>
              <span class="mr-2">Custom Tags</span>
              <Tooltip
                :content="findFeildTips('Custom Tags')"
              >
                <t-icon name="help-circle" size="16px" color="rgba(0, 0, 0, 0.4)" />
              </Tooltip>
            </span>
          </template>
          <t-input
            v-model="formData['Custom Tags']"
            :maxlength="500"
            show-limit-number
            clearable
          />
        </t-form-item>
        <t-form-item
          v-if="isEditType"
          name="Language"
        >
          <template #label>
            <span>
              <span class="mr-2">Language</span>
            </span>
          </template>
          <t-select
            v-model="formData.Language"
            class="demo-select-base"
            clearable
            filterable
            @change="onChangeLanguage"
          >
            <t-option
              v-for="(item, index) in languageOptions" :key="index" :value="item.value"
              :label="item.label"
            >
              {{ item.label }}
            </t-option>
          </t-select>
        </t-form-item>
        <t-form-item>
          <div class="w-full flex justify-end">
            <Space size="small">
              <Button
                theme="default"
                variant="base"
                @click="() => dialogRef?.hide()"
              >
                Cancel
              </Button>
              <Button
                :loading="formSubmitBtnLoading"
                theme="primary"
                type="submit"
              >
                Confirm
              </Button>
              <!-- 下方示例代码，有效，勿删 -->
              <!--<t-button theme="default" @click="submitForm">实例方法提交</t-button>-->
              <!--<t-button theme="default" variant="base" @click="resetForm">实例方法重置</t-button>-->
              <!--<t-button theme="default" variant="base" @click="validateOnly">仅校验</t-button>-->
            </Space>
          </div>
        </t-form-item>
      </t-form>
    </div>
  </BaseDialog>
</template>
<script lang="ts" setup>
import BaseDialog from 'common/components/Dialog/Base';
import DateRangePicker from 'common/components/DateTimePicker';
import { ref, PropType, computed, nextTick, watch } from 'vue';
import { Button, Space, Tooltip, MessagePlugin, FormRule, CustomValidateObj } from 'tdesign-vue-next';
// import dayjs from 'dayjs';
import { useConfigurationManagementKolStore } from '@/store/influencer/campaignSetUp/kol.store';
import { storeToRefs } from 'pinia';
import { DEFAULT_FORM_DATA } from './formModalDialog.const';
import { FormDataType, FormType } from './formModalDialog.type';
import { KOL_FORMAT_OPTIONS, config, FieldValidate } from '../const/config';
import { KolFormatEnum, ADDITIONAL_REGHTS_LV_OPTIONS } from '../const/const';
import { useGlobalGameStore } from '@/store/global/game.store';

const gameStore = useGlobalGameStore();
const store = useConfigurationManagementKolStore();
const { countryFlatOptions, contentOptions, languageOptions } = storeToRefs(store);


const props = defineProps({
  managementType: {
    type: String,
    default: 'kol',
  },
  originEditFormData: {
    type: Object as PropType<FormDataType> | undefined,
    default: () => undefined,
  },
  formSubmitBtnLoading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:formSubmitBtnLoading', 'submit']);

const cfg = config[props.managementType];
// ---------------- form ----------------
const formType = ref<FormType>(FormType.CREATE);
const title = computed(() => (formType.value === FormType.CREATE ? 'Create Delivery' : 'Edit Delivery'));
const isCreateType = computed(() => formType.value === FormType.CREATE);
const isEditType = computed(() => formType.value === FormType.EDIT);
const fieldValidate = new FieldValidate();
// const FORMAT = 'YYYY-MM-DD';
const formData = ref(DEFAULT_FORM_DATA);

const isShowTargetStreamHrs = computed(() => formData.value.Format === KolFormatEnum.Stream);
const isShowDeliverableLink = computed(() => [KolFormatEnum.Video, KolFormatEnum.Shorts].includes(formData.value.Format as KolFormatEnum));
// required-mark


const formRules = computed(() => {
  const newFormRules: Record<string, Array<FormRule>> = {};
  Object.keys(cfg.validateRule)
    .filter((name: string) => !name.startsWith('$'))
    .forEach((name) => {
      if (cfg.validateRule?.[name]) {
        const ruleList = cfg.validateRule[name]?.rules ?? [];
        if (ruleList.length > 0) {
          const formRuleObj: FormRule = {
            trigger: 'change',
            validator(_val: string) {
              // console.log('test test', 'change', name, val);
              let result = true;
              let message: any = '';
              for (const ruleCfg of ruleList) {
                const ruleName = ruleCfg.rule!;
                const isOk = (fieldValidate as any)?.[ruleName]?.(
                  { data: formData.value, name, params: ruleCfg.params },
                  { gameStore, store },
                );
                if (isOk !== true) {
                  result = false;
                  message = isOk;
                  break;
                }
              }

              // 返回校验结果
              const customValidateObj: CustomValidateObj = {
                result,
                message,
              };
              console.log('test test', 'resulst', result, message);
              return customValidateObj;
            },
          };
          newFormRules[name] = [formRuleObj];
        }
      };
    });

  // console.log('test test', 'formRules', newFormRules);
  return newFormRules;
});

const isWatchFormat = ref(false);
watch(() => formData.value.Format, (newVal, oldVal) => {
  if (isWatchFormat.value) {
    console.log('test test format watch', newVal, oldVal);
    const isChangeHasStream = [newVal, oldVal].includes(KolFormatEnum.Stream); // 新旧值是否有一个是Stream
    switch (true) {
      case isChangeHasStream && isCreateType.value:
        formData.value['Target Stream Hrs'] = '';
        formData.value['Deliverable Link'] = '';
        break;
      case isChangeHasStream && isEditType.value && newVal === KolFormatEnum.Stream:
        formData.value['Target Stream Hrs'] = props?.originEditFormData?.['Target Stream Hrs'] ?? '';
        formData.value['Deliverable Link'] = '';
        break;
      case isChangeHasStream && isEditType.value && newVal !== KolFormatEnum.Stream:
        formData.value['Target Stream Hrs'] =  '';
        formData.value['Deliverable Link'] = props?.originEditFormData?.['Deliverable Link'] ?? '';
        break;
      default:
        break;
    }
  };
});
const onChangeLanguage = (val: any) => {
  console.log('test test', 'onChangeLanguage', val);
  if (val === undefined) {
    formData.value.Language = '';
  };
};
// const onChangeQuotation = (val: any) => {
//   console.log('test test', 'onChangeQuotation', val);
//   if (val === undefined) {
//     formData.value.Quotation = '';
//   };
// };

const onSubmit = async ({ validateResult, firstError }: any) => {
  emit('update:formSubmitBtnLoading', true); // 切换加载状态

  if (formData.value.Quotation === undefined) { // 数字输入框组件不填默认转成undefined; aminoac
    formData.value.Quotation = '';
  };

  if (validateResult === true) {
    // emit('submit', { formData: formData.value, formType: 'create' });
    emit('submit', { formData: formData.value, formType: formType.value });
  } else {
    console.log('Validate Errors: ', firstError, validateResult);
    MessagePlugin.warning(firstError);
    emit('update:formSubmitBtnLoading', false); // 切换加载状态
  }
};

const findFeildTips = (field: string) => cfg?.specificationTipsData
  .find((item: { title: string, desc: string}) => item.title === field)?.desc ?? '';
const dialogRef = ref();
defineExpose({
  show: () => {
    isWatchFormat.value = false;
    nextTick(() => {
      // console.log('test test', 'form edit data', props.originEditFormData);
      if (props.originEditFormData) {
        formType.value = FormType.EDIT;
        // console.log('test test', 'form edit data', props.originEditFormData);
        formData.value = { ...props.originEditFormData };
      } else {
        formType.value = FormType.CREATE;
        formData.value = { ...DEFAULT_FORM_DATA };
      };
      nextTick(() => {
        dialogRef.value.show();
        isWatchFormat.value = true;
      });
    });
  },
  hide: () => {
    isWatchFormat.value = false;
    dialogRef.value.hide();
  },
});
</script>
<style lang="scss" scoped>
:deep(.pzh-custom-no-margin) {
  .t-form__controls {
    margin-left: 0 !important;
  }
}
:deep(.pzh-custom-input-number) {
  width: 100%;
  .t-input__wrap {
    width: 100%;
  }
}
</style>
