export type FormDataType = {
  ID?: string,
  id?: string,
  'Campaign Name': string,
  'Region': string,
  'Channel Link': string,
  'Content': string,
  'Additional Rights': string,
  'Format': string,
  'Publish Date': string,
  'Cost': string,
  'Quotation': string,
  'Target Stream Hrs': string,
  'Deliverable Link': string,
  'Destination Link': string,
  'Tracking Link': string,
  'Custom Tags': string,
  'Language'?: string,
  'name'?: string,
  'picture'?: string,
  'platform'?: string,
  'channel_id'?: string,
  'lu_region'?: string,
};

export enum FormType {
  CREATE = 'create',
  EDIT = 'edit',
};
