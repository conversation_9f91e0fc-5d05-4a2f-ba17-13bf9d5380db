import { ITableCols } from 'common/components/table/type';
import { Space, Button, Dropdown, Tooltip, Tag } from 'tdesign-vue-next';
import './style.css';
import { KolManageDataModal } from '../modal/kolManage';
import { MoreIcon } from 'tdesign-icons-vue-next';
import { formatMoneyToFixed } from 'common/utils/format';
export function useKolManageTable({
  data,
  selectMetrics,
  currentUsername,
  isAdmin,
  generateTrackingLink,
  view,
  edit,
  remove,
}: {
  data: KolManageDataModal[];
  selectMetrics: string[];
  currentUsername: string | undefined;
  isAdmin: boolean;
  generateTrackingLink: (row: KolManageDataModal) => Promise<void>;
  view: (fileUrl: string, data: KolManageDataModal) => Promise<void>;
  edit: (fileUrl: string, data: KolManageDataModal) => Promise<void>;
  remove: (data: KolManageDataModal) => Promise<void>;
}) {
  const options = [
    // {
    //   content: 'View',
    //   value: 'view',
    // },
    {
      content: 'Edit',
      value: 'edit',
    },
    {
      content: 'Tracking Link',
      value: 'tracking_link',
    },
    {
      content: 'Delete',
      value: 'remove',
    },
  ];
  const tableData = data;
  const cols: ITableCols[] = [
    {
      colKey: 'id',
      title: 'ID',
      ellipsis: true,
      width: 50,
      cell: (_h: any, { row }: any) => <>{row.id}</>,
    },
    {
      colKey: 'campaign_name',
      title: 'Campaign Name',
      ellipsis: true,
      width: 200,
      cell: (_h: any, { row }: any) => <>{row.campaign_name}</>,
    },
    {
      colKey: 'country_code',
      title: 'Region',
      width: 80,
      cell: (_h: any, { row }: any) => <>{row.country_code}</>,
    },
    {
      colKey: 'influencer',
      title: 'Channel',
      width: 200,
      cell: (_h: any, { row }: any) => (
        <Tooltip content={row.name || '-'}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{ position: 'relative', display: 'inline-block', width: '48px', height: '48px' }}>
              <img
                src={row.picture || 'https://static.aix.intlgame.cn/v2/influencer/avatar/default.svg'}
                alt={row.name}
                style={{ width: '40px', height: '40px', borderRadius: '50%', marginRight: '8px',
                  position: 'absolute', top: '50%', left: '50%',
                  transform: 'translate(-50%, -50%)', objectFit: 'cover' }} // 设置头像样式
                onerror="this.onerror=null;this.src='https://static.aix.intlgame.cn/v2/influencer/avatar/default.svg';"
              />
              <img
                src={`https://static.aix.intlgame.cn/v2/influencer/channel/${row.platform.toLowerCase()}.svg`}
                alt="Icon"
                style={{
                  position: 'absolute',
                  bottom: '0',
                  right: '0',
                  width: '15px',
                  height: '15px',
                  borderRadius: '30%',
                  backgroundColor: 'white',
                  // border: '1px solid white'
                }}
                onerror="this.onerror=null;this.style.display='none'"
              />
            </div>
            <span style={{ paddingLeft: '8px',
              width: '152px',
              // class="whitespace-nowrap text-ellipsis overflow-hidden"
              flexGrow: 1, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis',
            }}>{row.name || '-'}</span>
          </div>
        </Tooltip>
      ),
    },
    {
      colKey: 'format',
      title: 'Format',
      width: 90,
      sorter: true,
      sortType: 'all',
      cell: (_h: any, { row }: any) => <>{capitalizeFirstLetter(row.format)}</>,
    },
    {
      colKey: 'cost',
      title: 'Cost',
      ellipsis: true,
      width: 100,
      sorter: true,
      sortType: 'all',
      cell: (_h: any, { row }: any) => <>${formatMoneyToFixed(row.cost)}</>,
    },
    {
      colKey: 'publish_date',
      title: 'Publish Date',
      width: 130,
      sorter: true,
      sortType: 'all',
      cell: (_h: any, { row }: any) => <>{row.publish_date}</>,
    },
    {
      colKey: 'task_status',
      title: 'Delivery Status',
      width: 120,
      cell: (_h: any, { row }: any) => <>{transStatus(row.task_status, row.task_status_msg)}</>,
    },
    {
      colKey: 'deliverable_link',
      title: 'Deliverable Link',
      ellipsis: (h, { row }) => (
        <div>
          {row.deliverable_link}
        </div>
      ),
      width: 200,
      cell: (_h: any, { row }: any) => (row.deliverable_link ? (
        <a
          href={row.deliverable_link}
          style="color: #5086f3"
        >
          {row.deliverable_link}
        </a>
      ) : (
        '-'
      )),
    },
    {
      colKey: 'tracking_link',
      title: 'Tracking Link',
      ellipsis: (h, { row }) => (
        <div>
          {row.tracking_link}
        </div>
      ),
      width: 200,
      cell: (_h: any, { row }: any) => (row.tracking_link ? (
        <a
          href={row.tracking_link}
          style="color: #5086f3"
        >
          {row.tracking_link}
        </a>
      ) : (
        '-'
      )),
    },
    // {
    //   align: 'left',
    //   colKey: 'day',
    //   title: 'Day',
    //   width: 250,
    //   fixed: 'left',
    //   cell: (h: any, { row }: any) => {
    //     const days = continuousTime(row.day ?? []);
    //     return (
    //       <div class="flex flex-wrap">
    //         {days.slice(0, 3).map((item: string, index: number) => (
    //           <div key={index}>
    //             <Tag
    //               class="m-1 inline-block max-w-[145px]"
    //               variant="outline"
    //             >
    //               <Tooltip
    //                 placement="bottom"
    //                 v-slots={{
    //                   content: () => <div class="max-h-80 overflow-y-auto">{item}</div>,
    //                 }}
    //               >
    //                 <div class="whitespace-nowrap text-ellipsis overflow-hidden">{item}</div>
    //               </Tooltip>
    //             </Tag>
    //           </div>
    //         ))}
    //         {days.length > 5 ? <div className={'leading-7'}>......</div> : <span></span>}
    //       </div>
    //     );
    //   },
    // },
    // {
    //   align: 'left',
    //   colKey: 'channel',
    //   title: 'Channel',
    //   width: 250,
    //   fixed: 'left',
    //   cell: (h: any, { row }: any) => {
    //     const channels = row.channel ?? [];

    //     return (
    //       <div class="flex flex-wrap">
    //         {channels.slice(0, 3).map((item: string, index: number) => (
    //           <div key={index}>
    //             <Tag
    //               class="m-1 inline-block max-w-[100px]"
    //               variant="outline"
    //             >
    // <Tooltip
    //   placement="bottom"
    //   v-slots={{
    //     content: () => <div class="max-h-80 overflow-y-auto">{item}</div>,
    //   }}
    // >
    //   <div class="whitespace-nowrap text-ellipsis overflow-hidden">{item}</div>
    // </Tooltip>
    //             </Tag>
    //           </div>
    //         ))}
    //         {channels.length > 5 ? <div className={'leading-7'}>......</div> : <span></span>}
    //       </div>
    //     );
    //   },
    // },
    // {
    //   colKey: 'file_name',
    //   title: 'File Name',
    //   ellipsis: true,
    //   width: 200,
    //   cell: (h: any, { row }: any) => <>{row.file_name}</>,
    // },
    // {
    //   colKey: 'size',
    //   title: 'Size',
    //   width: 100,
    // },
    // {
    //   colKey: 'upload_user',
    //   title: 'Uploader',
    //   ellipsis: true,
    //   width: 100,
    // },
    // {
    //   colKey: 'created_time',
    //   title: 'Upload Time',
    //   width: 200,
    // },
    // {
    //   colKey: 'status',
    //   title: 'Status',
    //   width: 100,
    //   cell: (h: any, { row }: any) => (
    //     <div class="flex items-center">
    //       <span
    //         class="dot"
    //         style={{ backgroundColor: statusMessage(row)?.color }}
    //       ></span>
    //       <div class="ml-2">{row.status === 'init' ? 'initFail' : row.status}</div>
    //     </div>
    //   ),
    // },
    {
      colKey: 'channel_link',
      title: 'Channel Link',
      ellipsis: (h, { row }) => (
        <div>
          {row.channel_link}
        </div>
      ),
      width: 200,
      cell: (_h: any, { row }: any) => (row.channel_link ? (
        <a
          href={row.channel_link}
          style="color: #5086f3"
        >
          {row.channel_link}
        </a>
      ) : (
        '-'
      )),
    },
    {
      colKey: 'content',
      title: 'Content',
      width: 120,
      ellipsis: true,
      cell: (_h: any, { row }: any) => <>{row.content}</>,
    },
    {
      colKey: 'quotation',
      title: 'Quotation',
      ellipsis: true,
      width: 100,
      cell: (_h: any, { row }: any) => <>{row.quotation}</>,
    },
    {
      colKey: 'target_stream_hrs',
      title: 'Target Stream Hrs',
      width: 150,
      cell: (_h: any, { row }: any) => <>{row.target_stream_hrs}</>,
    },
    {
      colKey: 'destination_link',
      title: 'Destination Link',
      ellipsis: (h, { row }) => (
        <div>
          {row.destination_link}
        </div>
      ),
      width: 200,
      cell: (_h: any, { row }: any) => (row.destination_link ? (
        <a
          href={row.destination_link}
          style="color: #5086f3"
        >
          {row.destination_link}
        </a>
      ) : (
        '-'
      )),
    },
    {
      colKey: 'custom_tags',
      title: 'Custom Tags',
      width: 120,
      ellipsis: true,
      cell: (_h: any, { row }: any) => <>{row.custom_tags}</>,
    },
    {
      colKey: 'additional_rights',
      title: 'Additional Rights',
      width: 140,
      ellipsis: true,
      cell: (_h: any, { row }: any) => <>{row.additional_rights}</>,
    },
    {
      colKey: 'action',
      title: 'Action',
      width: 120,
      cell: (_h: any, { row }: any) => (
        <Space>
          <Dropdown
            minColumnWidth="110px"
            options={getModifiedOptions(row, currentUsername)}
            trigger="click"
            onClick={e => onClickHandler(e, row)}
          >
            <Button variant="text">
              <MoreIcon />
            </Button>
          </Dropdown>
        </Space>
      ),
    },
  ];

  if (isAdmin) {
    const newCol: ITableCols = {
      colKey: 'fix_language',
      title: 'Language',
      ellipsis: true,
      width: 100,
      cell: (_h: any, { row }: any) => (row.fix_language ? <>{row.fix_language}</> : '-'),
    };
    const idx = cols.length - 1;
    cols.splice(idx, 0, newCol);
  }

  const getModifiedOptions = (row: any, _currentUsername: string | undefined) => options.map((option) => {
    const { status: rowStatus /* , upload_user: uploadUser*/ } = row ?? {};
    if (rowStatus === 'init' && (option.value === 'view' || option.value === 'download' || option.value === 'edit')) {
      return { ...option, disabled: true };
    }
    if (rowStatus === 'deleted' && (option.value === 'remove' || option.value === 'edit')) {
      return { ...option, disabled: true };
    }
    // if (option.value === 'edit' && currentUsername !== uploadUser) {
    //   return { ...option, disabled: true };
    // }
    return option;
  });

  function onClickHandler(target: { content: string; value: string } | any, row: KolManageDataModal) {
    switch (target.value) {
      case 'view':
        view(row.id!, row);
        break;
      case 'edit':
        edit(row.id!, row);
        break;
      case 'tracking_link':
        generateTrackingLink(row);
        break;
      case 'remove':
        remove(row);
        break;
      default:
        break;
    }
  }

  // function statusMessage(row: KolManageDataModal): { color: string } {
  //   const data: Record<string | number, { color: string }> = {
  //     ['success']: { color: '#0ABF5B' },
  //     ['deleted']: { color: '#e54545' },
  //     ['pending']: { color: '#dc7633' },
  //   };
  //   return data[row.status];
  // }

  function capitalizeFirstLetter(str: string): string {
    if (!str) return str; // 如果字符串为空，直接返回
    const lowercaseStr = str.toLowerCase();
    return lowercaseStr.charAt(0).toUpperCase() + lowercaseStr.slice(1);
  }

  function transStatus(status: string, msg: string) {
    switch (status) {
      case 'Link Empty':
      case 'Pending':
      case 'In Progress':
        return (
          <t-tooltip content={msg}>
            <Tag theme="primary">{status}</Tag>
          </t-tooltip>
        );
      case 'Successful':
        return (
          <t-tooltip content={msg}>
            <Tag theme="success">{status}</Tag>
          </t-tooltip>
        );
      case 'Failed':
        return (
          <t-tooltip content={msg}>
            <Tag theme="danger">{status}</Tag>
          </t-tooltip>
        );
      default:
        return '-';
    }
  }

  // 根据 selectMetrics 过滤数据展示行
  const selectCols: ITableCols[] = [];
  selectMetrics.forEach((sf) => {
    const matchItem = cols.find(item => item.colKey === sf);
    matchItem && selectCols.push(matchItem);
  });

  return {
    cols: selectCols,
    tableData,
  };
}
