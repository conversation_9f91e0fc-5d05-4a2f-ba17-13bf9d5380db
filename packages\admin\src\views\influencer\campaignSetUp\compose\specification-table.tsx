type ExampleRulesOptions = {
  type: 'defaultType' | 'valueType' | 'compeonentType';
  compeonent?: string;
  props?: any;
  value?: any;
};

export function useSpecificationTable({
  cols,
  rules = {},
}: {
  cols: Array<Record<string, any>>;
  rules?: Record<string, ExampleRulesOptions>;
}) {
  const exampleCols = Object.keys(rules);
  if (exampleCols.length === 0) {
    return cols;
  }

  return cols.map((itemRaw) => {
    if (itemRaw.colKey === 'example') {
      return {
        ...itemRaw,
        cell(_h: any, { row }: any) {
          const { column } = row;
          if (exampleCols.includes(column)) {
            const curRule = rules[column];
            if (curRule.type === 'valueType') {
              return curRule.value;
            }
            if (curRule.type === 'compeonentType') {
              if (curRule.compeonent === 't-cascader') {
                return (
                  <t-cascader
                    style={{ width: '300px' }}
                    {...(curRule.props ?? {})}
                  />
                );
              }
              if (curRule.compeonent === 't-select') {
                return (
                  <t-cascader
                    style={{ width: '300px' }}
                    {...(curRule.props ?? {})}
                  />
                );
              }
            }
          }
          return row.example;
        },
      };
    }
    return itemRaw;
  });
}
