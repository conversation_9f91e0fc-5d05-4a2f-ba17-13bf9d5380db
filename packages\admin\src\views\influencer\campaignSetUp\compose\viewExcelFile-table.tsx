import { Tooltip, Space, Input } from 'tdesign-vue-next';
import './style.css';
import { TabsFilesUploadModal } from '../modal/kolManage';
import { InfoCircleIcon } from 'tdesign-icons-vue-next';

// const operationOptions = [
//   {
//     content: EditTableOperationEnum.ADD_UPWARDS,
//     value: EditTableOperationEnum.ADD_UPWARDS,
//   },
//   {
//     content: EditTableOperationEnum.ADD_DOWNWARDS,
//     value: EditTableOperationEnum.ADD_DOWNWARDS,
//   },
// ];

export enum CellRenderEnum {
  DEFAULT = 'default',
  GENERAL_ERROR = 'general_error', // 一般错误
}
const findErrors = (row: any, name: string) => row.errors?.find((arr: { name: string }) => arr.name === name)?.message ?? undefined;

function customCell({
  data,
  isEditType,
  changeTableData,
}: {
  data: TabsFilesUploadModal['tableData'];
  isEditType: Boolean;
  changeTableData: Function;
  tableRowChangeHandler?: Function;
}) {
  console.log(data);
  // onChange
  return {
    cell(h: any, options: any) {
      const { row, rowIndex } = options;
      // console.log(`test test isEditType`,isEditType,options, row, rowIndex);
      const colKey = (this as any)?.colKey;
      const notOperaction = colKey !== 'custom-operation';
      // if (rowIndex === 0) {
      //   console.log('test test', colKey);
      // }
      // const width = (this as any)?.width ?? 100;
      const error = findErrors(row, colKey);
      const value = isEditType ? row[colKey] : error ?? row[colKey];
      if (isEditType) {
        // let isChange = false;
        // let inputChangeValue: any = '';
        return (
          <>
            <Space
              key={Math.random() + JSON.stringify(options || {})}
              class={'w-full'}
              size="small"
            >
              {notOperaction ? (
                <div class={'w-full flex items-center'}>
                  <Input
                    // autoWidth={true}
                    autoWidth={false}
                    inputClass={'flex-1 w-50 width100'}
                    defaultValue={value}
                    placeholder="please enter"
                    onBlur={(event: any) => {
                      // console.log(`test test`, event);
                      // console.log(`test test blur`, rowIndex, colKey, event);
                      changeTableData({ rowIndex, colKey, value: event.trim() });
                    }}
                  // onChange={(event: any) => {
                  //   isChange = true;
                  //   inputChangeValue = event;
                  // }}
                  // onMouseleave={() => {
                  //   if (isChange) {
                  //     console.log(`test test`);
                  //     console.log(`test test onMouseleave`, rowIndex, colKey, inputChangeValue);
                  //     changeTableData({rowIndex, colKey, value: inputChangeValue });
                  //   }
                  // }}
                  />
                  {error ? (
                    <div className={'ml-[4px] w-[20px]'}>
                      <Tooltip content={error}>
                        {
                          <div className={'flex items-center'}>
                            <InfoCircleIcon class="text-[#de7059]" />
                          </div>
                        }
                      </Tooltip>
                    </div>
                  ) : null}
                </div>
              ) : (
                ''
                // <div>
                //   <Dropdown
                //     options={operationOptions}
                //     trigger="click"
                //     maxColumnWidth={125}
                //     onClick={(e: any) => {
                //       const { value } = e || {};
                //       console.log('test test edit operation value', value);
                //       tableRowChangeHandler({
                //         EditTableOperationType: value as EditTableOperationEnum,
                //         rowIndex,
                //       });
                //     }}
                //   >
                //     <Button
                //       size="medium"
                //       variant="text"
                //       class={'cursor-pointer hover:text-brand'}
                //     >
                //       <AddCircleIcon />
                //     </Button>
                //   </Dropdown>
                //   <Button
                //     disabled={(data || []).length <= 1}
                //     size="medium"
                //     variant="text"
                //     class={'cursor-pointer hover:text-brand'}
                //     onClick={() =>
                //       tableRowChangeHandler({
                //         EditTableOperationType: EditTableOperationEnum.DELETE,
                //         rowIndex,
                //       })
                //     }
                //   >
                //     <DeleteIcon />
                //   </Button>
                // </div>
              )}
            </Space>
          </>
        );
      }
      return (
        <>
          <Space
            class={'w-full'}
            size="small"
          >
            <div>
              <Tooltip content={value}>
                {error ? (
                  <div className={'flex items-center text-ellipsis overflow-hidden'}>
                    <div className={'relative pr-[20px] min-w-[22px] min-h-[22px] text-ellipsis overflow-hidden'}>
                      <span className={'text-ellipsis overflow-hidden'}>{row[colKey]}</span>
                      <InfoCircleIcon class="absolute right-[0px] top-[50%] mt-[-8px] w-[16px] h-[16px] text-[#de7059]" />
                    </div>
                  </div>
                ) : (
                  <div className={'w-full text-ellipsis overflow-hidden'}>{value}</div>
                )}
              </Tooltip>
            </div>
          </Space>
        </>
      );
    },
  };
}

function customColFunc({
  data,
  customColItem,
  isEditType,
  changeTableData,
}: {
  data: TabsFilesUploadModal['tableData'];
  customColItem: any;
  isEditType: Boolean;
  changeTableData: Function;
  tableRowChangeHandler?: Function;
}) {
  return {
    // ellipsis: !isEditType,
    ellipsis: false,
    // width: 100,
    // width: 'auto',
    ...(customColItem ?? {}),
    ...(customCell({
      data,
      isEditType,
      changeTableData,
    }) ?? {}),
    ...{ width: '140px' },
    // ...(isEditType ? { width: 'auto' } : {}),
  };
}

export function useExcelFileTable({
  data,
  customCols,
  isEditType,
  changeTableData,
}: // tableRowChangeHandler,
{
  data: TabsFilesUploadModal['tableData'];
  customCols: Record<string, any>[];
  isEditType: Boolean;
  changeTableData: Function;
  tableRowChangeHandler?: Function;
}) {
  const cols = customCols.map(item => customColFunc({
    data,
    customColItem: item,
    isEditType,
    changeTableData,
  }),
  );

  return {
    cols,
    tableData: data,
  };
}
