import dayjs from 'dayjs';
import { OptionsItem } from 'common/components/Cascader';
import { FORMAT_OPTS, ADDITIONAL_REGHTS_OPTS } from './const';
import DOMPurify from 'dompurify';
import { getPlatformFromLink, getVideoIdByDeliveryLink } from '../utils';

export const findFirstOrSecondDefaultValue = (list: Array<OptionsItem> = [], level = 2) => {
  if (level === 1) {
    if (list.length === 0 || !list[0].value) {
      return '';
    }
    return list[0]?.value ?? '';
  }

  if (level === 2) {
    if (list.length === 0 || !list[0].children || list[0].children.length === 0) {
      return '';
    }
    return list[0].children[0].value ?? '';
  }
};


export const KOL_FORMAT_VALUES: Array<string> = FORMAT_OPTS.map(item => item);
export const KOL_FORMAT_OPTIONS: Array<OptionsItem> = KOL_FORMAT_VALUES.map(opt => ({ label: opt, value: opt }));
export const KOL_ADDITIONAL_REGHTS_OPTS: Array<OptionsItem> = ADDITIONAL_REGHTS_OPTS.map(opt => ({ label: opt, value: opt }));

type FieldOptions = {
  data: Record<string, string>; // rowData
  name: string; // fieldName
  params?: { [key: string]: any };
};

type StoreRecord = {
  gameStore: { gameCode: string };
  store: {
    regionOptions: { label: string; value: string }[];
    contentOptions: { label: string; value: string }[];
    countryValues: string[];
    channelValues: string[];
    languageValues: string[];
    isFileValidOpitionsLoading: boolean;
  };
};

const whilteDomainList = [
  'www.douyin.com',
  'www.bilibili.com',
  'www.youtube.com',
  'www.twitch.tv',
  'douyin.com',
  'bilibili.com',
  'youtube.com',
  'twitch.tv',
  'www.tiktok.com',
  'tiktok.com',
  'www.facebook.com',
  'facebook.com',
  'x.com',
  'www.instagram.com',
  'instagram.com',
];

export class FieldValidate {
  isNotEmpty({ data, name }: FieldOptions) {
    if (data?.[name]?.trim() === '') {
      return 'Empty or blank data is not allowed.';
    }
    return true;
  }
  isCurGame({ data, name }: FieldOptions, { gameStore }: StoreRecord) {
    const value = data?.[name];
    const curGame = gameStore.gameCode;
    if (value !== curGame) {
      return `The game type must be consistent; please upload game type ${curGame}`;
    }
    return true;
  }
  isLegalDate({ data, name }: FieldOptions) {
    // 自定义格式数组
    const dateFormats = [
      'YYYY/M/D',
      'YYYY/MM/DD',
      'YYYY-MM-DD',
      'YYYY-M-D',
    ];
    const value = data?.[name];
    for (const format of dateFormats) {
      const date = dayjs(value, format, true);
      if (date.isValid()) {
        return true;
      }
    }
    return 'Please enter UTC-0 timezone. Follow the format of YYYY-MM-DD, e.g. 2025-06-06.';
  }
  isLast30DayAgo({ data, name }: FieldOptions) {
    const value = data?.[name];
    const date = dayjs(value, 'YYYYMMDD', true);
    const last30DayAgo = dayjs().subtract(30, 'day')
      .startOf('day');

    if (date.isBefore(last30DayAgo) || date.isAfter(dayjs())) {
      return 'The date must be from 30 days ago until now.';
    }
    return true;
  }
  isOneYearAgo({ data, name }: FieldOptions) {
    const value = data?.[name];
    const date = dayjs(value, 'YYYYMMDD', true);
    const oneYearAgo = dayjs().subtract(1, 'year')
      .startOf('day');

    if (date.isBefore(oneYearAgo) || date.isAfter(dayjs())) {
      return 'The date must be within one year until now.';
    }
    return true;
  }
  isLegalCountry({ data, name }: FieldOptions, { store }: StoreRecord) {
    // !/^[a-z]{2}$/.test(value)
    const value = data?.[name];
    if (!value) {
      return 'Region is required.';
    }
    if (!store.countryValues.includes(value)) {
      // if (!store.countryValues.includes(value)) {
      // return `The country format is invalid, please enter the correct two-letter country code.`;
      return 'Please enter a region code or country code which should be uppercase Letters.';
    }
    return true;
  }

  isLegalLanguage({ data, name, params }: FieldOptions, { store }: StoreRecord) {
    // !/^[a-z]{2}$/.test(value)
    const value = data?.[name];
    const canBeEmpty = params?.canBeEmpty;
    if (typeof value === 'string' && value.length <= 0 && canBeEmpty) {
      return true;
    }
    if (!store.languageValues.includes(value)) {
      // if (!store.countryValues.includes(value)) {
      // return `The country format is invalid, please enter the correct two-letter country code.`;
      return 'The language format is invalid, please enter legal language.';
    }
    return true;
  }
  isLegalChannel({ data, name }: FieldOptions, { store }: StoreRecord) {
    // !/^[a-z]{2}$/.test(value)
    const value = data?.[name];
    if (!store.channelValues.includes(value)) {
      return 'The channel format is invalid, please provide the reference values for the \'channel\' field offered by the Specification module.';
    }
    return true;
  }
  isLegalContent({ data, name }: FieldOptions, { store }: StoreRecord) {
    const value = data?.[name];
    if (!store.contentOptions.map(item => item.value).includes(value)) {
      return 'Please select from Integration/Dedicated/Creative/Forward/Paid UA/Promotional Assets/Event Presence.';
    }
    return true;
  }
  isNotHasCommaAndDoublequotes({ data, name }: FieldOptions) {
    const value = data?.[name];
    if (value?.includes('"') || value?.includes(',')) {
      return `The ${name} cannot contain comma or double quotes.`;
    }
    return true;
  }
  isLegalCharacters({ data, name }: FieldOptions) {
    // 不能有潜在危险的字符或图案
    const value = data?.[name];
    const dangerousPatterns = /[<>]/;
    if (dangerousPatterns.test(value)) {
      return `The ${name} contains potentially dangerous characters.`;
    }
    return true;
  }
  // 仅spenManager做限制
  // isLegalPlatid({ data, name }: FieldOptions) {
  //   const value = data?.[name];
  //   if (!SPEND_PLATID_VALUES.includes(value)) {
  //     return `The platid is invalid, Please enter the correct platid only with '${SPEND_PLATID_VALUES.join(', ')}'.`;
  //   }
  //   return true;
  // }
  isNumberOrDecimal({ data, name }: FieldOptions) {
    const value = data?.[name];
    const amount = value?.trim().replace('$', '');
    if (amount === '' || isNaN(Number(amount)) || parseFloat(amount) < 0) {
      return `Some data in the "${name}" column is not a valid number or decimal.`;
    }
    return true;
  }
  // isLegalCpaMetric({ data, name }: FieldOptions) {
  //   const value = data?.[name];
  //   if (!CPA_METRIC_VALUES.includes(value)) {
  //     return `The metric is invalid, Please input either ${CPA_METRIC_VALUES.map(item => `'${item}'`).join(' or ')}.`;
  //   }
  //   return true;
  // }
  // isNotAllFieldEmpty ({ data }: FieldOptions) {
  //   const values = Object.values(data);
  //   const isEmpty = values.every(item => !item);
  //   if (isEmpty) {
  //     return 'Blank lines are not allowed. Please remove any blank lines.';
  //   }
  //   return true;
  // }
  strLen({ data, name, params }: FieldOptions) {
    const min = params?.min;
    const max = params?.max;
    if (Number.isNaN(min) || Number.isNaN(max) || min > max) {
      return 'strLen func params error.';
    }
    if (data?.[name]?.trim().length < min || data?.[name]?.trim().length > max) {
      switch (name) {
        case 'Campaign Name':
          return 'Campaign Name format is incorrect, please enter 100 or less text.';

        case 'Channel Link':
          return 'Channel Link format is incorrect, please enter 1000 or less text.';

        case 'Deliverable Link':
          return 'Deliverable Link format is incorrect, please enter 1000 or less text.';

        case 'Destination Link':
          return 'Destination Link format is incorrect, please enter 1000 or less text.';

        case 'Tracking Link':
          return 'Tracking Link format is incorrect, please enter 1000 or less text.';

        case 'Custom Tags':
          return 'Custom Tags format is incorrect, please enter 500 or less text.';

        default:
          return `${name} must between ${min} and ${max} characters.`;
      }
    }
    return true;
  }
  isInNumberRangeCanEmpty({ data, name, params }: FieldOptions) {
    const value: string | number = data?.[name];
    if (value) {
      if (isNaN(+value)) {
        return 'Please input a number.';
      }
      if (value === '0') {
        return true;
      }
      return this.isInNumberRange({ data, name, params });
    }
    return true;
  }
  isInNumberRange({ data, name, params }: FieldOptions) {
    let value: string | number = data?.[name];

    if (!/^-?\d+(\.\d+)?$/.test(value)) {
      return 'Please input a number.';
    }

    value = Number(value);
    const min = params?.min;
    const max = params?.max;
    const isUse = params?.isUse;
    let isCheck = isUse ?? true;

    if (typeof isUse === 'function') {
      isCheck = isUse(data);
    }

    if (!isCheck) {
      return true;
    }

    if (isNaN(min) || isNaN(max) || min > max) {
      return 'isInNumberRange func params error.';
    }
    if (value < 0) {
      return 'Please input a number greater than 0';
    }
    if (value < min || value > max) {
      // return `${name} is out of range(${min}, ${max}).`;
      return `Please fill in a number between ${min} and ${max} which could be two decimal places.`;
    }
    return true;
  }
  isInOptionLists({ data, name, params }: FieldOptions) {
    const options = params?.options;
    const canBeEmpty = params?.canBeEmpty;
    const caseInsensitive = params?.caseInsensitive;
    if (!Array.isArray(options) || !options.length) {
      return 'isInOptionLists func params error.';
    }
    const value = data?.[name];
    if (typeof value === 'string' && value.length <= 0 && canBeEmpty) {
      return true;
    }
    if (options.includes(value)) {
      return true;
    }
    if (caseInsensitive) {
      for (const opt of options) {
        const regex = new RegExp(opt, 'i');
        if (regex.test(value)) {
          return true;
        }
      }
    }
    switch (name) {
      case 'Additional Rights':
        return 'Please select from Content Usage Right/Exclusivity.';

      case 'Format':
        return 'Please select from Video/Stream/Shorts.';

      default:
        return `${value} is not in the list(${options.join('.')}).`;
    }
  }
  isValidURL({ data, name, params }: FieldOptions) {
    const canBeEmpty = params?.canBeEmpty;
    const value = data?.[name];
    if (typeof value === 'string' && value.length <= 0 && canBeEmpty === true) {
      return true;
    }

    try {
      new URL(value);
    } catch (e) {
      return 'Please Input a right url';
    }
    return true;
  }
  isContainXSS({ data, name, params }: FieldOptions) {
    const value = data?.[name];
    const canBeEmpty = params?.canBeEmpty;
    if (typeof value === 'string' && !value && canBeEmpty) {
      return true;
    }
    const clean = DOMPurify.sanitize(value);
    if (value !== clean) {
      return 'Do not input XSS dangerous HTML string.';
    }
    return true;
  }
  isChannelLinkValid({ data, name }: FieldOptions) {
    const channelLink = data?.[name];
    const format = data?.Format;
    const deliveryLink = data?.['Deliverable Link'];
    const platform = getPlatformFromLink(channelLink) || getPlatformFromLink(deliveryLink);
    let channelMust = true;
    if ((/Shorts/i.test(format) || /Video/i.test(format))
      && ['bilibili', 'douyin'].includes(platform.toLowerCase())
    ) {
      channelMust = false;
    }

    if (channelMust) {
      if (!channelLink) {
        return 'Channel link can not be empty.';
      }
    }
    return true;
  }
  isDeliveryLinkValid({ data, name }: FieldOptions) {
    const deliberyLink = data?.[name];
    const format = data.Format;
    const channelLink = data?.['Channel Link'];
    const platform = getPlatformFromLink(deliberyLink) || getPlatformFromLink(channelLink);
    let deliveryMust = false;
    if ((/Shorts/i.test(format) || /Video/i.test(format))
      && ['bilibili', 'douyin'].includes(platform.toLowerCase())
    ) {
      deliveryMust = true;
    }

    if (deliveryMust) {
      if (!deliberyLink) {
        return 'Delivery link can not be empty.';
      }
    }

    // 不管是否必填，如果填了值，获取一下video_id，看看格式是否为支持的格式
    if (deliberyLink) {
      const { video_id: videoId, tips } = getVideoIdByDeliveryLink(deliberyLink);
      if (!videoId) {
        return tips;
      }
    }

    return true;
  }
  isInDomainWhiteList({ data, name, params }: FieldOptions) {
    const canBeEmpty = params?.canBeEmpty;
    const value = data?.[name];
    if (typeof value === 'string' && value.length <= 0 && canBeEmpty === true) {
      return true;
    }

    let hostname = '';
    try {
      const urlInfo = new URL(value);
      hostname = urlInfo.hostname.toLowerCase();
    } catch (e) {
      return 'Please input a right url.';
    }
    if (!whilteDomainList.includes(hostname)) {
      return 'Please input a right url link, and must be in support formats list.';
    }
    return true;
  }
  isTargetHrsValid({ data, name, params }: FieldOptions) {
    const value: string | number = data?.[name];
    const hrs = +value;
    if (isNaN(hrs)) {
      return 'Please input a number.';
    }
    if (hrs < 0) {
      return 'Please input a number greater than 0';
    }

    // Stream时必须校验范围
    if (data.Format === 'Stream') {
      const min = params?.min;
      const max = params?.max;
      if (isNaN(min) || isNaN(max) || min > max) {
        return 'isInNumberRange func params error.';
      }
      if (hrs < 0) {
        return 'Please input a number greater than 0';
      }
      if (value < min || value > max) {
        return `${name} is out of range(${min}, ${max}).`;
      }
    }

    return true;
  }
}

export const FILE_INFO = {
  syncDataTip: 'The data uploaded today will be visible in the Pivot Dashboard tomorrow',
  syncDataTipSupplement: ', up to 30 days per day.',
};

const getfilePreviewTableColObj = {
  getfilePreviewTableCols(isEditType = false, isAdmin?: boolean) {
    const cols = (this as any)?.specificationTableData?.map((item: any) => ({
      colKey: item.column,
      title: item.column,
      width: item.width ?? 100,
    })) ?? [];

    if (isAdmin && isEditType) {
      cols.push({
        colKey: 'Language',
        title: 'Language',
        ellipsis: true,
        width: 100,
        // cell: (_h: any, { row }: any) => (row.fix_language ? <>{row.fix_language}</> : '-'),
      });
    }
    return cols;
  },
};

export const config: Record<string, any> = {
  kol: {
    specificationTableData: [
      {
        column: 'Campaign Name',
        width: 140,
        required: true,
        format: 'String(1,100)',
        example: 'PUBGM-2025Q3KOLCampaign',
      },
      { column: 'Region', width: 150, required: true, format: 'Uppercase Letters', example: 'EU' },
      {
        column: 'Channel Link',
        width: 100,
        required: true,
        format: 'String',
        example: 'https://www.youtube.com/@PUBGBOX ',
      },
      { column: 'Content', width: 150, required: true, format: 'String', example: 'Creative' },
      {
        column: 'Additional Rights',
        width: 140,
        required: false,
        format: 'String',
        example: 'Exclusivity',
      },
      {
        column: 'Format',
        width: 260,
        required: true,
        format: 'String',
        example: 'Video',
      },
      { column: 'Quotation', width: 140, required: false, format: 'Number(0, 10000000000)', example: '50000' },
      { column: 'Cost', width: 140, required: true, format: 'Number(0, 10000000000)', example: '49000' },
      { column: 'Publish Date', width: 100, required: true, format: 'Date', example: '2025-02-02' },
      { column: 'Target Stream Hrs', width: 140, required: false, format: 'Number(0, 100.00)', example: '3.5' },
      {
        column: 'Deliverable Link',
        width: 140,
        required: false,
        format: 'String(0, 1000)',
        example: 'https://www.youtube.com/@PUBGBOX',
      },
      {
        column: 'Destination Link',
        width: 140,
        required: false,
        format: 'String(0, 1000)',
        example: 'https://www.youtube.com/@PUBGBOX',
      },
      {
        column: 'Tracking Link',
        width: 140,
        required: false,
        format: 'String(0, 1000)',
        example: 'https://www.youtube.com/@PUBGBOX',
      },
      {
        column: 'Custom Tags',
        width: 140,
        required: false,
        format: 'String(0, 500)',
        example: 'High production value',
      },
    ],
    specificationTipsData: [
      {
        title: 'Region',
        // desc: 'Use the two-letter code from the internationally recognized national and regional ISO 3166-1 code table, which must be uppercase. For detailed codes, please refer to Wikipedia.',
        desc: 'Campaign Owner_Region/Country is a required field. Users who have permissions for the region can search and manipulate the data. You can select a region code or country code, and if you select a region code, only users who have full permissions for that region have permissions.',
      },
      {
        title: 'Channel Link',
        desc: 'Required. Please fill in the whole link of the influencer\'s channel.',
      },
      {
        title: 'Content',
        desc: 'Required. Please fill in Integration, Dedicated, Creative, Forward, Paid UA, Promotional Assets, or Event Presence.',
      },
      {
        title: 'Format',
        desc: 'Required. Please fill in Video, Stream, Shorts.',
      },
      {
        title: 'Cost',
        desc: 'Required. Please fill in the the contract price.',
      },
      {
        title: 'Quotation',
        desc: 'Optional: if you wish to track the original quotation from the creator and compare with acutal price',
      },
      {
        title: 'Publish Date',
        desc: 'Required. Please fill in the date of the content is going to be published.',
      },
      {
        title: 'Target Stream Hrs',
        desc: 'Required if "Format" is "Stream". Please fill in the duration of stream.',
      },
      {
        title: 'Deliverable Link',
        desc: 'The link of content from influencer. You could add it after uploading in list page.',
      },
      {
        title: 'Destination Link',
        desc: 'Destination link is the "destination" web page that you intend the users to visit and interact with. Destination link is required to generate a tracking link.',
      },
      {
        title: 'Tracking Link',
        desc: 'Tracking and result analysis, tracking link is required. You could generate it after uploading on campaign list page.',
      },
      {
        title: 'Custom Tags',
        desc: 'Multiple tags describing the campaign can be added. Separate multiple tags by commas.',
      },
      {
        title: 'Additional Rights',
        desc: `Use this column to select any additional rights provided by the influencer, such as 
        - Content Usage Right: secured rights to edit and repurpose the asset for commercial purposes such as UA, official channel release etc. 
        - Exclusivity: secure an exclusive period that the influencer can only work with the given brand.`,
      },
    ],
    exampleRule: {
      Format: {
        type: 'compeonentType',
        compeonent: 't-select',
        props: {
          filterable: true,
          defaultValue: findFirstOrSecondDefaultValue(KOL_FORMAT_OPTIONS, 1),
          options: KOL_FORMAT_OPTIONS,
        },
      },
      'Additional Rights': {
        type: 'compeonentType',
        compeonent: 't-select',
        props: {
          filterable: true,
          defaultValue: findFirstOrSecondDefaultValue(KOL_ADDITIONAL_REGHTS_OPTS, 1),
          options: KOL_ADDITIONAL_REGHTS_OPTS,
        },
      },
    },
    uploadFileTips: {
      fileSizeTips: 'File size cannot exceed 10MB.',
      doubleCountingTip: 'If the contents of files are duplicated, the spend will be recalculated.',
      fileFormatWrong: 'The file you uploaded is not the standard template of the system, please click the button above [Download blank template], download the standard template, fill in the data and upload.',
      rowDuplicateVideo: 'The deliverable link you fill in is duplicate with the deliverable link of an existing deliveray.',
      rowDuplicateStream: 'The stream data of this channel on that date has been entered, please do not create it again.',
    },
    ...getfilePreviewTableColObj,
    validateRule: {
      'Campaign Name': {
        rules: [{ rule: 'strLen', params: { min: 1, max: 100 } }, { rule: 'isContainXSS' }],
      },
      Region: {
        rules: [{ rule: 'isLegalCountry' }],
      },
      'Channel Link': {
        // 这里先校验必填，后续会调用levelup接口再次校验
        rules: [
          { rule: 'strLen', params: { min: 0, max: 1000 } },
          { rule: 'isInDomainWhiteList', params: { canBeEmpty: true } },
          { rule: 'isChannelLinkValid' },
        ],
      },
      Content: {
        rules: [{ rule: 'isLegalContent' }],
      },
      Format: {
        rules: [{ rule: 'isInOptionLists', params: { options: FORMAT_OPTS } }],
      },
      Quotation: { rules: [{ rule: 'isInNumberRangeCanEmpty', params: { min: 0, max: 10000000000 } }] },
      Cost: { rules: [{ rule: 'isInNumberRange', params: { min: 1, max: 10000000000 } }] },
      'Publish Date': { rules: [{ rule: 'isLegalDate' }] },
      'Target Stream Hrs': { rules: [{ rule: 'isTargetHrsValid', params: { min: 1, max: 100 } }] }, // isUse: (data: { [key: string]: string }) => (data.Format === 'Stream')
      'Deliverable Link': {
        rules: [
          { rule: 'isInDomainWhiteList', params: { canBeEmpty: true } },
          { rule: 'strLen', params: { min: 0, max: 1000 } },
          { rule: 'isDeliveryLinkValid' },
        ],
      },
      'Destination Link': {
        rules: [
          { rule: 'isValidURL', params: { canBeEmpty: true } },
          { rule: 'strLen', params: { min: 0, max: 1000 } },
        ],
      },
      'Tracking Link': { rules: [{ rule: 'isValidURL', params: { canBeEmpty: true } }, { rule: 'strLen', params: { min: 0, max: 1000 } }] },
      'Custom Tags': { rules: [{ rule: 'strLen', params: { min: 0, max: 500 } }, { rule: 'isContainXSS', params: { canBeEmpty: true } }] },
      'Additional Rights': { rules: [
        { rule: 'isInOptionLists', params: { options: ADDITIONAL_REGHTS_OPTS, canBeEmpty: true, caseInsensitive: true } },
      ] },
      Language: {
        rules: [{ rule: 'isLegalLanguage', params: { canBeEmpty: true } }],
      },
    },
  },
};
