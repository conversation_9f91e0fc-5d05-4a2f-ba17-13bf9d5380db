import dayjs from 'dayjs';
import { Checkbox } from 'tdesign-vue-next';
import components from '@/views/creative/common/components/components';
import { COMPONENTS_MAP } from 'common/const/components';
import { retBtn } from 'common/utils/common';


export enum KolFormatEnum {
  Video = 'Video',
  Stream = 'Stream',
  Shorts = 'Shorts',
};
export const FORMAT_OPTS = [KolFormatEnum.Video, KolFormatEnum.Stream, KolFormatEnum.Shorts];
export const PLATFORM_OPTS = ['Youtube', 'Twitch', 'Bilibili', 'Douyin', 'TikTok', 'Facebook', 'X', 'Instagram'].sort();

export const ADDITIONAL_REGHTS_LV_OPTIONS = [
  { label: 'Content Usage Right', value: 'Content Usage Right' },
  { label: 'Exclusivity', value: 'Exclusivity' },
];
export const ADDITIONAL_REGHTS_OPTS = ADDITIONAL_REGHTS_LV_OPTIONS.map(item => item.value);

export const TASK_STATUS_OPTIONS = [
  { label: 'Pending', value: '2' },
  { label: 'In Progress', value: '3' },
  { label: 'Successful', value: '5' },
  { label: 'Failed', value: '4' },
  { label: 'Link Empty', value: '1' },
];

export const RECORD_STATUS_OK = 1;
export const RECORD_STATUS_DELETE = 2;

export const KOL_TAB_PROPS = {
  modelValue: 0,
  showNum: 1,
  list: [],
  shareParams: {},
  hideSaveBtn: true,
  hideShareBtn: true,
  hideShareView: true,
  customIconList: [],
};

export const getKolFilterList = ({ src, fieldObj }: { src: any[]; fieldObj: any }) => src.map((item) => {
  const { props = {}, ext: { key = '' } = {} } = item;
  let newProps = props;
  const list = (fieldObj as any)[key];
  switch (key) {
    case 'country':
      newProps = { ...props, list };
      newProps.options = list;
      break;
    case 'format':
    case 'taskStatus':
    case 'channel':
    case 'platform':
    case 'content':
      newProps = { ...props, list };
      newProps.button = retBtn(key, list);
      break;
    case 'searchBox':
      newProps = { ...props, list };
      break;
  }
  return { ...item, props: newProps };
});

// 下拉选项的label值
export const KOL_FILTER_LABEL = {
  country: [],
  format: [],
  searchBox: [
    {
      condition: [],
      field: 'campaign_name',
      searchType: 1,
      text: 'Campaign Name',
    },
  ],
  taskStatus: [],
  channel: [],
  platform: [],
  content: [],
};

export const KOL_MANAGE_TABLE_COLUMNS = [
  'id',
  'campaign_name',
  'country_code',
  'influencer',
  'format',
  'cost',
  'publish_date',
  'task_status',
  'deliverable_link',
  'tracking_link',
  'fix_language',
  'action',
];

export const KOL_MATRICS_COLUMNS = [
  ...KOL_MANAGE_TABLE_COLUMNS,
  'channel_link',
  'content',
  'quotation',
  'target_stream_hrs',
  'destination_link',
  'custom_tags',
  'additional_rights',
];

export const KOL_MANAGE_VIEW_TABLE_COLUMNS = [
  'campaign_name',
  'country_code',
  'channel_link',
  'content',
  'format',
  'quotation',
  'cost',
  'publish_start_date',
  'target_stream_hrs',
  'deliverable_link',
  'destination_link',
  'tracking_link',
  'custom_tags',
];

export const TABLE_COLUMNS_TRANS_DISPLAY_TITLE: { [key: string]: string } = {
  id: 'ID',
  campaign_name: 'Campaign Name',
  country_code: 'Region',
  channel_link: 'Channel Link',
  content: 'Content',
  format: 'Format',
  quotation: 'Quotation',
  cost: 'Cost',
  publish_date: 'Publish Date',
  publish_start_date: 'Publish Date',
  target_stream_hrs: 'Target Stream Hrs',
  deliverable_link: 'Deliverable Link',
  destination_link: 'Destination Link',
  tracking_link: 'Tracking Link',
  custom_tags: 'Custom Tags',
  fix_language: 'Language',
  action: 'Action',
  influencer: 'Channel',
  task_status: 'Delivery Status',
  additional_rights: 'Additional Rights',
};

// 表单默认值
export const KOL_DEFAULT_FILTER = {
  country: [],
  format: [],
  searchBox: [
    {
      condition: [],
      field: 'campaign_name',
      searchType: 1,
      text: 'Campaign Name',
    },
  ],
  taskStatus: [],
  channel: [],
  platform: [],
  content: [],
};

export function getKolFilterCondition() {
  return [
    // {
    //   name: COMPONENTS_MAP['new-cascader'],
    //   props: {
    //     options: [],
    //     isEmptyWhenSelectAll: false,
    //     title: 'Region/Country',
    //     // class: 'w-[350px] h-[36px] region-country',
    //     mode: 'level',
    //     levelList: [
    //       {
    //         label: 'Region',
    //         value: 'region',
    //       },
    //       {
    //         label: 'Country',
    //         value: 'country',
    //       },
    //     ],
    //   },
    //   ext: {
    //     key: 'region', // 对应store的front_value中的key
    //     label: 'Region',
    //     isAllowClose: true,
    //     isHide: false,
    //   },
    // },
    {
      name: COMPONENTS_MAP['new-cascader'],
      props: {
        options: [],
        isEmptyWhenSelectAll: true,
        title: 'Region/Country',
        levelList: [
          {
            label: 'Region',
            value: 'region',
          },
          {
            label: 'Country',
            value: 'country_code',
          },
        ],
        mode: 'flat',
        isUseDefaultButton: true,
        isDirectUpdateValue: true,
        isOnlyNeedSon: true,
        // isShowQuestionMarkForDirtyData: true,
        // visible: true,
        // button: (textArr: string[] | string) => {
        //   if (textArr.length === 0) {
        //     return 'All';
        //   }
        //   if (!Array.isArray(textArr)) return textArr;
        //   return textArr.length > 1 ? textArr.length : textArr.join(',');
        // },
      },
      ext: {
        key: 'country',
        label: 'Region/Country',
        isAllowClose: true,
      },
    },
    {
      name: components.CommonSelect,
      props: {
        list: [],
        isEmptyWhenSelectAll: true,
        multiple: true,
        title: 'Format',
        visible: true,
        button: (textArr: string[] | string) => {
          if (textArr.length === 0) {
            return 'All';
          }
          if (!Array.isArray(textArr)) return textArr;
          return textArr.length > 1 ? textArr.length : textArr.join(',');
        },
      },
      ext: {
        key: 'format',
        label: 'Format',
        isAllowClose: true,
      },
    },
    {
      name: components.SearchBox,
      props: {
        title: 'Search',
        placeholder: 'Search Campaign',
        isShowTypeInnter: false,
      },
      ext: {
        key: 'searchBox',
        label: 'Search',
        isAllowClose: true,
        default: [
          {
            text: 'Campaign Name',
            field: 'campaign_name',
            searchType: 1,
            condition: [],
          },
        ],
      },
    },
    {
      name: components.CommonSelect,
      props: {
        list: [],
        isEmptyWhenSelectAll: true,
        multiple: true,
        title: 'Delivery Status',
        visible: true,
        button: (textArr: string[] | string) => {
          if (textArr.length === 0) {
            return 'All';
          }
          if (!Array.isArray(textArr)) return textArr;
          return textArr.length > 1 ? textArr.length : textArr.join(',');
        },
      },
      ext: {
        key: 'taskStatus',
        label: 'Delivery Status',
        isAllowClose: true,
      },
    },
    {
      name: components.CommonSelect,
      props: {
        list: [],
        isEmptyWhenSelectAll: true,
        multiple: true,
        title: 'Channel',
        visible: true,
        button: (textArr: string[] | string) => {
          if (textArr.length === 0) {
            return 'All';
          }
          if (!Array.isArray(textArr)) return textArr;
          return textArr.length > 1 ? textArr.length : textArr.join(',');
        },
      },
      ext: {
        key: 'channel',
        label: 'Channel',
        isAllowClose: true,
        isHide: true,
      },
    },
    {
      name: components.CommonSelect,
      props: {
        list: [],
        isEmptyWhenSelectAll: true,
        multiple: true,
        title: 'Platform',
        visible: true,
        button: (textArr: string[] | string) => {
          if (textArr.length === 0) {
            return 'All';
          }
          if (!Array.isArray(textArr)) return textArr;
          return textArr.length > 1 ? textArr.length : textArr.join(',');
        },
      },
      ext: {
        key: 'platform',
        label: 'Platform',
        isAllowClose: true,
        isHide: true,
      },
    },
    {
      name: components.CommonSelect,
      props: {
        list: [],
        isEmptyWhenSelectAll: true,
        multiple: true,
        title: 'Content',
        visible: true,
        button: (textArr: string[] | string) => {
          if (textArr.length === 0) {
            return 'All';
          }
          if (!Array.isArray(textArr)) return textArr;
          return textArr.length > 1 ? textArr.length : textArr.join(',');
        },
      },
      ext: {
        key: 'content',
        label: 'Content',
        isAllowClose: true,
        isHide: true,
      },
    },
  ];
}

export const SPECIFICATION_TABLE_COLUMNS = [
  { colKey: 'column', title: 'Column', width: 130 },
  {
    colKey: 'required',
    title: 'Required',
    cell: (h: any, { row }: { row: any }) => {
      const checkbox = h(Checkbox, {
        checked: row.required,
        readonly: true,
      });
      return h('div', [checkbox]);
    },
  },
  { colKey: 'format', title: 'Format', width: 200 },
  { colKey: 'example', title: 'Example', width: 340 },
];

export const EXCEL_DUPLICATE_ERRORS = [
  'The channel data for this date was uploaded.',
  'If data was uploaded by yourself, please delete it before reupload.',
  'If data was uploaded by others, plus you do not have permission to overwrite it, please contact the administrator.',
];
export const DUPLICATE_EXCEL_COLUMNS = ['fileName', 'day', 'channel', 'uploader', 'uploadedTime'];
export const DUPLICATE_TABLE_COLUMNS = [
  {
    colKey: 'fileName',
    title: 'File Name',
    ellipsis: true,
    attrs: () => ({
      style: {
        backgroundColor: '#f6bab5',
        border: 'none',
        borderRadius: 'initial',
      },
    }),
  },
  {
    colKey: 'day',
    title: 'Day',
    width: '100',
    ellipsis: true,
    attrs: () => ({
      style: {
        backgroundColor: '#f6bab5',
        border: 'none',
        borderRadius: 'initial',
      },
    }),
  },
  {
    colKey: 'channel',
    title: 'Channel',
    ellipsis: true,
    cell: (h: any, { row }: { row: any }) => row.channel?.join(', '),
    attrs: () => ({
      style: {
        backgroundColor: '#f6bab5',
        border: 'none',
        borderRadius: 'initial',
      },
    }),
  },
  {
    colKey: 'uploader',
    title: 'Uploader',
    ellipsis: true,
    attrs: () => ({
      style: {
        backgroundColor: '#f6bab5',
        border: 'none',
        borderRadius: 'initial',
      },
    }),
  },
  {
    colKey: 'uploadedTime',
    title: 'Uploaded Time',
    ellipsis: true,
    attrs: () => ({
      style: {
        backgroundColor: '#f6bab5',
        border: 'none',
        borderRadius: 'initial',
      },
    }),
    cell: (h: any, { row }: { row: any }) => dayjs(row.uploadedTime).format('YYYYMMDD'),
  },
];
export const CUSTOM_DIALOG_RECORD = {
  upload: {
    label: 'Upload',
    value: 'upload',
  },
  edit: {
    label: 'Edit',
    value: 'edit',
  },
  view: {
    label: 'View',
    value: 'view',
  },
};

export const TEMPLATE_UPLOAD_FILE_PATH = 'https://static.aix.intlgame.cn/kolManage/template/kol_influencer_campaign_template_utf8_v3.csv';

export const TEMPLATE_UPLOAD_FILE_PATH_BY_CSV = 'https://static.aix.intlgame.cn/kolManage/template/kol_influencer_campaign_template_utf8_v5.csv';
export const TEMPLATE_UPLOAD_FILE_PATH_BY_XLSX = 'https://static.aix.intlgame.cn/kolManage/template/kol_influencer_campaign_template_utf8_v5.xlsx';


export const DOWNLOAD_OPTIONS = [
  { content: 'USE CSV', value: TEMPLATE_UPLOAD_FILE_PATH_BY_CSV },
  { content: 'USE XLSX', value: TEMPLATE_UPLOAD_FILE_PATH_BY_XLSX },
];
