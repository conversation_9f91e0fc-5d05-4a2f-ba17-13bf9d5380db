<template>
  <!-- 通用包装组件，页面会在 store 的 init 方法中做初始化
    store 是 kolMange 状态管理，跨组件调用，沟通后台接口
    form-props 是通过 comman-view 透传给 FormContainer 组件的 props
      formList: 是要动态生成 form 表单的
  -->
  <common-view
    :store="store"
    :form-props="{
      modelValue: condition.cur,
      'onUpdate:modelValue': formUpdateValue,
      formList: filterList,
      onSubmit: formSubmit,
      onReset: formReset,
    }"
    hide-right
  >
    <!-- 具名插槽，是为content主要内容区 -->
    <template #views>
      <UploadTemplate
        v-model:files="files"
        v-model:show-upload-modal="showUploadModal"
        :upload-excel-columns="uploadExcelColumns"
        :tab="tabs"
        :custom-dialog-type="customDialogType"
        :loading="customDialogLoading"
        :is-admin="isAdmin"
        :select-metrics="selectMetrics"
        :metric-list="metricList"
        @update-select-metrics="updateSelectMetrics"
        @remove-tab="removeTab"
        @disabled-confirm-button="checkExcelError"
        @upload-file="uploadFile"
        @manage-file-request="manageFileRequest"
        @change-custom-dialog-type="changeCustomDialogType"
        @change-table-data-valid="changeTableDataValid"
        @download-file="downloadFile"
        @close-dialog="() => closeDialog()"
      >
        <template #left>
          <Button
            class="ml-4"
            theme="primary"
            @click="openFormModalHandler(FormType.CREATE)"
          >
            <template #icon><add-icon /></template>
            Create Delivery
          </Button>
        </template>
        <template #right>
          <Button
            variant="outline"
            @click="showSpecificationDrawer"
          >
            <template #icon>
              <file-1-icon />
            </template>
            Specification
          </Button>
        </template>
        <template #body>
          <data-container
            ref="dataContainerRef"
            class="w-full"
            :total="table.pageInfo.total"
            :page-size="table.pageInfo.pageSize"
            :default-page="table.pageInfo.pageIndex"
            :page-size-options="[10, 20, 30, 50, 100, 200]"
            :hide-header="true"
            @on-page-change="onPageChange"
          >
            <Table
              v-if="!store.isLoading"
              v-model:display-columns="KOL_MATRICS_COLUMNS"
              :sort="tableSort"
              class="w-full h-full"
              row-key="id"
              :empty="`No Data. Click &quot;Upload&quot; to complete campaign data.`"
              :data="table.records"
              :total="table.pageInfo.total"
              table-layout="fixed"
              :columns="displayTableCols"
              :update-columns="true"
              @sort-change="sortChange"
            />
            <FullLoading
              v-else
              class="rounded-large max-h-[450px]"
            />
          </data-container>
        </template>
      </UploadTemplate>

      <Specification
        :show-drawer="showDrawer"
        :management-type="managementType"
        @show="toggleDrawer"
      />
      <!-- 删除弹窗 -->
      <Dialog
        :visible="showConfirmDeleteModal"
        header="Are you sure to delete the record?"
        theme="danger"
        :confirm-loading="deleteLoading"
        :on-confirm="() => deleteRecord(currentView?.id)"
        :on-close="() => (showConfirmDeleteModal = false)"
      >
        <div>
          {{ cfg.deleteFileTips }}
        </div>
      </Dialog>

      <!-- 预览表格弹窗 -->
      <Dialog
        v-model:visible="showExcelView"
        width="80%"
        attach="body"
        :header="customDialogType"
        :close-on-overlay-click="false"
        :destroy-on-close="true"
        :confirm-on-enter="disableConfirm"
        placement="center"
        :confirm-btn="(null as any)"
        :cancel-btn="(null as any)"
      >
        <Row class="justify-center my-9 h-full">
          <Col>
            <data-container
              v-if="(excelTable.record as []).length > 0 && !excelTable.loading"
              class="w-full"
              hide-header
              hide-footer
              :total="excelTable.record?.length"
              :data="excelTable.record"
              :loading="excelTable.loading"
            >
              <Table
                ref="preViewTableRef"
                class="w-full"
                row-key="index"
                :max-height="`60vh`"
                :columns="excelTable.columns"
              />
            </data-container>
            <FullLoading
              v-else
              class="rounded-large w-full"
            />
          </Col>
        </Row>
      </Dialog>

      <!-- 生成 tracking link -->
      <Dialog
        :visible="showTrackingLinkModal"
        header="Generate Tracking Link?"
        theme="info"
        :confirm-loading="generateTrackingLinkLoading"
        :on-confirm="
          () => {
            if (showDestinationLink) {
              confirmGenerateTrackingLink();
            } else {
              confirmGenerateTrackingLinkStep2();
            }
          }
        "
        :on-close="
          () => {
            trackingLink = '';
            targetDestinationLink = '';
            showDestinationLink = true;
            showTrackingLinkModal = false;
          }
        "
      >
        <div>
          <Row>
            <Space
              direction="vertical"
              class="mb-3"
              style="gap: 0px"
            >
              <Text
                :content="tlDialogSubTitle"
                type="subTitle"
              />
              <small class="self-center text-[grey]"> {{ tlDialogDesc }} </small>
            </Space>
          </Row>
          <Row v-if="showDestinationLink">
            <t-input
              v-model="targetDestinationLink"
              placeholder="Destination Link"
            />
          </Row>
          <Row v-else>
            <Text
              style="
                width: 412px;
                padding: 10px;
                border-radius: 10px;
                background-color: #ecf2fe;
                word-wrap: break-word;
                overflow-wrap: break-word;
              "
            >
              {{ trackingLink }}
              <span
                style="color: blue; cursor: pointer"
                @click="copyTrackingLink()"
              >
                Copy
              </span>
            </Text>
          </Row>
        </div>
      </Dialog>
      <!-- 创建或编辑表单 -->
      <FormModalDialog
        ref="formModalDialogRef"
        v-model:form-submit-btn-loading="formSubmitBtnLoading"
        :origin-edit-form-data="currentEditFormData"
        @submit="createOrEditFormSubmit"
      />
    </template>
  </common-view>
</template>

<script lang="ts" setup>
// import { File1Icon } from 'tdesign-icons-vue-next';
import { Row, Text, Space, Col, Button, Dialog, MessagePlugin, Tooltip, NotifyPlugin, TableProps } from 'tdesign-vue-next';
import { AddIcon } from 'tdesign-icons-vue-next';
import DataContainer from 'common/components/Layout/DataContainer.vue';
import CommonView from 'common/components/Layout/CommonView.vue';
import Specification from './specification.vue';
import { ref, reactive, computed, watch, Reactive } from 'vue';
import Table from 'common/components/table';
import { useKolManageTable } from './compose/kolManage-table';
import { useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { useConfigurationManagementKolStore } from '@/store/influencer/campaignSetUp/kol.store';
import {
  getKolFilterList,
  KOL_FILTER_LABEL,
  getKolFilterCondition,
  KOL_MANAGE_TABLE_COLUMNS,
  CUSTOM_DIALOG_RECORD,
  TABLE_COLUMNS_TRANS_DISPLAY_TITLE,
  KOL_MANAGE_VIEW_TABLE_COLUMNS,
  KOL_MATRICS_COLUMNS,
  TEMPLATE_UPLOAD_FILE_PATH_BY_XLSX,
} from './const/const';
import UploadTemplate from './upload.vue';
import FullLoading from 'common/components/FullLoading';
import FormModalDialog from './components/FormModalDialog.vue';
import {
  KolFormOptions,
  KolManageFileModal,
  TabsFilesUploadModal,
  CustomDialogType,
  CustomDialogEnum,
  KolManageDataModal,
} from './modal/kolManage';
import { FieldValidate, config } from './const/config';
import { cloneDeep, isEqual, pick } from 'lodash-es';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import dayjs from 'dayjs';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useAuthStageStore } from '@/store/global/auth.store';
import { ITableCols } from 'common/components/table/type';
import { useClipboard } from '@vueuse/core';
import { PromisePool } from '@supercharge/promise-pool';
import { getPlatformFromLink } from './utils';
import { FormDataType, FormType } from './components/formModalDialog.type';
import * as ExcelJS from 'exceljs';

// import { v4 as uuidv4 } from 'uuid';

enum DataSourceType {
  FILE_SOURCE_TYPE = 'file_source_type',
  FORM_SOURCE_TYPE = 'form_source_type',
}

const router = useRouter();
const managementType = router.currentRoute.value.meta.managementType as string;
const store = useConfigurationManagementKolStore();
store.setManagementType(managementType);
const gameStore = useGlobalGameStore();

const cfg = config[managementType];
let uploadExcelColumns = cfg.specificationTableData.map((item: any) => item.column);

const { table, option, isAdmin } = storeToRefs(store);

// 并发运行promise的数量
const concurrencyNum = 5;
// 所有文件中全部的行
const totalFilesRowLimit = 1000;
// 全局变量，用来保存所有文件中，不相同的行
// TODO: 名字起的不好，实际上存的是所有行，修改了逻辑，原本是存不同的唯一行的，快上线了，暂不处理，留到二期来更改
let uniqueTableRows: { [key: string]: any }[] = [];
let totalRowCount: {[key: string]: number} = {};
function clearGlobalFlag() {
  uniqueTableRows = [];
  totalRowCount = {};
}
const channelInfoCache: {
  [channel_link: string]: {
    channelId: string;
    name: string;
    picture: string;
    platform: string;
    channel_link: string;
    region: string;
    language: string;
  };
} = {};

// #region form表单组件
dayjs.extend(customParseFormat); // 防止有不存在的日期出现
const { condition } = store;

// 过滤器
const formOptions = ref<KolFormOptions>({
  fieldObj: cloneDeep(KOL_FILTER_LABEL),
  conditionList: getKolFilterCondition(),
});
const filterList = computed(() => getKolFilterList({
  src: formOptions.value.conditionList,
  fieldObj: formOptions.value.fieldObj,
}),
);

// 更新表单数据
function formUpdateValue(value: Partial<typeof condition.cur>) {
  condition.cur = {
    ...condition.cur,
    ...value,
  };
}

const dataContainerRef = ref<InstanceType<typeof DataContainer> | null>(null);
// 提交表单
async function formSubmit(formData: any) {
  const { country, format, searchBox, channel, platform, content, taskStatus } = formData;

  // 兼容全选或者不选的情况
  if (country?.length === option.value.payload.countryInputList.length || country?.length === 0) {
    table.value.filter.country = [];
    condition.cur = { ...condition.cur, country: option.value.payload.countryInputList };
  } else {
    table.value.filter.country = country;
  }
  if (format?.length === option.value.payload.formatInputList.length || format?.length === 0) {
    table.value.filter.format = [];
    condition.cur = { ...condition.cur, format: option.value.payload.formatInputList };
  } else {
    table.value.filter.format = format;
  }
  if (taskStatus?.length === option.value.payload.taskStatusInputList.length || taskStatus?.length === 0) {
    table.value.filter.taskStatus = [];
    condition.cur = { ...condition.cur, taskStatus: option.value.payload.taskStatusInputList };
  } else {
    table.value.filter.taskStatus = taskStatus;
  }
  if (channel?.length === option.value.payload.channelInputList.length || channel?.length === 0) {
    table.value.filter.channel = [];
    condition.cur = { ...condition.cur, channel: option.value.payload.channelInputList };
  } else {
    table.value.filter.channel = channel;
  }
  if (platform?.length === option.value.payload.platformInputList.length || platform?.length === 0) {
    table.value.filter.platform = [];
    condition.cur = { ...condition.cur, platform: option.value.payload.platformInputList };
  } else {
    table.value.filter.platform = platform;
  }
  if (content?.length === option.value.payload.contentInputList.length || content?.length === 0) {
    table.value.filter.content = [];
    condition.cur = { ...condition.cur, content: option.value.payload.contentInputList };
  } else {
    table.value.filter.content = content;
  }

  if (Array.isArray(searchBox)) {
    // 目前只配置了 campaign 一个文本搜索类型
    table.value.filter.campaign = [...new Set((searchBox[0]?.condition ?? []) as string[])];
  }
  try {
    table.value.pageInfo.pageIndex = 1;
    if (dataContainerRef.value) {
      dataContainerRef.value.setPage(table.value.pageInfo.pageIndex, { pageSize: table.value.pageInfo.pageSize });
    }
    await store.getFilterData();
  } catch (e) {
    MessagePlugin.warning('Request table data error!');
  }
}
// 重置表单过滤器
async function formReset() {
  table.value.pageInfo.pageIndex = 1;
  // table.value.pageInfo.pageSize = 10;
  if (dataContainerRef.value) {
    dataContainerRef.value.setPage(table.value.pageInfo.pageIndex, { pageSize: table.value.pageInfo.pageSize });
  }
  table.value.filter.country = [];
  table.value.filter.format = [];
  table.value.filter.campaign = [];
  table.value.filter.channel = [];
  table.value.filter.platform = [];
  table.value.filter.content = [];
  table.value.filter.taskStatus = [];
  await store.init();
  condition.cur = cloneDeep(condition.default);
}
// #endregion

// #region UploadTemplate组件参数
const files = ref<Array<KolManageFileModal>>([]);
const showUploadModal = ref(false);
const customDialogLoading = ref(false);
const tabs = ref<TabsFilesUploadModal[]>([]);
const customDialogType = ref<CustomDialogType>(CustomDialogEnum.UPLOAD);

// cancel按钮
const closeDialog = () => {
  // uniqueTableRows = [];
  clearGlobalFlag();
  backDataBeforEdit = undefined;
};

// 删除tab
const removeTab = ({ index }: { index: number }) => {
  const removedTab = tabs.value[index];
  if (removedTab.status === 'success') {
    const fileIndex = files.value.findIndex(file => file.name === removedTab.label);
    if (fileIndex !== -1) {
      files.value.splice(fileIndex, 1);
    }
  }
  tabs.value[index] = [] as unknown as TabsFilesUploadModal;
  tabs.value.forEach((tab, index) => {
    // eslint-disable-next-line no-param-reassign
    tab.key = index + 1;
  });
  uniqueTableRows = uniqueTableRows.filter(
    uni => !removedTab.tableData.some(row => isEqual(pick(uni, uploadExcelColumns), pick(row, uploadExcelColumns))),
  );
  uniqueTableRows = Array.from(new Set(uniqueTableRows.map(item => JSON.stringify(item)))).map(item => JSON.parse(item),
  );
  removedTab.tableData.forEach((row) => {
    const flagKey = JSON.stringify(sortObjectKeys(pick(row, uploadExcelColumns)));
    if (totalRowCount[flagKey]) {
      totalRowCount[flagKey] = totalRowCount[flagKey] - 1;
    }
  });
};

// 调整confirm按钮 disabled 或 enabled
const checkExcelError = (tab: TabsFilesUploadModal[], callback: (arg: boolean) => boolean): boolean => {
  const hasTabWithOverallErrors = tab.some(data => data.overallErrors?.length !== 0);

  const hasTabWithRowErrors = tab.some(data => data.tableData.some(row => row.errors?.length !== 0));

  const hasTabWithLoading = tab.some(data => data.status === 'loading');

  return callback(hasTabWithOverallErrors || hasTabWithRowErrors || hasTabWithLoading || tab.length === 0);
};

// 上传excel，以及保存数据库
const uploadFile = async (dialogType: CustomDialogEnum, file: string | any[], callback: any) => {
  if (file.length === 0) {
    callback(false);
    return;
  }

  try {
    let result = null;
    if (dialogType === 'upload') {
      // const uniqueTableData = await beforeUpload();

      // 所有文件中不能有重复的行
      if (uniqueTableRows.length > 1) {
        let hasError = false;
        tabs.value.forEach((targetTab, index) => {
          checkExistInUniqueTableRow(targetTab!.tableData);
          if (targetTab.tableData.some(row => row.errors?.length !== 0)) {
            hasError = true;
            tabs.value[index].status = 'error';
          }
        });
        if (hasError) {
          callback(false);
          return false;
        }
      }

      if (uniqueTableRows.length > 0) {
        result = await store.uploadExcelData({
          fileData: uniqueTableRows,
          uploadUser: store.getCurrentUsername(),
        });
      }
    } else {
      // const uniqueTableData: { [key: string]: any }[] = [];
      // uniqueTableRows = [];
      clearGlobalFlag();
      uniqueTableRows.push({ ...tabs.value[0].tableData[0], editId: currentEditData.value?.id });

      result = await store.editExcelData({ fileData: uniqueTableRows, uploadUser: store.getCurrentUsername() });
    }

    if (result) {
      showUploadModal.value = false;
      NotifyPlugin.success({
        title: `${CUSTOM_DIALOG_RECORD[dialogType].label} Successfully`,
        // content: `${FILE_INFO.syncDataTip}${FILE_INFO.syncDataTipSupplement}`,
        content: '',
      });

      // table.value.pageInfo.pageIndex = 1;
      // table.value.pageInfo.pageSize = 10;
      // store.init();
      store.getFilterData();
      tabs.value = [];
      clearGlobalFlag();
      callback(true);
    } else {
      // showUploadModal.value = false;
      MessagePlugin.error(`${CUSTOM_DIALOG_RECORD[dialogType].label} Unsuccessful`);
      callback(false);
    }
  } catch (err) {
    console.error(err);
    // tabs.value = [];
    // uniqueTableRows = [];
    // clearGlobalFlag();
    callback(false);
  }
};
// #endregion

// #region 删除记录
const showConfirmDeleteModal = ref(false);
const deleteLoading = ref(false);
const currentView = ref<KolManageDataModal>();
const authStore = useAuthStageStore();
const { currentUser } = storeToRefs(authStore);

// 删除excel文件
const deleteRecord = async (id = '') => {
  try {
    deleteLoading.value = true;
    if (id) {
      const result = await store.removeRecord({
        id,
        user: currentUser.value,
      });

      deleteLoading.value = false;
      if (result) {
        MessagePlugin.success('Delete Successfully');
        showConfirmDeleteModal.value = false;
        store.getFilterData();
      }
    }
  } catch (e) {
    deleteLoading.value = false;
    MessagePlugin.error('An error occurred while deleting the Excel file. Please try again later.');
  }
};
// #endregion

// #region 列表页
// 处理切换页面
const onPageChange = async (current: number, info: any) => {
  table.value.pageInfo.pageIndex = current;
  table.value.pageInfo.pageSize = info.pageSize;
  await store.getFilterData();
};
// #endregion

// function onPageChangePreview(current: number, pageInfo: IPageInfo) {
//   const { page } = excelTable;
//   page.pageIndex = current;
//   page.pageSize = pageInfo.pageSize;
// }

const showTrackingLinkModal = ref(false);
const showDestinationLink = ref(true);
const targetDestinationLink = ref('');
const trackingLink = ref('');
const tlDialogSubTitle = ref('');
const tlDialogDesc = ref('');
const currentTrackingLinkRow = ref<KolManageDataModal>({});
const onGenerateTrackingLink = async (row: KolManageDataModal): Promise<void> => {
  currentTrackingLinkRow.value = row;
  tlDialogSubTitle.value = 'Destination Link (required):';
  tlDialogDesc.value = 'Destination link is the "destination" web page that you intend the users to visit and interact with. Destination link is required to generate a tracking link.';
  targetDestinationLink.value = row.destination_link!;
  showTrackingLinkModal.value = true;
};
const generateTrackingLinkLoading = ref(false);
const confirmGenerateTrackingLink = async () => {
  try {
    const destinationLink = targetDestinationLink.value.trim();
    // const tracking_link = currentTrackingLinkRow.value['Tracking Link']!;
    const id = currentTrackingLinkRow.value.id!;
    // const tracking_link = currentTrackingLinkRow.value?.tracking_link!;
    if (!destinationLink || !id) {
      MessagePlugin.warning('Destination Link is required, please edit to add it first.');
      return;
    }
    const isValidURL = (urlString: string) => {
      try {
        new URL(urlString);
        return true;
      } catch (e) {
        return false;
      }
    };
    if (!isValidURL(destinationLink)) {
      MessagePlugin.warning('Please Input A Right Destination Link.');
      return;
    }
    generateTrackingLinkLoading.value = true;
    const res = await store.generateTrackingLink({
      id,
      destination_link: destinationLink,
      platform: currentTrackingLinkRow.value.platform!,
    });

    generateTrackingLinkLoading.value = false;
    if (res?.track_url) {
      tlDialogSubTitle.value = 'Below is the generated tracking link';
      tlDialogDesc.value = 'Tracking and result analysis, tracking ink is requred';
      showDestinationLink.value = false;
      trackingLink.value = res.track_url;
      MessagePlugin.success('Generate Tracking Link Successfully');
    } else {
      MessagePlugin.error('Generate Tracking Link Failed.');
    }
  } catch (e) {
    console.error(e);
    generateTrackingLinkLoading.value = false;
    MessagePlugin.error('An error occurred while generate tracking link. Please try again later.');
  }
};
const confirmGenerateTrackingLinkStep2 = async () => {
  showTrackingLinkModal.value = false;
  showDestinationLink.value = true;
  trackingLink.value = '';
  targetDestinationLink.value = '';
  store.getFilterData();
};
function copyTrackingLink() {
  const { copy } = useClipboard();
  const textToCopy: string = trackingLink.value;
  copy(textToCopy).then(() => {
    MessagePlugin.success('Copied Tracking Link');
  });
}

const excelTable = reactive({
  record: [] as KolManageDataModal[],
  displayColumns: [] as string[],
  columns: [] as ITableCols[],
  page: {
    pageSize: 10,
    pageIndex: 0,
  },
  loading: true,
});
const showExcelView = ref(false);
const disableConfirm = ref(true);
const onView = async (id: string, data: KolManageDataModal) => {
  customDialogType.value = 'view';
  try {
    currentView.value = data;
    excelTable.record = [];
    excelTable.page.pageIndex = 1;
    excelTable.page.pageSize = 10;
    excelTable.loading = true;
    showExcelView.value = true;

    const res = await store.viewKolRecord({ id });
    excelTable.page.pageIndex = 1;
    excelTable.page.pageSize = 10;
    // const columnsWithoutId = Object.keys(res[0]).filter(
    //   (key) => !['id', 'status', 'name', 'picture', 'fix_language'].includes(key),
    // );

    excelTable.displayColumns = KOL_MANAGE_VIEW_TABLE_COLUMNS;
    if (isAdmin.value === true) {
      excelTable.displayColumns.push('fix_language');
    }
    excelTable.columns = KOL_MANAGE_VIEW_TABLE_COLUMNS.map(key => ({
      colKey: key,
      title: TABLE_COLUMNS_TRANS_DISPLAY_TITLE[key],
      // width: key === 'campaign' ? 500 : 100,
      width: 'auto',
      cell: (h: any, { row }: { row: any }) => {
        const tooltip = h(
          Tooltip,
          {
            content: row[key],
          },
          [h('div', { class: 'whitespace-nowrap text-ellipsis overflow-hidden' }, row[key])],
        );
        return h('div', { class: 'text-ellipsis' }, [tooltip]);
      },
    }));
    excelTable.record = res as KolManageDataModal[];
    excelTable.loading = false;
  } catch (error) {
    console.error('request record data error', error);
    MessagePlugin.warning('Failed to preview the record data.');
  }
};
const currentEditData = ref<KolManageDataModal>();
const currentEditFormData = ref<FormDataType>();
let backDataBeforEdit: {[key: string]: any}[] | undefined;
const onEdit = async (_id: string, data: KolManageDataModal) => {
  try {
    currentEditData.value = data;
    // customDialogLoading.value = true;
    // customDialogType.value = CustomDialogEnum.EDIT;
    // showUploadModal.value = true;

    const titilesToFiled: { [key: string]: string } = {};
    Object.entries(TABLE_COLUMNS_TRANS_DISPLAY_TITLE).forEach(([key, val]) => {
      titilesToFiled[val] = key;
    });
    const titles: string[] = [...uploadExcelColumns];
    if (isAdmin.value === true) {
      !titles.includes('Language') && titles.push('Language');
      !uploadExcelColumns.includes('Language') && uploadExcelColumns.push('Language');
    }
    // const csvDataRaw = titles.map(k => data[titilesToFiled[k] as keyof KolManageDataModal]).join(',');
    // const csvContentStr = `${titles.join(',') ? `${titles.join(',')}\n` : ''}${csvDataRaw}\n`;

    // // 2. 生成 UTF-8 BOM（可选但强烈推荐）
    // const bom = Uint8Array.from([0xEF, 0xBB, 0xBF]);
    // // 3. 将 CSV 文本转换为 UTF-8 字节流
    // const encoder = new TextEncoder();
    // const csvBuffer = encoder.encode(csvContentStr);

    // // 4. 合并 BOM 和 CSV 数据（BOM 在前）
    // const fullBuffer = new Uint8Array(bom.length + csvBuffer.length);
    // fullBuffer.set(bom, 0);
    // fullBuffer.set(csvBuffer, bom.length);

    // const modifiedBlob = new Blob([fullBuffer.buffer], { type: 'text/csv;charset=utf-8' });
    // const filesParams: any[] = [
    //   {
    //     name: data.campaign_name,
    //     raw: modifiedBlob,
    //     size: modifiedBlob.size,
    //     type: 'text/csv',
    //     editId: id,
    //     isCheckUniqueInDatabase: false,
    //   },
    // ];

    // manageFileRequest(filesParams, (result: KolManageFileModal) => {
    //   files.value = [result];
    //   customDialogLoading.value = false;
    // });

    // new write

    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const formEditData = titles.reduce((acc, key) => {
      acc[key] = data[titilesToFiled[key] as keyof KolManageDataModal] ?? '';
      return acc;
    }, {} as any);

    // 备份编辑前数据
    backDataBeforEdit = [cloneDeep(formEditData)];
    //
    currentEditFormData.value = formEditData as unknown as FormDataType;

    openFormModalHandler(FormType.EDIT);
  } catch (error) {}
};
const onRemove = async (data: KolManageDataModal) => {
  currentView.value = data;
  showConfirmDeleteModal.value = true;
};

watch([() => option.value.payload], ([option]) => {
  formOptions.value.fieldObj = cloneDeep({
    country: option.countryOptionList,
    format: option.formatOptionList,
    campaign: option.campaign,
    taskStatus: option.taskStatusOptionList,
    channel: option.channelOptionList,
    platform: option.platformOptionList,
    content: option.contentOptionList,
  }) as any;
  condition.cur = {
    ...condition.cur,
    country: option.countryInputList,
    format: option.formatInputList,
    campaign: option.campaign,
    taskStatus: option.taskStatusInputList,
    channel: option.channelInputList,
    platform: option.platformInputList,
    content: option.contentInputList,
  };
});

// #region specification 弹窗
const showDrawer = ref(false);
const toggleDrawer = () => {
  showDrawer.value = !showDrawer.value;
};
function showSpecificationDrawer() {
  showDrawer.value = true;
}
// #endregion

// 上传组件弹窗
// 修改自定义弹窗类型（上传（uplaod）或者 编辑（edit））
const changeCustomDialogType = (val: CustomDialogType) => {
  customDialogType.value = val;
};

watch(
  () => showUploadModal.value,
  () => {
    files.value = [];
    tabs.value = [];
  },
);

// 上传前确认excel格式，规格，模范
const manageFileRequest = async (files: any[], callback: any): Promise<any> => {
  // Utility function to process a single file
  const processFile = (file: { name: string; status: string; raw: Blob; size: string; type: string; }): Promise<any> => new Promise(async (resolve) => {
    const reader = new FileReader();
    reader.onload = async (e) => {
      // - - - - - - - - - - - Max Number - - - - - - - - - - - - -
      if (tabs.value.length > 4) return resolve({
        error: 'Cannot upload more than 5 files.',
        status: 'fail',
      });

      // - - - - - - - - - - - Max Size - - - - - - - - - - - - -
      if (parseInt(file.size, 10) > 10 * 1024 * 1024) return resolve({
        error: cfg.uploadFileTips.fileSizeTips,
        status: 'fail',
      });

      // - - - - - - - - - - - File to tabs - - - - - - - - - - - - -
      const buffer = e.target?.result as ArrayBuffer;
      // --------------------- 文件格式转换 -----------------------

      const isCsvFileType = file.name.endsWith('.csv') || file.type === 'text/csv';
      let csvContent = '';

      if (isCsvFileType) {
        try {
          csvContent = await detectAndDecode(buffer);
        } catch (error) {
          return resolve({
            error: cfg.uploadFileTips.fileFormatWrong,
            status: 'fail',
          });
        }
      }

      // Parse CSV content
      if (customDialogType.value === CustomDialogEnum.UPLOAD && uploadExcelColumns.includes('Language')) {
        uploadExcelColumns = uploadExcelColumns.filter((f: string) => f !== 'Language');
      }
      let csvData: string[][] = [];
      if (isCsvFileType) {
        csvData = parseCSV(csvContent);
      } else {
        try {
          csvData = await parseXlsxFileToRowList({
            arrayBuffer: buffer,
            fiexdHeaderList: uploadExcelColumns,
            headerReplaceRegexList: [
              /^\*+/g,   // 去掉前面的 *
              /\(\$+\)$/g, // 去掉后面的 ($)
            ],
            headerMap: { // 列名替换
              'Campaign Owner_Region/Country': 'Region',
            },
            fileType: file.type,
          });
        } catch (error) {
          console.error('xlsx parse error', error);
          return resolve({
            error: (error as Error)?.message ?? 'xlsx parse error',
            status: 'fail',
          });
        }
      };

      // 将csv数据转成table格式
      const formatCsvData = formatCSV(csvData);

      // 添加文件选项栏，并将文件的表格数据内容挂载对应的文件选项栏上
      tabs.value.push({
        label: file.name,
        key: tabs.value.length + 1,
        removable: true,
        contentDate: '',
        overallErrors: [],
        file: file.raw,
        tableData: formatCsvData,
        tableDataRaw: cloneDeep(formatCsvData), // csv文件转表格数据保留一份，内容不做任何修改
        status: 'loading',
      });
      const targetTab = tabs.value.find(tab => tab.label === file.name && tab.status === 'loading');

      // - - - - - - - - - - - Check file content is it empty - - - - - - - - - - - - -
      if (csvData[0]?.length === 0 || !csvData[1]) {
        targetTab!.overallErrors.push(cfg.uploadFileTips.fileFormatWrong);
        targetTab!.status = 'error';
        return resolve({
          status: 'fail',
        });
      }

      // - - - - - - - - - - - 所有行的列数是不是等于固定列数 - - - - - - - - - - - - -
      const isAllRowColLenEqualUploadExcelColLen = csvData.every(item => item.length ===  uploadExcelColumns.length);
      if (!isAllRowColLenEqualUploadExcelColLen) {
        targetTab!.overallErrors.push(cfg.uploadFileTips.fileFormatWrong);
        targetTab!.status = 'error';
        return resolve({
          status: 'fail',
        });
      }

      // 校验是否有空行
      let notHaveEmptyLine = true;
      formatCsvData.forEach((line) => {
        if (!notHaveEmptyLine) return;
        if (Object.values(line).find(item => !!item) === undefined) {
          notHaveEmptyLine = false;
        }
      });
      if (!notHaveEmptyLine) {
        targetTab!.overallErrors.push('Empty line in file. Please remove empty line first');
        targetTab!.status = 'error';
        return resolve({
          status: 'fail',
        });
      }

      // - - - - - - - - - - - 文件每一行的列数是否一致 - - - - - - - - - - - - -
      const everyRowColNum = csvData.map(item => item.length);
      const isEveryRowColNumSame = [...new Set(everyRowColNum)];
      if (isEveryRowColNumSame.length !== 1) {
        targetTab!.overallErrors.push(cfg.uploadFileTips.fileFormatWrong);
        targetTab!.status = 'error';
        return resolve({
          status: 'fail',
        });
      }

      // - - - - - - - - - - - Check if content is valid UTF-8 - - - - - - - - - - - - -
      const isValid = await store.isValidUTF8(csvContent, file.type);
      if (!isValid) {
        targetTab!.overallErrors.push('The file format is invalid; please upload a UTF-8 CSV file.');
        targetTab!.status = 'error';
        return resolve({
          status: 'fail',
        });
      }

      // - - - Check if fileColumns has all required columns in the correct order - - -
      const firstLine = csvData[0];
      const fileColumns = firstLine?.map((column: string) => column.trim()) ?? [];
      let hasAllColumns = true;
      for (let i = 0; i < fileColumns.length; i++) {
        if (fileColumns[i] !== uploadExcelColumns[i]) {
          hasAllColumns = false;
        }
      }
      if (!hasAllColumns) {
        // Check if file has the required columns and in order
        targetTab!.overallErrors.push(cfg.uploadFileTips.fileFormatWrong);
        targetTab!.status = 'error';
        return resolve({
          status: 'fail',
        });
      }

      // - - - - - - - - - - - csv内容不能包含逗号或双引号 - - - - - - - - - - - - -
      if (csvData.some(data => data.some(item => item.includes('"')))) {
        // 如果内容有逗号会多出一列，内容为双引号  eg: fackbook, 被当成 'fackbook', '"'
        targetTab!.overallErrors.push('Content cannot contain double quotes.');
        targetTab!.status = 'error';
        return resolve({
          status: 'fail',
        });
      }

      // - - - - - - - - - - - 遍历文件每一行数据，对每一行的列进行校验 - - - - - - - - - - - - -
      // 校验表格字段内容
      // 1. 字段对应的校验规则
      // 2. 最多5个文件
      // 3. 去重校验
      // 4. channel link 的有效性
      validTableData(targetTab!.tableData);

      // 所有文件的总行数不能超过N
      if (uniqueTableRows.length > totalFilesRowLimit) {
        targetTab!.overallErrors.push(`All files rows limit ${totalFilesRowLimit}`);
        targetTab!.status = 'error';
        return resolve({
          status: 'fail',
        });
      }

      // 这里不涉及接口校验，基础校验失败，也不必要发请求校验了，保护后台
      if (targetTab!.overallErrors.length > 0 || targetTab!.tableData.find(data => data.errors?.length > 0)) {
        // eslint-disable-next-line no-param-reassign
        targetTab!.status = 'error';
        return resolve({
          status: 'fail',
        });
      }

      const updatedFile = await backendValid({
        dialogType: customDialogType.value,
        dataSourceType: DataSourceType.FILE_SOURCE_TYPE,
        targetTab,
        resolve,
        csvData,
        file,
      });

      return resolve(updatedFile);
    };
    reader.readAsArrayBuffer(file.raw);
  });

  const filePromises = files.map(file => processFile(file));
  const results = await Promise.all(filePromises);
  const failed = results.some(result => result.status === 'fail');
  const response = failed
    ? { status: 'fail', response: results[0], tabs }
    : { status: 'success', response: results[0], tabs };

  callback(response);
};

async function batchCheckChannelLinks(tableData: { [key: string]: any }[]) {
  const needRequestChannelInfo = (format: string, platform: string) => {
    if (/Stream/i.test(format)) {
      return true;
    }
    if (/Shorts/i.test(format) || /Video/i.test(format)) {
      // if (['youtube', 'twitch'].includes(platform.toLowerCase())) {
      //   return true;
      // }
      if (['bilibili', 'douyin'].includes(platform.toLowerCase())) {
        return false;
      }
      return true;
    }
  };
  const checkChannelParamsList: { channel_link: string; platform: string }[] = [];
  tableData.forEach((row) => {
    const format = row.Format.trim();
    const channelLink = row?.['Channel Link'].trim();
    const deliveryLink = row?.['Deliverable Link'];
    const platform = getPlatformFromLink(channelLink) || getPlatformFromLink(deliveryLink);
    if (!channelLink || !platform) {
      return;
    }
    if (!channelInfoCache[channelLink] && needRequestChannelInfo(format, platform)) {
      // 没有缓存才去请求数据
      checkChannelParamsList.push({ channel_link: channelLink, platform });
    }
  });
  // 最新修改[2025-03-31]: 通过 channel_link 获取主播信息，变成非强校验，即拉取数据失败也没关系，主播信息留空即可
  // let channelsAllOk = true;
  // let errMsg = '';
  if (checkChannelParamsList.length > 0) {
    const { results: checkChannelRets, errors: checkChannelErrs } = await PromisePool.for(checkChannelParamsList)
      .withConcurrency(concurrencyNum)
      .process(async params => await store.checkChannel(params));

    // if (checkChannelErrs.length > 0) {
    //   channelsAllOk = false;
    //   errMsg = 'Channel_link error. Request levelup api to get channel info failed. Please check your channel_link.';
    // }

    if (checkChannelErrs.length === 0) {
      checkChannelRets.forEach((res) => {
        const channelId = res?.channelId;
        const channelLink = res?.channel_link;
        const name = res?.name;
        const picture = res?.picture;
        const platform = res?.platform;

        // if (!channelId || !name || !picture || !channelLink || !platform) {
        //   channelsAllOk = false;
        //   errMsg = 'Channel_link error. Request levelup api to get channel info(name,picture,channelId,platform) failed.';
        //   return;
        // }

        if (channelId && name && picture && platform) {
          channelInfoCache[channelLink] = res as {
            channelId: string;
            name: string;
            picture: string;
            platform: string;
            channel_link: string;
            region: string;
            language: string;
          };
        }
      });
    }
  }
  // if (!channelsAllOk) {
  //   tableData.forEach((row) => {
  //     const channelLink = row['Channel Link']!.trim();
  //     if (!channelInfoCache[channelLink]) {
  //       row.errors.push({ name: 'Channel Link', message: errMsg });
  //     }
  //   });
  //   return false;
  // }
  return true;
}

async function batchCheckRestrictionDatabase(tableData: { [key: string]: any }[], dataSourceType: DataSourceType = DataSourceType.FILE_SOURCE_TYPE) {
  const checkUniqueParamsList: {
    channel_id: string;
    deliverable_link: string;
    format: string;
    publish_date: string;
  }[] = [];
  tableData.forEach((row) => {
    const deliverableLink = row['Deliverable Link']!.trim();
    const format = row.Format!.trim();
    let publishDate = row['Publish Date']!.trim();
    publishDate = dayjs(publishDate).format('YYYY-MM-DD');

    const matchRow = uniqueTableRows.find(uni => isEqual(pick(uni, uploadExcelColumns), pick(row, uploadExcelColumns)))!;
    // const matchRow = uniqueTableRows.find(uni => uni['Channel Link'] === row['Channel Link'])!;
    checkUniqueParamsList.push({
      channel_id: matchRow.channel_id || '',
      deliverable_link: deliverableLink,
      format,
      publish_date: publishDate,
    });
  });
  // 批量请求，一次性校验是否有重复
  // let errMsg = '';
  const checkUniqueRes = await store.checkUnique(checkUniqueParamsList);
  if (Array.isArray(checkUniqueRes) && checkUniqueRes.length > 0) {
    checkUniqueRes.forEach((row) => {
      // 和数据库唯一性约束有重复了
      tableData.forEach((tableRow) => {
        if (/Stream/i.test(tableRow.Format)) {
          if (tableRow['Channel Link'] === row.channel_link && tableRow['Deliverable Link'] === row.deliverable_link
            && dayjs(tableRow['Publish Date']).valueOf() === dayjs(row.publish_start_date).valueOf()
          ) {
            if (dataSourceType === DataSourceType.FILE_SOURCE_TYPE) {
              // eslint-disable-next-line no-param-reassign
              tableRow.hasRowError = true;
              tableRow.errors.push({ name: 'Channel Link', message: 'The stream data of this channel on that date has been entered, please do not create it again.' });
            } else {
              const errMsg = 'The stream data of this channel on that date has been entered, please do not create it again.';
              MessagePlugin.warning(errMsg);
              throw new Error(errMsg);
            }
          }
        } else {
          if (tableRow['Channel Link'] === row.channel_link && tableRow['Deliverable Link'] === row.deliverable_link) {
            if (dataSourceType === DataSourceType.FILE_SOURCE_TYPE) {
              // eslint-disable-next-line no-param-reassign
              tableRow.hasRowError = true;
              tableRow.errors.push({ name: 'Deliverable Link', message: 'The deliverable link you fill in is duplicate with the deliverable link of an existing deliveray.' });
            } else {
              const errMsg = 'The deliverable link you fill in is duplicate with the deliverable link of an existing deliveray.';
              MessagePlugin.warning(errMsg);
              throw new Error(errMsg);
            }
          }
        }
      });
      // errMsg += `(ID: ${row.id} channel_id: ${row.channel_id} deliverable_id: ${row.deliverable_id} deliverable_link_seq: ${row.deliverable_link_seq}) is repeat.\n`;
    });

    return false;
  }
  return true;
}

// 将对象按照字段属性排序返回
function sortObjectKeys(obj: { [key: string]: any }) {
  const sortedKeys = Object.keys(obj).sort();
  const sortedObj: { [key: string]: any } = {};
  for (const key of sortedKeys) {
    sortedObj[key] = obj[key];
  }
  return sortedObj;
}

// 字段排序后插入到全局的uniqueTableRow
function sortedAndAddToUniqueTableRow(formatCsvData: { [key: string]: any }[]) {
  formatCsvData.forEach(({ key, errors, ...rest }) => {
    const row = sortObjectKeys(Object.assign({}, pick(rest, uploadExcelColumns)));
    uniqueTableRows.push(row);
    const flagKey = JSON.stringify(row);
    if (totalRowCount[flagKey]) {
      totalRowCount[flagKey] = totalRowCount[flagKey] + 1;
    } else {
      totalRowCount[flagKey] = 1;
    }
  });
}

// 校验是否存在相同的行
function checkExistInUniqueTableRow(tableRows: { [key: string]: any }[]) {
  if (uniqueTableRows.length <= 0) return false;
  // const hasSameRow = (tableRow: { [key: string]: any }) => {
  //   // 都是存的一维对象，而且根据字段顺序排序，所以可以通过json字符串来比较相等
  //   const { key, errors, ...rest } = tableRow;
  //   const uniqueJsonArr = Array.from(uniqueTableRows.map(item => JSON.stringify(item)));
  //   if (uniqueJsonArr.includes(JSON.stringify(sortObjectKeys(rest)))) {
  //     return true;
  //   }
  //   return false;
  // };
  // const hasSameRow = (tableRow: { [key: string]: any }) => uniqueTableRows.some(uni => isEqual(pick(uni, uploadExcelColumns), pick(tableRow, uploadExcelColumns)));
  tableRows.forEach((tableRow) => {
    const flagKey = JSON.stringify(sortObjectKeys(Object.assign({}, pick(tableRow, uploadExcelColumns))));
    if (totalRowCount[flagKey] && totalRowCount[flagKey] > 1) {
    // if (hasSameRow(tableRow)) {
      // Object.keys(tableRow).forEach((name) => {
      //   // 每一行都提示重复
      //   tableRow.errors.push({ name, message: 'This row data has repeated.' });
      // });
      // eslint-disable-next-line no-param-reassign
      tableRow.hasRowError = true;
      const format = tableRow?.Format;
      if (/Stream/i.test(format)) {
        tableRow.errors.push({ name: 'Channel Link', message: 'The stream data of this channel on that date has been entered, please do not create it again.' });
      } else {
        tableRow.errors.push({ name: 'Deliverable Link', message: 'The deliverable link you fill in is duplicate with the deliverable link of an existing deliveray.' });
      }
    }
  });
}

// 确保没有空格影响excel格式
function parseCSV(csvContent: string): string[][] {
  if (!csvContent) return [];
  const data: string[][] = csvContent
    .split('\n')
    ?.map((line) => {
      const lineStr = line.replace(/\t/g, '').trim(); // 1.去掉制表符\t 2.去掉前后空格
      if (lineStr) {
        const colValueList = lineStr.split(',').map(colValue => colValue.replace(/^"|"$/g, '').trim());
        return colValueList;
      }
      return undefined;
    })
    ?.filter(item => item) as string[][];

  return data;
}

// 把文件第一条数据转化成header
function formatCSV(rows: string[][]) {
  const data = rows.slice(1).map((row: string[]) => {
    const obj: { [key: string]: string } = {};
    uploadExcelColumns.forEach((header: string, index: number) => {
      obj[header] = row?.[index]?.trim();
    });
    return obj;
  });
  return data;
}

const fieldValidate = new FieldValidate();
/**
 * 验证表格数据
 * 对表格的每一行，每一列进行验证
 */
const validTableData = (tableData: any[]) => {
  interface ValidationParams {
    allowedChars?: string[];
    min?: number;
    max?: number;
    options?: string[];
  }

  interface ValidationRule {
    rule: string;
    params?: ValidationParams;
  }

  tableData.forEach((data, index) => {
    // eslint-disable-next-line no-param-reassign
    data.key = index + 1;
    // eslint-disable-next-line no-param-reassign
    data.errors = [];
    // if (!data.errors) {
    //   // eslint-disable-next-line no-param-reassign
    //   data.errors = [];
    // }

    // - - - - - - - - - - - common column start - - - - - - - - - - - - -
    Object.keys(data)
      .filter((name: string) => !name.startsWith('$'))
      .forEach((name) => {
        const ruleList = cfg.validateRule[name]?.rules ?? [];
        ruleList.forEach((ruleCfg: ValidationRule) => {
          const ruleName = ruleCfg.rule!;
          const isOk = (fieldValidate as any)?.[ruleName]?.(
            { data, name, params: ruleCfg.params },
            { gameStore, store },
          );
          if (isOk !== true) {
            data.errors.push({
              name,
              message: isOk,
            });
          }
        });
      });
  });
};

const checkTableRowErros = (tableData: { [key: string]: any }[]) => {
  const checkResult = (tableData || []).some(row => row.errors?.length !== 0);
  return checkResult;
};

/**
 *
 * @param _
 * @param colKey
 * @param hasChangeWithPreOp 当前操作列的值和上一次操作的值，是否有变化
 * @param callback
 */
const changeTableDataValid = async (_: { pageIndex: number; pageSize: number }, colKey: string, hasChangeWithPreOp?: boolean, callback?: any) => {
  // const { pageIndex = 1, pageSize = 0 } = pagination;
  if (!hasChangeWithPreOp) {
    tabs.value[0].status = 'success';
    callback?.();
    return;
  }

  if (tabs?.value?.[0]) {
    const currentTableData = tabs?.value?.[0]?.tableData ?? [];
    validTableData(currentTableData);
    if (checkTableRowErros(currentTableData)) {
      // 行有错误
      tabs.value[0].status = 'error';
      return;
    }

    // 这次操作列的值，和数据库中的该列的值比较，是否有变化
    // backDataBeforEdit 是点击编辑时备份的修改前的数据
    // currentPageData 是当前的新数据
    // 因为编辑行，这里百分百都只有一行，所以用到下标0
    const hasChangeWithDB = backDataBeforEdit?.[0] && !isEqual(pick(backDataBeforEdit[0], [colKey]), pick(currentTableData[0], [colKey]));

    if (['Channel Link'].includes(colKey) && backDataBeforEdit?.[0]) {
      try {
        if (!hasChangeWithDB) {
          // backDataBeforEdit备份的为仅有excel中的字段
          // 因为修改 channel_link 会导致补充主播信息，currentTableData 中可能会有无关信息
          // 当和数据库一致时，将备份的数据覆盖回去，以去除currentTableData中的无关信息
          // eslint-disable-next-line prefer-destructuring
          currentTableData[0] = cloneDeep(backDataBeforEdit[0]);
          tabs.value[0].status = 'success';
          callback();
          return;
        }

        tabs.value[0].status = 'loading';
        // 要重新校验链接
        await batchCheckChannelLinks(currentTableData);
        // const apiRet = await batchCheckChannelLinks(currentTableData);
        // if (!apiRet) {
        //   currentTableData[0].errors.push({ name: 'Channel Link', message: 'channel link is illegal.' });
        //   tabs.value[0].status = 'error';
        //   return ;
        // }

        // 补齐字段
        currentTableData.forEach((d) => {
          const channelLink = d['Channel Link']!;
          const matchedCache = channelInfoCache[channelLink];
          if (channelLink && matchedCache && Object.keys(matchedCache).length > 0) {
            const { name, picture, channelId, platform, region } = matchedCache;
            // eslint-disable-next-line no-param-reassign
            d.name = name;
            // eslint-disable-next-line no-param-reassign
            d.picture = picture;
            // eslint-disable-next-line no-param-reassign
            d.platform = platform;
            // eslint-disable-next-line no-param-reassign
            d.channel_id = channelId;
            // eslint-disable-next-line no-param-reassign
            d.lu_region = region;
          }
        });
        uniqueTableRows = cloneDeep(currentTableData);

        // 去后台校验联合索引的唯一性
        await batchCheckRestrictionDatabase(currentTableData);
        tabs.value[0].status = 'success';
        callback?.();
      } catch (err) {
        clearGlobalFlag();
        tabs.value[0].status = 'error';
      }
    }
  }
};

// 表单header右侧选择metric
const metricList = ref<string[]>([]);
const selectMetrics = ref<string[]>([]);
watch(() => isAdmin.value, () => {
  metricList.value = isAdmin.value === true
    ? KOL_MATRICS_COLUMNS.filter(item => !['id', 'action'].includes(item))
    : KOL_MATRICS_COLUMNS.filter(item => !['id', 'action', 'fix_language'].includes(item));
}, { immediate: true });
watch(() => isAdmin.value, () => {
  selectMetrics.value = isAdmin.value === true
    ? KOL_MANAGE_TABLE_COLUMNS
    : KOL_MANAGE_TABLE_COLUMNS.filter(item => !['fix_language'].includes(item));
}, { immediate: true });
const displayTableCols = computed(() => {
  const currentSelectMetrics = selectMetrics.value;
  const currentIsAdmin = isAdmin.value;

  const displayCols = useKolManageTable({
    data: table.value.records as unknown as KolManageDataModal[],
    selectMetrics: currentSelectMetrics,
    currentUsername: store.getCurrentUsername(),
    isAdmin: currentIsAdmin,
    generateTrackingLink: onGenerateTrackingLink,
    view: onView,
    edit: onEdit,
    remove: onRemove,
  }).cols;
  return displayCols;
});
function updateSelectMetrics(metrics: Reactive<string[]>) {
  selectMetrics.value = Array.from(new Set([...metrics, 'id', 'action']));
}
async function downloadFile(/* metricSelected: string[]*/) {
  // 纯前端下载，将表格里的内容保存成csv文件
  try {
    const titilesToFiled: { [key: string]: string } = {};
    Object.entries(TABLE_COLUMNS_TRANS_DISPLAY_TITLE).forEach(([key, val]) => {
      titilesToFiled[val] = key;
    });
    // 去掉一些字段
    // const omitFields = ['id', 'action'];
    // if (isAdmin.value !== true) {
    //   omitFields.push('fix_language');
    // }
    // const titles = metricSelected.filter((f) => !omitFields.includes(f));
    const titles = uploadExcelColumns as string[];
    const addHeaderList: string[] = [];
    if (isAdmin.value === true) {
      titles.push('Language');
      addHeaderList.push('Language');
    }

    // if (titles.length <= 0) {
    //   MessagePlugin.warning('Select Field First.');
    //   return;
    // }

    // const headerLine = titles.join(',');
    // const rowStr = table.value.records
    //   .map(row => titles
    //     .map((f) => {
    //       const field = titilesToFiled[f];
    //       if (field === 'influencer') {
    //         // 当为influencer换成name
    //         return row.name;
    //       }
    //       return row[field];
    //     })
    //     .join(','),
    //   )
    //   .join('\n');
    // const csvContentStr = `${headerLine}\n${rowStr}`;
    // // 添加 UTF-8 BOM 避免excel打开识别编码错误
    // const bom = '\uFEFF';
    // const fileContent = bom + csvContentStr;
    // const fileName = `Download_KOL_${new Date().getTime()}`;

    // const blob = new Blob([fileContent], { type: 'text/csv;charset=utf-8' });


    const fileName = `Download_KOL_${new Date().getTime()}`;
    const rowList = table.value.records.map((row) => {
      const rowItem: string[] = [];
      titles.forEach(((title) => {
        const field = titilesToFiled[title];
        if (field === 'influencer') {
          // 当为influencer换成name
          rowItem.push(row.name ?? '');
        } else {
          rowItem.push(row[field] ?? '');
        }
      }));

      return rowItem;
    });
    const blob = await generateExcelFromTemplate({
      templateUrl: TEMPLATE_UPLOAD_FILE_PATH_BY_XLSX,
      tableData: rowList,
      fiexdHeaderList: titles,
      addHeaderList,
    });


    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Download failed:', error);
    MessagePlugin.warning('Download failed!');
  }
}

/**
 * 强制指定编码解码（绕过自动检测）
 * @param buffer - 文件的二进制数据
 * @param encoding - 目标编码（如 'gbk', 'utf8', 'iso-8859-1'）
 * @returns {string} 解码后的文本
 */
function decodeWithForcedEncoding(buffer: ArrayBuffer, encoding: string): string {
  const decoder = new TextDecoder(encoding, { fatal: true });
  return decoder.decode(buffer);
}

// 解码时标记不可见字符：
//  使用 TextDecoder 的 { fatal: true } 选项，将无法识别的字符替换为 U+FFFD
// 检测是否包含 UTF-8 替代字符（U+FFFD）
function hasInvalidChars(str: string): boolean {
  return /[\uFFFD]/.test(str);
}

function detectAndDecode(buffer: ArrayBuffer): string {
  const encodings = [
    { name: 'utf-8' }, // 优先尝试 UTF-8 暂时只兼容utf-8
    // { name: 'gbk' },
    // { name: 'windows-1252' },
    // { name: 'iso-8859-1' },
    // { name: 'iso-8859-15' },
    // { name: 'us-ascii' } // 最后尝试 ASCII
  ];

  for (const { name } of encodings) {
    try {
      const result = decodeWithForcedEncoding(buffer, name);
      // const cleanedResult = removeBOM(result); // 去除 BOM

      if (!hasInvalidChars(result)) {
        return result;
      }
      console.warn(`Decoded result with ${name} is not valid: ${result}`);
    } catch (e) {
      const error = e as Error;
      console.warn(`Failed to decode with ${name}: ${error.message}`);
    }
  }

  throw new Error('无法解析文本，所有编码尝试均失败');
}

// 排序
const { tableSort } = storeToRefs(store);
const sortChange: TableProps['onSortChange'] = (val) => {
  tableSort.value = val;
  table.value.order = val;
  store.getFilterData();
};

// -------------- 创建或编辑表单 --------------
const formModalDialogRef = ref();
const formSubmitBtnLoading = ref(false);
const setFormSubmitBtnLoading = (loading: boolean) => {
  formSubmitBtnLoading.value = loading;
};
async function createOrEditFormSubmit(options: any) {
  try {
    setFormSubmitBtnLoading(true);
    clearGlobalFlag();
    tabs.value = [];
    // console.log('test test submit:', options);
    const { formData, formType } = options;
    // eslint-disable-next-line no-param-reassign
    options.formData.errors = [];

    // 伪造数据
    const targetTab = {
      label: 'from.csv',
      key: tabs.value.length + 1,
      removable: true,
      contentDate: '',
      overallErrors: [],
      file: {
        name: 'from.csv',
        status: 'success',
        raw: new Blob(),
        size: 0,
        type: 'text/csv',
      },
      tableData: [formData],
      tableDataRaw: cloneDeep([formData]), // csv文件转表格数据保留一份，内容不做任何修改
      status: 'loading',
    } as unknown as TabsFilesUploadModal;
    tabs.value.push(targetTab);

    if (formType === FormType.CREATE) { // 是创建表单
      // 创建
      // 添加文件选项栏，并将文件的表格数据内容挂载对应的文件选项栏上
      // { name: string; status: string; raw: Blob; size: string; type: string; }

      await backendValid({
        dialogType: CustomDialogEnum.UPLOAD,
        dataSourceType: DataSourceType.FORM_SOURCE_TYPE,
        targetTab,
        resolve: () => {
          // eslint-disable-next-line no-param-reassign
          // targetTab.status = 'success';
        },
        csvData: [],
        file: {
          name: 'from.csv',
          status: 'success',
          raw: new Blob(),
          size: '0',
          type: 'text/csv',
        },
      });
      // console.log('test test submit:1', targetTab);
      // console.log('test test submit:2:', uniqueTableRows);

      await uploadFile(CustomDialogEnum.UPLOAD, [{}], (callRes: any) => {
        console.log('test test form create callback', callRes);
      });

      // 关闭创建表单弹窗
      formModalDialogRef.value?.hide();
    } else if (formType === FormType.EDIT) {
      // 编辑
      // store.editKOL(formData);
      await backendValid({
        dialogType: CustomDialogEnum.EDIT,
        dataSourceType: DataSourceType.FORM_SOURCE_TYPE,
        targetTab,
        resolve: () => {
          // eslint-disable-next-line no-param-reassign
          // targetTab.status = 'success';
        },
        csvData: [],
        file: {
          name: 'from.csv',
          status: 'success',
          raw: new Blob(),
          size: '0',
          type: 'text/csv',
        },
      });

      await uploadFile(CustomDialogEnum.EDIT, [{}], (callRes: any) => {
        console.log('test test form edit callback', callRes);
      });

      // 关闭创建表单弹窗
      formModalDialogRef.value?.hide();
    }
  } catch (error) {
    console.error('test test Error in createOrEditFormSubmit:', error);
  } finally {
    setFormSubmitBtnLoading(false);
    clearGlobalFlag();
    tabs.value = [];
  }
}
const openFormModalHandler = (formType: FormType = FormType.CREATE) => {
  if (formType === FormType.CREATE) {
    currentEditFormData.value = undefined;
    formModalDialogRef.value?.show();
  } else if (formType === FormType.EDIT) {
    formModalDialogRef.value?.show();
  }
};
/**
 * 此为之前同步调用各个接口补齐字段的方法
 * 现该换方案为，在egg的后台服务，异步调用方法
 * 先注释以保留代码记录
 *
async function beforeUpload() {
  // 此时文本的读取，基础字段的校验已经完成
  // tableData中，仅有excel中的字段
  // 1. 根据excel中的channel_link去查询levelup，需要补齐：name,picture,platform,channel_id
  // 2. 去重校验：video/shots且deliverable_link不为空
  //      根据format,deliverable_link，补齐deliverable_id,seq
  //      根据deliverable_id,channel_id,sql，查询去重

  // 将多个文件的tableData收归到一起
  let uploadFileData: { [key: string]: any }[] = [];
  uploadFileData = tabs.value.reduce((accu, tab) => {
    const { tableData } = tab;
    tableData.forEach((row) => {
      const { errors, key, ...rest } = row as unknown as {
        errors: any;
        key: any;
        levelupInfo: { [key: string]: any };
        [key: string]: any;
      };
      accu.push(Object.assign({}, rest));
    });
    return accu;
  }, uploadFileData);

  // 1. 第一次去重：排除多个文件中有相同的行
  const sortObjectKeys = (obj: { [key: string]: any }) => {
    const sortedKeys = Object.keys(obj).sort();
    const sortedObj: { [key: string]: any } = {};
    for (const key of sortedKeys) {
      sortedObj[key] = obj[key];
    }
    return sortedObj;
  };
  // 字段排序，以避免 JSON.stringify 受字段顺序影响而不同
  const sortedFileData = uploadFileData.map(sortObjectKeys);
  // 使用 Set 和 JSON.stringify 去重
  const uniqueTableData = Array.from(new Set(sortedFileData.map((item) => JSON.stringify(item)))).map((item) =>
    JSON.parse(item),
  );

  // 2. 校验 channel_link
  const checkChannelParamsList: { channel_link: string; platform: string }[] = [];
  uniqueTableData.forEach((row, index) => {
    const channel_link = row['Channel Link']!.trim();
    const platform = getPlatformFromChannelLink(channel_link);
    uniqueTableData[index].platform = platform;

    checkChannelParamsList.push({ channel_link, platform });
  });
  if (checkChannelParamsList.length > 0) {
    const { results: checkChannelRets, errors: checkChannelErrs } = await PromisePool.for(checkChannelParamsList)
      .withConcurrency(10)
      .process(async (params) => await store.checkChannel(params));
    if (checkChannelErrs.length > 0) {
      MessagePlugin.error('Failed to call check channel api.');
      return;
    }
    let channelsAllOk = true;
    checkChannelRets.forEach((res) => {
      if (!channelsAllOk) return;
      const { name, picture, channelId, platform, channel_link } = res;
      console.log('check channel_link api: ', res);

      // 如果请求不成功，阻塞上传
      if (!channelId) {
        channelsAllOk = false;
        return;
      }

      uniqueTableData.forEach((d, idx) => {
        if (d['Channel Link']!.trim() === channel_link) {
          uniqueTableData[idx].name = name;
          uniqueTableData[idx].picture = picture;
          uniqueTableData[idx].platform = platform;
          uniqueTableData[idx].channel_id = channelId;
        }
      });
    });
    if (!channelsAllOk) {
      MessagePlugin.error('Failed to call check channel api.');
      return;
    }
  }

  // 3. 第二次去重：去后台查询
  const getDeliverabeIdByChannel = (channelLink: string) => {
    // 'https://www.youtube.com/@PUBGBOX'
    // const regex = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/@|twitch\.tv\/)([^/?]+)/i;
    const regex = /(?:youtube\.com\/(?:watch\?v=|shorts\/|)([^&?]+)|twitch\.tv\/videos\/(\d+))/i;
    const result = channelLink.match(regex);
    return result ? result[1]! : '';
  };
  const checkUniqueParamsList: { channel_id: string; deliverable_id: string; deliverable_link_seq: string }[] = [];
  uniqueTableData.forEach((row, index) => {
    const deliverable_link = row['Deliverable Link']!.trim();
    const format = row['Format']!.trim();
    if (/Shots/i.test(format) || /Video/i.test(format)) {
      if (!deliverable_link) {
        // 没有交付链接不做去重校验
        uniqueTableData[index].deliverable_id = '';
        uniqueTableData[index].deliverable_link_seq = uuidv4();
      } else {
        // 有交付链接要做去重校验
        uniqueTableData[index].deliverable_id = getDeliverabeIdByChannel(deliverable_link);
        uniqueTableData[index].deliverable_link_seq = '1';
        checkUniqueParamsList.push({
          channel_id: row.channel_id!,
          deliverable_id: uniqueTableData[index].deliverable_id,
          deliverable_link_seq: '1',
        });
      }
    } else if (/Stream/i.test(format)) {
      // stream类型要做去重校验
      let publish_date = row['Publish Date']!.trim();
      publish_date = dayjs(publish_date).format('YYYYMMDD');
      uniqueTableData[index].deliverable_id = `from=${publish_date}&to=${publish_date}`;
      uniqueTableData[index].deliverable_link_seq = '1';
      checkUniqueParamsList.push({
        channel_id: row.channel_id!,
        deliverable_id: uniqueTableData[index].deliverable_id,
        deliverable_link_seq: '1',
      });
    }
  });
  // 批量请求，一次性校验是否有重复
  const checkUniqueRes = await store.checkUnique(checkUniqueParamsList);
  if (Array.isArray(checkUniqueRes) && checkUniqueRes.length > 0) {
    let errMsg = '';
    checkUniqueRes.forEach((row) => {
      errMsg += `(ID: ${row.id} channel_id: ${row.channel_id} deliverable_id: ${row.deliverable_id} deliverable_link_seq: ${row.deliverable_link_seq}) is repeat.\n`;
    });
    MessagePlugin.error(errMsg);
    return;
  }

  // 4. 补充language字段
  type getLanguageParams = {
    temp_id: number;
    channel_link: string;
    content: string;
    format: string;
    platform: string;
    deliverable_link: string;
    date: string;
  };
  const getLanguageParamsList: getLanguageParams[][] = [];
  uniqueTableData.forEach((row, idx) => {
    const links: getLanguageParams[] = [];
    links.push({
      temp_id: idx, // 用来映射，不是数据库中的id，这些都是没有入库的新数据
      channel_link: row['Channel Link']!,
      content: row['Content']!,
      format: row['Format']!,
      platform: row.platform!,
      deliverable_link: row['Deliverable Link']!,
      date: row['Publish Date']!,
    });
    getLanguageParamsList.push(links);
  });
  if (getLanguageParamsList.length > 0) {
    const { results: getLanguageRets, errors: getLanguageErrs } = await PromisePool.for(getLanguageParamsList)
      .withConcurrency(10)
      .process(async (params) => await store.getLanguage(params, gameStore.gameCode));
    if (getLanguageErrs.length > 0) {
      MessagePlugin.error('Failed to call get language api.');
      return;
    }
    getLanguageRets.forEach((res) => {
      // 根据errors已经判断成功，每个批量请求其实只传了一套参数，所以直接用下标0取值
      const temp_id = res[0].id! ?? -1;
      const language = res[0].language! ?? ''; // 无值可为空
      +temp_id >= 0 && (uniqueTableData[+temp_id].fix_language = language);
    });
  }

  // 5. 默认全部生成tracking link
  // 如果有destination_link，且没有tracking_link，则默认调用接口生成tracking link;

  const getTrackingLinkParams: { id: string | number; destination_link: string; platform: string; type?: string }[] =
    [];
  uniqueTableData.forEach((row, idx) => {
    const destination_link = row['Destination Link']!.trim();
    const tracking_link = row['Tracking Link']!.trim();
    if (destination_link && !tracking_link) {
      getTrackingLinkParams.push({
        id: idx,
        destination_link,
        platform: row.platform!,
        type: 'upload',
      });
    }
  });
  if (getTrackingLinkParams.length > 0) {
    const { results: getTrackingLinkRets, errors: getTrackingLinkErrs } = await PromisePool.for(getTrackingLinkParams)
      .withConcurrency(10)
      .process(async (params) => await store.generateTrackingLink(params));
    if (getTrackingLinkErrs.length > 0) {
      MessagePlugin.error('Failed to call check generate tracking link api.');
      return;
    }
    getTrackingLinkRets.forEach((res) => {
      if (res?.track_url) {
        uniqueTableData[+res.id]['Tracking Link'] = res.track_url;
      }
    });
  }

  return uniqueTableData;
}

*/


async function parseXlsxFileToRowList({
  arrayBuffer,
  fiexdHeaderList, // 固定表头
  headerReplaceRegexList, // 表头替换正则表达式
  headerMap, // 表头替換
  fileType, // 文件类型
}: {
  arrayBuffer: ArrayBuffer,
  fiexdHeaderList: string[],
  headerReplaceRegexList: RegExp[],
  headerMap: Record<string, string>,
  fileType: string,
}) {
  // 读取文件内容
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.load(arrayBuffer, {}); // 加载 Excel 文件

  // 获取第一个工作表
  const worksheet = workbook.worksheets[0];

  // 获取表头（第二行）
  const headerRow = worksheet.getRow(2);
  const headersRaw = (headerRow?.values as any[])?.slice(1) ?? []; // 获取第二行的值，去掉第一个空值
  const headersReplace = replaceStrings(headersRaw, headerReplaceRegexList); //
  const headers = headersReplace.map((header: string) => {
    if (headerMap[header]) {
      return headerMap[header]; // 替换表头
    };
    return header; // 如果没有替换，则返回原表头
  });

  const isUtf8Headers = await store.isValidUTF8(headers.join(''), fileType);
  const uft8ErrorMessage = 'The file format is invalid; please upload a UTF-8 CSV file.';
  if (!isUtf8Headers) {
    throw new Error(uft8ErrorMessage);
  };

  if (headers.length === 0) {
    throw new Error('The header cannot be empty.');
  };
  if (headers.length !== fiexdHeaderList.length) {
    throw new Error('The number of columns in the header does not equal the fixed number of columns.');
  };
  // 检查表头是否包含固定的表头
  headers.every((header: string) => {
    const isHasFixedHeader = fiexdHeaderList.includes(header);
    // console.log(`key: ${header};  res: ${isHasFixedHeader}`);
    if (!isHasFixedHeader) {
      throw new Error(`The header does not contain the fixed header: ${header}`);
    }
    return isHasFixedHeader;
  });


  // 初始化结果数组
  const result: string[][] = [];

  // 从第四行开始读取数据
  for (let i = 4; i <= (worksheet?.lastRow?.number ?? 0); i++) {
    const dataRow = worksheet.getRow(i);
    // const rowData: Record<string, any> = {};
    const rowData: string[] = []; // 初始化行数据

    // 遍历每个单元格，构建对象
    headers.forEach((header: string, index: number) => {
      const cell = dataRow.getCell(index + 1); // 获取单元格的值
      // console.log('cell:', cell);
      // console.log(`cell: ${cell}; header: ${header}; typeof: ${ typeof cell} ; type: ${cell instanceof Date};`);
      // console.log(`cell: ${cell}; header: ${header}; typeof: ${ typeof cell} ; type: ${cell instanceof Date}; csv: ${cell.toCsvString()}`);

      let cellValue;
      // 检查单元格的类型
      if (cell?.type === ExcelJS.ValueType.Hyperlink) { // 超链接
        // 如果是链接对象，获取 hyperlink 属性
        cellValue = cell?.hyperlink; // 获取链接
      } else if (cell?.type === ExcelJS.ValueType.Date) { // 日期
        cellValue = cell?.text ? dayjs(cell?.text).format('YYYY-MM-DD') : ''; // 格式化日期
      } else if (cell?.type === ExcelJS.ValueType.RichText) { // 富文本（例如包含emoji表情）
        cellValue = cell?.text ?? ''; //
      } else if (cell?.type === ExcelJS.ValueType.Error) { // 单元格读取错误
        throw new Error(`xlsx cell read error; header: ${header}; row: ${i}; column: ${index + 1}`);
      } else {
        // 否则直接使用 cell.value
        cellValue = cell?.value ?? '';
      };

      if (header) { // 确保表头不为空
        // rowData[header] = cellValue || ''; // 如果单元格为空，设置为 ''
        rowData.push(cellValue ? (`${cellValue}`) : ''); // 如果单元格为空，设置为 ''
      }
    });

    // 只在行数据不为空时添加到结果数组
    if (Object.keys(rowData).length > 0) {
      result.push(rowData);
    };
  };

  // return Object.values(row).join('');
  const allRowContent = result.map(row => row?.join('') ?? '').join('');
  const isUtf8Content = await store.isValidUTF8(allRowContent, fileType);
  if (!isUtf8Content) {
    throw new Error(uft8ErrorMessage);
  };

  result.unshift(headers); // 将表头添加到结果数组的第一行
  // console.log(result); // 打印最终结果
  return result;


  // // 表格转字符串内容
  // let csvContent = result.map((row: { [key: string]: any }) => {
  //     return fiexdHeaderList.map((header: string) => {
  //         const cellValue = row[header];
  //         return cellValue;
  //     }).join(',');
  // }).join('\n');
  // csvContent = `${fiexdHeaderList.join(',') + '\n'}${csvContent}`;
  // return csvContent;
};
function replaceStrings(strings: string[], regexArray: RegExp[]): string[] {
  return strings.map((str) => {
    let replaceStr = str;
    regexArray.forEach((regex) => {
      replaceStr = replaceStr.replace(regex, '');
    });
    return replaceStr.trim(); // 最后去掉前后的空格
  });
};
async function generateExcelFromTemplate({
  templateUrl, // xlsx模板文件的URL
  tableData, // 需要填充到模板的数据
  fiexdHeaderList, // 固定表头
  addHeaderList, // 需要添加到表头的字段
}: { templateUrl: string, tableData: string[][], fiexdHeaderList: string[], addHeaderList: string[] }) {
  // 1. 下载模板文件
  const response = await fetch(templateUrl);
  const arrayBuffer = await response.arrayBuffer();

  // 2. 使用 exceljs 加载模板
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.load(arrayBuffer);

  // 3. 获取工作表
  const worksheet = workbook.worksheets[0]; // 假设数据填充到第一个工作表

  // 4. 在表头中添加新的字段（例如 'age'）
  const headerRow = worksheet.getRow(2); // 表头在第二行
  const beforeAddHeaderCount = fiexdHeaderList.length - addHeaderList.length; // 记录添加前的表头数量
  addHeaderList.forEach((header, index) => {
    const newHeaderCell = headerRow.getCell(beforeAddHeaderCount + index + 1); // 获取当前单元格
    newHeaderCell.value = header; // 在最后一列添加新的字段
  });
  // headerRow.getCell(headerRow.cellCount + 1).value = 'age'; // 在最后一列添加 'age'

  // // 5. 填充字段说明（假设在第三行）
  // const descriptionRow = worksheet.getRow(3); // 字段说明在第三行
  // descriptionRow.getCell(headerRow.cellCount).value = 'Age of the person'; // 为新字段添加说明

  // 6. 填充内容，从第四行开始
  tableData.forEach((row, rowIndex) => {
    row.forEach((cellValue, colIndex) => {
      worksheet.getCell(rowIndex + 4, colIndex + 1).value = cellValue; // 从第四行开始填充
    });
    // 假设每行的最后一列是 age，您可以根据需要设置 age 的值
    // worksheet.getCell(rowIndex + 4, headerRow.cellCount).value = row[3]; // 假设 row[3] 是 age 的值
  });

  // 7. 导出文件为 Blob
  const buffer = await workbook.xlsx.writeBuffer();

  // 8. 创建 Blob 对象
  const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

  // // 9. 创建返回对象
  // const fileObject = {
  //     raw: blob, // 将 Blob 对象赋值给 raw
  //     lastModified: Date.now(),
  //     name: fileName,
  //     size: blob.size,
  //     type: blob.type,
  //     // percent: 0,
  //     // status: 'progress'
  // };

  // 10. 文件对象
  // return [fileObject];

  return blob; // 返回 Blob 对象
}

//
async function backendValid({
  dialogType,
  dataSourceType,
  targetTab,
  resolve,
  csvData,
  file,
}: {
  dialogType: string;
  dataSourceType: DataSourceType; // 数据来源：'fileDataSouceType' | 'formDataSourceType'
  targetTab: TabsFilesUploadModal | undefined;
  resolve: (arg: any) => void;
  csvData: string[][];
  file: { name: string; status: string; raw: Blob; size: string; type: string; }
}) {
  if (dialogType === CustomDialogEnum.UPLOAD) {
    // 通过channel_link的拉取主播信息，拉不到主播信息也报错，上传表中存储对应字段的空值即可
    await batchCheckChannelLinks(targetTab!.tableData);
    // const apiRet = await batchCheckChannelLinks(targetTab!.tableData);
    // if (!apiRet) {
    //   targetTab!.status = 'error';
    //   return resolve({
    //     status: 'fail',
    //   });
    // }

    sortedAndAddToUniqueTableRow(targetTab!.tableData);
    // 补齐字段
    uniqueTableRows.forEach((d) => {
      const channelLink = d['Channel Link'];
      const matchedCache = channelInfoCache[channelLink];
      if (channelLink && matchedCache && Object.keys(matchedCache).length > 0) {
        const { name, picture, channelId, platform, region } = matchedCache;
        // eslint-disable-next-line no-param-reassign
        d.name = name;
        // eslint-disable-next-line no-param-reassign
        d.picture = picture;
        // eslint-disable-next-line no-param-reassign
        d.platform = platform;
        // eslint-disable-next-line no-param-reassign
        d.channel_id = channelId;
        // eslint-disable-next-line no-param-reassign
        d.lu_region = region;
      }
      // [2025-03-31]修改：拉不到数据不要阻塞保存提交
      // else {
      //   // channel_id 是关键数据，并且没有异步任务后台获取
      //   // 这里匹配不到 channel_id 禁止进行下一步
      //   targetTab!.overallErrors.push('check channel link api request error.');
      //   targetTab!.status = 'error';
      //   return resolve({
      //     status: 'fail',
      //   });
      // }
    });

    // 去后台校验联合索引的唯一性
    await batchCheckRestrictionDatabase(targetTab!.tableData, dataSourceType);
  } else {
    const isChangeChangeLink = backDataBeforEdit?.[0]?.['Channel Link'] !== targetTab!.tableData?.[0]?.['Channel Link'];
    await changeTableDataValid({ pageIndex: 0, pageSize: 0 }, 'Channel Link', isChangeChangeLink);
    sortedAndAddToUniqueTableRow(targetTab!.tableData);
  }

  if (targetTab!.overallErrors.length > 0 || targetTab!.tableData.find(data => data.errors?.length > 0)) {
    // eslint-disable-next-line no-param-reassign
    targetTab!.status = 'error';
    return resolve({
      status: 'fail',
    });
  }
  // eslint-disable-next-line no-param-reassign
  targetTab!.status = 'success';
  // 复制一份文件换去CRLF格式
  // const lines = csvContent.split(/\r?\n/);
  const lines = csvData.map(line => line.join(','));
  const titles = lines.shift(); // 移除头部
  while (lines.length > 0 && lines[lines.length - 1].trim() === '') {
    lines.pop();
  }
  const modifiedContent = `${lines.join('\r\n')}\r\n`; // 加一个空行
  const modifiedBlob = new Blob([modifiedContent], { type: 'text/csv' });

  const updatedFile = {
    ...file,
    crlfRaw: modifiedBlob,
    titles,
    status: 'success',
  };

  if (dialogType === CustomDialogEnum.EDIT && dataSourceType === DataSourceType.FORM_SOURCE_TYPE) {
    // 编辑时，保存一份数据留底
    // 用于比对修改，如果提交前实际未修改值，不做去重校验和提示
    backDataBeforEdit = cloneDeep(targetTab?.tableData);
  }

  return updatedFile;
};
</script>
