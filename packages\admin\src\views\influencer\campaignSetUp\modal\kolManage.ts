import { OptionsItem } from 'common/components/Cascader';

export type KolFormOptions = {
  fieldObj: {
    country?: OptionsItem[];
    format?: OptionsItem[];
    taskStatus?: OptionsItem[];
    campaign?: string;
    channel?: OptionsItem[];
    platform?: OptionsItem[];
    content?: OptionsItem[];
  };
  conditionList: any[];
};

export type KolFormParams = {
  // date?: string[];
  // channel?: string[];
  country?: string[];
  format?: string[];
  campaign?: string;
  channel?: string[];
  platform?: string[];
  content?: string[];
  taskStatus?: string[];
};

export type KolFileDayChannelModal = {
  id?: number;
  file_id?: number;
  game_code: string;
  day: string;
  channel: string;
};

export type KolManageFileModal = {
  lastModified: number;
  name: string;
  percent: number;
  raw: File;
  titles: string;
  gameCode: string;
  contentDate?: string;
  crlfRaw: Blob;
  response: KolManageFileModal;
  size: number;
  status: 'success' | 'fail' | 'progress' | 'waiting';
  type: string;
  uploadTime: string;
  rows?: KolFileDayChannelModal;
};

type OverallError = string;

export type TabsFilesUploadModal = {
  label: string;
  key: number;
  removable: boolean;
  file: Blob;
  overallErrors: OverallError[];
  contentDate?: string;
  status: 'success' | 'error' | 'loading';
  tableData: {
    [key: string]: any;
  }[];
  tableDataRaw: {
    [key: string]: any;
  }[];
};

export type KolManageDataUploadModal = {
  id?: string;
  status: 'success' | 'fail' | 'progress' | 'waiting';
  content_date?: string;
};

export enum CustomDialogEnum {
  UPLOAD = 'upload',
  EDIT = 'edit',
  VIEW = 'view',
}
export type CustomDialogType = `${CustomDialogEnum}`;

export type KolManageDataModal = {
  id?: string;
  campaign_name?: string;
  country_code?: string;
  influencer?: string;
  name?: string;
  picture?: string;
  channel_link?: string;
  format?: string;
  quotation?: number;
  cost?: number;
  publish_date?: string;
  target_stream_hrs?: number;
  deliverable_link?: string;
  destination_link?: string;
  tracking_link?: string;
  custom_tags?: string;
  content?: string;
  platform?: string;
  fix_language?: string;
};

export enum EditTableOperationEnum {
  ADD_UPWARDS = 'Add Upwards',
  ADD_DOWNWARDS = 'Add Downwards',
  DELETE = 'Delete',
}
