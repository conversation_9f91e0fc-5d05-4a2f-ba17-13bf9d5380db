<template>
  <Drawer
    :visible="props.showDrawer"
    header="Specification"
    size="large"
    :footer="false"
    :close-btn="true"
    :on-close="toggleShowDrawer"
  >
    <template #header>
      <Text
        content="Specification"
        type="subTitle"
      />
    </template>
    <Space
      direction="vertical"
      size="small"
    >
      <Row>
        <Text
          content="File Template Download"
          color="grey"
        />
      </Row>
      <Row>
        <DownLoadFileTemplateBtn
          :download-template-list="DOWNLOAD_OPTIONS"
        />
        <!-- <Text content="csv" />
        <Link
          class="ml-3"
          theme="primary"
          content="KOL_Campaign_Template_20250116.csv"
          @click="downloadSampleFile"
        />
        <download-icon
          class="mx-4 self-center cursor-pointer text-[#4981F2]"
          :on-click="downloadSampleFile"
        /> -->
      </Row>
      <Row>
        <Text
          content="Follow instructions to fill in the table."
          color="grey"
        />
      </Row>
    </Space>
    <Space
      class="mt-9"
      direction="vertical"
      size="small"
    >
      <Row>
        <Text
          content="Content Format"
          type="subTitle"
        />
      </Row>
      <Row>
        <Table
          v-if="!isFileValidOpitionsLoading"
          :columns="tableCol"
          :data="cfg['specificationTableData']"
        />
        <loading v-else />
      </Row>
    </Space>

    <!-- Tips -->
    <Row
      v-if="cfg.specificationTipsData?.length > 0"
      class="mt-5"
    >
      <Text
        content="Tips"
        type="subTitle"
      />
    </Row>
    <Row
      v-for="(item, index) in cfg.specificationTipsData"
      :key="index"
      class="mt-2"
    >
      <Col :span="3">
        <Text
          :content="index + 1 + '. ' + item.title"
          color="grey"
        />
      </Col>
      <Col :span="8">
        <Text :content="item.desc" class="text-with-line-breaks" />
      </Col>
    </Row>
  </Drawer>
</template>

<script setup lang="tsx">
import { Row, Col, Space, Table, Drawer } from 'tdesign-vue-next';
import loading from 'common/components/FullLoading/loading.vue';
import { storeToRefs } from 'pinia';
import { OptionsItem } from 'common/components/Cascader';
import Text from 'common/components/Text';
import { SPECIFICATION_TABLE_COLUMNS, DOWNLOAD_OPTIONS } from './const/const';
// import { DownloadIcon } from 'tdesign-icons-vue-next';
import { config } from './const/config';
import { computed } from 'vue';
import { useConfigurationManagementKolStore } from '@/store/influencer/campaignSetUp/kol.store';
import { useSpecificationTable } from './compose/specification-table';
// import { downloadTemplate } from './utils';
import DownLoadFileTemplateBtn from '../components/DownloadFileTemplateBtn.vue';

const store = useConfigurationManagementKolStore();
const { countryValues, contentOptions, isFileValidOpitionsLoading } = storeToRefs(store); // regionSecondlableCodeOptions

const props = defineProps({
  showDrawer: {
    type: Boolean,
    default: false,
  },
  managementType: {
    type: String,
    default: 'spend',
  },
});

const cfg = config[props.managementType];

const tableCol = computed(() => {
  const rules: Record<string, any> = {
    Region: {
      type: 'compeonentType',
      compeonent: 't-select',
      props: {
        loading: isFileValidOpitionsLoading.value,
        'show-all-levels': false,
        filterable: true,
        defaultValue: findFirstOrSecondDefaultValue(
          countryValues.value.map(item => ({ label: item, value: item })),
          1, // regionSecondlableCodeOptions.value as unknown as Array<OptionsItem>,
        ),
        options: countryValues.value.map(item => ({ label: item, value: item })), // regionSecondlableCodeOptions.value,
      },
    },
    Content: {
      type: 'compeonentType',
      compeonent: 't-select',
      props: {
        filterable: true,
        'show-all-levels': false,
        loading: isFileValidOpitionsLoading.value,
        defaultValue: findFirstOrSecondDefaultValue(contentOptions.value, 1),
        options: contentOptions.value,
      },
    },
    ...(cfg.exampleRule ?? {}),
  };
  return useSpecificationTable({ cols: SPECIFICATION_TABLE_COLUMNS, rules });
});

const findFirstOrSecondDefaultValue = (list: Array<OptionsItem> = [], level = 2) => {
  if (level === 1) {
    if (list.length === 0 || !list[0].value) {
      return '';
    }
    return list[0]?.value ?? '';
  }

  if (level === 2) {
    if (list.length === 0 || !list[0].children || list[0].children.length === 0) {
      return '';
    }
    return list[0].children[0].value ?? '';
  }
};

const emit = defineEmits(['show']);

const toggleShowDrawer = () => {
  emit('show');
};

// function downloadSampleFile() {
//   downloadTemplate(TEMPLATE_UPLOAD_FILE_PATH);
// }
</script>

<style lang="scss" scoped>
.text-with-line-breaks {
  white-space: pre-line;
}
</style>
