<template>
  <Row>
    <Col class="bg-white-primary w-full rounded-t-lg pt-[16px] flex gap-y-[16px] justify-between">
      <div>
        <slot name="left" />
        <Button
          class="mx-4"
          variant="outline"
          @click="clickUploadBtn"
        >
          <template #icon>
            <upload-icon />
          </template>
          Upload
        </Button>
        <slot name="right" />
      </div>

      <div class="rounded-default h-[36px] p-[8px] w-auto flex gap-x-[16px] border-[1px] mr-[16px]">
        <div class="flex items-center gap-x-[8px] cursor-pointer operate-item">
          <svg-icon
            class="normal-hover"
            name="more"
            @click="showMetricSelect"
          />
        </div>
        <div class="flex items-center gap-x-[8px] cursor-pointer operate-item">
          <svg-icon
            class="normal-hover"
            name="download"
            @click="downloadFile"
          />
        </div>
      </div>
    </Col>
    <Col class="bg-white-primary w-full rounded-b-lg p-[16px] flex gap-y-[16px] mb-6 min-h-[500px]">
      <slot name="body" />
    </Col>
  </Row>
  <!-- 上传或编辑弹窗 -->
  <Dialog
    v-model:visible="showUpload"
    :header="dialogTitle"
    attach="body"
    :close-on-overlay-click="false"
    :on-close="()=> {uploadLoading = false;emit('closeDialog');}"
    :destroy-on-close="true"
    placement="center"
    width="90%"
  >
    <template #confirmBtn>
      <Button
        :onclick="isEditType ? editData : uploadFile"
        :loading="uploadLoading"
        :disabled="disabledConfirmButton(tabs)"
      >
        Confirm
      </Button>
    </template>
    <Row class="justify-center h-full max-h-[70vh]">
      <div
        v-if="!loading"
        class="w-full"
      >
        <Row v-if="isUploadType">
          <Space
            direction="vertical"
            class="mb-3"
            style="gap: 0px"
          >
            <Text
              content="1. Download Template"
              type="subTitle"
            />
            <small class="self-center text-[grey]"> Follow instructions to fill in the table. </small>
            <DownLoadFileTemplateBtn
              :disabled="tabs.length > 4"
              :download-template-list="DOWNLOAD_OPTIONS"
            />
            <!-- <Button
              variant="outline"
              class="mb-3"
              :disabled="tabs.length > 4"
              @click="download"
            >
              <template #icon>
                <Download1Icon />
              </template>
              Download blank template
            </Button> -->
            <!-- <small class="self-center text-[grey]"
              >Supports batch upload of files, only 5 files can be uploaded at most.
            </small>
            <small class="self-center text-[grey]"> {{ cfg.uploadFileTips.doubleCountingTip }} </small>
            <small class="self-center text-[grey]">
              <span>{{ FILE_INFO.syncDataTip }}</span>
              <span class="text-black-primary text-[13px]">{{ FILE_INFO.syncDataTipSupplement }}</span>
            </small> -->
            <Text
              content="2. Upload Completed Template"
              type="subTitle"
            />
            <small class="self-center text-[grey]">
              Support batch upload of files, only 5 files can be uploaded at most.
            </small>
            <small class="self-center text-[grey]">
              If the contents of files are duplicated,the result will be recalculated.If there's an empty data column in
              the uploaded template, the corresponding field will be cleared.
            </small>
          </Space>
          <Space
            v-if="showErrorTips"
            class="w-full"
          >
            <div style="background-color: rgb(255, 243, 243); border: 1px solid rgb(255, 149, 144); border-radius: 5px; padding: 5px 10px;">
              <ErrorCircleFilledIcon
                size="20px"
                class="text-[#de7059]"
              />
              <Text
                class="ml-3"
                content="The red fields are not in the correct format and the system will not perform the import. Please edit and upload again."
                style="color: rgb(213, 73, 65); display: inline-block;"
              />
            </div>
          </Space>
          <br>
          <Upload
            ref="uploadRef"
            v-model="internalFiles"
            class="w-full"
            :class="{ 'hide-drag-area': tabs.length > 0 }"
            theme="custom"
            :abridge-name="[10, 8]"
            multiple
            draggable
            :use-mock-progress="false"
            :is-batch-upload="false"
            :upload-all-files-in-one-request="false"
            :allow-upload-duplicate-file="false"
            :before-upload="beforeUpload"
            :auto-upload="true"
            :on-remove="({ file }) => removeFile(file)"
            :max="5"
            :on-success="validateFile"
            :on-fail="validateFile"
            :request-method="(files: any) => manageFileRequest(files)"
            accept=".csv,.xlsx"
          >
            <template #file-list-display />
            <template #dragContent>
              <Button
                variant="outline"
                class="mb-3 justify-center"
                :disabled="tabs.length > 4"
              >
                <Upload1Icon />
                Upload
              </Button>
              <div>Click "Upload" above or drag the file to this area. Supported format: XLSX or CSV</div>
              <div class="text-warning-primary">The format must be XLSX or CSV.</div>
            </template>
          </Upload>
        </Row>
        <Row>
          <Col class="w-full">
            <Space
              direction="vertical"
              :class="{
                'w-full': true,
                'mt-7': isUploadType,
              }"
            >
              <Tabs
                v-if="tabs.length > 0"
                v-model="currentTab"
                @remove="removeTab"
                @change="onChangeTab"
              >
                <TabPanel
                  v-for="(data, tabIndex) in tabs"
                  :key="data.key"
                  class="testttttt"
                  :value="data.key"
                  :label="data.label"
                  :removable="isUploadType ? data.removable : undefined"
                >
                  <template #label>
                    <Space>
                      <LoadingIcon v-if="data.status === 'loading'" />
                      <CheckCircleIcon
                        v-else-if="data.status === 'success'"
                        class="text-[#4ac278]"
                      />
                      <ErrorCircleIcon
                        v-else
                        class="text-[#de7059]"
                      />
                      <Tooltip :content="data.label">
                        <div class="w-[150px] text-ellipsis overflow-hidden">{{ data.label }}</div>
                      </Tooltip>
                    </Space>
                  </template>
                  <!-- csv格式错误展示 -->
                  <Space
                    v-if="data.overallErrors.length > 0"
                    class="w-full my-10 pb-10 pt-3 px-5"
                    style="background-color: rgb(255, 243, 243); border: 1px solid rgb(255, 149, 144); border-radius: 5px; padding: 5px 10px;"
                    direction="vertical"
                  >
                    <div>
                      <ErrorCircleFilledIcon
                        size="20px"
                        class="text-[#de7059]"
                      />
                      <Text
                        class="ml-3"
                        style="color: rgb(213, 73, 65); display: inline-block;"
                        :content="data.overallErrors?.[0]"
                      />
                    </div>
                    <!-- <ul>
                      <li
                        v-for="(error, index) in data.overallErrors"
                        :key="index"
                      >
                        <template v-if="typeof error === 'string'"> {{ error }} </template>
                        <template v-else>
                          <div
                            v-for="(msg, sequence) in EXCEL_DUPLICATE_ERRORS"
                            :key="sequence"
                          >
                            {{ sequence === 0 ? index + 1 + '. ' + msg : msg }}
                          </div>
                          <Table
                            v-model:display-columns="DUPLICATE_EXCEL_COLUMNS"
                            class="mt-2"
                            :data="[error]"
                            :columns="DUPLICATE_TABLE_COLUMNS"
                            :total="[error].length"
                          />
                        </template>
                      </li>
                    </ul> -->
                  </Space>
                  <!-- 上传文件或者编辑文件表格展示 v-else -->
                  <Table
                    v-else
                    v-model:display-columns="cfg['uploadExcelColumns']"
                    :row-key="`upload-table-${tabIndex}-${Math.random() * 10}`"
                    class="w-full"
                    :row-class-name="getRowClassName"
                    :max-height="`40vh`"
                    :data="data.tableData"
                    :pagination="{
                      pageSize: pagination.pageSize,
                      current: pagination.pageIndex,
                      total: data.tableData.length,
                      onChange: onExcelPageChange,
                    }"
                    :loading="data.status === 'loading'"
                    :total="data.tableData.length"
                    :columns="
                      useExcelFileTable({
                        data: data.tableData,
                        customCols: cfg.getfilePreviewTableCols(isEditType, props.isAdmin),
                        isEditType: isEditType,
                        changeTableData: changeTableData,
                        tableRowChangeHandler: tableRowChangeHandler,
                      }).cols
                    "
                    @row-mouseenter="setToolTip"
                  />
                </TabPanel>
              </Tabs>
            </Space>
          </Col>
        </Row>
      </div>
      <FullLoading v-else />
    </Row>
  </Dialog>

  <CustomizeColumnsDialog
    v-if="isShowMetrics"
    v-model:visible="metricsVisible"
    title="Select Metrics"
    :list="metricList"
    type="single"
    :selected-list="metricSelected"
    :max-count="metricList.length"
    :min-count="1"
    @confirm="onMetricsConfirm"
  />
</template>

<script setup lang="ts">
import {
  UploadIcon,
  CheckCircleIcon,
  ErrorCircleIcon,
  ErrorCircleFilledIcon,
  LoadingIcon,
  Upload1Icon,
} from 'tdesign-icons-vue-next';
import { reactive, ref, watch, PropType, computed } from 'vue';
import {
  CUSTOM_DIALOG_RECORD,
  // TEMPLATE_UPLOAD_FILE_PATH,
  TABLE_COLUMNS_TRANS_DISPLAY_TITLE,
  DOWNLOAD_OPTIONS,
} from './const/const';
import { config } from './const/config';
import Text from 'common/components/Text';
import { useExcelFileTable } from './compose/viewExcelFile-table';
import {
  Row,
  Col,
  Upload,
  Button,
  Dialog,
  Space,
  Tabs,
  TabPanel,
  Table,
  TabValue,
  MessagePlugin,
  Tooltip,
} from 'tdesign-vue-next';
import FullLoading from 'common/components/FullLoading';
import { TabsFilesUploadModal, KolManageFileModal, CustomDialogType, CustomDialogEnum, EditTableOperationEnum } from './modal/kolManage';
import { tableDataToCsvStr } from '@/store/influencer/campaignSetUp/utils';
import { useGlobalGameStore } from '@/store/global/game.store';
import SvgIcon from 'common/components/SvgIcon/SvgIcon.vue';
// import { useAuthStageStore } from '@/store/global/auth.store';
// import { storeToRefs } from 'pinia';
import CustomizeColumnsDialog from 'common/components/CustomizeColumnsDialog';
import type { IMetric } from 'common/components/CustomizeColumnsDialog';
// import { downloadTemplate } from './utils';
import DownLoadFileTemplateBtn from '../components/DownloadFileTemplateBtn.vue';

const props = defineProps({
  files: {
    required: true,
    type: Array<KolManageFileModal>,
  },
  showUploadModal: {
    type: Boolean,
    required: true,
    default: false,
  },
  tab: {
    type: Array<TabsFilesUploadModal>,
    required: true,
  },
  customDialogType: {
    type: String as PropType<CustomDialogType>,
    default: 'upload',
  },
  loading: {
    type: Boolean,
    default: false,
  },
  uploadExcelColumns: {
    type: Array<string>,
    required: true,
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
  selectMetrics: {
    type: Array<string>,
    required: true,
  },
  metricList: {
    type: Array<string>,
    required: true,
  },
});

const emit = defineEmits([
  'update:files',
  'update:showUploadModal',
  'update:tabs',
  'uploadFile',
  'manageFileRequest',
  'removeFile',
  'disabledConfirmButton',
  'removeTab',
  'changeCustomDialogType',
  'changeTableDataValid',
  'updateSelectMetrics',
  'downloadFile',
  'closeDialog',
]);

const cfg = config.kol;

const gameStore = useGlobalGameStore();

const internalFiles = ref(props.files);
const currentTab = ref(1);
const uploadRef = ref();
const tabs = ref(props.tab);
const showUpload = ref(props.showUploadModal);
const errors = ref<string[]>([]);
const uploadLoading = ref(false);
const pagination = reactive({
  pageSize: 10,
  pageIndex: 1,
});

// --------- 弹窗类型区分的属性 -------------
const dialogTitle = computed(() => CUSTOM_DIALOG_RECORD[props.customDialogType].label ?? '');
const isUploadType = computed(() => props.customDialogType === CustomDialogEnum.UPLOAD);
const isEditType = computed(() => props.customDialogType === CustomDialogEnum.EDIT);

const beforeUpload = (file: any) => {
  // - - - - - - - - - - - 必须是csv文件 - - - - - - - - - - -
  if (!(file.raw.name.endsWith('.csv') || file.raw.name.endsWith('.xlsx'))) {
    MessagePlugin.warning('The format must be XLSX or CSV.');
    return false;
  }

  if (file.size > 10 * 1024 * 1024) {
    MessagePlugin.warning(cfg.uploadFileTips.fileSizeTips);
    return false;
  }

  return true;
};

/**
 * 构建编辑数据用于更新
 */
const buildEditData = () => {
  const editDataList: KolManageFileModal[] = [];

  const file = props.files?.[0];
  if (tabs?.value?.[0] && file) {
    const typeObj = { type: 'text/csv' };
    const { titles, name, type, status } = file.response;

    const editTableData = tabs?.value?.[0]?.tableData ?? []; // 编辑表格数据
    const csvContentStr = tableDataToCsvStr({
      tableList: editTableData,
      titles,
      isIncludeHeader: false,
    });
    const modifiedBlob = new Blob([csvContentStr], typeObj);
    // const raw = new File([modifiedBlob], name, typeObj);
    // const days = [...new Set(editTableData.map((row) => row.day))];
    // const channels = [...new Set(editTableData.map((row) => row.channel))];
    const editDataItem = {
      lastModified: 0,
      name,
      size: modifiedBlob.size,
      percent: 100,
      // raw: raw,
      status,
      response: {
        // channel: channels,
        crlfRaw: modifiedBlob,
        // day: days,
        // refreshMarkList: collectionBeforeDayRefreshMark([...tableDataRaw, ...editTableData], 6),
        lastModified: 0,
        name,
        percent: 100,
        // raw,
        size: modifiedBlob.size,
        status,
        titles,
        type,
        uploadTime: '',
      },
    } as unknown as KolManageFileModal;
    editDataList.push(editDataItem);
  }
  emit('update:files', editDataList);
  return editDataList;
};

const uploadFile = async () => {
  uploadLoading.value = true;
  try {
    const isDisabled = disabledConfirmButton(tabs?.value ?? []);
    if (isDisabled) {
      uploadLoading.value = false;
      return;
    }
    let submitData: KolManageFileModal[] = [];
    if (isEditType.value) {
      submitData = buildEditData();
    } else {
      submitData = internalFiles.value;
    }
    await new Promise((resolve, reject) => {
      emit('uploadFile', props.customDialogType, submitData, (result: boolean) => {
        if (result) {
          resolve(true);
        } else {
          reject(false);
        }
      });
    });
  } catch (error) {
    console.error('File upload failed:', error);
  } finally {
    internalFiles.value = [];
    uploadLoading.value = false;
  }
};

const editData = async () => {
  uploadLoading.value = true;
  try {
    const isDisabled = disabledConfirmButton(tabs?.value ?? []);
    if (isDisabled) {
      uploadLoading.value = false;
      return;
    }
    let submitData: KolManageFileModal[] = [];
    if (isEditType.value) {
      submitData = buildEditData();
    } else {
      submitData = internalFiles.value;
    }

    await new Promise((resolve, reject) => {
      emit('uploadFile', submitData, (result: boolean) => {
        if (result) {
          resolve(true);
        } else {
          reject(false);
        }
      });
    });
  } catch (error) {
    console.error('File upload failed:', error);
  } finally {
    // internalFiles.value = [];
    uploadLoading.value = false;
  }
};

const removeTab = ({ value, index }: { value: TabValue; index: number }) => {
  if (index < 0) return;
  if (tabs.value.length === 0) return;
  emit('removeTab', { value, index });

  tabs.value.splice(index, 1);

  if (tabs.value.length === 0) {
    currentTab.value = 1;
  } else {
    currentTab.value = tabs.value[tabs.value.length - 1]?.key;
  }
};

const disabledConfirmButton = (tab: any): boolean => {
  let isDisabled = true;
  emit('disabledConfirmButton', tab, (result: boolean) => {
    isDisabled = result;
  });
  return isDisabled;
};

// 切换excel内的桌表页面
const onExcelPageChange = async (pageInfo: { current: number; previous: number; pageSize: number }) => {
  const { current, pageSize } = pageInfo;
  pagination.pageIndex = pagination.pageSize !== pageSize ? 1 : current;
  pagination.pageSize = pageSize;
};

const removeFile = (data: any) => {
  emit('removeFile', data);
};

const onChangeTab = () => {
  pagination.pageIndex = 1;
  pagination.pageSize = 10;
};

const validateFile = () => {
  const uniqueArray = [...new Set(errors.value)];
  if (uniqueArray.length > 0) uniqueArray.map(data => MessagePlugin('error', data));
  errors.value = [];
};

const manageFileRequest = (data: any): Promise<any> => new Promise((resolve) => {
  emit('manageFileRequest', data, (result: any) => {
    if (result.response?.error) errors.value.push(result.response.error);
    currentTab.value = tabs.value.length;
    /**
       * 返回给request-method方法的数据格式 { status: 'success' | 'error', response: any};
       * success会将数据扩展到internalFiles数组对象上去
       */
    resolve(result);
  });
});

const clickUploadBtn = () => {
  emit('changeCustomDialogType', CustomDialogEnum.UPLOAD);
  showUpload.value = true;
};

// const upload = () => {
//   uploadRef.value.triggerUpload();
// };

// const download = () => {
//   downloadTemplate(TEMPLATE_UPLOAD_FILE_PATH);
// };

watch(internalFiles, (newFiles) => {
  emit('update:files', newFiles);
});

watch(tabs, (tab) => {
  emit('update:tabs', tab);
});

// 数据更改传至parent
watch(showUpload, (show) => {
  if (!showUpload.value) {
    internalFiles.value = [];
    tabs.value = [];
  }

  emit('update:showUploadModal', show);
});

watch(
  () => props.tab,
  (newTab) => {
    tabs.value = newTab;
  },
);

// 数据更改传至child
watch(
  () => props.showUploadModal,
  (newVal) => {
    showUpload.value = newVal;
    pagination.pageIndex = 1;
    pagination.pageSize = 10;
  },
);

const getRowClassName = (row: any) => (row.row.hasRowError ? 'error-row' : '');

const setToolTip = (context: any) => {
  const row = context?.row;
  const format = row?.Format;

  if (format) {
    const tooltip = /Stream/i.test(format)
      ? 'The stream data of this channel on that date has been entered, please do not create it again.'
      : 'The deliverable link you fill in is duplicate with the deliverable link of an existing deliveray.';

    const ele = context?.e?.currentTarget;
    if (ele) {
      ele.setAttribute('data-tooltip', tooltip);
    }
  }
};

const showErrorTips = computed(() => tabs.value.some(tab => tab.tableData.some(row => row?.errors?.length > 0 || row?.hasRowError === true)),
  // || tabs.value.some(tab => tab.overallErrors.length > 0);
);

// 创建默认行数据，用于行的新增默认数据
const createDefaultRow = () => {
  const createObj = props.uploadExcelColumns.reduce((record, col) => {
    const newRecord = { ...record };
    newRecord[col] = '';
    return newRecord;
  }, {} as any);
  // Object.assign(createObj, defaultObj);

  createObj.gamename = gameStore.gameCode; // 默认去当前game
  return createObj;
};

//
const changeTableData = ({
  rowIndex,
  colKey,
  value,
}: {
  rowIndex: number; //
  colKey: string; //
  value: any; //
}) => {
  // 表格文件修改只有一个表格文件
  if (tabs?.value[0]) {
    // 修改表格数据
    const currentTableList = tabs?.value?.[0]?.tableData ?? []; // 对应的表格
    const curentRowIndex = (pagination.pageIndex - 1) * pagination.pageSize + rowIndex; // 表格第几行
    const currentRow = currentTableList?.[curentRowIndex] ?? {}; // 修改表格对应的行数据
    let hasChangeWithPreOp = false;
    hasChangeWithPreOp = currentRow[colKey] !== value;
    currentRow[colKey] = value; // 修改单元格数据
    // emit('changeTableData', { rowIndex, colKey, value });

    if (colKey === 'Channel Link' && hasChangeWithPreOp) {
      uploadLoading.value = true;
      emit('changeTableDataValid', pagination, colKey, hasChangeWithPreOp, () => {
        uploadLoading.value = false;
      });
    } else {
      emit('changeTableDataValid', pagination, colKey, hasChangeWithPreOp);
    }
    // emit('changeTableDataValid');
  }
};

const tableRowChangeHandler = ({
  EditTableOperationType,
  rowIndex,
}: {
  EditTableOperationType: EditTableOperationEnum;
  rowIndex: number; //
}) => {
  // 表格文件修改只有一个表格文件
  if (tabs?.value[0]) {
    // 修改表格数据
    const currentTableList = tabs?.value?.[0]?.tableData ?? []; // 对应的表格
    const currentRowIndex = (pagination.pageIndex - 1) * pagination.pageSize + rowIndex; // 表格第几行(操作第几行)
    const currentPageFirstRow = (pagination.pageIndex - 1) * pagination.pageSize; // 当前页的第一行
    // const currentRow = currentTableList?.[curentRowIndex] ?? {}; // 修改表格对应的行数据
    // currentRow[colKey] = value; // 修改单元格数据
    // // emit('changeTableData', { rowIndex, colKey, value });
    const currentTableLen = currentTableList.length; // 表格的长度

    if (EditTableOperationType === EditTableOperationEnum.DELETE) {
      // 删除行的操作
      if (currentTableLen > 1) {
        // 表格行数要大于1才能执行删除行的操作
        currentTableList.splice(currentRowIndex, 1);
        // 操作行 等于 当前页第一行  &&  表格最后一行 等于 当前页第一行
        if (currentRowIndex === currentPageFirstRow && currentTableLen - 1 === currentPageFirstRow) {
          // 比如第五页只有一条数据，执行删除改行操作，页数需要变成第四页
          pagination.pageIndex = pagination.pageIndex - 1; // 选中的页数减一
        }
      }
    } else if (EditTableOperationType === EditTableOperationEnum.ADD_UPWARDS) {
      // 向上新增行操作
      const addRowObj = createDefaultRow();
      currentTableList.splice(currentRowIndex, 0, addRowObj);
    } else if (EditTableOperationType === EditTableOperationEnum.ADD_DOWNWARDS) {
      // 向下新增行操作
      const addRowObj = createDefaultRow();
      currentTableList.splice(currentRowIndex + 1, 0, addRowObj);
    }

    // 表格数据验证
    emit('changeTableDataValid', pagination);
    // emit('changeTableDataValid', pagination);
  }
};

const isShowMetrics = ref(false);
const metricsVisible = ref(false);
// const metricsList = ref<IMetric[]>([]);
// 头ID固定，尾Action固定
const reOrderArray = (metrics: string[]) => {
  // 找到 id 和 action 的索引
  const idIndex = metrics.indexOf('id');
  const actionIndex = metrics.indexOf('action');

  // 如果 id 存在且不是第一个，移动到第一个位置
  if (idIndex > -1 && idIndex !== 0) {
    metrics.splice(idIndex, 1); // 移除 id
    metrics.unshift('id'); // 添加到开头
  }

  // 如果 action 存在且不是最后一个，移动到最后一个位置
  if (actionIndex > -1 && actionIndex !== metrics.length - 1) {
    metrics.splice(actionIndex, 1); // 移除 action
    metrics.push('action'); // 添加到末尾
  }

  return metrics;
};
const metricSelected = computed(() => reOrderArray(props.selectMetrics)?.map((matric, index) => ({
  index,
  title: TABLE_COLUMNS_TRANS_DISPLAY_TITLE[matric],
  colKey: matric,
  tips: TABLE_COLUMNS_TRANS_DISPLAY_TITLE[matric],
  isHide: false,
  disabled: false,
})),
);
const metricList = computed(() => reOrderArray(props.metricList).map((matric, index) => {
  if (['id', 'action'].includes(matric)) {
    return {
      index,
      title: TABLE_COLUMNS_TRANS_DISPLAY_TITLE[matric],
      colKey: matric,
      tips: TABLE_COLUMNS_TRANS_DISPLAY_TITLE[matric],
      isHide: false,
      disabled: true,
    };
  }
  return {
    index,
    title: TABLE_COLUMNS_TRANS_DISPLAY_TITLE[matric],
    colKey: matric,
    tips: TABLE_COLUMNS_TRANS_DISPLAY_TITLE[matric],
    isHide: false,
    disabled: false,
  };
}));

const showMetricSelect = () => {
  isShowMetrics.value = true;
  metricsVisible.value = true;
};
const onMetricsConfirm = (data: IMetric[]) => {
  const metrics = data.map(d => d.colKey);
  emit('updateSelectMetrics', metrics);
};
const downloadFile = () => {
  const metrics = metricSelected.value.map(d => d.colKey);
  emit('downloadFile', metrics);
};
</script>

<style lang="scss" scoped>
:deep(.t-upload__flow-table) {
  border-collapse: collapse !important;
  border-left: none !important;
  border-top: none !important;
  border-right: none !important;
  border-bottom: 1px solid var(--td-component-border) !important;
}

:deep(.t-upload__flow-table > thead > tr > th) {
  font-weight: bolder !important;
  color: black !important;
  border: none !important;
  text-transform: capitalize !important;
  background-color: rgb(240 241 246 / var(--tw-bg-opacity)) !important;
}
:deep(.t-upload__flow-table > thead > tr > th:first-child) {
  border-top-left-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}
:deep(.t-upload__flow-table > thead > tr > th:last-child) {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
}

:deep(.t-upload__dragger-center) {
  width: 100% !important;
}

.hide-drag-area :deep(.t-upload__dragger-center) {
  display: none;
}
:deep(.t-upload__dragger .t-upload__trigger) {
  text-align: center;
}
.operate-item svg {
  fill: var(--aix-text-color-black-secondary);
}
:deep(.t-table__body tr) {
  position: relative; /* 使得伪元素相对于行定位 */
}
:deep(.t-table__body tr.error-row) {
  background-color: rgb(255, 243, 243);
}
:deep(.t-table__body tr.error-row::after) {
  content: attr(data-tooltip); /* 使用 data-tooltip 属性的内容 */
  position: absolute;
  bottom: 90%; /* 在行的上方 */
  left: 50%; /* 水平居中 */
  transform: translateX(-50%); /* 使其居中 */
  background-color: rgba(0, 0, 0, 0.7); /* 背景颜色 */
  color: white; /* 字体颜色 */
  padding: 0px 10px; /* 内边距 */
  border-radius: 5px; /* 圆角 */
  white-space: nowrap; /* 防止换行 */
  opacity: 0; /* 初始透明度为0 */
  transition: opacity 0.3s; /* 过渡效果 */
  pointer-events: none; /* 禁止鼠标事件 */
  z-index: 100; /* 确保提示在上层 */
  height: 30px;
  line-height: 30px;
}
:deep(.t-table__body tr.error-row:hover::after) {
  opacity: 1; /* 悬停时显示 */
}
</style>
