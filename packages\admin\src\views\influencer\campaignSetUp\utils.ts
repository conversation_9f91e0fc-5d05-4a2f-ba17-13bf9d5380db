/**
 * 下载模板文件
 * @param cdnLink
 */
export function downloadTemplate(cdnLink: string) {
  const downloadLink = cdnLink;
  const link = document.createElement('a');
  link.href = downloadLink;
  link.download = '';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

export function getPlatformFromLink(channelOrDeliveryLink: string) {
  const domainToPlatform: {[key: string]: string} = {
    'www.douyin.com': 'Douyin',
    'www.bilibili.com': 'Bilibili',
    'www.youtube.com': 'Youtube',
    'www.twitch.tv': 'Twitch',
    'douyin.com': 'Douyin',
    'bilibili.com': 'Bilibili',
    'youtube.com': 'Youtube',
    'twitch.tv': 'Twitch',
    'www.tiktok.com': 'TikTok',
    'tiktok.com': 'TikTok',
    'www.facebook.com': 'Facebook',
    'facebook.com': 'Facebook',
    'x.com': 'X',
    'www.instagram.com': 'Instagram',
    'instagram.com': 'Instagram',
  };

  let platform = '';
  let hostname = '';
  try {
    const urlInfo = new URL(channelOrDeliveryLink);
    hostname = urlInfo.hostname.toLowerCase();

    if (domainToPlatform[hostname]) {
      platform = domainToPlatform[hostname];
    }
  } catch (e) {}

  return platform;
}

export function getVideoIdByDeliveryLink(deliveryLink: string) {
  let videoId = '';
  let tips = 'Support URL Format:';

  try {
    const info = new URL(deliveryLink);
    const { hostname, pathname, searchParams } = info;

    const fbRegex1 = /\/reel\/(\d+)/;
    const fbRegex2 = /\/watch\//;
    const fbRegex3 = /\/videos\/([a-zA-Z0-9]+)\/?/;

    const insRegex1 = /\/p\/([^/]+)/;
    const insRegex2 = /\/reel\/([^/]+)/;

    const xRegex = /(?:[^/]+\/)?status\/(\d+)/;
    const tkRegex = /\/@[^/]+\/video\/(\d+)/;
    const bilibiliRegex = /\/video\/([a-zA-Z0-9]+)/;
    const dyRegex = /\/video\/(\d+)/;

    const youtubeRegex1 = /\/watch/;
    const youtubeRegex2 = /\/shorts\/([a-zA-Z0-9_-]{11})/;

    const twitchRegex = /\/videos\/(\d+)/;

    switch (hostname) {
      case 'www.facebook.com':
      case 'facebook.com':
        tips = `${tips} eg. https://www.facebook.com/reel/9699903146743558; https://www.facebook.com/watch/?v=1805735000000657; https://www.facebook.com/PUBGMOBILE/videos/1398082681612624`;

        if (fbRegex1.test(pathname)) {
          const matches = pathname.match(fbRegex1);
          videoId = matches?.[1] || '';
        }

        if (fbRegex2.test(pathname)) {
          videoId = searchParams.get('v') || '';
        }

        if (fbRegex3.test(pathname)) {
          const matches = pathname.match(fbRegex3);
          videoId = matches?.[1] || '';
        }
        break;

      case 'www.instagram.com':
      case 'instagram.com':
        tips = `${tips} eg. https://www.instagram.com/p/DH_RHlhKwL8/; https://www.instagram.com/reel/DBWjLR7ILM2/`;
        if (insRegex1.test(pathname)) {
          const matches = pathname.match(insRegex1);
          videoId = matches?.[1] || '';
        }

        if (insRegex2.test(pathname)) {
          const matches = pathname.match(insRegex2);
          videoId = matches?.[1] || '';
        }
        break;

      case 'www.x.com':
      case 'x.com':
        tips = `${tips} eg. https://x.com/page/status/1911448677773668814`;
        if (xRegex.test(pathname)) {
          const matches = pathname.match(xRegex);
          videoId = matches?.[1] || '';
        }
        break;

      case 'www.tiktok.com':
      case 'tiktok.com':
        tips = `${tips} eg. https://www.tiktok.com/@user/video/7488237814522465591`;

        if (tkRegex.test(pathname)) {
          const matches = pathname.match(tkRegex);
          videoId = matches?.[1] || '';
        }
        break;

      case 'www.bilibili.com':
      case 'bilibili.com':
        tips = `${tips} eg. https://www.bilibili.com/video/BV1e2DWY4Ee8`;

        if (bilibiliRegex.test(pathname)) {
          const matches = pathname.match(bilibiliRegex);
          videoId = matches?.[1] || '';
        }
        break;

      case 'www.douyin.com':
      case 'douyin.com':
        tips = `${tips} eg. https://www.douyin.com/video/7433428990209035570`;

        if (dyRegex.test(pathname)) {
          const matches = pathname.match(dyRegex);
          videoId = matches?.[1] || '';
        }
        break;

      case 'www.youtube.com':
      case 'youtube.com':
        tips = `${tips} eg. https://www.youtube.com/shorts/zdLktkgoXKY; https://www.youtube.com/watch?v=Re1k2S-kBh4`;

        if (youtubeRegex1.test(pathname)) {
          videoId = searchParams.get('v') || '';
        }

        if (youtubeRegex2.test(pathname)) {
          const matches = pathname.match(youtubeRegex2);
          videoId = matches?.[1] || '';
        }
        break;

      case 'www.twitch.tv':
      case 'twitch.tv':
        tips = `${tips} eg. https://www.twitch.tv/videos/2307427436`;

        if (twitchRegex.test(pathname)) {
          const matches = pathname.match(twitchRegex);
          videoId = matches?.[1] || '';
        }
        break;
    }

    if (!videoId && tips.length === 'Support URL Format:'.length) {
      tips = 'NOT Support URL Format.';
    }
  } catch (err) {
    tips = 'Please Input a Right URL.';
  }

  return { video_id: videoId, tips };
}
