<template>
  <div class="h-full">
    <FullLoading v-if="isIniting" />
    <div v-else-if="isInitError" class="flex flex-col justify-center items-center h-full">
      <span>Failed to load data, please try again</span>
      <t-button @click="campaignStore.init">
        <span>Retry</span>
      </t-button>
    </div>
    <Teleport v-else to="body" :disabled="!isFullScreenMode">
      <CommonView
        :class="{ 'fullscreen-content': isFullScreenMode }"
        :form-props="formProps"
        :tab-props="{
          modelValue: currentTabId,
          list: tabList,
          showNum: 4,
          shareParams,
          viewTableName: SYSTEM,
          customIconList,
          viewEventCallBack,
          'onUpdate:modelValue': (newValue: string) => switchTab(newValue),
        }"
      >
        <template #views>
          <Teleport to="body" :disabled="!isOnlyViewMode">
            <div :class="{ 'only-view-content': isOnlyViewMode }">
              <BusinessTable
                :model-value="modModelValue.table"
                :attr-list="pageConfig.table_attr_list"
                metrics-type="single"
                :metric-list="pageConfig.table_metric_list"
                :use-table-data-download="true"
                :is-show-filter="true"
                :fetch-data-list="(v: any) => campaignStore.fetchTableDataList({
                  isDownload: true,
                  downloadModelValue: v
                })"
                :is-loading="isTableLoading"
                :table-data="{
                  data: tableData.list,
                  rowKey: '',
                  count: tableData.count,
                }"
                :table-columns-rule="pageConfig.table_columns_rule"
                :page-size-options="[10, 20, 30, 50, 100, 200]"
                :is-number-type-right-align="true"
                @update:model-value="campaignStore.setTableModelValue"
              />
            </div>
          </Teleport>
        </template>
      </CommonView>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import CommonView from 'common/components/Layout/CommonView.vue';
import { SYSTEM, useInfluencerCampaignStore } from '@/store/influencer/campaign/campaign.store';
import BusinessTable from 'common/components/BusinessTable/index';
import { ref, computed, toRaw, unref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useGlobalGameStore } from '@/store/global/game.store';
import { VIEW_ACTION } from 'common/components/NewViewTab/const';
import { useGenFormData } from 'common/compose/form/gen-form-data';
import { unRefObj } from 'common/utils/reactive';
import { omit } from 'lodash-es';
import { closeTypeMap, resetTypeMap } from 'common/components/FormContainer';
import FullLoading from 'common/components/FullLoading';
import { getQueryString } from 'common/utils/url';
import { TTabView } from 'common/service/influencer/common/getView';

enum EViewMode {
  DEFAULT = '',
  ONLY_VIEW = 'only_view', // 全屏并隐藏可操作按钮
  FULLSCREEN = 'fullscreen', // 全屏并隐藏视图相关操作
};

const campaignStore = useInfluencerCampaignStore();
const { gameCode } = storeToRefs(useGlobalGameStore());

const {
  tabList,
  currentTab,
  currentTabId,
  formList,
  isIniting,
  isInitError,
  modModelValue,
  tableData,

  isTableLoading,

  pageConfig,
} = storeToRefs(campaignStore);

const { setFormModelValue } = campaignStore;

const shareParams = computed(() => ({
  param: {
    ...(unRefObj(currentTab?.value?.param) ?? {}),
    ...unRefObj(modModelValue.value ?? {}),
    form: unRefObj(unref(formProps.value?.modelValue)),
  },
  fold_list: toRaw(unref(formProps.value?.foldList)) ?? [],
  game: gameCode.value,
}));

watch(
  () => [unref(gameCode)],
  () => {
    campaignStore.init();
  },
);


// - - - - - - - - - - - 视图全屏功能 start - - - - - - - - - - -
const defaultViewMode = getQueryString('view_mode') ?? EViewMode.DEFAULT;
const viewMode = ref<EViewMode>(defaultViewMode as EViewMode);
// 是否为全屏模式
const isFullScreenMode = computed(() => viewMode.value === EViewMode.FULLSCREEN);
// 是否为only_view模式
const isOnlyViewMode = computed(() => viewMode.value === EViewMode.ONLY_VIEW);
// 自定义icon列表
const customIconList = computed(() => {
  const iconList = [
    {
      iconName: isFullScreenMode.value ? 'exit-fullscreen-2' : 'fullscreen-2',
      content: isFullScreenMode.value ? 'Exit Fullscreen' : 'Fullscreen',
      clickEvent: () => {
        viewMode.value = viewMode.value === EViewMode.FULLSCREEN ? EViewMode.DEFAULT : EViewMode.FULLSCREEN;
      },
    },
  ];
  return iconList;
});
// - - - - - - - - - - - 视图全屏功能 end - - - - - - - - - - -


// 视图事件回调(新增、编辑、删除)
const viewEventCallBack =  (action: keyof typeof VIEW_ACTION, result: TTabView) => {
  console.log('getViewEvent', action, result);
  if (action === VIEW_ACTION.add) {
    tabList.value.push({
      ...result,
      label: result?.name,
      value: result?.id,
    });
    switchTab(result?.id);
  } else if (action === VIEW_ACTION.edit) {
    const index = tabList.value.findIndex((item: TTabView) => item.value === result?.id);
    if (index !== -1) {
      tabList.value[index] = {
        ...result,
        label: result?.name,
        value: result?.id,
      };
    }
    switchTab(result?.id);
  } else if (action === VIEW_ACTION.del) {
    const defaultId = tabList.value[0]?.id;
    const index = tabList.value.findIndex((item: TTabView) => item.value === result?.id);
    if (index !== -1) {
      tabList.value.splice(index, 1);
    }
    switchTab(defaultId);
  }
};

const onFormSubmit = (formData: Record<string, any>) => {
  console.log('onFormSubmit', formData.value);
  setFormModelValue(formData.value);
  campaignStore.fetchAllDataList();
};
const onFormReset = (formData: Record<string, any>) => {
  console.log('onFormReset', formData.value);
  setFormModelValue(formData.value);
  campaignStore.fetchAllDataList();
};

const switchTab = (tabId: string) => {
  console.log('switchTab', tabId);
  currentTabId.value = tabId;
  setFormModelValue(currentTab.value?.param?.form || {});
  modModelValue.value = omit(currentTab.value?.param, 'form');
  campaignStore.fetchAllDataList();
};

const formProps = computed(() => {
  const form = useGenFormData(
    formList.value,
    (formData: Record<string, any>) => onFormSubmit(formData),
    (formData: Record<string, any>) => onFormReset(formData),
    currentTab.value?.fold_list ?? [],
  );
  return {
    ...form,
    closeType: closeTypeMap.toSelfType,
    resetType: resetTypeMap.initial,
  };
});

campaignStore.init();


</script>

<style lang="scss" scoped>
.fullscreen-content {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #f0f1f6;
  overflow: auto;
  padding-top: 20px;
  z-index: 10;
}

.only-view-content {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #f0f1f6;
  overflow: auto;
  padding: 0 20px;
  padding-top: 20px;
  z-index: 10;
}
</style>
