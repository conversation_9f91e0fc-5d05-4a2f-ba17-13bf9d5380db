<template>
  <div>
    <t-tooltip
      theme="light"
    >
      <div>
        <t-dropdown
          trigger="click"
          :options="downloadTemplateList"
          :placement="popupPlacement"
          :popup-props="{
            // overlayInnerStyle: {
            //   marginTop: '24px',
            // }
          }"
          @click="(item: any) => {
            // console.log('item', item);
            download(item.value);
          }"
        >
          <Button
            variant="outline"
            class="mb-3"
            :disabled="disabled"
          >
            <template #icon>
              <Download1Icon />
            </template>
            {{ props.btnText }}
          </Button>
        </t-dropdown>
      </div>
    </t-tooltip>
  </div>
</template>
<script setup lang="ts">
import { downloadTemplate } from '../campaignSetUp/utils';
import {
  Button,
} from 'tdesign-vue-next';
import {
  Download1Icon,
} from 'tdesign-icons-vue-next';

const props = defineProps({
  btnText: {
    type: String,
    default: 'Download blank template',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  downloadTemplateList: {
    type: Array<{ content: string, value: string }>,
    default: () => [],
  },
  popupPlacement: {
    type: String,
    default: 'right',
  },
});


const download = (fileUrl: string) => {
  downloadTemplate(fileUrl);
};
</script>
<style lang="scss" scoped />
