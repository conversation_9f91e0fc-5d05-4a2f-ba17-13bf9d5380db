// 指标卡
export const cardProps = {
  skeleton: {
    width: '100%',
    height: '116px',
  },
  containerMinWidth: '295px',
  styleSize: 'small',
  isAbsTrend: true,
  allowActive: false,
  metricCardStyle: {
    height: '100%',
  },
  loadingWrapperStyle: {
    height: '116px',
    width: '100%',
  },
  metricCardTipStyle: {
    maxWidth: '300px',
  },
} as any;

export const videosCardProps = {
  ...cardProps,
  skeleton: {
    width: '100%',
    height: '100px',
  },
  containerMinWidth: '200px',
  metricCardStyle: {
    width: '200px',
  },
  swiperSlideStyle: {
    flex: 'none',
    width: 'auto',
  },
  metricCardValueStyle: {
    'line-height': '24px',
  },
} as any;

export const streamsCardProps = {
  ...cardProps,
  skeleton: {
    width: '100%',
    height: '100px',
  },
  containerMinWidth: '200px',
  metricCardStyle: {
    width: '200px',
  },
  swiperSlideStyle: {
    flex: 'none',
    width: 'auto',
  },
  metricCardValueStyle: {
    'line-height': '24px',
  },
} as any;

export const shortsCardProps = {
  ...cardProps,
  skeleton: {
    width: '100%',
    height: '100px',
  },
  containerMinWidth: '200px',
  metricCardStyle: {
    width: '200px',
  },
  swiperSlideStyle: {
    flex: 'none',
    width: 'auto',
  },
  metricCardValueStyle: {
    'line-height': '24px',
  },
} as any;

// 柱状图
export const barChartProps = {
  skeleton: {
    width: '100%',
    height: '534px',
  },
  isAddBg: true,
  detailType: 'smooth',
  chartType: 'line',
  tooltipSort: 'desc',
  sortToLastValue: '*Others',
  attrType: 2,
  groupbyKey: 'date',
  isXAxisSort: true,
  chartTypeList: [
    {
      iconName: 'statistics',
      type: 'bar',
      detailType: 'stack',
    },
    {
      iconName: 'trend2',
      type: 'line',
      detailType: 'smooth',
    },
  ],
  basicChartProps: {
    isShowLegend: true,
    tooltipFilterZero: false,
    regRules: [],
  },
  downloadDataRule: [],
  isTransformDataToDown: true,
} as any;

// 折线图
export const LineChartProps = {
  skeleton: {
    width: '100%',
    height: '534px',
  },
  isAddBg: true,
  detailType: 'smooth',
  chartType: 'line',
  tooltipSort: 'desc',
  sortToLastValue: '*Others',
  attrType: 2,
  groupbyKey: 'date',
  isXAxisSort: true,
  chartTypeList: [
    {
      iconName: 'statistics',
      type: 'bar',
      detailType: 'stack',
    },
    {
      iconName: 'trend2',
      type: 'line',
      detailType: 'smooth',
    },
  ],
  basicChartProps: {
    isShowLegend: true,
    tooltipFilterZero: false,
    regRules: [],
  },
  downloadDataRule: [],
  isTransformDataToDown: true,
} as any;

// 饼图1
export const Pie1ChartProps = {
  isAddBg: true,
  detailType: 'ringTextOut',
  chartType: 'pie',
  attrType: 1,
  style: {
    width: '50%',
  },
  basicChartProps: {
    dataMode: 'y',
    yAxisName: ' ',
    tooltipFilterZero: false,
    yAxisLabelFormat: ['splitAndJoinValue(20,40)'],
    grid: {
      right: 40,
      bottom: 40,
      left: 0,
      containLabel: true,
    },
    regRules: [],
    yAxisLabel: {
      width: 120,
      overflow: 'truncate',
    },
    legendProps: {
      type: 'scroll',
    },
  },
  chartTypeList: [
    {
      iconName: 'chart-pie',
      type: 'pie',
      detailType: 'ringTextOut',
    },
    {
      iconName: 'chart-bar',
      type: 'bar',
      detailType: 'stack',
    },
  ],
  isTransformDataToDown: true,
} as any;

// 饼图2
export const Pie2ChartProps = {
  isAddBg: true,
  detailType: 'ringTextOut',
  chartType: 'pie',
  attrType: 1,
  style: {
    width: '50%',
  },
  basicChartProps: {
    dataMode: 'y',
    yAxisName: ' ',
    tooltipFilterZero: false,
    yAxisLabelFormat: ['splitAndJoinValue(20,40)'],
    grid: {
      right: 40,
      bottom: 40,
      left: 0,
      containLabel: true,
    },
    regRules: [],
    yAxisLabel: {
      width: 120,
      overflow: 'truncate',
    },
    legendProps: {
      type: 'scroll',
    },
  },
  chartTypeList: [
    {
      iconName: 'chart-pie',
      type: 'pie',
      detailType: 'ringTextOut',
    },
    {
      iconName: 'chart-bar',
      type: 'bar',
      detailType: 'stack',
    },
  ],
  isTransformDataToDown: true,
} as any;

export const CampaignChartProps = {
  componentTitle: 'Media Value By Campaign',
  skeleton: {
    width: '100%',
    height: '534px',
  },
  isAddBg: true,
  isShowMetric: false,
  chartType: [
    {
      chartType: 'bar',
      detailType: 'stack',
      dataValueFiled: 'media_value',
      dataItemField: 'type',
      dataGroupItemField: 'campaign_name',
      option: {
        series: {
          yAxisIndex: 0,
        },
      },
    },
    {
      chartType: 'line',
      detailType: 'smooth',
      dataValueFiled: 'roi',
      dataItemField: 'type',
      dataGroupItemField: 'campaign_name',
      option: {
        series: {
          yAxisIndex: 1,
        },
      },
    },
  ],
  tooltipSort: 'desc',
  isShowDownload: true,
  isXAxisSort: true,
  chartTypeList: [],
  basicChartProps: {
    isShowLegend: true,
    dataItemField: 'campaign_name',
    multiGraphDataTransformOption: {
      splitDataToRecordByMetricKeyList: ['media_value', 'roi'],
    },
    isRemoveEmptyDataInNoStackBar: false,
    isShowSeries: false,
    tooltipFilterZero: false,
    axisFormatType: '',
    regRules: [],
    xAxisLabelFormatBreakByNum: 20, // x轴字符过长时换行
  },
  isTransformDataToDown: false,
} as any;
