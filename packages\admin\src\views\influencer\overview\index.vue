<template>
  <div class="h-full">
    <FullLoading v-if="isIniting" />
    <div v-else-if="isInitError" class="flex flex-col justify-center items-center h-full">
      <span>Failed to load data, please try again</span>
      <t-button @click="overviewStore.init">
        <span>Retry</span>
      </t-button>
    </div>
    <Teleport v-else to="body" :disabled="!isFullScreenMode">
      <CommonView
        :class="{ 'fullscreen-content': isFullScreenMode }"
        :form-props="formProps"
        :tab-props="{
          modelValue: currentTabId,
          list: tabList,
          showNum: 4,
          shareParams,
          viewTableName: SYSTEM,
          customIconList,
          viewEventCallBack,
          'onUpdate:modelValue': (newValue: string) => switchTab(newValue),
        }"
      >
        <template #views>
          <Teleport to="body" :disabled="!isOnlyViewMode">
            <div :class="{ 'only-view-content': isOnlyViewMode }">
              <MetricCardSwiper
                v-bind="cardProps"
                :data="cardDataList"
                :is-loading="isCardLoading"
                :model-value="modModelValue.card"
                :cfg-list="pageConfig.card_metric_list"
              />
              <div>
                <div class="card-details" :style="{'height': isShowExpandDetails ? subCardHeight : '0'}">
                  <template v-if="isShowVideo">
                    <div class="card-title">Videos</div>
                    <MetricCardSwiper
                      v-bind="videosCardProps"
                      :data="videosCardDataList"
                      :is-loading="isVideosCardLoading"
                      :model-value="modModelValue.videos_card"
                      :cfg-list="pageConfig.video_card_metric_list"
                    />
                  </template>
                  <template v-if="isShowStream">
                    <div class="card-title">Streams</div>
                    <MetricCardSwiper
                      v-bind="streamsCardProps"
                      :data="streamsCardDataList"
                      :is-loading="isStreamsCardLoading"
                      :model-value="modModelValue.streams_card"
                      :cfg-list="pageConfig.stream_card_metric_list"
                    />
                  </template>
                  <template v-if="isShowShorts">
                    <div class="card-title">Shorts</div>
                    <MetricCardSwiper
                      v-bind="shortsCardProps"
                      :data="shortsCardDataList"
                      :is-loading="isShortsCardLoading"
                      :model-value="modModelValue.shorts_card"
                      :cfg-list="pageConfig.video_card_metric_list"
                    />
                  </template>
                </div>
                <div
                  class="flex justify-center items-center cursor-pointer mt-1 text-brand collapse-btn"
                  @click="isShowExpandDetails = !isShowExpandDetails"
                >
                  <span> {{ isShowExpandDetails ? 'Collapse Details' : 'Expand Details' }}</span>
                  <t-icon
                    class="transition"
                    :class="{'-rotate-180': isShowExpandDetails}"
                    size="18"
                    name="chevron-down"
                  />
                </div>
              </div>
              <BusinessChart
                class="mt-10"
                v-bind="barChartProps"
                :model-value="modModelValue.bar"
                :is-loading="isBarChartLoading"
                :data="barChartDataList"
                :attr-list="pageConfig.chart_attr_list"
                :metric-list="pageConfig.chart_metric_list"
                :download-data-rule="pageConfig.download_data_rule"
                :basic-chart-props="{
                  ...barChartProps.basicChartProps,
                  regRules: pageConfig.chart_data_rule,
                }"
                @update:model-value="overviewStore.setBarModelValue"
              />
              <BusinessChart
                class="mt-10"
                v-bind="LineChartProps"
                :model-value="modModelValue.line"
                :is-loading="isLineChartLoading"
                :data="lineChartDataList"
                :attr-list="pageConfig.chart_attr_list"
                :metric-list="pageConfig.chart_metric_list"
                :download-data-rule="pageConfig.download_data_rule"
                :basic-chart-props="{
                  ...LineChartProps.basicChartProps,
                  regRules: pageConfig.chart_data_rule,
                }"
                @update:model-value="overviewStore.setLineModelValue"
              />
              <div class="mt-10 flex justify-between gap-5">
                <BusinessChart
                  v-bind="Pie1ChartProps"
                  :model-value="modModelValue.pie1"
                  :is-loading="isPie1ChartLoading"
                  :data="pie1ChartDataList"
                  :attr-list="pageConfig.pie_attr_list"
                  :metric-list="pageConfig.pie_metric_list"
                  :download-data-rule="pageConfig.download_data_rule"
                  :basic-chart-props="{
                    ...Pie1ChartProps.basicChartProps,
                    regRules: pageConfig.chart_data_rule,
                  }"
                  @update:model-value="overviewStore.setPie1ModelValue"
                />
                <BusinessChart
                  v-bind="Pie2ChartProps"
                  :model-value="modModelValue.pie2"
                  :is-loading="isPie2ChartLoading"
                  :data="pie2ChartDataList"
                  :attr-list="pageConfig.pie_attr_list"
                  :metric-list="pageConfig.pie_metric_list"
                  :download-data-rule="pageConfig.download_data_rule"
                  :basic-chart-props="{
                    ...Pie2ChartProps.basicChartProps,
                    regRules: pageConfig.chart_data_rule,
                  }"
                  @update:model-value="overviewStore.setPie2ModelValue"
                />
              </div>
              <BusinessChart
                class="mt-10"
                v-bind="CampaignChartProps"
                :model-value="modModelValue.campaign_chart"
                :is-loading="isCampaignChartLoading"
                :data="campaignChartDataList"
                :attr-list="pageConfig.campaign_chart_attr_list"
                :metric-list="pageConfig.chart_metric_list"
                :basic-chart-props="{
                  ...CampaignChartProps.basicChartProps,
                  regRules: pageConfig.chart_data_rule,
                }"
                @download="onCampaignChartDownload"
                @update:model-value="overviewStore.setCampaignChartModelValue"
              />
              <t-tabs v-model="tabModelValue" class="mt-10">
                <t-tab-panel v-if="isShowVideo" value="Video" label="Video">
                  <BusinessTable
                    :model-value="modModelValue.video_table"
                    :attr-list="pageConfig.table_attr_list"
                    metrics-type="single"
                    :table-cell-type="TABLE_CELL_TYPE.progress"
                    :metric-list="pageConfig.table_metric_list"
                    :use-table-data-download="true"
                    :is-show-filter="true"
                    :fetch-data-list="(v: any) => overviewStore.fetchVideoTableDataList({
                      isDownload: true,
                      downloadModelValue: v
                    })"
                    :is-loading="isVideoTableLoading"
                    :table-data="{
                      data: videoTableData.list,
                      rowKey: '',
                      count: videoTableData.count,
                    }"
                    :hide-header="true"
                    :table-custom-props="{
                      progressCellCustomProps: {
                        contentWidth: 60,
                      }
                    }"
                    :table-columns-rule="pageConfig.table_columns_rule"
                    :page-size-options="[10, 20, 30, 50, 100, 200]"
                    @update:model-value="overviewStore.setVideoTableModelValue"
                  />
                </t-tab-panel>
                <t-tab-panel v-if="isShowStream" value="Stream" label="Stream">
                  <BusinessTable
                    :model-value="modModelValue.stream_table"
                    :attr-list="pageConfig.table_attr_list"
                    metrics-type="single"
                    :table-cell-type="TABLE_CELL_TYPE.progress"
                    :metric-list="pageConfig.table_metric_list"
                    :use-table-data-download="true"
                    :is-show-filter="true"
                    :fetch-data-list="(v: any) => overviewStore.fetchStreamTableDataList({
                      isDownload: true,
                      downloadModelValue: v
                    })"
                    :is-loading="isStreamTableLoading"
                    :table-data="{
                      data: streamTableData.list,
                      rowKey: '',
                      count: streamTableData.count,
                    }"
                    :hide-header="true"
                    :table-custom-props="{
                      progressCellCustomProps: {
                        contentWidth: 60,
                      }
                    }"
                    :table-columns-rule="pageConfig.table_columns_rule"
                    :page-size-options="[10, 20, 30, 50, 100, 200]"
                    @update:model-value="overviewStore.setStreamTableModelValue"
                  />
                </t-tab-panel>
                <t-tab-panel v-if="isShowShorts" value="Shorts" label="Shorts">
                  <BusinessTable
                    :model-value="modModelValue.shorts_table"
                    :attr-list="pageConfig.table_attr_list"
                    metrics-type="single"
                    :table-cell-type="TABLE_CELL_TYPE.progress"
                    :metric-list="pageConfig.table_metric_list"
                    :use-table-data-download="true"
                    :is-show-filter="true"
                    :fetch-data-list="(v: any) => overviewStore.fetchShortsTableDataList({
                      isDownload: true,
                      downloadModelValue: v
                    })"
                    :is-loading="isShortsTableLoading"
                    :table-data="{
                      data: shortsTableData.list,
                      rowKey: '',
                      count: shortsTableData.count,
                    }"
                    :hide-header="true"
                    :table-custom-props="{
                      progressCellCustomProps: {
                        contentWidth: 60,
                      }
                    }"
                    :table-columns-rule="pageConfig.table_columns_rule"
                    :page-size-options="[10, 20, 30, 50, 100, 200]"
                    @update:model-value="overviewStore.setShortsTableModelValue"
                  />
                </t-tab-panel>
              </t-tabs>
            </div>
          </Teleport>
        </template>
      </CommonView>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import CommonView from 'common/components/Layout/CommonView.vue';
import { SYSTEM, useInfluencerOverviewStore } from '@/store/influencer/overview/overview.store';
import BusinessChart from 'common/components/BusinessChart/index';
import MetricCardSwiper from 'common/components/MetricCardSwiper/index.vue';
import BusinessTable from 'common/components/BusinessTable/index';
import { computed, type ComputedRef, ref, toRaw, unref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useGlobalGameStore } from '@/store/global/game.store';
import { TABLE_CELL_TYPE } from 'common/components/BusinessTable/const';
import { VIEW_ACTION } from 'common/components/NewViewTab/const';
import { useGenFormData } from 'common/compose/form/gen-form-data';
import { unRefObj } from 'common/utils/reactive';
import { groupBy, map, omit, pick } from 'lodash-es';
import { closeTypeMap, resetTypeMap } from 'common/components/FormContainer';
import FullLoading from 'common/components/FullLoading';
import {
  barChartProps,
  CampaignChartProps,
  cardProps,
  LineChartProps,
  Pie1ChartProps,
  Pie2ChartProps,
  shortsCardProps,
  streamsCardProps,
  videosCardProps,
} from './const';
import { getQueryString } from 'common/utils/url';
import { useDownloadFile } from 'common/compose/download-file';
import TemplateHelper from 'common/utils/template/templateHelper';
import { TTabView } from 'common/service/influencer/common/getView';

enum EViewMode {
  DEFAULT = '',
  ONLY_VIEW = 'only_view', // 全屏并隐藏可操作按钮
  FULLSCREEN = 'fullscreen', // 全屏并隐藏视图相关操作
}

const overviewStore = useInfluencerOverviewStore();
const { gameCode } = storeToRefs(useGlobalGameStore());

const {
  tabList,
  currentTab,
  currentTabId,
  formList,
  formatData,
  isIniting,
  isInitError,
  modModelValue,
  barChartDataList,
  lineChartDataList,
  pie1ChartDataList,
  pie2ChartDataList,
  cardDataList,
  videosCardDataList,
  streamsCardDataList,
  shortsCardDataList,
  campaignChartDataList,
  videoTableData,
  streamTableData,
  shortsTableData,

  isCardLoading,
  isBarChartLoading,
  isLineChartLoading,
  isPie1ChartLoading,
  isPie2ChartLoading,
  isVideosCardLoading,
  isStreamsCardLoading,
  isShortsCardLoading,
  isCampaignChartLoading,
  isVideoTableLoading,
  isStreamTableLoading,
  isShortsTableLoading,

  pageConfig,
} = storeToRefs(overviewStore);

const { setFormModelValue } = overviewStore;

const isShowExpandDetails = ref(false);

const tabModelValue = ref<string>('Video');

const isShowVideo = computed(() => formatData.value.length === 0 || formatData.value.includes('Video'));
const isShowStream = computed(() => formatData.value.length === 0 || formatData.value.includes('Stream'));
const isShowShorts = computed(() => formatData.value.length === 0 || formatData.value.includes('Shorts'));
const subCardHeight = computed(() => (formatData.value.length === 0 ? '410px' : `${formatData.value.length * 130 + 20}px`));

watch(
  () => [unref(formatData)],
  () => {
    if (formatData.value.length !== 0 && !formatData.value.includes(tabModelValue.value)) {
      tabModelValue.value = formatData.value[0] as string;
    }
  },
);

const shareParams = computed(() => ({
  param: {
    ...(unRefObj(currentTab?.value?.param) ?? {}),
    ...unRefObj(modModelValue.value ?? {}),
    form: unRefObj(unref(formProps.value?.modelValue)),
  },
  fold_list: toRaw(unref(formProps.value?.foldList)) ?? [],
  game: gameCode.value,
}));

watch(
  () => [unref(gameCode)],
  () => {
    overviewStore.init();
  },
);

// - - - - - - - - - - - 视图全屏功能 start - - - - - - - - - - -
const defaultViewMode = getQueryString('view_mode') ?? EViewMode.DEFAULT;
const viewMode = ref<EViewMode>(defaultViewMode as EViewMode);
// 是否为全屏模式
const isFullScreenMode = computed(() => viewMode.value === EViewMode.FULLSCREEN);
// 是否为only_view模式
const isOnlyViewMode = computed(() => viewMode.value === EViewMode.ONLY_VIEW);
// 自定义icon列表
const customIconList = computed(() => [
  {
    iconName: isFullScreenMode.value ? 'exit-fullscreen-2' : 'fullscreen-2',
    content: isFullScreenMode.value ? 'Exit Fullscreen' : 'Fullscreen',
    clickEvent: () => {
      viewMode.value = viewMode.value === EViewMode.FULLSCREEN ? EViewMode.DEFAULT : EViewMode.FULLSCREEN;
    },
  },
]);
// - - - - - - - - - - - 视图全屏功能 end - - - - - - - - - - -


// 视图事件回调(新增、编辑、删除)
const viewEventCallBack =  (action: keyof typeof VIEW_ACTION, result: TTabView) => {
  console.log('getViewEvent', action, result);
  if (action === VIEW_ACTION.add) {
    tabList.value.push({
      ...result,
      label: result?.name,
      value: result?.id,
    });
    switchTab(result?.id);
  } else if (action === VIEW_ACTION.edit) {
    const index = tabList.value.findIndex((item: TTabView) => item.value === result?.id);
    if (index !== -1) {
      tabList.value[index] = {
        ...result,
        label: result?.name,
        value: result?.id,
      };
    }
    switchTab(result?.id);
  } else if (action === VIEW_ACTION.del) {
    const defaultId = tabList.value[0]?.id;
    const index = tabList.value.findIndex((item: TTabView) => item.value === result?.id);
    if (index !== -1) {
      tabList.value.splice(index, 1);
    }
    switchTab(defaultId);
  }
};

const onFormSubmit = (formData: Record<string, any>) => {
  console.log('onFormSubmit', formData.value);
  setFormModelValue(formData.value);
  overviewStore.fetchAllDataList();
};
const onFormReset = (formData: Record<string, any>) => {
  console.log('onFormReset', formData.value);
  setFormModelValue(formData.value);
  overviewStore.fetchAllDataList();
};

const switchTab = (tabId: string) => {
  console.log('switchTab', tabId);
  currentTabId.value = tabId;
  setFormModelValue(currentTab.value?.param?.form || {});
  modModelValue.value = omit(currentTab.value?.param, 'form');
  overviewStore.fetchAllDataList();
};

const formProps = computed(() => {
  const form = useGenFormData(
    formList.value,
    (formData: Record<string, any>) => onFormSubmit(formData),
    (formData: Record<string, any>) => onFormReset(formData),
    currentTab.value?.fold_list ?? [],
  );
  return {
    ...form,
    closeType: closeTypeMap.toSelfType,
    resetType: resetTypeMap.initial,
  };
});

overviewStore.init();


// - - - - - - - - - - - Media Value By Campaign 组件数据下载 start - - - - - - - - - - -
// 例如: [{ label: 'Network Type', value: 'network_type' }] => { network_type: 'Network Type', ... }
const downloadColumnsKVMap: ComputedRef<Record<string, string>> = computed(() => [
  ...pageConfig.value?.chart_metric_list,
  ...pageConfig.value?.chart_attr_list,
]?.reduce((record, { label, value }) => ({
  ...record,
  date: 'Date', // 回包中的date字段下载时的标题替换为Date
  campaign_name: 'Campaign',
  [value]: label, // 下载时标题字段的映射
}), {}));
// 合并campaignChartDataList的数据
const mergeCampaignChartData = (data: Record<string, any>[], groupByList: string[]) => {
  const groupedData = groupBy(data, item => groupByList.map((key: string) => item[key]).join('-'));
  return map(groupedData, (group) => {
    const combined = group.reduce((acc, item) => ({ ...acc, ...item }), {});
    return { ...pick(group[0], groupByList), ...combined };
  });
};
function onCampaignChartDownload({ value: fileType }: { value: string }) {
  const mergedData = mergeCampaignChartData(unref(campaignChartDataList), ['campaign_name', ...(modModelValue.value?.campaign_chart?.groupby || [])]);
  const templateHelper = new TemplateHelper();
  // 下载时转换回包数据的key
  const downloadData = mergedData?.map((item: Record<string, any>) => {
    const newItem: Record<string, string> = {};
    const downloadDataRuleList = unref(pageConfig.value?.download_data_rule); // 从配置获取的格式化下载数据配置
    // 经过格式化后的数据
    const formatItem = (Array.isArray(downloadDataRuleList) && downloadDataRuleList?.length > 0)
      ? templateHelper.formatData( // 将下载的数据先格式化
        item,
        downloadDataRuleList,
      )
      : item;
    Object.keys(formatItem).forEach((key) => {
      const title = downloadColumnsKVMap.value[key];
      title && (newItem[title] = formatItem[key]);
    });
    return newItem;
  });
  useDownloadFile(downloadData, `table.${fileType}`);
}
// - - - - - - - - - - - Media Value By Campaign 组件数据下载 end - - - - - - - - - - -


</script>

<style lang="scss" scoped>
.card-title {
  margin-top: 20px;
  margin-bottom: 10px;
}
.card-details {
  transition: all 0.3s ease-in-out;
  overflow: hidden;
}

.collapse-btn {
  padding-top: 50px;
  margin-top: -39px;
  position: relative;
  z-index: 1;
}

.fullscreen-content {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #f0f1f6;
  overflow: auto;
  padding-top: 20px;
  z-index: 10;
}

.only-view-content {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #f0f1f6;
  overflow: auto;
  padding: 0 20px;
  padding-top: 20px;
  z-index: 10;
}
</style>
