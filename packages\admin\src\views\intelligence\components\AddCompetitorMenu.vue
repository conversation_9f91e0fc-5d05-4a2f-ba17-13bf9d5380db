<template>
  <div v-if="!props.isLoading">
    <div class="gameWrap border-t border-b border-black-disabled pt-[90px] pb-[80px] flex flex-col items-center">
      <div class="searchBox relative w-[500px] rounded-sm">
        <t-input
          v-model="keyWord"
          class="rounded-sm w-[500px]"
          placeholder="Enter the competitor name"
          @change="(e: any)=>emit('inputChange',e)"
        >
          <template #suffixIcon>
            <div class="flex">
              <SVGIcon
                v-if="keyWord"
                name="clear"
                size="16px"
                color="#8c8888"
                class="cursor-pointer mr-2.5"
                @click="()=>emit('clearKeyWord')"
              />
              <SVGIcon
                name="search-input-1"
                size="16px"
                color="#8c8888"
                class="cursor-pointer"
                @click="()=>emit('clickSearch',keyWord)"
              />
            </div>
          </template>
        </t-input>
        <!-- v-if="keyWorld" -->
        <div
          v-if="keyWord"
          :class="`searchBox absolute  overflow-scroll left-[0px] top-[36px]
           w-[502px] h-[225px] border-2 border-black-disabled bg-white-primary z-40`"
        >
          <t-loading
            class="min-h-[200px] w-[100%]"
            :loading="props.searchStatus == SearchStatus.LOADING"
            size="small"
          >
            <div
              v-if="props.searchStatus == SearchStatus.SUCCESS && props.data!.length > 0"
              class="box min-h-[200px] pt-[27px]"
            >
              <div
                v-for="(item, index) in props.data"
                :key="index"
                :class="`searchItem flex justify-between items-center
                 pt-[5px] pb-[5px] pl-[20px] pr-[20px] hover:bg-[#eee] cursor-pointer`"
                @click="()=>emit('addCompetitor',item, index)"
              >
                <div class="left flex grow shrink items-center w-[0]">
                  <img
                    class="w-[25px] h-[25px]"
                    :src="item.competitor_icon"
                    alt=""
                  >
                  <div class="flex items-center gap-1 max-w-[90%]">
                    <div
                      :class="`name grow shrink ml-[8px] text-xs text-black-primary
                          overflow-hidden overflow-ellipsis whitespace-nowrap`"
                    >
                      <Text
                        overflow
                        tool-tip
                        size="normal"
                        class="font-bold"
                        color="var(--aix-text-color-black-primary)"
                        :content="item.competitor_name"
                      />
                    </div>
                    <SVGIcon
                      v-if="item.competitor_type"
                      :name="item.competitor_type"
                      size="18px"
                      color="#fff"
                      class="flex-none cursor-pointer"
                    />
                  </div>
                </div>
                <div class="right">
                  <SVGIcon
                    name="plus"
                    size="12px"
                    color="#8c8888"
                    class="cursor-pointer"
                    @click="()=>emit('clearKeyWord')"
                  />
                </div>
              </div>
              <!-- <div class="h-[600px]">2</div> -->
            </div>
            <div
              v-if="props.userSearchSelectedStatus &&
                props.searchStatus == SearchStatus.SUCCESS && props.data!.length == 0
              "
              class="box min-h-[200px] flex flex-col items-center justify-center text-xs text-black-primary"
            >
              <p>All items in the selected box have been chosen.</p>
              <p>Please try another keyword or search on other tables for the information you are looking for.</p>
            </div>
            <div
              v-else-if="props.searchStatus == SearchStatus.SUCCESS && props.data!.length == 0
              "
              class="box min-h-[200px] flex flex-col items-center justify-center text-xs text-black-primary"
            >
              <p>No entities found by current keyword</p>
              <p>Please try another keyword or search on other tables for the information you are looking for.</p>
            </div>
          </t-loading>
        </div>
        <div
          v-if="props.searchStatus == SearchStatus.SUCCESS && props.data!.length > 0 && keyWord"
          :class="`tips absolute left-[3px] top-[38px] text-xs text-black-disabled w-[95%]
           pl-[10px] leading-[27px] z-40 bg-[#fff]`"
        >
          A total of {{ props.data!.length }} related filter results
        </div>
      </div>
      <div
        v-if="competitorList!.length > 0"
        class="gameList w-[80%] mt-[65px] flex flex-col mb-[100px]"
      >
        <t-radio-group
          v-if="props.closeAdminTab?false:commonStore.isAdmin"
          v-model="tabName"
          :on-change="(e:any)=>emit('clickRadio',e)"
          default-value="list"
          variant="primary-filled"
        >
          <t-radio-button value="list">List</t-radio-button>
          <t-radio-button value="detail">Detail</t-radio-button>
        </t-radio-group>
        <div
          v-if="tabName === 'list'"
          class="w-[100%] bg-[#fff] mt-[10px] pl-[15px] pr-[15px] pt-[5px] pb-[5px] flex flex-wrap"
        >
          <div
            v-for="item in competitorList"
            :key="item.id"
            class="gameItem group w-[180px] h-[180px] flex flex-col items-center justify-around relative"
          >
            <div class="imgBox w-[100px] h-[100px]">
              <img
                class="w-[100px] h-[100px] display-block"
                :src="item.competitor_icon"
                alt=""
              >
            </div>
            <div
              class="gameName w-[115px] text-center overflow-hidden overflow-ellipsis whitespace-nowrap mb-[10px]"
            >
              {{ item.competitor_name }}
            </div>
            <div
              :class="`mask absolute hidden group-hover:flex left-[0px] top-[0px] w-[100%]
               h-[100%] bg-[rgba(0,0,0,0.7)] cursor-pointer items-center justify-center`"
              @click="()=>emit('deleteCompetitor',item)"
            >
              <SVGIcon
                name="delete"
                size="24px"
                color="#fff"
                class="cursor-pointer"
              />
            </div>
          </div>
        </div>
        <div v-else-if="tabName === 'detail'" class="mt-[15px]">
          <t-table
            row-key="index"
            hover
            bordered
            width="100%"
            table-layout="auto"
            :data="props.originData"
            :columns="competitorTableCol"
          />
        </div>
      </div>
      <div
        v-if="props.competitorList!.length == 0"
        class="noData mt-[40px]"
      >
        <img
          :src="noData"
          alt=""
        >
      </div>
    </div>
    <div v-if="props.showNext" class="fromBox mt-[30px] pl-[25px]">
      <t-button
        theme="primary"
        class="text-[16px]"
        :loading="props.nextStatus === NormalStatus.LOADING"
        @click="emit('clickNext')"
      >
        next
      </t-button>
    </div>
  </div>
  <div v-else class="w-full h-full">
    <FullLoading />
  </div>
</template>
<script setup lang="ts">
import { useIntelligenceCommonStore } from '@/store/intelligence/common.store';
import SVGIcon from 'common/components/SvgIcon';
import FullLoading from 'common/components/FullLoading';
import noData from '@/assets/img/intelligence/creative/creative_add.png';
import Text from 'common/components/Text';
import { SearchStatus, NormalStatus } from '@/store/intelligence/creative/competitor/competitor.const';
import { ICompetitor } from '@/store/intelligence/creative/competitor/competitor';
import { CompetitorMenuProps } from '../const/const';
import { toRef } from 'vue';

const props = defineProps(CompetitorMenuProps);
const competitorList = toRef(props, 'competitorList');
const emit = defineEmits(['clickNext', 'deleteCompetitor', 'addCompetitor', 'inputChange', 'clickSearch', 'clearKeyWord', 'clickRadio']);
const commonStore = useIntelligenceCommonStore();
const tabName = toRef(props, 'tabName');
const competitorTableCol = [
  {
    colKey: 'competitor_name',
    title: 'competitor',
    width: 250,
  },
  {
    colKey: 'store_ids',
    title: 'store_ids',
    cell(h: any, { row }: { row: ICompetitor }) {
      return h('div', { class: 'whitespace-normal' }, row.store_ids.join(', '));
    },
  },
];
const keyWord = toRef(props, 'input');
</script>
<style scoped></style>
