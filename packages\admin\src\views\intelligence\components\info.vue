<template>
  <CommonView
    class="flex-wrap"
    title="Intelligence / Prediction"
    :tab-props="tabProps"
  >
    <template #views>
      <div class="container">
        <div class="subContainer">
          <h3>LTV and Downloads Prediction</h3>
          <div>
            Collect the performance of internal and external competing games after their launch, and predict your
            game's LTV and downloads in the future. After inputting your game's competitors and their similarity and
            competitiveness, the prediction results will come out in about two days.
          </div>
          <!-- v-if="props.mode==='analyze'" -->
          <div
            class="mt-4 text-bold"
          >
            Please contact <span
              style="color:#006eff"
            >fisorpeng</span> to add your game's competitors and
            their similarity and competitiveness
          </div>
          <!-- <Button
            v-else
            class="text-link text-xl mt-14"
            @click="gotoPredictionOverviewSetting"
          >
            In Calculation. Edit >
          </Button> -->
        </div>
        <div class="imgContainer">
          <img :src="worldMapImg" class="aix_img">
          <img :src="barGraphImg" class="aix_img2">
        </div>
      </div>
    </template>
  </CommonView>
</template>
<script lang="ts" setup>
import { useRouter } from 'vue-router';

// ---------------------------- 参数 ------------------------------
// import { useGoto } from '@/router/goto';

// ---------------------------- 组件 ------------------------------
import CommonView from 'common/components/Layout/CommonView.vue';

// ---------------------------- 照片 ------------------------------
import worldMapImg from '@/assets/img/intelligence/overview/start1.png';
import barGraphImg from '@/assets/img/intelligence/overview/start2.png';
// import { InfoProps } from '../const/const';

// const { gotoPredictionOverviewSetting } = useGoto();

// const props = defineProps(InfoProps);

const tabList = [
  { value: '', label: '', game: '', param: {}, hidden: true, type: 'default' },
];

const router = useRouter();
const tabProps = {
  modelValue: '',
  list: tabList,
  showNum: 1,
  shareParams: {},
  hideSaveBtn: true,
  hideShareBtn: true,
  hideShareView: true,
  customIconList: [
    {
      iconName: 'setting',
      clickEvent: () => {
        router.push({
          path: '/intelligence/prediction/settings',
        });
      },
    },
  ],
  class: 'pr-6',
};
</script>
<style lang="scss" scoped>
.cardWrapper {
  position: absolute;
  top: 45%;
  width: 100%;
  transform: translate(0%, -50%);
}
.container {
  width: 1500px;
  min-width: 1500px;
  margin: 150px auto 0;
  height: 477px;
}
.subContainer {
  float: left;
  margin-top: 70px;
  text-align: left;
  width: 651px;
  font-size: 18px;
  line-height: 36px;
  h3 {
    font-size: 32px;
    margin-bottom: 53px;
  }
}
.imgContainer {
  display: flex;
  justify-content: flex-start;
  font-size: 16px;
  float: left;
  position: relative;
  width: 773px;
  margin-left: 54px;
  margin-right: 20px;
  // overflow: hidden;
  .aix_img {
    width: 707px;
    height: 443px;
  }
  .aix_img2 {
    width: 396px;
    height: 150px;
    top: -34px;
    right: 0px;
    position: absolute;
  }
}
</style>
