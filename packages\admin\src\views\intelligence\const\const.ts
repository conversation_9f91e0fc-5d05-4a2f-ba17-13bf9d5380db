import { PropType } from 'vue';

// intelligence components const
export const CompetitorMenuProps = {
  isLoading: {
    type: Boolean,
  },
  input: {
    type: String,
  },
  closeAdminTab: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Array as PropType<{
      competitor_code: string;
      competitor_icon: string;
      competitor_name: string;
      competitor_type: string;
      create_time: string;
      dashboard: number;
    }[]>,
  },
  competitorList: {
    type: Array as PropType<
    {
      id: number;
      competitor_code: string;
      competitor_icon: string;
      competitor_name: string;
      competitor_svg?: string;
      create_time: string;
      dashboard: number;
      store_ids: string[];
    }[]>,
  },
  originData: {
    type: Array as PropType<
    {
      id: number;
      competitor_code: string;
      competitor_icon: string;
      competitor_name: string;
      competitor_svg?: string;
      create_time: string;
      dashboard: number;
      store_ids: string[];
    }[]>,
  },
  searchStatus: {
    type: Number,
  },
  userSearchSelectedStatus: {
    type: Boolean,
    default: false,
  },
  showNext: {
    type: Boolean,
    default: true,
  },
  nextStatus: {
    type: Number,
  },
  tabName: {
    type: String,
  },
};

export const InfoProps = {
  mode: {
    type: String as PropType<'analyze' | 'overview'>,
    default: 'overview',
  },
};
