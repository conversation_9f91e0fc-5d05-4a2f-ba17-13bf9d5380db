<template>
  <CommonView
    :need-back="true"
    :router-index="-1"
    :store="addCompetitorStore"
    hide-right
  >
    <template #views>
      <CompetitorMenu
        :is-loading="isLoadingCompetitorList"
        :input="keyWorld"
        :data="searchList"
        :competitor-list="addCompetitorStore.competitorList"
        :origin-data="addCompetitorStore.originData"
        :search-status="addCompetitorStore.searchStatus"
        :next-status="addCompetitorStore.nextStatus"
        :tab-name="tabName"
        @click-next="addCompetitorStore.nextHandle"
        @delete-competitor="delCompetitor"
        @add-competitor="addCompetitor"
        @input-change="onChange"
        @click-radio="clickRadio"
        @click-search="onChange"
        @clear-key-word="clearKeyWorld"
      />
      <t-dialog
        v-model:visible="visible"
        theme="warning"
        header=""
        body="The maximum number of competitors is 30."
        :on-close="closeWarn"
        :cancel-btn="null"
        :confirm-btn="null"
      />
      <!-- next 之后的弹窗(添加的竞品有不在我们的数据库后的弹窗) -->
      <t-dialog
        v-model:visible="addCompetitorStore.newAddBigVisible"
        :close-btn="false"
        width="800px"
        header="Tips"
        :close-on-overlay-click="false"
        :close-on-esc-keydown="false"
      >
        <div>
          <div class="description mb-[20px]">
            The following games there is no data currently, and it will take <span style="color: red">10mins</span>
            to get creatives, please wait and refresh later.
          </div>
          <div class="list last: div border-b border-black-primary leading-8 text-[#000]">
            <div class="item border border-black-primary border-b-0 font-bold">Competitor Name</div>
            <div
              v-for="(item, index) in addCompetitorStore.noDbBigList"
              :key="index"
              class="item border border-black-primary border-b-0"
            >
              {{ item.competitor_name }}
            </div>
          </div>
        </div>
        <template #cancelBtn>
          <t-button
            v-if="!isLoadingNewAdd"
            variant="outline"
            theme="default"
            @click="onCancel"
          >
            Return
          </t-button>
        </template>
        <template #confirmBtn>
          <t-button :loading="isLoadingNewAdd" @click="onConfirm">Confirm</t-button>
        </template>
      </t-dialog>
    </template>
  </CommonView>
</template>
<script setup lang="tsx">
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import CommonView from 'common/components/Layout/CommonView.vue';
// import { useRouter } from 'vue-router';
import  CompetitorMenu  from '../../components/AddCompetitorMenu.vue';
import { useIntelligenceCreativeAddCompetitorStore } from '@/store/intelligence/creative/competitor/add-competitor.store';
import { useIntelligenceCommonStore } from '@/store/intelligence/common.store';
import { IAddCompetitor, ICompetitor } from '@/store/intelligence/creative/competitor/competitor.d';
// import { getGameCompetitor } from 'common/service/intelligence/creative/get-game-competitor';
// console.log(process.env.ooms_backend_target);
const keyWorld = ref<string>('');
const visible = ref<boolean>(false);
const tabName = ref<string>('list');
const addCompetitorStore = useIntelligenceCreativeAddCompetitorStore();
const commonStore = useIntelligenceCommonStore();
commonStore.init();
const { isLoadingCompetitorList, searchList, isLoadingNewAdd } = storeToRefs(addCompetitorStore);
// const suffixIcon = ref(() => h(SVGIcon, { name: 'search-input-1', height: '16px', width: '16px' }));
// const router = useRouter();
// const jumpIndexCompetitorPage = () => {
//   router.push({ path: '/intelligence/creative/competitor' });
// };
const clearKeyWorld = () => {
  keyWorld.value = '';
  addCompetitorStore.searchList = [];
};
const onChange = (val: string) => {
  keyWorld.value = val;
  addCompetitorStore.antiShake({ k: val });
};

const clickRadio = (val: 'list' |'detail') => {
  tabName.value = val;
};

const addCompetitor = (val: IAddCompetitor, index: number) => {
  if (addCompetitorStore.competitorList.length >= 30) {
    // 限制30个大包
    openWan();
    return;
  }
  addCompetitorStore.listPush(val);
  addCompetitorStore.delSearchItemByIndex(index);
};
const delCompetitor = (val: ICompetitor) => {
  addCompetitorStore.listSplice(val.competitor_code);
};
const closeWarn = () => {
  // 关闭超过30大包的警告
  visible.value = false;
};
const openWan = () => {
  visible.value = true;
};
const onCancel = () => {
  addCompetitorStore.newAddBigVisible = false;
  // jumpIndexCompetitorPage();
};
const onConfirm = async () => {
  await addCompetitorStore.onConfirm();
};
</script>
<style scoped></style>
