<template>
  <CommonView
    :router-index="-1"
    :store="cStore"
  >
    <template #subTitle>
      <div
        class="right flex justify-between items-center cursor-pointer"
        @click="jumpAddCompetitorPage()"
      >
        <div>
          <SvgIcon
            name="plus"
            size="12px"
            color="#4981f2"
            class="mr-[10px] cursor-pointer"
          />
        </div>
        <div class="text-brand font-medium">{{ 'Add Competitor Game' }}</div>
      </div>
    </template>
    <template #views>
      <div v-if="competitorListStatus === NormalStatus.LOADING || competitorList.length > 0">
        <div class="flex">
          <div
            v-if="competitorListStatus === NormalStatus.LOADING"
            class="w-[100%] h-[90px] rounded-large overflow-hidden"
          >
            <FullLoading
              class="relative"
            />
          </div>
          <div class="competitorList max-w-[100%]">
            <SwiperContainers
              v-if="competitorListStatus === NormalStatus.SUCCESS"
              class="max-w-[100%] h-[100%]"
              :list="competitorList"
            >
              <template #default="props: any">
                <div
                  class="card p-[16px] w-[130px] flex flex-col items-center justify-center"
                  :class="cStore.activeIndex === props.index ? 'card-active' : ''"
                  @click="cStore.changeActiveIndex(props.index)"
                >
                  <t-tooltip :content="props.data?.competitor_name" placement="bottom">
                    <div class="w-[100%] flex flex-col items-center justify-center">
                      <div class="imgBox w-[40px] h-[40px]">
                        <img
                          class="w-[40px] h-[40px] rounded-[8px] exclude-mode"
                          :src="props.data?.competitor_icon"
                          alt=""
                        >
                      </div>
                      <div class="max-w-[100%]">
                        <div class="name max-w-[100%] text-center overflow-hidden overflow-ellipsis whitespace-nowrap">
                          {{ props.data?.competitor_name }}
                        </div>
                      </div>
                    </div>
                  </t-tooltip>
                </div>
              </template>
            </SwiperContainers>
          </div>
        </div>
        <div
          v-if="activeCompetitor && activeCompetitor.competitor_type === CompetitorType.MOBILE"
          class="dataBox w-[100%] p-[16px] list-container1 mt-[24px]"
        >
          <FullLoading
            v-if="competitorListStatus === NormalStatus.LOADING"
            class="relative min-h-[1000px]"
            size="medium"
          />
          <div
            v-else
            class="w-[100%]"
          >
            <t-collapse
              :value="collapseValue"
              :default-value="collapseValue"
              :expand-icon-placement="'right'"
              :expand-on-row-click="!false"
              @change="cStore.changeCollapse"
            >
              <t-collapse-panel
                value="dailyLaset"
              >
                <template #header>
                  <div class="title pl-[20px] text-lg font-bold leading-10">
                    <span class="mr-[10px]">
                      UA - Daily Latest ({{ getYestoday('YYYY/MM/DD') }})
                    </span>
                    <t-tooltip content="Newly delivered creatives from the previous day">
                      <InfoCircleIcon size="18px" />
                    </t-tooltip>
                  </div>
                </template>
                <template #default>
                  <div class="my-pannal pt-[16px]">
                    <Container
                      v-if="cStore.dayList.length > 0 && dayWeekLoading === NormalStatus.SUCCESS"
                      :list="cStore.dayList"
                      :show-detail="showDetailDay"
                    />
                    <FullLoading
                      v-if="dayWeekLoading === NormalStatus.LOADING"
                      class="relative min-h-[400px]"
                    />
                    <div
                      v-if="cStore.dayList.length === 0 && dayWeekLoading === NormalStatus.SUCCESS"
                      class="nodata w-[100%] min-h-[400px] flex flex-col justify-center items-center"
                    >
                      <DataEmpty>
                        <template #content><span class="text-black-primary opacity-60">No Data</span></template>
                      </DataEmpty>
                      <div>
                        <span>There is no new creatives currently, please see</span>
                        <span
                          class="ml-[10px] text-brand cursor-pointer"
                          @click="jump()"
                        >More >></span>
                      </div>
                    </div>
                  </div>
                </template>
              </t-collapse-panel>
            </t-collapse>
            <div class="moreBox flex justify-end pr-[20px]">
              <div
                class="more text-[20px] text-brand leading-10 cursor-pointer"
                @click="jump()"
              >
                More>>
              </div>
            </div>
          </div>
        </div>

        <div
          v-if="activeCompetitor && activeCompetitor.competitor_type === CompetitorType.MOBILE"
          class="dataBox w-[100%] p-[16px] list-container1 mt-[24px]"
        >
          <FullLoading
            v-if="competitorListStatus === NormalStatus.LOADING"
            class="relative min-h-[1000px]"
            size="medium"
          />
          <div
            v-else
            class="w-[100%]"
          >
            <t-collapse
              :value="collapseValue"
              :default-value="collapseValue"
              :expand-icon-placement="'right'"
              :expand-on-row-click="!false"
              @change="cStore.changeCollapse"
            >
              <t-collapse-panel
                value="weekHot"
              >
                <template #header>
                  <div class="title pl-[20px] text-lg font-bold leading-10">
                    <span class="mr-[10px]">
                      UA - Weekly Hot ({{ lastWeekRange.startStr }} - {{ lastWeekRange.endStr }})
                    </span>
                    <t-tooltip
                      :content="`Sorted according to the calculation results of net value-added of impressions,
                   the top 20 popular creatives that performed better in the previous week`"
                    >
                      <InfoCircleIcon size="18px" />
                    </t-tooltip>
                  </div>
                </template>
                <template #default>
                  <div class="my-pannal pt-[16px]">
                    <Container
                      v-if="cStore.weekList.length > 0 && dayWeekLoading === NormalStatus.SUCCESS"
                      :list="cStore.weekList"
                      :show-detail="showDetailWeek"
                    />
                    <FullLoading
                      v-if="dayWeekLoading === NormalStatus.LOADING"
                      class="relative min-h-[400px]"
                    />
                    <div
                      v-if="cStore.weekList.length === 0 && dayWeekLoading === NormalStatus.SUCCESS"
                      class="nodata min-h-[400px] flex flex-col justify-center items-center"
                    >
                      <DataEmpty>
                        <template #content><span class="text-black-primary opacity-60">No Data</span></template>
                      </DataEmpty>
                      <div>
                        <span>There is no hot creatives currently, please see</span>
                        <span
                          class="ml-[10px] text-brand cursor-pointer"
                          @click="jump()"
                        >More >></span>
                      </div>
                    </div>
                  </div>
                </template>
              </t-collapse-panel>
            </t-collapse>
            <div class="moreBox flex justify-end pr-[20px]">
              <div
                class="more text-[20px] text-brand leading-10 cursor-pointer"
                @click="jump()"
              >
                More>>
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="activeCompetitor && activeCompetitor.competitor_type === CompetitorType.PC"
          class="dataBox w-[100%] p-[16px] list-container1 mt-[24px]"
        >
          <FullLoading
            v-if="competitorListStatus === NormalStatus.LOADING"
            class="relative min-h-[1000px]"
            size="medium"
          />
          <div
            v-else
            class="w-[100%]"
          >
            <t-collapse
              :value="collapseValue"
              :default-value="collapseValue"
              :expand-icon-placement="'right'"
              :expand-on-row-click="!false"
              @change="cStore.changeCollapse"
            >
              <t-collapse-panel
                value="uaCreative"
              >
                <template #header>
                  <div class="title pl-[20px] text-lg font-bold leading-10">
                    <span class="mr-[10px]">
                      UA - Creative
                    </span>
                  <!-- <t-tooltip
                    :content="`Sorted according to the calculation results of net value-added of impressions,
                   the top 20 popular creatives that performed better in the previous week`"
                  >
                    <InfoCircleIcon size="18px" />
                  </t-tooltip> -->
                  </div>
                </template>
                <template #default>
                  <div class="my-pannal pt-[16px]">
                    <Container
                      v-if="uaCreativeList.length > 0 && !isLoadingUaCreativeList"
                      :list="uaCreativeList"
                      :show-detail="showDetailUaCreative"
                    />
                    <FullLoading
                      v-if="isLoadingUaCreativeList"
                      class="relative min-h-[400px]"
                    />
                    <div
                      v-if="uaCreativeList.length === 0 && !isLoadingUaCreativeList"
                      class="nodata min-h-[400px] flex flex-col justify-center items-center"
                    >
                      <DataEmpty>
                        <template #content><span class="text-black-primary opacity-60">No Data</span></template>
                      </DataEmpty>
                      <div>
                        <span>There is no creatives currently, please see</span>
                        <span
                          class="ml-[10px] text-brand cursor-pointer"
                          @click="jump()"
                        >More >></span>
                      </div>
                    </div>
                  </div>
                </template>
              </t-collapse-panel>
            </t-collapse>
            <div class="moreBox flex justify-end pr-[20px]">
              <div
                class="more text-[20px] text-brand leading-10 cursor-pointer"
                @click="jump()"
              >
                More>>
              </div>
            </div>
          </div>
        </div>
        <div class="dataBox w-[100%] p-[16px] list-container1 mt-[24px]">
          <FullLoading
            v-if="competitorListStatus === NormalStatus.LOADING"
            class="relative min-h-[1000px]"
            size="medium"
          />
          <div
            v-else
            class="w-[100%]"
          >
            <t-collapse
              :value="collapseValue"
              :default-value="collapseValue"
              :expand-icon-placement="'right'"
              :expand-on-row-click="!false"
              @change="cStore.changeCollapse"
            >
              <t-collapse-panel
                value="socialMedia"
              >
                <template #header>
                  <div class="title pl-[20px] text-lg font-bold leading-10">
                    <span class="mr-[10px]">
                      Social Media - Video ({{ lastWeekRange.startStr }} - {{ yestodayRange.endStr }})
                    </span>
                  <!-- <t-tooltip
                    :content="`Sorted according to the calculation results of net value-added of impressions,
                   the top 20 popular creatives that performed better in the previous week`"
                  >
                    <InfoCircleIcon size="18px" />
                  </t-tooltip> -->
                  </div>
                </template>
                <template #default>
                  <div class="my-pannal pt-[16px]">
                    <Container
                      v-if="smvList.length > 0 && !isLoadingSmvList"
                      :list="smvList"
                    >
                      <template #item="{data, index}">
                        <CompetitorItem
                          :competitor-item="data"
                          :index="index"
                          :show-detail="showDetailSocialMediaVide"
                        >
                          <template #label>
                            <slot name="label">{{ data.smv_view_count }}</slot>
                          </template>
                          <template #titleRight>
                            <img
                              class="w-[20px] h-[20px] rounded-game-icon"
                              :src="getImgByCdn(getDatabrainChannelImg(data.smv_channel || ''))"
                              alt=""
                            >
                          </template>
                          <template #body>
                            <div class="w-[100%] text-right">{{ data.smv_comment_time || '' }}</div>
                          </template>
                          <template #firstAnylaze>
                            <div class="analyzeItem w-[100%] relative">
                              <div class="num text-base text-center font-bold">{{ data.smv_view_count }}</div>
                              <div class="text-center text-xs text-[#666]">Views</div>
                              <div
                                :class="`shu absolute left-[0px] h-[24px] border-r-[1px]
                                 border-[#eee] top-[50%] translate-y-[-50%]`"
                              />
                            </div>
                          </template>
                        </CompetitorItem>
                      </template>
                    </Container>
                    <FullLoading
                      v-if="isLoadingSmvList"
                      class="relative min-h-[400px]"
                    />
                    <div
                      v-if="smvList.length === 0 && !isLoadingSmvList"
                      class="nodata min-h-[400px] flex flex-col justify-center items-center"
                    >
                      <DataEmpty>
                        <template #content><span class="text-black-primary opacity-60">No Data</span></template>
                      </DataEmpty>
                      <div>
                        <span>There is no creatives currently, please see</span>
                        <span
                          class="ml-[10px] text-brand cursor-pointer"
                          @click="jump(socialMeidaVideoPath, 'socialmeidavideo')"
                        >More >></span>
                      </div>
                    </div>
                  </div>
                </template>
              </t-collapse-panel>
            </t-collapse>
            <div class="moreBox flex justify-end pr-[20px]">
              <div
                class="more text-[20px] text-brand leading-10 cursor-pointer"
                @click="jump(socialMeidaVideoPath, 'socialmeidavideo')"
              >
                More>>
              </div>
            </div>
          </div>
        </div>
      </div>
      <NoCompetitor v-else />
      <CompetitorItemDetail
        v-if="cStore.dayList.length >= 0"
        :key="`one`"
        ref="competitorItemDetailDayRef"
        :creative-list="cStore.dayList"
        :creative-item="creativeDayItem"
        :creative-active-index="creativeActiveDayIndex"
        @change-active-index="changeActiveIndexDay"
      />
      <CompetitorItemDetail
        v-if="cStore.weekList.length >= 0"
        :key="`two`"
        ref="competitorItemDetailWeekRef"
        :creative-list="cStore.weekList"
        :creative-item="creativeWeekItem"
        :creative-active-index="creativeActiveWeekIndex"
        @change-active-index="changeActiveIndexWeek"
      />
      <CompetitorItemDetail
        v-if="uaCreativeList.length >= 0"
        :key="`three`"
        ref="competitorItemDetailUaCreativeRef"
        :creative-list="uaCreativeList"
        :creative-item="uaCreativeItem"
        :creative-active-index="uaCreativeActiveIndex"
        @change-active-index="changeActiveIndexUaCreative"
      />
    </template>
  </CommonView>
</template>
<script setup lang="tsx">
import CommonView from 'common/components/Layout/CommonView.vue';
import SwiperContainers from 'common/components/SwiperContainers/index.vue';
import SvgIcon from 'common/components/SvgIcon';
import { InfoCircleIcon } from 'tdesign-icons-vue-next';
import FullLoading from 'common/components/FullLoading';
import DataEmpty from '@/components/nullable/DataEmpty.vue';
import Container from '../components/Container/Index.vue';
import CompetitorItem from '../components/CompetitorItem/Index.vue';
import NoCompetitor from './NoCompetitor.vue';
import { ref, watch, nextTick, unref, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';
import { useRouterStore } from '@/store/global/router.store';
import { useIntelligenceCreativeCompetitorStore } from '@/store/intelligence/creative/competitor/index-competitor.store';
import { NormalStatus, CreateItemDefault, CompetitorType } from '@/store/intelligence/creative/competitor/competitor.const';
import {
  getLastWeekRange, getReleaseTimeForPc, getYestoday, getYestodayRange,
  getImgByCdn, getDatabrainChannelImg,
} from '@/store/intelligence/creative/competitor/untis';
import { useIntelligenceJumpRoute } from '@/store/intelligence/creative/common/jumpRoute';
import CompetitorItemDetail from '../../creative/components/CompetitorItemDetail/Index.vue';
import { ICreativeItem } from '@/store/intelligence/creative/competitor/competitor.d';
import { cloneDeep } from 'lodash-es';
// import { useGlobalGameStore } from '@/store/global/game.store';
const competitorItemDetailDayRef = ref();
const competitorItemDetailWeekRef = ref();
const competitorItemDetailUaCreativeRef = ref();
const lastWeekRange = getLastWeekRange('YYYY/MM/DD');
const yestodayRange = getYestodayRange('YYYY/MM/DD');
const router = useRouter();
const routerStore = useRouterStore();
const { jumpAddCompetitorPage, jumpOverviewCreativeGalleryPage } = useIntelligenceJumpRoute();
// const gameStore = useGlobalGameStore();
const cStore = useIntelligenceCreativeCompetitorStore();
const {
  competitorList, competitorListStatus, dayWeekLoading, isLoadingUaCreativeList, uaCreativeList,
  activeCompetitor, collapseValue, smvList, isLoadingSmvList, curGameName,
} = storeToRefs(cStore);
const creativeDayItem = ref<ICreativeItem>(CreateItemDefault);
const creativeActiveDayIndex = ref<number>(-1);
const creativeWeekItem = ref<ICreativeItem>(CreateItemDefault);
const creativeActiveWeekIndex = ref<number>(-1);
const uaCreativeItem = ref<ICreativeItem>(CreateItemDefault);
const uaCreativeActiveIndex = ref<number>(-1);

// ------ 社媒 ------
const socialMeidaVideoPath = computed(() => {
  if (routerStore.isIntelligenceRouteModule()) {
    return '/intelligence/creative/socialmeidavideo';
  }
  return '/creative/competitor/socialmeidavideo';
});

watch(
  creativeActiveDayIndex,
  () => {
    if (cStore.dayList.length > 0 && creativeActiveDayIndex.value >= 0) {
      creativeDayItem.value = cloneDeep(cStore.dayList[creativeActiveDayIndex.value]);
    }
  },
  {
    deep: true,
    immediate: true,
  },
);
watch(
  cStore.dayList,
  () => {
    if (cStore.dayList.length > 0 && creativeActiveDayIndex.value >= 0) {
      creativeDayItem.value = cloneDeep(cStore.dayList[creativeActiveDayIndex.value]);
    }
  },
  {
    deep: true,
    immediate: true,
  },
);
watch(
  creativeActiveWeekIndex,
  () => {
    if (cStore.weekList.length > 0 && creativeActiveWeekIndex.value >= 0) {
      creativeWeekItem.value = cloneDeep(cStore.weekList[creativeActiveWeekIndex.value]);
    }
  },
  {
    deep: true,
    immediate: true,
  },
);
watch(
  cStore.weekList,
  () => {
    if (cStore.weekList.length > 0 && creativeActiveWeekIndex.value >= 0) {
      creativeWeekItem.value = cloneDeep(cStore.weekList[creativeActiveWeekIndex.value]);
    }
  },
  {
    deep: true,
    immediate: true,
  },
);
// ua-creative watch
watch(
  uaCreativeActiveIndex,
  () => {
    if (uaCreativeList.value.length > 0 && uaCreativeActiveIndex.value >= 0) {
      uaCreativeItem.value = cloneDeep(uaCreativeList.value[uaCreativeActiveIndex.value]);
    }
  },
  {
    deep: true,
    immediate: true,
  },
);
watch(
  uaCreativeList,
  () => {
    if (uaCreativeList.value.length > 0 && uaCreativeActiveIndex.value >= 0) {
      uaCreativeItem.value = cloneDeep(uaCreativeList.value[uaCreativeActiveIndex.value]);
    }
  },
  {
    deep: true,
    immediate: true,
  },
);
const jump = (path = '', moduleName = '') => {
  const activeCompetitorCode = activeCompetitor.value?.competitor_code;
  const competitorType = activeCompetitor.value?.competitor_type;
  const timeRange = getReleaseTimeForPc(activeCompetitor.value?.release_time, 'YYYY-MM-DD');
  const queryRaw: Record<string, string> = {
    activeCompetitorCode: activeCompetitorCode || '',
  };
  if (moduleName === 'socialmeidavideo') {
    queryRaw.search_text = unref(curGameName);
    queryRaw.start_time = lastWeekRange.startStr;
    queryRaw.end_time = yestodayRange.endStr;
  }
  if (competitorType === CompetitorType.PC) {
    queryRaw.startTime = timeRange.startStr;
    queryRaw.endTime = timeRange.endStr;
  };
  if (path) {
    router.push({
      path,
      query: queryRaw,
    });
  } else {
    jumpOverviewCreativeGalleryPage(queryRaw);
  }
};
const showDetailDay = (index: number) => {
  // 展示详情
  changeActiveIndexDay(-1);
  changeActiveIndexDay(index);
  nextTick(() => {
    creativeWeekItem.value = cloneDeep(cStore.dayList[creativeActiveDayIndex.value]);
    competitorItemDetailDayRef.value.show();
  });
};
const showDetailWeek = (index: number) => {
  // 展示详情
  changeActiveIndexWeek(-1);
  changeActiveIndexWeek(index);
  creativeWeekItem.value = cloneDeep(cStore.weekList[creativeActiveWeekIndex.value]);
  competitorItemDetailWeekRef.value.show();
};
const showDetailUaCreative = (index: number) => {
  // 展示详情
  changeActiveIndexUaCreative(-1);
  changeActiveIndexUaCreative(index);
  uaCreativeItem.value = cloneDeep(uaCreativeList.value[uaCreativeActiveIndex.value]);
  competitorItemDetailUaCreativeRef.value.show();
};
const showDetailSocialMediaVide = (index: number) => {
  if (smvList.value[index]) {
    window.open(smvList.value[index]?.smv_content_url);
  };
};
const changeActiveIndexDay = (index: number) => {
  creativeActiveDayIndex.value = index;
};
const changeActiveIndexWeek = (index: number) => {
  creativeActiveWeekIndex.value = index;
};
const changeActiveIndexUaCreative = (index: number) => {
  uaCreativeActiveIndex.value = index;
};
</script>
<style lang="scss" scoped>
.list-container1 {
  background: #fff;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.03);
  border-radius: 12px;
  display: flex;
  overflow: hidden;
}
.card {
  @apply bg-white-primary rounded-large overflow-hidden cursor-pointer;
  border: 2px solid transparent;
  &:hover {
    border: 2px solid var(--aix-border-color-gray-placeholder);
    box-shadow: 0 10px 10px rgba(6, 40, 118, 0.03);
  }
  &.card-active {
    border: 2px solid var(--aix-border-color-brand);
  }
}

:deep(.dataBox .t-collapse){
  border: none;
}
:deep(.dataBox .t-collapse-panel__wrapper .t-collapse-panel__header){
  padding: 0;
}
:deep(.dataBox .t-collapse-panel__wrapper .t-collapse-panel__body){
  background: transparent !important;;
}
:deep(.dataBox .t-collapse-panel__wrapper .t-collapse-panel__content){
  padding: 0 !important;
}
</style>
