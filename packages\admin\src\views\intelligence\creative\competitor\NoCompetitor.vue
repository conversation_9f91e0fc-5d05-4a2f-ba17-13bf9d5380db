<template>
  <div>
    <!-- <div class="p-[10px] bg-white mb-[20px] rounded-large">
      <div class="font-medium">Game: None</div>
    </div> -->
    <div class="flex content pt-[80px] pl-[50px] pr-[25px] pb-[100px] rounded-large">
      <div class="left pr-[50px]">
        <h4 class="text-xl font-semibold leading-10 mb-[20px]">Ads Creatives Insights</h4>
        <p class="description text-black-primary text-lg leading-8">
          Monitor the top ads creative of the competitors. Creatives are
        </p>
        <p class="description text-black-primary text-lg leading-8">
          automatically annotated with computer vision technology to
        </p>
        <p class="description text-black-primary text-lg leading-8">accelerate your advertising analytics</p>
        <div class="btnBox flex">
          <t-button
            class="mt-[40px]"
            @click="jump"
          >
            <template #icon>
              <SvgIcon
                :name="'plus'"
                size="12px"
                color="#fff"
                class="mr-[10px]"
              />
            </template>
            Add Competitor Game
          </t-button>
        </div>
      </div>
      <div class="right">
        <div class="imgBox mt-[60px] relative">
          <img
            :src="noCompetitorOne"
            alt=""
            class="w-[700px] exclude-mode"
          >
          <img
            :src="noCompetitorTwo"
            alt=""
            class="absolute right-[-70px] top-[-60px] exclude-mode"
          >
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="tsx">
import SvgIcon from 'common/components/SvgIcon';
import noCompetitorOne from '@/assets/img/intelligence/creative/no_competitor_01.png';
import noCompetitorTwo from '@/assets/img/intelligence/creative/no_competitor_02.png';
import { useRouter } from 'vue-router';
import { useRouterStore } from '@/store/global/router.store';
const router = useRouter();
const routerStore = useRouterStore();
const jump = () => {
  if (routerStore.isIntelligenceRouteModule()) {
    router.push({ path: '/intelligence/creative/competitor/addcompetitor' });
  } else {
    router.push({ path: '/creative/competitor/competitor/addcompetitor' });
  };
};
// const jumpAddCompetitorPage = () => {
//   router.push({ path: '/intelligence/creative/competitor/addcompetitor' });
// };
</script>
