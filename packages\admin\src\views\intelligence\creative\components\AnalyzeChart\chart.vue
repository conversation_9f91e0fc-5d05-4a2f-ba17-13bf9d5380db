<template>
  <t-space
    direction="vertical"
    class="bg-white-primary rounded-large p-[16px] overflow-y-auto flex flex-col gap-y-[16px] mb-6"
  >
    <div class="module_one rounded min-h-[450px] relative">
      <FullLoading v-if="props.isLoading" />
      <div
        v-else-if="props.data.length === 0"
        class="min-h-[450px] flex justify-center items-center"
      >
        <DataEmpty />
      </div>
      <BasicChart
        v-else
        :chart-type="props.chartType"
        detail-type="ringTextOut"
        :data-mode="props.chartType === BAR_CHART ? DataMode.y : DataMode.x"
        :x-axis-label-format="numberAxisLabelFormat"
        :y-axis-label-format="nameAxisLabelFormat"
        data-value-filed="value"
        data-item-field="name"
        is-show-legend
        :is-show-x-axis="props.chartType === BAR_CHART ? false : true"
        :is-show-series="false"
        :data="props.data"
        :y-axis="props.chartType === BAR_CHART ? [
          {
            type: 'category',
            data: props.data.map(item => item.name),
            inverse: true,
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              formatter: nameAxisLabelFormat,
            },
          },
          {
            type: 'category',
            data: props.data.map(item => item.value),
            inverse: true,
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              formatter: numberAxisLabelFormat,
            },
          },
        ] : []"
        :series="props.chartType === BAR_CHART ?
          [
            {
              showBackground: true,
              backgroundStyle: {
                color: '#ddd',
              },
              itemStyle: {
                color: '#376ae7',
              },
              barMaxWidth: 20,
              data: props.data,
              type: 'bar',
            },
          ] : [
            {
              label: {
                alignTo: 'edge',
                formatter: (params: any) =>
                  `${nameAxisLabelFormat(params.name)}:${numberAxisLabelFormat(params.value)}`,
                minMargin: 5,
                edgeDistance: '8%',
                lineHei8ght: 14,
                rich: {},
              },
              lebelLine: {
                length: 10,
                length2: 0,
                maxSurfaceAngle: 80,
              },
              itemStyle: {
                borderColor: '#fff',
                borderWidth: 1,
              },
              radius: '45%',
              selectedOffset: 50,
              data: props.data,
              type: 'pie',
            },
          ]"
        :reg-rules="[
          {
            name: 'value',
            value: [
              's1000',
              'decimal',
            ],
          },
        ]"
      />
    </div>
  </t-space>
</template>
<script setup lang="ts">
import { useIntelligenceCreativeOverviewStore } from '@/store/intelligence/creative/overview/index-overview.store';
import { defineAsyncComponent } from 'vue';

// ---------------------------- 参数 ------------------------------
import { DataMode, IData } from 'common/components/BasicChart/type.d';
import { BAR_CHART, PIE_CHART } from '@/store/intelligence/creative/analyze/analyze.const';

// ---------------------------- 组件 ------------------------------
import FullLoading from 'common/components/FullLoading';
import DataEmpty from '@/components/nullable/DataEmpty.vue';

const props = defineProps({
  chartType: {
    type: String,
    default: () => BAR_CHART,
    validator: value => value === BAR_CHART || value === PIE_CHART,
  },
  data: {
    type: Array as () => IData[],
    default: () => [],
  },
  isLoading: {
    type: Boolean,
    default: true,
  },
});

// Const
const BasicChart = defineAsyncComponent(() => import('common/components/BasicChart'));
const store = useIntelligenceCreativeOverviewStore();
const { nameAxisLabelFormat, numberAxisLabelFormat } = store;
</script>
<style scoped lang="scss"></style>
