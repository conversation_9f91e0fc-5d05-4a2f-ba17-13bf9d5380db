<template>
  <t-space
    direction="vertical"
    class="bg-white-primary w-full rounded-large p-[16px] overflow-y-auto flex gap-y-[16px] mb-6"
  >
    <div class="flex gap-6 w-full justify-between">
      <div class="flex gap-3">
        <Select
          v-model="leftOptions"
          title=""
          :list="props.leftOptionsList as CommonOptionsItemType[]"
          :disabled="props.isLoading"
          :multiple="false"
          @update:model-value="(newValue: any) => (leftOptions = newValue)"
        />
        <Text
          class="flex items-center font-bold"
          type="navTitle"
          content="Attribute By"
        />
        <Select
          v-model="rightOptions"
          :disabled="props.isLoading"
          title=""
          :list="props.rightOptionsList as CommonOptionsItemType[]"
          :multiple="false"
          @update:model-value="(newValue: any) => (rightOptions = newValue)"
        />
      </div>
      <div class="flex gap-1 ">
        <t-button
          :disabled="props.isLoading"
          variant="text"
          :class="{ 'is-selected': selectedChart === BAR_CHART }"
          @click="selectedChart = BAR_CHART"
        >
          <icon name="chart-bar" />
        </t-button>
        <t-button
          :disabled="props.isLoading"
          variant="text"
          :class="{ 'is-selected': selectedChart === PIE_CHART }"
          @click="selectedChart = PIE_CHART"
        >
          <icon name="chart-pie" />
          <!-- <icon name="pie" /> -->
        </t-button>
        <t-button
          :disabled="props.isLoading"
          variant="text"
          @click="downloadExcel"
        >
          <icon name="download" />
        </t-button>
      </div>
    </div>
    <chart
      :chart-type="selectedChart"
      :data="formattedData"
      :is-loading="props.isLoading"
    />
  </t-space>
</template>
<script lang="ts" setup>
import { useIntelligenceCreativeOverviewStore } from '@/store/intelligence/creative/overview/index-overview.store';
import { onMounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';

// ---------------------------- 参数 ------------------------------
import { BAR_CHART, PIE_CHART } from '@/store/intelligence/creative/analyze/analyze.const';
import { OptionsItem } from 'common/components/Cascader/type';
import { CommonOptionsItemType } from '@@/index';
import { LANGUAGE, WORLDCODECREATIVE } from '@/store/intelligence/creative/config/selectOptions.const';

// ---------------------------- 组件 ------------------------------
import Text from 'common/components/Text';
import Select from 'common/components/Select';
import { Icon } from 'tdesign-icons-vue-next';
import chart from '../AnalyzeChart/chart.vue';
import { useDownloadFile } from 'common/compose/download-file';
import { IPieItem, IChartDataItem } from '@/store/intelligence/creative/analyze/analyze';

const store = useIntelligenceCreativeOverviewStore();
const { model1SelectedDropdownItem, model2SelectedDropdownItem } = storeToRefs(store);

const props = defineProps({
  data: {
    type: Array as any,
    default: () => [],
  },
  leftOptionsList: {
    type: Array,
    default: () => [],
  },
  rightOptionsList: {
    type: Array,
    default: () => [],
  },
  attributeByDefaultOptions: {
    type: Array,
    default: () => [],
  },
  modelNo: {
    type: Number,
    default: 1,
  },
  isLoading: {
    type: Boolean,
    default: true,
  },
});

// Const
const isFirstModel = props.modelNo === 1;

// Ref
const selectedChart = ref(PIE_CHART);
const leftOptions = ref((props.leftOptionsList[0] as OptionsItem).value);
const rightOptions = ref((props.rightOptionsList[0]  as OptionsItem).value);
const formattedData = ref();
const excelData = ref();

onMounted(async () => {
  await init();
});

// 根据下拉框选择的项处理数据
function formatData(chartData: any, dropdownItem: string) {
  const selectedItem = dropdownItem.split(',')[1];

  if (selectedItem === 'Country') {
    /*
      * 对 chartData.list 中的每个项进行处理
      * 在 WORLDCODECREATIVE 数组中查找对应的国家名字
      * 如无则返回原本的名字
      */
    const result = chartData.list.map((item: any) => ({
      name: WORLDCODECREATIVE[item.name] || item.name,
      sum: item.sum,
    }));
    return {
      list: result,
      total_sum: chartData.total_sum,
    };
  }

  if (selectedItem === 'Language') {
    /*
      * 对 chartData.list 中的每个项进行处理
      * 在 LANGUAGE 数组中查找对应的语言名字
      * 如无则返回原本的名字
      */
    const result = chartData.list.map((item: any) => {
      const language = LANGUAGE.find(lang => lang.value === item.name);
      return {
        name: language ? language.label : item.name,
        sum: item.sum,
      };
    });
    return {
      list: result,
      total_sum: chartData.total_sum,
    };
  }

  // 其它选项无需处理， 直接返回
  return chartData;
}

function getChartData(chartData: IPieItem) {
  const max = 7;
  const res = chartData.list.slice(0, max).map((item: IChartDataItem) => ({
    name: item.name,
    value: item.sum,
  }));
    // 如果项目数量超过 7 个，将剩余的数据合并成Other
  if (chartData.list.length > max) {
    // 计算剩余数据的综合
    const totalSum = chartData.list.slice(max + 1).reduce((total: number, item: IChartDataItem) => total + item.sum, 0);
    res.push({
      name: 'Other',
      value: totalSum,
    });
  }
  return res;
};

function getExcelData(data: any) {
  const res = data.list.map((item: any) => ({
    [`${leftOptions.value}`]: item.name,
    [`${rightOptions.value}`]: item.sum,
    Percent: `${((item.sum / data.total_sum) * 100).toFixed(2)}%`,
  }));
  return res;
};

const downloadExcel = () => {
  useDownloadFile(
    excelData.value,
    'Aix Intelligence_Creative Analyze.xlsx',
  );
};

/**
   * 初始化函数，根据传入的预设选项设置下拉菜单，并更新模型项的值
   */
async function init() {
  // 如果有传入预设选项，则把右侧下拉菜单设为第所传入的预测选项
  if (props.attributeByDefaultOptions.length > 0) {
    rightOptions.value = (props.attributeByDefaultOptions[0] as OptionsItem).value;
  };
  /**
     *  根据 isFirstModel 变量的值来判断左右组件来更新数据
     * model1SelectedDropdownItem 和 model2SelectedDropdownItem 为2个下拉菜单的总结
     */
  if (isFirstModel) {
    model1SelectedDropdownItem.value = `${leftOptions.value},${rightOptions.value}`;
  } else {
    model2SelectedDropdownItem.value = `${leftOptions.value},${rightOptions.value}`;
  }
}

watch(leftOptions, (selectedItem) => {
  if (isFirstModel) {
    model1SelectedDropdownItem.value = `${selectedItem},${rightOptions.value}`;
  } else {
    model2SelectedDropdownItem.value = `${selectedItem},${rightOptions.value}`;
  }
});

watch(rightOptions, (selectedItem) => {
  if (isFirstModel) {
    model1SelectedDropdownItem.value = `${leftOptions.value},${selectedItem}`;
  } else {
    model2SelectedDropdownItem.value = `${leftOptions.value},${selectedItem}`;
  }
});

watch(() => props.data, (data) => {
  const res = formatData(data, `${leftOptions.value},${rightOptions.value}`);
  formattedData.value = getChartData(res);
  excelData.value = getExcelData(res);
});
</script>
  <style lang="scss">
  .is-selected,
  .is-selected:hover {
    z-index: 2;
    border-color: #006eff;
    color: #006eff;
  }
</style>
