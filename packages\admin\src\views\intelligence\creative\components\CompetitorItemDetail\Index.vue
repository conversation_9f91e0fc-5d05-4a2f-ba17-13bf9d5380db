<template>
  <BaseDialog
    ref="dialogRef"
    :title="`Creative detail`"
    :v-if="creativeItem.id"
    :footer="false"
    placement="center"
  >
    <div class="content1 w-[1180px] bg-[#fff] relative">
      <div class="top w-[100%] flex">
        <div class="left mr-[30px]">
          <div
            id="dplayer"
            class="video relative w-[800px] h-[480px] border-2 border-[black] rounded-large overflow-hidden bg-[black]"
          >
            <Video
              v-if="creativeItem.resources[0] && creativeItem.type === 2"
              class="w-full h-full vjs-big-play-centered exclude-mode"
              :url="creativeItem.resources[0]"
              :poster="creativeItem.preview_img"
            />
            <img
              v-else
              :class="`min-w-[100%] min-h-[100%] block
              absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%] exclude-mode`"
              :src="creativeItem.preview_img" alt=""
            >
          </div>
        </div>
        <div class="right info grow shrink w-[0px]">
          <div class="head flex justify-between items-center">
            <div class="left flex items-center w-[0px] grow shrink mr-[40px]">
              <img
                class="w-[30px] h-[30px] mr-[15px] exclude-mode"
                :src="creativeItem.app_logo"
                alt=""
              >
              <div class="gameName grow shrink w-[0px] text-[16px] overflow-hidden overflow-ellipsis whitespace-nowrap">
                {{ creativeItem.app_name }}
              </div>
            </div>
            <div class="right downloadBtn flex justify-center">
              <t-button @click="download">
                <template #icon>
                  <SvgIcon
                    name="download"
                    size="15px"
                    color="#fff"
                    class="mr-[10px]"
                  />
                </template>
                Creative
              </t-button>
            </div>
          </div>
          <div class="analyze flex justify-between mt-[10px] border-b-[1px] border-[#eee]">
            <div class="analyzeItem flex flex-col items-center w-[100%] relative">
              <div class="num text-base text-[#444] font-bold">{{ creativeItem.impression }}</div>
              <div class="text-xs text-[#444]">Impression</div>
              <div
                class="shu absolute right-[0px] h-[24px] border-r-[1px] border-[#eee] top-[50%] translate-y-[-50%]"
              />
            </div>
            <div class="analyzeItem flex flex-col items-center w-[100%] relative">
              <div class="num text-base text-[#444] font-bold">{{ creativeItem.days }}</div>
              <div class="text-xs text-[#444]">Days Active</div>
              <div
                class="shu absolute right-[0px] h-[24px] border-r-[1px] border-[#eee] top-[50%] translate-y-[-50%]"
              />
            </div>
            <div class="analyzeItem flex flex-col items-center w-[100%] relative">
              <div class="num text-base text-[#444] font-bold">{{ creativeItem.like_count }}</div>
              <div class="text-xs text-[#444]">Likes</div>
            </div>
          </div>
          <div class="property">
            <div class="propertyItem flex justify-between mt-[10px]">
              <div class="k text-xs text-[#888]">Title</div>
              <t-tooltip
                v-if="creativeItem.title"
                class="placement top center"
                :content="creativeItem.title"
                placement="top-right"
                :overlay-style="{ width: '200px' }"
                show-arrow
              >
                <div
                  class="v max-w-[180px] text-xs text-black-primary overflow-hidden overflow-ellipsis whitespace-nowrap"
                >
                  {{ creativeItem.title }}
                </div>
              </t-tooltip>
              <div v-else class="text-xs text-black-primary">None</div>
            </div>
            <div class="propertyItem flex justify-between mt-[10px]">
              <div class="k text-xs text-[#888]">Copywriting</div>
              <t-tooltip
                v-if="creativeItem.body"
                class="placement top center"
                :content="creativeItem.body"
                placement="top-right"
                :overlay-style="{ width: '200px' }"
                show-arrow
              >
                <div
                  class="v max-w-[180px] text-xs text-black-primary overflow-hidden overflow-ellipsis whitespace-nowrap"
                >
                  {{ creativeItem.body }}
                </div>
              </t-tooltip>
              <div v-else class="text-xs text-black-primary">None</div>
            </div>
            <div class="propertyItem flex justify-between mt-[10px]">
              <div class="k text-[12px] text-[#888]">First Seen</div>
              <div
                :class="`v max-w-[180px] text-[12px] text-black-primary
                overflow-hidden overflow-ellipsis whitespace-nowrap`"
              >
                {{ timeStrFormat(creativeItem.first_seen, 'YYYY.M.D') }}
              </div>
            </div>
            <div class="propertyItem flex justify-between mt-[10px]">
              <div class="k text-[12px] text-[#888]">Last Seen</div>
              <div
                :class="`v max-w-[180px] text-xs text-black-primary
                 overflow-hidden overflow-ellipsis whitespace-nowrap`"
              >
                {{ timeStrFormat(creativeItem.last_seen, 'YYYY.M.D') }}
              </div>
            </div>
            <div class="propertyItem flex justify-between items-center mt-[10px]">
              <div class="k text-xs text-[#888]">Country/Market</div>
              <div class="v max-w-[180px] flex items-center">
                <t-tooltip
                  v-if="countryList.length >= 2"
                  class="placement top center bg-[white]"
                  placement="top-right"
                  show-arrow
                >
                  <div class="flex items-center">
                    <img
                      v-for="(item, index) in countryList.slice(0, 3)"
                      :key="index"
                      class="w-[21px] h-[14px] mr-[5px] exclude-mode"
                      :src="getImgByCdn(item.flag)"
                      alt=""
                    >
                    <div
                      v-if="countryList.length > 3"
                      class="courntryMore text-xs flex items-center"
                    >
                      ...<span class="text-[#0076ff]">({{ countryList.length }})</span>
                    </div>
                  </div>
                  <template #content>
                    <div class="list flex flex-wrap max-w-[590px] max-h-[400px] overflow-y-auto p-[10px] pb-[0px]">
                      <div
                        v-for="(item, index) in countryList"
                        :key="index"
                        class="item flex items-center w-[140px] mb-[15px]"
                      >
                        <img
                          class="w-[21px] h-[14px] mr-[7px] exclude-mode"
                          :src="getImgByCdn(item.flag)"
                          alt=""
                        >
                        <div class="countryName">{{ item.fullName }}</div>
                      </div>
                    </div>
                  </template>
                </t-tooltip>
                <div
                  v-if="countryList.length === 1"
                  class="item flex items-center"
                >
                  <img
                    class="w-[21px] h-[14px] mr-[7px] exclude-mode"
                    :src="getImgByCdn(countryList[0].flag)"
                    alt=""
                  >
                  <div class="countryName text-xs text-black-primary">{{ countryList[0].fullName }}</div>
                </div>
                <div
                  v-if="countryList.length === 0"
                  class="item flex items-center"
                >
                  <div class="countryName text-xs text-black-primary">None</div>
                </div>
              </div>
            </div>
            <div class="propertyItem flex justify-between items-center mt-[10px]">
              <div class="k text-[12px] text-[#888]">Channel</div>
              <div class="v max-w-[180px] flex items-center">
                <div class="item flex items-center">
                  <img
                    class="w-[20px] h-[20px] mr-[7px] exclude-mode"
                    :src="getImgByCdn(getChannelImg(creativeItem.channel))"
                    alt=""
                  >
                  <div class="countryName text-xs text-black-primary">{{ getChannelName(creativeItem.channel) }}</div>
                </div>
              </div>
            </div>
            <div class="propertyItem flex justify-between mt-[10px]">
              <div class="k text-xs text-[#888]">OS</div>
              <div class="v max-w-[180px] text-xs text-[#000] overflow-hidden overflow-ellipsis whitespace-nowrap">
                {{ convertionOs(creativeItem.os) }}
              </div>
            </div>
            <div class="propertyItem flex justify-between mt-[10px]">
              <div class="k text-xs text-[#888]">Landing Page</div>
              <t-tooltip
                v-if="creativeItem.store_url"
                class="placement top center"
                :content="creativeItem.store_url"
                placement="top-right"
                :overlay-style="{ width: '200px' }"
                show-arrow
              >
                <div
                  :class="`v max-w-[180px] text-xs text-black-primary overflow-hidden
                  overflow-ellipsis whitespace-nowrap cursor-pointer`"
                >
                  <a target="_blank" :href="creativeItem.store_url">{{ creativeItem.store_url }}</a>
                </div>
              </t-tooltip>
              <div v-else class="text-xs text-black-primary">None</div>
            </div>
            <!-- <div class="propertyItem flex justify-between mt-[10px]">
              <div class="k text-[12px] text-[#888]">Theme</div>
              <div
                :class="`v max-w-[180px] text-xs text-black-primary
                 overflow-hidden overflow-ellipsis whitespace-nowrap`"
              >
                {{ convertionTheme(creativeItem.theme) }}
              </div>
            </div> -->
            <!-- <div class="propertyItem flex justify-between mt-[10px]">
              <div class="k text-xs text-[#888]">Tag</div>
              <div class="v flex items-center">
                <t-tooltip
                  v-if="creativeItem.tags.length >= 2"
                  class="placement top center bg-[white]"
                  placement="top-right"
                  show-arrow
                >
                  <div class="flex items-center">
                    <div
                      v-for="(item, index) in creativeItem.tags.slice(0, 3)"
                      :key="index"
                      :class="`tagItem w-[65px] h-[20px] pl-[7px] pr-[7px] text-xs rounded-game-icon
                       overflow-hidden overflow-ellipsis whitespace-nowrap
                       ml-[5px] mr-[5px] bg-[#f2f3f5] leading-[20px] text-center`"
                    >
                      {{ item }}
                    </div>
                    <div
                      v-if="creativeItem.tags.length > 3"
                      class="courntryMore text-xs flex items-center"
                    >
                      ...<span class="text-[#0076ff]">({{ creativeItem.tags.length }})</span>
                    </div>
                  </div>
                  <template #content>
                    <div class="list flex flex-wrap max-w-[590px] p-[10px] pt-[0px] pb-[0px]">
                      {{ creativeItem.tags.join(',&nbsp;&nbsp;') }}
                    </div>
                  </template>
                </t-tooltip>
                <div class="item flex items-center">
                  <div
                    v-if="creativeItem.tags.length === 1"
                    :class="`tagItem w-[65px] h-[20px] pl-[7px] pr-[7px] text-[12px] rounded-[5px]
                       overflow-hidden overflow-ellipsis whitespace-nowrap
                       ml-[5px] mr-[5px] bg-[#f2f3f5] leading-[20px] text-center`"
                  >
                    {{ creativeItem.tags[0] }}
                  </div>
                </div>
              </div>
            </div> -->
          </div>
        </div>
      </div>
      <!-- <div class="bottom mt-[25px]">
        <div
          v-show="keyFrames.length > 0 && creativeItem.type === 2"
          class="keyFrames"
        >
          <div class="swiper mySwipe mySwipe-pzh w-[100%] h-[220px]">
            <div
              ref="wrapper"
              class="swiper-wrapper"
            >
              <div
                v-for="(item, index) in keyFrames"
                :key="index"
                :class="`swiper-slide overflow-hidden border-[1px] broder-[#000]
                 rounded-large relative cursor-pointer bg-black-primary`"
                @click="videoJumpTime(item.jumpTime)"
              >
                <img
                  class="max-w-[100%] max-h-[100%] absolute left-[50%] top-[50%]
                   translate-x-[-50%] translate-y-[-50%] exclude-mode"
                  :src="item.img"
                  alt=""
                >
                <SvgIcon
                  name="play-btn"
                  size="24px"
                  color="#fff"
                  class="absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
                />
              </div>
            </div>
            <div class="swiper-pagination swiper-pagination-pzh" />
          </div>
        </div>
      </div> -->
      <div
        v-if="creativeList.length >= 0 && creativeActiveIndex != 0"
        :class="`left flex justify-center items-center absolute left-[-100px] top-[50%] w-[50px]
         h-[50px] translate-y-[-50%] bg-[#56607A] rounded-[50%] cursor-pointer `"
        @click="changeActiveIndex(-1)"
      >
        <SvgIcon
          name="arrow"
          size="15px"
          color="#fff"
          class="rotate-[90deg]"
        />
      </div>
      <div
        v-if="creativeList.length >= 0 && creativeActiveIndex != creativeList.length - 1"
        :class="`right flex justify-center items-center absolute right-[-100px] top-[50%] w-[50px]
         h-[50px] translate-y-[-50%] bg-[#56607A] rounded-[50%] cursor-pointer `"
        @click="changeActiveIndex(1)"
      >
        <SvgIcon
          name="arrow"
          size="15px"
          color="#fff"
          class="rotate-[-90deg]"
        />
      </div>
    </div>
  </BaseDialog>
</template>
<script setup lang="tsx">
// import Swiper from 'swiper';
import Video from 'common/components/Video';
// import type { VideoJsPlayer } from 'video.js';
import { PropType, computed, watch, ref } from 'vue';
import BaseDialog from 'common/components/Dialog/Base';
import SvgIcon from 'common/components/SvgIcon';
import 'swiper/css/bundle';
import {
  timeStrFormat,
  convertionCountry,
  getChannelName,
  getChannelImg,
  convertionOs,
  // convertionTheme,
  // convertionKeyFrames,
  downloadImgOrVideo,
  getImgByCdn,
} from '@/store/intelligence/creative/competitor/untis';
// import { useDownloadFile } from 'common/compose/download-file';
import { ICreativeItem } from '@/store/intelligence/creative/competitor/competitor.d';
const dialogRef = ref();
// const player = shallowRef<any>(); // 视频实例
// let swiper: any = null;
// const videoJumpTime = (stepTime: number) => {
//   player.value.currentTime(stepTime);
// };
const props = defineProps({
  creativeItem: {
    type: Object as PropType<ICreativeItem>,
    required: true,
  },
  creativeList: {
    type: Array as PropType<Array<ICreativeItem>>,
    required: true,
  },
  creativeActiveIndex: {
    type: Number,
    required: true,
  },
});
const emit = defineEmits(['changeActiveIndex']);
defineExpose({
  show: () => dialogRef.value.show(),
  hide: () => dialogRef.value.hide(),
});
// const creativeItem = computed(() => props.creativeList[props.creativeActiveIndex]);
const countryList = computed(() => {
  // 城市列表
  const temp = convertionCountry(props.creativeItem?.countries || []);
  return temp;
});
// const keyFrames = computed(() => {
//   const temp = convertionKeyFrames(props.creativeItem?.key_frames_by_scene || []);
//   temp.unshift({ img: props.creativeItem.preview_img, jumpTime: 0, showTime: '00:00' });
//   return temp;
// });
// watch(
//   () => props.creativeItem,
//   () => {
//     if (swiper) {
//       setTimeout(() => {
//         // swiper.update();
//         // swiper.slideTo(0, 0, false);
//         swiperInit();
//       }, 40);
//     }
//   },
// );
watch(dialogRef.value?.innerVisible?.value, () => {
});
// const handleMounted = (payload: any) => {
//   player.value = payload.player;
// };
const changeActiveIndex = (val: number) => {
  emit('changeActiveIndex', props.creativeActiveIndex + val);
};
const download = () => {
  const url = props.creativeItem.resources[0];
  downloadImgOrVideo(url);
};
// const swiperInit = () => {
//   swiper = new Swiper('.mySwipe-pzh', {
//     slidesPerView: 5,
//     spaceBetween: 20,
//     pagination: {
//       el: '.swiper-pagination-pzh',
//       clickable: true,
//     },
//   });
// };
// onMounted(() => {
//   swiperInit();
// });
</script>
<style scoped lang="scss">
.mask .t-tooltip .t-popup__content {
  max-width: 600px;
}
</style>
