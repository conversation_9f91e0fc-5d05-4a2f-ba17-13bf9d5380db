<template>
  <div class="rectBox mb-[12px]">
    <div class="rect border-[1px] border-[#ddd] rounded-default">
      <div class="head p-[10px] pb-[0px]">
        <div class="flex items-center">
          <!-- <img
            class="w-[25px] h-[25px] mr-[10px] rounded-game-icon"
            :src="competitorItem.app_logo"
            alt=""
          > -->
          <ImageView
            :style="{
              height: '25px',
              width: '25px',
              'margin-right': '10px',
              'border-radius': '4px',
            }"
            :image-url="competitorItem.app_logo"
            @on-error="(imageWarningUrl)=> imageWarningUrl.value = GRAY_COLOR"
          />
          <div class="gameName text-base text-black-primary overflow-hidden overflow-ellipsis whitespace-nowrap">
            {{ props.competitorItem.app_name }}
          </div>
          <div class="ml-[auto]">
            <slot name="titleRight" />
          </div>
        </div>
        <div
          :class="`title pt-[3px] pb-[3px] text-xs text-black-secondary
           overflow-hidden overflow-ellipsis whitespace-nowrap`"
        >
          {{ competitorItem.title ? competitorItem.title : 'No Title' }}
        </div>
      </div>
      <div
        class="section relative h-[150px] overflow-hidden cursor-pointer"
        @click="showDetail(index)"
      >
        <!-- referrerpolicy="no-referrer" 解决403防盗链 -->
        <!-- <img
          referrer="no-referrer|origin|unsafe-url"
          referrerpolicy="no-referrer"
          class="absolute w-[100%] h-[150px] left-[0px] top-[0px] blur-[15px]"
          :src="competitorItem.preview_img"
          alt=""
        > -->
        <ImageView
          :style="{
            position: 'absolute',
            height: '150px',
            left: '0px',
            top: '0px',
            filter: 'blur(15px)',
            width: '100%',
          }"
          :image-url="competitorItem.preview_img"
          @on-error="(imageWarningUrl)=> imageWarningUrl.value = NO_IMAGE_AVAILABLE"
        />
        <!-- referrer="no-referrer|origin|unsafe-url"
        referrerpolicy="no-referrer" -->
        <!-- <img
          referrer="no-referrer|origin|unsafe-url"
          referrerpolicy="no-referrer"
          class="absolute max-w-[100%] h-[150px] left-[50%] translate-x-[-50%]"
          :src="competitorItem.preview_img"
          alt=""
        > -->
        <ImageView
          :image-url="competitorItem.preview_img"
          :style="{
            transform: 'translateX(-50%)',
            position: 'absolute',
            'max-width': '100%',
            height: '150px',
            left: '50%'
          }"
          @on-error="(imageWarningUrl)=> imageWarningUrl.value = NO_IMAGE_AVAILABLE"
        />
        <SvgIcon
          v-if="competitorItem.type === 2"
          name="play-btn"
          size="24px"
          color="#fff"
          class="absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
        />
        <div
          :class="`index absolute  left-[0px] top-[2px] min-w-[70px] h-[30px] bg-[rgba(0,118,255,.77)] text-[14px]
           text-center text-white font-bold leading-[30px] pl-[16px] pr-[16px]`"
        >
          <slot name="label">
            {{ competitorItem.impression }}
          </slot>
        </div>
      </div>
      <div class="footer p-[10px]">
        <div
          class="`body w-[100%] h-[22px] text-black-primary text-xs text-black-secondary
           overflow-hidden overflow-ellipsis whitespace-nowrap`"
        >
          <slot name="body">
            <span>{{ competitorItem.body ? competitorItem.body : 'No Copywriting' }}</span>
          </slot>
        </div>
        <div class="analyze flex justify-around mt-[8px] whitespace-nowrap">
          <slot name="firstAnylaze">
            <div class="analyzeItem w-[100%] relative pr-[5px]">
              <div class="num flex justify-center text-base text-center font-bold">
                <span>{{ competitorItem.impression }}</span>
                <div
                  v-if="competitorItem.impression_distance"
                  class="distance flex items-center"
                >
                  <span>(</span>
                  <SvgIcon
                    name="arrow2"
                    size="15px"
                    color="#32925d"
                    class="rotate-90"
                  />
                  <span class="text-[#32925d]">
                    {{ convertionNumToStr(parseInt(competitorItem.impression_distance)) }}
                  </span>
                  <span>)</span>
                </div>
              </div>
              <div class="text-center text-xs text-[#666]">Impression</div>
            </div>
          </slot>
          <div class="analyzeItem w-[100%] relative">
            <div class="num text-base text-center font-bold">{{ competitorItem.like_count }}</div>
            <div class="text-center text-xs text-[#666]">Likes</div>
            <div class="shu absolute left-[0px] h-[24px] border-r-[1px] border-[#eee] top-[50%] translate-y-[-50%]" />
          </div>
          <div class="analyzeItem w-[100%] relative">
            <div class="num text-base text-center font-bold">{{ competitorItem.comment_count }}</div>
            <div class="text-center text-xs text-[#666]">Comments</div>
            <div class="shu absolute left-[0px] h-[24px] border-r-[1px] border-[#eee] top-[50%] translate-y-[-50%]" />
            <!-- <div
              class="shu absolute right-[0px] h-[24px] border-r-[1px] border-[#eee] top-[50%] translate-y-[-50%]"
            /> -->
          </div>
          <div class="analyzeItem w-[100%] relative">
            <div class="num text-base text-center font-bold">{{ competitorItem.share_count }}</div>
            <div class="text-center text-xs text-[#666]">Shares</div>
            <div class="shu absolute left-[0px] h-[24px] border-r-[1px] border-[#eee] top-[50%] translate-y-[-50%]" />
          </div>
          <!-- <div class="analyzeItem w-[100%]">
            <div class="num text-[16px] text-center font-bold">{{ competitorItem.conversion }}</div>
            <div class="text-center text-[12px] text-[#666]">Conversion</div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="tsx">
import SvgIcon from 'common/components/SvgIcon';
import ImageView from 'common/components/ImageView/Index.vue';
import { ICreativeItem } from '@/store/intelligence/creative/competitor/competitor';
import { NO_IMAGE_AVAILABLE, GRAY_COLOR } from '@/store/intelligence/creative/competitor/competitor.const';
import { PropType } from 'vue';
// untis
import { convertionNumToStr } from '@/store/intelligence/creative/competitor/untis';
const props = defineProps({
  competitorItem: {
    type: Object as PropType<ICreativeItem>,
    required: true,
  },
  showDetail: {
    type: Function,
    default: () => {},
  },
  index: {
    type: Number,
    default: 0,
  },
});
</script>
<style scoped>
/* @media only screen and (max-width: 1430px) {
  .rectBox {
    width: 100%;
  }
} */
/* @media only screen and (min-width: 1430px) {
  .rectBox {
    width: 50%;
  }
} */
/* @media only screen and (min-width: 1900px) {
  .rectBox {
    width: 33.333333%;
  }
}
@media only screen and (min-width: 2300px) {
  .rectBox {
    width: 25%;
  }
} */
</style>
