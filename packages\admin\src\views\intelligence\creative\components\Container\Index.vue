<template>
  <div class="h-full container-layout1">
    <template
      v-for="(item, index) in innerData"
      :key="item.id"
    >
      <slot
        name="item"
        :data="item"
        :index="index"
      >
        <CompetitorItem
          :competitor-item="item"
          :index="index"
          :show-detail="showDetail"
        />
      </slot>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, PropType } from 'vue';
import CompetitorItem from '../CompetitorItem/Index.vue';
import { ICreativeItem } from '@/store/intelligence/creative/competitor/competitor';

const props = defineProps({
  list: {
    type: Array as PropType<Array<ICreativeItem>>,
    default: () => [],
  },
  showDetail: {
    type: Function,
    default: () => {},
  },
});

const innerData = computed(() => props?.list);
</script>

<style scoped>
.container-layout1 {
  width: 100%;
  display: grid;
  grid-template-columns: 100%;
  justify-content: space-between;
  align-items: center;
  row-gap: 10px;
  column-gap: 12px;
  padding-bottom: 16px;
}

@media only screen and (min-width: 1100px) {
  .container-layout1 {
    grid-template-columns: repeat(2, minmax(300px, 600px));
    /* grid-column: 1 / 5; */
  }
}
@media only screen and (min-width: 1400px) {
  .container-layout1 {
    grid-template-columns: repeat(3, minmax(300px, 520px));
    /* grid-column: 1 / 5; */
  }
}
@media only screen and (min-width: 1700px) {
  .container-layout1 {
    grid-template-columns: repeat(3, minmax(400px, 650px));
    /* grid-column: 1 / 5; */
  }
}
@media only screen and (min-width: 2000px) {
  .container-layout1 {
    grid-template-columns: repeat(4, minmax(400px, 650px));
    /* grid-column: 1 / 5; */
  }
}
</style>
