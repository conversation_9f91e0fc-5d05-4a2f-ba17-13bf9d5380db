<template>
  <!-- 表格区域 -->
  <t-table
    ref="tableRef"
    v-model:displayColumns="testCol"
    :row-key="props.rowKey"
    :columns="props.columns"
    :data="props.data"
    hide-sort-tips
    show-sort-column-bg-color
    :max-height="500"
    ellipsis
    :sort="props.sort"
    :loading="props.loading"
    :resizable="props.resizable"
    @sort-change="sortChange"
  >
    <!-- 排序图标 -->
    <!-- <template #sortIcon>
      <SortIcon class="mt-[1px] mb-[1px]" />
    </template> -->
    <!-- 动态插槽 -->
    <template
      v-for="(soltItem, index) in props.columsSlot"
      :key="index"
      #[soltItem]="{ row, rowIndex, col, colIndex }"
    >
      <slot
        :row="row"
        :row-index="rowIndex"
        :col="col"
        :col-index="colIndex"
        :name="soltItem"
      />
    </template>
  </t-table>
</template>
<script lang="ts" setup>
import { ref, watch, type PropType } from 'vue';
const tableRef = ref();
const emit = defineEmits(['sortChange', 'pageChange']);
const displayCols = ref<Array<string>>(['id']);
const testCol = ref<Array<string>>(['']);
const props = defineProps({
  rowspanAndColspan: {
    // type: Object as PropType<IRowspanAndColspanItem>,
    type: Function,
    default: () => ({
      colspan: 1,
      rowspan: 1,
    }),
  },
  rowClassName: {
    type: Function,
    default: () => function () {},
  },
  // 表格的完整列，需在表格结构渲染之前确定
  columns: {
    type: Array,
    default: () => [],
  },
  // 表格需展示的列。https://tdesign.woa.com/vue/components/table?tab=api
  displayColumns: {
    type: Array as PropType<Array<string>>,
    default: () => [],
  },
  data: {
    type: Array as PropType<Array<any>>,
    default: () => [],
  },
  sort: {
    type: Object as PropType<any>,
    default: () => {},
  },
  // 动态插槽  要自定义渲染的列的插槽
  columsSlot: {
    type: Array as () => Array<string>,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: () => false,
  },
  rowKey: {
    type: String,
    default: () => 'id',
  },
  resizable: {
    type: Boolean,
    default: () => false,
  },
});
// 排序列的变化 当前是哪一列排序
function sortChange(val: any) {
  emit('sortChange', val);
};
// watch(
//   () => props.displayColumns,
//   (value) => {
//     console.log('cols', value);
//     displayCols.value = [...value];
//   },
// );
watch(displayCols, () => {
});
</script>
