<template>
  <div class="flex justify-between items-center mb-[24px]">
    <div class="left flex justify-between items-center">
      <SvgIcon
        v-if="leftIcon"
        :name="leftIcon"
        size="20px"
        color="#1676fd"
        class="mr-[10px] cursor-pointer"
        @click="jumpBack"
      />
      <div class="title text-[24px] font-semibold">{{ title }}</div>
    </div>
    <div
      v-if="rightText"
      class="right flex justify-between items-center cursor-pointer"
      @click="jumpOtherPage"
    >
      <div>
        <SvgIcon
          :name="'plus'"
          size="12px"
          color="#1676fd"
          class="mr-[10px] cursor-pointer"
        />
      </div>
      <div class="text-[#3d35d3] text-[16px] font-medium">{{ rightText }}</div>
    </div>
  </div>
</template>
<script setup lang="tsx">
import { useRouter } from 'vue-router';
import SvgIcon from 'common/components/SvgIcon';
const props = defineProps({
  title: {
    type: String,
    default: () => '',
  },
  leftIcon: {
    // 传递icon的name
    type: String,
    default: () => '',
  },
  rightText: {
    type: String,
    default: () => '',
  },
  rightIcon: {
    type: String,
    default: () => '',
  },
  rightJumpPath: {
    // 需要跳转的的路由地址
    type: String,
    default: () => '',
  },
});
const router = useRouter();
const jumpBack = () => {
  router.go(-1);
};
const jumpOtherPage = () => {
  router.push({ path: props.rightJumpPath });
};
</script>
