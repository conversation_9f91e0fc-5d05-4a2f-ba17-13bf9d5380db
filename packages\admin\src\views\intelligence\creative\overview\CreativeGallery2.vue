<template>
  <div class="bg-white pt-[16px] list-container">
    <DataContainer
      class="w-full h-full"
      :data="creativeList"
      :loading="isLoadingCreativeList"
      :default-page="page"
      :page-size="pageSize"
      :total="total"
      @on-page-change="onPageChange"
    >
      <template #attributeSlot>
        <div class="flex">
          <t-select
            v-if="isListType"
            v-model="orderBy.key"
            :options="ORDER_BY_OPTIONS"
            class="w-[200px]"
            @change="changeOrderBy"
          >
            <template #prefixIcon> Order By:</template>
          </t-select>
        </div>
      </template>
      <template #actionSlot>
        <div class="flex">
          <SvgBtnList :list="buttons" />
          <div
            v-if="isLoadingCreativeDown"
            class="flex w-[36px] h-[36px] justify-center items-center"
          >
            <t-progress
              :percentage="percentage"
              theme="circle"
              :label="false"
              :size="25"
              :stroke-width="3"
            />
          </div>
        </div>
      </template>
      <div>
        <Container
          v-if="isListType"
          :list="creativeList"
          :show-detail="showDetail"
        />
        <Table
          v-else
          ref="tableRef"
          resizable
          row-key="id"
          :data="creativeList"
          :columns="tableAllColumns"
          :horizontal-scroll-affixed-bottom="true"
          :header-affixed-top="true"
          :empty="' '"
          max-height="1000px"
          @cell-click="onCellClick"
          @sort-change="rStore.onSortChange"
        >
          <template #impression>
            <div>123</div>
          </template>
          <template #empty>
            <div>123</div>
          </template>
        </Table>
        <CompetitorItemDetail
          v-if="creativeList.length >= 0"
          :key="`two`"
          ref="competitorItemDetailRef"
          :creative-list="creativeList"
          :creative-item="creativeItem"
          :creative-active-index="creativeActiveIndex"
          @change-active-index="changeActiveIndex"
        />
      </div>
      <data-empty
        v-if="creativeList.length === 0 && !isLoadingCreativeList"
        class="w-full h-full max-h-[600px]"
      />
    </DataContainer>
  </div>
</template>
<script lang="tsx" setup>
import { storeToRefs } from 'pinia';
import { ref, nextTick, watch } from 'vue';
import DataContainer from 'common/components/Layout/DataContainer.vue';
// import MultipleSelect from 'common/components/Select/container.vue';
import CompetitorItemDetail from '../../creative/components/CompetitorItemDetail/Index.vue';
// import SvgIcon from 'common/components/SvgIcon';
import SvgBtnList from 'common/components/SvgIcon/SvgBtnList.vue';
import Container from '../components/Container/Index.vue';
import Table from 'common/components/table';
import DataEmpty from '@/components/nullable/DataEmpty.vue';
import { useIntelligenceCreativeOverviewStore } from '@/store/intelligence/creative/overview/index-overview.store';
import { ORDER_BY_OPTIONS } from '@/store/intelligence/creative/overview/overview.const';
import { CreateItemDefault } from '@/store/intelligence/creative/competitor/competitor.const';
import type { PageInfo as IPageInfo, BaseTableCellEventContext } from 'tdesign-vue-next';
import { type ICreativeItem } from '@/store/intelligence/creative/competitor/competitor';
import { cloneDeep } from 'lodash-es';
const rStore = useIntelligenceCreativeOverviewStore();
const {
  orderBy,
  isListType,
  creativeList,
  isLoadingCreativeList,
  isLoadingCreativeDown,
  percentage,
  page,
  pageSize,
  total,
  tableAllColumns,
  buttons,
  tableRef,
} = storeToRefs(rStore);
const competitorItemDetailRef = ref();
const creativeItem = ref<ICreativeItem>(CreateItemDefault);
const creativeActiveIndex = ref<number>(-1);
const changeActiveIndex = (val: number) => {
  creativeActiveIndex.value = val;
};
watch(
  creativeActiveIndex,
  () => {
    if (creativeList.value.length > 0 && creativeActiveIndex.value >= 0) {
      creativeItem.value = cloneDeep(creativeList.value[creativeActiveIndex.value]);
    }
  },
  {
    deep: true,
    immediate: true,
  },
);
watch(
  creativeList,
  () => {
    if (creativeList.value.length > 0 && creativeActiveIndex.value >= 0) {
      creativeItem.value = cloneDeep(creativeList.value[creativeActiveIndex.value]);
    }
  },
  {
    deep: true,
    immediate: true,
  },
);

const showDetail = (index: number) => {
  // 展示详情
  changeActiveIndex(-1);
  changeActiveIndex(index);
  nextTick(() => {
    creativeItem.value = cloneDeep(creativeList.value[creativeActiveIndex.value]);
    competitorItemDetailRef.value.show();
  });
};
// const onButton = (textArr: string[] | string) => {
//   if (textArr.length === 0) {
//     return 'All';
//   }
//   if (!Array.isArray(textArr)) return textArr;
//   return textArr.length > 1 ? textArr.length : textArr.join(',');
// };
const changeOrderBy = (val: string) => {
  rStore.setOrderBy({ key: val, rule: 'desc' });
};

function onPageChange(current: number, pageInfo: IPageInfo) {
  page.value = current;
  pageSize.value = pageInfo.pageSize;
  rStore.getCreativePage();
}
const onCellClick = (context: BaseTableCellEventContext<ICreativeItem>) => {
  // 点击单元格的回调
  if (context.col.colKey === 'resource') {
    showDetail(context.rowIndex);
  }
};
</script>

<style lang="scss" scoped>
.list-container {
  background: #fff;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.03);
  border-radius: 12px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.t-popup__content {
  max-width: 700px !important;
}
body {
  :deep(.t-popup__content) {
    max-width: 700px !important;
  }
}
.pzh-popup {
  padding: 10px;
  :deep(.t-popup__content) {
    background-color: rgb(36, 36, 36) !important;
  }
}
:global(.t-popup__content) {
  max-width: 700px !important;
}
</style>
