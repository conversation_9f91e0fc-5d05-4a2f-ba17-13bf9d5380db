<template>
  <CommonView
    class="w-full h-full intelligence-creative-overvew-box"
    :router-index="-1"
    :tab-props="rStore.tabProps"
    :form-props="{
      modelValue: conditionRef.cur,
      formList: conditionList,
      'onUpdate:modelValue': formUpdateValue,
      onSubmit: formSubmit,
      onReset: formReset,
    }"
    :store="rStore"
  >
    <!-- <template #title>
      <div>123</div>
    </template> -->
    <template #views>
      <CreativeGallery />
    </template>
  </CommonView>
</template>
<script setup lang="tsx">
import { storeToRefs } from 'pinia';
import { nextTick, onMounted } from 'vue';
// import { cloneDeep } from 'lodash-es';
import CommonView from 'common/components/Layout/CommonView.vue';
import CreativeGallery from './CreativeGallery2.vue';
import { useIntelligenceCreativeOverviewStore } from '@/store/intelligence/creative/overview/index-overview.store';
import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE, OverviewTabType } from '@/store/intelligence/creative/overview/overview.const';
const rStore = useIntelligenceCreativeOverviewStore();
const { condition: conditionRef, conditionList, page, pageSize, tabSelectId } = storeToRefs(rStore);
// v-model绑定表单的值
function formUpdateValue(value: typeof conditionRef) {
  // console.log('change value', value);
  conditionRef.value.cur = {
    ...conditionRef.value.cur,
    ...value,
  };
}
async function formReset() {
  // conditionRef.value.cur = cloneDeep(conditionRef.value.default); // 不能直接等于，否则改变cur时会改变default
  rStore.resetFormFilter();
  page.value = DEFAULT_PAGE;
  pageSize.value = DEFAULT_PAGE_SIZE;
  setTimeout(() => {
    nextTick(() => {
      rStore.getCreativePage();
    });
  }, 0);
}

function formSubmit(formData: any) {
  console.log(formData);
  rStore.getCreativePage();
  // Object.keys(formData).forEach((field) => {
  //   const value = (formData as any)[field];
  //   (conditionRef.value.cur as any)[field] = value;
  // });
  // // 重置页码数
  // conditionRef.value.cur.pageIndex = DEFAULT_PAGE_INDEX;
  // conditionRef.value.cur.pageSize = DEFAULT_PAGE_SIZE;
  // setPivot({});
};

onMounted(() => {
  tabSelectId.value = OverviewTabType.CREATIVEGALLERY;
});
rStore.customBeforeRouteLeave();
</script>
<style>
.zh-day-range .t-input {
  height: 28px!important;
}
.intelligence-creative-overvew-box .isolate {
  overflow: hidden;
}
</style>
