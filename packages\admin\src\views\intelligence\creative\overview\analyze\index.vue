<template>
  <div>
    <CommonView
      :store="store"
      :router-index="-1"
      :form-props="{
        modelValue: condition.cur,
        formList: conditionList,
        'onUpdate:modelValue': formUpdateValue,
        onSubmit: formSubmit,
        onReset: formReset,
      }"
      :tab-props="tabProps"
    >
      <template #views>
        <div
          class="flex-container flex place-content-around"
        >
          <chartComponents
            :data="chartData1"
            :left-options-list="LEFT_SIDE_OPTIONS"
            :right-options-list="RIGHT_SIDE_OPTIONS"
            :attribute-by-default-options="RIGHT_SIDE_OPTIONS.filter(item => item.label === 'Media Source')"
            :is-loading="chartLoading1"
          />
          <div style="width: 50px;" />
          <chartComponents
            :data="chartData2"
            :left-options-list="LEFT_SIDE_OPTIONS"
            :right-options-list="RIGHT_SIDE_OPTIONS"
            :attribute-by-default-options="RIGHT_SIDE_OPTIONS.filter(item => item.label === 'Country')"
            :model-no="2"
            :is-loading="chartLoading2"
          />
        </div>
      </template>
    </CommonView>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, watch, Ref, nextTick } from 'vue';
import { useIntelligenceCreativeOverviewStore } from '@/store/intelligence/creative/overview/index-overview.store';
import { useGlobalGameStore } from '@/store/global/game.store';
import { storeToRefs } from 'pinia';

// ---------------------------- 参数 ------------------------------
import { OverviewTabType } from '@/store/intelligence/creative/overview/overview.const';
import { LEFT_SIDE_OPTIONS, RIGHT_SIDE_OPTIONS } from '@/store/intelligence/creative/analyze/analyze.const';

// ---------------------------- 组件 ------------------------------
import CommonView from 'common/components/Layout/CommonView.vue';
import chartComponents from '../../components/AnalyzeChart/chartComponents.vue';
import { IFormParams } from '@/store/intelligence/creative/overview/overview';

const store = useIntelligenceCreativeOverviewStore();
const gameStore = useGlobalGameStore();
const { getPieChart, resetFormFilter } = store;
const {
  tabProps,
  condition,
  conditionList,
  tabSelectId,
  model1SelectedDropdownItem,
  model2SelectedDropdownItem,
  isRequetGameOptions,
} = storeToRefs(store);
const { gameCode } = storeToRefs(gameStore);

const chartData1 = ref();
const chartData2 = ref();
const chartLoading1 = ref();
const chartLoading2 = ref();

function formUpdateValue(value: typeof condition) {
  condition.value.cur = {
    ...condition.value.cur,
    ...value,
  };
}

async function formReset() {
  resetFormFilter();
  chartLoading1.value = true;
  chartLoading2.value = true;
  await initChartData();
}

async function formSubmit(formData: any) {
  chartLoading1.value = true;
  chartLoading2.value = true;
  await initChartData(formData);
}

/*
* formParams 为传入form所选择的params 如没传入则使用默认的选择
* selectedChartToRefresh 为要指定刷新的图表， 如没传入则一起刷新
*/
async function initChartData(formParams?: any, selectedChartToRefresh?: string) {
  const requestParams = formParams ? formParams : condition.value.cur;

  if (!selectedChartToRefresh || selectedChartToRefresh === '1') {
    await updateChart(chartLoading1, chartData1, requestParams, model1SelectedDropdownItem);
  }
  if (!selectedChartToRefresh || selectedChartToRefresh === '2') {
    await updateChart(chartLoading2, chartData2, requestParams, model2SelectedDropdownItem);
  }
}

async function updateChart(loading: Ref<boolean>, data: Ref<any>, formParams: IFormParams, selectedChart: Ref<string>) {
  const chartLoading = loading;
  const chartData = data;

  chartLoading.value = true;
  chartData.value = await getPieChart(formParams, selectedChart.value);
  chartLoading.value = false;
}

onMounted(async () => {
  tabSelectId.value = OverviewTabType.ANALYZE;
  if (isRequetGameOptions.value) {
    await initChartData();
  }
});

watch(
  [() => model1SelectedDropdownItem.value, () => model2SelectedDropdownItem.value],
  async ([item1, item2], [prevItem1, prevItem2]) => {
    if (item1 !== prevItem1) {
      await initChartData(condition.value.cur, '1');
    }
    if (item2 !== prevItem2) {
      await initChartData(condition.value.cur, '2');
    }
  },
);

watch(() => isRequetGameOptions.value, () => {
  if (isRequetGameOptions.value) {
    initChartData();
  }
});
watch(() => gameCode.value, async () => {
  await nextTick();
  initChartData();
});
store.customBeforeRouteLeave();

</script>
  <style lang="scss" scoped>
  @media (max-width: 1400px) {
  .flex-container {
      flex-direction: column;
    }
  }
</style>

<style>
  .zh-day-range .t-input {
    height: 28px!important;
  }
</style>
