<template>
  <CommonView
    class="w-full h-full intelligence-creative-smv-box"
    :router-index="-1"
    :form-props="{
      modelValue: conditionRef.cur,
      formList: conditionList,
      'onUpdate:modelValue': formUpdateValue,
      onSubmit: formSubmit,
      onReset: formReset,
    }"
    :store="rStore"
  >
    <template #subTitle>
      <div />
    </template>
    <template #views>
      <div class="bg-white p-[16px] pr-[0px] list-container h-full">
        <div class="pr-[16px] mr-[-2px] overflow-y-auto narrow-scrollbar h-full">
          <div
            v-if="creativeList.length > 0 && !isLoadingList"
          >
            <Container
              :list="creativeList"
            >
              <template #item="{data, index}">
                <CompetitorItem
                  :competitor-item="data"
                  :index="index"
                  :show-detail="showDetail"
                >
                  <template #label>
                    <slot name="label">{{ data.smv_view_count }}</slot>
                  </template>
                  <template #titleRight>
                    <img
                      class="w-[20px] h-[20px] rounded-game-icon"
                      :src="getImgByCdn(getDatabrainChannelImg(data.smv_channel || ''))"
                      alt=""
                    >
                  </template>
                  <template #body>
                    <div class="w-[100%] text-right">{{ data.smv_comment_time || '' }}</div>
                  </template>
                  <template #firstAnylaze>
                    <div class="analyzeItem w-[100%] relative">
                      <div class="num text-base text-center font-bold">{{ data.smv_view_count }}</div>
                      <div class="text-center text-xs text-[#666]">Views</div>
                      <div
                        :class="`shu absolute left-[0px] h-[24px] border-r-[1px]
                                 border-[#eee] top-[50%] translate-y-[-50%]`"
                      />
                    </div>
                  </template>
                </CompetitorItem>
              </template>
            </Container>
          </div>
          <data-empty
            v-if="creativeList.length === 0 && !isLoadingList"
            class="w-full h-full max-h-[600px]"
          />
          <FullLoading
            v-if="isLoadingList"
            class="my-load-box relative h-full"
          />
        </div>
      </div>
    </template>
  </CommonView>
</template>
<script setup lang="tsx">
import { storeToRefs } from 'pinia';
// import { nextTick } from 'vue';
import { cloneDeep } from 'lodash-es';
// import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from '@/store/intelligence/creative/social_media_video/index.const';
// import type { PageInfo as IPageInfo } from 'tdesign-vue-next';
import CommonView from 'common/components/Layout/CommonView.vue';
// import DataContainer from 'common/components/Layout/DataContainer.vue';
import Container from '../components/Container/Index.vue';
import FullLoading from 'common/components/FullLoading';
import DataEmpty from 'common/components/NullAble/DataEmpty.vue';
import { useSocialMediaVideoStore } from '@/store/intelligence/creative/social_media_video/index-social-media-video';
import { nextTick, watch } from 'vue';
import { getImgByCdn, getDatabrainChannelImg } from '@/store/intelligence/creative/competitor/untis';
import CompetitorItem from '../components/CompetitorItem/Index.vue';
import { useRoute } from 'vue-router';
const route = useRoute();
const rStore = useSocialMediaVideoStore();
const {
  condition: conditionRef, conditionList, creativeList, isLoadingList,
} = storeToRefs(rStore);

watch(
  route,
  () => {
    const { query: { search_text: searchText, start_time: startTime, end_time: endTime } } = route;
    if (searchText && startTime && endTime) {
      conditionRef.value.cur = {
        ...conditionRef.value.cur,
        channel: cloneDeep(conditionRef.value.default.channel),
        search_text: searchText as string,
        time_range: [startTime as string, endTime as string],
      };
    } else {
      conditionRef.value.cur = cloneDeep(conditionRef.value.default);
    }
  },
  {
    deep: true,
    immediate: true,
  },
);
// v-model绑定表单的值
function formUpdateValue(value: typeof conditionRef) {
  conditionRef.value.cur = {
    ...conditionRef.value.cur,
    ...value,
  };
}
async function formReset() {
  rStore.resetFormFilter();
}

function formSubmit(formData: any) {
  console.log(formData);
  // console.log(DEFAULT_PAGE, DEFAULT_PAGE_SIZE);
  rStore.resetData();
  nextTick(() => {
    rStore.getSocialMediaVideoPage();
  });
};
// function onPageChange(current: number, pageInfo: IPageInfo) {
//   page.value = current;
//   pageSize.value = pageInfo.pageSize;
//   rStore.getSocialMediaVideoPage();
// };

const showDetail = (index: number) => {
  if (creativeList.value[index]) {
    window.open(creativeList.value[index]?.smv_content_url);
  };
};
</script>
<style scoped>
.list-container {
  background: #fff;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.03);
  border-radius: 12px;
  height: calc(100% - 24px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.my-load-box{
  height: calc(100% - 24px);
}
</style>
<style>
.intelligence-creative-smv-box .isolate {
  overflow: hidden!important;;
}
</style>


