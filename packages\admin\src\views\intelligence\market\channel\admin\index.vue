<template>
  <CommonView
    :store="store"
    :hide-right="true"
    class="w-full h-full"
    :router-index="-1"
    :tab-props="UPLOAD_PROPS"
  >
    <template #views>
      <Card>
        <Form
          :data="formData"
          :rules="UPLOAD_RULES"
          @submit="onSubmit"
          @reset="onReset"
        >
          <Space direction="vertical">
            <Text size="small" type="subTitle" content="Step 1: Please select an Excel file to upload" />
            <FormItem label="File" name="file">
              <Upload
                v-model="formData.file"
                :max="1"
                :request-method="requestMethod"
                accept=".csv"
                @change="onFileChange"
                @remove="onReset"
              />
            </FormItem>
            <div>
              Please upload an Excel file with the suffix .csv or
              <Link
                theme="primary" :underline="false"
                href="https://static.aix.intlgame.cn/intelligence/template/install6-channel-tempalate.csv"
              >
                view sample templates
              </Link>
            </div>
            <FormItem name="removeStatus">
              <Switch v-model="formData.removeStatus" />
              <Space>
                <div class="ml-2">Discard Non-Standard Country Data</div>
                <Tooltip
                  content="Standard Country data represents Countries that exist in the
                 public.report_country_region table"
                >
                  <SvgIcon name="error" size="20px" />
                </Tooltip>
              </Space>
            </FormItem>
            <FormItem name="viewStatus">
              <Space>
                <Button :disabled="!showReset" theme="primary" @click="() => { showDrawer = true }">
                  View
                  Status
                </Button>
                <Button :disabled="!showReset || store.isLoading" theme="primary" type="submit">
                  Submit
                </Button>
                <Button
                  v-if="showReset" theme="default" variant="base"
                  type="reset"
                >
                  Reset
                </Button>
              </Space>
            </FormItem>
            <Text size="small" type="subTitle" content="Explanation: " />
            <Text
              size="small"
              type="subTitle"
              content="1. The data is uploaded according to the month.
              If there is an error after parsing, it will be marked red.
              Please modify it and re-upload it before inserting;
              the data format can refer to the template,
              and the final data is inserted into 'public'.'report_market_channel';"
            />
            <Text
              size="small"
              type="subTitle"
              content="2. Delete data on a specified date, for example,
              delete data on 202208 and 202209
            DELETE FROM public.report_market_channel WHERE date in (20220801, 20220901);"
            />
            <Text
              v-if="filterCountryKey.length > 0"
              :content="`Non-Standard Country Abbreviations: ${filterCountryKey}`"
              theme="danger"
            />
          </Space>
        </Form>
        <Drawer
          v-model:visible="showDrawer"
          :footer="false"
          size-draggable
          size="large"
        >
          <template #header>
            <Text content="Task List" type="subTitle" />
            <Text class="ml-3" content="The task sheet being updated" />
          </template>
          <Table
            v-model:displayColumns="CHANNEL_TASK_METRIC"
            :pagination="taskPagination"
            :current-page="taskPagination.current"
            :horizontal-scroll-affixed-bottom="true"
            resizable
            row-key="sheet"
            :data="taskList"
            :columns="useChannelTaskTable({ data: taskList, retry: onRetry }).cols"
            @page-change="onTaskPageChange"
          />
        </Drawer>
      </Card>
      <Card>
        <Tabs
          v-if="tabList.length"
          :model-value="tabSelectId"
          theme="card"
          @change="onChange"
        >
          <TabPanel
            v-for="item in tabList"
            :key="item.value"
            :label="item.value.toString()"
            :value="item.value"
          >
            <slot :name="item.value">
              <Table
                v-model:displayColumns="CHANNEL_METRIC"
                :pagination="pagination"
                :horizontal-scroll-affixed-bottom="true"
                resizable
                row-key="country"
                :data="filterTableRecords()"
                :columns="columns"
                @page-change="onPageChange"
              />
            </slot>
          </TabPanel>
        </Tabs>
        <div v-else-if="tabList.length === 0" class="flex justify-center items-center">
          Please upload Excel file
        </div>
      </Card>
    </template>
  </CommonView>
</template>

<script setup lang="ts">
import { Card, Form, FormItem, Upload, Link, Switch, Space, Tooltip, Button, MessagePlugin, RequestMethodResponse, Tabs, TabPanel, PageInfo, Drawer, SubmitContext, Data } from 'tdesign-vue-next';
import { ref, reactive, computed } from 'vue';
import Text from 'common/components/Text';
import { ViewItem } from 'common/components/ViewTab';
import SvgIcon from 'common/components/SvgIcon';
import * as XLSX from 'xlsx';
import Table from 'common/components/table';
import { useChannelTaskTable } from '../compose/channelTask-table';
import { useIntelligenceUploadStore } from '@/store/intelligence/common/upload.store';
import CommonView from 'common/components/Layout/CommonView.vue';
import { UPLOAD_CHANNEL_RULES, UPLOAD_PROPS, UPLOAD_RULES, CHANNEL_TASK_STATUS, CHANNEL_TABLE_COLUMNS, CHANNEL_METRIC, CHANNEL_TASK_METRIC } from '../const/const';
import { UploadFile as TdUploadFile } from 'tdesign-vue-next/es/upload/type';
import { storeToRefs } from 'pinia';
import { Column } from '../../../user/modal/user';

const store = useIntelligenceUploadStore();
const showReset = ref<boolean>(false);
const table = reactive<{ records: any[], column: any[], submitRecords: any[] }>({
  records: [],
  column: [],
  submitRecords: [],
});
const { countryList } = storeToRefs(store);
const filterCountryKey = ref<string[]>([]);
const uploading = ref(false);
const showDrawer = ref(false);
const excelList = ref<{ sheet: string, data: unknown[] }[]>([]);
const formData = reactive({
  file: [] as File[],
  removeStatus: true,
  date: 2024,
  viewStatus: 0,
});
// Tab
const tabList = ref<ViewItem[]>([]);
const tabSelectId = ref<string | number>('');
const columns = ref<Column[]>(CHANNEL_METRIC.map((item) => {
  const column: Column = { colKey: item, title: item, sorter: true };
  column.width = 150;
  column.sorter = false;
  return column;
}));
const pagination = reactive({
  total: computed(() => filterTableRecords().length),
  pageSize: 10,
  current: 1,
});
// Task List
const taskList = ref<{ country: string, status: string | number }[]>([]);
const taskPagination = reactive({
  total: computed(() => taskList.value.length),
  pageSize: 10,
  current: 1,
});

const requestMethod = async (file: TdUploadFile):
Promise<RequestMethodResponse> => new Promise((resolve) => {
  uploading.value = true;
  const validate = UPLOAD_CHANNEL_RULES.find(type => file.type === type);
  if (!validate) {
    uploading.value = false;
    resolve({ status: 'fail', error: 'File format is not valid!', response: {} });
  } else {
    formData.file = [file.raw as File];
    uploading.value = false;
    resolve({ status: 'success', response: file });
  }
});

const readFile = (file: File):
Promise<{ list: { sheet: string, data: unknown[] }[], file: File }> => {
  const fileReader: FileReader = new FileReader();

  // Use readAsArrayBuffer to read the file as binary data
  fileReader.readAsArrayBuffer(file);

  // Return a Promise to handle the asynchronous nature of file reading
  return new Promise((resolve, reject) => {
    fileReader.onload = (event) => {
      try {
        const { result } = event.target as { result: ArrayBuffer };  // Use const assertion
        // Use UInt8Array to handle binary data
        const uint8Array = new Uint8Array(result);

        // Now you can use uint8Array with XLSX
        const workbook = XLSX.read(uint8Array, { type: 'array', cellDates: true });

        const list: { sheet: string, data: unknown[] }[] = [];
        for (const sheet of Object.keys(workbook.Sheets)) {
          if (Object.prototype.hasOwnProperty.call(workbook.Sheets, sheet)) {
            const data = XLSX.utils.sheet_to_json(workbook.Sheets[sheet], { header: 1, defval: '' });
            list.push({ sheet, data });
          }
        }

        // Resolve with the list and the original file
        resolve({ list, file });
      } catch (err) {
        // Handle errors
        reject(err);
      }
    };

    fileReader.onerror = (event: ProgressEvent<FileReader>) => {
      // Handle read errors
      reject(event.target?.error);
    };
  });
};

async function onFileChange(file: TdUploadFile[]) {
  tabList.value = [];
  taskList.value = [];
  filterCountryKey.value = [];
  const res = await readFile(file[0].raw as File) as
    { list: { sheet: string, data: unknown[] }[], file: File };
  excelList.value = res.list;
  setRenderData(res.list);
  showReset.value = true;
};

function formatDate(date: any) {
  if (Object.prototype.toString.call(date) !== '[object Date]') {
    return date;
  }
  const year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day = date.getDate();
  if (month < 10) {
    month = `0${month}`;
  }
  if (day < 10) {
    day = `0${day}`;
  }
  return `${year}/${month}/${day}`;
}

// 按照 某个字段进行分组
function grouping(list: any, field: any) {
  const groupList = Array.from(new Set(list.map((item: { [x: string]: any; }) => item[field])));

  const newList: { key: unknown; data: any; }[] = [];
  groupList.forEach((group) => {
    newList.push({ key: group, data: list.filter((item: { [x: string]: unknown; }) => item[field] === group) });
  });
  return newList;
}

function getRegion(country: string) {
  const conutryObj = countryList.value.find(({ country_abbre }) => country_abbre === country);
  if (conutryObj) return conutryObj;
  return;
}

const setRenderData = (list: { sheet: string, data: any[] }[]) => {
  const tempData: {
    category: string; date: string; platform: string;
    country: string; ranking: string; media_source: string; overall_percentage: string;
    region?: string;
  }[] = [];
  list.forEach(({ data }) => data.forEach((row, rowIndex) => {
    // 跳过表头
    if (rowIndex > 0) {
      tempData.push({
        category: row[0],
        date: formatDate(row[1]),
        platform: row[2],
        country: row[3],
        ranking: row[4],
        media_source: row[5],
        overall_percentage: row[6],
        region: getRegion(row[3])?.region_abbre,
      });
    }
  }));

  const records = [];
  const originRecords = grouping(tempData, 'country');

  if (formData.removeStatus) {
    // 如果开关为开代表以国家为优先级 不符合的筛掉
    const countryKey = countryList.value.map((one: { country_abbre: any }) => one.country_abbre);
    records.push(...originRecords.filter((e: { key: any; }) => countryKey.includes(e.key)));
    const key = originRecords
      .filter((e: { key: any; }) => !countryKey.includes(e.key))
      .map((one: { key: any; }) => one.key)
      .join(',');
    if (key) {
      filterCountryKey.value.push(key);
    }
  } else {
    records.push(...originRecords);
  }

  records.forEach((data, index) => {
    tabList.value.push({
      label: data.key as string,
      value: data.key as string,
      param: index,
    });
    taskList.value.push({
      country: data.key as string,
      status: CHANNEL_TASK_STATUS.WAITING,
    });
  });

  tabSelectId.value = tabList.value[0]?.value;
  table.records = records;
  table.submitRecords = records;
  table.column = CHANNEL_TABLE_COLUMNS;
};

const filterTableRecords = () => {
  if (table.records.length > 0) {
    return table.records.find(country => country.key === tabSelectId.value).data;
  };
  return [];
};

const onChange = (tabId: string | number) => {
  tabSelectId.value = tabId;
};

const onPageChange = (pageInfo: PageInfo) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
};

const onTaskPageChange = (pageInfo: PageInfo) => {
  taskPagination.current = pageInfo.current;
  taskPagination.pageSize = pageInfo.pageSize;
};

// Task List
function updateTask(name: string, ret: string | number) {
  taskList.value = taskList.value.map(({ country, status }) => {
    let newStatus: string | number = status;
    if (country === name) {
      newStatus = ret;
    }
    return {
      country,
      status: newStatus,
    };
  });
}

const onSubmit = async (context: SubmitContext<Data>) => {
  if (context.validateResult) {
    table.submitRecords = table.submitRecords.map((record: any) => ({
      ...record,
      data: record.data.map((item: { date: string }) => ({
        ...item,
        date: item.date.replace(/\//g, ''),
      })),
    }));

    for (const data of table.submitRecords) {
      updateTask(data.key, CHANNEL_TASK_STATUS.UPDATING);
      const res = await store.uploadChannelDataExcel(data.data, data.key);
      if (res) {
        updateTask(data.key, res.ret);
      }
    }
  } else {
    console.log('Errors: ', context.validateResult);
    MessagePlugin.warning(context.firstError!);
  }
};

const onRetry = async (country: string): Promise<void> => {
  const sheet = table.submitRecords.find(data => data.key === country);
  if (sheet) {
    updateTask(sheet.key, CHANNEL_TASK_STATUS.UPDATING);
    const res = await store.uploadChannelDataExcel(sheet.data, sheet.key);
    if (res) {
      updateTask(sheet.key, res.ret);
    }
  } else {
    MessagePlugin.warning('Sheet name does not exist!');
  }
};

const onReset = () => {
  formData.file = [] as File[];
  formData.viewStatus = 0;
  formData.removeStatus = true;
  showReset.value = false;
  filterCountryKey.value = [];
  tabList.value = [];
  taskList.value = [];
  MessagePlugin.success('Reset Successfully');
};
</script>
<style lang="scss" scoped>
// tab栏 背景色

:deep(.t-tabs__nav--card) {
  // @apply bg-white-secondary;
  float: right;
  background: white;
}

// 激活时
:deep(.t-tabs__nav--card.t-tabs__nav-item.t-is-active) {
  @apply bg-white-secondary p-[16px] rounded-t-large;

  .label {
    @apply text-black-primary;
  }

  .label-icon {
    stroke: var(--aix-text-color-black-primary);
    @apply text-black-primary;
  }
}

//去掉边框
:deep(.t-tabs__nav--card.t-tabs__nav-item) {
  @apply border-none p-[16px] rounded-t-large bg-transparent;
}

//tab之间的间距
:deep(.t-tabs__nav--card.t-tabs__nav-item:not(:first-of-type)) {
  @apply ml-[8px];
}

:deep(.t-tabs__nav--card) {
  @apply rounded-t-large;
}

:deep(.t-tabs) {
  @apply rounded-extraLarge;
  overflow: hidden;
}
</style>
