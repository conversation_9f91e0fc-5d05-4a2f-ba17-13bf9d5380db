import { ITableCols } from 'common/components/table/type';
import { Icon } from 'tdesign-icons-vue-next';
import { Space, Link, Tooltip } from 'tdesign-vue-next';
import { CHANNEL_TASK_STATUS } from '../const/const';
export function useChannelTaskTable({
  data,
  retry,
}: {data: { country: string, status: string | number }[];retry: (country: string) => Promise<void>}) {
  const tableData = data;
  const cols: ITableCols[] = [
    {
      colKey: 'country',
      title: 'Country',
      width: 200,
    },
    {
      colKey: 'status',
      title: 'Status',
      cell: (h: any, { row }: any) => (
            <Space>
              <div style={{ color: statusMessage(row).color }}>{statusMessage(row).msg}</div>
              {row.status === CHANNEL_TASK_STATUS.REPEAT
                ? <Tooltip content={'The constraint rules of the database are: UNIQUE ("date", "country", "variable", "value");please query in the database, for example: select * from public.report_user where date=xxx and country="xxx" and variable="xxx" and value=xxx;'}>
              <Icon style={{ color: '#e54545' }} name='error-circle' />
            </Tooltip> : null}
              {row.status === CHANNEL_TASK_STATUS.REPEAT
                ? <Link theme='primary' onClick={() => {
                  retry(row.country); return;
                }}>retry</Link> : null}
            </Space>
      ),
    },
  ];

  function statusMessage(row: {status: string|number, country: string}): { msg: string; color: string } {
    const data: Record<string | number, { msg: string; color: string }> = {
      [CHANNEL_TASK_STATUS.REPEAT]: { msg: 'Update failed: there is duplicate data, please check', color: '#e54545' },
      [CHANNEL_TASK_STATUS.UPDATING]: { msg: 'Updating...', color: '#006eff' },
      [CHANNEL_TASK_STATUS.SUCCESS]: { msg: 'Update succeeded', color: '#0ABF5B' },
      [CHANNEL_TASK_STATUS.WAITING]: { msg: 'Waiting', color: 'black' },
      [CHANNEL_TASK_STATUS.ERROR]: { msg: 'Update failed: the task may have timed out, please click when idle', color: '#ff7200' },
      [CHANNEL_TASK_STATUS.EMPTY]: { msg: `In ${row.country}, the data passing the verification is empty`, color: '#aacfff' },
    };
    return data[row.status] ?? { msg: '', color: 'black' };
  }

  return {
    cols,
    tableData,
  };
}
