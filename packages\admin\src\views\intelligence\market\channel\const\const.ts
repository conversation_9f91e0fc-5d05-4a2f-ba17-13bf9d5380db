import { defineAsyncComponent } from 'vue';

const components: any = {
  Select: defineAsyncComponent(() => import('common/components/Select')),
};

export const CHANNEL_FILTER_LABEL = {
  category: undefined,
  region: undefined,
  date: undefined,
};

export const CHANNEL_FILTER_CONDITION = [
  {
    name: components.Select,
    props: {
      list: [],
      title: 'Category',
      button: (textArr: string[] | string) => {
        if (!Array.isArray(textArr)) return textArr;
      },
    },
    ext: {
      key: 'category',
      label: 'Category',
      isAllowClose: false,
    },
  },
  {
    name: components.Select,
    props: {
      list: [],
      title: 'Region',
      button: (textArr: string[] | string) => {
        if (!Array.isArray(textArr)) return textArr;
      },
    },
    ext: {
      key: 'region',
      label: 'Region',
      isAllowClose: false,
    },
  },
  {
    name: components.Select,
    props: {
      list: [],
      title: 'Date',
      button: (textArr: string[] | string) => {
        if (!Array.isArray(textArr)) return textArr;
      },
    },
    ext: {
      key: 'date',
      label: 'Date',
      isAllowClose: false,
    },
  },
];

export const getChannelFilterList = ({ src, fieldObj }: { src: any[]; fieldObj: any }) => src.map((item) => {
  const { props = {}, ext: { key = '' } = {} } = item;
  let newProps = props;
  const list = (fieldObj as any)[key];

  switch (key) {
    case 'category':
      newProps = { ...props, list };
      break;
    case 'region':
      newProps = { ...props, list };
      break;
    case 'date':
      newProps = { ...props, list };
      break;
  }

  return { ...item, props: newProps };
});

export const CHANNEL_TAB_PROPS = {
  modelValue: 0,
  showNum: 1,
  list: [],
  shareParams: {},
  hideSaveBtn: true,
  hideShareBtn: true,
  hideShareView: true,
  customIconList: [],
};

export const CHANNEL_DEFAULT_FILTER = {
  category: '',
  region: '',
  date: '',
};

export const UPLOAD_PROPS = {
  modelValue: 0,
  showNum: 1,
  list: [],
  shareParams: {},
  hideSaveBtn: true,
  hideShareBtn: true,
  hideShareView: true,
  customIconList: [],
};

export const UPLOAD_CHANNEL_RULES = ['text/csv'];

export const UPLOAD_RULES = {
  file: [{ required: true, message: 'Please upload one excel file.' }],
  removeStatus: [{ required: false }],
  date: [{ required: true, message: 'Please choose a year.' }],
  viewStatus: [{ required: false }],
};

export const CHANNEL_TASK_STATUS = {
  WAITING: 'wait',
  UPDATING: 'update',
  SUCCESS: 0,
  REPEAT: 60000,
  ERROR: 50000,
  EMPTY: 'EMPTY',
};

export const CHANNEL_TABLE_COLUMNS = [
  { key: 'category', header: 'category' },
  { key: 'country', header: 'country' },
  { key: 'date', header: 'date' },
  { key: 'media_source', header: 'media_source' },
  { key: 'overall_percentage', header: 'Overall_Percentage' },
  { key: 'platform', header: 'platform' },
  { key: 'ranking', header: 'ranking' },
  { key: 'region', header: 'region' },
];

export const CHANNEL_TASK_METRIC = ['country', 'status'];

export const CHANNEL_METRIC = ['category', 'date', 'platform', 'country', 'ranking', 'media_source', 'overall_percentage'];
