<template>
  <common-view
    :hide-right="payload.isAdmin ? false : true"
    :store="store"
    :form-props="{
      modelValue: condition.cur,
      formList: filterList,
      'onUpdate:modelValue': formUpdateValue,
      onSubmit: formSubmit,
      onReset: formReset,
    }"
    :tab-props="CHANNEL_TAB_PROPS"
  >
    <template #subTitle>
      <div
        v-if="payload.isAdmin" class="right flex justify-between items-center cursor-pointer"
        @click="showAdminUpload()"
      >
        <div>
          <SvgIcon
            name="plus" size="12px" color="#4981f2"
            class="mr-[10px] cursor-pointer"
          />
        </div>
        <div class="text-brand font-medium">{{ 'Upload Data' }}</div>
      </div>
    </template>
    <template #views>
      <Row class="justify-center flex flex-wrap">
        <Col class="bg-white-primary w-full rounded-large p-[16px] flex gap-y-[16px] mb-6 min-h-[500px] justify-center">
          <BasicChart
            v-if="payload.regionPieData.length > 0 && !isLoading"
            class="min-w-full"
            chart-type="pie"
            detail-type="ringTextOut"
            :data-mode="DataMode.x"
            data-value-filed="value"
            data-item-field="name"
            tooltip-sort="desc"
            :tooltip-filter-zero="true"
            :data="payload.regionPieData ?? []"
            :tooltip-show-value="true"
            :title="{
              text: payload.regionPieData[0]?.region_en,
              left: 'center',
            }"
            is-show-legend
            :series="[
              {
                type: 'pie',
                label: {
                  normal: {
                    formatter: '{c}%',
                  }
                },
                data: payload.regionPieData ?? [],
                radius: [70, 100],
                itemStyle: {
                  borderRadius: 10,
                  borderColor: '#fff',
                  borderWidth: 1
                },
              }
            ]"
          />
          <DataEmpty v-else-if="!isLoading" class="w-full" />
          <FullLoading v-else class="rounded-large max-h-[450px]" />
        </Col>
        <Col class="bg-white-primary w-full rounded-t-lg p-[16px] flex">
          <Select
            v-model="payload.countryFilterInput"
            class="pl-2"
            :list="payload.countryFilterSelection"
            title="Country"
            :multiple="false"
            @update:model-value="(newValue: string) => store.getFilterCountryPie(newValue)"
          />
        </Col>
        <Col class="bg-white-primary w-full rounded-b-lg p-[16px] flex gap-y-[16px] mb-6 min-h-[500px]">
          <BasicChart
            v-if="payload.countryPieData.length > 0 && !isLoading && !pieLoading"
            class="min-w-full"
            chart-type="pie"
            detail-type="ringTextOut"
            :data-mode="DataMode.x"
            data-value-filed="value"
            data-item-field="name"
            tooltip-sort="desc"
            :tooltip-filter-zero="true"
            :tooltip-show-value="true"
            :data="payload.countryPieData ?? []"
            :title="{
              text: payload.countryPieData[0]?.country_en,
              left: 'center',
            }"
            is-show-legend
            :series="[
              {
                type: 'pie',
                label: {
                  normal: {
                    formatter: '{c}%',
                  }
                },
                data: payload.countryPieData ?? [],
                radius: [70, 100],
                itemStyle: {
                  borderRadius: 10,
                  borderColor: '#fff',
                  borderWidth: 1
                },
              }
            ]"
          />
          <DataEmpty v-else-if="!isLoading && !pieLoading" class="w-full" />
          <FullLoading v-else class="rounded-large max-h-[450px]" />
        </Col>
      </Row>
    </template>
  </common-view>
</template>
<script setup lang="ts">
import DataEmpty from 'common/components/NullAble/DataEmpty.vue';
import { defineAsyncComponent, ref, reactive, computed, watch } from 'vue';
import { cloneDeep } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { Row, Col } from 'tdesign-vue-next';
import Select from 'common/components/Select';
import { useRouter } from 'vue-router';
import SvgIcon from 'common/components/SvgIcon';
import FullLoading from 'common/components/FullLoading';
import { useIntelligenceMarketChannelStore } from '@/store/intelligence/market/channel/channel.store';
import CommonView from 'common/components/Layout/CommonView.vue';
import { DataMode } from 'common/components/BasicChart';
import { ChannelFormOptions, ChannelFormParams } from './modal/channel';
import { CHANNEL_DEFAULT_FILTER, CHANNEL_FILTER_CONDITION, CHANNEL_FILTER_LABEL, getChannelFilterList, CHANNEL_TAB_PROPS } from './const/const';

const BasicChart = defineAsyncComponent(() => import('common/components/BasicChart'));
const store = useIntelligenceMarketChannelStore();
const router = useRouter();
const { payload, pieLoading, isLoading } = storeToRefs(store);

function showAdminUpload() {
  router.push({
    path: '/intelligence/market/channel/admin',
  });
}

// 过滤器
const formOptions = ref<ChannelFormOptions>({
  fieldObj: cloneDeep(CHANNEL_FILTER_LABEL),
  conditionList: cloneDeep(CHANNEL_FILTER_CONDITION),
});
const condition = reactive<{ cur: ChannelFormParams; default: ChannelFormParams }>({
  cur: cloneDeep(CHANNEL_DEFAULT_FILTER),
  default: cloneDeep(CHANNEL_DEFAULT_FILTER),
});
const filterList = computed(() => getChannelFilterList({
  src: formOptions.value.conditionList,
  fieldObj: formOptions.value.fieldObj,
}));

function formUpdateValue(value: Partial<typeof condition.cur>) {
  condition.cur = {
    ...condition.cur,
    ...value,
  };
}

async function formSubmit(formData?: any) {
  const { date, region, category } = formData ?? {};
  await store.getFilterData(category, date, region);
}

async function formReset() {
  await store.init();
}

watch(
  [() => payload.value.option_obj],
  ([option]) => {
    const { dateInput, categoryInput, regionInput } = payload.value.conditionObj;
    formOptions.value.fieldObj = cloneDeep({
      date: option.dateList, region: option.regionList, category: option.categoryList,
    }) as any;
    condition.cur = {
      date: dateInput, region: regionInput, category: categoryInput,
    };
  },
);
</script>
<style lang='scss' scoped>
.chartStyle {
  min-height: 482px;
}
</style>
