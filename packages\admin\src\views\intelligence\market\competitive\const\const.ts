import { retBtn } from 'common/utils/common';
import { defineAsyncComponent } from 'vue';

const components: any = {
  Select: defineAsyncComponent(() => import('common/components/Select')),
  NewCascader: defineAsyncComponent(() => import('common/components/NewCascader')),
};

export const COMPETITIVE_FILTER_LABEL = {
  date: undefined,
  region: undefined,
  platform: undefined,
  category: undefined,
};

export const COMPETITIVE_FILTER_CONDITION = [
  {
    name: components.Select,
    props: {
      list: [],
      title: 'Category',
      button: (textArr: string[] | string) => {
        if (!Array.isArray(textArr)) return textArr;
      },
    },
    ext: {
      key: 'category',
      label: 'Category',
      isAllowClose: false,
    },
  },
  {
    name: components.NewCascader,
    props: {
      levelList: [
        { label: 'Region', value: 'region' },
        { label: 'Country/Market', value: 'country' },
      ],
      options: [],
      mode: 'level',
      title: 'Country/Market',
      isEmptyWhenSelectAll: true,
    },
    ext: {
      key: 'region',
      label: 'Country/Market',
      isAllowClose: false,
    },
  },
  {
    name: components.Select,
    props: {
      list: [],
      title: 'Game Terminal',
      button: (textArr: string[] | string) => {
        if (!Array.isArray(textArr)) return textArr;
      },
    },
    ext: {
      key: 'platform',
      label: 'Game Terminal',
      isAllowClose: false,
    },
  },
  {
    name: components.Select,
    props: {
      list: [],
      title: 'Month',
      button: (textArr: string[] | string) => {
        if (!Array.isArray(textArr)) return textArr;
      },
    },
    ext: {
      key: 'date',
      label: 'Month',
      isAllowClose: false,
    },
  },
];

export const getCompetitiveFilterList = ({ src, fieldObj }: { src: any[]; fieldObj: any }) => src.map((item) => {
  const { props = {}, ext: { key = '' } = {} } = item;
  let newProps = props;
  const list = (fieldObj as any)[key];
  switch (key) {
    case 'region':
      newProps = { ...props, options: fieldObj[key] };
      newProps.button = retBtn(key, list);
      break;
    case 'category':
      newProps = { ...props, list };
      break;
    case 'platform':
      newProps = { ...props, list };
      break;
    case 'date':
      newProps = { ...props, list };
      break;
  }
  return { ...item, props: newProps };
});

export const COMPETITIVE_TAB_PROPS = {
  modelValue: 0,
  showNum: 1,
  list: [],
  shareParams: {},
  hideSaveBtn: true,
  hideShareBtn: true,
  hideShareView: true,
  customIconList: [],
};
