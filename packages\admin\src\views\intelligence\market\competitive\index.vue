<template>
  <common-view
    :hide-right="true"
    :store="store"
    :form-props="{
      modelValue: condition.cur,
      formList: filterList,
      'onUpdate:modelValue': formUpdateValue,
      onSubmit: formSubmit,
      onReset: formReset,
    }"
    :tab-props="COMPETITIVE_TAB_PROPS"
  >
    <template #views>
      <Row>
        <Col class="bg-white-primary w-full rounded-large p-[16px] flex gap-y-[16px] mb-6 min-h-[650px]">
          <WorldMap
            v-if="payload.competitiveMap.length > 0 && !store.isLoading"
            :series="[{
              data: payload.competitiveMap.map(item => ({
                ...item,
                name: item.country_abbre,
                value: item.type,
              })),
              roam: 'false',
            }]"
            :visual-map="{
              inRange: {
                color: [
                  '#778899',
                  '#87CEFA',
                  '#90EE90',
                  '#F08080',] // 渐变颜色
              },
              itemHeight: '10px',
              left: '10px',
              orient: 'horizontal',
              min: 1,
              max: 4,
              bottom: '-5px',
              pieces: [
                { gt: 0, lte: 1, label: 'Dormant Market', color: '#778899' },
                { gt: 1, lte: 2, label: 'Blue sea Market', color: '#87CEFA' },
                { gt: 2, lte: 3, label: 'Opportunity Market', color: '#90EE90' },
                { gt: 3, lte: 4, label: 'Red sea Market', color: '#F08080' },
              ],
            }"
            :tooltip="{
              formatter(params: any) {
                return worldHtml(params);
              },
            }"
            class="min-w-full"
          />
          <DataEmpty v-else-if="!store.isLoading" class="w-full" />
          <FullLoading v-else class="rounded-large max-h-[500px]" />
        </Col>
      </Row>
    </template>
  </common-view>
</template>
<script setup lang="ts">
import DataEmpty from 'common/components/NullAble/DataEmpty.vue';
import { ref, reactive, computed, watch } from 'vue';
import { cloneDeep } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { Row, Col } from 'tdesign-vue-next';
import FullLoading from 'common/components/FullLoading';
import WorldMap from 'common/components/WorldMap/WorldMap.vue';
import { useIntelligenceMarketCompetitiveStore } from '@/store/intelligence/market/competitive/competitive.store';
import CommonView from 'common/components/Layout/CommonView.vue';
import { CompetitiveFormOptions, CompetitiveFormParams } from './modal/competitive';
import { COMPETITIVE_FILTER_CONDITION, COMPETITIVE_FILTER_LABEL, getCompetitiveFilterList, COMPETITIVE_TAB_PROPS } from './const/const';
import { CountryListModal, RegionOptionsModal, CountryOptionsModal } from '@/store/intelligence/market/common.d';

const store = useIntelligenceMarketCompetitiveStore();
const { payload } = storeToRefs(store);
const defaultFilter = reactive<CompetitiveFormParams>({
  date: '',
  region: [],
  platform: '',
  category: '',
});

// 过滤器
const formOptions = ref<CompetitiveFormOptions>({
  fieldObj: cloneDeep(COMPETITIVE_FILTER_LABEL),
  conditionList: cloneDeep(COMPETITIVE_FILTER_CONDITION),
});
const condition = reactive<{ cur: CompetitiveFormParams; default: CompetitiveFormParams }>({
  cur: cloneDeep(defaultFilter),
  default: cloneDeep(defaultFilter),
});
const filterList = computed(() => getCompetitiveFilterList({
  src: formOptions.value.conditionList,
  fieldObj: formOptions.value.fieldObj,
}));

function formUpdateValue(value: Partial<typeof condition.cur>) {
  condition.cur = {
    ...condition.cur,
    ...value,
  };
}

async function formSubmit(formData?: any) {
  await store.getFilterData(
    formData.region.country ?? [],
    formData.date, formData.region.region ?? [], formData.category, formData.platform,
  );
}

async function formReset() {
  await store.init();
}

const getRegionCountryOption = (allCountryList: CountryListModal[]) => {
  const regionOptions: RegionOptionsModal[] = [];
  const countryOptions: CountryOptionsModal = {};

  allCountryList.forEach(({ region_en, region_abbre, country_en, country_abbre }) => {
    if (!regionOptions.some(option => option.value === region_abbre)) {
      regionOptions.push({
        label: region_en ?? region_abbre,
        value: region_abbre,
      });
    }
    countryOptions[region_abbre] = countryOptions[region_abbre] || [];
    countryOptions[region_abbre].push({
      label: country_en,
      value: country_abbre,
    });
  });

  regionOptions.forEach((region) => {
    // eslint-disable-next-line no-param-reassign
    region.children = countryOptions[region.value];
  });
  return { regionOptions };
};

// 国家详细数据信息框
function worldHtml(val: any) {
  const { data = {} } = val;

  let html = '<div class="MarketAll">';
  html += `<div class="ltvHtml"><span>${val.name}:  ${data.type_name ?? ''}</span>`;
  html += '</div>';
  return html;
}

watch(
  [() => payload.value.conditionCountry, () => payload.value.dateList,
    () => payload.value.categoryList, () => payload.value.platformList],
  ([conditionCountry, dateList, category, platform]) => {
    const { regionOptions } = getRegionCountryOption(conditionCountry);
    formOptions.value.fieldObj = cloneDeep({
      date: dateList, region: regionOptions, platform, category,
    }) as any;
    condition.cur = {
      date: dateList[0].value,
      region: regionOptions?.flatMap(
        item => (item?.children ?? []).map(item => item.value)),
      platform: platform[0].value,
      category: category[0].value,
    };
  },
);
</script>
<style lang='scss' scoped>
.chartStyle {
  min-height: 482px;
}

.MarketAll {
  .market {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ccc;
    padding: 5px 0;
    width: 100%;
    cursor: pointer;
  }
}

#worldMap {
  position: relative;
}

.world_words {
  position: absolute;
  left: 15px;
  bottom: 15px;
  font-weight: bolder;
  font-size: large;
}
</style>
