<template>
  <common-view
    :hide-right="true"
    :store="store"
    :form-props="{
      modelValue: condition.cur,
      formList: filterList,
      'onUpdate:modelValue': formUpdateValue,
      onSubmit: formSubmit,
      onReset: formReset,
    }"
    :tab-props="OS_TAB_PROPS"
  >
    <template #views>
      <Row>
        <Col class="bg-white-primary w-full rounded-t-lg p-[16px] flex gap-y-[16px] min-h-[500px]">
          <BasicChart
            v-if="payload.barData.length > 0 && !store.isLoading"
            key="1"
            class="min-w-full"
            chart-type="bar"
            detail-type="''"
            :data-mode="DataMode.x"
            data-value-filed="download"
            data-item-field="platform"
            :tooltip-filter-zero="true"
            is-legend-bar-bottom
            data-group-item-field="region_en"
            :data="payload.barData ?? []"
            is-show-legend
            :y-axis-label-format="(value: string) => {
              return value.toLocaleString();
            }"
            y-axis-name="Total"
            :y-axis-name-gap="80"
            :reg-rules="[{
              name: 'download', value: ['s1000', 'decimal']
            }]"
            :legend-props="{ top: 'bottom', left: 'center', labelRotate: 60, icon: 'circle' }"
            :y-axis="yAxis"
          />
          <DataEmpty v-else-if="!store.isLoading" class="w-full" />
          <FullLoading v-else class="rounded-large max-h-[450px]" />
        </Col>
        <Col class="bg-white-primary w-full rounded-b-lg p-[16px] flex gap-y-[16px] mb-6 min-h-[500px]">
          <BasicChart
            v-if="payload.barData.length > 0 && !store.isLoading"
            key="2"
            class="min-w-full"
            chart-type="bar"
            detail-type="stack"
            :data-mode="DataMode.x"
            data-value-filed="percentage"
            data-item-field="platform"
            :tooltip-filter-zero="true"
            is-legend-bar-bottom
            data-group-item-field="region_en"
            :data="payload.percentageBarData ?? []"
            is-show-legend
            :y-axis-label-format="(value: string) => {
              return value.toLocaleString();
            }"
            y-axis-name="Total"
            :y-axis-name-gap="80"
            :legend-props="{ top: 'bottom', left: 'center', labelRotate: 60, icon: 'circle' }"
            :y-axis="yAxis2"
            is-show-bar-label
            :bar-label-formatter="(param: any) => param.value + '%'"
            :bar-max-width="80"
            bar-label-position="inside"
          />
          <DataEmpty v-else-if="!store.isLoading" class="w-full" />
          <FullLoading v-else class="rounded-large max-h-[450px]" />
        </Col>
        <Col class="bg-white-primary w-full rounded-large p-[16px] flex gap-y-[16px] mb-6 min-h-[500px]">
          <data-container
            class="w-full"
            :total="table.pageInfo.total"
            :page-size="table.pageInfo.pageSize"
            :default-page="table.pageInfo.pageIndex"
            :page-size-options="[10, 20, 30, 50, 100, 200]"
            hide-header
            @on-page-change="onPageChange"
          >
            <Table
              v-if="table.records.length > 0 && !store.isLoading"
              v-model:displayColumns="tableColumns.displayColumns"
              class="w-full"
              row-key="index"
              :data="table.records"
              :total="table.pageInfo.total"
              :columns="tableColumns.columns"
              :loading="tableLoading"
            />
            <DataEmpty v-else-if="!store.isLoading" class="w-full" />
            <FullLoading v-else class="rounded-large max-h-[450px]" />
          </data-container>
        </Col>
      </Row>
    </template>
  </common-view>
</template>
<script setup lang="ts">
import DataEmpty from 'common/components/NullAble/DataEmpty.vue';
import { defineAsyncComponent, ref, reactive, computed, watch } from 'vue';
import { cloneDeep } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { Row, Col } from 'tdesign-vue-next';
import FullLoading from 'common/components/FullLoading';
import Table from 'common/components/table';
import DataContainer from 'common/components/Layout/DataContainer.vue';
import { useIntelligenceMarketOSStore } from '@/store/intelligence/market/os/os.store';
import CommonView from 'common/components/Layout/CommonView.vue';
import { DataMode } from 'common/components/BasicChart';
import { CountryListModal, CountryOptionsModal, RegionOptionsModal } from '@/store/intelligence/market/common.d';
import { OSFormOptions, OSFormParams } from './modal/os';
import { getOSFilterList, OS_DEFAULT_FILTER, OS_FILTER_CONDITION, OS_FILTER_LABEL, OS_TAB_PROPS } from './const/const';

const BasicChart = defineAsyncComponent(() => import('common/components/BasicChart'));
const store = useIntelligenceMarketOSStore();
const { table, payload, tableLoading, tableColumns } = storeToRefs(store);
const yAxis = computed(() => [
  {
    type: 'value',
    data: payload.value.barData.map(item => item.download),
    axisLabel: {
      formatter(a: number) {
        return a.toLocaleString();
      },
    },
  }]);
const yAxis2 = computed(() => [
  {
    type: 'value',
    data: payload.value.percentageBarData.map(item => item.percentage),
    axisLabel: {
      formatter(a: number) {
        return `${a}%`;
      },
    },
  }]);

// 过滤器
const formOptions = ref<OSFormOptions>({
  fieldObj: cloneDeep(OS_FILTER_LABEL),
  conditionList: cloneDeep(OS_FILTER_CONDITION),
});
const condition = reactive<{ cur: OSFormParams; default: OSFormParams }>({
  cur: cloneDeep(OS_DEFAULT_FILTER),
  default: cloneDeep(OS_DEFAULT_FILTER),
});
const filterList = computed(() => getOSFilterList({
  src: formOptions.value.conditionList,
  fieldObj: formOptions.value.fieldObj,
}));

function formUpdateValue(value: Partial<typeof condition.cur>) {
  condition.cur = {
    ...condition.cur,
    ...value,
  };
}

async function formSubmit(formData?: any) {
  const { date = [], region = [], platform, category } = formData ?? {};
  await store.getFilterData(category, date, region?.region ?? [], platform, region?.country ?? []);
}

async function formReset() {
  await store.init();
}

// 处理切换页面
const onPageChange = async (current: number, info: any) => {
  table.value.pageInfo.pageIndex = current;
  table.value.pageInfo.pageSize = info.pageSize;
  const { categoryInputList, countryInputList, platformInputList,
    dateInputList, regionInputList } = payload.value.conditionObj;
  await store.getTable(
    regionInputList, categoryInputList, countryInputList,
    { pageIndex: current, pageSize: info.pageSize }, dateInputList, platformInputList,
  );
};

const getRegionCountryOption = (allCountryList: CountryListModal[]) => {
  const regionOptions: RegionOptionsModal[] = [];
  const countryOptions: CountryOptionsModal = {};

  allCountryList.forEach(({ region_en, region_abbre, country_en, country_abbre }) => {
    if (!regionOptions.some(option => option.value === region_abbre)) {
      regionOptions.push({
        label: region_en ?? region_abbre,
        value: region_abbre,
      });
    }
    countryOptions[region_abbre] = countryOptions[region_abbre] || [];
    countryOptions[region_abbre].push({
      label: country_en,
      value: country_abbre,
    });
  });

  regionOptions.forEach((region) => {
    // eslint-disable-next-line no-param-reassign
    region.children = countryOptions[region.value];
  });
  return { regionOptions };
};

watch(
  [() => payload.value.conditionObj],
  ([filter]) => {
    const { dateInputList, categoryInputList, platformInputList, regionInputList, countryInputList } = filter;
    const option = payload.value.option_obj;
    const { regionOptions } = getRegionCountryOption(payload.value.conditionCountry);
    formOptions.value.fieldObj = cloneDeep({
      date: option.dateList, region: regionOptions, category: option.categoryList, platform: option.platformList,
    }) as any;
    condition.cur = {
      date: dateInputList,
      region: { region: regionInputList, country: countryInputList },
      category: categoryInputList, platform: platformInputList,
    };
  },
);
</script>
<style lang='scss' scoped>
.chartStyle {
    min-height: 482px;
}
</style>
