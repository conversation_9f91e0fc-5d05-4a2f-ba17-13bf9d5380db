import { retBtn } from 'common/utils/common';
import { defineAsyncComponent } from 'vue';

const components: any = {
  Select: defineAsyncComponent(() => import('common/components/Select')),
  NewCascader: defineAsyncComponent(() => import('common/components/NewCascader')),
};

export const SCALE_FILTER_LABEL = {
  date: undefined,
  region: undefined,
};

export const SCALE_FILTER_CONDITION = [
  {
    name: components.NewCascader,
    props: {
      levelList: [
        { label: 'Region', value: 'region' },
        { label: 'Country/Market', value: 'country' },
      ],
      options: [],
      mode: 'level',
      title: 'Country/Market',
      isEmptyWhenSelectAll: true,
    },
    ext: {
      key: 'region',
      label: 'Country/Market',
      isAllowClose: false,
    },
  },
  {
    name: components.Select,
    props: {
      list: [],
      title: 'Year',
      button: (textArr: string[] | string) => {
        if (!Array.isArray(textArr)) return textArr;
      },
    },
    ext: {
      key: 'date',
      label: 'Year',
      isAllowClose: false,
    },
  },
];

export const getScaleFilterList = ({ src, fieldObj }: { src: any[]; fieldObj: any }) => src.map((item) => {
  const { props = {}, ext: { key = '' } = {} } = item;
  let newProps = props;
  const list = (fieldObj as any)[key];
  switch (key) {
    case 'region':
      newProps = { ...props, options: fieldObj[key] };
      newProps.button = retBtn(key, list);
      break;
    case 'date':
      newProps = { ...props, list };
      break;
  }
  return { ...item, props: newProps };
});

export const SCALE_TAB_PROPS = {
  modelValue: 0,
  showNum: 1,
  list: [],
  shareParams: {},
  hideSaveBtn: true,
  hideShareBtn: true,
  hideShareView: true,
  customIconList: [],
};

export const SCALE_METRIC = ['date', 'region_en', 'country_en', 'total_population',
  'total_online_population', 'total_players', 'total_payers', 'pc_players',
  'mobile_players', 'console_players', 'pc_payers', 'mobile_payers', 'console_payers'];

export const SCALE_COL_METRIC = {
  date: 'Date',
  console_payers: 'Console Payers',
  console_players: 'Console Players',
  country_en: 'Country',
  region_en: 'Region',
  mobile_payers: 'Mobile Payers',
  mobile_players: 'Mobile Players',
  pc_payers: 'PC Payers',
  pc_players: 'PC Players',
  total_online_population: 'Online Population',
  total_payers: 'Payers',
  total_players: 'Players',
  total_population: 'Population',
};
