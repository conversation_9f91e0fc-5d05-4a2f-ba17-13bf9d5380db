<template>
  <common-view
    :hide-right="true"
    :store="store"
    :form-props="{
      modelValue: condition.cur,
      formList: filterList,
      'onUpdate:modelValue': formUpdateValue,
      onSubmit: formSubmit,
      onReset: formReset,
    }"
    :tab-props="SCALE_TAB_PROPS"
  >
    <template #views>
      <Row>
        <Col class="bg-white-primary w-full rounded-large p-[16px] flex gap-y-[16px] mb-6 min-h-[500px]">
          <BasicChart
            v-if="payload.barData.length > 0 && !store.isLoading"
            class="min-w-full"
            chart-type="bar"
            detail-type="y"
            :data-mode="DataMode.y"
            data-value-filed="value"
            data-item-field="name"
            :tooltip-filter-zero="true"
            :y-axis-name-gap="80"
            y-axis-name="Total"
            is-legend-bar-bottom
            data-group-item-field="region"
            :data="payload.barData ?? []"
            :legend-props="{
              labelRotate: 60,
              top: 'bottom',
              left: 'center',
            }"
            is-show-legend
            :y-axis="yAxis"
            :reg-rules="[{ name: 'value', value: ['s1000', 'decimal'] }]"
          />
          <DataEmpty v-else-if="!store.isLoading" class="w-full" />
          <FullLoading v-else class="rounded-large max-h-[450px]" />
        </Col>
        <Col class="bg-white-primary w-full rounded-large p-[16px] flex gap-y-[16px] mb-6 min-h-[650px]">
          <WorldMap
            v-if="payload.scaleMap.length > 0 && !store.isLoading"
            :series="[{
              data: payload.scaleMap.map(item => ({
                ...item,
                name: item.country_en,
                value: parseInt(item.total_players, 10),
              }))
            }]"
            :tooltip="{
              formatter(params: any) {
                return worldHtml(params);
              },
            }"
            class="min-w-full"
          >
            <template #rangeLabel>
              <div className="world_words">Players</div>
            </template>
          </WorldMap>
          <DataEmpty v-else-if="!store.isLoading" class="w-full" />
          <FullLoading v-else class="rounded-large max-h-[500px]" />
        </Col>
        <Col class="bg-white-primary w-full rounded-large p-[16px] flex gap-y-[16px] mb-6 min-h-[500px]">
          <data-container
            class="w-full"
            :total="table.pageInfo.total"
            :page-size="table.pageInfo.pageSize"
            :default-page="table.pageInfo.pageIndex"
            :page-size-options="[10, 20, 30, 50, 100, 200]"
            hide-header
            @on-page-change="onPageChange"
          >
            <Table
              v-if="(table.records as []).length > 0 && !store.isLoading"
              v-model:display-columns="tableColumns.displayColumns"
              class="w-full"
              row-key="index"
              :data="table.records"
              :total="table.pageInfo.total"
              :columns="tableColumns.columns"
              :loading="tableLoading"
            />
            <DataEmpty v-else-if="!store.isLoading" class="w-full" />
            <FullLoading v-else class="rounded-large max-h-[450px]" />
          </data-container>
        </Col>
      </Row>
    </template>
  </common-view>
</template>
<script setup lang="ts">
import DataEmpty from 'common/components/NullAble/DataEmpty.vue';
import { defineAsyncComponent, ref, reactive, computed, watch } from 'vue';
import { cloneDeep } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { Row, Col } from 'tdesign-vue-next';
import FullLoading from 'common/components/FullLoading';
import WorldMap from 'common/components/WorldMap/WorldMap.vue';
import Table from 'common/components/table';
import DataContainer from 'common/components/Layout/DataContainer.vue';
import { useIntelligenceMarketScaleStore } from '@/store/intelligence/market/scale/scale.store';
import CommonView from 'common/components/Layout/CommonView.vue';
import { DataMode } from 'common/components/BasicChart';
import { SCALE_FILTER_CONDITION, SCALE_FILTER_LABEL, getScaleFilterList, SCALE_TAB_PROPS } from './const/const';
import { ScaleFormOptions, ScaleFormParams } from './modal/scale';
import { CountryListModal, CountryOptionsModal, RegionOptionsModal } from '@/store/intelligence/market/common.d';

const BasicChart = defineAsyncComponent(() => import('common/components/BasicChart'));
const store = useIntelligenceMarketScaleStore();
const { table, payload, tableLoading, conditionObj, tableColumns } = storeToRefs(store);
const yAxis = computed(() => [
  {
    type: 'value',
    data: payload.value.barData.map(item => item.value),
    axisLabel: {
      formatter(a: number) {
        return a.toLocaleString();
      },
    },
  }]);
const defaultFilter = reactive<ScaleFormParams>({
  date: '',
  region: [],
});

// 过滤器
const formOptions = ref<ScaleFormOptions>({
  fieldObj: cloneDeep(SCALE_FILTER_LABEL),
  conditionList: cloneDeep(SCALE_FILTER_CONDITION),
});
const condition = reactive<{ cur: ScaleFormParams; default: ScaleFormParams }>({
  cur: cloneDeep(defaultFilter),
  default: cloneDeep(defaultFilter),
});
const filterList = computed(() => getScaleFilterList({
  src: formOptions.value.conditionList,
  fieldObj: formOptions.value.fieldObj,
}));

function formUpdateValue(value: Partial<typeof condition.cur>) {
  condition.cur = {
    ...condition.cur,
    ...value,
  };
}

async function formSubmit(formData?: any) {
  await store.getFilterData(formData.region.country ?? [], formData.date, formData.region.region ?? []);
}

async function formReset() {
  await store.init();
}

// 处理切换页面
const onPageChange = async (current: number, info: any) => {
  table.value.pageInfo.pageIndex = current;
  table.value.pageInfo.pageSize = info.pageSize;
  await store.getTable(
    conditionObj.value.countryInputList, payload.value.conditionCountry,
    { pageIndex: current, pageSize: info.pageSize },
  );
};

const getRegionCountryOption = (allCountryList: CountryListModal[]) => {
  const regionOptions: RegionOptionsModal[] = [];
  const countryOptions: CountryOptionsModal = {};

  allCountryList.forEach(({ region_en, region_abbre, country_en, country_abbre }) => {
    if (!regionOptions.some(option => option.value === region_abbre)) {
      regionOptions.push({
        label: region_en ?? region_abbre,
        value: region_abbre,
      });
    }
    countryOptions[region_abbre] = countryOptions[region_abbre] || [];
    countryOptions[region_abbre].push({
      label: country_en,
      value: country_abbre,
    });
  });

  regionOptions.forEach((region) => {
    // eslint-disable-next-line no-param-reassign
    region.children = countryOptions[region.value];
  });
  return { regionOptions };
};

// 国家详细数据信息框
function worldHtml(val: any) {
  const { data = {} } = val;
  let html = '<div class="MarketAll">';
  html += `<div class="market"><span>${val.name}</span></div>`;
  if (data.total_players > 0) {
    html += `<div class="font-bold"> Players: ${Number(data.total_players).toLocaleString()}</div>`;
    html += `<div>-Mobile:${Number(data.mobile_players).toLocaleString()}</div>
          <div>-PC: ${Number(data.pc_players).toLocaleString()}</div>
          <div>-Console:  ${Number(data.console_players).toLocaleString()}</div>   <br />`;

    html += `<div class="font-bold"> Payers: ${Number(data.total_payers).toLocaleString()}</div>`;
    html
        += `<div>-Mobile:  ${Number(data.mobile_payers).toLocaleString()}</div>
          <div>-PC: ${Number(data.pc_payers).toLocaleString()}</div>
          <div>-Console:  ${Number(data.console_payers).toLocaleString()}</div>`;

    html += '</div>';
  } else {
    html += '0</div>';
  }

  html += '</div>';
  return html;
}

watch(
  [() => payload.value.conditionCountry, () => payload.value.dateList],
  ([conditionCountry, dateList]) => {
    const { regionOptions } = getRegionCountryOption(conditionCountry);
    formOptions.value.fieldObj = cloneDeep({
      date: dateList, region: regionOptions,
    }) as any;
    condition.cur = {
      date: dateList[0].value,
      region: regionOptions?.flatMap(
        item => (item?.children ?? []).map(item => item.value)),
    };
  },
);
</script>
<style lang='scss' scoped>
  .chartStyle {
    min-height: 482px;
  }

  .world_words {
    position: absolute;
    left: 15px;
    bottom: 6px;
    font-weight: bolder;
    font-size: large;
  }

  .MarketAll {
    .market {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #ccc;
      padding: 5px 0;
      width: 100%;
      cursor: pointer;
    }
  }

  #worldMap {
    position: relative;
  }

  .market {
    font-weight: bolder;
    color: #1b1b1b;
    border-bottom: 1px solid #1b1b1b;
  }

  .span-title {
    font-weight: bolder;
    color: #1b1b1b;
  }
</style>
