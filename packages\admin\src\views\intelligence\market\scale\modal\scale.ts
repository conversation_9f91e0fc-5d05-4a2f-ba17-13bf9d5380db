import { OptionsItem } from 'common/types/cascader';

export type ScaleFormOptions = {
  fieldObj: {
    region?: OptionsItem[];
    date?: OptionsItem;
  };
  conditionList: any[];
};

export type ScaleFormParams = {
  date?: string;
  region?: Array<string | number>;
};

// World Map
export type ScaleWorldMap = {
  console_payers: string,
  console_players: string,
  country_abbre: string,
  country_en: string,
  mobile_payers: string,
  mobile_players: string,
  name: string,
  pc_payers: string,
  pc_players: string,
  region_abbre: string,
  region_en: string,
  total_payers: string,
  total_players: string,
  value: number,
};
