import { retBtn } from 'common/utils/common';
import { defineAsyncComponent } from 'vue';

const components: any = {
  Select: defineAsyncComponent(() => import('common/components/Select')),
  CommonSelect: defineAsyncComponent(() => import('common/components/creative/search/CommonInputSelect')),
  NewCascader: defineAsyncComponent(() => import('common/components/NewCascader')),
};

export const TREND_FILTER_LABEL = {
  category: undefined,
  region: undefined,
  platform: undefined,
  date: undefined,
};

export const TREND_FILTER_CONDITION = [
  {
    name: components.CommonSelect,
    props: {
      list: [],
      multiple: true,
      title: 'Category',
      button: (textArr: string[] | string) => {
        if (textArr.length === 0) {
          return 'All';
        }
        if (!Array.isArray(textArr)) return textArr;
        return textArr.length > 2 ? textArr.length : textArr.join(',');
      },
    },
    ext: {
      key: 'category',
      label: 'Category',
      isAllowClose: false,
    },
  },
  {
    name: components.NewCascader,
    props: {
      levelList: [
        { label: 'Region', value: 'region' },
        { label: 'Country/Market', value: 'country' },
      ],
      options: [],
      mode: 'level',
      title: 'Country/Market',
      isEmptyWhenSelectAll: true,
    },
    ext: {
      key: 'region',
      label: 'Country/Market',
      isAllowClose: false,
    },
  },
  {
    name: components.Select,
    props: {
      list: [],
      title: 'Game Terminal',
      button: (textArr: string[] | string) => {
        if (!Array.isArray(textArr)) return textArr;
      },
    },
    ext: {
      key: 'platform',
      label: 'Game Terminal',
      isAllowClose: false,
    },
  },
  {
    name: components.CommonSelect,
    props: {
      list: [],
      multiple: true,
      title: 'Month',
      button: (textArr: string[] | string) => {
        if (textArr.length === 0) {
          return 'All';
        }
        if (!Array.isArray(textArr)) return textArr;
        return textArr.length > 2 ? textArr.length : textArr.join(',');
      },
    },
    ext: {
      key: 'date',
      label: 'Month',
      isAllowClose: false,
    },
  },
];

export const getTrendFilterList = ({ src, fieldObj }: { src: any[]; fieldObj: any }) => src.map((item) => {
  const { props = {}, ext: { key = '' } = {} } = item;
  let newProps = props;
  const list = (fieldObj as any)[key];

  switch (key) {
    case 'category':
      newProps = { ...props, list };
      newProps.button = retBtn(key, list);
      break;
    case 'region':
      newProps = { ...props, options: fieldObj[key] };
      newProps.button = retBtn(key, list);
      break;
    case 'date':
      newProps = { ...props, list };
      newProps.button = retBtn(key, list);
      break;
    case 'platform':
      newProps = { ...props, list };
      break;
  }

  return { ...item, props: newProps };
});

export const TREND_TAB_PROPS = {
  modelValue: 0,
  showNum: 1,
  list: [],
  shareParams: {},
  hideSaveBtn: true,
  hideShareBtn: true,
  hideShareView: true,
  customIconList: [],
};

export const TREND_DEFAULT_FILTER = {
  category: [],
  region: [],
  platform: '',
  date: [],
};
