<template>
  <common-view
    :hide-right="true"
    :store="store"
    :form-props="{
      modelValue: condition.cur,
      formList: filterList,
      'onUpdate:modelValue': formUpdateValue,
      onSubmit: formSubmit,
      onReset: formReset,
    }"
    :tab-props="TREND_TAB_PROPS"
  >
    <template #views>
      <Row>
        <Col class="bg-white-primary w-full rounded-large p-[16px] flex gap-y-[16px] mb-6 min-h-[500px]">
          <ComboChart
            v-if="payload.lineBarData.length > 0 && !store.isLoading"
            class="min-w-full"
            :chart-type="['bar', 'bar', 'bar', 'line']"
            :detail-type="['', '', '', 'smooth']"
            :data-mode="DataMode.x"
            data-value-filed="value"
            data-item-field="label"
            :tooltip-filter-zero="true"
            is-legend-bar-bottom
            data-group-item-field="date"
            :data="payload.lineBarData ?? []"
            is-show-legend
            :y-axis-label-format="(value: string) => {
              return value.toLocaleString();
            }"
            :legend-props="{ top: 'bottom', left: 'center', }"
            :grid="{ bottom: '10%', containLabel: true, left: 40, right: 60 }"
            :y-axis-secondary-max-config="{ max: null, interval: null }"
            :y-axis-first-max-config="{ max: null, interval: null }"
            :reg-rules="[{ name: 'value', value: ['s1000', 'decimal'] }]"
          />
          <DataEmpty v-else-if="!store.isLoading" class="w-full" />
          <FullLoading v-else class="rounded-large max-h-[450px]" />
        </Col>
        <Col class="bg-white-primary w-full rounded-large p-[16px] flex gap-y-[16px] mb-6 min-h-[500px]">
          <data-container
            class="w-full"
            :total="table.pageInfo.total"
            :page-size="table.pageInfo.pageSize"
            :default-page="table.pageInfo.pageIndex"
            :page-size-options="[10, 20, 30, 50, 100, 200]"
            hide-header
            @on-page-change="onPageChange"
          >
            <Table
              v-if="table.records.length > 0 && !store.isLoading"
              v-model:displayColumns="store.tableColumns.displayColumns"
              class="w-full"
              row-key="index"
              :data="table.records"
              :total="table.pageInfo.total"
              :columns="store.tableColumns.columns"
              :loading="tableLoading"
            />
            <DataEmpty v-else-if="!store.isLoading" class="w-full" />
            <FullLoading v-else class="rounded-large max-h-[450px]" />
          </data-container>
        </Col>
      </Row>
    </template>
  </common-view>
</template>
<script setup lang="ts">
import DataEmpty from 'common/components/NullAble/DataEmpty.vue';
import { defineAsyncComponent, ref, reactive, computed, watch } from 'vue';
import { cloneDeep } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { Row, Col } from 'tdesign-vue-next';
import FullLoading from 'common/components/FullLoading';
import Table from 'common/components/table';
import DataContainer from 'common/components/Layout/DataContainer.vue';
import { useIntelligenceMarketTrendStore } from '@/store/intelligence/market/trend/trend.store';
import CommonView from 'common/components/Layout/CommonView.vue';
import { TrendFormOptions, TrendFormParams } from './modal/trend';
import { TREND_FILTER_CONDITION, TREND_FILTER_LABEL, getTrendFilterList, TREND_TAB_PROPS, TREND_DEFAULT_FILTER } from './const/const';
import { DataMode } from 'common/components/BasicChart';
import { CountryListModal, CountryOptionsModal, RegionOptionsModal } from '@/store/intelligence/market/common.d';

const ComboChart = defineAsyncComponent(() => import('common/components/ComboChart'));
const store = useIntelligenceMarketTrendStore();
const { table, payload, tableLoading } = storeToRefs(store);

// 过滤器
const formOptions = ref<TrendFormOptions>({
  fieldObj: cloneDeep(TREND_FILTER_LABEL),
  conditionList: cloneDeep(TREND_FILTER_CONDITION),
});
const condition = reactive<{ cur: TrendFormParams; default: TrendFormParams }>({
  cur: cloneDeep(TREND_DEFAULT_FILTER),
  default: cloneDeep(TREND_DEFAULT_FILTER),
});
const filterList = computed(() => getTrendFilterList({
  src: formOptions.value.conditionList,
  fieldObj: formOptions.value.fieldObj,
}));

function formUpdateValue(value: Partial<typeof condition.cur>) {
  condition.cur = {
    ...condition.cur,
    ...value,
  };
}

async function formSubmit(formData?: any) {
  const { date = [], region = {}, platform, category } = formData ?? {};
  await store.getFilterData(region?.country ?? [], date, region?.region ?? [], platform, category);
}

async function formReset() {
  await store.init();
}

// 处理切换页面
const onPageChange = async (current: number, info: any) => {
  table.value.pageInfo.pageIndex = current;
  table.value.pageInfo.pageSize = info.pageSize;
  const { categoryInputList, countryInputList, platformInputList,
    dateInputList, regionInputList } = payload.value.conditionObj;
  await store.getTable(
    regionInputList, categoryInputList, countryInputList,
    { pageIndex: current, pageSize: info.pageSize }, dateInputList, platformInputList,
  );
};

const getRegionCountryOption = (allCountryList: CountryListModal[]) => {
  const regionOptions: RegionOptionsModal[] = [];
  const countryOptions: CountryOptionsModal = {};

  allCountryList.forEach(({ region_en, region_abbre, country_en, country_abbre }) => {
    if (!regionOptions.some(option => option.value === region_abbre)) {
      regionOptions.push({
        label: region_en ?? region_abbre,
        value: region_abbre,
      });
    }
    countryOptions[region_abbre] = countryOptions[region_abbre] || [];
    countryOptions[region_abbre].push({
      label: country_en,
      value: country_abbre,
    });
  });

  regionOptions.forEach((region) => {
    // eslint-disable-next-line no-param-reassign
    region.children = countryOptions[region.value];
  });
  return { regionOptions };
};

watch(
  [() => payload.value.conditionObj],
  ([filter]) => {
    const { dateInputList, categoryInputList, platformInputList, regionInputList, countryInputList } = filter;
    const option = payload.value.option_obj;
    const { regionOptions } = getRegionCountryOption(option.saveCountryInputList);
    formOptions.value.fieldObj = cloneDeep({
      date: option.dateList, region: regionOptions, category: option.categoryList, platform: option.platformList,
    }) as any;
    condition.cur = {
      date: dateInputList,
      region: { region: regionInputList, country: countryInputList },
      category: categoryInputList,
      platform: platformInputList,
    };
  },
);
</script>
<style lang='scss' scoped>
.chartStyle {
  min-height: 482px;
}
</style>
