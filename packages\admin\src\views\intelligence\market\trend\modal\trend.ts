import { OptionsItem } from 'common/types/cascader';

export type TrendFormOptions = {
  fieldObj: {
    category?: OptionsItem[];
    platform?: OptionsItem;
    region?: OptionsItem[];
    date?: OptionsItem[];
  };
  conditionList: any[];
};

export type TrendFormParams = {
  date?: Array<string>;
  region?: Array<string | number> | { region: string[], country: string[] };
  category?: Array<string>;
  platform?: string;
};
