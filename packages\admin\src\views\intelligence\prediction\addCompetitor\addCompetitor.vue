<template>
  <div class="bg-white-primary rounded-large p-[16px] h-full overflow-y-auto flex flex-col gap-y-[16px]">
    <div v-if="props.steps?.current === StepsSequenceItem.addCompetitor">
      <CompetitorMenu
        :is-loading="false"
        :input="keyWord"
        :data="dropdown as any"
        :close-admin-tab="true"
        :search-status="searchStatus"
        :competitor-list="selectedCompetitor as any"
        :tab-name="tabName"
        :origin-data="options as any"
        :next-status="0"
        :show-next="false"
        :user-search-selected-status="store.userSearchSelectedStatus"
        @delete-competitor="onRemove"
        @add-competitor="onChange"
        @input-change="getKeyword"
        @click-radio="clickRadio"
        @click-search="getKeyword"
        @clear-key-word="clearKeyWord"
      />
      <!-- <t-row>
        <t-col
          :offset="4"
          :span="4"
        >
          <Select
            :on-input-change="getKeyword"
            :value="showSelectedOption()"
            placeholder="Enter the competitor name"
            :loading="loading"
            :popup-props="{
              overlayInnerClassName: ['narrow-scrollbar'],
              overlayInnerStyle: {
                maxHeight: '280px',
                overflowY: 'auto',
                overscrollBehavior: 'contain',
                padding: '6px',
              },
            }"
            clearable
            filterable
            multiple
            @change="onChange"
          >
            <template v-if="options.length">
              <Option
                v-for="item in dropdown"
                :key="item.competitor_code"
                :value="item.competitor_code"
                :label="item.label"
              >
                <div class="tdesign-demo__user-option">
                  <img :src="item.competitor_icon">
                  <div class="tdesign-demo__user-option-info">
                    <div>{{ item.label }}</div>
                  </div>
                </div>
              </Option>
            </template>
            <template v-else>
              <div class="tdesign-demo__select-empty-multiple">
                No data available
              </div>
            </template>
            <template #suffixIcon>
              <icon name="search" />
            </template>
          </Select>
        </t-col>
      </t-row>
      <div>
        <t-row :gutter="12" class="h-96">
          <t-col
            v-for="item in selectedCompetitor"
            :key="item.competitor_code"
            class="my-4"
            :span="2"
          >
            <Card
              :id="item.competitor_code"
              card-type="selector"
              :title="item.competitor_name"
              :avatar="item.competitor_icon"
              :bordered="false"
              @on-remove="onRemove"
            />
          </t-col>
        </t-row>
        <t-divider />
      </div> -->
      <t-row
        class="mt-2"
        justify="end"
        :gutter="12"
      >
        <t-col>
          <t-space>
            <t-button
              variant="outline"
              @click="()=>displayCancel=true"
            >
              Cancel
            </t-button>
            <t-button
              @click="()=>handleChange(StepsSequenceItem.evaluation)"
            >
              Next
            </t-button>
          </t-space>
        </t-col>
      </t-row>
      <Dialog
        v-model:visible="displayCancel"
        theme="info"
        body="Are you sure to return to settings, changes wouldn't save"
        header="Sure to Return"
        :confirm-on-enter="true"
        :on-cancel="onCancelSave"
        :on-confirm="onConfirmCancel"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref, toRef } from 'vue';
import { Dialog, MessagePlugin } from 'tdesign-vue-next';
import {
  AddCompetitorModal,
  InputOptionsModal,
  CompetitorExportListModal,
} from '../modal/prediction';
import { StepsSequenceItem, AddCompetitorProps } from '../const/const';
import { useIntelligencePredictionStore } from '@/store/intelligence/prediction/prediction.store';
import CompetitorMenu from '../../components/AddCompetitorMenu.vue';
import { SearchStatus } from '@/store/intelligence/creative/competitor/competitor.const';
import { useRouter } from 'vue-router';

const props: AddCompetitorModal = defineProps(AddCompetitorProps);
let selectedCompetitor = toRef(props.data, 'selectedCompetitor').value;
const selectedGroupCompetitor = toRef(props.data, 'selectedGroupCompetitor').value;
const groupCompetitor = toRef(props.data, 'groupCompetitor');
const store = useIntelligencePredictionStore();
const route = useRouter();
const displayCancel = ref(false);
const options = ref<CompetitorExportListModal[]>([]);
let dropdown: (InputOptionsModal & CompetitorExportListModal)[] = reactive([]);
const keyWord = ref<string>('');
const tabName = ref<string>('list');
const searchStatus = ref<number>(SearchStatus.LOADING);
let debounceTimeout: NodeJS.Timeout | null = null;

const emit = defineEmits(['handleChangePage']);

const onChange = (item: CompetitorExportListModal) => {
  // Find competitors to be added
  const data = selectedCompetitor.find(competitor => competitor.competitor_code === item.competitor_code);
  if (!data) {
    selectedCompetitor.push({
      competitor_code: item.competitor_code,
      competitor_name: item.competitor_name,
      competitor_icon: item.competitor_icon,
      dashboard: item.dashboard,
      market: '',
      market_score: null,
      commercial_score: null,
      similarity_score: null,
      group_name: '',
      steam_id: item.steam_id,
      competitor_type: item.competitor_type,
    });
    if (groupCompetitor.value.groups.find(group => group.competitors.length !== selectedCompetitor.length)) {
      groupCompetitor.value.groups.forEach((group) => {
        group.competitors.push({
          competitor_code: item.competitor_code,
          competitor_name: item.competitor_name,
          competitor_icon: item.competitor_icon,
          dashboard: item.dashboard,
          steam_id: item.steam_id,
          competitor_type: item.competitor_type,
          market: group.market ?? '',
          market_score: null,
          commercial_score: null,
          similarity_score: null,
          group_name: group.group_name ?? '',
        });
      });
    }
    options.value = options.value.filter(data => data.competitor_code !== item.competitor_code);
    dropdown = dropdown.filter(data => data.competitor_code !== item.competitor_code);
    store.userSearchSelectedStatus = true;
  }
};

const clickRadio = (val: 'list' |'detail') => {
  tabName.value = val;
};

const clearKeyWord = () => {
  keyWord.value = '';
};

const handleChange = (page: number) => {
  if (selectedCompetitor.length < 3) {
    MessagePlugin.error('Please choose more than 3 competitor');
  } else {
    emit('handleChangePage', { selectedCompetitor, selectedGroupCompetitor, groupCompetitor: groupCompetitor.value, page });
  }
};

const onConfirmCancel = () => {
  route.go(0);
  displayCancel.value = false;
};

const onCancelSave = () => {
  displayCancel.value = false;
};

// CRUD operation
const getKeyword = async (word: string) => {
  keyWord.value = word;
  searchStatus.value = SearchStatus.LOADING;

  if (!word) {
    setTimeout(() => {
      keyWord.value = '';
    }, 500);
    return;
  }

  // Clear the previous timeout if exists
  if (debounceTimeout) {
    clearTimeout(debounceTimeout);
  }

  // Set a new timeout
  // eslint-disable-next-line @typescript-eslint/no-misused-promises
  debounceTimeout = setTimeout(async () => {
    try {
      const response = await store.getCompetitorBySearch(word);
      // eslint-disable-next-line function-paren-newline
      options.value = response.filter(
        item => !selectedCompetitor.some(value => value.competitor_code === item.competitor_code));

      dropdown = options.value.map(competitor => ({
        label: competitor.competitor_name,
        value: competitor.competitor_code,
        ...competitor,
      }));
    } catch (error) {
      // Handle API error
      console.error('API error:', error);
    }

    searchStatus.value = SearchStatus.SUCCESS;
  }, 500); // Adjust the delay as needed
  store.userSearchSelectedStatus = false;
};

onMounted(async () => {
  if (props.data.selectedCompetitor.length > 0) {
    props.data.selectedCompetitor.forEach((item) => {
      dropdown.push({
        ...item,
        label: item.competitor_name,
        value: item.competitor_code,
        competitor_type: 'mobile',
        steam_id: '',
      });
      options.value.push({
        ...item,
        competitor_type: 'mobile',
        steam_id: '',
      });
    });
  }
});

const onRemove = (code: CompetitorExportListModal) => {
  selectedCompetitor = selectedCompetitor.filter(item => item.competitor_code !== code.competitor_code);
  groupCompetitor.value.groups.forEach((group) => {
  // eslint-disable-next-line no-param-reassign
    group.competitors = group.competitors.filter(game => game.competitor_code !== code.competitor_code);
  });
  if (keyWord.value !== '') getKeyword(keyWord.value);
  else options.value = [];
};
</script>
<style lang="scss" scoped>
.tdesign-demo__user-option {
  display: flex;
}

.tdesign-demo__user-option > img {
  max-width: 40px;
  max-height: 40px;
  border-radius: 50%;
}

.tdesign-demo__user-option-desc {
  font-size: 14px;
  color: var(--td-text-color-secondary);
}

.tdesign-demo__user-option-info {
  margin-left: 16px;
}

.tdesign-demo-select__overlay-option .t-select-option {
  height: 100%;
  padding: 8px;
}

.tdesign-demo__panel-options-multiple {
  width: 100%;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.tdesign-demo__panel-options-multiple .t-checkbox {
  display: flex;
  border-radius: 3px;
  line-height: 22px;
  cursor: pointer;
  padding: 3px 8px;
  color: var(--td-text-color-primary);
  transition: background-color 0.2s linear;
  white-space: nowrap;
  word-wrap: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0;
}
.tdesign-demo__panel-options-multiple .t-checkbox:hover {
  background-color: var(--td-bg-color-container-hover);
}
</style>
