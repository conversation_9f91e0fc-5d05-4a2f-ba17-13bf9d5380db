<template>
  <FullLoading v-if="store.isLoading || loading" />
  <div v-if="props.steps.current === StepsSequenceItem.evaluation">
    <Dialog
      :key="'prediction_custom_dialog_one'"
      :on-confirm="() => removeTab(removeID)"
      :on-cancel="() => showConfirmRemoveMarket = false"
      :on-close-btn-click="() => showConfirmRemoveMarket = false"
      header="Confirm Remove Market?"
      :visible="showConfirmRemoveMarket"
    />
    <Tabs
      :value="tabSelectId"
      :list="(selectedGroupCompetitor as any)"
      @change="changeTab"
      @remove-tab="(id: number) => showConfirmDialog(id)"
      @add-tab="addTab"
    >
      <template v-for="(item, index) in selectedGroupCompetitor" :key="index" #[item.value?.toString()]>
        <div class="bg-white-primary p-[16px] h-full overflow-y-auto flex flex-col gap-y-[16px]">
          <Button class="w-fit" theme="primary" @click="addTab">
            <template #icon><add-icon /></template>
            Add Market Group
          </Button>
          <Text size="l" type="title" content="Release Market" />
          <NewCascader
            :model-value="selectedMarket"
            :options="allMarket"
            :level-list="levelList"
            :is-empty-when-select-all="false"
            title="Market"
            mode="tree"
            class="w-fit"
            @update:model-value="updateModelValue"
          />
          <Text size="l" type="title" content="Competitor Evaluation" />
          <Table
            row-key="index"
            :display-columns="displayCols(useEvaluationTable({ data: item.competitors }).cols)"
            :columns="useEvaluationTable({ data: item.competitors }).cols"
            :data="displayData(useEvaluationTable({ data: item.competitors }).tableData, index)"
            table-layout="auto"
          />
          <div v-if="props.data.gameId">
            <Text size="l" type="title" content="View" />
            <Button
              class="float-right"
              theme="primary"
              shape="square"
              variant="base"
              @click="() => refresh()"
            >
              <refresh-icon />
            </Button>
          </div>
          <div v-if="props.data.gameId && !store.isLoading" class="flex">
            <div class="basis-1/2">
              <div class="text-center"><Text content="P-Downloads" /></div>
              <BasicChart
                :chart-type="graphState.chartType"
                :data-mode="graphState.dataMode"
                :y-axis-label-format="(e: number) => yAxisLabelFormat(e, false)"
                :tooltip-value-format="(e: number) => yAxisLabelFormat(e, false)"
                data-value-filed="value"
                data-item-field="name"
                tooltip-sort="desc"
                data-group-item-field="xLabel"
                :data="graphState.chartData ?? []"
                is-show-legend
                is-legend-bar-bottom
                :legend-props="{ top: 'bottom', left: 'center', }"
                :grid="{ bottom: '10%', containLabel: true, left: 20, right: 20 }"
                :reg-rules="[{ name: 'value', value: ['s1000'] }]"
                :tooltip-filter-zero="false"
              />
            </div>
            <div class="basis-1/2">
              <div class="text-center"><Text content="P-LTV" /></div>
              <BasicChart
                :chart-type="graph2State.chartType"
                :data-mode="graph2State.dataMode"
                :y-axis-label-format="(e: number) => yAxisLabelFormat(e, true)"
                :tooltip-value-format="(e: number) => yAxisLabelFormat(e, true)"
                data-value-filed="value"
                data-item-field="name"
                tooltip-sort="desc"
                data-group-item-field="xLabel"
                :data="graph2State.chartData ?? []"
                is-show-legend
                is-legend-bar-bottom
                :legend-props="{ top: 'bottom', left: 'center', }"
                :grid="{ bottom: '10%', containLabel: true, left: 20, right: 20 }"
                :reg-rules="[{ name: 'value', value: ['s1000'] }]"
                :tooltip-filter-zero="false"
              />
            </div>
          </div>
        </div>
      </template>
    </Tabs>
    <t-divider />
    <t-row justify="end" :gutter="12">
      <t-col :span="4" class="text-end">
        <t-space>
          <Button theme="default" variant="base" @click="() => displayCancel = true">
            Cancel
          </Button>
          <Button
            theme="default" variant="base"
            @click="handleChangePage(selectedCompetitor, StepsSequenceItem.addCompetitor)"
          >
            Previous
          </Button>

          <Button :loading="loading" @click="() => displayVersionInput = true"> Save </Button>
        </t-space>
        <t-dialog
          :key="'prediction_custom_dialog_two'"
          v-model:visible="displayVersionInput"
          header="Version Name"
          :confirm-on-enter="true"
          :on-cancel="onCancel"
          :on-confirm="createOrUpdateGame"
        >
          <t-input v-model="input" type="text" size="large" />
        </t-dialog>
      </t-col>
    </t-row>
    <Dialog
      :key="'prediction_custom_dialog_three'"
      v-model:visible="displayCancel"
      theme="info"
      body="Are you sure to return to settings, changes wouldn't save"
      header="Sure to Return"
      :confirm-on-enter="true"
      :on-cancel="onCancelSave"
      :on-confirm="onConfirmCancel"
    />
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref, toRef, watch, onMounted, defineAsyncComponent } from 'vue';
import { RefreshIcon, AddIcon } from 'tdesign-icons-vue-next';
import {
  CompetitorExportListModal,
  CompetitorListGroupContentModal,
  CompetitorListGroupModal,
  EvaluationLineGraph,
  EvaluationModal,
  InputOptionsModal,
  Market,
  MarketFilter,
} from '../modal/prediction';
import { ALLDAYS, ALLPLATFORM, EvaluationProps, StepsSequenceItem } from '../const/const';
import { useIntelligencePredictionStore } from '@/store/intelligence/prediction/prediction.store';
import { storeToRefs } from 'pinia';
import { useGlobalGameStore } from '@/store/global/game.store';
import Tabs from '../components/Tabs.vue';
import Text from 'common/components/Text';
import NewCascader, { ILevel, IModelValueTree } from 'common/components/NewCascader';
import { worldCodeAlias as worldCode, WORLD_OPTIONS as world } from 'common/components/WorldMap/const';
import Table from 'common/components/table';
import { useEvaluationTable } from '@/views/intelligence/prediction/compose/evaluation-table';
import { ITableCols } from 'common/components/table/type';
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
} from 'echarts/components';
import { LineChart } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import { DataMode } from 'common/components/BasicChart';
import { Dialog, MessagePlugin, Button } from 'tdesign-vue-next';
import { useRouter } from 'vue-router';
import { useWatchGameChange } from 'common/compose/request/game';
import { useGoto } from '@/router/goto';
import FullLoading from 'common/components/FullLoading';

const props: EvaluationModal = defineProps(EvaluationProps);
const store = useIntelligencePredictionStore();
const emit = defineEmits(['handleChangePage']);
const { gameCode } = storeToRefs(useGlobalGameStore());
const BasicChart = defineAsyncComponent(() => import('common/components/BasicChart'));
const loading = ref<boolean>(false);
const router = useRouter();
const showConfirmRemoveMarket = ref<boolean>(false);
const removeID = ref<number>(0);
const { goToPredictionOverview } = useGoto();
const previousGameCode = ref<string>(gameCode.value);
// Version Name Input
const displayVersionInput = ref<boolean>(false);
const input = ref<string>('');
const graphState = reactive({
  chartType: 'line',
  chartData: [] as EvaluationLineGraph[],
  dataMode: 'x' as DataMode,
  xAxisName: '',
  yAxisName: '',
  isShowDataZoom: true,
  showLable: false,
});

const graph2State = reactive({
  chartType: 'line',
  chartData: [] as EvaluationLineGraph[],
  dataMode: 'x' as DataMode,
  xAxisName: '',
  yAxisName: '',
  isShowDataZoom: true,
  showLable: false,
});

// Competitor
const tabSelectId = ref(0);
const competitorCode = ref([]);
const selectedCompetitor = toRef(props.data, 'selectedCompetitor');
const groupCompetitor = toRef(props.data, 'groupCompetitor');
const selectedGroupCompetitor = ref<(CompetitorListGroupModal & InputOptionsModal)[]>([]);

// Market
const selectedMarket = ref<IModelValueTree[]>([]);
let allMarket = reactive<Market[]>(world);
const levelList: ILevel[] = [
  {
    label: 'Region',
    value: 'region',
  },
  {
    label: 'Country',
    value: 'country',
  },
];

// ltv,PDownloads
echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
]);
const group = ref<any[]>([]);
const market = ref<any[]>([]);
const allDataSpecify = ref<any>({});
const selectedKeys = ref<any[]>([]);
const sortJson = ref<any>({});
const displayCancel = ref(false);

// Version Name Input
const onCancel = () => {
  displayVersionInput.value = false;
};

async function fetchGame() {
  let gameJson: any[] = [];
  const groupData = await store.getCompetitorByCode(gameCode.value);
  if (groupData) {
    groupData.forEach((ele: any) => {
      if (ele.game_id === props.data.gameId) {
        gameJson = [...ele.groups];
      }
    });

    const worldAll: any = [];
    const worldG: any = [];
    const worldC: any = [];
    group.value.length = 0;
    market.value.length = 0;
    gameJson.forEach((item) => {
      const cc = JSON.parse(item.market);
      cc[99].forEach((element: any) => {
        if (!worldG.includes(element[0])) {
          worldG.push(element[0]);
        }
        if (!worldC.includes(element[1])) {
          worldC.push(element[1]);
          worldAll.push(element);
        }
      });

      const similarityALl = {
        score: 0,
      };
      item.competitors.forEach((v: { similarity_score: number; }) => {
        const num = similarityALl.score + v.similarity_score;
        similarityALl.score = num;
      });
      market.value.push(...cc[99]);
      market.value = [...market.value];
      group.value.push([item.competitors, cc[99], similarityALl]);
      group.value = [...group.value];
    });

    if (group.value.length > 0) {
      const code = group.value[0][0].map((item: { competitor_code: any; }) => item.competitor_code);
      const dashboard = group.value[0][0].filter((v: { dashboard: number; }) => v.dashboard !== 0);
      allDataSpecify.value.code = code;
      allDataSpecify.value.market = market.value;
      allDataSpecify.value.group = group.value;
      allDataSpecify.value.dashboard = dashboard;
      const a: any = [];
      allDataSpecify.value.group[0][0].forEach((element: { competitor_name: any; }, i: number) => {
        if (i < 9) {
          a.push(element.competitor_name);
        }
      });
      selectedKeys.value = a;
      competitorCode.value = code;
      await downloadCode(code);
    }
  }
}

async function downloadCode(code: any[]) {
  const configData = await store.getCompetitorConfigDetails();
  if (configData) {
    allDataSpecify.value.downloadCode = configData;
    await fetchLTV(code);
  }
}

async function fetchLTV(code: any[]) {
  const ltvData = await store.getLtvDetails({ day: ALLDAYS.join(','), competitor: code.toString() });
  if (ltvData) {
    const dataDE = ltvData as any;
    if (!allDataSpecify.value.arr || typeof allDataSpecify.value.arr !== 'object' || Array.isArray(allDataSpecify.value.arr)) {
      allDataSpecify.value.arr = {};
    }

    ALLDAYS.forEach((day) => {
      const dayKey = `day${day}`;
      if (!allDataSpecify.value.arr[dayKey]) {
        allDataSpecify.value.arr[dayKey] = {};
      }
      market.value.forEach((country) => {
        if (!allDataSpecify.value.arr[dayKey][country[1]]) {
          allDataSpecify.value.arr[dayKey][country[1]] = {};
          ALLPLATFORM.forEach((platform) => {
            allDataSpecify.value.arr[dayKey][country[1]][platform] = [];
          });
        }
      });
    });

    const dJson = dataDE as any;
    market.value.forEach((market) => {
      allDataSpecify.value.code.forEach((competitorCode: any) => {
        if (!dJson[competitorCode]?.[market[1]]) {
          dJson[competitorCode][market[1]] = [];
          ALLDAYS.forEach((day) => {
            ALLPLATFORM.forEach((platform) => {
              dJson[competitorCode][market[1]].push({ platform, t: day, ltv: 0, download: 0 });
              allDataSpecify.value.arr[`day${day}`][market[1]][platform].push(competitorCode);
            });
          });
        } else {
          if (dataDE[competitorCode]?.[market[1]]) {
            dataDE[competitorCode][market[1]].forEach((val: any) => {
              if (val.ltv === 0) {
                allDataSpecify.value.arr[`day${val.t}`][market[1]][val.platform].push(competitorCode);
              }
            });
          }
        }
      });
    });
    await ltv(dJson);
  }
}

async function ltv(data: any) {
  allDataSpecify.value.lineChart = {
    x: [],
    ltv: {},
    download: {},
  }; // 折线图

  // code q,s
  const cj: any[][] = [];
  const jp: any = {};
  group.value.forEach((item, index) => {
    cj[index] = item[1].map((val: any[]) => val[1]);
  });
  group.value.forEach((gitem, gindex) => {
    // 循环组，gitem:组详情 ，iindex组id
    // 每个国家的ltv ltv国家 = 市场值 * 相似度 / 竞品相似度总和 * 该竞品国家平台天数ltv + 重复
    jp[gindex] = {};
    jp[gindex].dp = {};
    ALLPLATFORM.forEach((platform: number) => {
      jp[gindex].dp[platform] = {};
    });
    gitem[0].forEach((gameVal:
    {
      competitor_code: string | number;
      commercial_score: number;
      similarity_score: number;
      market_score: number;
    }) => {
      // 循环竞品， gameVal:竞品详情。;
      const competitorCode = gameVal.competitor_code;
      jp[gindex][competitorCode] = {};
      cj[gindex].forEach((country: string) => {
        // index为第几组，0标识竞品 ,v标识竞品参数
        if (!jp[gindex][competitorCode][country]) {
          jp[gindex][competitorCode][country] = {};
          ALLPLATFORM.forEach((platform: number) => {
            jp[gindex][competitorCode][country][platform] = {};
          });
        }

        const countryKey = `${country}A`;
        if (!jp[gindex][competitorCode][countryKey]) {
          jp[gindex][competitorCode][countryKey] = {};
          ALLPLATFORM.forEach((platform: number) => {
            jp[gindex][competitorCode][countryKey][platform] = {};
          });
        }

        if (data[competitorCode]) {
          data[competitorCode][country].forEach((dataVV:
          {
            platform: number;
            t: number;
            ltv: number;
            download: number;
          }) => {
            // 总数据中第gameVal.competitor_code的竞品中第Market2Val的国家 ,dataVV标识竞品参数
            ALLPLATFORM.forEach((platform: number) => {
              jp[gindex][competitorCode][`${country}A`][platform][dataVV.t] = dataVV.ltv * dataVV.download;
              jp[gindex][competitorCode][country][platform][dataVV.t] = {
                ltv: ((gameVal.commercial_score * gameVal.similarity_score)
                      / group.value[gindex][2].score.toFixed(2))
                      * dataVV.ltv,
                download: dataVV.download,
              };
              const num1 = jp[gindex].dp[platform][country] > 0 ? jp[gindex].dp[platform][country] : 0;
              if (dataVV.download * gameVal.market_score > num1 && dataVV.t === 360) {
                jp[gindex].dp[platform][country] = dataVV.download * gameVal.market_score;
                jp[gindex].dp[platform][`game${country}`] = {
                  game: competitorCode,
                  market_score: gameVal.market_score,
                };
              }
            });
          });
        };
      });
    });
  });
  sortJson.value.jp = jp;
  allDataSpecify.value.jp = sortJson.value.jp;
}

function lineChart() {
  const platform = ALLPLATFORM[2];
  const daysArr = {
    d: Object.fromEntries(ALLDAYS.map(day => [day, 0])),
    ltv: Object.fromEntries(ALLDAYS.map(day => [day, 0])),
    cp_d: Object.fromEntries(ALLDAYS.map(day => [day, 0])),
    cp_dcode: Object.fromEntries(ALLDAYS.map(day => [day, 0])),
    downloadC: Object.fromEntries(ALLDAYS.map(day => [day, 0])),
  };
  // 折线图
  // 柱状折现对比图
  // 绘制图表
  allDataSpecify.value.lineChart2 = {
    x: [],
    ltv: {},
    download: {},
  }; // 折线图2
  const ltvData: any = {};
  const ltvData2: any = {};

  if (
    market.value.length > 0
    && market.value[0].length > 0
    && sortJson.value.jp
    && Object.keys(sortJson.value.jp).length === group.value.length
  ) {
    market.value.forEach((element: any) => {
      const country = element[1];
      ltvData[country] = {};
      daysArr.cp_d = Object.fromEntries(ALLDAYS.map(day => [day, 0]));
      daysArr.cp_dcode = Object.fromEntries(ALLDAYS.map(day => [day, 0]));
      ALLDAYS.forEach((day) => {
        ltvData[country][day] = {
          ltv: 0,
          download: 0,
        };
      });
      group.value.forEach((item, i) => {
        if (sortJson.value.jp[i].dp[platform][`game${country}`]) {
          const gameCode = sortJson.value.jp[i].dp[platform][`game${country}`].game;
          ALLDAYS.forEach((day) => {
            daysArr.d[day] += sortJson.value.jp[i][gameCode][country][platform][360].download
              * sortJson.value.jp[i].dp[platform][`game${country}`].market_score
              * (allDataSpecify.value.downloadCode[day] || 0);
          });
        }

        allDataSpecify.value.code.forEach((competitorCode: any) => {
          if (sortJson.value.jp[i][competitorCode][country]) {
            ALLDAYS.forEach((day) => {
              ltvData[country][day].ltv += sortJson.value.jp[i][competitorCode][country][platform][day].ltv;
              ltvData[country][day].download += sortJson.value.jp[i][competitorCode][country][platform][day].download;
            });

            item[0].forEach((sc: { competitor_code: any; market_score: number; }) => {
              if (sc.competitor_code === competitorCode) {
                ALLDAYS.forEach((day: any) => {
                  const downloadRecord = sortJson.value.jp[i][competitorCode][country][platform][day];
                  if (downloadRecord.download * sc.market_score > daysArr.cp_d[day]) {
                    daysArr.cp_d[day] = downloadRecord.download * sc.market_score;
                    daysArr.cp_dcode[day] = allDataSpecify.value.downloadCode[day];
                  }
                });
              }
            });
          }
        });
      });

      const dataD: number[] = [];
      const dataLtv: (string | number)[] = [];
      ALLDAYS.forEach((day) => {
        const dayKey = `day${day}`;
        const countryKey = country;
        const platformLength = allDataSpecify.value.group[0][0].length;
        const currentLtv = ltvData[countryKey][day].ltv;

        if (allDataSpecify.value.arr[dayKey][countryKey][platform].length < platformLength) {
          if (isNaN(Number(daysArr.ltv[day]))) {
            daysArr.ltv[day] = currentLtv * daysArr.cp_d[day];
          } else {
            daysArr.ltv[day] += currentLtv * daysArr.cp_d[day];
          }
        } else if (daysArr.ltv[day] === 0) {
          daysArr.ltv[day] = 0;
        }

        daysArr.downloadC[day] += daysArr.cp_d[day];
        dataD.push(parseFloat(ltvData[country][day].download.toFixed(0)));

        dataLtv.push(allDataSpecify.value.arr[dayKey][country][platform].length < platformLength
          ? (isNaN(parseFloat(ltvData[country][day].ltv.toFixed(2)))
            ? 0
            : parseFloat(ltvData[country][day].ltv.toFixed(2)))
          : '-');
      });

      const countryKey = country as keyof typeof worldCode;
      const countryFullName = worldCode[countryKey];
      allDataSpecify.value.lineChart.x.push(countryFullName);

      allDataSpecify.value.lineChart.download[countryFullName] = {
        name: countryFullName,
        data: dataD,
      };
      allDataSpecify.value.lineChart.ltv[countryFullName] = {
        name: countryFullName,
        data: dataLtv,
      };
    });

    // lineChart 2
    ALLDAYS.forEach((day) => {
      daysArr.ltv[day] = daysArr.downloadC[day] !== 0 ? (daysArr.ltv[day] / daysArr.downloadC[day]) : 0;
    });
    allDataSpecify.value.code.forEach((competitorCode: any) => {
      ltvData2[competitorCode] = {};
      ALLDAYS.forEach((day) => {
        ltvData2[competitorCode][day] = {
          ltv: 0,
          download: 0,
        };
      });
      market.value.forEach((market) => {
        group.value.forEach((groupItem, gIndex) => {
          const sortJsonCompetitor = sortJson.value.jp[gIndex][competitorCode];
          if (sortJsonCompetitor[market[1]]) {
            ALLDAYS.forEach((day) => {
              ltvData2[competitorCode][day].download += sortJsonCompetitor[market[1]][platform][day].download;
            });
          }
          if (sortJsonCompetitor[`${market[1]}A`]) {
            ALLDAYS.forEach((day) => {
              const ltvValue = sortJsonCompetitor[`${market[1]}A`][platform][day];

              if (!allDataSpecify.value.arr[`day${day}`][market[1]][platform].includes(competitorCode)) {
                if (!isNaN(parseFloat(ltvData2[competitorCode][day].ltv))) {
                  ltvData2[competitorCode][day].ltv += ltvValue;
                } else {
                  ltvData2[competitorCode][day].ltv = ltvValue;
                }
              } else if (ltvData2[competitorCode][day].ltv === 0) {
                ltvData2[competitorCode][day].ltv = '';
              }
            });
          }
        });
      });

      if (allDataSpecify.value.dashboard.length === 0) {
        let name = '';
        allDataSpecify.value.group[0][0].forEach((val: any) => {
          if (val.competitor_code === competitorCode && val.dashboard === 0) {
            name = val.competitor_name;
            return false;
          }
        });
        if (name) {
          allDataSpecify.value.lineChart2.x.push(name.length > 12 ? `${name.substring(0, 12)}...` : name);
          const line2DataD: number[] = [];
          const line2DataLtv: any[] = [];
          ALLDAYS.forEach((day) => {
            const ltv2Data = ltvData2[competitorCode][day];
            line2DataD.push(parseFloat(ltv2Data.download.toFixed(0)));
            line2DataLtv.push(ltv2Data.ltv === ''
              ? '-'
              : isNaN(parseFloat((ltv2Data.ltv / ltv2Data.download).toFixed(2)))
                ? '-'
                : parseFloat((ltv2Data.ltv / ltv2Data.download).toFixed(2)));
          });
          allDataSpecify.value.lineChart2.download[competitorCode] = {
            name: name.length > 12 ? `${name.substring(0, 12)}...` : name,
            data: line2DataD,
          };
          allDataSpecify.value.lineChart2.ltv[competitorCode] = {
            name: name.length > 12 ? `${name.substring(0, 12)}...` : name,
            data: line2DataLtv,
          };
        }
      }
    });
  }

  allDataSpecify.value.gameCodeDownloadLineChart = ALLDAYS.map(day => parseFloat((daysArr.d[day] || 0).toFixed(0)));
  allDataSpecify.value.gameCodeLtvLineChart = ALLDAYS.map(day => daysArr.ltv[day] || 0);
  allDataSpecify.value.lineChart2.x.unshift(gameCode.value.length > 12 ? `${gameCode.value.substring(0, 12)}...` : gameCode.value);
  const xAxis = ALLDAYS.map(day => `D${day}`);
  if (selectedKeys.value.length === 0) {
    const downloadData = allDataSpecify.value.lineChart2.download;
    Object.keys(downloadData).forEach((key) => {
      downloadData[key].data.forEach((item: number, index: number) => {
        graphState.chartData.push({
          name: downloadData[key].name,
          value: item,
          xLabel: xAxis[index],
          yLabel: '',
        });
      });
    });

    const ltvData = allDataSpecify.value.lineChart2.ltv;
    Object.keys(ltvData).forEach((key) => {
      ltvData[key].data.forEach((item: number, index: number) => {
        graph2State.chartData.push({
          name: ltvData[key].name,
          value: item,
          xLabel: xAxis[index],
          yLabel: '',
        });
      });
    });
  } else {
    selectedKeys.value.forEach((val) => {
      let code = '';
      // eslint-disable-next-line array-callback-return
      group.value[0][0].find((item: { competitor_name: any; competitor_code: string; }) => {
        if (item.competitor_name === val) {
          code = item.competitor_code;
          return false;
        }
      });

      allDataSpecify.value.lineChart2.download[code]?.data.forEach((item: number, index: number) => {
        graphState.chartData.push({
          name: allDataSpecify.value.lineChart2.download[code].name,
          value: item,
          xLabel: xAxis[index],
          yLabel: '',
        });
      });
      allDataSpecify.value.lineChart2.ltv[code]?.data.forEach((item: number, index: number) => {
        graph2State.chartData.push({
          name: allDataSpecify.value.lineChart2.ltv[code].name,
          value: item,
          xLabel: xAxis[index],
          yLabel: '',
        });
      });
    });
  }

  allDataSpecify.value.gameCodeDownloadLineChart.forEach((data: number, index: number) => {
    graphState.chartData.push({
      name: gameCode.value,
      value: data,
      xLabel: xAxis[index],
      yLabel: '',
    });
  });
  allDataSpecify.value.gameCodeLtvLineChart.forEach((data: number, index: number) => {
    graph2State.chartData.push({
      name: gameCode.value,
      value: data,
      xLabel: xAxis[index],
      yLabel: '',
    });
  });
  console.log(allDataSpecify.value);
  console.log(daysArr);
  console.log(graphState.chartData);
  console.log(graph2State.chartData);
}

const refresh = () => {
  let error = null;
  let marketError = true;
  // 检查数据是否有误
  selectedGroupCompetitor.value.forEach((group) => {
    if (group.market.length === 0) marketError = false;
    error = group.competitors.find(game => game.commercial_score === null
      || game.market_score === null || game.similarity_score === null || game.similarity_score > 1
      || game.similarity_score < 0.1 || game.commercial_score < 0.1 || game.market_score < 0.1);
  });
  if (!marketError) {
    MessagePlugin.error('Please choose at least one market before refresh.');
  } else if (error) {
    MessagePlugin.error('Please make sure to fill in all available score before refresh.');
  } else {
    let score = 0;
    selectedGroupCompetitor.value[tabSelectId.value].competitors.map(game => score += game.similarity_score ?? 0);
    if (group.value.length > 0) {
      group.value[0][2].score = score;
      group.value[0][0] = selectedGroupCompetitor.value[tabSelectId.value].competitors;
      downloadCode(competitorCode.value).then(() => lineChart());
    } else {
      MessagePlugin('success', 'No Data');
    }
  }
};

const yAxisLabelFormat = (value: any, dollar: boolean) => `${dollar ? `$${value}` : value}`;

// Tab

const addTab = () => {
  selectedGroupCompetitor.value.push({
    value: selectedGroupCompetitor.value.length,
    label: `group ${selectedGroupCompetitor.value.length + 1}`,
    market: '',
    group_name: '',
    index: 0,
    competitors: selectedCompetitor.value.map(competitor => ({
      ...competitor,
      similarity_score: null,
      commercial_score: null,
      market_score: null,
    })),
  });
  changeTab((selectedGroupCompetitor.value.length - 1).toString());
};

const showConfirmDialog = (id: number) => {
  showConfirmRemoveMarket.value = true;
  removeID.value = id;
};

const removeTab = (index: number) => {
  if (index < 0) return;
  if (selectedGroupCompetitor.value.length === 1) return MessagePlugin('warning', 'Can not delete all the market group');

  selectedGroupCompetitor.value.splice(index, 1);
  selectedGroupCompetitor.value = selectedGroupCompetitor.value.map((group, idx) => {
    const isDefaultLabel = group.label.startsWith('group');
    return {
      ...group,
      value: idx,
      label: isDefaultLabel ? `group ${idx + 1}` : group.label,
    };
  });
  showConfirmRemoveMarket.value = false;
  changeTab(selectedGroupCompetitor.value[selectedGroupCompetitor.value.length - 1].value);
};

const changeTab = (value: string | number) => {
  tabSelectId.value = typeof value === 'number' ? value : parseInt(value, 10);
};

const handleChangePage = (selectedCompetitor: CompetitorListGroupContentModal[], page: number) => {
  emit('handleChangePage', { selectedCompetitor, page, selectedGroupCompetitor: selectedGroupCompetitor.value, groupCompetitor: groupCompetitor.value });
};

// Table
const displayData = (newData: (CompetitorListGroupContentModal & CompetitorExportListModal)[], index: number) => {
  if (newData) {
    selectedGroupCompetitor.value[index].competitors = newData;
    return selectedGroupCompetitor.value[index].competitors.map((item:
    (CompetitorListGroupContentModal & CompetitorExportListModal), index: number) => ({
      key: index,
      index: index + 1,
      competitor_name: item.competitor_name,
      similarity_score: item.similarity_score,
      commercial_score: item.commercial_score,
      market_score: item.market_score,
      competitor_icon: item.competitor_icon,
    }));
  }
  return;
};

const displayCols = (cols: ITableCols[]) => cols.map(item => item.colKey);


// Cascader
const convertMarketToResult = (market: string): [] => {
  if (market && market !== '') {
    const result: any = [];

    JSON.parse(market)['99'].forEach(([region, countryCode]: [string, string]) => {
      // Find the region in the result array
      let regionObj = result.find((item: { value: string; }) => item.value === region);

      // If the region doesn't exist, create it
      if (!regionObj) {
        regionObj = { value: region, children: [] };
        result.push(regionObj);
      }

      // Add the country code to the region's children
      regionObj.children.push({ value: countryCode });
    });
    return result;
  }
  return [];
};

const convertResultToMarket = (result: any): string => {
  // eslint-disable-next-line no-param-reassign
  const marketObject: { [key: string]: string[][] } = { 99: [] };

  result.forEach((regionObj: any) => {
    const region = regionObj.value;
    const countryCodes = regionObj.children.map((countryObj: { value: string }) => [region, countryObj.value]);

    marketObject['99'].push(...countryCodes);
  });

  return JSON.stringify(marketObject);
};

const getMarketLabel = (market: string) => {
  if (market !== '') {
    const regionCountMap: Record<string, number> = {};
    let total = 0;
    const marketObject = JSON.parse(market)['99'];
    marketObject.forEach(([region]: [string]) => {
      regionCountMap[region] = (regionCountMap[region] || 0) + 1;
    });
    marketObject.sort((a: string, b: string) => a[0].localeCompare(b[0]));

    const country = world.find(item => item.value === marketObject[0][0])
      ?.children.find(arr => arr.value === marketObject[0][1]);

    Object.entries(regionCountMap).map(data => total = total + data[1]);
    return `${country?.label}: ${total}`;
  }
  return;
};

const createOrUpdateGame = async () => {
  let verisonNameError = false;
  let versionNameLengthError = false;

  selectedCompetitor.value.forEach((game) => {
    // eslint-disable-next-line no-param-reassign
    game.group_name = input.value;
    verisonNameError = game.group_name === '' || game.group_name === null;
    versionNameLengthError = game.group_name?.length >= 200;
  });

  // 检查数据是否有误
  const marketError = selectedGroupCompetitor.value.some(group => group.market.length === 0);
  const error = selectedGroupCompetitor.value.flatMap(group => group.competitors.filter(game => (
    game.commercial_score === null
    || game.market_score === null
    || game.similarity_score === null
    || game.similarity_score > 1
    || game.similarity_score < 0.1
    || game.commercial_score < 0.1
    || game.market_score < 0.1
  )));


  if (marketError) {
    MessagePlugin.error('Please choose at least one market before submitting.');
  } else if (verisonNameError) {
    MessagePlugin.error('Please make sure to fill the version name before submitting.');
  } else if (versionNameLengthError) {
    MessagePlugin.error('The version name maximum length is 200.');
  } else if (error.length > 0) {
    MessagePlugin.error('Please make sure to fill in all available score before submitting.');
  } else if (store.evaluationEditStatus) {
    MessagePlugin.error('Please check before submitting.');
  } else {
    displayVersionInput.value = false;
    selectedGroupCompetitor.value = selectedGroupCompetitor.value.map(item => ({ ...item, group_name: item.label }));

    const updateGame = async (gameId: number, updateGroup: boolean) => {
      loading.value = true;

      try {
        const result = await store.createOrUpdateGroup({
          game_id: gameId,
          group: selectedGroupCompetitor.value,
        }, updateGroup);

        if (result) {
          const response = await store.saveGameVersion({
            version_name: input.value,
            game_id: gameId,
            apply: updateGroup ? groupCompetitor.value.apply : 1,
          });
          if (response === 0) {
            MessagePlugin('success', updateGroup ? 'Update Successfully' : 'Create Successfully');
            emit('handleChangePage', { selectedCompetitor: [], selectedGroupCompetitor: [], groupCompetitor: [], page: StepsSequenceItem.settings });
          } else {
            MessagePlugin('error', updateGroup ? 'Update Unsuccessful' : 'Create Unsuccessful');
          }
        }
      } catch (error) {
        MessagePlugin('error', 'Failed to update/create game');
      } finally {
        loading.value = false;
      }
    };

    if (!props.data.gameId) {
      const gameId = await store.createGameIDByGame(gameCode.value, selectedCompetitor.value);
      if (gameId) {
        await updateGame(parseInt(gameId, 10), false);
      }
    } else {
      await updateGame(props.data.gameId, true);
    }
  }
};

function compareAndFilter(firstStructure: Market[], secondStructure: MarketFilter[]) {
  const chosenCountries = [];
  const notChosenCountries = [];

  for (const continentData of firstStructure) {
    const continent = continentData.value;

    // Find the corresponding continent in the second structure
    const matchingContinent = secondStructure.find(item => item.value === continent);

    const continentStructure: Market = {
      label: continentData.label,
      value: continentData.value,
      children: [],
    };

    const notContinentStructure: Market = {
      label: continentData.label,
      value: continentData.value,
      children: [],
    };

    // Loop through the children of the first structure
    for (const countryData of continentData.children || []) {
      const country = countryData.value;

      // Check if the country exists in the children of the corresponding continent in the second structure
      if (matchingContinent?.children?.some(child => child.value === country)) {
        continentStructure.children!.push({ value: country, label: countryData.label || country });
      } else {
        notContinentStructure.children!.push({ value: country, label: countryData.label || country });
      }
    }

    if (continentStructure.children && continentStructure.children.length > 0) {
      chosenCountries.push(continentStructure);
    } else {
      // If continent has no children in the second structure, add it to notChosenCountries
      notChosenCountries.push(notContinentStructure);
    }
  }

  // Iterate through the first structure again to find parents and children not in the second structure
  for (const continentData of firstStructure) {
    const continent = continentData.value;

    // Find the corresponding continent in the second structure
    const matchingContinent = secondStructure.find(item => item.value === continent);

    if (!matchingContinent) {
      const continentStructure: Market = {
        label: continentData.label,
        value: continentData.value,
        children: continentData.children?.map(child => ({
          value: child.value,
          label: child.label || child.value,
        })) || [],
      };

      notChosenCountries.push(continentStructure);
    } else {
      // Check for children in the first structure not found in the second structure
      // eslint-disable-next-line max-len
      const notMatchingChildren = continentData.children?.filter(child => !matchingContinent.children?.some(c => c.value === child.value));

      if (notMatchingChildren && notMatchingChildren.length > 0) {
        const notMatchingContinent: Market = {
          label: continentData.label,
          value: continentData.value,
          children: notMatchingChildren.map(child => ({
            value: child.value,
            label: child.label || child.value,
          })),
        };

        notChosenCountries.push(notMatchingContinent);
      }
    }
  }

  // eslint-disable-next-line max-len
  const removeDuplicationCountry = notChosenCountries.filter((obj: any, index: number, self: any) => index === self.findIndex((o: any) => o.label === obj.label && o.value === obj.value));

  return { chosenCountries, removeDuplicationCountry };
}

const onConfirmCancel = () => {
  router.go(0);
  displayCancel.value = false;
};

const onCancelSave = () => {
  displayCancel.value = false;
};

useWatchGameChange(async () => {
  if (previousGameCode.value !== gameCode.value) {
    goToPredictionOverview();
  }
  previousGameCode.value = gameCode.value;
});

// init, watch
onMounted(async () => {
  if (selectedGroupCompetitor.value[tabSelectId.value]) {
    selectedMarket.value = convertMarketToResult(selectedGroupCompetitor.value[tabSelectId.value].market);
  }
  if (selectedGroupCompetitor.value.length === 0 && !props.data.gameId) {
    selectedGroupCompetitor.value.push({
      value: 0,
      label: `group ${selectedGroupCompetitor.value.length + 1}`,
      market: '',
      group_name: '',
      index: 0,
      competitors: [...selectedCompetitor.value],
    });
  }
  if (props.data.gameId) {
    await store.getCompetitorById(props.data.gameId);
    groupCompetitor.value.groups.forEach((item, index) => {
      selectedGroupCompetitor.value.push({
        index,
        competitors: item.competitors,
        value: index,
        label: getMarketLabel(item.market) ?? `group ${index}`,
        group_name: item.group_name,
        market: item.market,
      });
    });
    if (props.data.groupCompetitor.status === 'success') {
      await fetchGame().then(() => lineChart());
    }
    // 第一页的market
    input.value = groupCompetitor.value.version_name;
    selectedMarket.value = convertMarketToResult(groupCompetitor.value.groups[0]?.market);
    const { chosenCountries, removeDuplicationCountry } = compareAndFilter(
      world,
      convertMarketToResult(selectedGroupCompetitor.value[0].market),
    );
    allMarket = [...chosenCountries, ...removeDuplicationCountry].reduce((acc: any, curr) => {
      // 查找是否已经存在相同父级值的项
      const existingItem = acc.find((item: any) => item.value === curr.value);

      if (existingItem) {
        // 如果存在，将当前项的 children 合并到已存在的项中
        existingItem.children = existingItem.children.concat(curr.children);
      } else {
        // 如果不存在，直接添加到结果数组中
        acc.push(curr);
      }

      return acc;
    }, []);;
  }
});

const getSelectedCountry = (chosen: Market[], market: any[]): MarketFilter[] => {
  const result = [];
  for (const data of chosen) {
    const found = market.find(item => item.value === data.value);

    const structure: Market = {
      label: data.label,
      value: data.value,
      children: [],
    };

    for (const child of data.children!) {
      if (found?.children.some((childData: { value: string; }) => childData.value === child.value)) {
        structure.children?.push({
          label: child.label,
          value: child.value,
        });
      }
    }
    if (structure.children && structure.children?.length > 0) result.push(structure);
  }
  return result;
};

watch(
  () => tabSelectId.value,
  (value) => {
    const data = convertMarketToResult(selectedGroupCompetitor.value[value === 0 ? 0 : value]?.market);
    selectedMarket.value = data;
    let totalMarket: MarketFilter[] = [];

    for (const group of selectedGroupCompetitor.value) {
      totalMarket = [...convertMarketToResult(group.market), ...totalMarket];
    }
    totalMarket = totalMarket.reduce((acc: any, curr) => {
      // 查找是否已经存在相同父级值的项
      const existingItem = acc.find((item: any) => item.value === curr.value);

      if (existingItem) {
        // 如果存在，将当前项的 children 合并到已存在的项中
        existingItem.children = existingItem.children.concat(curr.children);
      } else {
        // 如果不存在，直接添加到结果数组中
        acc.push(curr);
      }

      return acc;
    }, []);
    const { chosenCountries, removeDuplicationCountry } = compareAndFilter(world, totalMarket);
    if (data.length > 0) {
      allMarket = [...getSelectedCountry(
        chosenCountries,
        convertMarketToResult(selectedGroupCompetitor.value[tabSelectId.value]?.market),
      ),
      ...removeDuplicationCountry].reduce((acc: any, curr) => {
        // 查找是否已经存在相同父级值的项
        const existingItem = acc.find((item: any) => item.value === curr.value);

        if (existingItem) {
          // 如果存在，将当前项的 children 合并到已存在的项中
          existingItem.children = existingItem.children.concat(curr.children);
        } else {
          // 如果不存在，直接添加到结果数组中
          acc.push(curr);
        }

        return acc;
      }, []);
    } else allMarket = removeDuplicationCountry;
  },
);

const updateModelValue = (value: any) => {
  const index = tabSelectId.value === 0 ? 0 : tabSelectId.value;
  const selectedGroup = selectedGroupCompetitor.value[index];

  if (selectedGroup) {
    if (value.length !== 0) {
      selectedGroup.market = convertResultToMarket(value);
      // eslint-disable-next-line no-param-reassign
      selectedGroup.competitors.forEach(game => game.market = convertResultToMarket(value));
    } else {
      selectedGroup.market = '';
    }
    selectedGroup.label = getMarketLabel(selectedGroup.market) ?? selectedGroup.label;
  }
};

</script>
<style lang="scss" scoped></style>
