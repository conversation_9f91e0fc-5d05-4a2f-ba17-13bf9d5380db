<template>
  <div>
    <CommonView
      :hide-right="true"
      :need-back="true"
      :router-index="-1"
      :store="store"
      class="text-center"
    >
      <template #views>
        <Steps
          v-if="steps.current !== StepsSequenceItem.settings"
          class="mb-5"
          :layout="steps.layout"
          :sequence="steps.sequence"
          :current="steps.current"
          :theme="steps.theme"
          :content="steps.content"
        />
        <div
          :class="steps.current===StepsSequenceItem.evaluation?'':
            `bg-white-primary rounded-large p-[16px] h-full overflow-y-auto flex flex-col gap-y-[16px]`"
        >
          <Settings
            v-if="steps.current === StepsSequenceItem.settings"
            :data="(props as any)"
            :steps="steps"
            @handle-change-page="handleChangePage"
          />
          <AddCompetitor
            v-if="steps.current === StepsSequenceItem.addCompetitor"
            :steps="steps"
            :data="(props as any)"
            @handle-change-page="handleChangePage"
          />
          <Evaluation
            v-if="steps.current === StepsSequenceItem.evaluation"
            :data="(props as any)"
            :steps="steps"
            @handle-change-page="handleChangePage"
          />
        </div>
      </template>
    </CommonView>
  </div>
</template>
<script lang="ts" setup>
import { useIntelligencePredictionStore } from '@/store/intelligence/prediction/prediction.store';
import CommonView from 'common/components/Layout/CommonView.vue';
import { reactive, onMounted } from 'vue';
import { StepsSequenceItem } from '../const/const';
import Steps from '../components/Steps.vue';
import AddCompetitor from './addCompetitor.vue';
import Evaluation from './evaluation.vue';
import Settings from './settings.vue';
import { CompetitorListGroupContentModal, ChangePageModal, StepsModal, CompetitorListGroupModal, InputOptionsModal, CompetitorListContentModal } from '../modal/prediction';
import { useRouter } from 'vue-router';

const store = useIntelligencePredictionStore();
const router = useRouter();
store.setRouter(router);
const props = reactive<{selectedCompetitor: CompetitorListGroupContentModal[]; gameId: number|undefined;
  groupCompetitor: CompetitorListContentModal;
  // eslint-disable-next-line @typescript-eslint/func-call-spacing
  selectedGroupCompetitor: (CompetitorListGroupModal & InputOptionsModal)[]}>
    ({
      selectedCompetitor: [], gameId: undefined, selectedGroupCompetitor: [],
      groupCompetitor: {
        version_name: '',
        apply: 0,
        game_id: 0,
        status: undefined,
        groups: [],
      },
    });

const steps = reactive<StepsModal>({
  layout: 'horizontal',
  current: StepsSequenceItem.settings,
  theme: 'dot',
  content: [
    {
      title: 'Add Competitor',
      status: 'process',
    },
    {
      title: 'Evaluation',
      status: 'default',
    },
  ],
});

onMounted(async () => {
  if (router.options.history.state?.type === 'init') {
    steps.current = StepsSequenceItem.addCompetitor;
  }
});

const handleChangePage = (item: ChangePageModal) => {
  props.selectedCompetitor = item.selectedCompetitor ?? [];
  props.selectedGroupCompetitor = item.selectedGroupCompetitor ?? [];
  if (item.groupCompetitor) props.groupCompetitor = item.groupCompetitor;
  if (item.data) props.gameId = item.data;
  steps.current = item.page ?? StepsSequenceItem.addCompetitor;
};
</script>
<style lang="scss" scoped></style>
