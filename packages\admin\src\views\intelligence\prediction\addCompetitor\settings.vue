<template>
  <div class="text-left">
    <t-space>
      <t-button
        @click="handleChangePage({
          page: StepsSequenceItem.addCompetitor,
          selectedGroupCompetitor: [], selectedCompetitor: [],
          groupCompetitor: {
            version_name: '',
            apply: 0,
            game_id: 0,
            status: undefined,
            groups: [],
          },
        })"
      >
        Add
      </t-button>
    </t-space>
  </div>
  <t-row>
    <t-col>
      <EnhancedTable
        ref="table"
        :display-columns="(displayCols(cols) as any)"
        :loading="loading"
        row-key="key"
        :data="tableData"
        :horizontal-scroll-affixed-bottom="true"
        :columns="cols"
        :tree="treeConfig"
        :tree-expand-and-fold-icon="treeExpandAndFoldIconRender"
      />
    </t-col>
  </t-row>
  <t-space />
  <t-row class="justify-end">
    <t-col>
      <t-space>
        <t-button variant="outline" @click="() => router.back()">Cancel</t-button>
        <t-button :disabled="checkApply" @click="saveSettings(group.find(item => item.apply === 1))">
          Save
        </t-button>
      </t-space>
    </t-col>
  </t-row>
  <Dialog
    v-model:visible="displayRename"
    header="Rename Version"
    :confirm-on-enter="true"
    :on-cancel="onCancel"
    :on-confirm="onConfirm"
  >
    <t-input v-model="input" type="text" size="large" />
  </Dialog>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';
import { useSettingsTable } from '@/views/intelligence/prediction/compose/settings-table';
import { EditOption, SettingsProps, StepsSequenceItem } from '../const/const';
import { CompetitorListGroupContentModal, SettingsModal, CompetitorListContentModal, ChangePageModal } from '../modal/prediction';
import { ITableCols } from 'common/components/table/type';
import { useIntelligencePredictionStore } from '@/store/intelligence/prediction/prediction.store';
import { storeToRefs } from 'pinia';
import { useGlobalGameStore } from '@/store/global/game.store';
import { Dialog, DropdownOption, EnhancedTable, MessagePlugin } from 'tdesign-vue-next';
import { ChevronDownIcon, ChevronRightIcon } from 'tdesign-icons-vue-next';
import { responseMessage } from '../util/general';
import { useRouter } from 'vue-router';
import { useGoto } from '@/router/goto';
import { useWatchGameChange } from 'common/compose/request/game';

const props: SettingsModal = defineProps(SettingsProps);
const { goToPredictionOverview } = useGoto();
const store = useIntelligencePredictionStore();
const loading = ref(false);
const treeConfig = reactive({ childrenKey: 'children', treeNodeColumnIndex: 1, indent: 25 });
let group = reactive<CompetitorListContentModal[]>([]);
const displayCols = (cols: ITableCols[]) => cols.map(item => item.colKey);
const { gameCode } = storeToRefs(useGlobalGameStore());
const tableData = ref<any[]>([]);
const checkApply = ref<boolean>(false);
const table = ref<any>(null);
const displayRename = ref<boolean>(false);
const emit = defineEmits(['handleChangePage', 'getEvaluationPage']);
const router = useRouter();
const previousGameCode = ref<string>(gameCode.value);
// Rename Input
const currentGroup = ref<number>(0);
const input = ref<string>('');

// table function
const onChangeApply = (input: boolean | string | number, index: number) => {
  group = group.map(item => ({ ...item, apply: 0 }));
  group[index].apply = input ? 1 : 0;
  checkApply.value = !checkGameApply(group);
  tableData.value = displayData(group);
};

const onRemove = (index: number) => {
  store.removeGameById(group[index].game_id).then((result) => {
    if (result === 0) responseMessage(result, 'delete');
  });
  group.splice(index, 1);
  tableData.value = displayData(group);
};

const handleChangePage = (item: ChangePageModal) => {
  emit('handleChangePage', { page: item.page });
};

const onEdit = (content: DropdownOption, key: number) => {
  if (content.value === EditOption.addCompetitor) emit('handleChangePage', { selectedCompetitor: getSelectedCompetitor(key), groupCompetitor: group[key], selectedGroupCompetitor: group[key], data: group[key].game_id, page: StepsSequenceItem.addCompetitor });
  if (content.value === EditOption.evaluation) emit('handleChangePage', { selectedCompetitor: getSelectedCompetitor(key), selectedGroupCompetitor: group[key], groupCompetitor: group[key], data: group[key].game_id, page: StepsSequenceItem.evaluation });
  if (content.value === EditOption.rename) {
    displayRename.value = true;
    currentGroup.value = key;
    input.value = group[currentGroup.value].version_name;
  }
};

const axiosLtv = async (code: CompetitorListGroupContentModal[]) => {
  try {
    const ltvData = await store.getLtvDetails({
      day: '7,30,90,180,360',
      competitor: code.map(item => item.competitor_code).toString(),
    });
    if (!ltvData || Object.keys(ltvData).sort()
      .toString() !== code.map(item => item.competitor_code).sort()
      .toString()) {
      return 'pending';
    }

    return 'success';
  } catch (e) {
    console.error(e);
    return 'error';
  }
};

const treeExpandAndFoldIconRender = (h: any, { type }: any) => (type === 'expand' ? h(
  ChevronRightIcon,
  {},
  '',
) : h(
  ChevronDownIcon,
  {},
  '',
));

const { cols } = useSettingsTable({ method: { onChangeApply, onRemove, onEdit } });

const displayData = (newData: CompetitorListContentModal[]) => newData.map((
  item: CompetitorListContentModal,
  index: number,
) => ({
  key: index,
  apply: item.apply === 1,
  game_id: item.game_id,
  version_name: item.version_name,
  status: item.status,
  action: true,
  children: item.groups.map((competitor, num) => ({
    version_name: competitor.group_name,
    apply: null,
    key: parseInt(`${item.game_id}${num}`, 10),
    status: null,
    action: false,
    children: [{
      version_name: competitor.competitors,
      key: parseInt(`${item.game_id}${num}${num}`, 10),
      apply: null,
      status: null,
      action: false,
    }],
  })),
}));

const saveSettings = async (data: CompetitorListContentModal | undefined) => {
  if (data) {
    const result = await store.saveGameVersion({
      version_name: data.version_name,
      game_id: data.game_id,
      apply: data.apply,
    });
    if (result === 0) {
      MessagePlugin('success', 'Save Successfully');
      goToPredictionOverview();
    }
  } else {
    MessagePlugin('error', 'Please select at least one item to apply');
  }
};

const onCancel = () => {
  displayRename.value = false;
};

const onConfirm = async () => {
  if (input.value?.length >= 200) return MessagePlugin.error('Rename Unsuccessfully. The version name maximum length is 200.');

  group[currentGroup.value].version_name = input.value;
  tableData.value = displayData(group);

  const result = await store.saveGameVersion({
    version_name: input.value,
    game_id: group[currentGroup.value].game_id,
    apply: group[currentGroup.value].apply,
  });
  if (result === 0) MessagePlugin.success('Rename Successfully');
  else MessagePlugin.error('Rename Unsuccessfully');
  displayRename.value = false;
};

const getSelectedCompetitor = (key: number) => group[key].groups[0].competitors;

const checkGameApply = (data: CompetitorListContentModal[]) => data.some(item => item.apply === 1);

useWatchGameChange(async () => {
  if (previousGameCode.value !== gameCode.value) {
    goToPredictionOverview();
  }
  previousGameCode.value = gameCode.value;
});

// init, watch
onMounted(async () => {
  loading.value = true;
  if (props.data.competitor) {
    group = props.data.competitor.data;
    checkApply.value = !checkGameApply(props.data.competitor.data);
    tableData.value = displayData(props.data.competitor.data);
    loading.value = false;
  } else {
    try {
      const result: any = await store.getCompetitorByCode(gameCode.value);
      if (result) {
        // 使用 Promise.all 等待所有 axiosLtv 调用完成
        const item = await Promise.all(result.map(async (item: any) => {
          const updatedItem = { ...item };
          updatedItem.status = await axiosLtv(item.groups[0]?.competitors ?? []);
          return updatedItem;
        }));
        group = item;
        checkApply.value = !checkGameApply(item);
        tableData.value = displayData(item);
        loading.value = false;
      }
      loading.value = false;
    } catch (e) {
      console.error(e);
    }
  }
});

</script>
<style lang="scss" scoped></style>

