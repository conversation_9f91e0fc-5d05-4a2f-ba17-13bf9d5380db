<template>
  <!-- <FullLoading v-if="initLoading" /> -->
  <!-- v-if="!store.isLoading && isNoData" -->
  <!-- v-if="!initLoading && !isNoData" -->
  <CommonView
    v-if="!isNoData"
    class="w-full h-full"
    :store="store"
    title="Intelligence / Prediction / Analyze"
    :form-props="{
      modelValue: condition.cur,
      formList: filterList,
      'onUpdate:modelValue': formUpdateValue,
      onSubmit: formSubmit,
      onReset: formReset,
    }"
    :tab-props="tabProps"
  >
    <template #views>
      <t-space
        direction="vertical"
        class="bg-white-primary rounded-large p-[16px] overflow-y-auto flex flex-col gap-y-[16px] mb-6"
      >
        <BasicChart
          v-if="!loading"
          :chart-type="graphState.chartType"
          :detail-type="graphState.detailType"
          :data-mode="graphState.dataMode"
          :y-axis-label-format="(e:number)=>yAxisLabelFormat(e)"
          :tooltip-value-format="(e:number)=>yAxisLabelFormat(e)"
          data-value-filed="value"
          data-item-field="name"
          tooltip-sort="desc"
          :tooltip-filter-zero="false"
          data-group-item-field="xLabel"
          :data="graphState.chartData ?? []"
          is-show-legend
          is-legend-bar-bottom
          :legend-props="{ top: 'bottom', left: 'center', }"
          :grid="{ bottom: '10%', containLabel: true, left: 20, right: 20 }"
          :reg-rules="[{ name: 'value', value: ['s1000', 'decimal'] }]"
        />
        <FullLoading v-else class="relative w-full min-h-[450px]" />
      </t-space>
      <t-space
        direction="vertical"
        class="bg-white-primary rounded-large p-[16px] overflow-y-auto flex flex-col gap-y-[16px]"
      >
        <AnalyzeTable v-if="!loading" :mode="mode" @handle-select-change="handleSelectChange" />
        <FullLoading v-else class="relative w-full min-h-[450px]" />
      </t-space>
    </template>
  </CommonView>
  <info
    v-else
  />
</template>
<script lang="ts" setup>
import { useIntelligencePredictionStore } from '@/store/intelligence/prediction/prediction.store';
import { useGlobalGameStore } from '@/store/global/game.store';
import { computed, defineAsyncComponent, nextTick, onMounted, reactive, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';
import { cloneDeep } from 'lodash-es';
import info from '../../components/info.vue';


// ---------------------------- 参数 ------------------------------
import { ALLDAYS_X_AXIS, ANALYZE, ANALYZE_FILTER_CONDITION, TABLE_HEAD } from '../const/const';
import { Platform } from '@/store/intelligence/creative/config/selectOptions.json';

// ---------------------------- Type ------------------------------
import { AnalyzeFormOptions, AnalyzeFormParams, EvaluationLineGraph } from '../modal/prediction';

// ---------------------------- 组件 ------------------------------
import CommonView from 'common/components/Layout/CommonView.vue';
import { DataMode } from 'common/components/BasicChart';
import FullLoading from 'common/components/FullLoading';
import AnalyzeTable from '../components/AnalyzeTable.vue';

// ---------------------------- Util ------------------------------
import { getAnalyzeFilterList } from '../util/general';

// ---------------------------- 函数 ------------------------------
// import { configureChart, fetchAnalyzeChartData } from '../overview/ltvCalculation';
import { fetchAnalyzeChartData } from '../overview/ltvCalculation';
import { useWatchGameChange } from 'common/compose/request/game';

const store = useIntelligencePredictionStore();
store.setRouter(useRouter());
const { gameCode } = storeToRefs(useGlobalGameStore());
const mode = ref<'download' | 'ltv'>('download');
const BasicChart = defineAsyncComponent(() => import('common/components/BasicChart'));
const { allData, tabProps, countryOptions } = storeToRefs(useIntelligencePredictionStore());
const graphState = reactive({
  chartType: 'line',
  detailType: 'stack',
  chartData: [] as (EvaluationLineGraph & {id: string})[],
  chartDataClone: [] as (EvaluationLineGraph & {id: string})[],
  dataMode: 'x' as DataMode,
  xAxisName: '',
  yAxisName: '',
  isShowDataZoom: true,
  showLable: false,
});
const initLoading = ref(true);
const loading = ref(false);
const isNoData = ref(false);
const yAxisLabelFormat = (value: any) => `${mode.value === 'ltv' ? `$${value}` : value.toLocaleString()}`;

const DEFAULT_FILTER: AnalyzeFormParams = {
  type: 0,
  market: {},
  platform: Platform.map(it => it.value),
};

const ANALYZE_FILTER_LABEL = {
  type: [{
    label: 'P-Downloads',
    value: 0,
  }, {
    label: 'P-LTV',
    value: 1,
  }],
  market: countryOptions as any,
  platform: Platform.map(data => ({
    label: data.text,
    value: data.value,
  })),
};

const formOptions = ref<AnalyzeFormOptions>({
  fieldObj: cloneDeep(ANALYZE_FILTER_LABEL),
  conditionList: cloneDeep(ANALYZE_FILTER_CONDITION),
});
const filterList = computed(() => getAnalyzeFilterList({
  src: formOptions.value.conditionList,
  fieldObj: formOptions.value.fieldObj,
}));
const condition = reactive<{ cur: AnalyzeFormParams; default: AnalyzeFormParams }>({
  cur: cloneDeep(DEFAULT_FILTER),
  default: cloneDeep(DEFAULT_FILTER),
});

function formUpdateValue(value: typeof condition) {
  condition.cur = {
    ...condition.cur,
    ...value,
  };
}

async function getCompetitorDetails() {
  const router = useRouter();
  const result = await store.getCompetitorByCode(gameCode.value);
  if (!result || result?.length === 0) {
    return false;
  }
  const data = result.find((item: { apply: number; }) => item.apply === 1);

  const allMarkets: any[][] = [];
  const allRegions: any[] = [];
  const allCountries: any[] = [];
  if (data) {
    allData.value.Market = [];
    allData.value.competitorCodes = data.groups[0].competitors.map((e: { competitor_code: any; }) => e.competitor_code);
    if (!allData.value.competitorCodes?.length || data.groups?.length === 0) {
      router.push({
        path: '/intelligence/prediction/settings',
      });
      return false;
    };
    data.groups.forEach((item: { market: string; competitors: any[]; }) => {
      const marketData = JSON.parse(item.market);
      const markets: Array<any>  = marketData[99];
      markets.forEach((market: any[]) => {
        const region = market[0];
        const country = market[1];
        if (!allRegions.includes(region)) {
          allRegions.push(region);
        }
        if (!allCountries.includes(country)) {
          allCountries.push(country);
          allMarkets.push(market);
        }
      });

      const similarityALl = {
        score: 0,
      };

      item.competitors.forEach((v: { similarity_score: number; }) => {
        const num = similarityALl.score + v.similarity_score;
        similarityALl.score = num;
      });
      allData.value.Market.push(...markets);
      allData.value.marketSelect.push(...markets);
      // allData.value.Market = [...allData.value.Market];
      allData.value.group.push([item.competitors, markets, similarityALl]);
      // allData.value.group = [...allData.value.group];
      allData.value.dashboard = allData.value.group[0][0].filter((v: any) => v.dashboard !== 0);
    });
    return true;
  }
  return false;
}

async function formSubmit(formData?: any) {
  loading.value = true;
  // 处理 selectedPlatform
  allData.value.selectedPlatform = formData === undefined || formData.platform.length === 0 ? 3
    : formData.platform.reduce((acc: any, val: string) => acc + parseInt(val, 10), 0);
  mode.value = formData === undefined || formData.type === 0 ? 'download' : 'ltv';
  // 处理 worldSelect
  const marketData = JSON.stringify(formData?.market ?? {});
  allData.value.marketSelect = marketData === JSON.stringify({})
    ? allData.value.Market.map(item => [, item[1]]) : formData.market?.country?.map((item: string) => [, item]);
  loading.value = true;
  graphState.chartData = [];
  allData.value.barA = [];
  allData.value.lineA = [];
  // allData.value.selectedKeys = [];
  allData.value.analyzeLineChart = { x: [], ltv: {}, download: {} };
  allData.value.analyzeTable = [];
  // await Promise.all([
  //   await configureChart(gameCode.value, mode.value, false).then((value) => {
  //     if (value === false) isNoData.value = true;
  //     else isNoData.value = false;
  //   }),
  //   allData.value.competitorCodes.push(gameCode.value),
  //   allData.value.competitorCodes.forEach((code) => {
  //     insertTableData(code);
  //     const { data } = allData.value.analyzeLineChart[mode.value][code] ?? { data: [] };

  //     if (Array.isArray(data) && data.length > 0) {
  //       data.forEach((item: any, index: number) => {
  //         if (index < 360) {
  //           graphState.chartData.push({
  //             name: allData.value.analyzeLineChart[mode.value][code].name as string,
  //             value: mode.value === 'ltv' ? item.toFixed(2) : Math.round(item),
  //             xLabel: ALLDAYS_X_AXIS[index],
  //             yLabel: '',
  //             id: code,
  //           });
  //           graphState.chartDataClone.push({
  //             name: allData.value.analyzeLineChart[mode.value][code].name as string,
  //             value: mode.value === 'ltv' ? item.toFixed(2) : Math.round(item),
  //             xLabel: ALLDAYS_X_AXIS[index],
  //             yLabel: '',
  //             id: code,
  //           });
  //         }
  //       });
  //     }
  //   }),
  //   allData.value.analyzeTable.forEach((row, index: number) => {
  //     if (index <= 8) {
  //       allData.value.selectedKeys.push(row.index);
  //     }
  //   }),
  //   allData.value.competitorCodes.pop(),
  // ]);

  resolveChartTableData();
}

async function formReset() {
  formOptions.value.fieldObj = cloneDeep(ANALYZE_FILTER_LABEL);
  formOptions.value.conditionList = cloneDeep(ANALYZE_FILTER_CONDITION);
  nextTick(() => {
    // condition.cur = cloneDeep(DEFAULT_FILTER) as any;
    condition.cur.market = { ...DEFAULT_FILTER.market };
    condition.cur.platform = [...DEFAULT_FILTER.platform];
    condition.cur.type = DEFAULT_FILTER.type;
  });
  await formSubmit();
}

// function insertTableData(code: string) {
//   // 输入卓表
//   const tableData: any = {};
//   const arr = allData.value.analyzeLineChart[mode.value][code];
//   if (arr) {
//     TABLE_HEAD.forEach((head: number) => {
//       const res: number = arr.data[head - 1] as unknown as number;
//       if (mode.value === 'download') {
//         tableData[`P-D${head} Downloads`] = Math.round(res);
//       }
//       if (mode.value === 'ltv') {
//         tableData[`P-D${head} LTV`] = res === undefined ? 0 : `$${res.toFixed(2)}`;
//       }
//     });
//     tableData.index = code;
//     tableData.Competitor = allData.value.analyzeLineChart[mode.value][code].name;
//     allData.value.analyzeTable.push(tableData);
//   }
//   // 排列数据
//   if (mode.value === 'download') {
//     allData.value.analyzeTable.sort((a, b) => b['P-D1 Downloads'] - a['P-D1 Downloads']);
//   }
//   if (mode.value === 'ltv') {
//     allData.value.analyzeTable.sort((a, b) => b['P-D1 LTV'] - a['P-D1 LTV']);
//   }
// }

function filterArrayByKeys(selectedKeys: string[]) {
  return graphState.chartDataClone.filter(obj => selectedKeys.includes(obj.id));
};

async function downloadCodeAssgin() {
  allData.value.downloadCode = await store.getCompetitorConfigDetails();
}

onMounted(async () => {
  useWatchGameChange(async () => {
    initLoading.value = true;
    store.tabSelectId = ANALYZE;
    loading.value = true;
    await store.clearData();
    allData.value.gameCompetitorCodes = [];
    graphState.chartData = [];
    allData.value.selectedKeys = [];
    allData.value.analyzeLineChart = { x: [], ltv: {}, download: {} };
    allData.value.analyzeTable = [];
    const res = await Promise.all([
      getCompetitorDetails(),
      downloadCodeAssgin(),
      // await configureChart(gameCode.value, 'download', true).then((value) => {
      //   if (value === false) isNoData.value = true;
      //   else isNoData.value = false;
      // }),
      // allData.value.competitorCodes.push(gameCode.value),
      // allData.value.competitorCodes.forEach((code) => {
      //   insertTableData(code);
      //   const { data } = allData.value.analyzeLineChart.download[code] ?? { data: [] };

      //   if (Array.isArray(data) && data.length > 0) {
      //     data.forEach((item: any, index: number) => {
      //       graphState.chartData.push({
      //         name: allData.value.analyzeLineChart.download[code].name as string,
      //         value: Math.round(item),
      //         xLabel: ALLDAYS_X_AXIS[index],
      //         yLabel: '',
      //         id: code,
      //       });
      //       graphState.chartDataClone.push({
      //         name: allData.value.analyzeLineChart.download[code].name as string,
      //         value: Math.round(item),
      //         xLabel: ALLDAYS_X_AXIS[index],
      //         yLabel: '',
      //         id: code,
      //       });
      //     });
      //   }
      // }),
      // allData.value.analyzeTable.forEach((row, index: number) => {
      //   if (index <= 8) {
      //     allData.value.selectedKeys.push(row.index);
      //   }
      // }),
      // allData.value.competitorCodes.pop(),
    ]);
    if (!res[0]) {
      initLoading.value = false;
      isNoData.value = true;
      loading.value = false;
      return;
    }
    await resolveChartTableData();
    formOptions.value.fieldObj = cloneDeep(ANALYZE_FILTER_LABEL);
    formOptions.value.conditionList = cloneDeep(ANALYZE_FILTER_CONDITION);
    condition.cur = cloneDeep(DEFAULT_FILTER) as any;
    initLoading.value = false;
  });
});

const resolveChartTableData = async () => {
  isNoData.value = false;
  loading.value = true;
  // ------- 初始化table选中 -----------
  allData.value.gameCompetitorCodes = [
    gameCode.value,
    ...allData.value.competitorCodes,
  ];
  if (allData.value.gameCompetitorCodes.length > 8) {
    allData.value.selectedKeys = allData.value.gameCompetitorCodes.slice(0, 8);
  } else {
    allData.value.selectedKeys = allData.value.gameCompetitorCodes.slice(0);
  };
  // ------- 请求后端download或ltv数据 ---------
  const result: any = await fetchAnalyzeChartData(mode.value);
  // Object.keys(result).length === 0 ? isNoData.value = true : isNoData.value = false;
  // ----- chartData数据处理 -----------
  const chartDataList: Array<EvaluationLineGraph & {id: string}> = [];
  const tableList: Array<any> = [];
  allData.value.gameCompetitorCodes.forEach((code: string) => {
    const fullName = result?.[code]?.name;
    if (fullName) {
      const name = fullName.length > 12 ? `${fullName.substring(0, 12)}...` : fullName;
      const tableObjItem: any = {
        index: code,
        Competitor: name,
      };
      result?.[code]?.data?.forEach((item: number, index: number) => {
        chartDataList.push({
          name,
          value: item,
          xLabel: ALLDAYS_X_AXIS[index],
          yLabel: '',
          id: code,
        });
        const iPlus = index + 1;
        if (TABLE_HEAD.includes(iPlus)) {
          if (mode.value === 'download') {
            tableObjItem[`P-D${iPlus} Downloads`] = Math.round(item);
          } else {
            tableObjItem[`P-D${iPlus} LTV`] = item === undefined ? 0 : `$${item.toFixed(2)}`;
          }
        }
      });
      tableList.push(tableObjItem);
    }
  });
  graphState.chartData = chartDataList;
  graphState.chartDataClone = chartDataList;
  allData.value.analyzeTable = tableList;
  loading.value = false;
};

const handleSelectChange = (key: string[]) => {
  graphState.chartData = [];
  graphState.chartData = filterArrayByKeys(key);
};

</script>
<style lang="scss" scoped>
:deep(.t-input__prefix) {
  color: #747d98,
}
:deep(.t-form__controls-content .t-icon.t-icon-chevron-down) {
  width: 16px;
  height: 16px;
  color: var(--td-text-color-placeholder);
}
</style>

