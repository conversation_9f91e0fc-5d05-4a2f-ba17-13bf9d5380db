<template>
  <t-space
    direction="vertical"
    class="bg-white-primary rounded-large p-[16px] overflow-y-auto flex flex-col gap-y-[16px] mb-6"
  >
    <data-container
      class="w-full"
      :total="total"
      :page-size="pageSize"
      :default-page="pageNum"
      :page-size-options="[10, 20, 30, 50, 100, 200]"
      :hide-header="false"
      @on-page-change="onPageChange"
    >
      <template #actionSlot>
        <div class="flex">
          <SvgBtnList :list="tableButtons" />
        </div>
      </template>
      <Table
        v-if="props.mode==='download'"
        ref="tableRef"
        v-model:displayColumns="displayColumns"
        table-layout="auto"
        resizable
        row-key="index"
        :data="tableData"
        :columns="columns"
        :total="total"
        :selected-row-keys="selectedRowKeys"
        @select-change="handleSelectChange"
        @sort-change="handleSortChange"
      />
      <Table
        v-if="props.mode==='ltv'"
        ref="tableRef"
        v-model:displayColumns="displayColumns2"
        row-key="index"
        :data="tableData"
        :columns="columns2"
        :total="total"
        :selected-row-keys="selectedRowKeys"
        @select-change="handleSelectChange"
        @sort-change="handleSortChange"
      />
    </data-container>
  </t-space>
</template>

<script setup lang='ts'>
import { useIntelligencePredictionStore } from '@/store/intelligence/prediction/prediction.store';
import { ref, PropType, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import Table from 'common/components/table';
import DataContainer from 'common/components/Layout/DataContainer.vue';
import SvgBtnList from 'common/components/SvgIcon/SvgBtnList.vue';
// import { useMockData } from 'common/mock/table/mockData';
import { ANALYZE_DOWNLOAD_METRIC, ANALYZE_LTV_METRIC } from '../const/const';
import { SortInfo, SortOptions } from 'tdesign-vue-next';
import { Column, AnalyzeTableItem } from '../modal/prediction';

// Const
const { allData } = storeToRefs(useIntelligencePredictionStore());
const defaultPageSize = 10;
const defaultPageIndex = 1;
// const { isLoading } = useMockData(5);

// Refs
const props: {mode: 'download' | 'ltv'} = defineProps({
  mode: {
    type: String as PropType<'download' | 'ltv'>,
    default: 'download',
  },
  loading: {
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits(['handleSelectChange']);
const tableRef = ref<InstanceType<typeof Table> | null>(null);
const pageSize = ref(10);
const pageNum = ref(1);
const total = ref(100);
const orgTableData = ref();
const tableData = ref();
const columns = ref<Column[]>(ANALYZE_DOWNLOAD_METRIC.map((item) => {
  const column: Column & { cell?: Function} = { colKey: item, title: item, sorter: true };
  if (item === '') {
    column.type = 'multiple';
    column.sorter = false;
    column.required = true;
    // column.fixed = 'left';
    column.isHide = true;
  } else if (item === 'Competitor') {
    column.sorter = true;
    column.required = true;
    // column.fixed = 'left';
  } else {
    column.sorter = true;
    if (props.mode === 'download') {
      column.cell = (_h: any, { row }: any) => row?.[item]?.toLocaleString();
    } else {
      column.cell = (_h: any, { row }: any) => `$${row?.[item]}`;
    }
  }
  return column;
}));
const columns2 = ref<Column[]>(ANALYZE_LTV_METRIC.map((item) => {
  const column: Column = { colKey: item, title: item, sorter: true };
  if (item === '' || item === 'Competitor') {
    column.type = item === '' ? 'multiple' : '';
    column.sorter = item !== '';
    column.required = true;
    // column.fixed = 'left';
    column.isHide = item === '';
  }
  return column;
}));

const selectedRowKeys = ref<Array<string>>(allData.value.selectedKeys);
const displayColumns = ref(ANALYZE_DOWNLOAD_METRIC);
const displayColumns2 = ref(ANALYZE_LTV_METRIC);
const tableButtons = [
  {
    name: 'more',
    label: 'Select More Metrics',
    method: () => showTableSelect(),
  },
  {
    name: 'download',
    label: 'Download',
    method: () => download(),
  },
];
onMounted(() => {
  tableData.value = paginateArray(
    allData.value.analyzeTable,
    { pageIndex: defaultPageIndex, pageSize: defaultPageSize },
  );
  orgTableData.value = allData.value.analyzeTable;
  total.value = allData.value.analyzeTable.length;
  // tableData.value = allData.value.analyzeTable;
});
// 获取表数据，并且设置表的各种配置
// watch(() => allData.value.selectedKeys.length, async () => {
//   orgTableData.value = [];
//   const data = allData.value.analyzeTable;
//   orgTableData.value = data;
//   tableData.value = paginateArray(data, { pageIndex: defaultPageIndex, pageSize: defaultPageSize });
//   total.value = data.length;
//   selectedRowKeys.value = allData.value.selectedKeys;
// });

const handleSelectChange = async (value: Array<string>) => {
  selectedRowKeys.value = value;
  allData.value.selectedKeys = value;
  emit('handleSelectChange', value);
};

// 处理切换页面
const onPageChange = (current: number, info: any) => {
  pageNum.value = current;
  pageSize.value = info.pageSize;
  const result = paginateArray(orgTableData.value, { pageIndex: current, pageSize: info.pageSize });
  tableData.value = result;
};

// 获取不同页面的数据
function paginateArray(data: any[], pagination: {pageIndex: number, pageSize: number}): any[] {
  const { pageIndex, pageSize } = pagination;
  const startIndex = (pageIndex - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  return data.slice(startIndex, endIndex);
}

const handleSortChange = (sort: SortInfo, options: SortOptions<AnalyzeTableItem >) => {
  const sortedData = [...orgTableData.value || []];
  const { colKey } = options.col;
  // console.log('options', options);
  // console.log('options_sort', sort);

  sortedData.sort((a, b) => {
    if (colKey === undefined) {
      return 0;
    }

    const valueA = a[colKey as keyof AnalyzeTableItem];
    const valueB = b[colKey as keyof AnalyzeTableItem];

    // 根据排序顺序比较两个值
    if (sort?.sortBy) {
      const { descending } = sort;
      if (typeof valueA === 'number' && typeof valueB === 'number') {
        return descending ? (valueB - valueA) : (valueA - valueB);
      }
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return descending ? valueB.localeCompare(valueA) : valueA.localeCompare(valueB);
      }
      // 无法比较的情况
      return 0;
    }
    // // 反向排序
    // if (typeof valueA === 'number' && typeof valueB === 'number') {
    //   return valueB - valueA;
    // }
    // if (typeof valueA === 'string' && typeof valueB === 'string') {
    //   return valueB.localeCompare(valueA);
    // }
    // 无法比较的情况
    return 0;
  });
  // orgTableData.value = sortedData;
  tableData.value = paginateArray(sortedData, { pageIndex: pageNum.value, pageSize: pageSize.value });
};

const showTableSelect = () => {
  tableRef.value?.showMetricsSelect();
};

const download = () => {
  tableRef.value?.downloadContent('Analyze.xlsx');
};
</script>
  <style lang="scss" scoped>
  </style>

