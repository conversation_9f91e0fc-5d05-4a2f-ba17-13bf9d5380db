<template>
  <!-- Card Menu -->
  <t-card
    v-if="props.cardType === 'normal'"
    :title="props.title"
    :size="props.size"
    :theme="props.theme"
    :cover="props.cover"
    :loading="props.loading"
    :bordered="props.bordered"
    :style="props.selectedCompetitor.find(
      (card:CompetitorListGroupContentModal) => card.competitor_code == props.id) ? 'border-color:cornflowerblue' : ''"
    class="card-menu"
    @click="handleClick(props.id)"
  >
    <template #avatar>
      <t-avatar
        lazy
        :image="props.avatar"
        size="56px"
      />
    </template>
  </t-card>
  <!-- Selected Card -->
  <div class="relative">
    <t-card
      v-if="props.cardType === 'selector'"
      :size="props.size"
      :theme="props.theme"
      :cover="props.cover"
      :loading="props.loading"
      :bordered="props.bordered"
      class="selector-card"
    >
      <div class="flex justify-center">
        <img
          :src="props.avatar !== '' ? props.avatar : logo"
          class="image-icon"
        >
      </div>
      <div class="truncate my-3">{{ props.title }}</div>
    </t-card>
    <t-tooltip :content="props.title">
      <t-card
        v-if="props.cardType === 'selector'"
        :bordered="false"
        class="delete-component"
        @click="onClickRemove(props.id)"
      >
        <icon
          name="delete"
          class="delete-icon"
          size="medium"
        />
      </t-card>
    </t-tooltip>
  </div>
</template>

<script setup lang="ts">
import { CardProps } from '../const/const';
import { Icon } from 'tdesign-icons-vue-next';
import { CompetitorListGroupContentModal } from '../modal/prediction';
import logo from '@/assets/icons/none.svg';

const props = defineProps(CardProps);
const emit = defineEmits(['onAddChange', 'onRemove']);

const handleClick = (id: string) => {
  emit('onAddChange', id);
};

const onClickRemove = (id: string) => {
  emit('onRemove', id);
};
</script>

<style scoped>
.delete-component {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.4);
  opacity: 0;
  cursor: pointer;
}
.delete-icon {
  color: white;
}
.selector-card {
  display: block;
  text-align: center;
  width: 100%;
  height: 100%;
}
.delete-component:hover {
  opacity: 1;
}
.card-menu:hover {
  border-color: cornflowerblue;
  cursor: pointer;
  transform: scale(1.03);
}

.image-icon {
  max-width: 66px;
  min-width: 66px;
  min-height: 66px;
}
</style>
