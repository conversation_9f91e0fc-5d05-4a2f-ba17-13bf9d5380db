<template>
  <t-collapse :borderless="props.borderless" :default-expand-all="props.defaultExpandAll">
    <t-collapse-panel :header="props.header">
      <t-list v-for="groups in props.competitor.groups" :key="groups.group_name">
        <t-list-item>
          <t-row :gutter="16" class="min-w-full">
            <t-col
              v-for="competitor in groups.competitors" :key="competitor.group_name" :span="4"
              class="my-2"
            >
              <Card
                :id="competitor.competitor_code" :title="competitor.competitor_name" :loading="props.loading"
                :avatar="competitor.competitor_icon" :bordered="true" :selected-competitor="props.selectedCompetitor"
                @on-add-change="handleSelectCompetitor"
              />
            </t-col>
          </t-row>
        </t-list-item>
      </t-list>
    </t-collapse-panel>
  </t-collapse>
</template>

<script setup lang="ts">
import { CollapseProps } from '../const/const';
import Card from '../components/Card.vue';

const props = defineProps(CollapseProps);
const emit = defineEmits(['onAddChange']);

const handleSelectCompetitor = (id: number) => {
  emit('onAddChange', id);
};
</script>

<style scoped></style>
