<template>
  <t-space class="self-center">
    <t-steps
      :layout="props.layout"
      :sequence="props.sequence"
      :current="props.current"
      :theme="props.theme"
      readonly
    >
      <t-step-item
        v-for="item in props.content"
        :key="item.value"
        class="md:w-52 sm:w-auto"
        :title="item.title"
        :extra="item.extra"
        :status="item.status"
      />
    </t-steps>
  </t-space>
</template>
<script lang="ts" setup>
import { StepsModal } from '../modal/prediction';
import { StepsProps } from '../const/const';

const props: StepsModal = defineProps(StepsProps);
</script>
