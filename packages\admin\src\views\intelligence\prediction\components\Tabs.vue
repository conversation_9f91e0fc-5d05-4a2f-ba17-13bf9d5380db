<template>
  <div>
    <!-- <t-tabs
      v-model="selectedId"
      theme="card"
      :disabled="props.disabled"
      @change="onChange"
      @add="addTab"
      @remove="removeTab"
    >
      <t-tab-panel
        v-for="(item) in props.list"
        :key="item.value"
        :value="item.value"
        :removable="true"
        :disabled="item.disabled === true"
      >
        <template #label>
          <div class="flex items-center">
            <span class="mr-[8px] label-icon">
              <slot :name="item.labelIconSlotName" />
            </span>
            <Text
              class="label"
              :content="item.label"
              color="var(--aix-text-color-black-primary)"
            />
          </div>
        </template>
        <div>
          <slot :name="item.value" />
        </div>
      </t-tab-panel>
    </t-tabs> -->
    <t-head-menu
      v-model="selectedId" theme="light" class="rounded-t-2xl"
      @change="onChange"
    >
      <t-menu-item
        v-for="(item, index) in props.list"
        :key="item.toString()"
        :value="item.value"
      >
        <span>{{ item.label }}</span>
        <SvgIcon
          name="close"
          size="8px"
          class="ml-[10px] mb-[2px] text-xs"
          @click="removeTab({ index, })"
        />
      </t-menu-item>
    </t-head-menu>
    <div v-for="(item) in props.list" :key="item.toString() + new Date().getTime()">
      <slot v-if="item.value === selectedId" :name="item.value" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { toRef } from 'vue';
import type { ItabPanelProps } from 'common/components/trade/ads-management/tabs/type';
// import Text from 'common/components/Text';
import SvgIcon from 'common/components/SvgIcon';
import { CompetitorListGroupModal } from '../modal/prediction';

const emit = defineEmits(['change', 'addTab', 'removeTab']);
const props = defineProps({
  // tabs数据
  list: {
    type: Array as () => Array<ItabPanelProps & CompetitorListGroupModal & {label: string, value: string | number}>,
    default: () => [],
  },
  value: {
    type: Number,
    default: 0,
  },
  disabled: {
    type: Boolean,
    default: () => false,
  },
});
const selectedId = toRef(props, 'value');

// 选项卡切换事件
function onChange(tab: string) {
  emit('change', tab);
}

// const addTab = () => {
//   emit('addTab');
// };

const removeTab = ({ index }: { index: number}) => {
  emit('removeTab', index);
};

</script>
<style lang="scss" scoped>
// tab栏 背景色

:deep(.t-tabs__nav--card) {
  // @apply bg-white-secondary;
  background: var(--td-gray-color-1);
}
// 激活时
:deep(.t-tabs__nav--card.t-tabs__nav-item.t-is-active) {
  @apply bg-white-primary p-[16px] rounded-t-large;
  .label {
    @apply text-black-primary;
  }
  .label-icon {
    stroke: var(--aix-text-color-black-primary);
    @apply text-black-primary;
  }
}
//去掉边框
:deep(.t-tabs__nav--card.t-tabs__nav-item) {
  @apply border-none p-[16px] rounded-t-large bg-transparent;
}
//tab之间的间距
:deep(.t-tabs__nav--card.t-tabs__nav-item:not(:first-of-type)) {
  @apply ml-[8px];
}
:deep(.t-tabs__nav--card) {
  @apply rounded-t-large;
}
:deep(.t-tabs) {
  @apply rounded-extraLarge;
  overflow: hidden;
}
:deep(.t-head-menu) {
  background-color: transparent;
}
:deep(.t-head-menu__inner) {
  height: auto;
}
:deep(.t-head-menu__inner .t-menu:first-child) {
  margin-left: 0;
}
:deep(.t-menu) {
  overflow-y: auto;
}
:deep(.t-menu__item) {
  border-radius: 15px 15px 0 0;
  height: -webkit-fill-available;
}
:deep(.t-menu__item.t-is-active) {
  color: black !important;
  background-color: white !important;
}
/* 隐藏所有的 .text-xs */
:deep(.text-xs) {
  display: none;
}

/* 仅当 .t-menu__item 包含 .t-is-active 类时显示 .text-xs */
:deep(.t-menu__item.t-is-active .text-xs) {
  display: inline;
}

</style>
