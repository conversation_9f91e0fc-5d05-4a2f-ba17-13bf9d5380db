import { ITableCols } from 'common/components/table/type';
import { InputNumber, Comment, Tooltip, Icon, MessagePlugin } from 'tdesign-vue-next';
import { useIntelligencePredictionStore } from '@/store/intelligence/prediction/prediction.store';
import { CompetitorExportListModal, CompetitorListGroupContentModal } from '../modal/prediction';
export function useEvaluationTable({
  data,
}: {data: (CompetitorListGroupContentModal & CompetitorExportListModal)[];}) {
  const store = useIntelligencePredictionStore();
  const tableData = data;
  const cols: ITableCols[] = [
    {
      colKey: 'index',
      title: '#',
      width: 50,
    },
    {
      title: 'Competitor',
      colKey: 'competitor_name',
      align: 'left',
      ellipsis: true,
      cell: (_h: any, { row }: any) => (
          <Comment
          avatar={row.competitor_icon}
          content={() => <div class="truncate">{row.competitor_name}</div>}
        />),
    },
    {
      title: () => (
        <div>
          Similarity
      <Tooltip content='The degree of similarity between game content and gameplay. The value is between 0.1 and 1, with 1 being completely similar to the competitor.'>
         <Icon
              class="ml-1"
              name="info-circle"
            /></Tooltip>
        </div>),
      colKey: 'similarity_score',
      edit: {
        component: InputNumber,
        props: {
          clearable: true,
          autofocus: true,
          step: 0.1,
          placeholder: '0.1 - 1',
          decimalPlaces: 2,
          max: 1,
          min: 0.1,
          allowInputOverLimit: false,
        },
        validateTrigger: 'change',
        on: () => ({
          onBlur: () => {
            store.evaluationEditStatus = true;
          },
          onEnter: (ctx: { e: { preventDefault: () => void; }; }) => {
            ctx?.e?.preventDefault();
          },
        }),
        rules: [
          { required: true, message: 'cannot be empty' },
          { max: 1, message: 'cannot more than 1', type: 'warning' },
          { min: 0.1, message: 'cannot less than 0.1', type: 'warning' },
        ],
        abortEditOnEvent: ['onEnter'],
        onEdited: (context) => {
          if (context.newRowData.similarity_score > 1 || context.newRowData.similarity_score < 0) {
            MessagePlugin('error', 'Similarity Score had to be between 1 and 0');
          } else {
            tableData[context.rowIndex].similarity_score = parseFloat(context.newRowData.similarity_score);
            store.evaluationEditStatus = false;
          }
        },
        defaultEditable: false,
      },
    },
    {
      title: () => (
        <div>
          Commercial Competitiveness
      <Tooltip content='The potential and depth of the game to attract players to pay. The value must be greater than 0.1, the value greater than 1 means stronger than the competitor.'>
         <Icon
              class="ml-1"
              name="info-circle"
            /></Tooltip>
        </div>),
      colKey: 'commercial_score',
      edit: {
        component: InputNumber,
        props: {
          clearable: true,
          autofocus: true,
          placeholder: '>= 0.1',
          step: 0.1,
          decimalPlaces: 2,
          min: 0.1,
          max: 999,
          allowInputOverLimit: false,
        },
        validateTrigger: 'change',
        on: () => ({
          onBlur: () => {
            store.evaluationEditStatus = true;
          },
          onEnter: (ctx: { e: { preventDefault: () => void; }; }) => {
            ctx?.e?.preventDefault();
          },
        }),
        abortEditOnEvent: ['onEnter'],
        onEdited: (context) => {
          if (context.newRowData.commercial_score < 0.1) {
            MessagePlugin('error', 'Commercial Score had to be more than or equal to 0.1');
          } else if (context.newRowData.commercial_score >= 999) {
            MessagePlugin('error', 'Commerical Score is out of range');
            tableData[context.rowIndex].commercial_score = 999;
          } else {
            tableData[context.rowIndex].commercial_score = parseFloat(context.newRowData.commercial_score);
            store.evaluationEditStatus = false;
          }
        },
        rules: [
          { required: true, message: 'cannot be empty' },
          { max: 999, message: 'cannot more than 999', type: 'warning' },
          { min: 0.1, message: 'cannot less than 0.1', type: 'warning' },
        ],
        defaultEditable: false,
      },
    },
    {
      title: () => (
        <div>
          Market Competitiveness
      <Tooltip  placement="top-right" content='The market popularity of the game that attracts players to download. The value must be greater than 0.1, the value greater than 1 means stronger than the competitor.'>
         <Icon
              class="ml-1"
              name="info-circle"
            /></Tooltip>
        </div>),
      colKey: 'market_score',
      edit: {
        component: InputNumber,
        props: {
          clearable: true,
          autofocus: true,
          placeholder: '>= 0.1',
          step: 0.1,
          decimalPlaces: 2,
          min: 0.1,
          max: 999,
          allowInputOverLimit: false,
        },
        validateTrigger: 'change',
        on: () => ({
          onBlur: () => {
            store.evaluationEditStatus = true;
          },
          onEnter: (ctx: { e: { preventDefault: () => void; }; }) => {
            ctx?.e?.preventDefault();
          },
        }),
        abortEditOnEvent: ['onEnter'],
        onEdited: (context) => {
          if (context.newRowData.market_score < 0.1) {
            MessagePlugin('error', 'Market Score had to be more than or equal to 0.1');
          } else if (context.newRowData.market_score >= 999) {
            MessagePlugin('error', 'Market Score is out of range');
            tableData[context.rowIndex].market_score = 999;
          } else {
            tableData[context.rowIndex].market_score = parseFloat(context.newRowData.market_score);
            store.evaluationEditStatus = false;
          }
        },
        rules: [
          { required: true, message: 'cannot be empty' },
          { max: 999, message: 'cannot more than 999', type: 'warning' },
          { min: 0.1, message: 'cannot less than 0.1', type: 'warning' },
        ],
        defaultEditable: false,
      },
    },
  ];

  return {
    cols,
    tableData,
  };
}
