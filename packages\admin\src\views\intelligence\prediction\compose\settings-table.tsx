import { ITableCols } from 'common/components/table/type';
import { LoadingIcon, CheckCircleFilledIcon, EditIcon, DeleteIcon } from 'tdesign-icons-vue-next';
import { CompetitorListGroupContentModal, StatusNameList } from '../modal/prediction';
import { SETTINGSEDITOPTION } from '../const/const';
import { Tooltip, Icon, Tag, Table, Comment, Switch, Dropdown, DropdownOption, Popconfirm, Space } from 'tdesign-vue-next';
export function useSettingsTable({
  method: {
    onChangeApply,
    onRemove,
    onEdit,
  },
}: { method: {onChangeApply: (input: boolean | string |number, index: number) => void,
  onRemove: (index: number) => void,
  onEdit: (content: DropdownOption, key: number) => void}}) {
  const statusNameList: StatusNameList = {
    success: { label: 'Ready', theme: 'success', icon: <CheckCircleFilledIcon /> },
    pending: { label: 'Pending', theme: 'warning', icon: <LoadingIcon /> },
  };

  const gameColumn: ITableCols[] = [
    {
      colKey: 'index',
      title: '#',
      width: 50,
    },
    {
      title: 'Competitor',
      colKey: 'competitor_name',
      align: 'left',
      ellipsis: true,
      cell: (_h: any, { row }: any) => (
          <Comment
          avatar={row.competitor_icon}
          content={() => <div class="truncate">{row.competitor_name}</div>}
        />),
    },
    {
      title: () => (
        <div class="h-[22px]">
          <div class="max-w-[100%] relative pr-[20px]">
            <div class="inline-block max-w-[100%] overflow-hidden overflow-ellipsis whitespace-nowrap">Similarity</div>
            <Tooltip content={'The degree of similarity between game content and gameplay. The value is between 0.1 and 1, with 1 being completely similar to the competitor.'}>
              <Icon
                class="ml-1 w-[16px] h-[16px] h-[16px] absolute top-[50%] bottom-[0px] translate-y-[-11px]"
                name="info-circle"
            /></Tooltip>
          </div>
        </div>
      ),
      colKey: 'similarity_score',
    },
    {
      title: () => (
        <div class="h-[22px]">
          <div class="max-w-[100%] relative pr-[20px]">
            <div class="inline-block max-w-[100%] overflow-hidden overflow-ellipsis whitespace-nowrap">Commercial Competitiveness</div>
            <Tooltip content={'The potential and depth of the game to attract players to pay. The value must be greater than 0.1, the value greater than 1 means stronger than the competitor.'}>
              <Icon
                class="ml-1 w-[16px] h-[16px] h-[16px] absolute top-[50%] bottom-[0px] translate-y-[-11px]"
                name="info-circle"
              /></Tooltip>
          </div>,
        </div>
      ),
      colKey: 'commercial_score',
    },
    {
      title: () => (
        <div class="h-[22px]">
          <div class="max-w-[100%] relative pr-[20px]">
            <div class="inline-block max-w-[100%] overflow-hidden overflow-ellipsis whitespace-nowrap">Market Competitiveness</div>
            <Tooltip placement="top-right" content={'The market popularity of the game that attracts players to download. The value must be greater than 0.1, the value greater than 1 means stronger than the competitor.'}>
              <Icon
                class="ml-1 w-[16px] h-[16px] h-[16px] absolute top-[50%] bottom-[0px] translate-y-[-11px]"
                name="info-circle"
            /></Tooltip>
         </div>,
        </div>
      ),
      colKey: 'market_score',
    },
  ];

  const cols: ITableCols[] = [
    {
      colKey: 'apply',
      title: 'Apply',
      width: 70,
      cell: (_h: any, { row }: any) => {
        if (row.apply !== null) {
          return (
            <Switch key={row.key}  v-model={row.apply} onChange={
              (e: boolean |string|number) => onChangeApply(e, row.key ?? null)}/>
          );
        }
        return '';
      },
    },
    {
      title: () => (
        <div>
          Version
          <Tooltip placement="top-right" content={'Version with "*" that include own side game will only show data of predicted game.'}>
         <Icon
              class="ml-1"
              name="info-circle"
            /></Tooltip>
        </div>),
      colKey: 'version_name',
      align: 'left',
      width: 1400,
      ellipsis: true,
      cell: (h, { row }) => {
        if (typeof row.version_name === 'object') {
          return (
            <div>
          <Table
          display-columns={gameColumn.map(item => item.colKey)}
          resizable
          row-key="index"
          data={row.version_name.map((game: CompetitorListGroupContentModal, index: number) => ({
            index: index + 1,
            competitor_name: game.competitor_name,
            competitor_icon: game.competitor_icon,
            similarity_score: game.similarity_score,
            commercial_score: game.commercial_score,
            market_score: game.market_score,
          }))}
          columns={gameColumn}/>
          </div>
          );
        }
        return (<span>{row.version_name} {row?.status && row.status === 'success'
          ? (<em style={{ color: 'red' }}>*</em>) : ''}</span>);
      },
    },
    {
      title: () => (
        <div>
          Status
          <Tooltip placement="top" content={'Ready means that the data of this version has been prepared. Preparing means the data is being got and calculated.'}>
         <Icon
              class="ml-1"
              name="info-circle"
            /></Tooltip>
        </div>),
      colKey: 'status',
      align: 'left',
      cell: (h, { row }) => {
        if (row.status) {
          return (<Tag shape="round" theme={statusNameList[row.status ?? 'pending'].theme} variant="outline">
              {statusNameList[row.status ?? 'pending'].icon}
              {statusNameList[row.status ?? 'pending'].label}
            </Tag>);
        }
        return '';
      },
    },
    {
      title: 'Action',
      align: 'left',
      colKey: 'action',
      cell: (_h: any, { row }: any) => {
        if (row.action) {
          return (<Space>
            <Dropdown
              onClick={(dropdownItem: DropdownOption) => onEdit(dropdownItem, row.key)}
              options={SETTINGSEDITOPTION}
            >
        <EditIcon />
      </Dropdown>
      <Popconfirm showArrow={false} onConfirm={() => onRemove(row.key)} content={`Comfirm Remove Version: ${row.version_name}`}>
        <DeleteIcon />
        </Popconfirm>
      </Space>);
        } return '';
      },
    },
  ];

  return {
    cols,
  };
}
