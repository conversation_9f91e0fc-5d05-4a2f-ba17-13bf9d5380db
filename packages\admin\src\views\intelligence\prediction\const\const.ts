import { PropType } from 'vue';
import {
  StepsLayoutModal,
  StepsContentModal,
  StepsSequence,
  StepsTheme,
  StepsModal,
  CardTheme,
  CardSize,
  CardType,
  CompetitorListGroupContentModal,
  CompetitorListContentModal,
  CompetitorListModal,
  TabsTheme,
  TabsSize,
  TabsPlacement,
  CompetitorListGroupModal,
  InputOptionsModal,
  AllData,
  CompetitorExportListModal,
} from '../modal/prediction';
// import CountryCascader from 'common/components/CountryCascader';
import NewCascader from 'common/components/NewCascader';
import MultipleSelect from 'common/components/Select/container.vue';
import { Select } from 'tdesign-vue-next';
import { Platform } from '@/store/intelligence/creative/config/selectOptions.json';

export const EditOption = {
  addCompetitor: 0,
  evaluation: 1,
  rename: 2,
};

// API
export const APISTATUS = {
  ok: 0,
  fail: 99999,
  err: 50000,
  noallow: 40000,
};

// Steps
export const StepsProps = {
  layout: {
    type: String as PropType<StepsLayoutModal>,
    default: 'vertical',
  },
  current: {
    type: Number,
    default: 1,
  },
  sequence: {
    type: String as PropType<StepsSequence>,
    default: 'positive',
  },
  theme: {
    type: String as PropType<StepsTheme>,
    default: 'default',
  },
  content: {
    type: Array as PropType<StepsContentModal[]>,
    default: [],
  },
};

export const StepsSequenceItem = {
  settings: -1,
  addCompetitor: 0,
  evaluation: 1,
  preparingData: 2,
  overview: 3,
};

// Settings
export const SettingsProps = {
  steps: {
    type: Object as PropType<StepsModal>,
    default: {},
  },
  data: {
    type: Object as PropType<{
      selectedCompetitor: (CompetitorListGroupContentModal & CompetitorExportListModal)[];
      competitor: CompetitorListModal;
    }>,
    default: () => ({
      selectedCompetitor: [],
      competitor: {},
    }),
  },
};

export const SETTINGSEDITOPTION = [
  { content: 'Add Competitor', value: EditOption.addCompetitor },
  { content: 'Evaluation', value: EditOption.evaluation },
  { content: 'Rename', value: EditOption.rename }];

// Add Competitor
export const AddCompetitorProps = {
  steps: {
    type: Object as PropType<StepsModal>,
    default: {},
  },
  data: {
    type: Object as PropType<{
      selectedCompetitor: (CompetitorListGroupContentModal & CompetitorExportListModal)[];
      groupCompetitor: CompetitorListContentModal,
      selectedGroupCompetitor: (CompetitorListGroupModal & InputOptionsModal)[],
      competitor: CompetitorListModal;
    }>,
    default: () => ({
      selectedCompetitor: [],
      competitor: {},
    }),
  },
};

// Evaluation
export const EvaluationProps = {
  steps: {
    type: Object as PropType<StepsModal>,
    default: {},
  },
  data: {
    type: Object as PropType<{
      selectedCompetitor: (CompetitorListGroupContentModal & CompetitorExportListModal)[];
      groupCompetitor: CompetitorListContentModal,
      gameId: number;
    }>,
    default: () => ({
      selectedCompetitor: [],
    }),
  },
};

export const CollapseProps = {
  borderless: {
    type: Boolean,
    default: false,
  },
  defaultExpandAll: {
    type: Boolean,
    default: false,
  },
  header: {
    type: String,
    default: '',
  },
  loading: {
    type: Boolean,
    default: 'false',
  },
  competitor: {
    type: Object as PropType<CompetitorListContentModal>,
    default: {},
  },
  selectedCompetitor: {
    type: Array as PropType<CompetitorListGroupContentModal[]>,
    default: [],
  },
};

export const CardProps = {
  title: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: '',
  },
  theme: {
    type: String as PropType<CardTheme>,
    default: 'normal',
  },
  size: {
    type: String as PropType<CardSize>,
    default: 'medium',
  },
  loading: {
    type: Boolean,
    default: false,
  },
  description: {
    type: String,
    default: '',
  },
  avatar: {
    type: String,
    default: '',
  },
  cover: {
    type: String,
    default: '',
  },
  bordered: {
    type: Boolean,
    default: true,
  },
  cardType: {
    type: String as PropType<CardType>,
    default: 'normal',
  },
  selectedCompetitor: {
    type: Array as PropType<CompetitorListGroupContentModal[]>,
    default: [],
  },
};

export const TabsProps = {
  theme: {
    type: String as PropType<TabsTheme>,
    default: 'normal',
  },
  placement: {
    type: String as PropType<TabsPlacement>,
    default: 'top',
  },
  size: {
    type: String as PropType<TabsSize>,
    default: 'medium',
  },
  addable: {
    type: Boolean,
    default: false,
  },
  list: {
    type: Array as PropType<CompetitorListGroupModal[]>,
    default: [],
  },
};

// -------- Overview --------
export const InitialAllData: AllData = {
  competitorCodes: [],
  Market: [],
  group: [],
  downloadCode: {},
  arr: {},
  lineChart: {
    x: [],
    ltv: {
      7: [] as any,
      30: [] as any,
      90: [] as any,
      180: [] as any,
      360: [] as any,
    },
    download: {
      7: [] as any,
      30: [] as any,
      90: [] as any,
      180: [] as any,
      360: [] as any,
    },
  },
  analyzeLineChart: {
    x: [],
    ltv: {},
    download: {},
  },
  WorldMap: {
    download: {
      7: [] as any,
      30: [] as any,
      90: [] as any,
      180: [] as any,
      360: [] as any,
    },
    ltv: {
      7: [] as any,
      30: [] as any,
      90: [] as any,
      180: [] as any,
      360: [] as any,
    },
  },
  barChart: {
    x: [],
    ltv: {
      7: [] as any,
      30: [] as any,
      90: [] as any,
      180: [] as any,
      360: [] as any,
    },
    download: {
      7: [] as any,
      30: [] as any,
      90: [] as any,
      180: [] as any,
      360: [] as any,
    },
  },
  table: [] as any,
  dashboard: [] as any,
  analyzeTable: [] as any,
  records: [],
  worldSelect: [], // Form中所选择的国家
  marketSelect: [] as any,
  selectedKeys: [], // Table中所选择的国家
  selectedPlatform: 3, // Form中所选择的平台
  barA: [], // 柱状图平均数据
  lineA: [], // 线条图平均数据
  gameCompetitorCodes: [], // 当前业务游戏与所有竞品的code
};

// Tab
export const OVERVIEW = 'Overview';
export const ANALYZE = 'Analyze';

// Table
export const MAX_COUNTRY_SELECTED = 9;
export const METRIC = ['', 'Market',
  'P-D7 Downloads', 'P-D7 LTV',
  'P-D30 Downloads', 'P-D30 LTV',
  'P-D90 Downloads', 'P-D90 LTV',
  'P-D180 Downloads', 'P-D180 LTV',
  'P-D360 Downloads', 'P-D360 LTV'];
export const ANALYZE_DOWNLOAD_METRIC = ['', 'Competitor',
  'P-D1 Downloads', 'P-D3 Downloads', 'P-D7 Downloads',
  'P-D14 Downloads', 'P-D30 Downloads', 'P-D60 Downloads',
  'P-D90 Downloads', 'P-D120 Downloads', 'P-D150 Downloads',
  'P-D180 Downloads', 'P-D210 Downloads', 'P-D240 Downloads', 'P-D270 Downloads', 'P-D300 Downloads',
  'P-D330 Downloads', 'P-D360 Downloads'];
export const ANALYZE_LTV_METRIC = ['', 'Competitor',
  'P-D1 LTV', 'P-D3 LTV', 'P-D7 LTV',
  'P-D14 LTV', 'P-D30 LTV', 'P-D60 LTV',
  'P-D90 LTV', 'P-D120 LTV', 'P-D150 LTV',
  'P-D180 LTV', 'P-D210 LTV', 'P-D240 LTV', 'P-D270 LTV', 'P-D300 LTV',
  'P-D330 LTV', 'P-D360 LTV'];
export const TABLE_HEAD = [
  1, 3, 7, 14, 30, 60, 90, 120, 150, 180, 210, 240, 270, 300, 330, 360,
];
// Chart
export const COLORS = [
  '#376ae7',
  '#3ecbff',
  '#4fcb87',
  '#ffd14e',
  '#ed9142',
  '#e95f6a',
  '#cf61ac',
  '#8164cd',
  '#839df8',
  '#4fb0df',
];
export const ALLDAYS = [7, 30, 90, 180, 360];
export const ALLDAYSANALYZE = Array.from({ length: 360 }, (_, index) => index + 1);
export const ALLDAYS_X_AXIS = Array.from({ length: 360 }, (_, index) => `D${index + 1}`);
export const ALLPLATFORM = [1, 2, 3];
export const MODE_DOWNLOAD = 'download';
export const MODE_LTV = 'ltv';
export const WORLD_MAP_CHART = 'map';
export const BAR_CHART = 'bar';
export const LINE_CHART = 'line';

export const ANALYZE_FILTER_CONDITION = [
  {
    name: Select,
    props: {
      list: [],
      label: 'Type',
      button: (textArr: string[] | string) => {
        if (textArr.length === 0) {
          return 0;
        }
        if (!Array.isArray(textArr)) return textArr;
      },
    },
    ext: {
      key: 'type',
      label: 'Type', // 包裹容器下拉列表中展示名称
      isAllowClose: false,
    },
  },
  // {
  //   name: CountryCascader,
  //   props: {
  //     labels: ['Region', 'Country/Market'],
  //     options: [],
  //     title: 'Country/Market',
  //     outReset: true,
  //   },
  //   ext: {
  //     key: 'market',
  //     label: 'Country/Market',
  //     isAllowClose: true,
  //   },
  // },
  {
    name: NewCascader, // Country Select,
    props: {
      options: [],
      levelList: [
        {
          label: 'Region',
          value: 'region',
        },
        {
          label: 'Country/Market',
          value: 'country',
        },
      ],
      title: 'Country/Market',
      isEmptyWhenSelectAll: true,
    },
    ext: {
      key: 'market',
      label: 'Market',
      isAllowClose: false,
    },
  },
  {
    name: MultipleSelect,
    props: {
      list: Platform.map(data => ({
        label: data.text,
        value: data.value,
      })),
      title: 'Platform',
      multiple: true,
    },
    ext: {
      key: 'platform',
      label: 'Platform', // 包裹容器下拉列表中展示名称
      isAllowClose: false,
    },
  },
];
