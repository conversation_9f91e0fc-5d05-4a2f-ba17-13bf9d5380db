import { UseCommonSearchReturn } from '@/compose/useCommonSearch';
import { ViewOperationReturn } from '@/compose/useHeadPanelView';
import { ArrayStringList, AttributesType, ObjectList, OrderByType, StringList } from '@/store/creative/dashboard/dashboard';
import { ComputedRef, Ref, ShallowRef, Slot } from 'vue';
import { IData } from 'common/components/BasicChart/type';
import { ViewItem } from '@/views/creative/dashboard/components/TabView';
import { OptionsItem } from 'common/components/Cascader';

// Steps
export type StepsLayoutModal = 'vertical' | 'horizontal';

export type StepsSequence = 'positive' | 'reverse';

export type StepsTheme = 'default' | 'dot';

export type StepsContentModal = {
  title: String | Slot | Function;
  value?: String | Number;
  extra?: String | Slot | Function;
  icon?: boolean | Slot | Function;
  status: 'default' | 'process' | 'finish' | 'error';
};

export type StepsModal = {
  layout: StepsLayoutModal;
  sequence?: StepsSequence;
  current: number;
  content: StepsContentModal[];
  theme?: StepsTheme;
};

export type StepsItemModal = {
  content: String;
};

export type ChangePageModal={
  selectedCompetitor?: (CompetitorListGroupContentModal & CompetitorExportListModal)[];
  selectedGroupCompetitor: (CompetitorListGroupModal & InputOptionsModal)[],
  groupCompetitor?: CompetitorListContentModal,
  data?: number,
  page: number
};

// Settings
export type SettingsModal = {
  steps: StepsModal;
  data: {
    selectedCompetitor: (CompetitorListGroupContentModal & CompetitorExportListModal)[];
    competitor: CompetitorListModal;
  };
};

export type EditModal={
  content: string,
  value: number,
};

// Add Competitor
export type CardTheme = 'normal' | 'poster1' | 'poster2';

export type CardSize = 'medium' | 'small';

export type CardType = 'selector' | 'normal';

export type InputOptionsModal = {
  label: string;
  value: string | number;
};

export type AddCompetitorModal = {
  steps: StepsModal;
  data: {
    selectedCompetitor: (CompetitorListGroupContentModal & CompetitorExportListModal)[];
    selectedGroupCompetitor: (CompetitorListGroupModal & InputOptionsModal)[],
    groupCompetitor: CompetitorListContentModal,
    competitor: CompetitorListModal;
  };
};

export type CompetitorListModal = {
  code: number;
  data: CompetitorListContentModal[];
  msg: string;
};

export type CompetitorListContentModal = {
  version_name: string;
  apply: number;
  game_id: number;
  status?: 'pending' | 'success' |'error';
  groups: CompetitorListGroupModal[];
};

export type CompetitorListGroupModal = {
  index: number;
  market: string;
  group_name: string;
  competitors: (CompetitorListGroupContentModal & CompetitorExportListModal)[];
};

export type CompetitorExportListModal={
  competitor_code: string,
  competitor_name: string,
  competitor_icon: string,
  competitor_type: 'mobile' | 'all',
  steam_id: string,
  dashboard: number
};

export type CompetitorListGroupContentModal = {
  competitor_code: string;
  competitor_name: string;
  competitor_icon: string;
  dashboard: number;
  market_score: number | null;
  commercial_score: number | null;
  similarity_score: number | null;
  market: string;
  group_name: string;
};

// Evaluation
export type TabsTheme = 'normal' | 'card';

export type TabsSize = 'medium' | 'large';

export type TabsPlacement = 'left' | 'top' | 'bottom' | 'right';

export type EvaluationModal = {
  steps: StepsModal;
  data: {
    selectedCompetitor: (CompetitorListGroupContentModal & CompetitorExportListModal)[];
    groupCompetitor: CompetitorListContentModal,
    gameId: number;
  };
};

export type EvaluationLineGraph={
  name: string,
  value: number,
  xLabel: string,
  yLabel: string,
};

// API Modal
export type GameCode ={
  game: string,
};

export type FindGameById={
  id: number,
};

export type CreateGameId={
  game: string,
  competitors: CompetitorListGroupContentModal[]
};

export type SearchGameModal={
  k: string,
  gameType: 'all' | 'mobile';
};

export type Ltv = {
  day?: string | number[];
  competitor?: string,
  country?: string,
  metric?: string,
  platform?: string,
  group?: any,
  downloadCode?: Record<string, any>,
};

export type CreateOrUpdateGroupModal={
  game_id: number,
  group: (CompetitorListGroupModal & InputOptionsModal)[],
};

export type SaveGameVersionModal={
  game_id: number,
  version_name: string,
  apply: number,
};

export type CompetitorByGameId={
  amount: number,
  channel: string,
  competitor_code: string,
  competitor_icon: string,
  competitor_name: string,
  countries: string,
  dashboard: number,
  date_absolute_begin: string,
  date_absolute_end: string,
  date_absolute_task: number,
  date_relative: number,
  date_type: number,
  id: number,
};

// Models Modal
export type DayData = {
  [key: string]: CountryData;
};

export type CountryData = {
  [key: string]: PlatformData;
};

export type PlatformData = {
  [key: string]: string[];
};

export type ProcessedDataType = {
  [key: number]: {
    [key: string]: {
      [key: string]: any;
    };
  };
};

export type LtvData = {
  [country: string]: {
    [day: string]: {
      ltv: number;
      download: number;
    };
  };
};

// 全局数据
export type AllData = {
  competitorCodes: string[];
  Market: [string, string][];
  group: any[];
  downloadCode: {
    [key: number]: number
  };
  arr: DayData
  lineChart: {
    x: any[];
    ltv: {
      [key: number]: {
        [key: string]: number | '';
      }
    };
    download: {
      [key: number]: {
        [key: string]: number;
      }
    };
  };
  analyzeLineChart: {
    x: any[];
    ltv: {
      [key: string]: {
        [key: string]: string | [];
      }
    };
    download: {
      [key: string]: {
        [key: string]: string | [];
      }
    };
  };
  WorldMap: {
    download: {
      [key: string]: {
        [key: string]: number
      }
    };
    ltv: {
      [key: string]: {
        [key: string]: number
      }
    };
  };
  barChart: {
    x: any[];
    ltv: {
      [key: number]: {
        [key: string]: number | '';
      }
    };
    download: {
      [key: number]: {
        [key: string]: number | '';
      }
    };
  };
  table: any[];
  analyzeTable: any[];
  records: any[];
  dashboard: any[];
  worldSelect: string[]; // Form中所选择的国家
  selectedKeys: any[]; // Table中所选择的国家
  selectedPlatform: any; // Form中所选择的平台
  marketSelect: any;
  barA: any[]; // Overview柱状图
  lineA: any[]; // Overview线条图
  gameCompetitorCodes: string[]; // 当前业务游戏与所有竞品的code
};

// Overview
// form
export type FormData = {
  platform: {
    platform: any[];
  };
  Market: {
    country: string[];
  };
};

// table
export type Column = {
  colKey: string;
  title: string;
  width?: number;
  type?: string;
  sorter?: boolean;
  required?: boolean;
  fixed?: string;
  isHide?: boolean,
};

export type OverviewTableItem = {
  Market: string,
  P_D7_Downloads: number,
  P_D7_LTV: number,
  P_D30_Downloads: number,
  P_D30_LTV: number,
  P_D90_Downloads: number,
  P_D90_LTV: number,
  P_D180_Downloads: number,
  P_D180_LTV: number,
  P_D360_Downloads: number,
  P_D360_LTV: number
};

export type AnalyzeTableItem = {
  Competitor: string,
  P_D1_Downloads: number,
  P_D2_Downloads: number,
  P_D3_Downloads: number,
  P_D7_Downloads: number,
  P_D14_Downloads: number,
  P_D30_Downloads: number,
  P_D60_Downloads: number,
  P_D90_Downloads: number,
  P_D120_Downloads: number,
  P_D150_Downloads: number,
  P_D180_Downloads: number,
  P_D210_Downloads: number,
  P_D240_Downloads: number,
  P_D270_Downloads: number,
  P_D300_Downloads: number,
  P_D330_Downloads: number,
  P_D360_Downloads: number,
  P_D1_LTV: number,
  P_D2_LTV: number,
  P_D3_LTV: number,
  P_D7_LTV: number,
  P_D14_LTV: number,
  P_D30_LTV: number,
  P_D60_LTV: number,
  P_D90_LTV: number,
  P_D120_LTV: number,
  P_D150_LTV: number,
  P_D180_LTV: number,
  P_D210_LTV: number,
  P_D240_LTV: number,
  P_D270_LTV: number,
  P_D300_LTV: number,
  P_D330_LTV: number,
  P_D360_LTV: number,
};

type StatusName = {
  label: string;
  theme: 'default' | 'success' | 'warning' | 'primary' | 'danger' | undefined;
  icon: JSX.Element;
};

export type StatusNameList = Record<string, StatusName>;

// Anlayze
export type FormType = {
  metric: string[],
  orderby: OrderByType[],
  groupby: string[],
  pageSize: number,
  pageIndex: number,
  where: any,
  region?: string[],
  top: number[],
  topKey: string[],
} & {
  [key in ArrayStringList]: string[];
} & {
  [key in StringList]: string;
} & {
  [key in ObjectList]: any;
};

export type MetricItemType ={
  format: string,
  key: string,
  title: string,
  label: string,
  colKey: string,
  type: string,
  groupName: string,
  width?: number,
  value?: string,
};

export type AnalyzeTypeDashboard= {
  // game: Ref<string>,
  isInit: Ref<boolean>,
  view: ViewOperationReturn,
  form: UseCommonSearchReturn<FormType, any>['form'],
  schema: UseCommonSearchReturn<FormType, any>['schema'],
  chartData: Ref<IData[]>,
  metricList: ShallowRef<MetricItemType[]>,
  color: string[],
  yAxisLabelFormat: (value: any, index: number) => any,
  tableLoading: Ref<boolean>,
  table: ComputedRef<any[]>,
  tableTotal: Ref<number>,
  allLoading: ComputedRef<boolean>,
  init: () => void,
  chartLoading: Ref<boolean>,
  swiperKey: Ref<string>,
  displayColumns: ShallowRef<string[]>
  tableAllColumns: ComputedRef<(MetricItemType | AttributesType)[]>,
  configLoading: Ref<boolean>,
  filterTotal: Ref<boolean>,
  getTableData: () => void,
  getChartData: () => void,
  downloadHandler: () => Promise<any[]>,
  updateViewList: (params: ViewItem) => void,
  attribute: Ref<AttributesType[]>,
  onResetHandler: () => void,
  getAllData: () => void,
  onFormSubmit: (newValue: any) => void,
  addViewHandler: (item: ViewItem) => void,
  hideAttributeMetricByOther: () => void,
  asyncDownloadHandler: (param: {user: string, fileName: string}) => void,
  getTableParam: Ref<Function>
  rbacWhere: ShallowRef<any[]>
  showSwiper: Ref<boolean>,
  filterAssetName: Ref<boolean>,
  options: ShallowRef<any>,
  dtstattimePresets: Ref<string>,
};

export type AnalyzeFormOptions = {
  fieldObj: {
    type?: Array<OptionsItem>,
    platform?: Array<OptionsItem>,
    market?: Array<OptionsItem>,
  },
  conditionList: any[],
};

export type AnalyzeFormParams = {
  type: number,
  // market: Array<string> | ComputedRef<string[]>,
  market: Record<string, any>,
  platform: Array<string>,
};

export type Market = {
  label: string;
  value: string;
  children?: Market[];
};

export type MarketFilter={
  value: string;
  children?: { value: string; label?: string }[];
};
