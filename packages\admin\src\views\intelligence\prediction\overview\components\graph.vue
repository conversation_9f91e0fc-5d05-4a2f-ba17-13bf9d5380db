<template>
  <t-space
    direction="vertical"
    class="bg-white-primary rounded-large p-[16px] overflow-y-auto flex flex-col gap-y-[16px] mb-6"
  >
    <div class="module_one rounded min-h-[450px] relative">
      <ComboChart
        v-if="graphData && !isDataLoading"
        :chart-type="['bar', 'line']"
        :detail-type="['', '', 'area']"
        :data-mode="DataMode.x"
        :y-axis-label-format="(value: string) => {
          return value.toLocaleString();
        }"
        :y-axis-secondary-label-format="(value: any) => {
          return `$${value.toLocaleString()}`;
        }"
        data-value-filed="value"
        data-item-field="seriesName"
        data-group-item-field="day"
        :data="graphData"
        is-show-legend
        is-show-bar-label
        label-color="#000"
        :color="['#69D19C', '#5086F3']"
        :label-formatter="(params: any) => {
          return Number(params.value).toLocaleString();
        }"
        :y-axis-secondary-max-config="{ max: null, interval: null }"
        :y-axis-first-max-config="{ max: null, interval: null }"
        :reg-rules="[{ name: 'value', value: ['s1000', 'decimal'] }]"
        is-legend-bar-bottom
        :legend-props="{ top: 'bottom', left: 'center', }"
        :grid="{ bottom: '10%', containLabel: true, left: 20, right: 20 }"
      />
      <FullLoading v-if="isDataLoading" />
    </div>
  </t-space>
</template>
<script setup lang="ts">
import { useIntelligencePredictionStore } from '@/store/intelligence/prediction/prediction.store';
import { defineAsyncComponent, onMounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { DataMode } from 'common/components/BasicChart/type.d';
import { ALLDAYS } from '../../const/const';
import FullLoading from 'common/components/FullLoading';

// Const
const ComboChart = defineAsyncComponent(() => import('common/components/ComboChart'));
const { allData, isDataLoading } = storeToRefs(useIntelligencePredictionStore());

// Refs
const graphData = ref();

// 初始化配置
onMounted(async () => {
  await initChart();
});

async function initChart() {
  graphData.value = await getChartData();
}

// 从全局数据里获取关于download和ltv的平均数据，并转换成图表所需的格式
async function getChartData() {
  const day = ALLDAYS.map((day: number) => `D${day}`);
  const campaign = ['P-Downloads', 'P-LTV'];
  const result = day.flatMap((dayItem, index) => campaign.map(seriesName => ({
    day: dayItem,
    seriesName,
    value: seriesName === campaign[0] ? allData.value.barA[index] : allData.value.lineA[index],
  })));
  return result;
}

watch(() => allData.value.barA, async () => {
  await initChart();
});
</script>
<style scoped lang="scss">
</style>
