<template>
  <t-space
    direction="vertical"
    class="bg-white-primary rounded-large p-[16px] overflow-y-auto flex flex-col gap-y-[16px] mb-6"
  >
    <data-container
      class="w-full"
      :total="total"
      :page-size="pageSize"
      :default-page="pageNum"
      :page-size-options="[10, 20, 30, 50, 100, 200]"
      :hide-header="false"
      @on-page-change="onPageChange"
    >
      <template #actionSlot>
        <div class="flex">
          <SvgBtnList :list="tableButtons" />
        </div>
      </template>
      <Table
        ref="tableRef"
        v-model:displayColumns="displayColumns"
        table-layout="auto"
        resizable
        row-key="index"
        :data="tableData"
        :columns="columns"
        :loading="isDataLoading"
        :total="total"
        :selected-row-keys="selectedRowKeys"
        @select-change="handleSelectChange"
        @sort-change="handleSortChange"
      />
    </data-container>
  </t-space>
</template>
<script setup lang='ts'>
import { useIntelligencePredictionStore } from '@/store/intelligence/prediction/prediction.store';
import { onMounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import SvgBtnList from 'common/components/SvgIcon/SvgBtnList.vue';
import Table from 'common/components/table';
import DataContainer from 'common/components/Layout/DataContainer.vue';
import { MAX_COUNTRY_SELECTED, METRIC } from '../../const/const';
import { Column, OverviewTableItem } from '../../modal/prediction';
import { SelectOptions, SortInfo, SortOptions } from 'tdesign-vue-next';

// Const
const { allData, isDataLoading } = storeToRefs(useIntelligencePredictionStore());
const { extractNumber } = useIntelligencePredictionStore();
const defaultPageSize = 10;
const defaultPageIndex = 1;

// Refs
const tableRef = ref<InstanceType<typeof Table> | null>(null);
const pageSize = ref(defaultPageSize);
const pageNum = ref(defaultPageIndex);
const total = ref(100);
const orgTableData = ref();
const tableData = ref();
const columns = ref<Column[]>(METRIC.map((item) => {
  const column: Column = { colKey: item, title: item, sorter: true, required: false };
  if (item === '' || item === 'Market') {
    column.sorter = item !== '';
    column.required = true;
    column.type = item === '' ? 'multiple' : '';
    column.isHide = item === '';
  }
  return column;
}));
const selectedRowKeys = ref<Array<string>>();
const displayColumns = ref(METRIC);

const tableButtons = [{
  name: 'more',
  label: 'Select More Metrics',
  method: () => showTableSelect(),
},
{
  name: 'download',
  label: 'Download',
  method: () => download(),
},
];

onMounted(async () => {
  init();
});

// 获取表数据，并且设置表的各种配置
function init() {
  // 默认选择8个， 最大选择9个
  allData.value.selectedKeys = allData.value.table.slice(0, MAX_COUNTRY_SELECTED - 1).map(data => data.index);
  selectedRowKeys.value = allData.value.selectedKeys;

  const data = allData.value.table;
  orgTableData.value = data;
  tableData.value = paginateArray(data, { pageIndex: defaultPageIndex, pageSize: defaultPageSize });
  total.value = data.length;
  pageNum.value = defaultPageIndex;
  pageSize.value = defaultPageSize;
}

const handleSelectChange = <T>(value: Array<string>, options?: SelectOptions<T>) => {
  if (options?.currentRowKey === 'CHECK_ALL_BOX') { // checkAll按钮
    // 全部被选择时，取消全部选择
    if (options?.type === 'uncheck') {
      selectedRowKeys.value = [];
      allData.value.selectedKeys = [];
    } else {
      // 如果选择的数量少于最大选择，则自动选择前面的数据
      if (allData.value.selectedKeys.length < MAX_COUNTRY_SELECTED) {
        // 确保在自动选择前面的数据时，不会移除以选择的数据
        const selectedKeys = Array.from(new Set([...allData.value.selectedKeys,
          ...tableData.value.map((data: { index: any; }) => data.index)]))
          .slice(0, MAX_COUNTRY_SELECTED);
        selectedRowKeys.value = selectedKeys;
        allData.value.selectedKeys = selectedKeys;
      } else {
        // 选择的数量等于最大选择时， 则取消全部选择
        selectedRowKeys.value = [];
        allData.value.selectedKeys = [];
      }
    }
  } else if (value.length <= MAX_COUNTRY_SELECTED) { // 行的选择按钮
    selectedRowKeys.value = value;
    allData.value.selectedKeys = value;
  }
};

const handleSortChange = (sort: SortInfo, options: SortOptions<OverviewTableItem>) => {
  const sortedData = [...orgTableData.value || []];
  const { colKey } = options.col;

  sortedData.sort((a, b) => {
    if (colKey === undefined) {
      return 0;
    }

    const valueA = a[colKey as keyof OverviewTableItem];
    const valueB = b[colKey as keyof OverviewTableItem];

    // 如果存在排序信息
    if (sort) {
      // 根据排序顺序比较两个值
      // 顺序排序
      if (!sort?.descending) {
        // 处理国家以外的排序
        if (colKey !== METRIC[1]) {
          return extractNumber(valueA) - extractNumber(valueB);
        }
        // 处理国家的排序
        return valueA.localeCompare(valueB);
      }

      // 反向排序
      if (colKey !== METRIC[1]) {
        return extractNumber(valueB) - extractNumber(valueA);
      }
      return valueB.localeCompare(valueA);
    }
    // 处理取消排序，回复原始的排序（以P-D7 Downloads的内容来排序）
    return extractNumber(b[METRIC[2]]) - extractNumber(a[METRIC[2]]);
  });
  orgTableData.value = sortedData;
  tableData.value = paginateArray(orgTableData.value, { pageIndex: pageNum.value, pageSize: pageSize.value });
};

// 处理切换页面
const onPageChange = (current: number, info: any) => {
  pageNum.value = current;
  pageSize.value = info.pageSize;
  const result = paginateArray(orgTableData.value, { pageIndex: current, pageSize: info.pageSize });
  tableData.value = result;
};

// 获取不同页面的数据
function paginateArray(data: any[], pagination: {pageIndex: number, pageSize: number}): any[] {
  const { pageIndex, pageSize } = pagination;
  const startIndex = (pageIndex - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  return data.slice(startIndex, endIndex);
}

const showTableSelect = () => {
  tableRef.value?.showMetricsSelect();
};

const download = () => {
  tableRef.value?.downloadContent('Overview.xlsx');
};

watch(() => allData.value.table, () => {
  init();
  if (allData.value.table.length > 0 && allData.value.table[0][METRIC[2]] === 0) {
    handleSelectChange([]);
  } else {
    handleSelectChange(allData.value.selectedKeys);
  }
});
</script>
<style lang="scss" scoped>
</style>
