<template>
  <div id="main" style="width: 100%; height: 600px;" />
  <div class="zoomButton">
    <button
      class="h-9 font-normal text-sm min-w-9"
      @click="handleZoom(true)"
    >
      <!-- <SvgIcon
        name="plus"
        size="16px"
      /> -->
      <icon
        name="plus"
        size="33px"
      />
    </button>
    <button
      class="h-9 font-normal text-sm min-w-9"
      @click="handleZoom(false)"
    >
      <!-- <SvgIcon
        name="minus"
        size="16px"
      /> -->
      <icon
        name="minus"
        size="33px"
      />
    </button>
  </div>
</template>
<script setup lang="ts">
import { useIntelligencePredictionStore } from '@/store/intelligence/prediction/prediction.store';
import { ref, onMounted, watch } from 'vue';
import { storeToRefs } from 'pinia';
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  TooltipComponent,
  VisualMapComponent,
  GeoComponent,
} from 'echarts/components';
import { MapChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import { Icon } from 'tdesign-icons-vue-next';
// import SvgIcon from 'common/components/SvgIcon';
import { ALLDAYS, MODE_DOWNLOAD, MODE_LTV } from '../../const/const';

// Const
const { getCountryFullName } = useIntelligencePredictionStore();
const { allData, selectedMode, selectedDay } = storeToRefs(useIntelligencePredictionStore());

// Refs
const worldJson = ref<any>(null);
const zoomValue = ref(1);

echarts.use([
  TitleComponent,
  TooltipComponent,
  VisualMapComponent,
  GeoComponent,
  MapChart,
  CanvasRenderer,
]);

// 初始化世界地图并加载世界地图
onMounted(async () => {
  const mapData = await getWorldMapData(MODE_DOWNLOAD, 7);
  await initWorldMap(mapData);
});

// 从全局数据里获取关于世界地图的数据，并转换成世界地图所需的格式
async function getWorldMapData(mode: 'download' | 'ltv', day: number) {
  const result: { name: string; value: number; }[] = [];
  allData.value.selectedKeys.forEach((country) => {
    const item = allData.value.WorldMap[mode][day][country];
    result.push({
      name: getCountryFullName(country),
      value: item,
    });
  });
  return result;
}

// 世界地图初始化
async function initWorldMap(mapData: any) {
  const chartDom = document.getElementById('main');
  if (!chartDom) return;
  const myChart = echarts.init(chartDom);
  myChart.showLoading();

  try {
    const response = await fetch('https://static.aix.intlgame.cn/web/json/world.json');
    worldJson.value = await response.json();
    echarts.registerMap('world', worldJson.value as any);
    const option = {
      tooltip: {
        trigger: 'item',
        showDelay: 0,
        transitionDuration: 0.2,
        axisPointer: {
          type: 'shadow',
          shadowStyle: {
            color: 'rgba(187,187,187,0.2)',
          },
        },
        triggerOn: 'mousemove',
        enterable: false,
        alwaysShowContent: false,
        formatter(params: any) {
          return worldHtml(params);
        },
      },
      visualMap: {
        itemHeight: '300px',
        left: 'left',
        min: 0,
        max: worldMapMax(mapData),
        formatter(params: any) {
          return selectedMode.value === MODE_LTV ? `$${params.toLocaleString()}` : params.toLocaleString();
        },
        inRange: {
          color: [
            '#f2f2f2',
            '#d7e7fd',
            '#BCCEFB',
            '#a0b6fa',
            '#839df8',
            '#6585f7',
            '#4970e3',
            '#295ccc',
            '#1a47b1',
            '#0f3196',
            '#061d79',
          ],
        },
        text: ['High', 'Low'],
        calculable: true,
        realtime: false,
      },
      series: [
        {
          type: 'map',
          roam: 'move',
          map: 'world',
          itemStyle: {
            normal: {
              borderColor: 'rgba(190, 190, 190,0.5)',
              borderWidth: 1,
              areaColor: '#f2f2f2',
            },
          },
          zoom: zoomValue.value,
          selectedMode: false,
          emphasis: {
            label: {
              show: false,
            },
            itemStyle: {
              areaColor: 'inherit',
              borderColor: 'white',
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          data: mapData,
        },
      ],
    };

    myChart.setOption(option);
    myChart.hideLoading();
  } catch (error) {
    console.error('Failed to load data:', error);
    myChart.showLoading({ text: 'Failed to load data' });
  }
}

// 获取数据的最大值
function worldMapMax(val: any[]) {
  if (!Array.isArray(val)) {
    return 200;
  }
  let a = 0;
  val.forEach((element: { value: number }) => {
    if (element.value > a) {
      a = element.value;
    }
  });
  return Math.ceil(a) === 0 ? 200 : Math.ceil(a);
}

// 国家详细数据信息框
function worldHtml(val: { name: string; value: number; }) {
  const formatValue = val.value.toLocaleString();
  const countryDisplayText = `<div class="MarketAll">
    <div class="market">
      <span>${val.name}</span><span></span>
    </div>
    <div class="ltvHtml">
      <span>P-${selectedDay.value} ${selectedMode.value === MODE_LTV ? 'LTV:' : 'Download:'}</span>
      ${val.value > 0 ? selectedMode.value === MODE_LTV ? `$${formatValue}` : `${formatValue}` : 0}
    </div>
  </div>`;
  return countryDisplayText;
}

function handleZoom(isZoom: boolean) {
  const chartDom = document.getElementById('main');
  if (isZoom) {
    zoomValue.value += 0.3;
  } else {
    zoomValue.value -= 0.3;
  }

  if (chartDom) {
    const myChart = echarts.init(chartDom);
    myChart.setOption({
      series: [
        {
          zoom: zoomValue.value,
        },
      ],
    });
  }
}

// 模式或者天数改变时，重新渲染世界地图
async function handleDataChange(modeOrDay: string, isMode: boolean) {
  const mode = isMode ? modeOrDay : selectedMode.value;
  const day = isMode ? selectedDay.value : modeOrDay;
  const dayNumber = Number(day.substring(1));

  if (ALLDAYS.includes(dayNumber)) {
    const mapData = await getWorldMapData(mode as 'download' | 'ltv', dayNumber);
    await initWorldMap(mapData);
  }
}

watch(selectedMode, async (mode) => {
  await handleDataChange(mode, true);
});

watch(selectedDay, async (day) => {
  await handleDataChange(day, false);
});

// 监控所选择的国家变化
watch(() => allData.value.selectedKeys, async () => {
  await handleDataChange(selectedMode.value, true);
});
</script>
<style lang="scss">
.is-selected, .is-selected:hover {
  z-index: 2;
  border-color: #006eff;
  background: #fff;
  color: #006eff;
}
.MarketAll .market {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #ccc;
  padding: 5px 0;
  width: 100%;
  cursor: pointer;
}
.MarketAll .ltvHtml {
  min-width: 150px;
  overflow: hidden;
  text-align: right;
  margin-top: 10px;
  font-weight: 700;
}
.MarketAll .ltvHtml span {
  font-weight: 400;
  float: left;
}
#worldMap {
  position: relative;
}
.zoomButton {
  position: absolute;
  right: 0;
  bottom: 0;
}
button {
  min-width: 36px;
}
button:hover {
  background-color:#ebeef2;
}
</style>
