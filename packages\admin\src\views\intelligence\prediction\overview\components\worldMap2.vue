<template>
  <div class="prediction-world-map-box w-full ">
    <worldMap
      v-if="!isLoading"
      :series="series"
      :tooltip="{
        formatter(params: any) {
          return worldHtml(params);
        },
      }"
      :visual-map="visualMap"
    />
  </div>
</template>
<script setup lang="ts">
import WorldMap from 'common/components/WorldMap/WorldMap.vue';
import { getCountryFullName } from 'common/components/WorldMap/util';
import { useLoading } from 'common/compose/loading';
import { useIntelligencePredictionStore } from '@/store/intelligence/prediction/prediction.store';
import { ref, onMounted, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { ALLDAYS, MODE_LTV } from '../../const/const';
import { WorldMapVisualMapModal } from 'common/components/WorldMap/modal';

// Const
const { allData, selectedMode, selectedDay, confirmResetCount } = storeToRefs(useIntelligencePredictionStore());

const series = ref<any[]>([{
  data: [],
}]);

const visualMap = ref<WorldMapVisualMapModal<any>>({});


const { isLoading, hideLoading, showLoading } = useLoading();

// 从全局数据里获取关于世界地图的数据，并转换成世界地图所需的格式
async function getWorldMapData(mode: 'download' | 'ltv', day: number) {
  showLoading();
  const result: { name: string; value: number; }[] = [];
  // allData.value.selectedKeys.forEach((country) => {
  //   const item = allData.value.WorldMap[mode][day][country];
  //   result.push({
  //     name: getCountryFullName(country),
  //     value: item,
  //   });
  // });
  const tempObj = allData.value?.WorldMap?.[mode]?.[day];
  if (tempObj) {
    const keys = Object.keys(tempObj);
    keys.forEach((key: any) => {
      const val = tempObj[key];
      result.push({
        name: getCountryFullName(key),
        value: val,
      });
    });
  };
  // allData.value.WorldMap
  return result;
}

// 初始化世界地图并加载世界地图
onMounted(async () => {
  handleDataChange();
  // const mapData = await getWorldMapData(MODE_DOWNLOAD, 7);
  // initWorldMap(mapData);
});


// 世界地图初始化
async function initWorldMap(mapData: any) {
  series.value = [
    {
      data: mapData,
    },
  ];
  visualMap.value = {
    min: 0,
    max: worldMapMax(mapData),
    formatter(params: any) {
      return selectedMode.value === MODE_LTV ? `$${params.toLocaleString()}` : params.toLocaleString();
    },
    inRange: {
      color: [
        '#f2f2f2',
        '#d7e7fd',
        '#BCCEFB',
        '#a0b6fa',
        '#839df8',
        '#6585f7',
        '#4970e3',
        '#295ccc',
        '#1a47b1',
        '#0f3196',
        '#061d79',
      ],
    },
    // text: ['High', 'Low'],
    calculable: true,
    realtime: false,
  };
  hideLoading();
}


// 获取数据的最大值
function worldMapMax(val: any[]) {
  if (!Array.isArray(val)) {
    return 200;
  }
  let a = 0;
  val.forEach((element: { value: number }) => {
    if (element.value > a) {
      a = element.value;
    }
  });
  return Math.ceil(a) === 0 ? 200 : Math.ceil(a);
}

// 国家详细数据信息框
function worldHtml(val: { name: string; value: number; }) {
  const formatValue = val.value.toLocaleString();
  const countryDisplayText = `<div class="MarketAll">
    <div class="market">
      <span>${val.name}</span><span></span>
    </div>
    <div class="ltvHtml">
      <span>P-${selectedDay.value} ${selectedMode.value === MODE_LTV ? 'LTV:' : 'Download:'}</span>
      ${val.value > 0 ? selectedMode.value === MODE_LTV ? `$${formatValue}` : `${formatValue}` : 0}
    </div>
  </div>`;
  return countryDisplayText;
}

// 模式或者天数改变时，重新渲染世界地图
async function handleDataChange() {
  // const mode = isMode ? modeOrDay : selectedMode.value;
  const day = selectedDay.value ?? 'D7';
  const dayNumber = Number(day.substring(1));

  if (ALLDAYS.includes(dayNumber)) {
    const mapData = await getWorldMapData(selectedMode.value as 'download' | 'ltv', dayNumber);
    initWorldMap(mapData);
  }
}

watch(selectedMode, async (_mode) => {
  await handleDataChange();
});

watch(selectedDay, async (_day) => {
  await handleDataChange();
});
watch(confirmResetCount, async (_confirmResetCount) => {
  await handleDataChange();
});

// 监控所选择的国家变化
// watch(() => allData.value.selectedKeys, async () => {
//   await handleDataChange(selectedMode.value, true);
// });
</script>
<style scoped>
</style>
