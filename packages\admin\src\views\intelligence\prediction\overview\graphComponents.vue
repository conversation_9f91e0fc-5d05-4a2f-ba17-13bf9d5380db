<template>
  <t-space
    direction="vertical"
    class="bg-white-primary rounded-large p-[16px] overflow-y-auto flex gap-y-[16px] mb-6"
  >
    <div class="flex gap-6 w-full justify-between">
      <div class="flex gap-3">
        <t-button
          variant="outline"
          :class="{ 'is-selected': selectedMode === MODE_DOWNLOAD }"
          @click="selectedMode = MODE_DOWNLOAD"
        >
          P-Downloads
        </t-button>
        <t-button
          variant="outline"
          :class="{ 'is-selected': selectedMode === MODE_LTV }"
          @click="selectedMode = MODE_LTV"
        >
          P-LTV
        </t-button>
        <Select
          v-if="selectedGraph !== LINE_CHART"
          v-model="selectedDay"
          title=""
          :list="extractOptions(day)"
          :multiple="false"
          :show-tooltip="false"
          @update:model-value="(newValue: any) => (selectedDay = newValue)"
        />
      </div>
      <div class="flex gap-1 ">
        <t-button
          variant="text"
          :class="{ 'is-selected': selectedGraph === WORLD_MAP_CHART }"
          @click="selectedGraph = WORLD_MAP_CHART"
        >
          <icon name="earth" />
        </t-button>
        <t-button
          variant="text"
          :class="{ 'is-selected': selectedGraph === BAR_CHART }"
          @click="selectedGraph = BAR_CHART"
        >
          <icon name="chart-bar" />
        </t-button>
        <t-button
          variant="text"
          :class="{ 'is-selected': selectedGraph === LINE_CHART }"
          @click="selectedGraph = LINE_CHART"
        >
          <icon name="chart-line" />
        </t-button>
      </div>
    </div>
    <div>
      <div
        v-if="selectedGraph===WORLD_MAP_CHART"
        id="worldMap"
      >
        <!-- <worldMap /> -->
        <worldMap2 />
      </div>
      <div
        v-if="selectedGraph!==WORLD_MAP_CHART"
        class="module_one rounded min-h-[450px] relative"
      >
        <BasicChart
          v-if="selectedGraph===BAR_CHART && !isDataLoading"
          id="BAR_CHART"
          :chart-type="barGraphState.chartType"
          :detail-type="barGraphState.detailType"
          :data-mode="barGraphState.dataMode"
          :y-axis-label-format="axisLabelFormat"
          :tooltip-value-format="axisLabelFormat"
          data-value-filed="value"
          data-item-field="seriesName"
          data-group-item-field="day"
          :data="barGraphState.chartData"
          :series="[
            {
              name: selectedMode === MODE_DOWNLOAD ? 'Downloads' : 'LTV',
              label: {
                show: true,
                position: 'top',
                formatter: format,
              },
              data: barGraphState.chartData,
              type: 'bar',
            },
          ]"
          :reg-rules="[{ name: 'value', value: ['s1000', 'decimal'] }]"
        />
        <BasicChart
          v-if="selectedGraph===LINE_CHART && !isDataLoading"
          id="LINE_CHART"
          :chart-type="lineGraphState.chartType"
          :detail-type="lineGraphState.detailType"
          :data-mode="lineGraphState.dataMode"
          :y-axis-label-format="axisLabelFormat"
          :tooltip-value-format="axisLabelFormat"
          data-value-filed="value"
          data-item-field="seriesName"
          data-group-item-field="day"
          :data="lineGraphState.chartData"
          is-show-legend
          is-legend-bar-bottom
          :legend-props="{ top: 'bottom', left: 'center', }"
          :grid="{ bottom: '10%', containLabel: true, left: 20, right: 20 }"
          :reg-rules="[{ name: 'value', value: ['s1000', 'decimal'] }]"
        />
        <FullLoading v-if="isDataLoading" />
      </div>
    </div>
  </t-space>
</template>
<script setup lang="ts">
import { useIntelligencePredictionStore } from '@/store/intelligence/prediction/prediction.store';
import { ref, defineAsyncComponent, reactive, onMounted, watch } from 'vue';
import { storeToRefs } from 'pinia';
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  TooltipComponent,
  VisualMapComponent,
  GeoComponent,
} from 'echarts/components';
import { MapChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import { DataMode } from 'common/components/BasicChart/type.d';
import { Icon } from 'tdesign-icons-vue-next';
import Select from 'common/components/Select';
import FullLoading from 'common/components/FullLoading';
// import worldMap from './components/worldMap.vue';
import worldMap2 from './components/worldMap2.vue';
import { getBarChartData, getLineChartData } from './ltvCalculation';
import { ALLDAYS, MODE_DOWNLOAD, MODE_LTV, WORLD_MAP_CHART, BAR_CHART, LINE_CHART } from '../const/const';

// Const
const day = ALLDAYS.map((day: number) => `D${day}`);
const BasicChart = defineAsyncComponent(() => import('common/components/BasicChart'));
const barGraphState = reactive({
  chartType: 'bar',
  detailType: 'stack',
  chartData: [] as any,
  dataMode: 'x' as DataMode,
  xAxisName: '',
  yAxisName: '',
  isShowDataZoom: true,
  showLable: false,
});
const lineGraphState = reactive({
  chartType: 'line',
  detailType: 'stack',
  chartData: [] as any,
  dataMode: 'x' as DataMode,
  xAxisName: '',
  yAxisName: '',
  isShowDataZoom: true,
  showLable: false,
});
const { allData, isDataLoading, selectedMode, selectedDay } = storeToRefs(useIntelligencePredictionStore());
const { axisLabelFormat } = useIntelligencePredictionStore();

// Refs
const selectedGraph = ref(WORLD_MAP_CHART);

echarts.use([
  TitleComponent,
  TooltipComponent,
  VisualMapComponent,
  GeoComponent,
  MapChart,
  CanvasRenderer,
]);

// 初始化数据
// 设置初始选择模式为P-Downloads 及 初始选择天数为D7
// 获取图标数据
onMounted(async () => {
  selectedMode.value = MODE_DOWNLOAD;
  [selectedDay.value] = day;
  await initChart();
});

// 把数据转换成下拉菜单的内容格式
function extractOptions(sourceArray: any[]) {
  return sourceArray.map(item => ({
    text: item,
    label: item,
    value: item,
  }));
}

function format(params: any) {
  const formatterValue = params.value.toLocaleString();
  return selectedMode.value === MODE_DOWNLOAD ? formatterValue : `$${formatterValue}`;
}

async function initChart() {
  barGraphState.chartData = await getBarChartData();
  lineGraphState.chartData = await getLineChartData();
}

// 监控模式和天数的变化
// selectedMode = P-Downlaods / P-LTV
// selectedDay = D7， D30， D90， D180， D360
watch([selectedMode, selectedDay], async () => {
  await initChart();
});

// 监控所选择的国家变化
watch(() => allData.value.selectedKeys, async () => {
  await initChart();
});
</script>
<style lang="scss">
.is-selected,
.is-selected:hover {
  z-index: 2;
  border-color: #006eff;
  background: #fff;
  color: #006eff;
}
</style>
