<template>
  <div>
    <FullLoading v-if="isDataLoading" />
    <info v-else-if="!isDataLoading && isNoData" />
    <CommonView
      v-else
      :store="store"
      title="Intelligence / Prediction / Overview"
      :form-props="formProps"
      :tab-props="tabProps"
    >
      <template #views>
        <div>
          <div>
            <graph />
          </div>
          <graphComponents />
          <tableComponents />
        </div>
      </template>
    </CommonView>
  </div>
</template>
<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { useGlobalGameStore } from '@/store/global/game.store';
import { useIntelligencePredictionStore } from '@/store/intelligence/prediction/prediction.store';
import { ref, onMounted, reactive, defineAsyncComponent } from 'vue';
import { storeToRefs } from 'pinia';
import { useWatchGameChange } from 'common/compose/request/game';
import { cloneDeep } from 'lodash-es';

// ---------------------------- 参数 ------------------------------
import { Platform } from '@/store/intelligence/creative/config/selectOptions.json';
import { useGoto } from '@/router/goto';
import { OVERVIEW } from '../const/const';

// ---------------------------- Type ------------------------------
import { Ext, IFormDynamicItem } from 'common/components/FormContainer/type';
import { FormData } from '../modal/prediction';

// ---------------------------- 组件 ------------------------------
import CommonView from 'common/components/Layout/CommonView.vue';
import FullLoading from 'common/components/FullLoading';
import graph from './components/graph.vue';
import graphComponents from './graphComponents.vue';
import tableComponents from './components/table.vue';
import info from '../../components/info.vue';

// ---------------------------- 函数 ------------------------------
import { createChartItem, configureChart } from './ltvCalculation';

const store = useIntelligencePredictionStore();
const {
  clearData,
  getCompetitorByCode,
  getCompetitorConfigDetails,
  getCountryOptions,
  showLoading,
  hideLoading,
  setRouter,
} = store;
const {
  allData,
  countryOptions,
  tabProps,
  tabSelectId,
  isDataLoading,
  confirmResetCount,
} = storeToRefs(useIntelligencePredictionStore());

setRouter(useRouter());


const { gotoPredictionOverviewAddCompetitor } = useGoto();
const DefaultOptions = { Market: {}, platform: {} };

// Refs
const formData = ref(cloneDeep(DefaultOptions));
const isNoData = ref(true);

const componentMap = {
  select: defineAsyncComponent(() => import('common/components/NewCascader')),
};
// 下拉选择器的数据源，后续通过异步获取变更
const mappedPlatform = Platform.map((item: { text: any; value: any; }): Ext => ({
  key: item.value,
  label: item.text,
  value: item.value,
}));
const formList: Array<IFormDynamicItem> = [
  {
    name: componentMap.select, // Country Select,
    props: {
      options: countryOptions,
      levelList: [
        {
          label: 'Region',
          value: 'region',
        },
        {
          label: 'Country/Market',
          value: 'country',
        },
      ],
      title: 'Country/Market',
      isEmptyWhenSelectAll: true,
    },
    ext: {
      key: 'Market',
      label: 'Market',
      isAllowClose: false,
    },
  },
  {
    name: componentMap.select, // Platform Select,
    props: {
      options: mappedPlatform,
      levelList: [
        {
          label: 'Platform',
          value: 'platform',
        },
      ],
      title: 'Platform',
      isEmptyWhenSelectAll: true,
    },
    ext: {
      key: 'platform',
      label: 'Platform',
      isAllowClose: false,
    },
  },
];

const formProps = reactive({
  modelValue: formData,
  formList,
  onSubmit: onFormSubmit,
  onReset: onFormReset,
  'onUpdate:modelValue': (newValue: FormData) => {
    formData.value = newValue;
  },
});

// 初始化配置
onMounted(async () => {
  useWatchGameChange(async () => {
    showLoading();
    await initial();
    hideLoading();
  });
});

async function initial() {
  isNoData.value = true;
  isDataLoading.value = true;
  tabSelectId.value = OVERVIEW;
  await clearData();

  // 初始化选择的国家， 最多可以选择8个国家
  countryOptions.value.splice(0, countryOptions.value.length, ...getCountryOptions(allData.value.Market));

  // 空Array为选择全部
  formData.value = cloneDeep(DefaultOptions);

  // 调用API获取竞品相关的数据，然后分到对应的全局数据里
  const result = await getCompetitorDetails();
  if (result === false) {
    isNoData.value = true;
    isDataLoading.value = false;
    return false;
  }

  // 调用API获取360天竞品数据
  allData.value.downloadCode = await getCompetitorConfigDetails();

  // 调用计算逻辑来获取图表数据
  await configureChart().then((value) => {
    if (value === false) isNoData.value = true;
    else isNoData.value = false;
  });
  console.log(allData.value);
  isDataLoading.value = false;
}

async function onFormSubmit(formData: FormData) {
  // 处理 selectedPlatform
  const platformData = JSON.stringify(formData.platform);
  allData.value.selectedPlatform = platformData === JSON.stringify({}) ? 3 : Number(formData.platform.platform);

  // 处理 worldSelect
  const marketData = JSON.stringify(formData.Market);
  allData.value.worldSelect = marketData === JSON.stringify({})
    ? allData.value.Market.map(item => item[1]) : formData.Market.country;
  await createChartItem();
  confirmResetCount.value += 1;
  console.log('test test test', allData.value);
};

async function onFormReset() {
  formData.value = cloneDeep(DefaultOptions);
  allData.value.selectedPlatform = 3;
  allData.value.worldSelect = allData.value.Market.map(item => item[1]);
  await createChartItem();
  confirmResetCount.value += 1;
}

// 调用API获取竞品相关的数据，然后分到对应的全局数据里
async function getCompetitorDetails() {
  const { gameCode } = useGlobalGameStore();
  const result = await getCompetitorByCode(gameCode);

  if (!result || result?.length === 0) {
    return false;
  }
  isNoData.value = false;

  const data = result.find((item: { apply: number; }) => item.apply === 1);
  if (!data || data.groups?.length === 0) {
    gotoPredictionOverviewAddCompetitor();
    return false;
  }
  allData.value.Market = [];
  allData.value.group = [];
  if (data && allData.value.Market.length === 0) {
    allData.value.competitorCodes = data.groups[0].competitors.map((e: { competitor_code: any; }) => e.competitor_code);
    if (!allData.value.competitorCodes?.length) {
      gotoPredictionOverviewAddCompetitor();
      return false;
    }
    data.groups.forEach((item: { market: string; competitors: any[]; }) => {
      const marketData = JSON.parse(item.market);
      const markets: Array<any> = marketData[99];

      const similarityALl = {
        score: 0,
      };

      item.competitors.forEach((v: { similarity_score: number; }) => {
        const num = similarityALl.score + v.similarity_score;
        similarityALl.score = num;
      });
      allData.value.Market.push(...markets);
      allData.value.group.push([item.competitors, markets, similarityALl]);
    });
    allData.value.worldSelect = allData.value.Market.map(item => item[1]);
  }
}
</script>
<style lang="scss" scoped></style>
