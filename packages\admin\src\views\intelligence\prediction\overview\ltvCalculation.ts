import { useIntelligencePredictionStore } from '@/store/intelligence/prediction/prediction.store';
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { ProcessedDataType, DayData, PlatformData, LtvData, Ltv } from '../modal/prediction';
import { METRIC, COLORS, ALLDAYS, ALLPLATFORM, ALLDAYSANALYZE, OVERVIEW, ANALYZE } from '../const/const';
import { worldCode } from '@/store/intelligence/creative/config/selectOptions.json';

// Const
const {
  getLtvDetails,
  getAnalyzeLtvDetails,
  getAnalyzeLtvDetails2,
  getCountryFullName, extractNumber, sortByValue, showLoading, hideLoading } = useIntelligencePredictionStore();
const { allData, selectedMode, selectedDay, tabSelectId } = storeToRefs(useIntelligencePredictionStore());

// Refs
const sortJson = ref<any>({});

async function fetchLTV(metric?: string) {
  const { competitorCodes } = allData.value;
  let data: any;
  const params: Ltv = {};
  if (tabSelectId.value === OVERVIEW) {
    params.competitor = competitorCodes?.join(',');
    params.day = ALLDAYS.join(',');
    data = await getLtvDetails(params);
  } else {
    showLoading();
    params.day =  ALLDAYSANALYZE.join(',');
    params.platform = allData.value.selectedPlatform;
    params.metric = metric;
    params.country = allData.value.marketSelect?.map((item: any) => item[1])?.join(',');
    // ------ old --------
    const result = await Promise.all(competitorCodes
      .map(code => getAnalyzeLtvDetails({ ...params, competitor: code })));
    const res = {};
    result.forEach((rItem: any) => {
      Object.assign(res, rItem);
    });
    data = res;
    hideLoading();
    // data = await getAnalyzeLtvDetails(params);
  }
  // 获取 LTV 详情数据
  allData.value.arr = {};
  // 处理异常情况
  if (Object.keys(data).length === 0) {
    return false;
  };
  allData.value.Market.forEach((market) => {
    const country = market[1];
    const dayList = tabSelectId.value === OVERVIEW ? ALLDAYS : ALLDAYSANALYZE;

    /*
      allData.value.arr = {
        day1: {
          cn: {
            1: [],
            2: [],
            3: [],
          }
        }
      }
    */
    dayList.forEach((day) => {
      const dayKey = `day${day}`;
      // 初始化数据结构（如果不存在）
      if (!allData.value.arr[dayKey]) {
        allData.value.arr[dayKey] = Object.create(null) as DayData[string];
      }

      // 初始化每天每个国家的数据结构
      allData.value.arr[dayKey][country] = {
        1: [],
        2: [],
        3: [],
      } satisfies PlatformData;
    });

    /*
      allData.value.arr = {
        day1: {
          cn: {
            // platform
            1: [ competitorCode1, competitorCod2 ],
            2: [],
            3: [],
          }
        }
      }
    */
    competitorCodes?.forEach((competitorCode: string) => {
      // 如果数据中不存在该竞品码，则初始化
      if (!data[competitorCode]) {
        data[competitorCode] = {};
      }
      if (!data[competitorCode][country]) {
        // 如果竞品码在该国家下无数据，则初始化数据结构
        data[competitorCode][country] = [];
        (tabSelectId.value === OVERVIEW ? ALLDAYS : ALLDAYSANALYZE).forEach((day) => {
          ALLPLATFORM.forEach((platform) => {
            // 更新数据结构
            allData.value.arr[`day${day}`][country][platform].push(competitorCode);
            data[competitorCode][country].push({ platform, t: day, ltv: 0, download: 0 });
          });
        });
      } else {
        // 如果竞品码在该国家下有数据，则处理数据
        data[competitorCode][country].forEach((val: { ltv: number; t: number; platform: number}) => {
          if (val.ltv === 0) {
            // 更新数据结构
            allData.value.arr[`day${val.t}`][country][val.platform].push(competitorCode);
          }
        });
      }
    });
  });
  await calculateLTV(data);
};

export async function fetchAnalyzeChartData(metric?: string) {
  const params: Ltv = {};
  showLoading();
  params.day =  ALLDAYSANALYZE.join(',');
  params.platform = `${allData.value.selectedPlatform}`;
  params.metric = metric;
  params.country = allData.value.marketSelect?.map((item: any) => item[1])?.join(',');
  params.group = allData.value.group;
  params.downloadCode = allData.value.downloadCode;
  // ------ old --------
  const result = getAnalyzeLtvDetails2({ ...params });
  hideLoading();
  return result;
};

async function calculateLTV(data: { [x: string]: { [x: string]: any; }; }) {
  const jp: ProcessedDataType = {};
  const activeGroup = allData.value.group;
  activeGroup.forEach((itemByRegion, index) => {
    const competitorCfgList  = itemByRegion[0];
    const countries = itemByRegion[1].map((val: any[]) => val[1]); // 提取组中的国家列表
    const competitorAllScore = itemByRegion[2].score.toFixed(2);

    // 初始化 jp 结构
    jp[index] = {
      /*
      competitorCode1: {
        `${country}`: {
          1: {
            ${day}: {
              ltv: gameVal.commercial_score * gameVal.similarity_score / competitorAllScore * ltv,
              download: download, // 后端数据匹配到数据中download
            }
          },
          2: {},
          3: {},
        }
        `${country}A`: {
          1: {
            `${day}`: ltv * download
          },
          2: {},
          3: {},
        }
      }
      dp: {
        1: {
          `${country}`: download * gameVal.market_score,
          `game${country}`: {
            competitorCode: competitorCode1,
            market_score: gameVal.market_score,
          }
        },
        2: {},
        3: {},
      }
      */
      dp: {
        1: {},
        2: {},
        3: {},
      },
    };

    competitorCfgList.forEach((gameVal: {
      competitor_code: string;
      commercial_score: number;
      similarity_score: number;
      market_score: number;
    }) => {
      const competitorCode = gameVal.competitor_code;

      // 初始化 jp 结构
      jp[index][competitorCode] = {};
      countries.forEach((country: string) => {
        const countryData = data[competitorCode][country];
        // 如果不存在，则初始化对应的数据结构
        if (!jp[index][competitorCode][country]) {
          jp[index][competitorCode][country] = {};
          jp[index][competitorCode][country][1] = {};
          jp[index][competitorCode][country][2] = {};
          jp[index][competitorCode][country][3] = {};
        }
        const countryKey = `${country}A`;
        if (!jp[index][competitorCode][countryKey]) {
          jp[index][competitorCode][countryKey] = {};
          jp[index][competitorCode][countryKey][1] = {};
          jp[index][competitorCode][countryKey][2] = {};
          jp[index][competitorCode][countryKey][3] = {};
        }

        // 处理竞品码里国家的数据
        if (countryData) {
          countryData.forEach((competitor: { t: number; ltv: number; download: number; platform: number; }) => {
            const { platform, download, ltv, t } = competitor;

            if (tabSelectId.value === OVERVIEW) {
              const platformData = jp[index][competitorCode][country];
              const platformDataA = jp[index][competitorCode][countryKey];

              if (!platformData[platform]) {
                platformData[platform] = {};
                platformData[platform][competitor.t] = {};
              }

              // 计算LTV和下载量
              platformDataA[platform][competitor.t] = competitor.ltv * competitor.download;
              platformData[platform][competitor.t] = {
                ltv: ((gameVal.commercial_score * gameVal.similarity_score) / competitorAllScore)
                 * competitor.ltv,
                download: competitor.download,
              };

              // 更新特定条件下的数据
              if (competitor.download * gameVal.market_score > 0 && competitor.t === 360) {
                const dpPlatform = jp[index].dp[platform];
                const gameCountryKey = `game${country}`;

                if (dpPlatform[country]) {
                  if (competitor.download * gameVal.market_score > dpPlatform[country]) {
                    dpPlatform[country] = competitor.download * gameVal.market_score;
                    dpPlatform[gameCountryKey] = {
                      competitorCode: gameVal.competitor_code,
                      market_score: gameVal.market_score,
                    };
                  }
                } else {
                  dpPlatform[country] = competitor.download * gameVal.market_score;
                  dpPlatform[gameCountryKey] = {
                    competitorCode: gameVal.competitor_code,
                    market_score: gameVal.market_score,
                  };
                }
              }
            } else {
              jp[index][competitorCode][`${country}A`][platform][t] = ltv * download; // ?
              jp[index][competitorCode][country][platform][t] = {
                ltv:
                    ((gameVal.commercial_score * gameVal.similarity_score)
                    / competitorAllScore)
                    * ltv,
                download,
              };
              const num1 = jp[index].dp[platform][country] > 0 ? jp[index].dp[platform][country] : 0;
              if (download * gameVal.market_score > num1 && t === 360) {
                jp[index].dp[platform][country] = download * gameVal.market_score;
                jp[index].dp[platform][`game${country}`] = {
                  competitorCode,
                  market_score: gameVal.market_score,
                };
              }
            }
          });
        }
      });
    });
  });

  // 更新全局数据
  sortJson.value.jp = jp;
}

// 获取图表数据
// 通过 fetchLTV() 获取 LTV 数据
// 使用 LTV 数据创建图表项
// 返回生成的图表项
/**
 * @param game_code 业务game
 * @param type download | ltv
 * @param fetchAPI 是否请求过
 * @returns
 */
export async function configureChart(game_code?: string, type?: 'download' | 'ltv', fetchAPI?: boolean) {
  if (tabSelectId.value === OVERVIEW) {
    const result = await fetchLTV();
    if (result === false) {
      return false;
    }
    await createChartItem();
  }  else if (tabSelectId.value === ANALYZE && game_code && type) {
    if (fetchAPI) {
      const result = await fetchLTV(type);
      if (result === false) {
        return false;
      }
    }
    lineChart(game_code, type);
  }
}

// 创建图表配置项
// 根据数据计算图表所需的各种值
// 设置图表的样式和数据
// 返回生成的图表配置项
export async function createChartItem() {
  const { jp } = sortJson.value;
  const ltvData: LtvData = sortJson.value.jp;

  // 初始化变量
  const platform = allData.value.selectedPlatform;
  const downloadlist = ALLDAYS.map(() => 0);
  const ltvList: any[] = [];
  let allDownload = 0;

  if (
    Array.isArray(allData.value.Market)
    && allData.value.Market.length > 0
    && allData.value.Market[0] && allData.value.Market[0].length > 0
    && jp !== undefined
    && platform > 0
    && allData.value.arr !== undefined
  ) {
    allData.value.records = [];
    allData.value.selectedKeys = [];
    allData.value.table = [];
    allData.value.worldSelect.forEach((country: any) => {
      const tableData: { [x: string]: string; }[] = [];
      let maxDownload = 0;

      ltvData[country] = {};
      ALLDAYS.forEach((day) => {
        ltvData[country][day] = { ltv: 0, download: 0 };
      });
      allData.value.group.forEach((item, groupIndex) => {
        // 处理数据并计算各种值
        const gameCountry = jp[groupIndex].dp[platform][`game${country}`];
        if (gameCountry) {
          const data360 = jp[groupIndex][gameCountry.competitorCode][country][platform][360];
          const dataDownload = data360.download * gameCountry.market_score;
          ALLDAYS.forEach((day, i) => {
            downloadlist[i] += Number(dataDownload) * allData.value.downloadCode[day];
          });
        }
        // 将计算得到的值存入相应的数据结构中
        allData.value.competitorCodes?.forEach((competitorCode) => {
          if (jp[groupIndex][competitorCode][country]) {
            const dataVV = jp[groupIndex][competitorCode][country][platform];
            ALLDAYS.forEach((day) => {
              if (dataVV[day]) {
                ltvData[country][day].ltv += dataVV[day].ltv;
                ltvData[country][day].download += dataVV[day].download;
              }
            });
            item[0].forEach((sc: { competitor_code: string; market_score: number; }) => {
              if (sc.competitor_code === competitorCode) {
                const maxDayDownload = dataVV[360].download * sc.market_score;
                maxDownload = Math.max(maxDayDownload, maxDownload);
              }
            });
          }
        });
      });

      // 生成图表所需的数据
      ALLDAYS.forEach((day, dIndex) => {
        const barChartDownloadValue = parseFloat((maxDownload * allData.value.downloadCode[day]).toFixed(0));
        const barChartLtvValue = allData.value.arr[`day${day}`][country][platform].length < allData.value.group[0][0].length
          ? isNaN(parseFloat(Number(ltvData[country][day].ltv).toFixed(2)))
            ? 0 : parseFloat(Number(ltvData[country][day].ltv).toFixed(2))
          : '';
        const lineChartDownloadValue = parseFloat((maxDownload * allData.value.downloadCode[day]).toFixed(0));
        const lineChartLtvValue = allData.value.arr[`day${day}`][country][platform].length < allData.value.group[0][0].length
          ? isNaN(parseFloat(ltvData[country][day].ltv.toFixed(2)))
            ? 0 : parseFloat(ltvData[country][day].ltv.toFixed(2))
          : '';
        const worldMapDownloadValue = parseFloat((maxDownload * allData.value.downloadCode[day]).toFixed(0));
        const worldMapLtvValue = parseFloat(Number(ltvData[country][day].ltv).toFixed(2));

        if (allData.value.arr[`day${day}`][country][platform].length < allData.value.group[0][0].length) {
          if (isNaN(parseFloat(ltvList[dIndex]))) {
            ltvList[dIndex] = Number(ltvData[country][day].ltv) * maxDownload;
          } else {
            ltvList[dIndex] = parseFloat(ltvList[dIndex]) + Number(ltvData[country][day].ltv) * maxDownload;
          }
        } else if (ltvList[dIndex] === 0) {
          ltvList[dIndex] = '';
        }
        // 世界地图
        allData.value.WorldMap.download[day][country] = worldMapDownloadValue;
        allData.value.WorldMap.ltv[day][country] = worldMapLtvValue;

        // 柱状图
        allData.value.barChart.x.push(country);
        if (!allData.value.barChart.download[day][country]) {
          allData.value.barChart.download[day][country] = 0;
        }
        allData.value.barChart.download[day][country] = barChartDownloadValue;
        allData.value.barChart.ltv[day][country] = barChartLtvValue;

        // 折线图
        allData.value.lineChart.x.push(country);
        if (!allData.value.lineChart.download[day][country]) {
          allData.value.lineChart.download[day][country] = 0;
        }
        allData.value.lineChart.download[day][country] = lineChartDownloadValue;
        allData.value.lineChart.ltv[day][country] = lineChartLtvValue;

        // table
        tableData.push({
          [`P-D${day} Downloads`]: parseFloat((maxDownload * allData.value.downloadCode[day]).toFixed(0)).toLocaleString(),
          [`P-D${day} LTV`]: `$${parseFloat(ltvData[country][day].ltv.toFixed(2)).toLocaleString()}`,
        });
      });
      const data = tableData.reduce((acc, obj) => {
        Object.entries(obj).forEach(([key, value]) => {
          acc[key] = value;
        });
        return acc;
      }, {});
      allData.value.table.push({
        index: country,
        Market: getCountryFullName(country),
        ...data,
      });
      // 提取P-D7 Downloads的内容来排序
      allData.value.table.sort((a, b) => extractNumber(b[METRIC[2]]) - extractNumber(a[METRIC[2]]));
      allDownload += maxDownload;
      allData.value.selectedKeys.push(country);
    });
    allData.value.barA = downloadlist.map(day => day.toFixed(0));
    allData.value.lineA = ltvList.map(ltv => (ltv === '' ? '' : parseFloat((ltv / allDownload).toFixed(2))));
  }
}

export function lineChart(game_code: string, type: 'download' | 'ltv') {
  // 折线图
  // 柱状折现对比图
  // 绘制图表
  allData.value.records = [];
  const platform = allData.value.selectedPlatform;
  const daysArr: any = {
    /**
     * d: [0,...,0] 360个 // 记录1-360的
     * ltv: [0,0,...,0] 361个 // 因为从索引1开始-360结束
     * downloadC: [0,0,...,0] 361个 // 因为从索引1开始-360结束
     * cp_d: [0,...,0] 360个 sortJson.value.jp[i][code][country][platform][360].download * sc.market_score
     * cp_dcode: [0,...,0] 360个 就是downloadCode
     */
    d: [],
    ltv: [],
    downloadC: [],
  };
  ALLDAYSANALYZE.forEach((dd) => {
    daysArr.d.push(0);
    daysArr.ltv[dd] = 0;
    daysArr.downloadC[dd] = 0;
  });
  // linechart2
  allData.value.records = [];
  /**
    ltvData结构
    ltvData = {
      `${country}`: {
        `${day}`: {
          ltv: 0, 从sortJson.value.jp[i][code][country][platform][dd].ltv累加
          download: 0, 从sortJson.value.jp[i][code][country][platform][dd].download累加
        }
      }
    }
   */
  const ltvData: any = {};
  const ltvData2: any = {};
  if (allData.value.Market.length > 0 && allData.value.Market[0].length > 0 && sortJson.value.jp && platform > 0) {
    allData.value.marketSelect.forEach((element: any) => {
      // element[1]是country
      const country = element[1];
      ltvData[country] = {};
      daysArr.cp_d = [];
      daysArr.cp_dcode = [];
      ALLDAYSANALYZE.forEach((dd) => {
        daysArr.cp_d.push(0);
        daysArr.cp_dcode.push(0);
        ltvData[country][dd] = {
          ltv: 0,
          download: 0,
        };
      });
      allData.value.group.forEach((itemByRegion, i) => {
        const competitorCfgList  = itemByRegion[0];
        if (sortJson.value.jp[i].dp[platform][`game${country}`]) {
          const gameCode = sortJson.value.jp[i].dp[platform][`game${country}`].competitorCode;
          daysArr.d.forEach((v: any, di: any) => {
            daysArr.d[di]
                += sortJson.value.jp[i][gameCode][country][platform][360].download
                * sortJson.value.jp[i].dp[platform][`game${country}`].market_score
                * (allData.value.downloadCode[di + 1] ? allData.value.downloadCode[di + 1] : 0);
          });
        }
        const competitorCodes = allData.value.group[0][0].map((item: any) => item.competitor_code);
        competitorCodes.forEach((code: any) => {
          if (sortJson.value.jp[i][code][country]) {
            ALLDAYSANALYZE.forEach((dd) => {
              ltvData[country][dd].ltv += sortJson.value.jp[i][code][country][platform][dd].ltv;
              ltvData[country][dd].download += sortJson.value.jp[i][code][country][platform][dd].download;
            });

            competitorCfgList.forEach((sc: any) => {
              if (sc.competitor_code === code) {
                daysArr.cp_d.forEach((v: any, di: any) => {
                  if (sortJson.value.jp[i][code][country][platform][360].download * sc.market_score > v) {
                    daysArr.cp_d[di] = sortJson.value.jp[i][code][country][platform][360].download * sc.market_score;
                    daysArr.cp_dcode[di] = allData.value.downloadCode[di + 1];
                  }
                });
              }
            });
          }
        });
      });
      /**
        dataD = [ltvData[country][day].download.toFixed(0),...]; // 每个城市1-360的download
        dataLtv = [parseFloat(ltvData[country][day].ltv.toFixed(2)),]; // 每个城市1-360的ltv
       */
      const dataD: any = [];
      const dataLtv: any = [];
      ALLDAYSANALYZE.forEach((day) => {
        const dayKey = `day${day}`;
        const groupKey = country;
        const platformLength = allData.value.group[0][0].length;
        const currentLtv = parseFloat(ltvData[groupKey][day].ltv);

        if (allData.value.arr[dayKey][groupKey][platform].length < platformLength) {
          if (isNaN(parseFloat(daysArr.ltv[day]))) {
            daysArr.ltv[day] = currentLtv * daysArr.cp_d[day - 1];
          } else {
            daysArr.ltv[day] = parseFloat(daysArr.ltv[day]) + currentLtv * daysArr.cp_d[day - 1];
          }
        } else if (daysArr.ltv[day] === 0) {
          daysArr.ltv[day] = '';
        }


        daysArr.downloadC[day] += daysArr.cp_d[day - 1];
        dataD.push(parseFloat(ltvData[country][day].download.toFixed(0)));

        dataLtv.push(allData.value.arr[`day${day}`][country][platform].length < allData.value.group[0][0].length
          ? (isNaN(parseFloat(ltvData[country][day].ltv.toFixed(2)))
            ? 0
            : parseFloat(ltvData[country][day].ltv.toFixed(2)))
          : '');
      });
      const key = country as keyof typeof worldCode;
      const countryFullName = worldCode[key];
      allData.value.lineChart.x.push(countryFullName);
      /**
       * lineChart = {
            download: {
              `${countryFullName}`: {
                name: `${countryFullName}`,
                data: dataD,
              }
            },
            ltv: {
              `${countryFullName}`: {
                name: `${countryFullName}`,
                data: dataLtv
              }
            }
       * }
       */
      allData.value.lineChart.download[countryFullName as any] = {
        name: countryFullName as any,
        data: dataD,
      };
      allData.value.lineChart.ltv[countryFullName as any] = {
        name: countryFullName as any,
        data: dataLtv,
      };
      // table
      const tableJ: {[key: string]: string | number} = {
        Market: countryFullName,
      };
      ALLDAYSANALYZE.forEach((dd) => {
        tableJ[`D${dd}L`] = `$${ltvData[country][dd].ltv.toFixed(2)}`;
        tableJ[`D${dd}D`] = ltvData[country][dd].download.toFixed(0);
      });
      allData.value.records.push(tableJ);
    });
    ALLDAYSANALYZE.forEach((dd) => {
      daysArr.ltv[dd] = daysArr.ltv[dd] === '' ? 0 : parseFloat((daysArr.ltv[dd] / daysArr.downloadC[dd]).toFixed(2));
    });
    /**
      ltvData2 = {
        competiotrCode1: {
          `${day}`: {
            ltv:0, // sortJson.value.jp[i][element][`${market[1]}A`][platform][dd]
            download: 0, // 从sortJson.value.jp[i][element][market[1]][platform][v].download累加
          }
        }
      }
     */
    allData.value.competitorCodes.forEach((element: any) => {
      ltvData2[element] = {};
      ALLDAYSANALYZE.forEach((dd) => {
        ltvData2[element][dd] = {
          ltv: 0,
          download: 0,
        };
      });
      allData.value.Market.forEach((market) => {
        allData.value.group.forEach((item, i) => {
          if (sortJson.value.jp[i][element][market[1]]) {
            ALLDAYSANALYZE.forEach((v) => {
              ltvData2[element][v].download += sortJson.value.jp[i][element][market[1]][platform][v].download;
            });
          }
          if (sortJson.value.jp[i][element][`${market[1]}A`]) {
            ALLDAYSANALYZE.forEach((dd) => {
              const ltvValue = sortJson.value.jp[i][element][`${market[1]}A`][platform][dd];

              if (!allData.value.arr[`day${dd}`][market[1]][platform].includes(element)) {
                if (!isNaN(parseFloat(ltvData2[element][dd].ltv))) {
                  ltvData2[element][dd].ltv += ltvValue;
                } else {
                  ltvData2[element][dd].ltv = ltvValue;
                }
              } else if (ltvData2[element][dd].ltv === 0) {
                ltvData2[element][dd].ltv = '';
              }
            });
          }
        });
      });
      if (allData.value.dashboard.length === 0) {
        let name = '';
        allData.value.group[0][0].forEach((val: any) => {
          if (val.competitor_code === element && val.dashboard === 0) {
            name = val.competitor_name;
            return false;
          }
        });
        if (name) {
          allData.value.analyzeLineChart.x.push(name.length > 12 ? `${name.substring(0, 12)}...` : name);
          // allData['lineChart2']['x'].push(element);
          const tableJ: {[key: string]: string | number} = {
            Competitor: name,
          };

          const line2DataD: any = [];
          const line2DataLtv: any = [];
          ALLDAYSANALYZE.forEach((dd) => {
            line2DataD.push(parseFloat(ltvData2[element][dd].download.toFixed(0)));
            line2DataLtv.push(ltvData2[element][dd].ltv === ''
              ? 0
              : isNaN(parseFloat((ltvData2[element][dd].ltv / ltvData2[element][dd].download).toFixed(2)))
                ? 0
                : parseFloat((ltvData2[element][dd].ltv / ltvData2[element][dd].download).toFixed(2)));
            tableJ[`D${dd}D`] = parseFloat(ltvData2[element][dd].download.toFixed(0));
            tableJ[`D${dd}L`] =                ltvData2[element][dd].ltv === ''
              ? 0
              : isNaN(parseFloat((ltvData2[element][dd].ltv / ltvData2[element][dd].download).toFixed(2)))
                ? 0
                : parseFloat((ltvData2[element][dd].ltv / ltvData2[element][dd].download).toFixed(2));
          });
          allData.value.records.push(tableJ);
          allData.value.analyzeLineChart.download[element] = {
            name: name.length > 12 ? `${name.substring(0, 12)}...` : name,
            data: line2DataD,
          };
          allData.value.analyzeLineChart.ltv[element] = {
            name: name.length > 12 ? `${name.substring(0, 12)}...` : name,
            data: line2DataLtv,
          };
        }
      }
    });
    const P_TABLE: {[key: string]: string | number} = {
      Competitor: game_code,
    };
    allData.value.barA = [];
    allData.value.lineA = [];
    daysArr.d.forEach((v: any, di: number) => {
      allData.value.barA.push(v.toFixed(0));
      P_TABLE[`D${di + 1}D`] = parseFloat(v.toFixed(0));
    });
    daysArr.ltv.forEach((v: any, di: number) => {
      allData.value.lineA.push(!isNaN(v) ? 0 : v);
      P_TABLE[`D${di}L`] = !isNaN(v) ? 0 : v;
    });

    allData.value.records.unshift(P_TABLE);
  }

  if (type === 'download') {
    allData.value.analyzeLineChart.x.unshift(game_code.length > 12 ? `${game_code.substring(0, 12)}...` : game_code);
  } else if (type === 'ltv') {
    allData.value.analyzeLineChart.x.unshift(game_code.length > 12 ? `${game_code.substring(0, 12)}...` : game_code);
  }
  const seriesArr = [];
  if (allData.value.selectedKeys.length === 0) {
    // for (let item in allData['lineChart2'][worldMapType]) {
    //     seriesArr.push(allData['lineChart2'][worldMapType][item]);
    // }
    allData.value.selectedKeys.forEach((val, i) => {
      let code = '';
      if (i > 0) {
        allData.value.group[0][0].forEach((item: any) => {
          if (item.competitor_name === val) {
            code = item.competitor_code;
            return false;
          }
        });
        seriesArr.push(allData.value.analyzeLineChart[type][code]);
      }
    });
  } else {
    allData.value.selectedKeys.forEach((val) => {
      let code = '';
      allData.value.group[0][0].forEach((item: any) => {
        if (item.competitor_name === val) {
          code = item.competitor_code;
          return false;
        }
      });
      seriesArr.push(allData.value.analyzeLineChart[type][code]);
    });
  }

  const jsonL = {
    name: game_code,
    type: 'line',
    symbol: 'none',
    data: allData.value.lineA,
  };
  const jsonD = {
    name: game_code,
    type: 'line',
    symbol: 'none',
    data: allData.value.barA,
  };
  if (allData.value.Market.length !== 0 && !isNaN(platform)) {
    if (allData.value.selectedKeys.includes(game_code)) {
      if (type === 'download') {
        seriesArr.unshift(jsonD);
      } else if (type === 'ltv') {
        seriesArr.unshift(jsonL);
      }
    }
  }
  // 添加demo数据有待更改
  allData.value.analyzeLineChart.download[game_code] = {
    name: game_code,
    data: daysArr.d,
  };
  allData.value.analyzeLineChart.ltv[game_code] = {
    name: game_code,
    data: daysArr.ltv,
  };
  console.log(allData.value);
}

// 获取柱状图数据
// 根据所选模式和日期，获取相应的数据
// 整理数据为适合柱状图的格式
// 返回选择国家的数据
export async function getBarChartData() {
  // 获取所选模式和日期的值
  const modeValue: keyof typeof allData.value.barChart = selectedMode.value;
  const dayValue: number = selectedDay.value.substring(1);

  // 初始化结果数组和最大国家数量
  let result: { seriesName: string; value: number; itemStyle: {} }[] = [];

  allData.value.selectedKeys.forEach((country, countryIndex) => {
    if (country) {
      const value = allData.value.barChart[modeValue][dayValue][country];
      result.push({
        seriesName: getCountryFullName(country),
        value,
        itemStyle: {
          color: COLORS[countryIndex],
        },
      });
    }
  });
  result = sortByValue(result);
  return result;
}

// 获取折线图数据
// 根据所选模式和日期，获取相应的数据
// 整理数据为适合折线图的格式
// 返回选择国家的数据
export async function getLineChartData() {
  // 获取所选模式和日期的值
  const modeValue: keyof typeof allData.value.lineChart = selectedMode.value;

  // 初始化结果数组和最大国家数量
  let result: { day: string; seriesName: string; value: number }[] = [];

  allData.value.selectedKeys.forEach((country) => {
    if (country) {
      ALLDAYS.forEach((day) => {
        const value = allData.value.lineChart[modeValue][day][country];
        result.push({
          day: `D${day}`,
          seriesName: getCountryFullName(country),
          value,
        });
      });
    }
  });
  result = sortByValue(result);
  return result;
}
