import { MessagePlugin } from 'tdesign-vue-next';
import { APISTATUS } from '../const/const';

export const responseMessage = (code: number, type: 'delete') => {
  if (code === APISTATUS.ok && type === 'delete') {
    MessagePlugin.success('Remove Successfully');
  } else if (code === APISTATUS.ok) {
    MessagePlugin.success('Submit Successfully');
  } else if (code === APISTATUS.err) {
    MessagePlugin.error('Submit Unsuccessfully');
  } else if (code === APISTATUS.fail) {
    MessagePlugin.error('Submit Unsuccessfully');
  } else if (code === APISTATUS.noallow) {
    MessagePlugin.error('Permission Unauthorized');
  } else MessagePlugin.info('Unknown Code');
};

const retBtn = (key: string, list: Array<any>): Function => {
  const button = (textArr: string[] | string) => {
    if (textArr.length === 0 || textArr.length === list.length) {
      return 'All';
    };
    if (!Array.isArray(textArr)) return textArr;
    return textArr.length > 1 ? textArr.length : textArr.join(',');
  };
  return button;
};

export const getAnalyzeFilterList = ({ src, fieldObj }: { src: any[]; fieldObj: any }) => src.map((item) => {
  const { props = {}, ext: { key = '' } = {} } = item;
  let newProps = props;
  const list = (fieldObj as any)[key];
  switch (key) {
    case 'type':
      newProps = { ...props, options: list };
      break;
    case 'market':
      newProps = { ...props, options: list };
      break;
    case 'os':
      newProps = { ...props, list };
      newProps.button = retBtn(key, list);
      break;
  }
  return { ...item, props: newProps };
});

export const countryCheckAll = (list: Array<any>) => {
  const checkAll: Array<any> = [];
  list.forEach((item: any) => {
    if (item.children && item.children.length > 0) {
      item.children.forEach((child: any) => {
        checkAll.push(child.value);
      });
    }
  });
  return checkAll;
};
