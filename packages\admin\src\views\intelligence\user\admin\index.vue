<template>
  <CommonView
    :store="store" :hide-right="true" class="w-full h-full"
    :router-index="-1" :tab-props="UPLOAD_PROPS"
  >
    <template #views>
      <Card>
        <Form
          :data="formData" :rules="UPLOAD_RULES" @submit="onSubmit"
          @reset="onReset"
        >
          <Space direction="vertical">
            <Text size="small" type="subTitle" content="Step 1: Please select an Excel file to upload" />
            <FormItem label="File" name="file">
              <Upload
                v-model="formData.file" :max="1" :request-method="requestMethod"
                accept=".xls, .xlsx"
                @change="onFileChange"
                @remove="onReset"
              />
            </FormItem>
            <div>
              Please upload an Excel file with the suffix .xls or .xlsx. or
              <Link
                theme="primary" :underline="false"
                href="https://static.aix.intlgame.cn/intelligence/template/user-template.xlsx"
              >
                view sample templates
              </Link>
            </div>
            <FormItem name="removeStatus">
              <Switch v-model="formData.removeStatus" />
              <Space>
                <div class="ml-2">Discard Non-Standard Country Data</div>
                <Tooltip
                  content="Standard Country data represents Countries that exist in the
                 public.report_country_region table"
                >
                  <SvgIcon name="error" size="20px" />
                </Tooltip>
              </Space>
            </FormItem>
            <Text size="small" type="subTitle" content="Step 2: Please select the year of the data to upload" />
            <FormItem label="Date" name="date">
              <Space>
                <Select v-model="formData.date" class="w-28" :options="getDateList()" />
                <Button :disabled="!showReset || store.isLoading" theme="primary" type="submit">Submit</Button>
                <Button
                  v-if="showReset" theme="default" variant="base"
                  type="reset"
                >
                  Reset
                </Button>
              </Space>
            </FormItem>
            <Text size="small" type="subTitle" content="Step 3: View upload task status" />
            <FormItem name="viewStatus">
              <Button :disabled="!showReset" theme="primary" @click="() => { showDrawer = true }">
                View
                Status
              </Button>
            </FormItem>
            <Text size="small" type="subTitle" content="Explanation: " />
            <Text
              size="small" type="subTitle"
              content="1. Upload the data. If there is an error after parsing, it will be marked red.
              Please modify it and re-upload it before inserting it.
              For the data format, please refer to the template.
              The final data will be inserted into “public” .“report_user”;"
            />
            <Text
              size="small" type="subTitle"
              content="2.Delete data on a specified date, for example, delete data in 2021 and 2022
               DELETE FROM public.report_user WHERE date in (2021, 2022);"
            />
            <Text
              v-if="filterCountryKey.length > 0"
              theme="danger"
              :content="`Non-Standard Country Abbreviations: ${filterCountryKey}`"
            />
          </Space>
        </Form>
        <Drawer
          v-model:visible="showDrawer" :footer="false" size-draggable
          size="large"
        >
          <template #header>
            <Text content="Task List" type="subTitle" />
            <Text class="ml-3" content="The task sheet being updated" />
          </template>
          <Table
            v-model:displayColumns="USER_TASK_METRIC" :pagination="taskPagination"
            :horizontal-scroll-affixed-bottom="true" resizable row-key="sheet"
            :data="taskList"
            :columns="useUserAdminTable({ data: taskList, retry: onRetry }).cols" @page-change="onTaskPageChange"
          />
        </Drawer>
      </Card>
      <Card>
        <Tabs
          v-if="tabList.length" :model-value="tabSelectId" theme="card"
          @change="onChange"
        >
          <TabPanel
            v-for="item in tabList" :key="item.value" :label="item.value.toString()"
            :value="item.value"
          >
            <slot :name="item.value">
              <Table
                v-model:displayColumns="USER_METRIC" :pagination="pagination"
                :horizontal-scroll-affixed-bottom="true" resizable row-key="country"
                :data="filterTableRecords()"
                :columns="columns" @page-change="onPageChange"
              />
            </slot>
          </TabPanel>
        </Tabs>
        <div v-else-if="tabList.length === 0" class="flex justify-center items-center">
          Please upload Excel file
        </div>
      </Card>
    </template>
  </CommonView>
</template>

<script setup lang="ts">
import { Card, Form, FormItem, Upload, Link, Switch, Space, Tooltip, Select, Button, MessagePlugin, RequestMethodResponse, Tabs, TabPanel, PageInfo, Drawer, SubmitContext, Data } from 'tdesign-vue-next';
import { ref, reactive, computed } from 'vue';
import Text from 'common/components/Text';
import { ViewItem } from 'common/components/ViewTab';
import SvgIcon from 'common/components/SvgIcon';
import * as XLSX from 'xlsx';
import Table from 'common/components/table';
import { useUserAdminTable } from '@/views/intelligence/user/compose/tasklist-table';
import CommonView from 'common/components/Layout/CommonView.vue';
import { UPLOAD_USER_RULES, UPLOAD_PROPS, UPLOAD_RULES, USER_METRIC, USER_TABLE_COLUMNS, USER_TASK_STATUS, USER_TASK_METRIC } from '../const/const';
import { UploadFile as TdUploadFile } from 'tdesign-vue-next/es/upload/type';
import { storeToRefs } from 'pinia';
import { Column } from '../modal/user';
import { useIntelligenceUploadStore } from '@/store/intelligence/common/upload.store';


const store = useIntelligenceUploadStore();
const showReset = ref<boolean>(false);
const table = reactive<{ records: any[], column: any[] }>({ records: [], column: [] });
const { countryList } = storeToRefs(store);
const filterCountryKey = ref<string[]>([]);
const uploading = ref(false);
const showDrawer = ref(false);
const excelList = ref<{ sheet: string, data: unknown[] }[]>([]);
const formData = reactive({
  file: [] as File[],
  removeStatus: true,
  date: 2024,
  viewStatus: 0,
});
// Tab
const tabList = ref<ViewItem[]>([]);
const tabSelectId = ref<string | number>('');
const columns = ref<Column[]>(USER_METRIC.map((item) => {
  const column: Column = { colKey: item, title: item, sorter: true };
  column.width = 150;
  column.sorter = false;
  return column;
}));
const pagination = reactive({
  total: computed(() => filterTableRecords().length),
  pageSize: 10,
  current: 1,
});
// Task List
const taskList = ref<{ sheet: string, status: string | number }[]>([]);
const taskPagination = reactive({
  total: computed(() => taskList.value.length),
  pageSize: 10,
  current: 1,
});

const getDateList = () => {
  const currentYear: number = new Date().getFullYear();
  const startYear = 2022;

  const options: { value: number, text: number }[] = Array.from({ length: currentYear - startYear + 1 }, (_, index) => {
    const year = startYear + index;
    return {
      value: year,
      text: year,
    };
  });

  return options;
};

const requestMethod = async (file: TdUploadFile): Promise<RequestMethodResponse> => new Promise((resolve) => {
  uploading.value = true;
  const validate = UPLOAD_USER_RULES.find(type => file.type === type);
  if (!validate) {
    uploading.value = false;
    resolve({ status: 'fail', error: 'File format is not valid!', response: {} });
  } else {
    formData.file = [file.raw as File];
    uploading.value = false;
    resolve({ status: 'success', response: file });
  }
});

const readFile = (file: File): Promise<{ list: { sheet: string, data: unknown[] }[], file: File }> => {
  const fileReader: FileReader = new FileReader();

  // Use readAsArrayBuffer to read the file as binary data
  fileReader.readAsArrayBuffer(file);

  // Return a Promise to handle the asynchronous nature of file reading
  return new Promise((resolve, reject) => {
    fileReader.onload = (event) => {
      try {
        const { result } = event.target as { result: ArrayBuffer };  // Use const assertion
        // Use UInt8Array to handle binary data
        const uint8Array = new Uint8Array(result);

        // Now you can use uint8Array with XLSX
        const workbook = XLSX.read(uint8Array, { type: 'array', cellDates: true });

        const list: { sheet: string, data: unknown[] }[] = [];
        for (const sheet of Object.keys(workbook.Sheets)) {
          if (Object.prototype.hasOwnProperty.call(workbook.Sheets, sheet)) {
            const data = XLSX.utils.sheet_to_json(workbook.Sheets[sheet], { header: 1, defval: '' });
            list.push({ sheet, data });
          }
        }

        // Resolve with the list and the original file
        resolve({ list, file });
      } catch (err) {
        // Handle errors
        reject(err);
      }
    };

    fileReader.onerror = (event: ProgressEvent<FileReader>) => {
      // Handle read errors
      reject(event.target?.error);
    };
  });
};

async function onFileChange(file: TdUploadFile[]) {
  tabList.value = [];
  taskList.value = [];
  filterCountryKey.value = [];
  const res = await readFile(file[0].raw as File) as
    { list: { sheet: string, data: unknown[] }[], file: File };
  excelList.value = res.list;
  setRenderData(res.list);
  showReset.value = true;
};

const setRenderData = (list: { sheet: string, data: any[] }[]) => {
  const newList = list.map(({ data }) => {
    const newData: any[] = [];
    let countryKey = '';

    for (const [rowIndex, row] of data.entries()) {
      if (rowIndex > 0) {
        // 前三列 都不能为空，否则就是空行，没意义了
        if (row[0] || row[1] || row[2]) {
          const { country, variable, isValid } = getVariable(row[0]);
          if (!isValid) {
            MessagePlugin('error', 'Excel format error, please upload again');
            // If isValid is false, exit the loop
            break;
          }
          countryKey = countryKey || country;
          const { share } = getShare(row[2]);
          const value = getValue(row[1]);
          newData.push({
            country,
            share,
            value,
            variable,
            date: formData.date,
          });
        }
      }
    }

    return {
      key: countryKey,
      data: newData,
    };
  });
  const records: {
    key: string;
    data: any;
  }[] = [];

  if (formData.removeStatus) {
    // 如果开关为开代表以国家为优先级 不符合的筛掉
    const countryKey = countryList.value.map((one: { country_abbre: any }) => one.country_abbre);
    records.push(...newList.filter(e => countryKey.includes(e.key)));
    const key = newList.filter(e => !countryKey.includes(e.key)).map(one => one.key)
      .join(',');
    if (key) {
      filterCountryKey.value.push(key);
    }
  } else {
    records.push(...newList);
  }

  records.forEach((data, index) => {
    tabList.value.push({
      label: data.key,
      value: data.key,
      param: index,
    });
    taskList.value.push({
      sheet: data.key,
      status: USER_TASK_STATUS.WAITING,
    });
  });

  tabSelectId.value = tabList.value[0]?.value;
  table.records = records;
  table.column = USER_TABLE_COLUMNS;
};

const filterTableRecords = () => {
  if (table.records.length > 0) {
    return table.records.find(country => country.key === tabSelectId.value).data;
  };
  return [];
};

const onChange = (tabId: string | number) => {
  tabSelectId.value = tabId;
};

const onPageChange = (pageInfo: PageInfo) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
};

const onTaskPageChange = (pageInfo: PageInfo) => {
  taskPagination.current = pageInfo.current;
  taskPagination.pageSize = pageInfo.pageSize;
};

// Task List
function updateTask(name: string, ret: string | number) {
  taskList.value = taskList.value.map(({ sheet, status }) => {
    let newStatus: string | number = status;
    if (sheet === name) {
      newStatus = ret;
    }
    return {
      sheet,
      status: newStatus,
    };
  });
}

// 整理excel数据

// Modified getVariable function
const getVariable = (variable: string) => {
  const reg = /(?<=\[')(.+?)(?='\])/g;
  const isCountry = variable.match(reg);

  if (!isCountry) {
    return {
      isValid: false,
      country: '',
      variable: '',
    };
  }
  return {
    isValid: true,
    country: isCountry[0],
    variable: variable.replace(`['${isCountry[0]}']`, '').trim(),
  };
};

// Modified getShare function
const getShare = (share: string) => {
  const reg = /^(\d+)(\.\d+)?$/;

  if (reg.test(share)) {
    return {
      isValid: true,
      share: parseFloat(share),
    };
  }
  return {
    isValid: false,
    share,
  };
};


const getValue = (value: string) => {
  if (!value) return '';
  return value.trim();
};

const onSubmit = async (context: SubmitContext<Data>) => {
  if (context.validateResult) {
    for (const data of table.records) {
      updateTask(data.key, USER_TASK_STATUS.UPDATING);
      const res = await store.uploadUserDataExcel(data.data, data.key);
      if (res) {
        updateTask(data.key, res.ret);
      }
    }
  } else {
    console.log('Errors: ', context.validateResult);
    MessagePlugin.warning(context.firstError!);
  }
};

const onRetry = async (sheetName: string): Promise<void> => {
  const sheet = table.records.find(data => data.key === sheetName);
  if (sheet) {
    updateTask(sheet.key, USER_TASK_STATUS.UPDATING);
    const res = await store.uploadUserDataExcel(sheet.data, sheet.key);
    if (res) {
      updateTask(sheet.key, res.ret);
    }
  } else {
    MessagePlugin.warning('Sheet name does not exist!');
  }
};

const onReset = () => {
  formData.date = 2024;
  formData.file = [] as File[];
  formData.viewStatus = 0;
  formData.removeStatus = true;
  showReset.value = false;
  filterCountryKey.value = [];
  tabList.value = [];
  taskList.value = [];
  MessagePlugin.success('Reset Successfully');
};
</script>
<style lang="scss" scoped>
// tab栏 背景色

:deep(.t-tabs__nav--card) {
  // @apply bg-white-secondary;
  float: right;
  background: white;
}

// 激活时
:deep(.t-tabs__nav--card.t-tabs__nav-item.t-is-active) {
  @apply bg-white-secondary p-[16px] rounded-t-large;

  .label {
    @apply text-black-primary;
  }

  .label-icon {
    stroke: var(--aix-text-color-black-primary);
    @apply text-black-primary;
  }
}

//去掉边框
:deep(.t-tabs__nav--card.t-tabs__nav-item) {
  @apply border-none p-[16px] rounded-t-large bg-transparent;
}

//tab之间的间距
:deep(.t-tabs__nav--card.t-tabs__nav-item:not(:first-of-type)) {
  @apply ml-[8px];
}

:deep(.t-tabs__nav--card) {
  @apply rounded-t-large;
}

:deep(.t-tabs) {
  @apply rounded-extraLarge;
  overflow: hidden;
}
</style>
@/store/intelligence/common/upload.store
