import { ITableCols } from 'common/components/table/type';
import { Icon } from 'tdesign-icons-vue-next';
import { Space, Link, Tooltip } from 'tdesign-vue-next';
import { USER_TASK_STATUS } from '../const/const';
export function useUserAdminTable({
  data,
  retry,
}: {data: { sheet: string, status: string | number }[];retry: (sheetName: string) => Promise<void>}) {
  const tableData = data;
  const cols: ITableCols[] = [
    {
      colKey: 'sheet',
      title: 'sheet',
      width: 200,
    },
    {
      title: 'status',
      colKey: 'status',
      cell: (h: any, { row }: any) => (
            <Space>
              <div style={{ color: statusMessage(row.status).color }}>{statusMessage(row.status).msg}</div>
              {row.status === USER_TASK_STATUS.REPEAT
                ? <Tooltip content={'The constraint rules of the database are: UNIQUE ("date", "country", "platform", "category");please query in the database, for example:select * from public.report_market_channel where date=xxx and country=\'xxx\' and platform=\'xxx\' and category=xxx;'}>
              <Icon style={{ color: '#e54545' }} name='error-circle' />
            </Tooltip> : null}
              {row.status === USER_TASK_STATUS.ERROR
                ? <Link theme='primary' onClick={() => {
                  retry(row.sheet); return;
                }}>retry</Link> : null}
            </Space>
      ),
    },
  ];

  function statusMessage(status: string | number): { msg: string; color: string } {
    const data: Record<string | number, { msg: string; color: string }> = {
      [USER_TASK_STATUS.REPEAT]: { msg: 'Update failed: there is duplicate data, please check', color: '#e54545' },
      [USER_TASK_STATUS.UPDATING]: { msg: 'Updating...', color: '#006eff' },
      [USER_TASK_STATUS.SUCCESS]: { msg: 'Update succeeded', color: '#0ABF5B' },
      [USER_TASK_STATUS.WAITING]: { msg: 'Waiting', color: 'black' },
      [USER_TASK_STATUS.ERROR]: { msg: 'Update failed: the task may have timed out, please click when idle', color: '#ff7200' },
    };
    return data[status] ?? { msg: '', color: 'black' };
  }

  return {
    cols,
    tableData,
  };
}
