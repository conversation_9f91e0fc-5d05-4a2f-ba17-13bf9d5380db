import { defineAsyncComponent } from 'vue';

const components: any = {
  Select: defineAsyncComponent(() => import('common/components/Select')),
};

export const USER_FILTER_LABEL = {
  country: undefined,
  date: undefined,
};

export const USER_FILTER_CONDITION = [
  {
    name: components.Select,
    props: {
      list: [],
      title: 'Country/Market',
      button: (textArr: string[] | string) => {
        if (!Array.isArray(textArr)) return textArr;
      },
    },
    ext: {
      key: 'country',
      label: 'Country/Market',
      isAllowClose: false,
    },
  },
  {
    name: components.Select,
    props: {
      list: [],
      title: 'Year',
      button: (textArr: string[] | string) => {
        if (!Array.isArray(textArr)) return textArr;
      },
    },
    ext: {
      key: 'date',
      label: 'Year',
      isAllowClose: false,
    },
  },
];

export const getUserFilterList = ({ src, fieldObj }: { src: any[]; fieldObj: any }) => src.map((item) => {
  const { props = {}, ext: { key = '' } = {} } = item;
  let newProps = props;
  const list = (fieldObj as any)[key];
  switch (key) {
    case 'country':
      newProps = { ...props, list };
      break;
    case 'date':
      newProps = { ...props, list };
      break;
  }
  return { ...item, props: newProps };
});

export const USER_TAB_PROPS = {
  modelValue: 0,
  showNum: 1,
  list: [],
  shareParams: {},
  hideSaveBtn: true,
  hideShareBtn: true,
  hideShareView: true,
  customIconList: [],
};

export const USER_FILTER_DATE = {
  // -- 日期
  metricKeys: ['date'],
  group: ['date'],
  order: [{ order: 'DESC', by: 'date' }],
  pageSize: 500,
  pageNum: 0,

};

// API 固定数字
export const PIE_VARIABLE = ['Gender', 'Age Combined', 'Education levels', 'Household income levels', 'Work situation'];

export const PIE_METRIC_KEYS = ['date', 'variable', 'value', 'share',
  {
    name: 'country',
    as: 'country_abbre', // -- 游戏月活用户数|游戏付费用户数
  }];

export const USER_COUNTRY_PARAM = {
  gen: {
    metricKeys: [
      {
        name: 'country', // -- 国家缩写
        as: 'country_abbre',
      }],
    group: ['country'],
    order: ['country'],
    pageSize: 500,
    pageNum: 0,
  },
};

export const inStringCond = <T, E>(key: T, value: E | E[]) => ({
  name: key,
  in_list: Array.isArray(value) ? value : [value],
  type: 1,
});

export const UPLOAD_USER_RULES = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', '.csv'];

export const UPLOAD_PROPS = {
  modelValue: 0,
  showNum: 1,
  list: [],
  shareParams: {},
  hideSaveBtn: true,
  hideShareBtn: true,
  hideShareView: true,
  customIconList: [],
};

export const UPLOAD_RULES = {
  file: [{ required: true, message: 'Please upload one excel file.' }],
  removeStatus: [{ required: false }],
  date: [{ required: true, message: 'Please choose a year.' }],
  viewStatus: [{ required: false }],
};

export const USER_METRIC = ['country', 'share', 'value', 'variable', 'date'];

export const USER_TABLE_COLUMNS = [
  { key: 'country', header: 'country' },
  { key: 'share', header: 'share' },
  { key: 'value', header: 'value' },
  { key: 'variable', header: 'variable' },
  { key: 'date', header: 'date' },
];

export const USER_TASK_STATUS = {
  WAITING: 'wait',
  UPDATING: 'update',
  SUCCESS: 0,
  REPEAT: 60000,
  ERROR: 50000,
};

export const USER_TASK_METRIC = ['sheet', 'status'];
