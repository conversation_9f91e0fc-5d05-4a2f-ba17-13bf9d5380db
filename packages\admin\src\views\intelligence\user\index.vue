<template>
  <common-view
    :store="store" :hide-right="verifyAdmin ? false : true" :form-props="{
      modelValue: condition.cur,
      formList: filterList,
      'onUpdate:modelValue': formUpdateValue,
      onSubmit: formSubmit,
      onReset: formReset,
    }"
    :tab-props="USER_TAB_PROPS"
  >
    <template #subTitle>
      <div v-if="verifyAdmin" class="right flex justify-between items-center cursor-pointer" @click="showAdminUpload()">
        <div>
          <SvgIcon
            name="plus" size="12px" color="#4981f2"
            class="mr-[10px] cursor-pointer"
          />
        </div>
        <div class="text-brand font-medium">{{ 'Upload Data' }}</div>
      </div>
    </template>
    <template #views>
      <Row class="justify-center flex flex-wrap">
        <Col v-for="(param, index) in organizeDataByVariable(pieData)" :key="index" class="text-center w-1/2 p-4">
          <div :class="getColumnClass(index)">
            <Text type="title" :content="convertChartTitle(param[0].variable)" />
            <BasicChart
              v-if="param[0].name && !store.isLoading"
              :chart-type="graphState.chartType"
              :detail-type="graphState.detailType"
              :data-mode="graphState.dataMode"
              data-value-filed="share"
              data-item-field="name" tooltip-sort="desc"
              :tooltip-filter-zero="true"
              data-group-item-field="data"
              :data="param ?? []" is-show-legend :series="[
                {
                  type: 'pie',
                  label: {
                    normal: {
                      formatter: '{d}%',
                    }
                  },
                  data: param ?? [],
                  radius: [70, 100],
                  itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 1
                  },
                }
              ]"
            />
            <DataEmpty v-else-if="!store.isLoading && !param[0].name" class="emptyStyle" />
            <FullLoading v-if="store.isLoading" class="loadingStyle" />
          </div>
        </Col>
      </Row>
    </template>
  </common-view>
</template>

<script setup lang="ts">
import DataEmpty from 'common/components/NullAble/DataEmpty.vue';
import { reactive, computed, ref, nextTick, watch, defineAsyncComponent } from 'vue';
import { useRouter } from 'vue-router';
import { cloneDeep } from 'lodash-es';
import { storeToRefs } from 'pinia';
import SvgIcon from 'common/components/SvgIcon';
import Text from 'common/components/Text';
import { Row, Col, MessagePlugin } from 'tdesign-vue-next';
import FullLoading from 'common/components/FullLoading';
import { useIntelligenceUserStore } from '@/store/intelligence/user/user.store';
import CommonView from 'common/components/Layout/CommonView.vue';
import { PieDataModal, UserFormOptions, UserFormParams } from './modal/user';
import { USER_FILTER_CONDITION, USER_FILTER_LABEL, getUserFilterList, USER_TAB_PROPS, PIE_VARIABLE } from './const/const';
import { DataMode } from 'common/components/BasicChart';


const BasicChart = defineAsyncComponent(() => import('common/components/BasicChart'));
const store = useIntelligenceUserStore();
const router = useRouter();
const { pieData, dateList, countryList, verifyAdmin } = storeToRefs(store);
const defaultFilter = reactive<UserFormParams>({
  country: '',
  date: '',
});

// 过滤器
const formOptions = ref<UserFormOptions>({
  fieldObj: cloneDeep(USER_FILTER_LABEL),
  conditionList: cloneDeep(USER_FILTER_CONDITION),
});
const condition = reactive<{ cur: UserFormParams; default: UserFormParams }>({
  cur: cloneDeep(defaultFilter),
  default: cloneDeep(defaultFilter),
});
const filterList = computed(() => getUserFilterList({
  src: formOptions.value.conditionList,
  fieldObj: formOptions.value.fieldObj,
}));

// 饼图数据
const graphState = reactive({
  chartType: 'pie',
  detailType: 'ringTextOut',
  dataMode: 'x' as DataMode,
  xAxisName: '',
  yAxisName: '',
  isShowDataZoom: true,
  showLable: false,
});

function organizeDataByVariable(data: PieDataModal) {
  PIE_VARIABLE.forEach((variable) => {
    // Check if the variable exists in the data array
    const variableExists = data.some((entry: { variable: string; }) => entry.variable === variable);

    // If the variable doesn't exist, add a default object
    if (!variableExists) {
      data.push({
        variable,
        country_abbre: '',
        date: 0,
        share: 0,
        value: '',
        name: '',
      });
    }
  });

  const result: Record<string, PieDataModal> = {};

  for (const entry of data) {
    const { variable, value, share } = entry;

    if (!result[variable]) {
      result[variable] = [];
    }

    // Create a new object with modified properties
    const modifiedEntry = {
      ...entry,
      name: value as any,
      value: share as any,
    };

    result[variable].push(modifiedEntry);
  }
  // Convert the result object to an array of arrays
  const resultArray = Object.values(result);

  // create order
  const sortedData = resultArray.sort((a, b) => {
    const variableA = a[0].variable;
    const variableB = b[0].variable;

    const indexA = PIE_VARIABLE.indexOf(variableA);
    const indexB = PIE_VARIABLE.indexOf(variableB);

    return indexA - indexB;
  });
  return sortedData;
}

function showAdminUpload() {
  router.push({
    path: '/intelligence/user/admin',
  });
}

function formUpdateValue(value: typeof condition) {
  condition.cur = {
    ...condition.cur,
    ...value,
  };
}

function convertChartTitle(title: string): string {
  const titleRecord: Record<typeof title, string> = {
    'Age Combined': 'Age',
    'Education levels': 'Education',
    'Household income levels': 'Income',
    'Work situation': 'Work',
  };
  return titleRecord[title] ?? title;
}

async function formSubmit(formData?: any) {
  if (!formData.country || isNaN(formData.date) || formData.date === '') {
    return MessagePlugin.error('Please choose a date and country to submit');
  }
  const config: { in_list: string[], name: string, type: number }[] = [{
    name: 'country',
    in_list: [formData.country],
    type: 1,
  }, {
    name: 'variable',
    in_list: PIE_VARIABLE,
    type: 1,
  }, {
    name: 'date',
    in_list: [formData.date],
    type: 1,
  }];
  const result = await store.getPieChartData(config);
  if (result) {
    result.default.forEach((data: { share: number; }) => {
      // eslint-disable-next-line no-param-reassign
      data.share = (data.share * 100).toFixed(2) as any;
    });
    pieData.value = result.default;
  }
}

async function formReset() {
  formOptions.value.fieldObj = cloneDeep({ country: countryList.value, date: dateList.value }) as any;
  formOptions.value.conditionList = cloneDeep(USER_FILTER_CONDITION);
  const [date] = dateList.value;
  nextTick(() => {
    condition.cur.country = countryList.value[0].value;
    condition.cur.date = date.value;
  });
  await store.init();
}

function getColumnClass(index: number) {
  if (index % 2 === 0) return 'bg-white-primary rounded-large mr-4 py-4 h-full backgroundStyle';
  return 'bg-white-primary rounded-large ml-4 py-4 h-full backgroundStyle';
}

watch(
  () => countryList.value,
  () => {
    formOptions.value.fieldObj = cloneDeep({ country: countryList.value, date: dateList.value }) as any;
    condition.cur = { country: countryList.value[0].value, date: condition.cur.date };
  },
);

watch(
  () => dateList.value,
  () => {
    const [date] = dateList.value;
    formOptions.value.fieldObj = cloneDeep({ country: countryList.value, date: dateList.value }) as any;
    condition.cur = { date: date.value, country: condition.cur.country };
  },
);

</script>

<style lang='scss' scoped>
.emptyStyle {
  min-width: 631px;
  min-height: 450px;

  @media (max-width: 1300px) {
    // Apply these styles if the screen width is 600 pixels or smaller
    min-width: auto;
    min-height: auto;
  }
}

.loadingStyle {
  min-width: 631px;
  min-height: 450px;
  position: relative;

  @media (max-width: 1300px) {
    // Apply these styles if the screen width is 600 pixels or smaller
    min-width: auto;
    min-height: auto;
  }
}

.backgroundStyle {
  min-width: 631px;
  min-height: 550px;

  @media (max-width: 1300px) {
    // Apply these styles if the screen width is 600 pixels or smaller
    min-width: auto;
    min-height: auto;
  }
}
</style>
