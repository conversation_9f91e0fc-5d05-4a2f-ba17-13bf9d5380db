import { OptionsItem } from 'common/components/Cascader';

export type UserFormOptions = {
  fieldObj: {
    country?: OptionsItem,
    date?: OptionsItem,
  },
  conditionList: any[],
};

export type UserFormParams = {
  country?: string,
  date?: string,
};

export type PieDataModal = {
  country_abbre: string,
  date: number,
  share: number,
  value: string,
  variable: string,
  name: string,
}[];

export type Column = {
  colKey: string;
  title: string;
  width?: number | undefined;
  type?: string | undefined;
  sorter?: boolean | undefined;
  required?: boolean | undefined;
};
