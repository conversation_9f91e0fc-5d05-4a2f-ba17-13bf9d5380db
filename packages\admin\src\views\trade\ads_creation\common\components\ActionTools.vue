<template>
  <t-dialog
    v-model:visible="contentChangeVisible"
    attach="body"
    class="template-action-dialog"
    :close-on-overlay-click="false"
  >
    <template #body>
      <div>{{ message }}</div>
      <div class="flex justify-center mt-[30px]">
        <t-button
          class="mx-[10px] w-[90px]" theme="primary" :disabled="saveLoading"
          :loading="saveLoading" @click="onConfirm(true)"
        >
          {{ isBack ? 'Go Back' : 'Confirm' }}
        </t-button>
        <t-button
          v-if="showNo" class="mx-[10px] w-[90px]" variant="outline"
          @click="onConfirm(false)"
        >
          No
        </t-button>
        <t-button
          v-if="showCancel" class="mx-[10px] w-[90px]" variant="outline"
          @click="onCancel"
        >
          Cancel
        </t-button>
      </div>
    </template>
    <template #cancelBtn />
    <template #confirmBtn />
  </t-dialog>
</template>
<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { cloneDeep } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { useDialog } from 'common/compose/useDialog';
import { useCurrentData, useInfoChange } from '../template/compose/currentCompose';
import { isNodeTemp } from '../template/utils-common';
import { Level } from '../template/config';
import { useTreeListDataStore } from '@/store/trade/template.store';
import { EBusOn } from '../template/event';

const emit = defineEmits(['success', 'loading']);

const { visible: contentChangeVisible, show: showDialog, hide: hideDialog } = useDialog();
const { saveNodes, initTreeFromLevel } = useTreeListDataStore();

const Message = {
  save: 'Do you want to save drafts?',
  change: 'Your content has been changed, do you want to update it?',
  discard: 'Are you sure to discard drafts?',
  important: 'The change will initialize your ad, are you sure to update it?',
  published: 'Are you sure to update it?', // 已发布，点击save需要提示
  goBack: 'Your changes will be lost, are you sure to go back?',
};
const showNo = ref(true); // 是否展示"No"按钮
const showCancel = ref(true); // 是否展示"Cancel"按钮
const message = ref(Message.change);
const saveLoading = ref(false);
const important = ref(false); // 是否有关键字段
const isBack = ref(false); // 是否是点击回退按钮
const isDelDraft = ref(false); // 是否删除草稿

// 确认操作，调用保存接口
const onConfirm = async (saveChange = false) => {
  if (isBack.value || isDelDraft.value) { // 返回或者删除草稿操作，不做处理
    emit('success');
  } else {
    if (saveChange) {
      if (curCb.value) curCb.value();
      // 重要字段变更，需要判断当前节点是否是临时节点，是的话不需要保存
      if (important.value && importantInfo.isTemp) {
        initTreeFromLevel(importantInfo.level);
      } else {
        saveLoading.value = true;
        const saveRes = await saveNodes(important.value, importantInfo.level, true);
        saveLoading.value = false;
        // important场景，不需要emit success，防止触发template/index.vue绑定事件
        if (saveRes && !important.value) {
          emit('success');
        }
        if (importantInfo.final) {
          setTimeout(() => importantInfo.final());
        }
      }
    } else {
      // 返回操作，不做处理
      const { treeList, initTreeList, channelConfig } = storeToRefs(useTreeListDataStore()); // 需要使用响应式
      const { current, getTreeNode } = useTreeListDataStore();
      const initCampaignNode = getTreeNode(initTreeList.value, current.campaignNode.id, channelConfig.value);
      const initAdgroupNode = getTreeNode(initTreeList.value, current.adgroupNode.id, channelConfig.value);
      const initAdNode = getTreeNode(initTreeList.value, current.adNode.id, channelConfig.value);
      const campaignNode = getTreeNode(treeList.value, current.campaignNode.id, channelConfig.value);
      const adgroupNode = getTreeNode(treeList.value, current.adgroupNode.id, channelConfig.value);
      const adNode = getTreeNode(treeList.value, current.adNode.id, channelConfig.value);
      // 使用旧的值覆盖当前值
      campaignNode!.data = cloneDeep(initCampaignNode!.data);
      adgroupNode!.data = cloneDeep(initAdgroupNode!.data);
      adNode!.data = cloneDeep(initAdNode!.data);
      emit('success');
    }
  }
  curCb.value = null;
  hideDialog();
};

const onCancel = () => {
  hideDialog();
};

/**
 * 检查当前表单数据，无任何变更返回false，需要保存返回true
 * if ad is tmp: // ad是临时节点，说明整个层级都是临时节点
 *   弹框是否保存草稿：
 * else:
 *  检查每个层级是否有变化
 *  if change:
 *    弹框是否保存变更
 *  else:
 *    忽略 @return false
 */
const checkDataChange = (isReviewing = false, back = false) => {
  isDelDraft.value = false;
  isBack.value = back;
  showCancel.value = true;
  important.value = false;

  // reivew状态下不做校验
  if (isReviewing) {
    emit('success');
    return;
  }
  const { ad } = useCurrentData();
  const { channelConfig } = useTreeListDataStore(); // 需要使用响应式
  const { publishedChange, campaignSame, adgroupSame, adSame, campaignImportant, adgroupImportant } = useInfoChange();
  const same = campaignSame && adgroupSame && adSame;
  if (back) { // 优先判断点击返回场景，不需要处理临时节点问题
    showNo.value = false;
    message.value = Message.goBack;
    if (same) emit('success'); // 没有变化，直接返回成功
    else showDialog();
  } else {
    if (isNodeTemp(Level.AdLevel, ad, channelConfig)) { // 临时节点，直接保存
      onConfirm(true);
    } else if (same) {
      emit('success'); // 没有变化，直接返回成功
    } else {
      if (campaignImportant || adgroupImportant) {
        important.value = true;
        message.value = Message.important; // 关键字段变更，更换提示
        showDialog();
      } else {
        if (publishedChange) { // 已发布节点发生变化，需要弹框确认
          showNo.value = true;
          showCancel.value = false;
          message.value = Message.change;
          showDialog();
        } else { // 都是草稿的变化，直接保存
          onConfirm(true);
        }
      }
    }
  }
};

// 主动触发save
const save = async (needNoneTip = true) => {
  isBack.value = false;
  showCancel.value = true;
  important.value = false;
  const { publishedChange, campaignImportant, adgroupImportant } = useInfoChange();
  // 优先判断已发布的数据
  if (publishedChange) {
    showNo.value = false;
    message.value = Message.change; // 关键字段变更，更换提示
    showDialog();
  } else if (campaignImportant || adgroupImportant) {
    important.value = true;
    showNo.value = false;
    message.value = Message.important; // 关键字段变更，更换提示
    showDialog();
  } else {
    emit('loading');
    const result = await saveNodes(important.value, importantInfo.level, true, needNoneTip);
    emit('success', result);
  }
};

// 删除草稿
const discard = () => {
  isDelDraft.value = true;
  isBack.value = false;
  showNo.value = false;
  showCancel.value = true;
  message.value = Message.discard;
  showDialog();
};

// 重要字段变更
const curCb = ref(); // 当前回调函数
const importantInfo = reactive({
  isTemp: false,
  level: Level.AdLevel, // 需要重置的层级
  final: () => {}, // 请求完成后的回调
});
EBusOn((evt: unknown, data: any) => {
  if (evt === 'importantChange') {
    const { cb, isTemp = false, level, final } = data;
    curCb.value = cb;
    isDelDraft.value = false;
    isBack.value = false;
    importantInfo.isTemp = isTemp;
    importantInfo.level = level;
    importantInfo.final = final;
    important.value = true;
    showNo.value = false;
    message.value = Message.important;
    if (level === Level.AdgroupLevel) message.value = message.value.replace('ad', 'adgroup and ad');
    showDialog();
  }
});

defineExpose({
  checkDataChange,
  save,
  discard,
});
</script>
<style lang="scss">
.template-action-dialog {
  .t-dialog__footer {
    padding: 0;
  }
}
</style>
