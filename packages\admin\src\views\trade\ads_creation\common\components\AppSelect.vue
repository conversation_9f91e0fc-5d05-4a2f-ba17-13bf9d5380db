<template>
  <t-select v-bind="$attrs" :disabled="disabled" @change="onSelect">
    <t-option
      v-for="item in appOptions"
      :key="item.value" class="app-list-option" :label="item.label"
      :value="item.value" :disabled="item.disabled"
    >
      <div class="flex justify-between w-[100%]">
        <div>{{ item.label }}</div>
        <div>{{ item.platform === 'IOS' ? 'IOS' : 'Android' }}</div>
      </div>
    </t-option>
    <template #valueDisplay="{ value }">
      <div class="flex justify-between w-[100%]">
        <div>{{ getItem(value)?.label }}</div>
        <div>{{ getItem(value)?.platform === 'IOS' ? 'IOS' : 'Android' }}</div>
      </div>
    </template>
  </t-select>
  <t-button
    v-if="showCreate"
    theme="primary" variant="text" :disabled="$attrs.disabled"
    @click="addApp"
  >
    Add App
  </t-button>
  <CreateApp v-if="showCreate" ref="createApp" @confirm="onConfirm" />
</template>
<script setup lang="ts">
import { PropType, computed, ref } from 'vue';
import type { MEDIA } from '@@/index';
import CreateApp from '../../tiktok/components/CreatApp.vue';
import type { AppOptions, TreeNode } from '../../common/template/type';
import { useCurrentData } from '../../common/template/compose/currentCompose';
import { EBusEmit } from '../../common/template/event';
import { getAppInfo, TWAppInfo } from '../../twitter/utils';

const props = defineProps({
  options: {
    type: Array as PropType<AppOptions[]>,
    default: () => [],
  },
  disabled: { type: Boolean, default: false },
  media: {
    type: String as PropType<MEDIA>,
    default: '',
  },
  showCreate: { type: Boolean, default: false },
});

const appOptions = computed(() => {
  const { campaign } = useCurrentData();
  return props.options.map((item) => {
    let disabled = false;
    if (props.media === 'tiktok') {
      // 开启ios_14，需要禁用安卓选项
      const isIos = campaign.ios14_status === 'ON';
      disabled = item.platform === 'ANDROID' && isIos;
    }
    return { ...item, disabled };
  });
});

const emit = defineEmits(['update:modelValue']);

const getItem = (val: string) => props.options.find(item => item.value === val);

// 选择app_id事件
const onSelect = (val: string) => {
  const target = props.options.find(item => item.app_id === val) as AppOptions;
  const { campaignNode, campaign, adgroup } = useCurrentData();

  switch (props.media) {
    case 'tiktok':
      // 同步修改os字段，值有变化才触发
      if (adgroup.os !== target?.platform) {
        EBusEmit('updateNodeData', {
          key: 'os', level: 'adgroup', val: target?.platform,
        });
      }
      break;
    case 'google':
      // 情况之前选择的track_install_volume
      EBusEmit('updateNodeData', { key: 'track_install_volume', level: 'campaign', val: '' });
      EBusEmit('updateNodeData', { key: 'important_actions', level: 'campaign', val: [] });
      EBusEmit('updateNodeData', {
        key: 'app_info', level: 'campaign', val: {
          app_platform: target.platform === 'IOS' ? 2 : 1,
          app_id: target.app_id,
        },
      });
      break;
    case 'twitter': {
      const appInfo = getAppInfo(target);
      Object.keys(appInfo).forEach((key) => {
        const k = key as keyof TWAppInfo;
        EBusEmit('updateNodeData', { level: 'adgroup', key: k, val: appInfo[k] });
      });
      EBusEmit('updateNodeData', { level: 'adgroup', key: 'devices.version', val: '' });

      const res = {
        ios: false,
        android: false,
      };
      // 查看所有adgroup，有android和ios，platform要改为all
      campaignNode.children.forEach((adgroupNode: TreeNode) => {
        const {
          android_app_store_identifier: androidId,
          ios_app_store_identifier: iosId,
        } = adgroupNode.data;
        if (androidId) res.android = true;
        if (iosId) res.ios = true;
      });
      const names = campaign.name.split('-');
      if (res.android && res.ios) names[2] = 'all';
      else names[2] = target.platform === 'IOS' ? 'ios' : 'and';
      EBusEmit('updateNodeData', { level: 'campaign', key: 'name', val: names.join('-') });
      break;
    }
  }
  emit('update:modelValue', val);
};

const createApp = ref();
const addApp = () => {
  createApp.value.addApp();
};

// 创建app，重新获取数据，选中新创建的appid
const onConfirm = async (appId: string) => {
  emit('update:modelValue', appId);
  EBusEmit('updateOptions', {
    key: 'app_id',
  });
};

</script>
<style lang="scss" scoped>
:deep(.t-input__prefix) {
  width: 100%;
}
</style>
<style lang="scss">
.app-list-option {
  width: 100%;
  span {
    width: 100%;
  }
}
</style>
