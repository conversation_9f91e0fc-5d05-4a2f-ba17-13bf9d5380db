<template>
  <div>
    <t-form-item v-if="!isReviewing" label="Audience">
      <t-radio-group
        v-model="isNewAudience"
        :disabled="audienceTemplateList.length === 0 || isAdvantageAppAds"
        @change="() => switchAudienceType()"
      >
        <t-radio :value="true">
          New Audience
        </t-radio>
        <t-radio :value="false">
          Saved Audience
        </t-radio>
      </t-radio-group>
    </t-form-item>
    <t-form-item v-if="!isNewAudience && !isReviewing" label="">
      <t-select
        v-if="isShowSelectList"
        v-model="selectedAudienceId"
        placeholder="Search or select audience"
        clearable
        @change="selectAudienceChange"
      >
        <t-option
          v-for="item in audienceTemplateList"
          :key="item.template_id + item.template_name"
          :label="item.template_name"
          :value="item.template_id"
        >
          <div class="flex items-center">
            <span>{{ item.template_name }}</span>
            <span title="delete" @click.stop="() => deleteTemplate(item)">
              <Icon name="delete" class="ml-[10px] cursor-pointer" />
            </span>
          </div>
        </t-option>
      </t-select>
    </t-form-item>
    <t-form-item v-if="!isNewAudience && !isReviewing" label="Name">
      <span>
        {{ selectedAudience.template_name }}
      </span>
    </t-form-item>
    <t-form-item
      label="Locations"
      name="audience.location"
      style="clear: both;"
    >
      <span v-if="isReviewing">
        {{ getOptionsText(computedOptions.locations, datas.location) }}
      </span>
      <Country
        v-else
        v-model="datas.location"
        :options="computedOptions.locations || []"
        @change="locationsChange"
      />
    </t-form-item>
    <t-form-item
      name="language" style="clear: both;"
    >
      <template #label>
        <div class="flex items-center">
          <div style="white-space: pre;">Language(optional)</div>
          <Tooltip v-if="isAdvantageAppAds" :content="languageTip">
            <InfoCircleIcon class="min-w-[1em]" />
          </Tooltip>
        </div>
      </template>
      <span v-if="isReviewing">
        {{ getOptionsText(computedOptions.languageOptions, datas.language) || 'All' }}
      </span>
      <t-select
        v-else
        v-model="languageValue"
        clearable
        :multiple="!isAdvantageAppAds"
        filterable
        :min-collapsed-num="4"
        placeholder="Search or select a location"
        :options="computedOptions.languageOptions"
        class="max-w-[688px] inline-block"
      >
        <template #prefixIcon>
          <div class="flex justify-center pl-[8px]">
            <icon name="search" class="text-lg text-black-secondary" />
            <span
              class="inline-block pr-[7px] text-black-secondary w-[1px] h-[16px]"
              style="border-right: 1px solid var(--aix-border-color-black-disabled);"
            />
          </div>
        </template>
      </t-select>
    </t-form-item>
    <t-form-item
      label="Gender" name="gender(optional)" style="clear: both;"
    >
      <span v-if="isReviewing">
        {{ getOptionsText(computedOptions.genderOptions, [datas.gender]) }}
      </span>
      <t-radio-group
        v-else
        v-model="datas.gender"
        :options="computedOptions.genderOptions"
        :disabled="isAdvantageAppAds"
        @change="() => agesOrGenderChange()"
      />
    </t-form-item>
    <t-form-item label="Age(optional)" name="age">
      <span v-if="isReviewing">
        {{ datas.age_min }} - {{ datas.age_max }}
      </span>
      <div
        v-else
        class="flex"
      >
        <t-select
          v-model="datas.age_min"
          :options="AGE_LEFT_OPTIONS || []"
          class="max-w-[688px] mr-[8px]"
          :disabled="isAdvantageAppAds"
          @change="() => agesOrGenderChange()"
        />
        <t-select
          v-model="datas.age_max"
          :options="AGE_RIGHT_OPTIONS || []"
          class="max-w-[688px]"
          :disabled="isAdvantageAppAds"
          @change="() => agesOrGenderChange()"
        />
      </div>
    </t-form-item>
    <t-form-item label="Detailed targeting include people who match(optional)" class="three-lines">
      <span v-if="isReviewing">
        {{ getOptionsText(targetMatchingDetailOptions, targetMatchingDatas) }}
      </span>
      <t-select
        v-else
        v-model="targetMatchingDatas"
        multiple
        filterable
        clearable
        placeholder="input keyword"
        reserve-keyword
        :min-collapsed-num="4"
        :loading="targetingLoading"
        class="w-[400px]"
        :disabled="isAdvantageAppAds"
        :filter="filter"
        @change="targetMatchingDatasChange"
        @search="(values: string) => {getDetailedTargetingMatchSearch(values)}"
      >
        <t-option
          v-for="item in targetMatchingDetailOptions"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </t-select>
    </t-form-item>
    <t-form-item v-if="!isReviewing && !isAdvantageAppAds">
      <t-button
        v-if="isNewAudience"
        theme="primary"
        @click="isShowSaveAudienceDialog = true;"
      >
        Save Audience
      </t-button>
      <t-button
        v-else
        theme="primary"
        @click="isShowSaveAudienceDialog = true;"
      >
        Update Audience
      </t-button>
    </t-form-item>
    <t-dialog
      header="Save audience"
      :visible="isShowSaveAudienceDialog"
      :on-close="() => { isShowSaveAudienceDialog = false; }"
    >
      <template #body>
        <div class="h-[50px]">
          <div class="t-form__controls " :class="isDulplicationName ? 't-is-error' : ''">
            <div class="t-form__controls-content">
              <t-input
                v-if="isNewAudience"
                v-model="newSavedAudience.template_name"
                placeholder="name this audience"
                @change="isDulplicationOfNameFun"
              />
              <t-input
                v-else
                v-model="selectedAudience.template_name"
                placeholder="name this audience"
                @change="isDulplicationOfNameFun"
              />
            </div>
            <div v-if="isDulplicationName" class="t-input__extra">Name already exists！</div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="flex justify-end">
          <t-button
            theme="primary"
            :disabled="isDulplicationName || (isNewAudience && !newSavedAudience.template_name)
              || (!isNewAudience && !selectedAudience.template_name)"
            :loading="isLoading"
            @click="() => { isNewAudience ? saveAudience() : updateAudience() }"
          >
            Confirm
          </t-button>
        </div>
      </template>
    </t-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, defineComponent, toRefs, computed, watch, onMounted, nextTick, watchEffect } from 'vue';
import { cloneDeep, set, uniq } from 'lodash-es';
import { Icon, InfoCircleIcon } from 'tdesign-icons-vue-next';
import { useTreeListDataStore } from '@/store/trade/template.store';
import { useFBTreeListData } from '@/store/trade/facebook/facebook.store';
import Country from '../Locations.vue';
import Language from 'common/components/LanguageSelect';
import MultiFilterSelect from '../MultiFilterSelect.vue';
import { ageRightOptions, ageLeftOptions } from '../../../facebook/adgroup/const';
import { MessagePlugin, DialogPlugin, TdOptionProps, Tooltip } from 'tdesign-vue-next';
import { getOptionsText, getLocationsText, getOptionsValues, getLanguageCode } from '../../template/utils-common';
import { EBusEmit, EBusOn } from '../../template/event';
import { initAdgroupAudienceData } from 'common/service/td/facebook/utils';
import type { FlexibleSpec, FlexibleSpecObj } from '../../../../ads_creation/facebook/type';
import { useEnv } from 'common/compose/env';

const { isProduction } = useEnv();

const AGE_RIGHT_OPTIONS = computed(() => ageRightOptions(datas.value.age_min));
const AGE_LEFT_OPTIONS = computed(() => ageLeftOptions(datas.value.age_max));

type AudienceTemplate = {
  template_id: string,
  template_name: string,
  targeting: any,
};

defineComponent({
  Country,
  Icon,
  Language,
  MultiFilterSelect,
});

const treeStore = useTreeListDataStore();
const fbTreeStore = useFBTreeListData();

const isAdvantageAppAds = computed(() => {
  const { campaignNode } = treeStore.current;
  if (!campaignNode) return false;
  const { smart_promotion_type: smartPromotionType, objective } = campaignNode.data;
  return smartPromotionType === 'SMART_APP_PROMOTION' && ['APP_INSTALLS', 'OUTCOME_APP_PROMOTION'].includes(objective);
});

const defaultAudienceData = initAdgroupAudienceData();

const props = defineProps({
  isReviewing: {
    type: Boolean,
    default: false,
  },
  options: {
    type: Object,
    default: () => {},
  },
  modelValue: {
    type: Object,
    default: () => {},
  },
  customAudienceOptions: {
    type: Array,
    default: () => [{
      label: 'text', value: '111',
    }],
  },
});

const emit = defineEmits(['update:modelValue']);

EBusOn((evt: unknown, data: any) => {
  const { key, val } = data;
  if (evt === 'AudienceValue') {
    set(datas.value, key, val);
  }
});
const languageTip = `Leave this blank unless you have specific language requirements for your ad.
  It's not necessary to add a language if it's already the main language in your selected location. Adding a language may significantly reduce your reach.`;
const isNewAudience = ref(true);
const { options, isReviewing } = toRefs(props);
const isShowSelectList = ref(true);

const genderMap: { [key: string]: string} = {
  0: 'all',
  1: 'm',
  2: 'f',
};


// 直接值复制，不对外绑定。
const datas = ref(cloneDeep(props.modelValue));
const languageValue = ref<string[] | string>([]);

watchEffect(() => {
  if (isAdvantageAppAds.value) {
    languageValue.value = datas.value.language[0] || '';
  } else {
    languageValue.value = datas.value.language;
  }
});

watch(() => languageValue.value, () => {
  if (isAdvantageAppAds.value) {
    datas.value.language = languageValue.value ? [languageValue.value] : [];
  } else {
    datas.value.language = languageValue.value;
  }
});


const getDefaultTemplateName = () => {
  const { campaignNode } = treeStore.current;
  const rejion = campaignNode.data.campaign_name.split('-')[1] || 'gl';
  const { age_min: ageMin, age_max: ageMax, gender = '0' } = datas.value || {};
  return `${rejion}_${ageMin}-${ageMax}_${genderMap[gender]}`;
};
const newSavedAudience = ref<AudienceTemplate>({
  template_id: '',
  template_name: getDefaultTemplateName(),
  targeting: datas.value,
});
const agesOrGenderChange = () => {
  if (isNewAudience.value) {
    newSavedAudience.value.template_name = getDefaultTemplateName();
  } else {
    selectedAudience.value.template_name = getDefaultTemplateName();
  }

  // 触发修改adgroup name
  EBusEmit('ageOrGenderChangeEvt', {
    age: [datas.value.age_min, datas.value.age_max],
    gender: genderMap[datas.value.gender],
  });
};

const locationsChange = function () {
  const result = getLocationsText(computedOptions.value.locations, datas.value.location);
  let {
    locationTexts,
  } = result as any;
  let isGl = true;
  computedOptions.value.locations.some((item: TdOptionProps) => {
    if (!locationTexts.includes(item.value)) {
      isGl = false;
      return true;
    }
    return false;
  });
  if (isGl) {
    locationTexts = 'gl';
  }
  const { codeResult: languageTexts } = getOptionsValues(
    computedOptions.value.languageCountrys,
    (result as any).codeResult, computedOptions.value.languageOptions,
  );
  if (!isAdvantageAppAds.value) {
    datas.value.language = uniq(languageTexts.concat(datas.value.language));
  }
  EBusEmit('locationsEvt', {
    locationTexts: locationTexts.toLowerCase(),
    languageTexts,
  });
  nextTick(() => {
    agesOrGenderChange();
  });
};

const audienceTemplateList = ref<AudienceTemplate[]>([]);
const isShowSaveAudienceDialog = ref(false);
const isLoading = ref(false);
const isDulplicationName = ref(false);
const selectedAudienceId = ref('');
const selectedAudience = ref<AudienceTemplate>({
  template_id: '',
  template_name: getDefaultTemplateName(),
  targeting: {},
});
const targetMatchingDetailOptions = ref<FlexibleSpec[]>([]);
const targetingLoading = ref(false);
const targetMatchingDatas = ref<string[]>([]);
const targetMatchKeyword = ref<string>('');
let isTargetingChange = false;

const filter = (search: string, option: TdOptionProps) => {
  if (!search) {
    return true;
  }
  console.log('search:', search, ', option:', option);
  const label = option?.label?.toLowerCase() || '';
  return label.indexOf(search.toLowerCase()) !== -1 || targetMatchingDatas.value.includes(label);
};


const targetMatchingDatasChange = () => {
  isTargetingChange = true;
  Object.keys(datas.value.flexible_spec).forEach((key: string) => {
    datas.value.flexible_spec[key].length = 0;
  });
  targetMatchingDatas.value.forEach((item: string) => {
    const matchItem = targetMatchingDetailOptions.value
      .find((optionItem: FlexibleSpec) => optionItem.value === item);
    if (matchItem) {
      if (!datas.value.flexible_spec[matchItem.type]) {
        datas.value.flexible_spec[matchItem.type] = [];
      }
      const matchItem2 = datas.value.flexible_spec[matchItem.type]
        .find((optionItem2: FlexibleSpec) => optionItem2.name === item);
      if (!matchItem2) {
        datas.value.flexible_spec[matchItem.type].push(matchItem);
      }
    }
  });
};

watch(() => isAdvantageAppAds.value, () => {
  if (isAdvantageAppAds.value && !newSavedAudience.value) {
    isNewAudience.value = true;
    switchAudienceType();
  }
});
const getFlexibleSpec = (flexibleSpec: FlexibleSpecObj) => {
  const temp: string[] = [];
  const tempOptions: FlexibleSpec[] = [];
  if (flexibleSpec) {
    Object.keys(flexibleSpec).forEach((key: string) => {
      flexibleSpec[key].forEach((item: FlexibleSpec) => {
        temp.push(item.name);
        tempOptions.push({
          ...item, value: item.name, label: `${item.name}`,
        });
      });
    });
  }
  return { temp, tempOptions };
};
watch(() => props.modelValue.flexible_spec, (newVal, oldVal) => {
  if (isTargetingChange) {
    isTargetingChange = false;
    return;
  }
  const { temp, tempOptions } = getFlexibleSpec(props.modelValue.flexible_spec);
  targetMatchingDatas.value = temp;
  if (oldVal === undefined && temp.length > 0) {
    // 说明是初始化，
    targetMatchingDetailOptions.value = tempOptions;
  }
}, {
  deep: true,
  immediate: true,
});

const selectAudienceChange = () => {
  if (!selectedAudienceId.value) {
    return;
  }
  selectedAudience.value = audienceTemplateList.value
    .find(item => item.template_id === selectedAudienceId.value) || {
    template_id: '',
    template_name: getDefaultTemplateName(),
    targeting: {},
  };
  if (!isNewAudience.value) {
    datas.value = selectedAudience.value.targeting;
  }
  const { temp, tempOptions } = getFlexibleSpec(datas.value.flexible_spec);
  targetMatchingDatas.value = temp;
  targetMatchingDetailOptions.value = tempOptions;
};


const isDulplicationOfNameFun = () => {
  if (isNewAudience.value) {
    isDulplicationName.value = !!audienceTemplateList.value
      .find(item => item.template_name === newSavedAudience.value.template_name);
  } else {
    isDulplicationName.value = !!audienceTemplateList.value
      .find(item => (item.template_name === selectedAudience.value.template_name
        && item.template_id !== selectedAudienceId.value));
  }
};

const switchAudienceType = () => {
  if (isNewAudience.value) {
    datas.value = newSavedAudience.value.targeting;
    newSavedAudience.value.template_name = getDefaultTemplateName();
  } else {
    datas.value = selectedAudience.value?.targeting || cloneDeep(defaultAudienceData);
  }
  const { temp, tempOptions } = getFlexibleSpec(datas.value.flexible_spec);
  targetMatchingDatas.value = temp;
  targetMatchingDetailOptions.value = tempOptions;
};

watch(() => JSON.stringify(props.modelValue), () => {
  datas.value = props.modelValue;
});

const asyncData = () => {
  const languageIds = datas.value.language;
  const languageCodes = getLanguageCode(computedOptions.value.languageOptions, languageIds);
  EBusEmit('languageEvt', { languageCodes });
  emit('update:modelValue', {
    ...datas.value, languageCodes,
  });
};

watch(() => datas.value, () => {
  asyncData();
  isDulplicationOfNameFun();
}, {
  deep: true,
});

const computedOptions = computed(() => options.value || {});

// 同步更新languageCodes
watch(() => computedOptions.value.languageOptions, () => {
  asyncData();
});

const updateAudience = async () => {
  if (isDulplicationName.value) {
    return false;
  }
  isLoading.value = true;
  selectedAudience.value.targeting.audience_name = selectedAudience.value.template_name;
  const result = await treeStore.addOrUpdateTemplate({
    template_id: selectedAudience.value.template_id,
    template_name: selectedAudience.value.template_name,
    template_content: JSON.stringify(datas.value),
  });
  const { template: { template_id: templateId } } = result as any;
  selectedAudienceId.value = templateId;
  selectedAudience.value.template_id = templateId;
  await getAudienceTemplateList();
  // 关闭命名弹窗
  isShowSaveAudienceDialog.value = false;
  isLoading.value = false;
  MessagePlugin.success('Update Audience success!');
};
const saveAudience = async () => {
  if (isDulplicationName.value) {
    return false;
  }
  if (isProduction) {
    isNewAudience.value = false;
    newSavedAudience.value = {
      template_id: '',
      template_name: '',
      targeting: cloneDeep(defaultAudienceData),
    };
    isShowSaveAudienceDialog.value = false;
    MessagePlugin.success('save Audience success!');
  } else {
    isLoading.value = true;
    newSavedAudience.value.targeting.audience_name = newSavedAudience.value.template_name;
    const result = await treeStore.addOrUpdateTemplate({
      template_id: newSavedAudience.value.template_id,
      template_name: newSavedAudience.value.template_name,
      template_content: JSON.stringify(datas.value),
    });
    const { template } = result as any;
    // 保存后切换到已保存的audience列表， 并重置new audiece页面为空
    isNewAudience.value = false;
    // datas.value = newSavedAudience.value.targeting;

    await getAudienceTemplateList();
    selectedAudienceId.value = template.template_id;
    // 获取到当前选中的对象值
    selectAudienceChange();
    newSavedAudience.value = {
      template_id: '',
      template_name: '',
      targeting: cloneDeep(defaultAudienceData),
    };
    MessagePlugin.success('save Audience success!');
    // 关闭命名弹窗
    isShowSaveAudienceDialog.value = false;
    isLoading.value = false;
  }
};

const getAudienceTemplateList = async () => {
  isShowSelectList.value = false;
  const resultList = await treeStore.getTemplateList();
  let { templates = [] } = resultList as any;
  templates = templates.map((item: { template_content: string}) => ({
    ...item,
    targeting: JSON.parse(item.template_content),
  }));
  audienceTemplateList.value = templates;
  // 如果列表有值，并且当前选中是一个空值，则默认选择第一个
  if (audienceTemplateList.value.length > 0) {
    if (selectedAudienceId.value === '') {
      selectedAudienceId.value = templates[0].template_id;
      [selectedAudience.value] = templates;
    }
  }
  isShowSelectList.value = true;
  isDulplicationOfNameFun();
};

const deleteTemplate = async (item: AudienceTemplate) => {
  const confirm = DialogPlugin.confirm({
    theme: 'danger',
    header: 'Delete audience',
    body: 'Please confirm to delete this audience?',
    cancelBtn: 'Cancel',
    confirmBtn: 'Confirm',
    zIndex: 8000,
    onConfirm: () => {
      confirmCallback();
      confirm.hide();
    },
  });
  const confirmCallback = async () => {
    const isCurrentTemplateDelete = item.template_id === selectedAudienceId.value;
    await treeStore.deleteTemplate([item.template_id]);
    if (isCurrentTemplateDelete) {
      selectedAudienceId.value = '';
      selectedAudience.value.template_id = '';
    }
    await getAudienceTemplateList();
    selectAudienceChange();
    if (audienceTemplateList.value.length === 0) {
      isNewAudience.value = true;
      switchAudienceType();
    }
  };
};

const getDetailedTargetingMatchSearch = async (searchWord: string) => {
  if (!searchWord) {
    return;
  }
  targetMatchKeyword.value = searchWord;
  targetingLoading.value = true;
  const barOptions: FlexibleSpec[] = [];
  const listOptions = await fbTreeStore.getDetailedTargetingMatch(searchWord);
  if (targetMatchingDatas.value.length) {
    targetMatchingDatas.value.forEach((item: string) => {
      const findItem = targetMatchingDetailOptions.value.find(itemOption => itemOption.label === item);
      if (findItem) {
        const innerItemIndex = listOptions.findIndex((listItem: FlexibleSpec) => listItem.id === findItem.id);
        if (innerItemIndex !== -1) {
          listOptions.splice(innerItemIndex, 1);
        }
        barOptions.push(findItem);
      }
    });
  };

  targetMatchingDetailOptions.value = listOptions.concat(barOptions);
  targetingLoading.value = false;
};

onMounted(async () => {
  // 获取到模板列表
  getAudienceTemplateList();
});

</script>
<style lang="scss" scoped>

</style>
