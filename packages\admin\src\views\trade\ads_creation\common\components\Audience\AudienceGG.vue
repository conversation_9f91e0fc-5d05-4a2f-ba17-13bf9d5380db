<template>
  <div id="targeting-block">
    <t-form-item v-if="!isReviewing" label="Audience">
      <t-radio-group
        v-model="isNewAudience"
        :disabled="audienceTemplateList.length === 0"
        @change="() => switchAudienceType()"
      >
        <t-radio :value="false">
          Saved Audience
        </t-radio>
        <t-radio :value="true">
          New Audience
        </t-radio>
      </t-radio-group>
    </t-form-item>
    <t-form-item v-if="!isNewAudience && !isReviewing" label="Audience signal(optional)" class="max-w-[688px]">
      <t-select
        v-if="isShowSelectList"
        v-model="selectedAudienceId"
        placeholder="Search or select audience"
        clearable
        @change="selectAudienceChange"
      >
        <t-option
          v-for="item in audienceTemplateList"
          :key="item.template_id + item.template_name"
          :label="item.template_name"
          :value="item.template_id"
        >
          <div class="flex items-center">
            <span>{{ item.template_name }}</span>
            <!-- <span title="delete" @click.stop="() => deleteTemplate(item)">
              <Icon name="delete" class="ml-[10px] cursor-pointer" />
            </span> -->
          </div>
        </t-option>
      </t-select>
    </t-form-item>
    <div v-if="!(!selectedAudienceId && !isNewAudience)">
      <t-form-item label="Audience name">
        <span v-if="props.isReviewing">
          {{ isNewAudience ? newSavedAudience.template_name :
            (selectedAudienceId ? selectedAudience.template_name : '') }}
        </span>
        <div v-else class="h-[50px] flex-1">
          <div class="t-form__controls " :class="isShowNameError ? 't-is-error' : ''">
            <div class="t-form__controls-content max-w-[688px]">
              <t-input
                v-if="isNewAudience"
                v-model="newSavedAudience.template_name"
                placeholder="name this audience"
                @change="isDulplicationOfNameFun"
              />
              <t-input
                v-else
                v-model="selectedAudience.template_name"
                :disabled="!isNewAudience"
                placeholder="name this audience"
                @change="isDulplicationOfNameFun"
              />
            </div>
            <div v-if="isShowNameError" class="t-input__extra">{{ nameErrorTip() }}</div>
          </div>
        </div>
      </t-form-item>

      <t-form-item
        label="Custom segments(optional)" name="segments" style="clear: both;"
      >
        <span v-if="isReviewing">
          {{ isReviewing || selectedAudienceId ?
            getOptionsText(segmentsListOptions, datas.audienceSegments.customAudience) : '' }}
        </span>
        <t-select
          v-else
          v-model="datas.audienceSegments.customAudience"
          clearable
          multiple
          filterable
          :disabled="!isNewAudience"
          :min-collapsed-num="4"
          placeholder="Add or create custom segments"
          class="max-w-[688px] inline-block multi-filter-select mr-[8px]"
        >
          <template #prefixIcon>
            <div class="flex justify-center pl-[8px]">
              <icon name="search" class="text-lg text-black-secondary" />
              <span
                class="inline-block pr-[7px] text-black-secondary w-[1px] h-[16px]"
                style="border-right: 1px solid var(--aix-border-color-black-disabled);"
              />
            </div>
          </template>
          <t-option
            v-for="item in segmentsListOptions" :key="item.value"
            :value="item.value"
            :label="item.label"
          >
            <div class="flex items-center justify-between options-hover">
              <span>{{ item.label }}</span>
              <span @click.stop.prevent="() => { editSegment(item) }">
                <icon name="edit-1" class="text-lg text-black-secondary" />
              </span>
            </div>
          </t-option>
        </t-select>
        <t-link
          v-if="!isReviewing && isNewAudience"
          class="ml-[8px]" theme="primary" @click="() => { segmentDrawerVisible = true; isEditSegments = false; }"
        >
          New segment
        </t-link>
      </t-form-item>
      <t-form-item
        label="Your data(optional)" name="yuarData" style="clear: both;"
      >
        <span v-if="isReviewing">
          {{ isReviewing || selectedAudienceId ?
            getOptionsText(yourDataListOptions, datas.audienceSegments.userList) : '' }}
        </span>
        <MultiFilterSelect
          v-else
          v-model="datas.audienceSegments.userList"
          :options="yourDataListOptions"
          :disabled="!isNewAudience"
        />
      </t-form-item>
      <t-form-item
        label="Interests & detailed demographics(optional)"
        name="interestsAndDemographics"
        class="interests-demographics"
        style="clear: both;"
      >
        <span v-if="isReviewing">
          {{ isReviewing || selectedAudienceId ?
            getOptionsText(userInterestListOptions, datas.audienceSegments.userInterest) : '' }}
        </span>
        <MultiFilterSelect
          v-else
          v-model="datas.audienceSegments.userInterest"
          :options="userInterestListOptions"
          :disabled="!isNewAudience"
        />
      </t-form-item>
      <Collpse
        :header-title="{content: 'Demographics', style: 'width: 118px' }"
      >
        <t-form-item
          label="Gender"
          name=""
        >
          <div
            class="t-form__controls"
            :class="datas.gender.genders.length === 0 && !datas.gender.include_undetermined ? 't-is-error' : ''"
          >
            <div class="t-form__controls-content max-w-[688px]">
              <span v-if="isReviewing">
                <span class="mr-[8px]">
                  {{ isReviewing || selectedAudienceId ?
                    getOptionsText(GENDEROPTIONS, datas.gender.genders) : '' }}
                </span>
                <span v-if="(isReviewing || selectedAudienceId) && datas.gender.include_undetermined">Unknown</span>
              </span>
              <span v-else>
                <t-checkbox-group
                  v-model="datas.gender.genders"
                  :options="GENDEROPTIONS"
                  :disabled="!isNewAudience"
                />
                <t-checkbox
                  v-model="datas.gender.include_undetermined"
                  :disabled="!isNewAudience"
                  label="Unknown" class="ml-[8px]"
                />
              </span>
            </div>
            <div
              v-if="datas.gender.genders.length === 0 && !datas.gender.include_undetermined && isValidate"
              class="t-input__extra"
            >
              Gender is required！
            </div>
          </div>
        </t-form-item>
        <t-form-item
          label="Age(optional)"
        >
          <div v-if="isReviewing">
            <span>
              {{ getAgeText(MINAGEOPTIONS, datas.age.age_ranges[0].min_age) }}
            </span>
            <span v-if="getAgeText(MINAGEOPTIONS, datas.age.age_ranges[0].min_age)" class="ml-[8px]">to</span>
            <span class="ml-[8px]">
              {{ getAgeText(MAXAGEOPTIONS, datas.age.age_ranges[0].max_age) }}
            </span>
            <span
              v-if="(isReviewing || selectedAudienceId) && datas.age.include_undetermined"
              class="ml-[8px]"
            >
              Unknown
            </span>
          </div>
          <div
            v-else
            class="flex items-center"
          >
            <t-select
              v-model="datas.age.age_ranges[0].min_age"
              :disabled="!isNewAudience"
              :options="getMinOptions(MINAGEOPTIONS)"
            />
            <span class="ml-[8px]">to</span>
            <t-select
              v-model="datas.age.age_ranges[0].max_age"
              :options="getMaxOptions(MAXAGEOPTIONS)"
              :disabled="!isNewAudience"
              class="ml-[8px]"
            />
            <t-checkbox
              v-model="datas.age.include_undetermined"
              :disabled="!isNewAudience"
              label="Unknown"
              class="ml-[8px]"
            />
          </div>
        </t-form-item>
      </Collpse>
      <t-form-item v-if="!isReviewing && isNewAudience" class="mt-[20px]">
        <t-button
          theme="primary"
          @click="saveAudience"
        >
          Save Audience
        </t-button>
      </t-form-item>
    </div>
    <!-- <t-dialog
      header="Save audience"
      :visible="isShowSaveAudienceDialog"
      :on-close="() => { isShowSaveAudienceDialog = false; }"
    >
      <template #body>
        <div class="h-[50px]">
          <div class="t-form__controls " :class="isDulplicationName ? 't-is-error' : ''">
            <div class="t-form__controls-content">
              <t-input
                v-if="isNewAudience"
                v-model="newSavedAudience.template_name"
                placeholder="name this audience"
                @change="isDulplicationOfNameFun"
              />
              <t-input
                v-else
                v-model="selectedAudience.template_name"
                placeholder="name this audience"
                @change="isDulplicationOfNameFun"
              />
            </div>
            <div v-if="isDulplicationName" class="t-input__extra">Name already exists！</div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="flex justify-end">
          <t-button
            theme="primary"
            :disabled="isShowNameError"
            :loading="isLoading"
            @click="() => { isNewAudience ? saveAudience() : updateAudience() }"
          >
            Confirm
          </t-button>
        </div>
      </template>
    </t-dialog> -->
    <t-drawer
      v-model:visible="segmentDrawerVisible"
      placement="right"
      size="600px"
      :close-on-esc-keydown="false"
      :on-close="() => resetFormData()"
      :on-confirm="() => (segmentDrawerVisible = false)"
      header="New segment"
      style="z-index: 6000"
    >
      <t-form-item label="Segment name">
        <div class="h-[50px] flex-1">
          <div class="t-form__controls " :class="!newSegmentsData.name ? 't-is-error' : ''">
            <div class="t-form__controls-content max-w-[688px]">
              <t-input
                v-model="newSegmentsData.name"
                :disabled="isEditSegments"
                placeholder="Enter segment name"
              />
              <div v-if="!newSegmentsData.name" class="t-input__extra">Segment name is required！</div>
            </div>
          </div>
        </div>
      </t-form-item>
      <t-form-item label="Include people with following interest or behaviors" class="interest-behaviors">
        <t-select
          v-model="newSegmentsData.type"
          :disabled="isEditSegments"
          :options="CUSTOMAUDIENCETYPE"
        />
      </t-form-item>
      <t-form-item label="Interests or purchase intentions">
        <div class="h-[50px] flex-1">
          <div class="t-form__controls " :class="!newSegmentsData.keyword ? 't-is-error' : ''">
            <div class="t-form__controls-content max-w-[688px]">
              <textarea
                v-model="newSegmentsData.keyword"
                rows="10"
                :placeholder="keywordPlaceholder"
                :disabled="isEditSegments"
                class="rounded-default"
                style="border: 1px solid var(--aix-border-color-gray-placeholder); padding: 8px;width: 100%;"
              />
              <div v-if="!newSegmentsData.keyword" class="t-input__extra">
                Interests or purchase intentions is required！
              </div>
            </div>
          </div>
        </div>
      </t-form-item>
      <template #footer>
        <div class="flex items-center justify-center">
          <t-button
            v-if="!isEditSegments"
            class="mr-[16px]"
            :loading="newSegmentsLoading"
            @click="() => saveNewSegments()"
          >
            Save
          </t-button>
          <t-button variant="outline" @click="() => resetFormData()">Cancel</t-button>
        </div>
      </template>
    </t-drawer>
  </div>
</template>
<script setup lang="ts">
import {
  ref, defineComponent, toRefs,
  watch, onMounted, computed, nextTick,
} from 'vue';
import { cloneDeep, set } from 'lodash-es';
import { Icon } from 'tdesign-icons-vue-next';
import MultiFilterSelect from '../MultiFilterSelect.vue';
import { GENDEROPTIONS, MINAGEOPTIONS, MAXAGEOPTIONS, CUSTOMAUDIENCETYPE } from '../../../google/const';

import Country from '../Locations.vue';
import Language from 'common/components/LanguageSelect';
import Collpse from '../Collapse';
import { MessagePlugin, TdOptionProps } from 'tdesign-vue-next';
import { EBusOn } from '../../template/event';
import { initAdgroupModuleTargetingData } from 'common/service/td/google/utils';
import { useGGTreeListData } from '@/store/trade/google/google.store';
import { storeToRefs } from 'pinia';
import { AudienceSegments, AudienceSegmentsItem, AudienceSignal, AudienceType } from '../../../google/type';
import { getOptionsText } from '../../template/utils-common';
import { getCustomSegmentsList, createAustomSements, getAudienceList } from 'common/service/td/google/options';
import { useCommonParams, useCurrentData } from '../../../common/template/compose/currentCompose';
import { useEnv } from 'common/compose/env';

const { isProduction } = useEnv();
const { globalOptions } = storeToRefs(useGGTreeListData());
const googleStore = useGGTreeListData();


type AudienceTemplate = {
  template_id: string,
  template_name: string,
  targeting: any,
};

defineComponent({
  Country,
  Icon,
  Language,
  Collpse,
  MultiFilterSelect,
});

const defaultAudienceData = initAdgroupModuleTargetingData();

type SegmentItem = {
  name: string,
  type: number,
  keyword: string,
};

const defaultNewSegmentsData: SegmentItem = {
  name: '',
  type: 5,
  keyword: '',
};

const props = defineProps({
  isReviewing: {
    type: Boolean,
    default: false,
  },
  options: {
    type: Object,
    default: () => {},
  },
  modelValue: {
    type: String,
    default: '',
  },
  campaignName: {
    type: String,
    default: '',
  },
});
const emits = defineEmits(['update:modelValue']);

EBusOn((evt: unknown, data: any) => {
  if (evt === 'AudienceValue') {
    const { key, val } = data;
    set(datas.value, key, val);
  }
  if (evt === 'checkIsAudienceSaved') {
    if (isNewAudience.value) {
      saveAudience();
    }
  }
});

const isNewAudience = ref(true);
const { isReviewing } = toRefs(props);
const isShowSelectList = ref(true);
// new segments相关
const segmentDrawerVisible = ref(false);
const newSegmentsData = ref(cloneDeep(defaultNewSegmentsData));
const keywordPlaceholder = 'Add interests or purchase intentions separated by a new line or ","';
const segmentsListOptions = ref<AudienceSegmentsItem[]>([]);
const yourDataListOptions = ref<AudienceType[]>([]);
const newSegmentsLoading = ref(false);
const isEditSegments = ref(false);


const datas = ref(cloneDeep(defaultAudienceData));
const getDefaultTemplateName = (currentData?: any) => {
  const rejion = props.campaignName.split('-')[1] || 'gl';
  const {
    gender: {
      genders,
      include_undetermined: genderIncludeUndetermined,
    },
    age: {
      age_ranges: [
        {
          min_age: minAge,
          max_age: maxAge,
        },
      ],
      include_undetermined: ageIncludeUndetermined,
    },
  } = currentData || datas.value;

  const minAgeText = MINAGEOPTIONS.find(item => item.value === minAge)?.label;
  const maxAgeText = MAXAGEOPTIONS.find(item => item.value === maxAge)?.label;
  const unkonwnAgeText = ageIncludeUndetermined ? '_unknow' : '';
  const ageAgroups = `${minAgeText}_${maxAgeText}${unkonwnAgeText}`;
  const genderMap: { [key: string]: string } = {
    10: 'm',
    11: 'f',
  };
  const isAllGender = genders.length === 2 && genderIncludeUndetermined;
  let genderText = '';
  if (isAllGender) {
    genderText = 'all';
  } else {
    const genderText1 = genderMap[genders[0]];
    const genderText2 = genderMap[genders[1]];
    const genderText3 = genderIncludeUndetermined ? 'unknow' : '';
    genderText = [genderText1, genderText2, genderText3].filter(item => item).join('_');
  }
  return `${rejion}-${ageAgroups}${genderText ? `-${genderText}` : ''}`;
};
const newSavedAudience = ref<AudienceTemplate>({
  template_id: '',
  template_name: getDefaultTemplateName(),
  targeting: datas.value,
});

const userInterestListOptions = computed(() => {
  const { userInterestCategory = [] } = globalOptions.value;
  const userInterestList = userInterestCategory.map((item: { name: string, resource_name: string, }) => ({
    label: item.name,
    value: item.resource_name,
    ...item,
  }));
  return userInterestList;
});

const isValidate = ref(true);
const isShowNameError = computed(() => (isDulplicationOfNameFun()
    || (isNewAudience.value && !newSavedAudience.value.template_name)
    || (!isNewAudience.value && !selectedAudience.value.template_name)) && isValidate.value);
const nameErrorTip = () => {
  if (isDulplicationOfNameFun()) {
    return 'Name already exists！';
  }
  return 'Audience name is required!';
};
// 监听age和gender的变化，跟audience name l联动
watch(() => [datas.value.age, datas.value.gender, props.campaignName], () => {
  nextTick(() => {
    const name = getDefaultTemplateName();
    if (isNewAudience.value) {
      newSavedAudience.value.template_name = name;
    }
  });
}, {
  deep: true,
});

const putParamToTargeting = () => {
  const {
    audienceSegments: {
      customAudience,
      userInterest,
      userList,
    },
    gender: {
      genders,
      include_undetermined: genderIncludeUndetermined,
    },
    age: {
      age_ranges: [{
        min_age: minAge,
        max_age: maxAge,
      }],
      include_undetermined: ageIncludeUndetermined,
    },
  } = datas.value;
  const templateName = isNewAudience.value
    ? newSavedAudience.value.template_name : selectedAudience.value.template_name;
  const dimensions = [];
  const audienceSegmets: AudienceSegments = {
    audience_segments: {
      segments: [],
    },
  };
  if (customAudience.length || userInterest.length || userList.length) {
    dimensions.push(audienceSegmets);
  }
  const { segments } = audienceSegmets.audience_segments;
  if (customAudience.length) {
    customAudience.forEach((item: string) => {
      segments.push({
        custom_audience: {
          custom_audience: item,
        },
      });
    });
  }
  if (userInterest.length) {
    userInterest.forEach((item: string) => {
      segments.push({
        user_interest: {
          user_interest_category: item,
        },
      });
    });
  }
  if (userList.length) {
    userList.forEach((item: string) => {
      segments.push({
        user_list: {
          user_list: item,
        },
      });
    });
  }
  dimensions.push({
    gender: {
      genders,
      include_undetermined: genderIncludeUndetermined,
    },
  });
  const ageRanges = maxAge === 65 ? [{
    min_age: minAge,
  }] : [{ minAge, maxAge }];
  dimensions.push({
    age: {
      age_ranges: ageRanges,
      include_undetermined: ageIncludeUndetermined,
    },
  });
  return {
    name: templateName,
    dimensions,
  };
};

const getMinOptions = (options: TdOptionProps[]) => options.map((item: TdOptionProps) => {
  const newItem = { ...item };
  newItem.disabled = Number(item.value) > datas.value.age.age_ranges[0].max_age;
  return newItem;
});
const getMaxOptions = (options: TdOptionProps[]) => options.map((item: TdOptionProps) => {
  const newItem = { ...item };
  newItem.disabled = Number(item.value) < datas.value.age.age_ranges[0].min_age;
  return newItem;
});
const getAgeText = (options: any, age: number) => (isReviewing.value || selectedAudienceId.value ? getOptionsText(options, [age]) : '');

const resetFormData = () => {
  newSegmentsData.value = cloneDeep(defaultNewSegmentsData);
  segmentDrawerVisible.value = false;
};

const editSegment = (item: AudienceSegmentsItem) => {
  newSegmentsData.value = {
    name: item.name,
    type: item.type,
    keyword: item.members.map(key => key.keyword).join(','),
  };
  segmentDrawerVisible.value = true;
  isEditSegments.value = true;
};


const audienceTemplateList = ref<AudienceTemplate[]>([]);
const isLoading = ref(false);
const selectedAudienceId = ref(props.modelValue || '');
const selectedAudience = ref<AudienceTemplate>({
  template_id: '',
  template_name: getDefaultTemplateName(),
  targeting: {},
});

const selectAudienceChange = () => {
  if (!selectedAudienceId.value) {
    return;
  }
  selectedAudience.value = audienceTemplateList.value
    .find(item => item.template_id === selectedAudienceId.value) || {
    template_id: '',
    template_name: getDefaultTemplateName(cloneDeep(defaultAudienceData)),
    targeting: {},
  };
  if (!isNewAudience.value) {
    datas.value = selectedAudience.value.targeting;
  }
};

const isDulplicationOfNameFun = () => {
  if (isNewAudience.value) {
    return !!audienceTemplateList.value
      .find(item => item.template_name === newSavedAudience.value.template_name);
  }
  return !!audienceTemplateList.value
    .find(item => (item.template_name === selectedAudience.value.template_name
      && item.template_id !== selectedAudienceId.value));
};

const switchAudienceType = () => {
  if (isNewAudience.value) {
    datas.value = newSavedAudience.value.targeting;
    newSavedAudience.value.template_name = getDefaultTemplateName();
    selectedAudienceId.value = '';
  } else {
    datas.value = selectedAudience.value?.targeting || cloneDeep(defaultAudienceData);
  }
};

// 数据变化时，提交到
watch(() => selectedAudienceId.value, () => {
  emits('update:modelValue', selectedAudienceId.value);
});

watch(() => props.modelValue, () => {
  selectedAudienceId.value = props.modelValue;
  if (props.modelValue) {
    isNewAudience.value = false;
    selectAudienceChange();
  }
});

// const updateAudience = async () => {
//   if (isDulplicationName.value) {
//     return false;
//   }
//   isLoading.value = true;
//   const result = await googleStore.saveTargetingTemp(selectedAudience.value);
//   const { targeting: { template_id: templateId } } = result as any;
//   selectedAudienceId.value = templateId;
//   selectedAudience.value.template_id = templateId;
//   await getAudienceTemplateList();
//   // 关闭命名弹窗
//   isShowSaveAudienceDialog.value = false;
//   isLoading.value = false;
//   MessagePlugin.success('Update Audience .!');
// };
const saveAudience = async () => {
  isValidate.value = true;
  const audience = putParamToTargeting();
  const { genders, include_undetermined: genderIncludeUndetermined } = datas.value.gender;
  if (isDulplicationOfNameFun() || !audience.name || (genders.length === 0 && !genderIncludeUndetermined)) {
    MessagePlugin.error('There are items in the Targeting form that fail verification!');
    document.getElementById('targeting-block')?.scrollIntoView();
    return false;
  }
  if (isProduction) {
    MessagePlugin.success('save Audience success!');
    newSavedAudience.value = {
      template_id: '',
      template_name: '',
      targeting: cloneDeep(defaultAudienceData),
    };
    isNewAudience.value = false;
  } else {
    isLoading.value = true;
    const result = await googleStore.saveTargetingTemp(audience);
    const { resource_name: resourceName, result: { error_code: errCode, error_message: errorMsg } } = result as any;
    if (errCode !== 0) {
      return MessagePlugin.error(errorMsg);
    }
    // 保存后切换到已保存的audience列表， 并重置new audiece页面为空
    isNewAudience.value = false;
    // datas.value = newSavedAudience.value.targeting;

    await getAudienceTemplateList();
    selectedAudienceId.value = resourceName;
    // 获取到当前选中的对象值
    // selectAudienceChange();
    newSavedAudience.value = {
      template_id: '',
      template_name: '',
      targeting: cloneDeep(defaultAudienceData),
    };
    MessagePlugin.success('save Audience success!');
    isLoading.value = false;
  }
};

const getAudienceTemplateList = async () => {
  isShowSelectList.value = false;
  const resultList = await googleStore.getTargetingTemp();
  let { audiences = [] as AudienceSignal[] } = resultList as any;
  audiences = audiences.map((item: AudienceSignal) => {
    const { dimensions } = item;
    const targeting = {
      ...cloneDeep(defaultAudienceData),
    };
    dimensions.forEach((item) => {
      const { audience_segments: audienceSegments, age, gender } = item;
      if (gender) {
        targeting.gender = gender;
      }
      if (age) {
        if (!age.age_ranges[0].max_age) {
          age.age_ranges[0].max_age = 65;
        }
        targeting.age = age;
      }
      if (audienceSegments) {
        audienceSegments.segments.forEach((item) => {
          const { custom_audience: customAudience, user_list: userList, user_interest: userInterest } = item;
          if (customAudience) {
            targeting.audienceSegments.customAudience.push(customAudience.custom_audience);
          }
          if (userList) {
            targeting.audienceSegments.userList.push(userList.user_list);
          }
          if (userInterest) {
            targeting.audienceSegments.userInterest.push(userInterest.user_interest_category);
          }
        });
      }
    });

    const newItem = {
      template_name: item.name,
      template_id: item.resource_name,
      targeting,
    };
    return newItem;
  });


  audienceTemplateList.value = audiences;
  if (audienceTemplateList.value.length > 0) {
    isNewAudience.value = false;
    // 这里处理如果外部已经选中某个id，直接显示
    if (selectedAudienceId.value) {
      selectAudienceChange();
    }
  } else {
    isNewAudience.value = true;
  }
  isShowSelectList.value = true;
};

// const deleteTemplate = async (item: AudienceTemplate) => {
//   const confirm = DialogPlugin.confirm({
//     theme: 'danger',
//     header: 'Delete audience',
//     body: 'Please confirm to delete this audience?',
//     cancelBtn: 'Cancel',
//     confirmBtn: 'Confirm',
//     zIndex: 8000,
//     onConfirm: () => {
//       confirmCallback();
//       confirm.hide();
//     },
//   });
//   const confirmCallback = async () => {
//     const isCurrentTemplateDelete = item.template_id === selectedAudienceId.value;
//     await googleStore.deleteTargetingTemp([item.template_id]);
//     await getAudienceTemplateList();
//     if (isCurrentTemplateDelete) {
//       selectedAudience.value.template_id = '';
//     }
//     if (audienceTemplateList.value.length > 0) {
//       isCurrentTemplateDelete && (selectedAudienceId.value = '');
//     } else {
//       isNewAudience.value = true;
//       switchAudienceType();
//     }
//   };
// };

const saveNewSegments = async () => {
  const { name, type, keyword } = newSegmentsData.value;
  const keywords = keyword.split(/\n|,/g).filter(item => item);
  if (!name || !keyword || keywords.length === 0) {
    return false;
  }
  newSegmentsLoading.value = true;
  const params = {
    custom_audience: {
      name,
      type,
      members: keywords.map(item => ({
        member_type: 2,
        keyword: item,
      })),
    },
  };
  const { game_code: gameCode, account_id: accountId } = useCommonParams();
  const {
    resource_name: resourceName, result: { error_message: errMsg },
  } = await createAustomSements(gameCode, accountId, params);
  if (resourceName) {
    await getSegmentsListOptions();
    datas.value.audienceSegments.customAudience.push(resourceName);
    segmentDrawerVisible.value = false;
    resetFormData();
  } else {
    MessagePlugin.error(errMsg);
  }
  newSegmentsLoading.value = false;
};
const getSegmentsListOptions = async () => {
  const params = useCommonParams();
  const segmentsList = await getCustomSegmentsList(params.game_code, params.account_id);
  segmentsListOptions.value = segmentsList;
};

const getYourDataListOptions = async () => {
  const params = useCommonParams();
  const { campaign } = useCurrentData();
  const { app_info: { app_id: appId } } = campaign;
  const list = await getAudienceList(params.game_code, params.account_id, appId, 'retargeting');
  const yourDataList = list.filter(item => Number(item.status) === 2).map(item => ({
    ...item, label: item.td_info.name, value: item.td_info.id,
  }));
  yourDataListOptions.value = yourDataList;
};

onMounted(async () => {
  // 获取到模板列表
  getAudienceTemplateList();
  // audienceSegments
  getSegmentsListOptions();
  getYourDataListOptions();
});

</script>
<style lang="scss" scoped>
  :deep .interests-demographics, :deep .interest-behaviors {
    .t-form__label {
      height: 48px;
    }
  }
</style>
