<template>
  <div>
    <t-form-item v-if="!isReviewing" label="Audience">
      <t-radio-group
        v-model="isNewAudience"
        :disabled="audienceTemplateList.length === 0"
        @change="() => switchAudienceType()"
      >
        <t-radio :value="true">
          New Audience
        </t-radio>
        <t-radio :value="false">
          Saved Audience
        </t-radio>
      </t-radio-group>
    </t-form-item>
    <t-form-item v-if="!isNewAudience && !isReviewing" label="">
      <t-select
        v-if="isShowSelectList"
        v-model="selectedAudienceId"
        placeholder="Search or select audience"
        clearable
        @change="selectAudienceChange"
      >
        <t-option
          v-for="item in audienceTemplateList"
          :key="item.template_id + item.template_name"
          :label="item.template_name"
          :value="item.template_id"
        >
          <div class="flex items-center">
            <span>{{ item.template_name }}</span>
            <span title="delete" @click.stop="() => deleteTemplate(item)">
              <Icon name="delete" class="ml-[10px] cursor-pointer" />
            </span>
          </div>
        </t-option>
      </t-select>
    </t-form-item>
    <t-form-item v-if="!isNewAudience && !isReviewing" label="Name">
      <span>
        {{ selectedAudience.template_name }}
      </span>
    </t-form-item>
    <t-form-item label="Mode">
      <t-radio-group
        v-if="!isReviewing"
        v-model="datas.auto_targeting_enabled"
        :options="autoTragetingOptions"
      />
      <span v-else>
        {{ getOptionsText(autoTragetingOptions, [datas.auto_targeting_enabled]) }}
      </span>
    </t-form-item>
    <t-form-item
      label="Locations" name="module_targeting.demographics.location_ids"
      style="clear: both;"
    >
      <span v-if="isReviewing">
        {{ getOptionsText(computedOptions.locations, datas.demographics.location_ids) }}
      </span>
      <Country
        v-else
        v-model="datas.demographics.location_ids"
        :options="computedOptions.locations || []"
        @change="locationsChange"
      />
    </t-form-item>
    <t-form-item
      label="Language(optional)" name="language" style="clear: both;"
    >
      <span v-if="isReviewing">
        {{ getOptionsText(computedOptions.languageOptions, datas.demographics.languages) || 'All' }}
      </span>
      <t-select
        v-else
        v-model="datas.demographics.languages"
        clearable
        multiple
        filterable
        :min-collapsed-num="4"
        placeholder="Search or select a location"
        :options="computedOptions.languageOptions"
        class="max-w-[688px] inline-block"
      >
        <template #prefixIcon>
          <div class="flex justify-center pl-[8px]">
            <icon name="search" class="text-lg text-black-secondary" />
            <span
              class="inline-block pr-[7px] text-black-secondary w-[1px] h-[16px]"
              style="border-right: 1px solid var(--aix-border-color-black-disabled);"
            />
          </div>
        </template>
      </t-select>
    </t-form-item>
    <template v-if="!datas.auto_targeting_enabled">
      <t-form-item
        label="Gender" name="gender" style="clear: both;"
      >
        <span v-if="isReviewing">
          {{ getOptionsText(computedOptions.genderOptions, [datas.demographics.gender]) }}
        </span>
        <t-radio-group
          v-else
          v-model="datas.demographics.gender"
          :options="computedOptions.genderOptions"
          @change="agesOrGenderChange"
        />
      </t-form-item>
      <t-form-item label="Age(optional)" name="age">
        <span v-if="isReviewing">
          {{ getOptionsText(computedOptions.ageOptions, datas.demographics.age_groups) || 'All' }}
        </span>
        <t-select
          v-else
          v-model="datas.demographics.age_groups"
          :options="computedOptions.ageOptions"
          multiple
          clearable
          :min-collapsed-num="4"
          placeholder="All"
          class="max-w-[688px]"
          @change="agesOrGenderChange"
        />
      </t-form-item>
      <Collpse :header-title="{content: 'Custom Audience\n(optional)', style: 'width: 118px',}" :is-vertical="false">
        <t-form-item label-align="top">
          <div class="mb-[24px]">
            <t-form-item label="Include" label-align="top">
              <span v-if="isReviewing">
                {{ getOptionsText(computedOptions.audienceOptions, datas.audience.audience_ids) }}
              </span>
              <t-select
                v-else
                v-model="datas.audience.audience_ids"
                :min-collapsed-num="2"
                creatable
                multiple
                placeholder="select one or more categories "
                class="w-[400px] inline-block"
              >
                <t-option
                  v-for="item in computedOptions.audienceOptions"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                  :disabled="datas.audience.excluded_audience_ids.includes(item.value)"
                />
              </t-select>
            </t-form-item>
            <t-form-item label="Exclude" label-align="top">
              <span v-if="isReviewing">
                {{ getOptionsText(computedOptions.audienceOptions, datas.audience.excluded_audience_ids) }}
              </span>
              <t-select
                v-else
                v-model="datas.audience.excluded_audience_ids"
                clearable
                multiple
                :min-collapsed-num="2"
                placeholder="select one or more categories"
                class="w-[400px] inline-block"
              >
                <t-option
                  v-for="item in computedOptions.audienceOptions"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                  :disabled="datas.audience.audience_ids.includes(item.value)"
                />
              </t-select>
            </t-form-item>
          </div>
        </t-form-item>
      </Collpse>
      <t-form-item label="Os versions" name="os">
        <span v-if="isReviewing">
          {{ getOptionsText(computedOptions.osOptions, [datas.device.min_os_version]) || 'All' }}
        </span>
        <t-select
          v-else
          v-model="datas.device.min_os_version" :options="computedOptions.osOptions"
          class="max-w-[688px]"
        />
      </t-form-item>
      <t-form-item name="os">
        <template #label>
          Device model<br>(optional)
        </template>
        <span v-if="isReviewing">
          {{ getOptionsText(computedOptions.deviceModelOptions, datas.device.device_models_ids) || 'All' }}
        </span>
        <t-cascader
          v-else
          v-model="datas.device.device_models_ids"
          :options="computedOptions.deviceModelOptions"
          :min-collapsed-num="4"
          placeholder="All"
          multiple
          clearable
          class="max-w-[688px]"
          :disabled="isDisableDevice"
        />
      </t-form-item>
      <t-form-item label="Connection type">
        <span v-if="isReviewing">
          {{ getOptionsText(computedOptions.connectionTypeOptions, datas.device.network_types) }}
        </span>
        <t-checkbox-group
          v-else
          v-model="datas.device.network_types"
          :options="computedOptions.connectionTypeOptions"
          @change="networkTypeChange"
        />
      </t-form-item>
      <t-form-item label="Carriers(optional)">
        <span v-if="isReviewing">
          {{ getOptionsText(computedOptions.carriersOptions, datas.device.carrier_ids) || 'All' }}
        </span>
        <t-cascader
          v-else
          v-model="datas.device.carrier_ids"
          :options="computedOptions.carriersOptions"
          placeholder="All"
          multiple
          clearable
          class="max-w-[688px]"
        />
      </t-form-item>
      <t-form-item label="Device price">
        <div>
          <div>
            <span v-if="isReviewing">
              {{ getOptionsText(devicePriceTypeOptions, [devicePriceType]) }}
            </span>
            <t-radio-group
              v-else
              v-model="devicePriceType"
              :options="devicePriceTypeOptions"
              :disabled="isDisablePrice"
              @change="devicePriceTypeChange"
            />
          </div>
          <div
            v-show="devicePriceType"
            class="flex items-center mt-[20px]"
          >
            <span v-if="isReviewing">
              {{ getOptionsText(computedOptions.priceRangeStartOptions, [datas.device.device_price_ranges[0]]) }}
            </span>
            <t-select
              v-else
              v-model="datas.device.device_price_ranges[0]"
              :options="computedOptions.priceRangeStartOptions" class="w-[200px]"
            />
            <span class="ml-[10px] mr-[10px]">-</span>
            <span v-if="isReviewing">
              {{ getOptionsText(computedOptions.priceRangeEndOptions, [datas.device.device_price_ranges[1]]) }}
            </span>
            <t-select
              v-else
              v-model="datas.device.device_price_ranges[1]"
              :options="computedOptions.priceRangeEndOptions" class="w-[200px]"
            />
            <span class="ml-[10px]">USD</span>
          </div>
        </div>
      </t-form-item>
      <t-form-item>
        <template #label>
          Add interests<br>(optional)
        </template>
        <span v-if="isReviewing">
          {{ getOptionsText(computedOptions.interestOptions, datas.interests_behavior.interest_category_ids) }}
        </span>
        <t-cascader
          v-else
          v-model="datas.interests_behavior.interest_category_ids"
          :options="computedOptions.interestOptions"
          :min-collapsed-num="2"
          multiple
          clearable
          class="max-w-[688px]"
        />
      </t-form-item>
      <Collpse
        :header-title="{content: 'Add behavior Video interactions\n(optional)', style: 'width: 118px' }"
        :is-vertical="false"
      >
        <t-form-item
          label-align="top" name="module_targeting.interests_behavior.behaviors[0]"
        >
          <div class="mb-[24px]">
            <t-form-item
              label="Define how they've interacted with the videos"
              label-align="top"
            >
              <span v-if="isReviewing">
                {{ getOptionsText(computedOptions.interestVideoOptions,
                                  datas.interests_behavior.behaviors[0].video_user_actions) }}
              </span>
              <t-checkbox-group
                v-else
                v-model="datas.interests_behavior.behaviors[0].video_user_actions"
                :options="computedOptions.interestVideoOptions"
              />
            </t-form-item>
            <t-form-item
              label="Define what kind of videos they’ve interacted with"
              label-align="top"
            >
              <span v-if="isReviewing">
                {{ getOptionsText(computedOptions.videoCategoriesOptions,
                                  datas.interests_behavior.behaviors[0].action_category_ids) }}
              </span>
              <t-cascader
                v-else
                v-model="datas.interests_behavior.behaviors[0].action_category_ids"
                :options="computedOptions.videoCategoriesOptions"
                :min-collapsed-num="2"
                multiple
                clearable
                class="w-[400px]"
              />
            </t-form-item>
            <t-form-item
              label="Select a time period to include action from"
              label-align="top"
            >
              <span v-if="isReviewing">
                {{ getOptionsText(computedOptions.periodOptions,
                                  [datas.interests_behavior.behaviors[0].action_period]) }}
              </span>
              <t-radio-group
                v-else
                v-model="datas.interests_behavior.behaviors[0].action_period"
                :options="computedOptions.periodOptions"
              />
            </t-form-item>
          </div>
        </t-form-item>
      </Collpse>
      <Collpse
        :header-title="{
          content: 'Add behavior Creator interactions\n(optional)',
          style: 'width: 118px;padding-bottom: 18px',
        }"
        :is-vertical="false"
      >
        <t-form-item
          label-align="top"
          name="module_targeting.interests_behavior.behaviors[1]"
        >
          <div>
            <t-form-item
              label="Define how they’ve interacted with creators"
              label-align="top"
              name="module_targeting.interests_behavior.behaviors[1].video_user_actions"
            >
              <span v-if="isReviewing">
                {{ getOptionsText(computedOptions.videoUserActionsOptions,
                                  datas.interests_behavior.behaviors[1].video_user_actions) }}
              </span>
              <t-checkbox-group
                v-else
                v-model="datas.interests_behavior.behaviors[1].video_user_actions"
                :options="computedOptions.videoUserActionsOptions"
              />
            </t-form-item>
            <t-form-item
              label="Define what kind of creators they’ve interacted with"
              label-align="top"
              name="module_targeting.interests_behavior.behaviors[1].action_category_ids"
            >
              <span v-if="isReviewing">
                {{ getOptionsText(computedOptions.creatorCategoriesOptions,
                                  datas.interests_behavior.behaviors[1].action_category_ids) }}
              </span>
              <t-cascader
                v-else
                v-model="datas.interests_behavior.behaviors[1].action_category_ids"
                :options="computedOptions.creatorCategoriesOptions"
                multiple
                clearable
                class="w-[400px]"
              />
            </t-form-item>
          </div>
        </t-form-item>
      </Collpse>
      <Collpse
        :header-title="{content: 'Add behavior Hashtag interactions\n(optional)', style: 'width: 118px' }"
        :is-vertical="false"
      >
        <t-form-item label-align="top">
          <div class="mb-[24px]">
            <t-form-item
              label="People who’ve watched videos with the following hashtags"
              label-align="top"
            >
              <span v-if="isReviewing">
                {{ getOptionsText(hashTagRecommendOptions,
                                  datas.interests_behavior.behaviors[2].action_category_ids) }}
              </span>
              <t-select
                v-else
                v-model="datas.interests_behavior.behaviors[2].action_category_ids"
                multiple
                filterable
                placeholder="input keyword"
                reserve-keyword
                :loading="loading"
                class="w-[400px]"
                @search="(values: string) => {handleSearch(values)}"
              >
                <t-option
                  v-for="item in hashTagRecommendOptions"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </t-select>
            </t-form-item>
          </div>
        </t-form-item>
      </Collpse>
    </template>
    <t-form-item v-if="!isReviewing">
      <t-button
        v-if="isNewAudience"
        theme="primary"
        @click="isShowSaveAudienceDialog = true;"
      >
        Save Audience
      </t-button>
      <t-button
        v-else
        theme="primary"
        @click="isShowSaveAudienceDialog = true;"
      >
        Update Audience
      </t-button>
    </t-form-item>
    <t-dialog
      header="Save audience"
      :visible="isShowSaveAudienceDialog"
      :on-close="() => { isShowSaveAudienceDialog = false; }"
    >
      <template #body>
        <div class="h-[50px]">
          <div class="t-form__controls " :class="isDulplicationName ? 't-is-error' : ''">
            <div class="t-form__controls-content">
              <t-input
                v-if="isNewAudience"
                v-model="newSavedAudience.template_name"
                placeholder="name this audience"
                @change="isDulplicationOfNameFun"
              />
              <t-input
                v-else
                v-model="selectedAudience.template_name"
                placeholder="name this audience"
                @change="isDulplicationOfNameFun"
              />
            </div>
            <div v-if="isDulplicationName" class="t-input__extra">Name already exists！</div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="flex justify-end">
          <t-button
            theme="primary"
            :disabled="isDulplicationName || (isNewAudience && !newSavedAudience.template_name)
              || (!isNewAudience && !selectedAudience.template_name)"
            :loading="isLoading"
            @click="() => { isNewAudience ? saveAudience() : updateAudience() }"
          >
            Confirm
          </t-button>
        </div>
      </template>
    </t-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, defineComponent, toRefs, computed, watch, onMounted, nextTick, watchEffect } from 'vue';
import { cloneDeep, set, uniq } from 'lodash-es';
import { Icon } from 'tdesign-icons-vue-next';
import { useTreeListDataStore } from '@/store/trade/template.store';
import Country from '../Locations.vue';
import Language from 'common/components/LanguageSelect';
import Collpse from '../Collapse';
import type { AudienceOptionText } from '../../template/type';
import { MessagePlugin, DialogPlugin, TdOptionProps } from 'tdesign-vue-next';
import { getAgeText, getGenderText, getOptionsText, getLocationsText, getOptionsValues } from '../../template/utils-common';
import { EBusEmit, EBusOn } from '../../template/event';
import { initAdgroupModuleTargetingData } from 'common/service/td/tiktok/utils';
import { useEnv } from 'common/compose/env';

const { isProduction } = useEnv();

type AudienceTemplate = {
  template_id: string,
  template_name: string,
  targeting: any,
};

defineComponent({
  Country,
  Icon,
  Language,
  Collpse,
});

const treeStore = useTreeListDataStore();

const defaultAudienceData = initAdgroupModuleTargetingData();

const props = defineProps({
  isReviewing: {
    type: Boolean,
    default: false,
  },
  options: {
    type: Object,
    default: () => {},
  },
  modelValue: {
    type: Object,
    default: () => {},
  },
  customAudienceOptions: {
    type: Array,
    default: () => [{
      label: 'text', value: '111',
    }],
  },
});
const emits = defineEmits(['update:modelValue']);
EBusOn((evt: unknown, data: any) => {
  const { key, val } = data;
  if (evt === 'AudienceValue') {
    set(datas.value, key, val);
  }
});
const isNewAudience = ref(true);
const { options, isReviewing } = toRefs(props);
const isShowSelectList = ref(true);

// 直接值复制，不对外绑定。
const datas = ref(cloneDeep(props.modelValue));
const getDefaultTemplateName = (currentTemplateName?: string) => {
  const { campaignNode } = treeStore.current;
  const rejion = campaignNode.data.campaign_name.split('-')[1] || 'gl';
  const { age_groups: ageAgroups = [], gender = 'GENDER_UNLIMITED' } = datas.value?.demographics || {};
  let lastffix = '';
  if (currentTemplateName) {
    const temp = currentTemplateName.split('-');
    lastffix = temp.slice(temp[2] === 'all' ? 3 : 4)
      .join('_');
  }
  return `${rejion}-${getAgeText(ageAgroups)}-${getGenderText(gender)}${lastffix ? `-${lastffix}` : ''}`;
};
const newSavedAudience = ref<AudienceTemplate>({
  template_id: '',
  template_name: getDefaultTemplateName(),
  targeting: datas.value,
});
const agesOrGenderChange = () => {
  if (isNewAudience.value) {
    newSavedAudience.value.template_name = getDefaultTemplateName(newSavedAudience.value.template_name);
  } else {
    selectedAudience.value.template_name = getDefaultTemplateName(selectedAudience.value.template_name);
  }
};

const locationsChange = function () {
  const result = getLocationsText(computedOptions.value.locations, datas.value.demographics.location_ids);
  const {
    codeResult,
  } = result as any;
  let {
    locationTexts,
  } = result as any;
  let isGl = true;
  computedOptions.value.locations.some((item: TdOptionProps) => {
    if (!locationTexts.includes(item.value)) {
      isGl = false;
      return true;
    }
    return false;
  });
  if (isGl) {
    locationTexts = 'gl';
  }
  const { codeResult: languageTexts } = getOptionsValues(
    computedOptions.value.languageCountrys,
    codeResult, computedOptions.value.languageOptions,
  );
  locationTexts = locationTexts.toLowerCase();
  datas.value.demographics.languages = uniq(languageTexts.concat(datas.value.demographics.languages));
  EBusEmit('locationsEvt', {
    locationTexts,
    languageTexts,
  });
  nextTick(() => {
    agesOrGenderChange();
  });
};

const audienceTemplateList = ref<AudienceTemplate[]>([]);
const isShowSaveAudienceDialog = ref(false);
const isLoading = ref(false);
const isDulplicationName = ref(false);
const selectedAudienceId = ref('');
const selectedAudience = ref<AudienceTemplate>({
  template_id: '',
  template_name: getDefaultTemplateName(),
  targeting: {},
});

const selectAudienceChange = () => {
  if (!selectedAudienceId.value) {
    return;
  }
  selectedAudience.value = audienceTemplateList.value
    .find(item => item.template_id === selectedAudienceId.value) || {
    template_id: '',
    template_name: getDefaultTemplateName(),
    targeting: {},
  };
  if (!isNewAudience.value) {
    datas.value = selectedAudience.value.targeting;
  }
};


const isDulplicationOfNameFun = () => {
  if (isNewAudience.value) {
    isDulplicationName.value = !!audienceTemplateList.value
      .find(item => item.template_name === newSavedAudience.value.template_name);
  } else {
    isDulplicationName.value = !!audienceTemplateList.value
      .find(item => (item.template_name === selectedAudience.value.template_name
        && item.template_id !== selectedAudienceId.value));
  }
};

const switchAudienceType = () => {
  if (isNewAudience.value) {
    datas.value = newSavedAudience.value.targeting;
    newSavedAudience.value.template_name = getDefaultTemplateName();
  } else {
    datas.value = selectedAudience.value?.targeting || cloneDeep(defaultAudienceData);
  }
};
const updateModelValue = () => {
  emits('update:modelValue', datas.value);
};

const isDisablePrice = ref(false);
const isDisableDevice = ref(false);
watch(() => props.modelValue, () => {
  datas.value = props.modelValue;
});
// 数据变化时，提交到
watch(() => datas.value, () => {
  if (datas.value.device.device_models_ids.length > 0) {
    isDisablePrice.value = true;
    devicePriceType.value = false;
    if (datas.value.device.device_price_ranges.length > 0) {
      datas.value.device.device_price_ranges = [];
    }
  } else {
    isDisablePrice.value = false;
  }
  if (
    datas.value.device.device_price_ranges.length > 0
    && (datas.value.device.device_price_ranges[0] || datas.value.device.device_price_ranges[1])) {
    isDisableDevice.value = true;
  } else {
    isDisableDevice.value = false;
  }
  updateModelValue();
}, {
  deep: true,
});

const devicePriceType = ref(false);
const autoTragetingOptions = [
  { label: 'Custom', value: false },
  { label: 'Auto', value: true },
];
watch(() => datas, () => {
  if (datas.value.device.device_price_ranges.length > 0
    && (datas.value.device.device_price_ranges[0] || datas.value.device.device_price_ranges[1])) {
    devicePriceType.value = true;
  }
}, {
  immediate: true,
});

const computedOptions = computed(() => options.value || {});
// 这个没有在结果集中  所以不写入computedOptions
const devicePriceTypeOptions = [
  { label: 'Any price', value: false },
  { label: 'Specific range', value: true },
];
const devicePriceTypeChange = () => {
  if (!devicePriceType.value) {
    isDisableDevice.value = false;
    datas.value.device.device_price_ranges = [];
  }
};

// 获取hashTagRecommendsOPtions
const hashTagRecommendOptions = ref<AudienceOptionText[]>([]);

const loading = ref(false);
const handleSearch = (values: string | string[], type = 'keyword') => {
  console.log(values);
  if (!values || values.length === 0) {
    return false;
  }
  loading.value = true;
  computedOptions.value.useHashTagRecommendsOptions(Array.isArray(values)
    ? values : [values], type).then((tempOptions: {label: string, value: string}[]) => {
    const currentOptionsList = hashTagRecommendOptions.value
      .filter(item => datas.value.interests_behavior.behaviors[2].action_category_ids.includes(item.value)
        && !tempOptions.find(tempItem => tempItem.value === item.value));
    hashTagRecommendOptions.value = [...tempOptions, ...currentOptionsList];
  });
  loading.value = false;
};
// 处理初始化数据
let isInitTagRecoment = false;
watchEffect(() => {
  if (computedOptions.value.useHashTagRecommendsOptions
    && datas.value.interests_behavior.behaviors[2].action_category_ids.length > 0 && !isInitTagRecoment) {
    handleSearch(datas.value.interests_behavior.behaviors[2].action_category_ids, 'keywords_id');
    isInitTagRecoment = true;
  }
});

const networkTypeChange = () => {
  if (datas.value.device.network_types.length > 1) {
    const noneIndex = datas.value.device.network_types.findIndex((item: string) => item === 'NONE');
    if (noneIndex !== -1) {
      datas.value.device.network_types.splice(noneIndex, 1);
    }
  }
  if (datas.value.device.network_types.length === 0) {
    datas.value.device.network_types.push('NONE');
  }
};

const updateAudience = async () => {
  if (isDulplicationName.value) {
    return false;
  }
  isLoading.value = true;
  const result = await treeStore.addOrUpdateTemplate({
    template_id: selectedAudience.value.template_id,
    template_name: selectedAudience.value.template_name,
    template_content: JSON.stringify(selectedAudience.value.targeting),
  });
  const { template: { template_id: templateId } } = result as any;
  selectedAudienceId.value = templateId;
  selectedAudience.value.template_id = templateId;
  await getAudienceTemplateList();
  // 关闭命名弹窗
  isShowSaveAudienceDialog.value = false;
  isLoading.value = false;
  MessagePlugin.success('Update Audience success!');
};
const saveAudience = async () => {
  if (isDulplicationName.value) {
    return false;
  }
  if (isProduction) {
    newSavedAudience.value = {
      template_id: '',
      template_name: '',
      targeting: cloneDeep(defaultAudienceData),
    };
    isNewAudience.value = false;
    MessagePlugin.success('save Audience success!');
    isShowSaveAudienceDialog.value = false;
  } else {
    isLoading.value = true;
    const result = await treeStore.addOrUpdateTemplate({
      template_id: newSavedAudience.value.template_id,
      template_name: newSavedAudience.value.template_name,
      template_content: JSON.stringify(newSavedAudience.value.targeting),
    });
    const { template } = result as any;
    // 保存后切换到已保存的audience列表， 并重置new audiece页面为空
    isNewAudience.value = false;
    // datas.value = newSavedAudience.value.targeting;

    await getAudienceTemplateList();
    selectedAudienceId.value = template.template_id;
    // 获取到当前选中的对象值
    selectAudienceChange();
    newSavedAudience.value = {
      template_id: '',
      template_name: '',
      targeting: cloneDeep(defaultAudienceData),
    };
    MessagePlugin.success('save Audience success!');
    // 关闭命名弹窗
    isShowSaveAudienceDialog.value = false;
    isLoading.value = false;
  }
};

const getAudienceTemplateList = async () => {
  isShowSelectList.value = false;
  const resultList = await treeStore.getTemplateList();
  let { templates = [] } = resultList as any;
  templates = templates.map((item: { template_content: string}) => ({
    ...item,
    targeting: JSON.parse(item.template_content),
  }));
  audienceTemplateList.value = templates;
  // 如果列表有值，并且当前选中是一个空值，则默认选择第一个
  if (audienceTemplateList.value.length > 0) {
    if (selectedAudienceId.value === '') {
      selectedAudienceId.value = templates[0].template_id;
      [selectedAudience.value] = templates;
    }
  }
  isShowSelectList.value = true;
};

const deleteTemplate = async (item: AudienceTemplate) => {
  const confirm = DialogPlugin.confirm({
    theme: 'danger',
    header: 'Delete audience',
    body: 'Please confirm to delete this audience?',
    cancelBtn: 'Cancel',
    confirmBtn: 'Confirm',
    zIndex: 8000,
    onConfirm: () => {
      confirmCallback();
      confirm.hide();
    },
  });
  const confirmCallback = async () => {
    const isCurrentTemplateDelete = item.template_id === selectedAudienceId.value;
    await treeStore.deleteTemplate([item.template_id]);
    if (isCurrentTemplateDelete) {
      selectedAudienceId.value = '';
      selectedAudience.value.template_id = '';
    }
    await getAudienceTemplateList();
    selectAudienceChange();
    if (audienceTemplateList.value.length === 0) {
      isNewAudience.value = true;
      switchAudienceType();
    }
  };
};

onMounted(async () => {
  // 获取到模板列表
  getAudienceTemplateList();
});

</script>
<style lang="scss" scoped>

</style>
