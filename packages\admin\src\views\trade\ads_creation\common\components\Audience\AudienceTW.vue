<!--
 * @Date: 2023-05-19 14:24:38
 * @LastEditors: maclerylin
 * @LastEditTime: 2023-05-29 20:39:44
-->
<template>
  <div>
    <t-form-item v-if="!isReviewing" label="Audience">
      <t-radio-group
        v-model="isNewAudience"
        :disabled="audienceTemplateList.length === 0"
        @change="() => switchAudienceType()"
      >
        <t-radio :value="true">
          New Audience
        </t-radio>
        <t-radio :value="false">
          Saved Audience
        </t-radio>
      </t-radio-group>
    </t-form-item>
    <t-form-item v-if="!isNewAudience && !isReviewing" label="">
      <t-select
        v-if="isShowSelectList"
        v-model="selectedAudienceId"
        placeholder="Search or select audience"
        class="max-w-[688px]"
        clearable
        @change="selectAudienceChange"
      >
        <t-option
          v-for="item in audienceTemplateList"
          :key="item.template_id + item.template_name"
          :label="item.template_name"
          :value="item.template_id"
        >
          <div class="flex items-center">
            <span>{{ item.template_name }}</span>
            <span title="delete" @click.stop="() => deleteTemplate(item)">
              <Icon name="delete" class="ml-[10px] cursor-pointer" />
            </span>
          </div>
        </t-option>
      </t-select>
    </t-form-item>
    <t-form-item v-if="!isNewAudience && !isReviewing" label="Name">
      <span>
        {{ selectedAudience.template_name }}
      </span>
    </t-form-item>
    <t-form-item
      label="Gender" name="gender" style="clear: both;"
    >
      <span v-if="isReviewing">
        {{ getOptionsText(GENDEROPTIONS, [datas.demographics.gender]) }}
      </span>
      <t-radio-group
        v-else
        v-model="datas.demographics.gender"
        :options="GENDEROPTIONS"
        @change="agesOrGenderChange"
      />
    </t-form-item>
    <t-form-item label="Age" name="age">
      <div class="flex flex-col mt-[8px]">
        <span class="mb-[24px]">
          <span v-if="isReviewing">
            {{ datas.demographics.age_range ? 'Age range' : 'All' }}
          </span>
          <t-radio-group
            v-else
            v-model="datas.demographics.age_range"
            :options="AGERANGEOPTIONS"
            class="max-w-[688px]"
            @change="agesOrGenderChange"
          />
        </span>
        <div v-if="datas.demographics.age_range">
          <span v-if="isReviewing">
            {{ getOptionsText(MINAGEOPTIONS, [Number(datas.demographics.min_age)]) || '' }} -
            {{ getOptionsText(MAXAGEOPTIONS, [Number(datas.demographics.max_age)]) }}
          </span>
          <div v-else class="flex items-center">
            <t-select
              v-model="datas.demographics.min_age"
              :options="computedMinAgeOptions"
              placeholder=""
              class="max-w-[688px] mr-[8px]"
              @change="agesOrGenderChange"
            />
            <span class="mr-[8px]">-</span>
            <t-select
              v-model="datas.demographics.max_age"
              :options="computedMaxAgeOptions"
              placeholder=""
              class="max-w-[688px]"
              @change="agesOrGenderChange"
            />
          </div>
        </div>
      </div>
    </t-form-item>
    <t-form-item
      label="Locations(optional)" name="module_targeting.demographics.location_ids"
      style="clear: both;"
    >
      <span v-if="isReviewing">
        {{ getOptionsText(computedOptions.locations, datas.demographics.locations) }}
      </span>
      <Country
        v-else
        v-model="datas.demographics.locations"
        :options="computedOptions.locations || []"
        @change="locationsChange"
      />
    </t-form-item>
    <t-form-item
      label="Language(optional)" name="language" style="clear: both;"
    >
      <span v-if="isReviewing">
        {{ getOptionsText(computedOptions.languageOptions, datas.demographics.languages) || 'All' }}
      </span>
      <t-select
        v-else
        v-model="datas.demographics.languages"
        clearable
        multiple
        filterable
        :min-collapsed-num="4"
        placeholder="Search or select a location"
        :options="computedOptions.languageOptions"
        class="max-w-[688px] inline-block"
        @change="updateModelValue"
      >
        <template #prefixIcon>
          <div class="flex justify-center pl-[8px]">
            <icon name="search" class="text-lg text-black-secondary" />
            <span
              class="inline-block pr-[7px] text-black-secondary w-[1px] h-[16px]"
              style="border-right: 1px solid var(--aix-border-color-black-disabled);"
            />
          </div>
        </template>
      </t-select>
    </t-form-item>
    <t-form-item label="Operation System" name="os">
      <span class="mr-[15px]">
        {{ currentOS }}:
      </span>
      <span v-if="isReviewing">
        {{ getOptionsText(currentOS === 'Android' ? androidOsVersionList : iosOsVersionList,
                          [datas.devices.version]) || 'All' }}
      </span>
      <t-select
        v-else
        v-model="datas.devices.version"
        :options="currentOS === 'Android' ? androidOsVersionList : iosOsVersionList"
        :tips="osVersionTip"
        class="max-w-[646px]"
        @change="updateModelValue"
      />
    </t-form-item>
    <t-form-item label="Connection">
      <span v-if="isReviewing">
        {{ datas.devices.wifi === '1' ? 'Wifi' : 'Any' }}
      </span>
      <t-radio-group
        v-else
        v-model="datas.devices.wifi"
        :options="[{ label: 'Any', value: '' }, { label: 'Wifi', value: '1' }]"
      />
    </t-form-item>
    <t-form-item label="Target people(optional)">
      <div class="flex flex-col">
        <t-checkbox
          :model-value="!!datas.devices.active_type"
          :disabled="isReviewing"
          label="Target people who first used Twitter on a new device or carrier"
          @change="(val: boolean) => { datas.devices.active_type=Number(val); updateModelValue(); }"
        />
        <div v-if="datas.devices.active_type" class="mt-[10px] flex">
          <span v-if="isReviewing">
            {{ datas.devices.active_type === 1 ? 'within last' : 'more than' }}
          </span>
          <t-select
            v-else
            v-model="datas.devices.active_type"
            :options="[{ label: 'within last', value: 1 }, { label: 'more than', value: 2 }]"
            class="mr-[15px] w-[325px]"
            @change="() => updateModelValue()"
          />
          <span v-if="isReviewing" class="ml-[8px]">
            {{ getOptionsText(MONTHSOPTIONS, [datas.devices.months]) }}
          </span>
          <t-select
            v-else
            v-model="datas.devices.months"
            :options="MONTHSOPTIONS"
            class="w-[325px]"
            @change="() => updateModelValue()"
          />
        </div>
      </div>
    </t-form-item>
    <Collpse :header-title="{content: 'Custom Audience\n(optional)', style: 'width: 118px',}" :is-vertical="false">
      <t-form-item label-align="top">
        <div class="mb-[24px]">
          <t-form-item label="Include" label-align="top" style="margin-right: initial">
            <span v-if="isReviewing">
              {{ getOptionsText(initAudienceList, datas.audiences.include) }}
            </span>
            <CollapseSelect
              v-else
              v-model="datas.audiences.include"
              :min-collapsed-num="2"
              :multiple="true"
              :clearable="true"
              :options="includeAudienceList"
              placeholder="select one or more categories"
              class="min-w-[400px] inline-block"
              @change="() => updateModelValue()"
            />
          </t-form-item>
          <t-form-item label="Exclude" label-align="top">
            <span v-if="isReviewing">
              {{ getOptionsText(initAudienceList, datas.audiences.exclude) }}
            </span>
            <CollapseSelect
              v-else
              v-model="datas.audiences.exclude"
              :min-collapsed-num="2"
              :multiple="true"
              :clearable="true"
              :options="excludeAudienceList"
              placeholder="select one or more categories"
              class="min-w-[400px] inline-block"
              @change="() => updateModelValue()"
            />
          </t-form-item>
          <t-checkbox
            v-model="datas.audiences.look_alike"
            :disabled="isReviewing"
            label="Include look-alikes of your selected custom audiences"
            class="mt-[16px]"
          />
        </div>
      </t-form-item>
    </Collpse>
    <Collpse :header-title="{content: 'Keywords\n(optional)', style: 'width: 118px',}" :is-vertical="false">
      <t-form-item label-align="top">
        <div class="mb-[24px]">
          <t-form-item label="Include" label-align="top">
            <span v-if="isReviewing">
              {{ datas.targetingFeatures.key_words_include }}
            </span>
            <t-textarea
              v-else
              v-model="datas.targetingFeatures.key_words_include"
              :autosize="{minRows: 5, maxRows: 10}"
              placeholder="Separated by commas or newlines"
              style="white-space: pre-line;"
              class="max-w-[688px] w-[406px] min-h-[80px]"
              @change="() => updateModelValue()"
            />
          </t-form-item>
          <t-form-item label="Exclude" label-align="top">
            <span v-if="isReviewing">
              {{ datas.targetingFeatures.key_words_exclude }}
            </span>
            <t-textarea
              v-else
              v-model="datas.targetingFeatures.key_words_exclude"
              :autosize="{minRows: 5, maxRows: 10}"
              placeholder="Separated by commas or newlines"
              style="white-space: pre-line;"
              class="max-w-[688px] w-[406px] min-h-[80px]"
              @change="() => updateModelValue()"
            />
          </t-form-item>
        </div>
      </t-form-item>
    </Collpse>
    <t-form-item label="Follower look-alikes(optional)">
      <span v-if="isReviewing">
        {{ getOptionsText(followerOptions, datas.targetingFeatures.follower) }}
      </span>
      <t-select
        v-else
        v-model="datas.targetingFeatures.follower"
        multiple
        filterable
        clearable
        placeholder="Please enter a keyword"
        reserve-keyword
        :loading="loading"
        class="max-w-[688px]"
        @search="(values: string) => {handleSearch(values)}"
        @change="followerChange"
      >
        <template #prefixIcon>
          <div class="flex justify-center pl-[8px]">
            <icon name="search" class="text-lg text-black-secondary" />
            <span
              class="inline-block pr-[7px] text-black-secondary w-[1px] h-[16px]"
              style="border-right: 1px solid var(--aix-border-color-black-disabled);"
            />
          </div>
        </template>
        <t-option
          v-for="item in followerOptions"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        >
          <div class="flex items-center">
            <img :src="item!.profile_image_url" alt="" class="m-[4px] w-[30px] h-[30px] rounded-large">
            <span>{{ item.label }}</span>
          </div>
        </t-option>
      </t-select>
    </t-form-item>
    <t-form-item label="Interests(optional)">
      <span v-if="isReviewing" style="white-space: break-spaces;">
        {{ getOptionsText(initInterestList, datas.targetingFeatures.interests, 'label', ',   ') }}
      </span>
      <CollapseSelect
        v-else
        v-model="datas.targetingFeatures.interests"
        :multiple="true"
        :min-collapsed-num="4"
        :clearable="true"
        :options="interestsList"
        placeholder="Select interests"
        @change="() => updateModelValue()"
      />
    </t-form-item>

    <t-form-item v-if="!isReviewing">
      <t-button
        v-if="isNewAudience"
        theme="primary"
        @click="isShowSaveAudienceDialog = true;"
      >
        Save Audience
      </t-button>
      <t-button
        v-else
        theme="primary"
        @click="isShowSaveAudienceDialog = true;"
      >
        Update Audience
      </t-button>
    </t-form-item>
    <t-dialog
      header="Save audience"
      :visible="isShowSaveAudienceDialog"
      :on-close="() => { isShowSaveAudienceDialog = false; }"
    >
      <template #body>
        <div class="h-[50px]">
          <div class="t-form__controls " :class="isDulplicationName ? 't-is-error' : ''">
            <div class="t-form__controls-content">
              <t-input
                v-if="isNewAudience"
                v-model="newSavedAudience.template_name"
                placeholder="name this audience"
                @change="isDulplicationOfNameFun"
              />
              <t-input
                v-else
                v-model="selectedAudience.template_name"
                placeholder="name this audience"
                @change="isDulplicationOfNameFun"
              />
            </div>
            <div v-if="isDulplicationName" class="t-input__extra">Name already exists！</div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="flex justify-end">
          <t-button
            theme="primary"
            :disabled="isDulplicationName || (isNewAudience && !newSavedAudience.template_name)
              || (!isNewAudience && !selectedAudience.template_name)"
            :loading="isLoading"
            @click="() => { isNewAudience ? saveAudience() : updateAudience() }"
          >
            Confirm
          </t-button>
        </div>
      </template>
    </t-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, defineComponent, toRefs, computed, watch, onMounted, nextTick, PropType } from 'vue';
import { cloneDeep, debounce, set, uniq } from 'lodash-es';
import { Icon } from 'tdesign-icons-vue-next';
import { useTreeListDataStore } from '@/store/trade/template.store';
import Country from '../Locations.vue';
import Language from 'common/components/LanguageSelect';
import MultiFilterSelect from '../MultiFilterSelect.vue';
import CollapseSelect from '../CollapseSelect.vue';
import Collpse from '../Collapse';
import { MessagePlugin, DialogPlugin, TdOptionProps } from 'tdesign-vue-next';
import {  getOptionsText, getLocationsText, getOptionsValues } from '../../template/utils-common';
import { EBusEmit, EBusOn } from '../../template/event';
import { initAdgroupModuleTargetingData } from 'common/service/td/twitter/utils';
import type { TargetingTemplateData, DemoGraphics, Devies, Audiences, TargetingFeature } from '../../../twitter/type';
import { GENDEROPTIONS, AGERANGEOPTIONS, MINAGEOPTIONS, MAXAGEOPTIONS, MONTHSOPTIONS } from '../../../twitter/adgroup/const';
import { storeToRefs } from 'pinia';
import { useTWTreeListData } from '@/store/trade/twitter/twitter.store';
import { useEnv } from 'common/compose/env';

const { isProduction } = useEnv();

type AudienceTemplate = {
  template_id: string,
  template_name: string,
  targeting: any,
};
type FollowerOptionItem = {
  label: string,
  value: string,
  id: string,
  name: string,
  profile_image_url: string,
  screen_name: string,
};

defineComponent({
  Country,
  Icon,
  Language,
  Collpse,
  MultiFilterSelect,
  CollapseSelect,
});

const treeStore = useTreeListDataStore();
const twStore = useTWTreeListData();

const { current } = storeToRefs(treeStore);

const defaultAudienceData = initAdgroupModuleTargetingData();

const props = defineProps({
  isReviewing: {
    type: Boolean,
    default: false,
  },
  options: {
    type: Object,
    default: () => {},
  },
  demographics: {
    type: Object as PropType<DemoGraphics>,
    default: () => {},
  },
  devices: {
    type: Object as PropType<Devies>,
    default: () => {},
  },
  audiences: {
    type: Object as PropType<Audiences>,
    default: () => {},
  },
  targetingFeatures: {
    type: Object as PropType<TargetingFeature>,
    default: () => {},
  },
  customAudienceOptions: {
    type: Array,
    default: () => [{
      label: 'text', value: '111',
    }],
  },
});
const emits = defineEmits(['update:demographics', 'update:devices', 'update:audiences', 'update:targetingFeatures']);
EBusOn((evt: unknown, data: any) => {
  const { key, val } = data;
  if (evt === 'AudienceValue') {
    set(datas.value, key, val);
  }
});
const isNewAudience = ref(true);
const { options, isReviewing } = toRefs(props);
const isShowSelectList = ref(true);
// 下面是一些常量options

const genderMap: { [key: string]: string } = {
  '': 'all',
  FEMALE: 'f',
  MALE: 'm',
};


// 直接值复制，不对外绑定。
const datas = ref<TargetingTemplateData>(defaultAudienceData);

const getDefaultTemplateName = (currentTemplateName?: string) => {
  const { campaignNode } = treeStore.current;
  const rejion = campaignNode.data.name.split('-')[1] || 'gl';
  const { min_age: minAge, max_age: maxAge, age_range: ageRange, gender = 'all' } = datas.value?.demographics || {};
  let lastffix = '';
  if (currentTemplateName) {
    const temp = currentTemplateName.split('-');
    lastffix = temp.slice(temp[2] === 'all' ? 3 : 4)
      .join('_');
  }
  let ageText = 'all';
  if (ageRange) {
    ageText = `${minAge}_${maxAge === 0 ? 'and_up' : maxAge}`;
  }
  return `${rejion}-${ageText}-${genderMap[gender]}${lastffix ? `-${lastffix}` : ''}`;
};
const newSavedAudience = ref<AudienceTemplate>({
  template_id: '',
  template_name: getDefaultTemplateName(),
  targeting: datas.value,
});
const agesOrGenderChange = () => {
  EBusEmit('ageOrGenderChangeEvt', {
    age_min: datas.value.demographics.min_age,
    age_max: datas.value.demographics.max_age,
    age_range: datas.value.demographics.age_range,
  });
  if (isNewAudience.value) {
    newSavedAudience.value.template_name = getDefaultTemplateName(newSavedAudience.value.template_name);
  } else {
    selectedAudience.value.template_name = getDefaultTemplateName(selectedAudience.value.template_name);
  }
  updateModelValue();
};

const locationsChange = function () {
  const result = getLocationsText(computedOptions.value.locations, datas.value.demographics.locations);
  const {
    codeResult,
  } = result as any;
  let {
    locationTexts,
  } = result as any;
  let isGl = true;
  computedOptions.value.locations.some((item: TdOptionProps) => {
    if (!locationTexts.includes(item.value)) {
      isGl = false;
      return true;
    }
    return false;
  });
  if (isGl) {
    locationTexts = 'gl';
  }
  const { codeResult: languageTexts } = getOptionsValues(
    computedOptions.value.countryLanguages,
    codeResult, computedOptions.value.languageOptions,
  );
  datas.value.demographics.languages = uniq(languageTexts.concat(datas.value.demographics.languages));
  EBusEmit('locationsEvt', {
    locationTexts,
    languageTexts,
  });
  nextTick(() => {
    agesOrGenderChange();
  });
  updateModelValue();
};

const audienceTemplateList = ref<AudienceTemplate[]>([]);
const isShowSaveAudienceDialog = ref(false);
const isLoading = ref(false);
const isDulplicationName = ref(false);
const selectedAudienceId = ref('');
const osVersionTip = ref('');
const selectedAudience = ref<AudienceTemplate>({
  template_id: '',
  template_name: getDefaultTemplateName(),
  targeting: {},
});

const getExistFollowerInfoList = async (ids: string[]) => {
  if (!ids.length) {
    followerOptions.value = [];
    return;
  }
  const result: FollowerOptionItem[] = await twStore.getUsers(ids);
  followerOptions.value = result || [];
};
const currentOS = computed(() => (current.value.adgroupNode?.data?.android_app_store_identifier ? 'Android' : 'iOS'));

const checkOsVersion = () => {
  const osOptions = currentOS.value === 'Android' ? androidOsVersionList.value : iosOsVersionList.value;
  if (datas.value.devices.version && !osOptions.find(item => item.value === datas.value.devices.version)) {
    const fromOs = currentOS.value === 'Android' ? 'iOS' : 'Android';
    const toOs = currentOS.value === 'Android' ? 'Android' : 'iOS';
    osVersionTip.value = `The Operating System set in the selected audience (${fromOs}) is automatically changed to your APP platform (${toOs}).`;
    datas.value.devices.version = '';
  } else {
    osVersionTip.value = '';
  }
};

watch(() => currentOS.value, () => {
  datas.value.devices.version = '';
});

const computedMinAgeOptions = computed(() => {
  const maxAge = datas.value.demographics.max_age === 0 ? Number.POSITIVE_INFINITY : datas.value.demographics.max_age;
  return MINAGEOPTIONS.map((item: { value: Number}) => ({
    ...item,
    disabled: item.value > maxAge,
  }));
});
const computedMaxAgeOptions = computed(() => MAXAGEOPTIONS.map((item: { value: Number}) => {
  const value = item.value === 0 ? Number.POSITIVE_INFINITY : item.value;
  return {
    ...item,
    disabled: value < datas.value.demographics.min_age,
  };
}));

const selectAudienceChange = () => {
  if (!selectedAudienceId.value) {
    return;
  }
  selectedAudience.value = audienceTemplateList.value
    .find(item => item.template_id === selectedAudienceId.value) || {
    template_id: '',
    template_name: getDefaultTemplateName(),
    targeting: {},
  };
  if (!isNewAudience.value) {
    datas.value = selectedAudience.value.targeting;
  }
  locationsChange();
  checkOsVersion();
  getExistFollowerInfoList(datas.value.targetingFeatures.follower);
  updateModelValue();
};


const isDulplicationOfNameFun = () => {
  if (isNewAudience.value) {
    isDulplicationName.value = !!audienceTemplateList.value
      .find(item => item.template_name === newSavedAudience.value.template_name);
  } else {
    isDulplicationName.value = !!audienceTemplateList.value
      .find(item => (item.template_name === selectedAudience.value.template_name
        && item.template_id !== selectedAudienceId.value));
  }
};

const switchAudienceType = () => {
  if (isNewAudience.value) {
    datas.value = newSavedAudience.value.targeting;
    newSavedAudience.value.template_name = getDefaultTemplateName();
  } else {
    datas.value = selectedAudience.value?.targeting || cloneDeep(defaultAudienceData);
  }
  checkOsVersion();
  locationsChange();
};
const updateModelValue = debounce(() => {
  emits('update:audiences', datas.value.audiences);
  emits('update:demographics', datas.value.demographics);
  emits('update:devices', {
    ...datas.value.devices,
    platform: currentOS.value === 'Android' ? '1' : '0',
  });
  emits('update:targetingFeatures', datas.value.targetingFeatures);
  nextTick(() => {
    isFollowerEventChange = false;
  });

  EBusEmit('languagesEvt', {
    languages: datas.value.demographics.languages,
  });
}, 200);

watch(() => [props.demographics, props.devices, props.audiences, props.targetingFeatures], () => {
  datas.value = {
    demographics: cloneDeep(props.demographics),
    devices: cloneDeep(props.devices),
    audiences: cloneDeep(props.audiences),
    targetingFeatures: cloneDeep(props.targetingFeatures),
  };
}, {
  immediate: true,
  deep: true,
});

const computedOptions = computed(() => options.value || {});


// 获取hashTagRecommendsOPtions
const followerOptions = ref<FollowerOptionItem[]>([]);
let isFollowerEventChange = false;
const loading = ref(false);
const handleSearch = async (values: string) => {
  if (!values || values.length === 0) {
    return false;
  }
  loading.value = true;
  const initOptions: FollowerOptionItem[] = [];
  datas.value.targetingFeatures.follower.forEach((followerStr: string) => {
    const findItem = followerOptions.value.find(item => item.value === followerStr);
    if (findItem) {
      initOptions.push(findItem);
    }
  });
  followerOptions.value = await twStore.getFollowers(values);
  followerOptions.value.push(...initOptions);
  loading.value = false;
};
// 处理初始化数据
watch(() => datas.value.targetingFeatures?.follower, () => {
  if (datas.value.targetingFeatures?.follower?.length > 0 && !isFollowerEventChange) {
    getExistFollowerInfoList(datas.value.targetingFeatures.follower);
  }
}, {
  immediate: true,
});
const followerChange = () => {
  isFollowerEventChange = true;
  updateModelValue();
};

const updateAudience = async () => {
  if (isDulplicationName.value) {
    return false;
  }
  isLoading.value = true;
  const result = await treeStore.addOrUpdateTemplate({
    template_id: selectedAudience.value.template_id,
    template_name: selectedAudience.value.template_name,
    template_content: JSON.stringify(datas.value),
  });
  const { template: { template_id: templateId } } = result as any;
  selectedAudienceId.value = templateId;
  selectedAudience.value.template_id = templateId;
  await getAudienceTemplateList();
  // 关闭命名弹窗
  isShowSaveAudienceDialog.value = false;
  isLoading.value = false;
  MessagePlugin.success('Update Audience success!');
};
const saveAudience = async () => {
  if (isDulplicationName.value) {
    return false;
  }
  if (isProduction) {
    newSavedAudience.value = {
      template_id: '',
      template_name: '',
      targeting: cloneDeep(defaultAudienceData),
    };
    isNewAudience.value = false;
    MessagePlugin.success('save Audience success!');
    isShowSaveAudienceDialog.value = false;
  } else {
    isLoading.value = true;
    const result = await treeStore.addOrUpdateTemplate({
      template_id: newSavedAudience.value.template_id,
      template_name: newSavedAudience.value.template_name,
      template_content: JSON.stringify(datas.value),
    });
    const { template } = result as any;
    // 保存后切换到已保存的audience列表， 并重置new audiece页面为空
    isNewAudience.value = false;
    // datas.value = newSavedAudience.value.targeting;

    await getAudienceTemplateList();
    selectedAudienceId.value = template.template_id;
    // 获取到当前选中的对象值
    selectAudienceChange();
    newSavedAudience.value = {
      template_id: '',
      template_name: '',
      targeting: cloneDeep(defaultAudienceData),
    };
    MessagePlugin.success('save Audience success!');
    // 关闭命名弹窗
    isShowSaveAudienceDialog.value = false;
    isLoading.value = false;
  }
};

const getAudienceTemplateList = async () => {
  isShowSelectList.value = false;
  const resultList = await treeStore.getTemplateList();
  let { templates = [] } = resultList as any;
  templates = templates.map((item: { template_content: string}) => ({
    ...item,
    targeting: JSON.parse(item.template_content),
  }));
  audienceTemplateList.value = templates;
  // 如果列表有值，并且当前选中是一个空值，则默认选择第一个
  if (audienceTemplateList.value.length > 0) {
    if (selectedAudienceId.value === '') {
      selectedAudienceId.value = templates[0].template_id;
      [selectedAudience.value] = templates;
    }
  }
  isShowSelectList.value = true;
};

const deleteTemplate = async (item: AudienceTemplate) => {
  const confirm = DialogPlugin.confirm({
    theme: 'danger',
    header: 'Delete audience',
    body: 'Please confirm to delete this audience?',
    cancelBtn: 'Cancel',
    confirmBtn: 'Confirm',
    zIndex: 8000,
    onConfirm: () => {
      confirmCallback();
      confirm.hide();
    },
  });
  const confirmCallback = async () => {
    const isCurrentTemplateDelete = item.template_id === selectedAudienceId.value;
    await treeStore.deleteTemplate([item.template_id]);
    if (isCurrentTemplateDelete) {
      selectedAudienceId.value = '';
      selectedAudience.value.template_id = '';
    }
    await getAudienceTemplateList();
    selectAudienceChange();
    if (audienceTemplateList.value.length === 0) {
      isNewAudience.value = true;
      switchAudienceType();
    }
  };
};

const androidOsVersionList = ref<TdOptionProps[]>([]);
const iosOsVersionList = ref<TdOptionProps[]>([]);
const customeAudienceList = ref<{group: string, children: TdOptionProps[]}[]>([]);
const interestsList = ref<{group: string, children: TdOptionProps[]}[]>([]);
const initInterestList = ref<TdOptionProps[]>([]);
const initAudienceList = ref<TdOptionProps[]>([]);


const getosVersion = async () => {
  [androidOsVersionList.value, iosOsVersionList.value] = await twStore.getOsVersionList();
};

const getCustomAudience = async () => {
  const { initAudience, audiences } = await twStore.getCustomAudienceList();
  customeAudienceList.value = audiences;
  initAudienceList.value = initAudience;
};
const getInterestList = async () => {
  const { initInterests, interests } = await twStore.getInterests();
  interestsList.value = interests;
  initInterestList.value = initInterests;
};

const includeAudienceList = computed(() => cloneDeep(customeAudienceList.value).map((firstLevelItem) => {
  if (firstLevelItem.children) {
    firstLevelItem.children.forEach((secondLevelItem) => {
      if (datas.value.audiences.exclude.includes(`${secondLevelItem.value}` || '')) {
        // eslint-disable-next-line no-param-reassign
        secondLevelItem.disabled = true;
      }
    });
  }
  return firstLevelItem;
}));
const excludeAudienceList = computed(() => cloneDeep(customeAudienceList.value).map((firstLevelItem) => {
  if (firstLevelItem.children) {
    firstLevelItem.children.forEach((secondLevelItem) => {
      if (datas.value.audiences.include.includes(`${secondLevelItem.value}` || '')) {
        // eslint-disable-next-line no-param-reassign
        secondLevelItem.disabled = true;
      }
    });
  }
  return firstLevelItem;
}));

onMounted(async () => {
  // 获取到模板列表
  getAudienceTemplateList();
  getosVersion();
  getCustomAudience();
  getInterestList();
});

</script>
<style lang="scss" scoped>

</style>
