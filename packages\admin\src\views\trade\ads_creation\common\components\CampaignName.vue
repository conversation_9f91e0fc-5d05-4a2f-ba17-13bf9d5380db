<template>
  <div class="w-[100%]">
    <t-form-item label="Campaign name">
      <div class="flex items-center cursor-pointer campaign-name-link" @click="isShowEditForm=!isShowEditForm">
        <Icon
          v-if="(!props.published || isEditStatus) && !props.publishing && !props.isReviewing"
          :name="isShowEditForm ? 'chevron-up' : 'chevron-down'"
          class="mr-[8px]"
        />
        <span class="text-brand">{{ props.modelValue }}</span>
        <span
          v-if="props.published && !props.isReviewing"
          class="edit-btn"
          @click.stop="editBtnClick"
        >
          <Icon
            name="edit" class="ml-[8px] cursor-pointer"
          />
        </span>
      </div>
    </t-form-item>
    <t-form-item
      v-if="!isStanderFormat && isEditStatus && isShowEditForm && !props.publishing && !props.isReviewing"
      label=""
      class="unstander-value-form"
    >
      <t-input v-model="unStanderValue" class="max-w-[688px]" @change="inputChange" />
    </t-form-item>
    <div
      v-if="isShowEditForm && isEditStatus && !props.publishing && isStanderFormat && !props.isReviewing"
      class="campaign-name-form"
    >
      <div class="campaign-name-item channel-item">
        <div class="campaign-name-val val-text">
          <span class="mr-[8px]"> {{ info.preffix }} </span>
          <t-input
            v-model="info.media"
            type="text"
            :disabled="props.publishing"
            :status="status.media"
            :tips="status.media ? 'media required' : ''"
            class="w-[75%]"
            @change="() => inputChange()"
          />
        </div>
        <div class="campaign-name-label" :class="isStatusError ? 'mt-[15px]' : ''">
          {{ t('channel') }}
        </div>
      </div>
      <div class="divider">
        -
      </div>
      <div class="campaign-name-item region-item">
        <div class="campaign-name-val">
          <t-input
            v-model="info.region"
            type="text"
            :disabled="props.publishing"
            :status="status.region"
            :tips="status.region ? 'region required' : ''"
            @change="() => inputChange()"
          />
        </div>
        <div class="campaign-name-label" :class="isStatusError ? 'mt-[15px]' : ''">
          {{ t('region') }}
        </div>
      </div>
      <div class="divider">
        -
      </div>
      <div class="campaign-name-item os-item">
        <div class="campaign-name-val">
          <t-select
            v-model="info.os"
            :options="platformOptions"
            :disabled="props.publishing"
            @change="() => inputChange()"
          />
        </div>
        <div class="campaign-name-label" :class="isStatusError ? 'mt-[15px]' : ''">
          {{ t('os') }}
        </div>
      </div>
      <div class="divider">
        -
      </div>
      <div class="campaign-name-item campaign-date-item">
        <div class="campaign-name-val">
          <t-date-picker
            :value="datePickerDate"
            format="YYYY-MM-DD"
            value-type="YYYY-MM-DD"
            :disabled="props.publishing"
            :disable-date="{before: dayjs(new Date(new Date().getTime() - 24 * 3600 * 1000))}"
            class="w-[100%]"
            @change="(val: string) => onDateChange(val)"
          />
        </div>
        <div class="campaign-name-label" :class="isStatusError ? 'mt-[15px]' : ''">
          {{ t('date') }}
        </div>
      </div>
      <div class="divider">
        -
      </div>
      <div class="campaign-name-item campaign-goal-item">
        <div class="campaign-name-val val-text">
          <t-input
            v-model="info.goal"
            type="text"
            :disabled="props.publishing"
            :status="status.goal"
            :tips="status.goal ? 'goal required' : ''"
            @change="() => inputChange()"
          />
        </div>
        <div class="campaign-name-label" :class="isStatusError ? 'mt-[15px]' : ''">
          {{ t('campaignGoal') }}
        </div>
      </div>
      <div class="divider">
        -
      </div>
      <div
        :class="{
          'campaign-name-item': true,
        }"
      >
        <div class="campaign-name-val">
          <t-input
            v-model="info.custom"
            type="text"
            class="t-is-error"
            :style="{width: '80px'}"
            :disabled="props.publishing"
            :status="status.custom"
            :tips="status.custom ? 'custom required' : ''"
            @change="() => inputChange()"
          />
        </div>
        <div class="campaign-name-label" :class="isStatusError ? 'mt-[15px]' : ''">
          {{ t('custom') }}
        </div>
      </div>
      <div class="divider">
        -
      </div>
      <div class="campaign-name-item spend-type-item">
        <div class="campaign-name-val">
          <t-select
            v-model="info.spendType"
            appearance="button"
            match-button-width
            :disabled="props.publishing"
            :options="SPEND_TYPE"
            @change="() => inputChange()"
          />
        </div>
        <div class="campaign-name-label" :class="isStatusError ? 'mt-[15px]' : ''">
          {{ t('spendType') }}
        </div>
      </div>
      <div class="divider">
        -
      </div>
      <div class="campaign-name-item region-type-item">
        <div class="campaign-name-val">
          <t-input
            v-model="info.regionType"
            type="text"
            :disabled="props.publishing"
            :status="status.regionType"
            :tips="status.regionType ? 'region type required' : ''"
            @change="inputChange()"
          />
        </div>
        <div class="campaign-name-label" :class="isStatusError ? 'mt-[15px]' : ''">
          {{ t('projectRegionType') }}
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, toRefs, watch, reactive, computed, defineComponent } from 'vue';
import { I18N_TD_FORM } from 'common/const/i18n';
import { useI18n } from 'common/compose/i18n';
import dayjs from 'dayjs';
import { CampaignNameSeg } from '../template/config';
import { Icon } from 'tdesign-icons-vue-next';

const { t } = useI18n([I18N_TD_FORM]);

defineComponent({
  Icon,
});

const props = defineProps({
  publishing: {
    type: Boolean,
    default: false,
  },
  published: {
    type: Boolean,
    default: false,
  },
  objective: {
    type: [Number, String],
    default: 2,
  },
  modelValue: {
    type: String,
    default: '',
  },
  isReviewing: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:modelValue']);

const info = ref<{[key: string]: string}>({
  media: '',
  region: '',
  goal: '',
  custom: '',
  os: '',
  spendType: '',
  date: dayjs().format('YYMMDD'),
  regionType: '',
});

const isShowEditForm = ref(!props.published);
const isEditStatus = ref(!props.published);
const isStanderFormat = ref(true);
const unStanderValue = ref('');

watch(() => props.published, () => {
  isShowEditForm.value = !props.published;
  isEditStatus.value = !props.published;
}, {
  immediate: true,
});

const status = reactive<{[key: string]: string}>({
  regionType: '',
  region: '',
  custom: '',
});

const isStatusError = computed(() => status.regionType || status.region || status.cutom || status.media || status.goal);
let isChangeEvent = false;
const inputChange = () => {
  const result = isStanderFormat.value ? infoToString() : unStanderValue.value;
  isChangeEvent = true;
  emit('update:modelValue', result);
  // key && validator(key);
};

const SPEND_TYPE = ref([
  { value: 'reattribution', label: 'reattribution' },
  { value: 'newinstall', label: 'newinstall' },
  { value: 'preregister', label: 'preregister' },
  { value: 'others', label: 'others' },
]);

const platformOptions = ref([
  { value: 'all', label: 'all' },
  { value: 'and', label: 'and' },
  { value: 'ios', label: 'ios' },
]);

const changeInfo = () => {
  const { modelValue } = toRefs(props);
  const names = modelValue.value.split('-');
  const [preffix, media] = names[CampaignNameSeg.media].split('_');
  // 说明有可能是渠道自定义的
  if (!isChangeEvent) {
    if (names.length !== 8 || !preffix || !media) {
      isStanderFormat.value = false;
    } else {
      isStanderFormat.value = true;
    }
  }

  info.value = {
    preffix: `${preffix}_`,
    media: media || '',
    region: names[CampaignNameSeg.region] || '',
    os: names[CampaignNameSeg.os] || '',
    date: names[CampaignNameSeg.date] || '',
    goal: names[CampaignNameSeg.goal] || '',
    custom: names[CampaignNameSeg.custom] || '',
    spendType: names[CampaignNameSeg.spendType] || '',
    regionType: names[CampaignNameSeg.regionType] || '',
  };
  // validator('region');
  // validator('custom');
  // validator('regionType');
  // validator('goal');
  // validator('media');
};
// 传入datePicker的值
const datePickerDate = computed(() => {
  const { date } = info.value; // eg: 230309
  return dayjs(`20${date}`).format('YYYY-MM-DD');
});

const onDateChange = (val: string) => {
  const date = dayjs(val).format('YYYYMMDD');
  info.value = {
    ...info.value,
    date: date.slice(2),
  };
  inputChange();
};
const editBtnClick = () => {
  isEditStatus.value = !isEditStatus.value;
  isShowEditForm.value = true;
};

// 将info数据结构转为字符串
const infoToString = () => {
  const val = info.value;
  const getValue = (str: string) => (`-${str}`);
  const result = `${val.preffix}${val.media}${getValue(val.region)}${getValue(val.os)}${getValue(val.date)}${getValue(val.goal)}${getValue(val.custom)}${getValue(val.spendType)}${getValue(val.regionType)}`;
  const resultSet = result.split('-').filter(item => item);
  if (resultSet.length === 1) {
    return resultSet[0];
  }
  return result;
};

watch(() => props.modelValue, () => {
  unStanderValue.value = props.modelValue;
  changeInfo();
  if (!isChangeEvent) {
    isEditStatus.value = !props.published;
  } else {
    isChangeEvent = false;
  }
}, {
  immediate: true,
});

watch(() => props.objective, () => {
  // facebook convertion类型，GG的website类型
  if ([2, 'CONVERSIONS'].includes(props.objective)) {
    platformOptions.value.push({ value: 'pc', label: 'pc' });
  } else {
    platformOptions.value = platformOptions.value.filter(item => item.value !== 'pc');
  }
}, {
  immediate: true,
});

</script>
<style lang="scss" scoped>
  .campaign-name-link {
    -moz-user-select:none;/*火狐*/
    -webkit-user-select:none;/*webkit浏览器*/
    -ms-user-select:none;/*IE10*/
    -khtml-user-select:none;/*早期浏览器*/
      user-select:none;
  }
</style>

<style lang="scss">
.campaign-name-form {
  display: flex;
  padding: 20px 10px;
  justify-content: space-around;
  align-items: center;
  overflow-x: auto;
  min-width: 900px;
  @apply text-sm;
  .campaign-name-item {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    position: relative;
    min-width: 70px;
    height: 80px;
    .val-text {
      height: 32px;
      line-height: 32px;
    }
    .campaign-name-val {
      display: flex;
      justify-content: center;
    }
    .campaign-name-label {
      text-align: center;
      @apply text-xs text-black-placeholder;
    }
    .t-select-input {
      vertical-align: middle;
    }
  }
  .channel-item {
    flex: 2;
  }
  .region-item {
    flex: 0.125;
  }
  .os-item {
    flex: 0.5;
  }
  .campaign-goal-item {
    flex: 1.5;
  }
  .campaign-date-item {
    flex: 2;
  }
  .campagin-custom-item {
    flex: 2;
  }
  .spend-type-item {
    flex: 1.5;
  }
  .region-type-item {
    flex: 2;
  }

  .divider {
    flex: 0.25;
    min-width: 20px;
    height: 80px;
    // width: 20px;
    padding-top: 12px;
    text-align: center;
  }
  .tea-datepicker__input input {
    width: 105px;
  }
}
</style>
