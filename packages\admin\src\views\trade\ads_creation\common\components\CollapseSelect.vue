<template>
  <PopupInputBox
    :title="props.title"
    v-bind="$attrs"
    @on-visible-change="onVisibleChange"
    @on-clearable="onClearable"
  >
    <template #button>
      <div v-if="props.multiple" class="w-[635px]">
        <t-icon
          class="text-black-secondary text-lg"
          name="search"
        />
        <span
          style="border-left: 1px solid var(--aix-border-color-black-disabled);"
          class="pl-[7px] ml-[7px] text-black-placeholder"
        >
          <span v-if="innerLabelList.length === 0">
            {{ attrs.placeholder || 'Search or select a location' }}
          </span>
          <span v-else>
            <t-tag
              v-for="item in innerLabelList.slice(0, 4)"
              :key="item.value"
              class="mr-[5px] mb-[5px]"
              closable
              :on-close="() => handleClose(item)"
            >{{ item.label }}</t-tag>
            <t-tag
              v-if="innerLabelList.length > 4"
              class="mr-[5px]"
            >+{{ innerLabelList.length - 4 }}</t-tag>
          </span>
        </span>
      </div>
      <div v-else>{{ innerLabel }}</div>
    </template>
    <template #content>
      <div>
        <t-input
          v-model="searchKey"
          placeholder="Search"
          style="padding: 10px 14px;"
        >
          <template #suffix>
            <t-icon
              class="text-black-secondary text-lg"
              name="search"
            />
          </template>
        </t-input>
      </div>
      <t-checkbox-group
        v-model="innerModelValue"
        class="flex-col max-h-[300px] overflow-y-auto	flex-nowrap min-w-[400px]"
        style="gap: 0"
        :on-change="change"
      >
        <t-collapse
          :default-value="[0]"
          class="t-select__list border-0"
        >
          <t-collapse-panel
            v-for="(item, index) in searchOptions"
            :key="index"
            :header="item.group"
            class="t-select-option-group t-size-m t-select-option-group__divider border-0"
          >
            <div class="flex flex-col bg-white-primary pl-[40px] pr-[14px]">
              <t-checkbox
                v-for="(child) in item.children"
                :key="child.value"
                :label="child.label"
                :value="child.value"
                :disabled="child.disabled"
                class="h-[40px]"
              />
            </div>
          </t-collapse-panel>
        </t-collapse>
      </t-checkbox-group>
    </template>
  </PopupInputBox>
</template>
<script setup lang="ts">
import { computed, ref, shallowRef, useAttrs, watch, PropType } from 'vue';
import PopupInputBox from 'common/components/PopupInputBox/index';
import { TdOptionProps } from 'tdesign-vue-next';
import { cloneDeep } from 'lodash-es';


interface GroupOptions {
  group: string,
  children: TdOptionProps[],
}

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  minCollapsedNum: {
    type: Number,
    default: 4,
  },
  modelValue: {
    type: Array as PropType<string[]>,
    default: () => ([]),
  },
  options: {
    type: Array as PropType<GroupOptions[]>,
    default: () => [],
  },
});
const innerModelValue = shallowRef<string[]>([]);
const emit = defineEmits(['isShowDialog', 'update:modelValue', 'change', 'delete']);

// 默认取从父组件传来的value
const innerLabel = ref<string | number>('');
const innerLabelList = ref<Array<TdOptionProps>>([]);

const attrs = useAttrs();
const searchKey = ref<string>('');

const searchOptions = computed(() => {
  if (!searchKey.value) {
    return props.options;
  }
  const searVal = searchKey.value.toLowerCase();
  const cloneOptions = cloneDeep(props.options);

  cloneOptions.forEach((item) => {
    if (`${item.group}`.toLowerCase().indexOf(searVal) === -1) {
      if (item.children) {
        // eslint-disable-next-line no-param-reassign
        item.children = item.children.filter(child => `${child.label}`.toLowerCase().indexOf(searVal) !== -1);
      }
    }
  });
  return cloneOptions;
});
const findText = (target: string) => {
  let result;
  props.options.some((item) => {
    if (item.children) {
      const targetItem = item.children.find(subItem => subItem.value === target);
      if (targetItem) {
        result = targetItem;
        return true;
      }
    }
    return false;
  });
  return result;
};
const effectLabelList = () => {
  const label: TdOptionProps[] = [];
  innerModelValue.value.forEach((item: string) => {
    const targetText = findText(item);
    if (targetText) {
      label.push(targetText);
    }
  });
  innerLabelList.value = label;
};
// 把options.value变成 parent.value-children.value

watch(() => props.modelValue, () => {
  innerModelValue.value = props.modelValue;
}, {
  immediate: true,
});

watch(() => [innerModelValue.value, props.options], () => {
  effectLabelList();
}, {
  immediate: true,
});


const handleClose = (currentItem: TdOptionProps) => {
  const index = innerModelValue.value.findIndex(item => item === currentItem.value);
  innerModelValue.value.splice(index, 1);
  change();
};


const change = () => {
  // effectLabelList();
  emit('update:modelValue', innerModelValue.value);
  emit('change');
};
const onClearable = () => {
  innerModelValue.value = [];
  change();
};

// 下拉面板展示活隐藏回调
const onVisibleChange = (visible: boolean) => {
  searchKey.value = '';
  emit('isShowDialog', visible);
};

</script>
<style lang="scss" scoped>
  :deep(.t-collapse-panel__wrapper .t-collapse-panel__header) {
    border-width: 0;
  }
  :deep(.t-collapse-panel__body) {
    border-width: 0;
  }
  // :deep(.select__list) {
  //   .t-collapse-panel__body {
  //     @apply bg-white-primary;
  //   }
  // }
</style>
