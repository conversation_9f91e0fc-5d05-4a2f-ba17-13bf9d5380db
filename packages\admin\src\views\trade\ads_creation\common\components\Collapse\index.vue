<!--
 * @Date: 2023-04-10 15:29:11
 * @LastEditors: maclerylin
 * @LastEditTime: 2023-04-18 14:57:23
-->
<template>
  <t-collapse
    :expand-icon="showArrow" :borderless="borderless"
    default-expand-all
    style="position: relative;"
    :class="{
      transverse: !isVertical,
      tradeCollapse: true,
    }"
    @change="handlePanelChange"
  >
    <t-collapse-panel value="0">
      <template #header>
        <div :style="headerTitle.style">
          {{ headerTitle.content }}
        </div>
      </template>
      <template #default>
        <div style="position: relative;" class="pb-[10px]">
          <slot />
        </div>
      </template>
    </t-collapse-panel>
  </t-collapse>
</template>
<script lang="ts">
export default {
  props: {
    headerTitle: {
      type: Object,
      default: () => {},
    },
    isVertical: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      borderless: true,
      showArrow: true,
    };
  },
  methods: {
    handlePanelChange(val: string) {
      console.log(val);
    },
  },
};
</script>
<style lang="scss">
.tradeCollapse .t-collapse-panel__header {
  padding: 0;
}
.transverse .t-collapse-panel__wrapper {
  display: flex;
  align-items: flex-start;
}
.transverse .t-collapse-panel__content {
  padding: 0;
}
</style>

