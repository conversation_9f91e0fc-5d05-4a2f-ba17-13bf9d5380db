<template>
  <t-collapse v-if="!props.isReviewing" :default-expand-all="true">
    <t-collapse-panel value="0" header="Conversion goals">
      <t-table
        v-if="tableRows.length > 0"
        v-model:selected-row-keys="selectedGoal"
        row-key="resource_name"
        :columns="columns"
        :data="tableRows"
        :bordered="true"
        @select-change="rehandleSelectChange"
      />
    </t-collapse-panel>
  </t-collapse>
  <t-form-item v-else label="Conversion goals">
    <t-table
      v-model:selected-row-keys="selectedGoal"
      row-key="resource_name"
      :columns="columns"
      :data="tableRows"
      :bordered="true"
      @select-change="rehandleSelectChange"
    />
  </t-form-item>
</template>
<script lang="ts" setup>
import  { ref, watch, toRefs, PropType } from 'vue';
import type { ConversionGoalItem } from '../../google/type' ;

const props = defineProps({
  modelValue: {
    type: Array as PropType<ConversionGoalItem[]>,
    default: () => [],
  },
  tableRows: {
    type: Array as PropType<ConversionGoalItem[]>,
    default: () => [],
  },
  isMultiSelect: {
    type: Boolean,
    default: true,
  },
  isReviewing: {
    type: Boolean,
    default: false,
  },
});
const { tableRows } = toRefs(props);
const emits = defineEmits(['update:modelValue']);
const rehandleSelectChange = () => {
  const value = tableRows.value.map((item) => {
    const newItem = { ...item };
    newItem.biddable = false;
    if (selectedGoal.value.includes(item.resource_name)) {
      newItem.biddable = true;
    }
    return newItem;
  });
  emits('update:modelValue', value);
};

const selectedGoal = ref<string[]>([]);
const columns = ref([
  {
    colKey: 'row-select',
    type: 'multiple',
    disabled: () => props.isReviewing,
    width: 50,
  },
  { colKey: 'category_en', title: 'Conversion Goals' },
  { colKey: 'origin_en', title: 'Conversion Source'  },
]);
// watch(() => props.isReviewing, () => {
//   columns.value[0].disabled = () => props.isReviewing;
// }, {
//   immediate: true,
// });
watch(() => props.modelValue, () => {
  selectedGoal.value = props.modelValue.filter(item => item.biddable).map(item => item.resource_name);
}, {
  immediate: true,
});
watch(() => props.isMultiSelect, () => {
  columns.value[0].type = props.isMultiSelect ? 'multiple' : 'single';
}, {
  immediate: true,
});


</script>
<style lang="scss" scoped>
  :deep .t-collapse-panel__wrapper .t-collapse-panel__content {
    padding-right: 40px;
  }
  :deep .t-table {
    @apply bg-gray-primary;
  }
</style>
<style>
  .t-is-error .t-collapse, .t-is-error .t-collapse-panel__wrapper .t-collapse-panel__body {
    @apply border-error-primary;
  }
</style>
