<template>
  <t-select v-bind="$attrs" :value="modelValue" @change="onSelect">
    <t-option
      v-for="item in options" :key="item.value" :label="getLabel(item)"
      :value="item.value" :disabled="item.disabled"
    >
      {{ getLabel(item) }}
    </t-option>
  </t-select>
</template>
<script setup lang="ts">
import { PropType, toRefs } from 'vue';
import type { TdOptionProps } from 'tdesign-vue-next';
import { importantChange } from './utils';
import { Level } from '@/views/trade/ads_creation/common/template/config';

const props = defineProps({
  options: {
    type: Array as PropType<TdOptionProps[]>,
    default: () => [],
  },
  modelValue: {
    type: String,
    default: '',
  },
  customUi: {
    type: Boolean,
    default: true,
  },
  important: {
    type: Boolean,
    default: false, // 是否是重要字段，需要弹框提示
  },
  importantLevel: {
    type: Number as PropType<Level>,
    default: Level.CampaignLevel, // 重要字段的层级，默认为campaign
  },
  splitChar: { // 分隔符
    type: String,
    default: '-',
  },
});

const { modelValue, important, importantLevel } = toRefs(props);
const emits = defineEmits(['update:modelValue']);

const getLabel = (item: TdOptionProps) => {
  if (props.customUi) return `${item.label} ${props.splitChar} ${item.value}`;
  return item.label;
};

const onSelect = (val: string) => {
  if (important.value) {
    importantChange(importantLevel.value, emits, val);
  } else {
    emits('update:modelValue', val);
  }
};
</script>
