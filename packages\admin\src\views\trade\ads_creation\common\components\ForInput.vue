<template>
  <div class="w-[100%]">
    <div v-if="maxNumber > 1 && !textarea" class="header-action mb-[11px] flex justify-between">
      <div />
      <div>
        <t-switch v-model="visible" size="large" :disabled="disabled" />
        <t-button
          theme="primary" variant="text" content="Bulk Editor"
          :disabled="disabled"
          @click="visible=!visible"
        />
      </div>
    </div>
    <div v-show="maxNumber > 1 && !textarea && visible" class="bulk-editor mb-[24px]">
      <textarea
        v-model="bulkEditor"
        :disabled="disabled"
        class="t-textarea__inner bluk-editor h-[128px]"
        placeholder="Entries should be on separate lines"
        name="description"
        @input="bulkEditorChange"
        @focus="onBlukFocus"
      />
    </div>
    <div v-for="(item, index) in list" :key="index">
      <div v-if="textarea" class="relative input-textarea">
        <t-textarea
          v-model="list[index]"
          :disabled="disabled"
          class="w-[100%]"
          :autosize="{ minRows: 3, maxRows: 5 }"
          :maxlength="maxLen > 0 ? maxLen : undefined"
          :class="{'mb-[12px]': index !== list.length - 1}"
          @change="() => { onChange(index)}"
        />
        <div class="absolute right-[-32px] top-[6px]">
          <svg-icon
            v-if="index >= 1 && list.length > minNumber"
            class="cursor-pointer mr-[2px]" name="delete" color="var(--aix-text-color-black-placeholder)"
            @click="deleteItem(index)"
          />
        </div>
      </div>
      <template v-else>
        <t-input
          v-model="list[index]"
          :disabled="disabled"
          class="w-[100%] input-textinput"
          :class="{'mb-[12px]': index !== list.length - 1}"
          @change="() => { onChange(index)}"
        >
          <template #suffix>
            <div class="flex items-center">
              <svg-icon
                v-if="index >= 1 && list.length > minNumber"
                class="cursor-pointer mr-[8px]" name="delete" color="var(--aix-text-color-black-placeholder)"
                @click="deleteItem(index)"
              />
              <span v-if="maxLen > 0" class="min-w-[40px]">{{ getLen(item) }}</span>
            </div>
          </template>
        </t-input>
      </template>
    </div>
    <div v-if="maxNumber > 1" class="footer-action mt-[16px]">
      <t-button
        v-if="list.length < maxNumber" theme="primary" variant="text"
        :disabled="disabled"
        @click="addItem"
      >
        <template #icon>
          <add-icon />
        </template>
        {{ addText }}
      </t-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { toRefs, toRef, ref, watch, PropType } from 'vue';
import { get } from '@vueuse/core';
import { sizeof } from 'common/utils/common';
import { getTargetString } from '../template/utils-common';
import { AddIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { SvgIcon } from 'common/components/SvgIcon';

const props = defineProps({
  modelValue: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  disabled: { type: Boolean, default: false },
  addText: { type: String,  default: 'Add' },
  maxNumber: { type: Number, default: 0 },
  minNumber: { type: Number, default: 1 },
  maxLen: { type: Number, default: 0 },
  textarea: { type: Boolean, default: false },
  isUTF8: { type: Boolean, default: false },
});
const emits = defineEmits(['update:modelValue']);
const { maxLen, maxNumber, minNumber } = toRefs(props);
const list = toRef(props, 'modelValue');
const visible = ref(false);
const bulkEditor = ref<string>(list.value.join('\n'));

const syncBulk = () => {
  bulkEditor.value = list.value.join('\n'); // 同步更新bulk数据
};

// 监听list值切换，同步更新bluk
watch(() => props.modelValue, () => {
  syncBulk();
});

// text输入事件
const inputWord = function (index: number) {
  const leg = sizeof(list.value[index], props.isUTF8 ? 2 : 1);
  if (maxLen.value >= 0 && leg >= maxLen.value) {
    list.value[index] = getTargetString(list.value[index], maxLen.value, props.isUTF8 ? 2 : 1);
  }
  syncBulk(); // 同步更新bulk数据
};

// bulk修改事件，同步更新list
const bulkEditorChange = (e: any) => {
  let currenVal = e.target.value;
  // 删除全部，保留最少的行数
  if (!currenVal) {
    currenVal = new Array(props.minNumber).fill('')
      .join('\n');
  }
  let bulkContents = currenVal.split('\n');
  if (e.inputType === 'insertFromPaste') {
    bulkContents = bulkContents.slice(0, props.maxNumber); // 复制的内容，不能超过最大行数
    bulkEditor.value = bulkContents.join('\n');
  }
  // 限制每行的字数
  bulkContents = bulkContents.map((text: string) => {
    if (props.maxLen >= 0) {
      const len = sizeof(text, props.isUTF8 ? 2 : 1);
      if (len > props.maxLen) return getTargetString(text, props.maxLen, props.isUTF8 ? 2 : 1);
    }
    return text;
  });
  currenVal = bulkContents.join('\n');

  if (bulkContents.length < props.minNumber || bulkContents.length > props.maxNumber) {
    const textarea = document.querySelector('.bluk-editor') as HTMLTextAreaElement;
    const listVal = list.value.join('\n');
    const curSelection = textarea.selectionStart; // 当前光标位置
    let index = curSelection - 1 || 0;
    while (listVal[index] !== '\n' && index < listVal.length) {
      index += 1;
    }
    syncBulk();
    setTimeout(() => {
      textarea.setSelectionRange(index + 1, index + 1); // 回车时，需要将光标移动到下一行
    }, 100);
    return;
  }
  emits('update:modelValue', currenVal.split('\n'));
  bulkEditor.value = currenVal;
};

// 删除文本
const deleteItem = (index: number) => {
  if (props.disabled) return;
  if (list.value.length <= get(minNumber)) {
    MessagePlugin.warning(`At least ${get(minNumber)} items`);
    return;
  }
  list.value.splice(index, 1);
  emits('update:modelValue', list.value);
  syncBulk();
};

// 添加文本
const addItem = () => {
  if (list.value.length >= maxNumber.value) return;
  list.value.push('');
  emits('update:modelValue', list.value);
  syncBulk();
};

const onChange = (index: number) => {
  inputWord(index);
  emits('update:modelValue', list.value);
};
const getLen = (val: string) => `${sizeof(val, props.isUTF8 ? 2 : 1)}/${maxLen.value}`;

// textarea focus事件
const onBlukFocus = () => {
  // 如果全部都是空的，光标需要移动到起始位置
  const allEmpty = list.value?.every(text => !text);
  setTimeout(() => {
    if (allEmpty) {
      const textarea = document.querySelector('.bluk-editor') as HTMLTextAreaElement;
      textarea?.setSelectionRange(0, 0);
    }
  }, 100);
};
</script>
