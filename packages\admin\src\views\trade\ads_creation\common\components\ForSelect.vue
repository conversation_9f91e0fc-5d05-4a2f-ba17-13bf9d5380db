<template>
  <div class="w-[100%]">
    <div v-for="(l, index) in list" :key="index" class="relative mb-[10px] for-select-dom ">
      <t-select v-model="list[index]" :disabled="disabled" @change="onSelect">
        <t-option
          v-for="(option, index2) in curOptions" :key="index2" :value="option.value"
          :label="option.label" :disabled="option.disabled"
        />
      </t-select>
      <svg-icon
        v-if="index >= 1"
        class="delete-icon" name="delete" color="var(--aix-text-color-black-placeholder)"
        @click="deleteItem(index)"
      />
    </div>
    <div v-if="maxNumber > 1 && list.length < maxNumber" class="footer-action mt-[11px]">
      <t-button
        theme="primary" variant="text" :disabled="disabled"
        @click="addItem"
      >
        <template #icon>
          <add-icon />
        </template>
        {{ addText }}
      </t-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { PropType, toRefs, computed } from 'vue';
import type { TdOptionProps } from 'tdesign-vue-next';
import { AddIcon } from 'tdesign-icons-vue-next';
import { SvgIcon } from 'common/components/SvgIcon';

const props = defineProps({
  options: {
    type: Array as PropType<TdOptionProps[]>,
    default: () => [],
  },
  addText: {
    type: String,
    default: 'Add',
  },
  maxNumber: {
    type: Number,
    default: 0,
  },
  exclude: { // 每个选项之间是否互斥
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  disabled: { type: Boolean, default: false },
  isReviewing: {
    type: Boolean,
    default: false,
  },
});
const emits = defineEmits(['update:modelValue']);
const { modelValue: list } = toRefs(props);

// 根基当前已选项，计算options的disabled
const curOptions = computed(() => props.options.map((item) => {
  const disabled = list.value.includes(item.value as string);
  return {
    ...item,
    disabled,
  };
}));

// 添加1项，同步修改modelValue
const addItem = () => {
  if (list.value.length >= props.maxNumber) return;
  list.value.push('');
  emits('update:modelValue', list.value);
};

// 删除一项，同步修改modelValue
const deleteItem = (index: number) => {
  if (props.disabled) return;
  list.value.splice(index, 1);
  emits('update:modelValue', list.value);
};

const onSelect = () => {
  emits('update:modelValue', list.value);
};
</script>
<style lang="scss" scoped>
.delete-icon {
  position: absolute;
  cursor: pointer;
  top: 50%;
  transform: translateY(-50%);
  right: 30px;
  z-index: 1;
}
</style>
