<!-- 可以计算字数的input -->
<template>
  <t-input
    v-if="!props.isTextarea"
    v-model="data"
    :disabled="disabled"
    :placeholder="placeholder"
    class="w-[100%] input-textinput"
    @change="onChange"
  >
    <template #suffix>
      <div class="flex items-center">
        <span class="min-w-[40px]">{{ getLen() }}</span>
      </div>
    </template>
  </t-input>
  <div
    v-else
    style="position: relative; padding-bottom: 0px; height: auto;"
    class="flex flex-col"
  >
    <t-textarea
      v-model="data"
      :disabled="disabled"
      :autosize="{minRows: 5, maxRows: 10}"
      :placeholder="placeholder"
      style="white-space: pre-line;"
      class="max-w-[688px] narrow-scrollbar input-textarea"
      @change="onChange"
    />
    <span class="t-textarea__limit" style="position: absolute; bottom: 3px; right: 8px;">{{ getLen() }}</span>
  </div>
</template>
<script lang="ts" setup>
import { sizeof } from 'common/utils/common';
import { getTargetString } from '../template/utils-common';
import { toRefs, ref, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  maxLen: {
    type: Number,
    default: 0,
  },
  placeholder: {
    type: String,
    default: '',
  },
  isUTF8: {
    type: Boolean,
    default: false,
  },
  isTextarea: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['update:modelValue']);
const { maxLen, placeholder } = toRefs(props);
const data = ref(props.modelValue);

watch(() => props.modelValue, (val) => {
  data.value = val;
});

const inputWord = function () {
  const leg = sizeof(data.value);
  if (leg >= maxLen.value) {
    data.value = getTargetString(data.value, maxLen.value, props.isUTF8 ? 2 : 1);
    console.log(data.value, leg);
  }
};
const onChange = () => {
  inputWord();
  emits('update:modelValue', data.value);
};
const getLen = () => `${sizeof(data.value, props.isUTF8 ? 2 : 1)}/${maxLen.value}`;
</script>
<style>
  input[type=number]::-webkit-inner-spin-button,
  input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  input[type=number] {
      appearance: textfield;
  }
</style>
