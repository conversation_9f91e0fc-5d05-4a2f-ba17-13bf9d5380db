<template>
  <t-input
    v-model="data"
    :placeholder="placeholder"
    type="number"
    class="max-w-[688px] inline-block input-number"
    v-bind="$attrs"
    @blur="dataBlur"
    @mousewheel.prevent=""
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
const props = defineProps({
  placeholder: {
    type: String,
    default: '',
  },
  modelValue: {
    type: [String, Number],
    default: '',
  },
  decimalPlaces: {
    type: Number,
    default: undefined,
  },
  integerPlaces: {
    type: Number,
    default: undefined,
  },
});

const emits = defineEmits(['update:modelValue']);

const data = ref(props.modelValue);
watch(() => props.modelValue, () => {
  data.value = props.modelValue;
});

const dataBlur = () => {
  let val = data.value;
  if (props.decimalPlaces !== undefined && val !== '') {
    val = Number((Number(val)).toFixed(props.decimalPlaces));
  }
  if (props.integerPlaces !== undefined && val !== '') {
    const dotPlace = val.toString().indexOf('.');
    let tempVal = val.toString();
    let decimalStr = '';
    if (dotPlace !== -1) {
      tempVal = tempVal.substring(0, dotPlace);
      decimalStr = val.toString().substring(dotPlace + 1);
    }
    tempVal = tempVal.substring(0, props.integerPlaces);
    val = `${tempVal}${decimalStr ? `.${decimalStr}` : ''}`;
  }
  data.value = val !== '' ? Number(val) : '';
  emits('update:modelValue', val);
};

</script>
<style>
  input[type=number]::-webkit-inner-spin-button,
  input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  input[type=number] {
      appearance: textfield;
  }
</style>
