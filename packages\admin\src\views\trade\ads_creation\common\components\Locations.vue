<template>
  <div class="flex justify-center">
    <Country
      v-if="props.options && props.options.length"
      ref="countryRef"
      v-model="datas"
      :labels="['Region', 'Country']"
      :placeholder="props.placeholder"
      :options="props.options || []"
      :is-td-location="true"
      :clearable="true"
      :search-key="['label', 'value', 'country_text_CN', 'code', 'country_code']"
      @change="locationChange"
      @delete="locationDelete"
    />
    <t-link
      content="Bulk Editor" theme="primary" class="ml-[10px]"
      :underline="false"
      @click="() => { isShowEditor = true; }"
    />
    <t-dialog
      header="Location"
      :visible="isShowEditor"
      :width="600"
      :on-close="() => { isShowEditor = false; }"
    >
      <template #body>
        <div
          class="flex rounded-l-default rounded-r-none"
          style="border: 1px solid var(--aix-border-color-gray-placeholder);"
        >
          <div class="t-form__controls " :class="isError ? 't-is-error' : ''">
            <div class="t-form__controls-content">
              <textarea
                v-model="editorValue"
                :placeholder="inputPlaceholder"
                class="w-[250px] h-[280px] m-[1px] p-[5px] overflow-y-auto"
                rows="10"
              />
            </div>
            <div v-if="isError" class="t-input__extra">Invalid locations name！</div>
          </div>
          <div
            class="w-[250px] m-[1px] p-[5px] overflow-y-auto"
            style="border-left: 1px solid var(--aix-border-color-gray-placeholder)"
          >
            <div v-for="item in editorArray" :key="item" class="flex items-center">
              <icon
                v-if="analysisResult[item]"
                name="check-circle-filled"
                class="text-success-primary ml-[8px]"
              />
              <icon
                v-else
                name="error-circle-filled"
                class="text-error-primary ml-[8px]"
              />
              <span v-if="analysisResult[item]" class="ml-[10px]">
                {{ (analysisResult[item] as any).code }}
              </span>
              <span v-else class="ml-[10px]">
                --
              </span>
            </div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="flex justify-between">
          <t-button variant="outline" theme="default" @click="editorValueChange">
            Verify
          </t-button>
          <t-button
            theme="primary"
            :disabled="Object.values(analysisResult).length === 0"
            @click="confirmLocation"
          >
            Apply
          </t-button>
        </div>
      </template>
    </t-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, nextTick, PropType } from 'vue';

import Country from 'common/components/InputCascader/Index.vue';
import { getLocationIdByCode } from '../template/utils-common';
import { Icon } from 'tdesign-icons-vue-next';

type CountryType = {
  updateValue: Function,
};
type OptionItem = {
  label: string,
  value: string,
  code: string,
  children: OptionItem[],
};

const emit = defineEmits(['change', 'update:modelValue']);
const props = defineProps({
  placeholder: {
    type: String,
    default: '',
  },
  options: {
    type: Array as PropType<OptionItem[]>,
    default: () => [],
  },
  modelValue: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});
const inputPlaceholder = 'Please input locations (English name/Chinese name/code) separated by a new line or ",". \nExample:\nus,jp';
const datas = ref<string[]>(props.modelValue);
const isShowEditor = ref(false);
const editorValue = ref('');
const analysisResult = ref<{[key: string]: Object}>({});

const editorArray = ref<string[]>([]);
let oldValue = '';

watch(() => props.modelValue, () => {
  datas.value = props.modelValue;
  if (countryRef.value?.updateValue) {
    countryRef.value.updateValue(datas.value, 'all');
  }
});

const locationDelete = (value: string) => {
  const findContinentItem = props.options.find(item => `${item.value}` === value);
  if (findContinentItem) {
    const result: string[] = [];
    datas.value.forEach((item) => {
      const findItem = findContinentItem.children.find(child => child.value === item);
      if (!findItem) {
        result.push(item);
      }
    });
    datas.value = result;
  } else {
    datas.value = datas.value.filter(item => item !== value);
  }
  locationChange();
};

// textarea 改变触发
const editorValueChange = () => {
  if (!editorValue.value) {
    validator();
    return;
  }
  const splitTextKeys = editorValue.value.split(/\n|,/g);
  analysisResult.value = getLocationIdByCode(props.options, splitTextKeys);
  editorValue.value = splitTextKeys.join('\n');
  editorArray.value = editorValue.value.split('\n');
  oldValue = editorValue.value;
  validator();
};
const isError = ref(false);
const countryRef = ref<CountryType>();

watch(() => editorValue.value, () => {
  if (editorValue.value !== oldValue) {
    analysisResult.value = {};
    editorArray.value = [];
  }
});

const validator = () => {
  if (!editorValue.value || Object.values(analysisResult.value).length === 0) {
    isError.value = true;
    return false;
  }
  isError.value = false;
  return true;
};
const locationChange = () => {
  emit('update:modelValue', datas.value);
  nextTick(() => {
    emit('change');
  });
};
const confirmLocation = async () => {
  if (!validator()) {
    return;
  }
  datas.value = Object.values(analysisResult.value).map(item => (item as any).value);
  emit('update:modelValue', datas.value);
  isShowEditor.value = false;
  nextTick(() => {
    emit('change');
  });
};
</script>
