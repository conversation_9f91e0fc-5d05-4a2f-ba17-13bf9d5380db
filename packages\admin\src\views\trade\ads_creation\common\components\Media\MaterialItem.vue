<template>
  <div class="material-item flex items-center max-w-[302px] mb-[20px]">
    <div class="preview-left relative left-item mr-[16px] cursor-pointer">
      <preview
        ref="previewRef"
        :title="data.name"
        :type="data.isVideo ? 'video' : 'image'"
        :get-url="getUrl"
        :poster="data.preview_url || data.detail?.cover"
        mode="modal"
        :link-url="data.isVideo ? data.video_url : ''"
        :no-preview="data.is_channel && data.isVideo && !data.video_url"
      />
      <SvgIcon
        v-if="data.isVideo"
        class="absolute inset-0 m-auto pointer-events-none" name="play-btn" size="24"
        color="white-primary': 'var(--aix-text-color-white-primary)"
        @click.capture="() => {}"
      />
    </div>
    <div class="right-item h-[85px] flex flex-col justify-around">
      <div class="media-title text-black-primary truncate w-[200px]" :title="data.name">
        {{ data.name }}
      </div>
      <div class="flex items-center ratio-info text-black-disabled">
        <icon name="chart" />
        <!-- media library -->
        <template v-if="data.detail">
          <template v-if="data.detail.width">{{ data.detail.width }} * {{ data.detail.high }}</template>
        </template>
        <!-- aix library -->
        <template v-else-if="data.material_ext">
          {{ data.material_ext.video.width }} * {{ data.material_ext.video.high }}
        </template>
      </div>
      <div>
        <t-button
          class="p-[0]" theme="primary" variant="text"
          :disabled="disabled"
          @click="deleteMaterialItem()"
        >
          Delete
        </t-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { PropType, ref } from 'vue';
import { Icon } from 'tdesign-icons-vue-next';
import { SvgIcon } from 'common/components/SvgIcon';
import Preview from 'common/components/Dialog/Preview/Preview.vue';
import type { MaterialItem } from '../../template/media.type';
import { getPreviewUrl } from 'common/service/creative/library/get-preview-url';

const props = defineProps({
  isReviewing: {
    type: Boolean,
    default: false,
  },
  depotToken: {
    type: Object as PropType<{ arthubCode: string, publicToken: string }>,
    default: () => {},
  },
  data: {
    type: Object as PropType<MaterialItem>,
    default: () => {},
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['delete', 'preview']);

const previewRef = ref(); // 预览弹框
const getUrl = async () => {
  const url = await getPreviewUrl({
    arthubCode: props.depotToken.arthubCode,
    assetId: props.data?.asset_id,
    publicToken: props.depotToken.publicToken,
  });
  return url;
};

const deleteMaterialItem = () => {
  const type = props.data?.isVideo ? 'video' : 'image';
  emit('delete', type, props.data);
};
</script>
<style lang="scss">
.material-item {
  .preview-left {
    > div {
      width: 85px;
      height: 85px;
      display: flex;
      align-items: center;
      justify-content: center;
      @apply bg-gray-primary;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        @apply bg-black-primary;
      }
    }
  }
}
</style>
