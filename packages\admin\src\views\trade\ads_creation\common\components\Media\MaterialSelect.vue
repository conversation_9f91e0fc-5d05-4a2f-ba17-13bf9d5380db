<template>
  <t-dialog
    v-model:visible="visible"
    class="material-select-dialog"
    attach="body"
    width="1000"
    :close-on-overlay-click="false"
    header=""
  >
    <template #body>
      <div>
        <t-tabs v-model="libraryType" class="library-type" @change="changeLibrary">
          <t-tab-panel value="media" label="Media Library" />
          <t-tab-panel :disabled="disabledAix" value="aix" label="AIX Library" />
        </t-tabs>
        <div v-if="visible" class="flex h-[550px]">
          <div v-if="libraryType === 'aix'" class="w-[220px] h-full">
            <LeftDictionary
              class="left-menu left-nav-height"
              :disable-action="true"
              :tree-list="store.dictionary.dictionaryList"
              :default-expanded="store.dictionary.defaultExpanded"
              :default-actived="store.dictionary.activeFolderId"
              :loading="store.isLoading"
              @active-change="activeChange"
              @open-folder="store.dictionary.initChildFolder"
            />
          </div>
          <MediaContentContainer
            ref="materialSelector"
            :class="(libraryType === 'aix' ? 'ml-[20px]' : '') + ' flex-1'"
            :type="libraryType"
            :ratios="ratios"
            :disable-action="true"
            :list="store.material.materialList"
            :loading="store.material.loading"
            :store="store"
            :disable-imgae="maxImgNum === 0"
            :disable-video="maxVideoNum === 0"
          />
        </div>
        <div class="relative bg-white-primary selected mt-[30px] pr-[30px]">
          <template v-if="maxNum !== -1">
            <div class="flex mb-[10px] items-center">
              <div class="selected-label">
                Selected creatives
                <span :class="selectedMedias.length > maxNum ? 'text-error-primary' : ''">
                  {{ selectedMedias.length }}
                </span>
                of {{ maxNum }}
                <t-tooltip v-if="tip" :content="tip">
                  <InfoCircleIcon />
                </t-tooltip>
              </div>
              <div class="flex-1 ml-[10px]">
                <selected-item
                  v-for="(item, index) in selectedMedias" :key="index" :type="item.type"
                  :poster="item.poster"
                  @delete="onDeleteSelected(item)"
                />
              </div>
            </div>
          </template>
          <template v-else>
            <div v-if="maxImgNum !== 0" class="flex mb-[10px] items-center">
              <div class="selected-label">
                Selected images
                <span :class="selectedImages.length > maxImgNum ? 'text-error-primary' : ''">
                  {{ selectedImages.length }}
                </span>
                of {{ maxImgNum }}
                <t-tooltip v-if="tip" :content="tip">
                  <InfoCircleIcon />
                </t-tooltip>
              </div>
              <div class="flex-1 ml-[10px]">
                <selected-item
                  v-for="(item, index) in selectedImages"
                  :key="index"
                  :type="item.type" :poster="item.poster"
                  @delete="onDeleteSelected(item)"
                />
              </div>
            </div>
            <div v-if="maxVideoNum !== 0" class="flex items-center">
              <div class="selected-label">
                Selected videos
                <span :class="selectedVideos.length > maxVideoNum ? 'text-error-primary' : ''">
                  {{ selectedVideos.length }}
                </span>
                of {{ maxVideoNum }}
                <t-tooltip v-if="videoTip" :content="videoTip">
                  <InfoCircleIcon />
                </t-tooltip>
              </div>
              <div class="flex-1 ml-[10px]">
                <selected-item
                  v-for="(item, index) in selectedVideos" :key="index" :type="item.type"
                  :poster="item.poster"
                  @delete="onDeleteSelected(item)"
                />
              </div>
            </div>
          </template>
          <t-button
            class="absolute right-0 top-[-20px]" theme="primary" variant="text"
            size="small"
            @click="clearAll"
          >
            Clear All
          </t-button>
          <div v-if="!canConfirm && tip" class="tip-error text-xs">{{ tip }}</div>
        </div>
      </div>
    </template>
    <template #cancelBtn>
      <t-button class="ml-[10px]" theme="default" @click="onCancel">Cancel</t-button>
    </template>
    <template #confirmBtn>
      <t-button
        class="ml-[10px]" theme="primary" :disabled="!canConfirm"
        @click="onConfirm"
      >
        Submit
      </t-button>
    </template>
  </t-dialog>
</template>
<script lang="ts" setup>
import { ref, watchEffect, computed, PropType } from 'vue';
import type { ITree } from 'common/components/TreeMenu/type';
import type { MSelectItem } from '../../template/media.type';
import { TSimpleAsset, MediaType } from '@/views/creative/library/define';
import LeftDictionary from '@/views/creative/library/components/LeftDictionary.vue';
import MediaContentContainer from './MediaContentContainer.vue';
import useAixLibraryStore from '@/store/creative/library/aix-library.store';
import { useTDMediaLibraryStore } from '@/store/creative/library/media-library.store';
import { useCheckAssetNameAndSize } from '@/views/creative/library/components/dialog/media/checkAssetInfo';
import { useValidateDialog } from '@/views/creative/library/compose/validate-dialog';
import SelectedItem from './SelectedItem.vue';
import { useDialog } from 'common/compose/useDialog';
import { get } from '@vueuse/core';
import { useCommonParams } from '../../template/compose/currentCompose';
import { useMediaParams } from '../../template/compose/mediaCompose';
import { RadiosLimit, uniqMedias, validRadiosLimit, generateData } from './utils';
import { InfoCircleIcon } from 'tdesign-icons-vue-next';

const props = defineProps({
  maxNum: { type: Number, default: -1 }, // 总的最大数量限制（tiktok渠道），-1表示不限制
  maxImgNum: { type: Number, default: -1 }, // 最大图片数量，为0表示不支持选择图片, -1表示不限制
  maxVideoNum: { type: Number, default: -1 }, // 最大视频数量，为0表示不支持选择视频，-1表示不限
  minImgNum: { type: Number, default: -1 }, // 最小图片限制个数，-1不限制
  minVideoNum: { type: Number, default: -1 }, // 最小视频限制个数，-1不限制
  splitCheckNum: { type: Boolean, default: false }, // 视频和图片分开校验
  mKey: { type: String, default: '' }, // 选择的素材key值
  tip: { type: String, default: '' }, // 图片文案提示
  videoTip: { type: String, default: '' }, // 视频文案提示
  disabledAix: { type: Boolean, default: false }, // 禁用aix library
  ratios: { // 分辨率
    type: Array as PropType<string[]>,
    required: false,
    default: () => [],
  },
  radiosLimit: { // 分辨率限制，不传给后台，只用于选择的校验和禁用
    type: Object as PropType<RadiosLimit | undefined>,
    default: () => undefined,
  },
});

const emit = defineEmits(['select', 'close']);

const aixStore = useAixLibraryStore();
const mediaStore = useTDMediaLibraryStore();
const libraryType = ref<'media' | 'aix'>('media');
const store = ref<any>(mediaStore); // 当前素材库store
const materialSelector = ref(); // 素材库列表对象
const formatType = computed(() => {
  if (props.maxNum !== -1) return 0;
  if (props.maxVideoNum !== -1 && props.maxImgNum !== -1
    && props.maxVideoNum !== 0 && props.maxImgNum !== 0) return 0;
  if (props.maxVideoNum !== 0) return 1;
  if (props.maxImgNum !== 0) return 2;
  return 0;
});
const { visible, show: showDialog, hide: hideDialog } = useDialog();

// 获取选中的视频列表，图片列表
const selectedMedias = ref<MSelectItem[]>([]);
const selectedImages = ref<MSelectItem[]>([]);
const selectedVideos = ref<MSelectItem[]>([]);
watchEffect(() => {
  if (!materialSelector.value) return;
  const mediaItemList = mediaStore.material.materialList || [];
  const aixItemList = aixStore.material.materialList || [];
  const selectedIds = materialSelector.value.checkValue;
  let medias = mediaItemList.concat(aixItemList)
    .concat(selectedImages.value)
    .concat(selectedVideos.value) as MSelectItem[];  // 需要把当前选中的数据也加入进来，防止翻页后找不到
  medias = generateData(medias);
  medias = uniqMedias(medias as MSelectItem[]); // 根据asset_id去重
  medias = medias.filter(item => selectedIds.includes(item.id)); // 再根据当前选中的过滤
  selectedMedias.value = medias;
  selectedImages.value = medias.filter(item => item.type === 'image');
  selectedVideos.value = medias.filter(item => item.type === 'video');
  console.log('medias', medias);
});
const onDeleteSelected = (item: MSelectItem) => {
  materialSelector.value.deleteItems([item.id]);
};
const clearAll = () => {
  materialSelector.value.clearAll();
};

// 目录树节点点击事件
function activeChange(treeItem: ITree, deep: number, index: number, activeValue: string) {
  store.value.dictionary.changeActiveDictionary(activeValue);
}

// 是否允许点击确认
const canConfirm = computed(() => {
  let valid = true;
  const { maxNum, maxVideoNum, maxImgNum, minImgNum, minVideoNum } = props;
  if (maxNum !== -1 && !props.splitCheckNum) {
    valid = selectedMedias.value.length <= maxNum;
  } else {
    if (maxVideoNum !== -1 && valid) valid = selectedVideos.value.length <= maxVideoNum;
    if (maxImgNum !== -1 && valid) valid = selectedImages.value.length <= maxImgNum;
    if (minImgNum !== -1 && valid) valid = selectedImages.value.length >= minImgNum;
    if (minVideoNum !== -1 && valid) valid = selectedVideos.value.length >= minVideoNum;
    if (maxVideoNum !== -1 && maxImgNum !== -1 && valid) {
      valid = selectedVideos.value.length <= maxVideoNum && selectedImages.value.length <= maxImgNum;
    }
    if (minImgNum !== -1 && maxVideoNum !== -1 && valid) {
      valid = selectedVideos.value.length >= maxVideoNum && selectedImages.value.length >= maxImgNum;
    }
  }
  if (props.radiosLimit && valid) {
    valid = validRadiosLimit(props.radiosLimit, selectedMedias.value);
  }
  return valid;
});

// 素材校验，只针对aix library
const ValidateDialog = useValidateDialog();
const validateMedias = () => new Promise<void>((resolve) => {
  const { media } = useCommonParams();
  const namingType = 'gpp';
  const simpleChecked = get(selectedMedias).filter(i => !i.ext.resource_name)
    .map(i => ({
      AssetID: i.id, AssetName: i.title, originName: i.ext.name, type: i.type, materialExt: i.ext.material_ext,
    }));
  const mediaIds = get(selectedMedias).filter(item => !!item.ext.resource_name)
    .map(item => item.id);
  if (simpleChecked.length === 0) {
    resolve();
    return;
  }
  const checkResult = useCheckAssetNameAndSize(media as MediaType, namingType, simpleChecked);
  if (!checkResult) {
    if (window.aegis) {
      window.aegis.infoAll('simpleChecked', simpleChecked);
    }
    ValidateDialog.show({
      showMediaSelect: false,
      assetList: simpleChecked,
      confirmText: 'Continue',
      media,
      namingType,
      beforeClose: (assets: TSimpleAsset[]) => new Promise<void>((resolve2) => {
        const newIds = mediaIds.concat(assets.map(item => item.AssetID));
        materialSelector.value.setSelected(newIds); // 重新设置选中值
        resolve2();
        resolve();
      }),
    });
  } else {
    resolve();
  }
});

const onConfirm = async () => {
  await validateMedias();
  emit('select', selectedMedias.value);
};

const onCancel = () => {
  hideDialog();
  emit('close');
};

function initFunc(store: any) {
  const { accountId, gameCode, media } = useMediaParams();
  const assetRatios = props.ratios;
  if (libraryType.value === 'media') {
    store.material.setParams({
      accountId, media, gameCode, assetRatios,
    });
  }
  store.material.resetNameAndText?.();
  store.material.setPageInfo(1, 48);
  store.dictionary.initDictionary(); // 重置目录
  store.init(formatType.value);
}

// 切换素材库类型
const changeLibrary = (type: string) => {
  if (type === 'media') store.value = mediaStore;
  else store.value = aixStore;
  store.value.material.setStorage(undefined);
  materialSelector.value.setSelectedType(formatType.value); // 重置选中类型
  initFunc(store.value);
};

const show = (initMedias: MSelectItem[] = []) => {
  showDialog();
  initFunc(store.value);

  // 设置当前选中值
  setTimeout(() => {
    selectedMedias.value = initMedias;
    selectedImages.value = initMedias.filter(item => item.type === 'image');
    selectedVideos.value = initMedias.filter(item => item.type === 'video');
    const selectedIds = initMedias.map(item => item.id);
    materialSelector.value.setSelected(selectedIds);
  });
};

defineExpose({
  show,
  hideDialog,
});
</script>
<style lang="scss" scoped>
.material-select-dialog {
  .left-menu {
    height: 100%;
    .left-nav-height {
      height: auto;
    }
  }
  .selected {
    .selected-label {
      min-width: 150px;
    }
  }
  .err-tip {
    color: var(--td-error-color);
  }
}
</style>
<style lang="scss">
.material-select-dialog {
  .t-dialog__body {
    padding: 0;
    overflow: hidden;
  }
  .t-dialog--top {
    padding-top: 30px !important;
    padding-bottom: 2vh !important;
  }
  .library-type {
    .t-tabs__nav-item {
      height: 36px;
      line-height: 36px;
    }
  }
}
.t-drawer {
  z-index: 4000; // 抽屉详情置于顶层
}
.td-media-drawer {
  .t-collapse-panel__content {
    ul {
      li:nth-child(5) {
        display: none;
      }
    }
  }
}
.tip-error {
  color: var(--td-error-color);
}
</style>
