<template>
  <div class="bg-white-primary pt-[20px] list-container">
    <DataContainer
      class="h-[calc(100%-42px)]"
      :data="list"
      :arthub-code="store.dictionary.arthubCode"
      :storage-type="store.dictionary.type"
      :public-token="store.dictionary.publicToken"
      :total="store.material.total"
      :page-size="store.material.pageSize"
      :default-page="store.material.pageNum"
      :page-size-options="[12, 24, 36, 48]"
      @on-page-change="onPageChange"
    >
      <template #attributeSlot>
        <t-space
          align="center"
        >
          <t-radio-group v-model="selectedType" @change="onSelectType">
            <t-radio-button :value="0" :disabled="disableVideo || disableImgae">All types</t-radio-button>
            <t-radio-button :value="1" :disabled="disableVideo">Videos</t-radio-button>
            <t-radio-button :value="2" :disabled="disableImgae">Images</t-radio-button>
          </t-radio-group>
        </t-space>
      </template>
      <template #actionSlot>
        <SearchBox v-model="Store.material.searchList" placeholder="Search and filter" />
        <SvgBtnList :list="actions" />
      </template>
      <div
        v-loading="loading"
        v-auto-animate
        class="w-full h-full"
      >
        <Table
          v-if="containerView === 'table'"
          ref="tableRef"
          v-model:displayColumns="showCols"
          :default-selected-row-keys="checkValue"
          max-height="1028px"
          row-key="id"
          :data="list"
          :columns="cols"
          @select-change="setSelected"
        />
        <Container
          v-else
          v-model="checkValue"
          :default-value="checkValue"
          mode="modal"
          :list="list"
          :need-detail-drawer="false"
          :need-preview="false"
        >
          <template #item="{ data }">
            <Item
              :ref="el => { mediaItems[`mediaItem-${data.id}`] = el }"
              class="media-item"
              :drawer-class="'td-' + type + '-drawer'"
              :data="data"
              mode="modal"
              :need-detail-drawer="true"
              :need-preview="false"
              @on-preview-detail="showPreview"
            >
              <template #bottom>
                <div v-if="data.type === 'video'" class="duration">{{ getDuration(data) }}</div>
                <div class="p-[10px] font-[500] text-xs text-black-primary">
                  <p class="mt-0 mb-[10px] leading-[16px] flex items-center justify-between h-[16px]">
                    <hover-title :title="data.title" />
                  </p>
                  <p class="mt-0 mb-[6px] space-x-[6px] leading-[16px]">
                    <span>{{ getRatio(data) }}</span>
                  </p>
                  <p
                    class="m-0 cursor-pointer leading-[16px] text-brand hover:text-link"
                    @click="viewDetail(data)"
                  >
                    <t-loading :loading="loading" size="small">View Details</t-loading>
                  </p>
                </div>
              </template>
            </Item>
          </template>
        </Container>
      </div>
    </DataContainer>
  </div>
</template>
<script setup lang="tsx">
import DataContainer from 'common/components/Layout/DataContainer.vue';
import { SvgBtnList } from 'common/components/SvgIcon';
import Table from 'common/components/table';
import { Container, IMaterialItem } from 'common/components/creative/MaterialItem/';
import { computed, PropType, ref, toRefs } from 'vue';
import { useCheckAll } from 'common/compose/form/check-all';
import { get } from '@vueuse/core';
import { useTable } from '@/views/creative/library/compose/library-table';
import { TSimpleAsset } from '@/views/creative/library/define';
import { useCreativeDialog } from '@/views/creative/library/compose/dialog';
import { useDetailDialog } from '@/views/creative/library/compose/detail-dailog';
import Item from 'common/components/creative/MaterialItem/Item.vue';
import HoverTitle from 'common/components/creative/MaterialItem/HoverTitle.vue';
import SearchBox from 'common/components/SearchBox';
import { getRatio } from './utils';
import { formatDuration } from 'common/utils/format';

const props = defineProps({
  list: {
    type: Array as PropType<Array<IMaterialItem>>,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: () => false,
  },
  store: {
    type: Object,
    default: () => {
    },
  },
  type: {
    type: String as PropType<'aix' | 'media'>,
    default: () => 'media',
  },
  disableImgae: { type: Boolean, default: false },
  disableVideo: { type: Boolean, default: false },
  ratios: { // 分辨率
    type: Array as PropType<string[]>,
    required: false,
    default: () => [],
  },
});

const { store: Store } = toRefs(props);
const containerView = ref<'list' | 'table'>('list');

const changeView = () => {
  containerView.value = get(containerView) === 'list' ? 'table' : 'list';
};
const isTableView = computed(() => get(containerView) === 'table');

const {
  selectValue: checkValue,
  handleSelectAll,
  setSelected,
} = useCheckAll<IMaterialItem>(
  computed(() => props.list),
  i => i.id,
);

// 根据类型选择素材
const initType = computed(() => {
  if (props.disableVideo) return 2;
  if (props.disableImgae) return 1;
  return 0;
});
const selectedType = ref<number>(get(initType)); // 空不选择，0全部 1视频 2图片
const setSelectedType = (type: number) => {
  selectedType.value = type;
};
const onSelectType = (val: number) => {
  let assetRatios = props.ratios;
  if (val === 1) assetRatios = []; // 视频不需要分辨率
  get(Store).material.setParams?.({ assetRatios });
  get(Store).material.setFormatType(val);
};
const clearAll = () => {
  handleSelectAll(false);
};
const deleteItems = (vals: string[]) => {
  const newVals = get(checkValue).filter(id => !vals.includes(id));
  setSelected(newVals);
};

const simpleCheckedAssets = computed<TSimpleAsset[]>(() => props
  .list
  ?.filter(i => checkValue?.value
    ?.includes?.(i.id))
  .map(i => ({
    AssetID: i.id,
    AssetName: i.title,
    originName: i.ext.name,
    type: i.type,
    materialExt: i.ext.material_ext,
  })));

const tableRef = ref();
const {
  buttons,
} = useCreativeDialog({
  checkValue,
  simpleCheckedAssets,
  changeView,
  isTableView,
  tableRef,
  containerType: props.type,
  store: props.store,
});
const actions = buttons.value.filter(item => item.name === 'more-select');
const { showDetail, showPreview } = useDetailDialog('modal', props.store);
const { cols } = useTable({
  hideRename: true,
  methods: {
    showDetail,
    showPreview,
  },
});
const showCols = ['row-select', 'title', 'createdAt', 'opt'];

const mediaItems = ref<any>([]);
const viewDetail = (data: IMaterialItem) => {
  mediaItems.value[`mediaItem-${data.id}`].viewDetail();
};

const onPageChange = (currenPage: number, pageInfo: any) => {
  props.store.material.setPageInfo(currenPage, pageInfo.pageSize);
};

const getDuration = (item: IMaterialItem) => formatDuration(item.ext.duration);

defineExpose({
  setSelected,
  checkValue,
  deleteItems,
  clearAll,
  setSelectedType,
});
</script>

<style scoped lang="scss">
.list-container {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.03);
  @apply rounded-t-extraLarge rounded-b-none bg-white-primary;
  height: 100%;
  display: flex;
  flex-direction: column;
  .media-item {
    position: relative;
    height: 250px;
    .duration {
      position: absolute;
      left: 8px;
      bottom: 92px;
      @apply text-white-primary bg-black-disabled;
    }
  }
}
</style>
