<template>
  <div v-loading="loading" class="media-list w-[100%]">
    <div v-if="maxNum !== -1" class="text-black-disabled">
      (Selected {{ selectedNum }} / {{ maxNum }})
    </div>
    <div v-if="maxImageNum > 0" class="text-black-disabled">
      Image (Selected {{ imageList.length }} / {{ maxImageNum }})
    </div>
    <div v-if="imageList.length > 0" class="media-type">
      <div class="top-tool flex justify-between mb-[20px]">
        <span v-if="maxNum !== -1">Image</span>
        <span v-else />
        <t-button
          theme="primary" :disabled="isReviewing || disabled" variant="text"
          @click="clearMediaList('image')"
        >
          Remove All
        </t-button>
      </div>
      <div class="flex flex-wrap">
        <SMaterialItem
          v-for="(item, index) in imageList"
          :key="item.asset_id"
          :disabled="isReviewing || disabled"
          :data="item"
          :depot-token="depotToken"
          :class="{'mr-[40px]': index % 2 === 0}"
          @delete="deleteMediaItem"
        />
      </div>
    </div>
    <div v-if="maxVideoNum > 0" class="text-black-disabled mt-[16px]">
      Video (Selected {{ videoList.length }} / {{ maxVideoNum }})
    </div>
    <div v-if="videoList.length > 0" class="media-type">
      <div class="top-tool flex justify-between mb-[20px]">
        <span v-if="maxNum !== -1">Videos</span>
        <span v-else />
        <t-button
          theme="primary" variant="text" :disabled="isReviewing || disabled"
          @click="clearMediaList('video')"
        >
          Remove All
        </t-button>
      </div>
      <div class="flex flex-wrap">
        <SMaterialItem
          v-for="(item, index) in videoList"
          :key="item.asset_id"
          :disabled="isReviewing || disabled"
          :data="item"
          :depot-token="depotToken"
          :class="{'mr-[40px]': index % 2 === 0}"
          @delete="deleteMediaItem"
        />
      </div>
    </div>
    <t-button
      theme="primary" variant="text" :disabled="!canAdd || isReviewing"
      @click="selectMedia"
    >
      <template #icon><add-icon /></template>Add
    </t-button>
    <MaterialSelect
      ref="materialSelect"
      :m-key="mKey"
      :tip="tip"
      :video-tip="videoTip"
      :ratios="ratios"
      :max-num="maxNum"
      :max-img-num="maxImageNum"
      :min-img-num="minImageNum"
      :max-video-num="maxVideoNum"
      :min-video-num="minVideoNum"
      :split-check-num="splitCheckNum"
      :radios-limit="radiosLimit"
      :disabled-aix="adPublished"
      @select="onSelect"
    />
    <t-dialog
      v-model:visible="nameConfirmVisible"
      class="name-confirm-dialog"
      attach="body"
      width="600"
      :close-on-overlay-click="false"
      header=""
    >
      <template #body>
        <div>
          Do you want to rename your ad as "{{ newName }}" ?
        </div>
      </template>
      <template #cancelBtn>
        <t-button class="ml-[10px]" theme="default" @click="onNameNoChange">No</t-button>
      </template>
      <template #confirmBtn>
        <t-button class="ml-[10px]" theme="primary" @click="onNameConfirm">Confirm</t-button>
      </template>
    </t-dialog>
  </div>
</template>
<script setup lang="ts">
import { AddIcon } from 'tdesign-icons-vue-next';
import { computed, onMounted, PropType, ref, watch } from 'vue';
import type { RadiosLimit } from './utils';
import type { MediaType } from '../../template/type';
import type { MaterialItem, MSelectItem } from '../../template/media.type';
import { generateData, normalizeFromMedia, normalizeToMedia } from './utils';
import { storeToRefs } from 'pinia';
import { useCommonParams } from '../../template/compose/currentCompose';
import { Level, MaterialMediaMap } from '../../template/config';
import { useTreeListDataStore } from '@/store/trade/template.store';
import {
  btMaterialsDetailByAssetId, btMaterialsDetailByResourceName, getMaterialDepotToken, getMaterialFromChannel,
} from 'common/service/td/media';
import { isNodePublished } from '../../template/utils-common';
import MaterialSelect from './MaterialSelect.vue';
import { EBusEmit } from '../../template/event';
import { useDialog } from 'common/compose/useDialog';
import SMaterialItem from './MaterialItem.vue';

const props = defineProps({
  maxVideoNum: { type: Number, default: -1 }, // 最大视频限制个数，-1不限制
  minVideoNum: { type: Number, default: -1 }, // 最小视频限制个数，-1不限制
  maxImageNum: { type: Number, default: -1 }, // 最大图片限制个数，-1不限制
  minImageNum: { type: Number, default: -1 }, // 最小图片限制个数，-1不限制
  maxNum: { type: Number, default: -1 }, // 总共限制个数，不区分视频图片，适用tiktok渠道
  splitCheckNum: { type: Boolean, default: false }, // 视频和图片分开校验数量
  modelValue: {
    type: [Object, Array] as PropType<any>,
    default: () => [],
  },
  disabled: { type: Boolean, default: false },
  isReviewing: { type: Boolean, default: false },
  mKey: { type: String, default: '' },
  tip: { type: String, default: '' }, // 图片文案提示
  videoTip: { type: String, default: '' }, // 视频文案提示
  ratios: { // 分辨率
    type: Array as PropType<string[]>,
    required: false,
    default: () => [],
  },
  radiosLimit: { // 分辨率限制，不传给后台，只用于选择的校验和禁用
    type: Object as PropType<RadiosLimit | undefined>,
    default: () => undefined,
  },
  confirmName: { type: Boolean, default: false }, // 是否需要确认名称修改
});

const emits = defineEmits(['update:modelValue']);

const { current, channelConfig } = storeToRefs(useTreeListDataStore()); // 响应式树结构
const loading = ref(false);
const { game_code: gameCode, media, account_id: accountId } = useCommonParams();
const imageList = ref<MaterialItem[]>([]);
const videoList = ref<MaterialItem[]>([]);
const isArr = props.modelValue instanceof Array;

const adPublished = computed(() => {
  const adData = current.value.adNode.data;
  return isNodePublished(Level.AdLevel, adData, channelConfig.value);
});

// 监听mediaid变化，从后台拉取详细数据展示
watch(() => JSON.stringify(props.modelValue), () => {
  const data = normalizeFromMedia(media, props.modelValue);
  getMaterialDetail(data.imageIds || [], data.videoIds || []);
}, {
  immediate: true,
});
const selectedNum = computed(() => imageList.value.length + videoList.value.length);
const canAdd = computed(() => {
  let valid = true;
  if (props.maxNum !== -1) valid = selectedNum.value < props.maxNum;
  if (props.maxVideoNum !== -1) valid = videoList.value.length < props.maxVideoNum;
  if (props.maxImageNum !== -1) valid = imageList.value.length < props.maxImageNum;
  if (props.maxVideoNum !== -1 && props.maxImageNum !== -1) {
    valid = videoList.value.length < props.maxVideoNum || imageList.value.length < props.maxImageNum;
  }
  return valid;
});

const depotToken = ref({
  arthubCode: '',
  publicToken: '',
});

// 打开素材选择弹框，将处理后的数据传过去
const materialSelect = ref();
const selectMedia = () => {
  const selectedList = imageList.value.concat(videoList.value).map(item => ({
    id: item.asset_id,
    title: item.asset_name || item.title,
    type: item.isVideo ? 'video' : 'image',
    poster: item.preview_url,
    ext: item,
  }));
  materialSelect.value.show(selectedList);
};

// 同步素材数据
const syncMedia = (data: MaterialItem[], changeAdName = true) => {
  const videoMaterials = data.filter((item: MaterialItem) => item.isVideo);
  const imageMaterials = data.filter((item: MaterialItem) => !item.isVideo);
  imageList.value = imageMaterials;
  videoList.value = videoMaterials;
  const mediaData = normalizeToMedia(media, {
    imageList: imageMaterials, videoList: videoMaterials,
  });
  if (isArr) {
    // 数组类型，直接赋值
    emits('update:modelValue', mediaData);
  } else {
    // 对象类型，需要包含其他字段值
    emits('update:modelValue', {
      ...props.modelValue,
      ...mediaData,
    });
  }
  EBusEmit('mediaSelect', {
    key: props.mKey,
    mediaList: data,
    mediaData,
    changeAdName,
  });

  // 用于rules判断，能够获取当前选中的素材
  data.forEach((item) => {
    if (!item.detail) {
      // eslint-disable-next-line no-param-reassign
      item.detail = {
        width: item.material_ext.video.width,
        high: item.material_ext.video.high,
        cover: '',
      };
    }
  });
  EBusEmit('updateNodeData', { level: 'ad', key: `${props.mKey}_list`, val: data });
};

/**
 * 选中勾选中的素材
 * @params
 * sData 选中的素材
 * from select-从素材选择弹框 remove-删除或者清除操作
 */
const curSelectData = ref();
const newName = ref(''); // 需要修改的新名称，取选中的素材名称
const onSelect = (sData: MSelectItem[] | MaterialItem[]) => {
  curSelectData.value = sData;
  if (props.confirmName) {
    newName.value = (sData[0] as MaterialItem).name;
    showNameDialog();
  } else {
    const data = generateData(sData as MSelectItem[]);
    syncMedia(data);
    materialSelect.value.hideDialog();
  }
};

/*
 * ad name弹框修改确认
 */
const { visible: nameConfirmVisible, show: showNameDialog, hide: onNameCancel } = useDialog();
const onNameConfirm = () => {
  const data = generateData(curSelectData.value as MSelectItem[]);
  syncMedia(data);
  materialSelect.value.hideDialog();
  onNameCancel();
  EBusEmit('updateNodeData', { level: 'ad', key: 'ad_name_editable', val: true });
};
const onNameNoChange = () => {
  const data = generateData(curSelectData.value as MSelectItem[]);
  syncMedia(data, false);
  materialSelect.value.hideDialog();
  onNameCancel();
};

/**
 * 批量查询素材详情，总共调2次接口
 * 判断要查询的id是否有不在已请求的数据里的，有的话才发起请求
 * 未上传的素材：bt_get_material_info_detail
 * 已上传的素材：get_material_name
 */
async function getMaterialDetail(imgIds: string[], videoIds: string[]) {
  const key = `${props.mKey}_list`; // 用于rules判断，能够获取当前选中的素材
  const none = imgIds.length === 0 && videoIds.length === 0;
  const curImgIds = imageList.value.map(item => item.backendId);
  const curVideoIds = videoList.value.map(item => item.backendId);
  const allInImg = imgIds.every(imgId => curImgIds.includes(imgId));
  const allInVideo = videoIds.every(videoId => curVideoIds.includes(videoId));

  // 待查询的数据都已存在，忽略请求，将已存在的数据赋值
  if (!none && allInImg && allInVideo) {
    const newImgList = imageList.value.filter(item => imgIds.includes(item.backendId));
    const newVideoList = videoList.value.filter(item => videoIds.includes(item.backendId));
    imageList.value = newImgList;
    videoList.value = newVideoList;
    EBusEmit('updateNodeData', { level: 'ad', key, val: newImgList.concat(newVideoList) });
    EBusEmit('validate', { level: 'ad' });
    return;
  }

  loading.value = true;
  const commonParams = {
    game_code: gameCode,
    media: MaterialMediaMap[media as MediaType],
    account_id: accountId,
  };
  const noUploadIds: string[] = []; // 未上传素材asset_ids，fake_前缀在请求时需要去掉
  const uploadIds: string[] = []; // 已上传素材resource_name
  imgIds.forEach((id) => {
    if (/fake_/.test(id)) noUploadIds.push(id);
    else uploadIds.push(id);
  });
  videoIds.forEach((id) => {
    if (/fake_/.test(id)) noUploadIds.push(id);
    else uploadIds.push(id);
  });

  const pList = []; // 请求列表
  const createEmpty = () => new Promise(resolve => resolve([])); // 返回空数组
  // 并行请求，查询素材详情
  if (noUploadIds.length > 0) {
    const originAssetIds = noUploadIds.map(id => id.replace('fake_', ''));
    pList.push(btMaterialsDetailByAssetId({ asset_ids: originAssetIds }));
  } else {
    pList.push(createEmpty());
  }
  if (uploadIds.length > 0) {
    pList.push(btMaterialsDetailByResourceName({
      ...commonParams, format_type: 0, resource_names: uploadIds, imgIds, videoIds,
    }));
  } else {
    pList.push(createEmpty());
  }

  let resImgList: MaterialItem[] = []; // 最终展示的图片列表
  let resVideoList: MaterialItem[] = []; // 最终展示的视频列表
  const resList = await Promise.all(pList) as MaterialItem[][];
  loading.value = false;
  const [noUploadItems, uploadItems] = resList;
  noUploadItems.forEach((item) => {
    if (item.isVideo) resVideoList.push(item);
    else resImgList.push(item);
  });
  uploadItems.forEach((item) => {
    if (item.isVideo) resVideoList.push(item);
    else resImgList.push(item);
  });

  // 调用素材库接口没获取到数据，说明业务没有接入，需要单独调用渠道接口
  const resIdList = resVideoList.concat(resImgList).map(item => item.resource_name); // 调用接口返回数据的id列表
  const leftIds = uploadIds.filter(id => !resIdList.find(item => item === id)); // 剩下不在结果列表里的id列表
  const leftImgIds = leftIds.filter(id => imgIds.includes(id)); // 剩余图片列表
  const leftVideoIds = leftIds.filter(id => videoIds.includes(id)); // 剩余视频列表
  if (leftIds.length > 0) {
    let extData: any;
    if (media === 'Twitter') {
      const adData = current.value.adNode.data;
      extData = adData.tweet.media_datas || [];
    }
    const res = await getMaterialFromChannel({
      media: media.toLowerCase(),
      game_code: gameCode,
      account_id: accountId,
      imgIds: leftImgIds,
      videoIds: leftVideoIds,
    }, extData);
    resImgList = resImgList.concat(res.filter(item => !item.isVideo));
    resVideoList = resVideoList.concat(res.filter(item => item.isVideo));
  }

  imageList.value = resImgList;
  videoList.value = resVideoList;

  EBusEmit('updateNodeData', { level: 'ad', key, val: resImgList.concat(resVideoList) });
  EBusEmit('validate', { level: 'ad' });
}


const clearMediaList = (type: string) => {
  if (type === 'video') videoList.value = [];
  else imageList.value = [];
  syncMedia(imageList.value.concat(videoList.value));
};

const deleteMediaItem = (type: string, item: MaterialItem) => {
  const targetList = type === 'video' ? videoList : imageList;
  targetList.value = targetList.value.filter(data => data.backendId !== item.backendId);
  syncMedia(imageList.value.concat(videoList.value));
};

// 获取预览接口所需的参数
onMounted(async () => {
  const data = await getMaterialDepotToken({ game: gameCode });
  if (data) {
    depotToken.value = {
      arthubCode: data.arthub_code,
      publicToken: data.public_token,
    };
  }
});
</script>
<style lang="scss" scoped>
.media-list {
  .media-type {
    @apply p-[20px] border border-gray-placeholder rounded-default w-[100%] max-w-[688px] mt-[20px];
  }
}
</style>
