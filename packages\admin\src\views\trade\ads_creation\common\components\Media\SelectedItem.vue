<template>
  <div ref="selectedItem" class="relative inline-block selected-item mr-[10px] mt-[5px] mb-[5px]">
    <img class="selected-preview-img" :src="poster" alt="">
    <SvgIcon
      v-if="type === 'video'"
      class="absolute inset-0 m-auto"
      name="play-btn"
      size="14"
      color="var(--aix-text-color-white-primary)"
    />
    <div
      v-if="isHovered"
      class="absolute left-[0] top-[0] w-[30px] h-[30px] bg-black-primary-primary bg-opacity-50
      cursor-pointer rounded-default"
      @click="deleteItem"
    >
      <SvgIcon
        class="absolute inset-0 m-auto"
        name="close"
        size="14"
        color="var(--aix-text-color-white-primary)"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { useElementHover } from '@vueuse/core';
import { SvgIcon } from 'common/components/SvgIcon';

defineProps({
  type: {
    type: String,
    required: true,
  },
  poster: {
    type: String,
    default: '',
  },
});
const emit = defineEmits(['delete']);

const selectedItem = ref();
const isHovered = useElementHover(selectedItem);

const deleteItem = () => {
  emit('delete');
};
</script>
<style lang="scss">
.selected-preview-img {
  display: inline-block;
  width: 30px;
  height: 30px;
  @apply rounded-default bg-black-primary;
  object-fit: contain;
}
</style>
