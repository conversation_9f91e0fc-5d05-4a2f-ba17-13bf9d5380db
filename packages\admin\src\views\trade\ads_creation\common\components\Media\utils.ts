import type { AdCreative, MediaInfos } from '../../../tiktok/type';
import type { MediaType, TreeNode } from '../../template/type';
import type { MaterialItem, MSelectItem } from '../../template/media.type';
import { useTreeListDataStore } from '@/store/trade/template.store';
import { useCommonParams, useCurrentData } from '../../template/compose/currentCompose';
import { addUpload } from 'common/service/creative/library/get-aix-task';
import dayjs from 'dayjs';

interface MaterialList {
  videoList: MaterialItem[],
  imageList: MaterialItem[],
}

export interface RadiosLimit {
  image?: string[], // 图片必须满足的分辨率
  video?: string[], // 视频必须满足的分辨率
  imageAndVideo?: string[], // 图片和视频必须同时满足的分辨率
}

export function normalizeFromMedia(media: MediaType, data: any): { videoIds: string[], imageIds: string[] } {
  if (media === 'TikTok') return normalizeFromTiktok(data);
  if (media === 'Google') return normalizeFromGG(data);
  if (media === 'Facebook') return normalizeFromFB(data);
  if (media === 'Twitter') return normalizeFromTwitter(data);
  return { videoIds: [], imageIds: [] };
}

export function normalizeToMedia(
  media: MediaType,
  { videoList = [], imageList = [] }: MaterialList,
) {
  if (media === 'TikTok') return normalizeToTiktok({ videoList, imageList });
  if (media === 'Google') return normalizeToGG({ videoList, imageList });
  if (media === 'Facebook') return normalizeToFB({ videoList, imageList });
  if (media === 'Twitter') return normalizeToTwitter({ videoList, imageList });
}

/**
 * 将gg后台保存的数据处理成id列表
 */
export function normalizeFromGG(data: { images: string[], videos: string[] }) {
  return { videoIds: data.videos, imageIds: data.images };
}

/**
 * 处理成gg渠道后台保存的数据结构
 * {
 *   images: string[],
 *   videos: string[],
 * }
 */
export function normalizeToGG({ videoList = [], imageList = [] }: MaterialList) {
  const videos = videoList.map(item => item.backendId);
  const images = imageList.map(item => item.backendId);
  return { videos, images };
}

/**
 * 将tiktok后台保存的数据处理成id列表
 */
export function normalizeFromTiktok(data: AdCreative | MediaInfos[]) {
  const { adgroup } = useCurrentData();
  const creative = adgroup.creative_material_mode === 'DYNAMIC';
  const videoIds: string[] = [];
  const imageIds: string[] = [];
  if (creative) {
    const adData = data as MediaInfos[];
    if (adData.length > 0) {
      adData.forEach((item) => {
        if (item.video_info.video_id) videoIds.push(item.video_info.video_id);
        else imageIds.push(item.image_infos[0].image_id);
      });
    }
  } else {
    const adData = data as AdCreative;
    if (adData.video_id) {
      videoIds.push(adData.video_id);
    } else if (adData.image_ids) {
      if (adData.image_ids[0]) imageIds.push(adData.image_ids[0]);
    }
  }
  return { videoIds, imageIds };
}

/**
 * 处理成tiktok渠道后台保存的数据结构
 * @创意ad
 * MediaInfos[]
 *
 * @非创意ad
 * {
 *   video_id: string,
 *   image_ids: string[],
 * }
 */
export function normalizeToTiktok({ videoList = [], imageList = [] }: MaterialList): AdCreative | MediaInfos[] {
  const { adgroup } = useCurrentData();
  let data;
  const creative = adgroup.creative_material_mode === 'DYNAMIC';
  if (creative) {
    data = [];
    videoList.forEach((item) => {
      data.push({
        video_info: { video_id: item.backendId, file_name: item.name },
        image_infos: [{ image_id: item.cover_hash || '', file_name: item.name }], // 带上封面信息
      });
    });
    imageList.forEach((item) => {
      data.push({
        video_info: { video_id: '', file_name: '' },
        image_infos: [{ image_id: item.backendId, file_name: item.name }],
      });
    });
  } else {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    data = { video_id: '', image_ids: [] } as AdCreative;
    if (videoList.length > 0) {
      data.video_id = videoList[0].backendId;
      data.image_ids[0] = videoList[0].cover_hash || ''; // 带上封面信息
    } else if (imageList.length > 0) {
      data.image_ids[0] = imageList[0].backendId;
    }
  }
  return data;
}

export function normalizeFromTwitter(datas: { media_key: string, type: string}[]) {
  const videoIds: string[] = [];
  const imageIds: string[] = [];
  datas.forEach((item) => {
    if (item.type === 'IMAGE') {
      imageIds.push(item.media_key);
    } else {
      videoIds.push(item.media_key);
    }
  });
  return { videoIds, imageIds };
};

export function normalizeToTwitter({ videoList = [], imageList = [] }: MaterialList) {
  const result: { media_key: string, type: string}[] = [];
  videoList.forEach((item) => {
    result.push({
      media_key: item.backendId,
      type: 'VIDEO',
    });
  });
  imageList.forEach((item) => {
    result.push({
      media_key: item.backendId,
      type: 'IMAGE',
    });
  });
  return result;
};

/**
 * 处理fb后台返回的数据
 */
export function normalizeFromFB(data: { images: string[], videos: string[] }) {
  const videoIds = data.videos || [];
  const imageIds = data.images || [];
  return { videoIds, imageIds };
}

/**
 * 处理成fb后台格式
 */
export function normalizeToFB({ videoList = [], imageList = [] }: MaterialList) {
  const videos = videoList.map(item => ({
    image_url: item.detail?.cover || item.poster,
    image_hash: item.cover_hash || '', // 带上封面信息
    video_id: item.backendId,
  }));
  const images = imageList.map(item => item.backendId);
  return { select_video: videos, select_image_hash_list: images };
}

/**
 * 上传未同步的素材
 * TODO: 区分渠道
 */
export const uploadAssets = () => {
  const { media } = useCommonParams();
  let ids: string[] = [];
  const { treeList } = useTreeListDataStore();

  // 获取当前所有选中素材的asset_id
  treeList.forEach((campaignNode: TreeNode) => {
    campaignNode.children?.forEach((adgroupNode) => {
      adgroupNode.children?.forEach((adNode) => {
        if (media === 'TikTok') {
          const isCreative = adgroupNode.data.creative_material_mode === 'DYNAMIC';
          if (isCreative) {
            adNode.data.media_infos.forEach((item: MediaInfos) => {
              if (item.video_info.video_id) ids.push(item.video_info.video_id);
              else ids.push(item.image_infos[0].image_id);
            });
          } else {
            const detail = adNode.data.module_ad_detail;
            if (detail.ad_format === 'SINGLE_VIDEO') {
              ids.push(detail.ad_creative.video_id);
            } else {
              ids.push(detail.ad_creative.image_ids[0]);
            }
          }
        }
        if (media === 'Google') {
          const {
            images = [],
            videos = [],
            square_marketing_images: qmImages = [],
            marketing_images: mImages = [],
            square_logo_images: slImages = [],
            logos: lImages = [],
            logo_images: lImages2 = [],
          } = adNode.data;
          const mediaIds = [...new Set([
            ...images, ...videos, ...qmImages, ...mImages, ...slImages, ...lImages, ...lImages2,
          ])];
          ids = ids.concat(mediaIds);
        }
        if (media === 'Facebook') {
          const {
            select_image_hash_list: imageIds,
            select_video: videos,
          } = adNode.data;
          const videosIds = videos.map((item: { video_id: string }) => item.video_id);
          ids = ids.concat(imageIds).concat(videosIds);
        }
        if (media === 'Twitter') {
          const {
            tweet: { media_datas: medias },
          } = adNode.data;
          const mediaIds = medias.map((item: { media_key: string }) => item.media_key);
          ids = ids.concat(mediaIds);
        }
      });
    });
  });
  ids = ids.filter(id => /fake_/.test(id)).map(id => id.replace('fake_', ''));
  if (ids.length === 0) return;

  const mediaPath = `${media}_${dayjs().format('YYYY-MM-DD')}`;
  addUpload({
    assetIDs: ids,
    toChannels: [
      {
        media: media as MediaType,
        path: mediaPath,
        accounts: '', // 全部账号
      },
    ],
    notifyDays: 0,
  });
};

/*
 * 根据assetid去重，优先保留已同步的素材
 */
export const uniqMedias = (medias: MSelectItem[]): MSelectItem[] => {
  const mediaMap: {
    [key: string]: MSelectItem,
  } = {};
  medias.forEach((item) => {
    const id = item.ext.asset_id;
    if (!mediaMap[id]) {
      mediaMap[id] = item;
    } else if (!mediaMap[id].ext.resource_name) {
      mediaMap[id] = item;
    }
  });
  return Object.values(mediaMap);
};

// 处理select后回传的数据
export const generateData = (list: MSelectItem[]) => list.map((item) => {
  let id = item.ext.resource_name; // 优先取resource_name
  let assetRatio = item.ext.asset_ratio || '0';
  // 获取不到说明未上传，取asset_id。补上fade_前缀，计算分辨率
  if (!id) {
    id = `fake_${item.id}`;
    const { width, high } = item.ext.material_ext.video;
    if (width && high) {
      assetRatio = (width / high).toFixed(2);
    }
  }
  return {
    ...item,
    ...item.ext,
    backendId: id,
    isVideo: item.type === 'video',
    asset_ratio: assetRatio,
  };
});

// 获取分辨率
export const getRatio = (data: any) => {
  let width = 0;
  let high = 0;
  // 渠道素材
  if (data.ext.detail) {
    width = data.ext.detail?.width || 0;
    high = data.ext.detail?.high || 0;
  }
  // aix素材
  if (data.ext.material_ext) {
    width = data.ext.material_ext.video?.width || 0;
    high = data.ext.material_ext.video?.high || 0;
  }
  if (width && high) return `${width} × ${high}`;
  return '——';
};

// 判断选择的素材是否符合分辨率要求
export const validRadiosLimit = (radiosLimit: RadiosLimit, mediaList: MSelectItem[]) => {
  let valid = true;
  const images = mediaList.filter(item => item.type === 'image');
  const videos = mediaList.filter(item => item.type === 'video');
  if (radiosLimit.image) {
    valid = images.every(item => radiosLimit.image!.includes(item.asset_ratio));
  }
  if (radiosLimit.video) {
    const validVideo = videos.every(item => radiosLimit.video!.includes(item.asset_ratio));
    if (valid) valid = validVideo;
  }
  if (radiosLimit.imageAndVideo) {
    if (valid) {
      valid = radiosLimit.imageAndVideo.some((radio) => {
        const [imgRadio, videoRadio] = radio.split('|');
        return images.every(item => item.asset_ratio === imgRadio)
          && videos.every(item => item.asset_ratio === videoRadio);
      });
    }
  }
  return valid;
};
