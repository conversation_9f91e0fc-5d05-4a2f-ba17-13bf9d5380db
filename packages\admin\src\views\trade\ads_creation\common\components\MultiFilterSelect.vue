<!--
 * @Date: 2023-04-10 15:29:11
 * @LastEditors: maclerylin
 * @LastEditTime: 2023-04-20 16:49:07
-->
<template>
  <t-select
    clearable
    multiple
    filterable
    :min-collapsed-num="4"
    :placeholder="placeholder"
    :options="options"
    class="max-w-[688px] inline-block multi-filter-select"
    v-bind="$attrs"
  >
    <template #prefixIcon>
      <div class="flex justify-center pl-[8px]">
        <icon name="search" class="text-lg text-black-secondary" />
        <span
          class="inline-block pr-[7px] text-black-secondary w-[1px] h-[16px]"
          style="border-right: 1px solid var(--aix-border-color-black-disabled);"
        />
      </div>
    </template>
  </t-select>
</template>
<script setup lang="ts">
import { toRefs } from 'vue';
import { Icon } from 'tdesign-icons-vue-next';

const props = defineProps({
  options: {
    type: Array,
    default: () => [],
  },
  placeholder: {
    type: String,
    default: '',
  },
});

const { options } = toRefs(props);

</script>
<style lang="scss">
.t-select-option-group {
  .t-select-option {
    height: initial;
    .t-checkbox {
      height: 40px;
    }
  }
}

</style>
