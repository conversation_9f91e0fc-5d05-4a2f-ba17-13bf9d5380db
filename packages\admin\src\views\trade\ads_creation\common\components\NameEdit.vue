<template>
  <svg-icon
    class="ml-[10px] cursor-pointer" name="pencil" color="var(--aix-text-color-black-placeholder)"
    @click="triggerEdit"
  />
</template>
<script lang="ts" setup>
import { SvgIcon } from 'common/components/SvgIcon';
import { toRefs } from 'vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
});

const { modelValue } = toRefs(props);
const emits = defineEmits(['update:modelValue']);

const triggerEdit = () => {
  emits('update:modelValue', !modelValue.value);
};
</script>
