<template>
  <div class="w-[240px]">
    <div class="nav-title">
      {{ t('campaignList') }}
    </div>
    <t-tree
      ref="tree"
      :actived="activeIds"
      :data="treeDataList"
      activable
      hover
      :transition="false"
      expand-parent
      value-mode="all"
      expand-all
      :keys="{ value: 'id', label: 'label', children: 'children' }"
      @active="onActive"
    >
      <template #icon="{ node }">
        <icon
          :name="getIconName(node.data)"
          class="ml-[2px]"
          :style="{
            color: activeIds.includes(node.value) ? '#1A76F1': 'var(--aix-text-color-black-secondary)'
          }"
        />
        <div class="border-left" />
      </template>
      <template #label="{ node }">
        <div class="node-content flex justify-between items-center text-xs">
          <span
            :style="{
              color: activeIds.includes(node.value) ? '#1A76F1': 'var(--aix-text-color-black-secondary)'
            }"
          >
            <t-tooltip
              overlay-class-name="nav-label-name"
              :content="getLabel(node.data)" placement="bottom"
              show-arrow
            >
              <span
                class="overflow-ellipsis overflow-x-hidden inline-block	align-middle node-text"
                :style="{
                  width: `${getStyleOfLabel(node.data.level)}px`
                }"
              >
                {{ getLabel(node.data) }}
              </span>
            </t-tooltip>
          </span>
          <icon
            v-if="showSuccess(node.data)"
            name="check-circle-filled"
            class="text-success-primary"
          />
          <icon
            v-if="showErr(node.data)"
            name="error-circle-filled"
            class="text-error-primary"
          />
        </div>
      </template>
    </t-tree>
  </div>
</template>
<script setup lang="ts">
import { Icon } from 'tdesign-icons-vue-next';
import { computed, onMounted, ref, toRefs } from 'vue';
import type { TreeNode } from '../template/type';
import { Level, LEVEL_LIST } from '../template/config';
import type { TreeNodeModel } from 'tdesign-vue-next';
import { I18N_TD_FORM } from 'common/const/i18n';
import { useI18n } from 'common/compose/i18n';
import { useTreeListDataStore } from '@/store/trade/template.store';
import { EBusOn } from '../template/event';

const { current } = useTreeListDataStore();
const { campaignNode, adgroupNode, adNode } = toRefs(current);
const { channelConfig } = toRefs(useTreeListDataStore());
const { t } = useI18n([I18N_TD_FORM]);

withDefaults(defineProps<{
  treeDataList: TreeNode[],
}>(), {
  treeDataList: () => [],
});

const emit = defineEmits(['change']);

const tree = ref(); // tree对象，同步进行增删改操作

onMounted(() => {
  // 监听树结构变化，同步调用tree api更新（ps：tree不支持data的双向绑定）
  EBusOn((evt: unknown, data: any) => {
    if (evt !== 'treeUpdate' || !tree.value) return;
    const { type } = data; // 操作类型
    const targetNode = data.node as TreeNode; // 操作节点
    if (type === 'add') {
      addTreeNode(targetNode);
    } else if (type === 'update') {
      updateTreeNode(targetNode, data);
    } else if (type === 'delete') {
      deleteTreeNode(targetNode);
    }
  });
});

const showErr = (node: TreeNode) => node.status === -1;
const showSuccess = (node: TreeNode) => node.status === 1;

// 通过新节点查找旧的value（ps：t-tree每个item的value不会跟随data.id变化）
const findNodeItemVal = (id: string) => {
  const item = tree.value.getItems().find((item: any) => item.data.id === id);
  return item.value;
};

// 增加节点
const addTreeNode = (node: TreeNode) => {
  if (node.parentNode) {
    const oldId = findNodeItemVal(node.parentNode.id);
    tree.value.appendTo(oldId, node);
  } else {
    tree.value.appendTo('', node); // 插入根节点
  }
};

// 修改节点
const updateTreeNode = (node: TreeNode, data: any) => {
  const nodeItem = tree.value.getItem(node.id);
  // 直接修改data数据，能够同步更新，不要调用setData（有bug）
  const nameKey = channelConfig.value.keyConfig.levelName[LEVEL_LIST[data.level]][data.level as Level];
  // 目前主要修改status和name
  nodeItem.data.staus = data.status;
  nodeItem.data.data[nameKey] = data[nameKey];
};

// 删除节点
const deleteTreeNode = (node: TreeNode) => {
  tree.value.remove(node.oldId);
  tree.value.remove(node.id);
};

// 当前选中的id
const activeIds = computed(() => {
  if (!campaignNode.value) return [];
  const campaignId = campaignNode.value.oldId || campaignNode.value.id;
  const adgroupId = adgroupNode.value.oldId || adgroupNode.value.id;
  const adId = adNode.value.oldId || adNode.value.id;
  return [campaignId, adgroupId, adId];
});
const onActive = (vals: string[], { node: treeInfo }: { node: TreeNodeModel}) => {
  const targetId = treeInfo.value;
  const curIds = [campaignNode.value.id, adgroupNode.value.id, adNode.value.id];
  const { level } = treeInfo.data;
  const contentBox = document.querySelector('.form-edit-block') as HTMLElement;
  const targetLevelBox = document.querySelector(`.${LEVEL_LIST[level]}-level`) as HTMLElement;
  contentBox.scrollTo(0, targetLevelBox.offsetTop - 40);
  // 单根节点切换，不需要处理
  if (!curIds.includes(targetId)) {
    emit('change', targetId, treeInfo.data.level);
  }
};

const getLabel = (data: TreeNode): string => {
  if (!channelConfig.value) {
    return '';
  }
  const nameKey = channelConfig.value.keyConfig.levelName[LEVEL_LIST[data.level]];
  return data.data[nameKey].split('-').filter((item: string) => item)
    .join('-');;
};

const getStyleOfLabel = (level: Level) => {
  if (level === Level.CampaignLevel) return 203;
  if (level === Level.AdgroupLevel) return 182;
  if (level === Level.AdLevel) return 158;
  return 158;
};

// 根据不同层级展示icon
const getIconName = (node: TreeNode) => ['folder', 'file-copy', 'file-paste'][node.level];
</script>
<style lang="scss" scoped>
:deep(.t-tree__label) {
  flex: 1;
  padding: 0;
  margin-left: 8px;
  > span {
    display: block;
  }
  &:hover {
    .node-text {
      color: var(--td-brand-color-9) !important;
    }
    background: initial !important;
  }
}
:deep(.text-success-primary) {
  color: var(--aix-text-color-success-primary) !important;
}
:deep(.text-error-primary) {
  color: var(--aix-text-color-error-primary) !important;
}
:deep(.t-tree__item) {
  display: flex;
  padding-top: 0!important;
  padding-bottom: 0!important;
  &.t-is-active {
    .border-left {
      position: absolute;
      width: 2px;
      height: 22px;
      left: -24px;
      @apply bg-brand;
    }
    .t-tree__label {
      background: initial;
    }
  }
  .node-content {
    .t-icon {
      position: absolute;
      right: 0;
    }
  }
}
.nav-title {
  @apply text-base text-black-primary;
  font-weight: bold;
  margin-bottom: 16px;
}
</style>
<style lang="scss">
.nav-label-name {
  .t-popup__content {
    word-break: break-all;
  }
}
</style>
