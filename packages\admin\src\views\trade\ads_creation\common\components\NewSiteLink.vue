<template>
  <div class="flex w-[100%]">
    <span v-if="props.isReviewing">
      {{ optionsText }}
    </span>
    <t-select
      v-if="!props.isReviewing"
      v-model="datas"
      clearable
      multiple
      filterable
      :min-collapsed-num="4"
      placeholder="Add an sitelinks"
      class="max-w-[688px] inline-block multi-filter-select mr-[8px]"
    >
      <template #prefixIcon>
        <div class="flex justify-center pl-[8px]">
          <icon name="search" class="text-lg text-black-secondary" />
          <span
            class="inline-block pr-[7px] text-black-secondary w-[1px] h-[16px]"
            style="border-right: 1px solid var(--aix-border-color-black-disabled);"
          />
        </div>
      </template>
      <t-option
        v-for="item in sitelinksOptions" :key="item.value"
        :value="item.value"
        :label="item.label"
      >
        <div class="flex items-center justify-between options-hover">
          <span>{{ item.label }}</span>
          <span @click.stop.prevent="() => { editSitelink(item) }">
            <icon name="edit-1" class="text-lg text-black-secondary" />
          </span>
        </div>
      </t-option>
    </t-select>
    <t-link v-if="!props.isReviewing" theme="primary" @click="addNewSitelink">New sitelink</t-link>
    <Teleport to=".form-edit-block">
      <t-drawer
        v-model:visible="sitelinkDrawerVisible"
        placement="right"
        size="650px"
        :close-on-esc-keydown="false"
        :on-close="() => resetFormData()"
        :on-confirm="() => (sitelinkDrawerVisible = false)"
        header="New sitelink"
        to="body"
        style="z-index: 6000"
      >
        <t-form-item label="sitelink name">
          <div class="h-[50px] flex-1">
            <div
              class="t-form__controls "
              :class="!newSitelinksData.sitelink_asset.link_text && isVerify ? 't-is-error' : ''"
            >
              <div class="t-form__controls-content max-w-[688px]">
                <t-input
                  v-model="newSitelinksData.sitelink_asset.link_text"
                  :disabled="isEditSitelink"
                  placeholder="Please input sitelink text"
                />
                <div
                  v-if="!newSitelinksData.sitelink_asset.link_text && isVerify"
                  class="t-input__extra"
                >
                  Sitelink text	is required！
                </div>
              </div>
            </div>
          </div>
        </t-form-item>
        <t-form-item label="Description line 1(optional)	">
          <div class="h-[50px] flex-1">
            <div
              class="t-form__controls"
              :class="newSitelinksData.sitelink_asset.description2
                &&!newSitelinksData.sitelink_asset.description1 && isVerify ? 't-is-error' : ''"
            >
              <div class="t-form__controls-content max-w-[688px]">
                <t-input
                  v-model="newSitelinksData.sitelink_asset.description1"
                  :disabled="isEditSitelink"
                  placeholder="Please input description line 1(optional)"
                />
                <div
                  v-if="newSitelinksData.sitelink_asset.description2
                    && !newSitelinksData.sitelink_asset.description1 && isVerify"
                  class="t-input__extra"
                >
                  Description line 1(optional) is required！
                </div>
              </div>
            </div>
          </div>
        </t-form-item>
        <t-form-item label="Description line 2(optional)	">
          <div class="h-[50px] flex-1">
            <div
              class="t-form__controls"
              :class="newSitelinksData.sitelink_asset.description1
                &&!newSitelinksData.sitelink_asset.description2 && isVerify ? 't-is-error' : ''"
            >
              <div class="t-form__controls-content max-w-[688px]">
                <t-input
                  v-model="newSitelinksData.sitelink_asset.description2"
                  :disabled="isEditSitelink"
                  placeholder="Please input description line 2(optional)"
                />
                <div
                  v-if="newSitelinksData.sitelink_asset.description1
                    &&!newSitelinksData.sitelink_asset.description2 && isVerify"
                  class="t-input__extra"
                >
                  Description line 2(optional) is required！
                </div>
              </div>
            </div>
          </div>
        </t-form-item>
        <t-form-item label="Final url">
          <div class="h-[50px] flex-1">
            <div
              class="t-form__controls "
              :class="!validateUrl(newSitelinksData.final_urls[0]) && isVerify ? 't-is-error' : ''"
            >
              <div class="t-form__controls-content max-w-[688px]">
                <t-input
                  v-model="newSitelinksData.final_urls[0]"
                  :disabled="isEditSitelink"
                  placeholder="https://www.example.com"
                />
                <div
                  v-if="!validateUrl(newSitelinksData.final_urls[0]) && isVerify"
                  class="t-input__extra"
                >
                  Enter a valid URL (ex. https://www.example.com)
                </div>
              </div>
            </div>
          </div>
        </t-form-item>
        <UrlParams
          v-model:final-url-suffix="newSitelinksData.final_url_suffix"
          v-model:tracking-url-template="newSitelinksData.tracking_url_template"
          v-model:url-custom-parameters="newSitelinksData.url_custom_parameters"
          v-model:final-mobile-urls="newSitelinksData.final_mobile_urls[0]"
          :is-show-mobile-url="true"
          header="Sitelink url options"
          :is-in-drawer="true"
          :disabled="isEditSitelink"
        />

        <template #footer>
          <div class="flex items-center justify-center">
            <t-button
              v-if="!isEditSitelink"
              class="mr-[16px]"
              :loading="newSitelinksLoading"
              @click="() => saveNewsitelinks()"
            >
              Save
            </t-button>
            <t-button variant="outline" @click="cancleSitelinks">Cancel</t-button>
          </div>
        </template>
      </t-drawer>
    </Teleport>
  </div>
</template>
<script lang="ts" setup>
import { cloneDeep } from 'lodash-es';
import { ref, defineComponent, onMounted, PropType, watch, computed } from 'vue';
import UrlParams from './UrlParams.vue';
import MultiFilterSelect from './MultiFilterSelect.vue';
import { createSitelink, getSitelinkList } from 'common/service/td/google/options';
import { useCommonParams } from '../../common/template/compose/currentCompose';
import { validateUrl } from '../template/utils-common';
import { Icon } from 'tdesign-icons-vue-next';

type SitelinkItem = {
  type: number,
  sitelink_asset: {
    link_text: string,
    description1: string,
    description2: string,
  },
  final_urls: string[],
  final_url_suffix: string
  tracking_url_template: string
  final_mobile_urls: string[],
  url_custom_parameters: { key: string, value: string } [],
  resource_name?: string,
  label?: string,
  value?: string,
};

defineComponent({
  UrlParams,
  MultiFilterSelect,
});

const props = defineProps({
  modelValue: {
    type: Array as PropType<SitelinkItem[]>,
    default: () => [],
  },
  isReviewing: {
    type: Boolean,
    default: false,
  },
});

const datas = ref<string[]>([]);
const emits = defineEmits(['update:modelValue']);
const isVerify = ref(false);

watch(() => props.modelValue, () => {
  if (props.modelValue.length !== datas.value.length) {
    datas.value = (props.modelValue || []).map(item => item?.resource_name || '').filter(item => item) || [];
  }
}, {
  immediate: true,
});


watch(() => datas.value, () => {
  emits('update:modelValue', sitelinksOptions.value.filter(item => datas.value.includes(item.resource_name || '')));
}, {
  deep: true,
});

const sitelinkDrawerVisible = ref(false);
const isEditSitelink = ref(false);

const defaultSitelinkDate: SitelinkItem = {
  type: 11,
  sitelink_asset: {
    link_text: '',
    description1: '',
    description2: '',
  },
  final_urls: [],
  final_url_suffix: '',
  tracking_url_template: '',
  final_mobile_urls: [],
  url_custom_parameters: [
    {
      key: '',
      value: '',
    },
  ],
};

const newSitelinksData = ref(cloneDeep(defaultSitelinkDate));
const newSitelinksLoading = ref(false);
const editSitelink = (item: SitelinkItem) => {
  const newItem = { ...item };
  if (newItem.url_custom_parameters.length === 0) {
    newItem.url_custom_parameters = [{ key: '', value: '' }];
  }
  newSitelinksData.value = newItem;
  sitelinkDrawerVisible.value = true;
  isEditSitelink.value = true;
};
const addNewSitelink = () => {
  isEditSitelink.value = false;
  sitelinkDrawerVisible.value = true;
};
const saveNewsitelinks = async () => {
  isVerify.value = true;
  const { sitelink_asset: { link_text: linkText }, final_urls: finalUrls } = newSitelinksData.value;
  if (!linkText || !validateUrl(finalUrls[0]) || (newSitelinksData.value.sitelink_asset.description1
    && !newSitelinksData.value.sitelink_asset.description2)) {
    return;
  }
  newSitelinksLoading.value = true;
  const params = useCommonParams();
  const sitelink = {
    sitelink: newSitelinksData.value,
  };
  const result = await createSitelink(params.game_code, params.account_id, sitelink);
  const { resource_name: resourceName } = result as any;
  await getSitelinkOptions();
  datas.value.push(resourceName);
  resetFormData();
};
const resetFormData = () => {
  // 值重置
  newSitelinksData.value = cloneDeep(defaultSitelinkDate);
  sitelinkDrawerVisible.value = false;
  newSitelinksLoading.value = false;
  isVerify.value = false;
};
const cancleSitelinks = () => {
  resetFormData();
};
const sitelinksOptions = ref<SitelinkItem[]>([]);
const getSitelinkOptions = async () => {
  const params = useCommonParams();
  sitelinksOptions.value = await getSitelinkList(params.game_code, params.account_id);
};

const optionsText = computed(() =>  sitelinksOptions.value.filter(item => datas.value.includes(item.resource_name || ''))
  ?.map(item => item.sitelink_asset.link_text)
  ?.join(','));

onMounted(() => {
  getSitelinkOptions();
});

</script>
<style lang="scss">
.options-hover {
  .t-icon {
    display: none;
  }
}
.options-hover:hover {
  .t-icon {
    display: block;
  }
}
</style>
