<template>
  <t-radio-group :value="modelValue" @change="onChange">
    <t-radio v-for="item in options" :key="item.value" :value="item.value">
      <template #label>
        <span>{{ item.label }}</span>
        <Tooltip v-if="item.tip" class="ml-[6px]" :content="item.tip">
          <InfoCircleIcon class="min-w-[1em]" />
        </Tooltip>
      </template>
    </t-radio>
  </t-radio-group>
</template>
<script setup lang="ts">
import { PropType, toRefs } from 'vue';
import { Tooltip } from 'tdesign-vue-next';
import { InfoCircleIcon } from 'tdesign-icons-vue-next';
import type { TdOptionProps } from 'tdesign-vue-next';
import { Level } from '../template/config';
import { importantChange } from './utils';

interface RadioItem extends TdOptionProps {
  tip?: string,
}

const props = defineProps({
  options: {
    type: Array as PropType<RadioItem[]>,
    default: () => [],
  },
  modelValue: {
    type: [Number, String],
    default: 0,
  },
  important: {
    type: Boolean,
    default: false, // 是否是重要字段，需要弹框提示
  },
  importantLevel: {
    type: Number as PropType<Level>,
    default: Level.CampaignLevel, // 重要字段的层级，默认为campaign
  },
});

const { options, modelValue, important, importantLevel } = toRefs(props);
const emits = defineEmits(['update:modelValue']);

const onChange = (val: number) => {
  if (val === props.modelValue) return;

  if (important.value) {
    importantChange(importantLevel.value, emits, val);
  } else {
    emits('update:modelValue', val);
  }
};
</script>
