<template>
  <div class="flex items-center">
    <span class="mr-[8px]">{{ props.operationType }}: </span>
    <t-select
      v-if="!props.isReviewing"
      v-model="val[0]"
      :options="fromOptionsComputed"
      class="mr-[8px]"
    />
    <span v-else class="mr-[8px]"> {{ fromOptionsComputed.find(item => item.value === val[0])?.label }}</span>
    <span class="mr-[8px]">to</span>
    <t-select
      v-if="!props.isReviewing"
      v-model="val[1]" :options="toOptionsComputed"
    />
    <span v-else> {{ toOptionsComputed.find(item => item.value === val[1])?.label }}</span>
  </div>
</template>
<script lang="ts" setup>
import { TdOptionProps } from 'tdesign-vue-next';
import { PropType, ref, watch, computed } from 'vue';
import { useGGTreeListData } from '@/store/trade/google/google.store';
import { storeToRefs } from 'pinia';
import type { OperatingSystemItem } from '../../google/type';
const { globalOptions } = storeToRefs(useGGTreeListData());

const props = defineProps({
  fromOptions: {
    type: Array as PropType<TdOptionProps[]>,
    default: () => [],
  },
  toOptions: {
    type: Array as PropType<TdOptionProps[]>,
    default: () => [],
  },
  modelValue: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  isReviewing: {
    type: Boolean,
    default: false,
  },
  operationType: {
    type: String,
    default: 'android',
  },
});
const emits = defineEmits(['update:modelValue']);
const val = ref(['0', 'current']);
// 要对回来的值进行转换
watch(() => props.modelValue, async () => {
  await new Promise((resolve) => {
    const interval = setInterval(() => {
      if (globalOptions.value.operatingSystem) {
        clearInterval(interval);
        resolve({});
      }
    }, 50);
  });
  const {
    android: initAndroidOptions = [],
    ios: initIosOptions = [],
  } = globalOptions.value.operatingSystem;
  let [from = '0', to = 'current'] = props.modelValue;
  let flag = false;
  const options = props.operationType === 'android' ? initAndroidOptions : initIosOptions;
  if (from.startsWith('630')) {
    from = options.find((item: OperatingSystemItem) => item.value === from)?.version;
    flag = true;
  }
  if (props.modelValue.length > 2) {
    let maxVersion = 0;
    props.modelValue.forEach((val) => {
      const { version } = options.find((item: OperatingSystemItem) => item.value === val);
      if (Number(version) > maxVersion) {
        maxVersion = Number(version);
        to = val;
      }
    });
  }
  if (to.startsWith('630')) {
    to = options.find((item: OperatingSystemItem) => item.value === to)?.version;
    flag = true;
  }
  if (flag) {
    val.value = [from, to];
  } else {
    val.value = props.modelValue;
  }
}, {
  immediate: true,
});

watch(() => val.value, () => {
  emits('update:modelValue', val.value);
}, {
  deep: true,
});
const fromOptionsComputed = computed(() => {
  const [, to] = val.value;
  const options = props.fromOptions.map((item) => {
    const newItem = { ...item };
    newItem.disabled = false;
    const toVal = to === 'current' ? Number.MAX_VALUE : Number(to);
    if (Number(item.value) > Number(toVal)) {
      newItem.disabled = true;
    }
    return newItem;
  });
  if (to !== 'current' && options.length) {
    options[0].disabled = true;
  }
  return options;
});
const toOptionsComputed = computed(() => {
  const [from] = val.value;
  const options = props.toOptions.map((item) => {
    const newItem = { ...item };
    newItem.disabled = false;
    const toVal = item.value === 'current' ? Number.MAX_VALUE : Number(item.value);
    if (toVal < Number(from)) {
      newItem.disabled = true;
    }
    return newItem;
  });
  return options;
});

</script>
