<template>
  <t-switch :value="modelValue" @change="onChange" />
</template>
<script setup lang="ts">
import { Level } from '../../common/template/config';
import { toRefs, PropType } from 'vue';
import { importantChange } from './utils';

const props = defineProps({
  modelValue: {
    type: [String, Boolean],
    default: '',
  },
  important: {
    type: Boolean,
    default: false, // 是否是重要字段，需要弹框提示
  },
  importantLevel: {
    type: Number as PropType<Level>,
    default: Level.CampaignLevel, // 重要字段的层级，默认为campaign
  },
});

const { modelValue, important, importantLevel } = toRefs(props);
const emits = defineEmits(['update:modelValue']);

const onChange = (val: string) => {
  if (important.value) {
    importantChange(importantLevel.value, emits, val);
  } else {
    emits('update:modelValue', val);
  }
};
</script>
