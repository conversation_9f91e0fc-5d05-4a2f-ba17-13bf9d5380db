<template>
  <div class="relative min-w-[930px]">
    <t-tabs
      :value="targetTab"
      size="medium"
      style="padding-right: 50px;"
      class="flex-1 bg-transparent"
      @change="tabChange"
    >
      <t-tab-panel
        v-for="(item) in treeNodeList"
        :key="item.id"
        :value="item.id"
        :label="getLabel(item)"
        class="py-[8px]"
      >
        <template #label>
          {{ getLabel(item) }}
          <icon
            v-if="item.status === 1 && !getErrorMsg(item) && isReviewing"
            name="check-circle-filled"
            class="text-success-primary ml-[8px]"
          />
          <icon
            v-if="(item.status === -1 || getErrorMsg(item)) && isReviewing"
            name="error-circle-filled"
            class="text-error-primary ml-[8px]"
          />
        </template>
        <slot />
      </t-tab-panel>
    </t-tabs>
    <div
      v-if="!isReviewing"
      class="action"
    >
      <t-dropdown :options="addOptions" :min-column-width="150" @click="copyClick">
        <icon name="file-copy" class="text-base text-gray-primary" style="margin-right: 8px" />
      </t-dropdown>
      <t-tooltip :content="'Create ' + LEVEL_LIST[level]">
        <icon
          name="add"
          class="text-xl text-gray-primary"
          @click="add"
        />
      </t-tooltip>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, PropType, toRefs } from 'vue';
import { Icon } from 'tdesign-icons-vue-next';
import type { TreeNode } from '../template/type';
import { Level, LEVEL_LIST } from '../template/config';
import { useTreeListDataStore } from '@/store/trade/template.store';

const props = defineProps({
  level: {
    type: Number as PropType<Level>,
    default: 0,
  },
  treeNodeList: {
    type: Array as PropType<TreeNode[]>,
    default: () => [],
  },
  targetTab: {
    type: String,
    default: '',
  },
  isReviewing: {
    type: Boolean,
    default: false,
  },
  getErrorMsg: {
    type: Function,
    default: () => {},
  },
});
const { channelConfig } = toRefs(useTreeListDataStore());
// 抛出事件
const emit = defineEmits(['change', 'copy', 'add']);

const getLabel = (node: TreeNode) => {
  if (!channelConfig.value) {
    return '';
  }
  const nameKey = channelConfig.value.keyConfig.levelName[LEVEL_LIST[node.level]];
  return node.data[nameKey].split('-').filter((item: string) => item)
    .join('-');
};
const addOptions = computed(() => {
  const actions = [
    { content: 'Duplicate(This level)', value: 1 },
    { content: 'Duplicate(All level)', value: 2 },
  ];
  if (props.level === Level.AdLevel) actions.splice(1, 1); // ad层只复制当前层级
  return actions;
});

const tabChange = (newValue: string) => {
  emit('change', newValue, props.level);
};

// 复制节点
const copyClick = ({ value }: { value: number }) => {
  const curNode = props.treeNodeList.find(item => item.id === props.targetTab) as TreeNode;
  const innerIdKey = `inner_${LEVEL_LIST[props.level].toLowerCase()}_id`;
  emit('copy', {
    copy_type: value,
    level: props.level,
    inner_id: curNode.data[innerIdKey],
  });
};

// 添加节点
const add = () => {
  emit('add', props.level);
};
</script>
<style lang="scss">
.action {
  position: absolute;
  right: 0;
  top: 16px;
  svg {
    cursor: pointer;
  }
}
</style>
