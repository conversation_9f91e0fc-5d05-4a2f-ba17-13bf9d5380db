<template>
  <t-collapse :default-expand-all="true" class="max-w-[855px] clear-both">
    <t-collapse-panel value="0" :header="header">
      <t-form-item label="Tracking template(optional)" name="tracking_url_template">
        <t-input
          v-if="!props.isReviewing"
          v-model="trackingUrlTemplate"
          :disabled="props.disabled"
          class="max-w-[688px]"
          placeholder="Example: https://www.trackingtemplate.foo/?url={lpurl}&id=5"
        />
        <span v-else>{{ trackingUrlTemplate }}</span>
      </t-form-item>
      <t-form-item label="Final url suffix(optional)">
        <t-input
          v-if="!props.isReviewing"
          v-model="finalUrlSuffix"
          :disabled="props.disabled"
          class="max-w-[688px]"
          placeholder="Example: param1=value1&param2=value2"
        />
        <span v-else>{{ finalUrlSuffix }}</span>
      </t-form-item>
      <t-form-item label="Custom parameter(optional)">
        <div>
          <div
            v-for="(item, index) in urlCustomParameters"
            :key="index"
            class="flex items-center mb-[8px]"
          >
            <t-input-adornment
              v-if="!props.isReviewing"
              prepend="{_" append="}"
            >
              <t-input
                v-model="item.key"
                :disabled="props.disabled"
                placeholder="Name"
                :class="{ 'w-[110px]': props.isInDrawer, 'w-[160px]': !props.isInDrawer }"
              />
            </t-input-adornment>
            <div v-else-if="item.key" class="flex items-center">
              <span class="mr-[8px]">&#123;_</span>
              <span>{{ item.key }}</span>
            </div>
            <span v-if="!props.isReviewing || item.key || item.value" class="mr-[8px] ml-[8px]">=</span>
            <t-input
              v-if="!props.isReviewing"
              v-model="item.value"
              :disabled="props.disabled"
              placeholder="Value"
              class="mr-[8px]"
              :class="{ 'w-[120px]': props.isInDrawer }"
            />
            <div v-else-if="item.value" class="flex items-center">
              <span class="mr-[8px]">{{ item.value }}</span>
              <span>&#125;</span>
            </div>
            <t-link
              v-if="urlCustomParameters.length > 1 && !props.isReviewing && !props.disabled"
              class="mr-[8px]" variant="text"
              theme="primary"
              hover="color"
              @click="() => deleteParams(index)"
            >
              Delete
            </t-link>
            <t-link
              v-if="!props.isReviewing && !props.disabled && urlCustomParameters.length < 8
                && index === urlCustomParameters.length - 1"
              variant="text"
              theme="primary"
              hover="color"
              @click="addParams"
            >
              Add
            </t-link>
          </div>
        </div>
      </t-form-item>
      <t-form-item
        v-if="isShowMobileUrl"
        name="final_mobile_urls"
        label="Use a different final url for mobile(optional)"
        class="pb-[10px] three-lines"
      >
        <t-input
          v-if="!props.isReviewing"
          v-model="finalMobileUrls"
          :disabled="props.disabled"
          placeholder="https://m.example.com"
        />
        <span v-else>{{ finalMobileUrls }}</span>
      </t-form-item>
    </t-collapse-panel>
  </t-collapse>
</template>
<script lang="ts" setup>
import  { ref, watch, toRefs, PropType, defineComponent } from 'vue';
import { Tooltip } from 'tdesign-vue-next';
import { InfoCircleIcon } from 'tdesign-icons-vue-next';
type UrlParams = {
  key: string,
  value: string
};

defineComponent({
  Tooltip,
  InfoCircleIcon,
});

const props = defineProps({
  header: {
    type: String,
    default: '',
  },
  finalUrlSuffix: {
    type: String as PropType<string>,
    default: '',
  },
  trackingUrlTemplate: {
    type: String as PropType<string>,
    default: '',
  },
  urlCustomParameters: {
    type: Array as PropType<UrlParams[]>,
    default: () => [],
  },
  finalMobileUrls: {
    type: String as PropType<string>,
    default: '',
  },
  isShowMobileUrl: {
    type: Boolean,
    default: false,
  },
  isReviewing: {
    type: Boolean,
    default: false,
  },
  isInDrawer: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});
const { header } = toRefs(props);
const finalUrlSuffix = ref(props.finalUrlSuffix || '');
const trackingUrlTemplate = ref(props.trackingUrlTemplate || '');
const urlCustomParameters = ref<UrlParams[]>(props.urlCustomParameters.length ? props.urlCustomParameters : [{ key: '', value: '' }]);
const finalMobileUrls = ref(props.finalMobileUrls);
const emits = defineEmits(['update:finalUrlSuffix', 'update:trackingUrlTemplate', 'update:urlCustomParameters', 'update:finalMobileUrls']);

watch(() => props.finalUrlSuffix, (val) => {
  finalUrlSuffix.value = val;
});
watch(() => finalUrlSuffix.value, () => {
  emits('update:finalUrlSuffix', finalUrlSuffix.value);
});

watch(() => props.trackingUrlTemplate, (val) => {
  trackingUrlTemplate.value = val;
});
watch(() => trackingUrlTemplate.value, () => {
  emits('update:trackingUrlTemplate', trackingUrlTemplate.value);
});

watch(() => JSON.stringify(props.urlCustomParameters), (val) => {
  let initValue = props.urlCustomParameters;
  if (val.length === 0) {
    initValue = [{ key: '', value: '' }];
  }
  urlCustomParameters.value = initValue;
});
watch(() => urlCustomParameters.value, () => {
  emits('update:urlCustomParameters', urlCustomParameters.value);
}, {
  deep: true,
});

watch(() => props.finalMobileUrls, (val) => {
  finalMobileUrls.value = val;
});
watch(() => finalMobileUrls.value, () => {
  emits('update:finalMobileUrls', finalMobileUrls.value);
});

const addParams = () => {
  urlCustomParameters.value.push({
    key: '',
    value: '',
  });
};
const deleteParams = (index: number) => {
  urlCustomParameters.value.splice(index, 1);
};
</script>
<style lang="scss" scoped>
// :deep .t-input-adornment {
//   width: 100%;
// }
</style>
