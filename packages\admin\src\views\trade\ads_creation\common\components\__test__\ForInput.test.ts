/*
 * @Date: 2023-06-07 09:28:05
 * @LastEditors: maclerylin
 * @LastEditTime: 2023-06-07 14:47:30
 */
import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import ForInput from '../ForInput.vue';


describe('ForInputomponentTest()', () => {
  it('test maxNumber and minNumber by input', async () => {
    // 进行断言和测试
    const wrapper = mount(ForInput, {
      props: {
        minNumber: 1,
        maxNumber: 3,
        modelValue: [''],
      },
    });
    expect(wrapper.findAll('.input-textinput')).toHaveLength(1);
    await wrapper.find('.footer-action button').trigger('click');
    expect(wrapper.findAll('.input-textinput')).toHaveLength(2);
    await (wrapper.find('.footer-action button')).trigger('click');
    expect(wrapper.findAll('.input-textinput')).toHaveLength(3);
    await wrapper.find('.footer-action').trigger('click');
    expect(wrapper.findAll('.input-textinput')).toHaveLength(3);
  });
  it('test maxNumber and minNumber by textarea', async () => {
    // 进行断言和测试
    const wrapper = mount(ForInput, {
      props: {
        minNumber: 1,
        maxNumber: 3,
        modelValue: [''],
        textarea: true,
      },
    });
    expect(wrapper.findAll('.input-textarea')).toHaveLength(1);
    await wrapper.find('.footer-action button').trigger('click');
    expect(wrapper.findAll('.input-textarea')).toHaveLength(2);
    await (wrapper.find('.footer-action button')).trigger('click');
    expect(wrapper.findAll('.input-textarea')).toHaveLength(3);
    await wrapper.find('.footer-action').trigger('click');
    expect(wrapper.findAll('.input-textarea')).toHaveLength(3);
  });
  it('test length by textarea', async () => {
    // 进行断言和测试
    const wrapper = mount(ForInput, {
      props: {
        minNumber: 1,
        maxNumber: 1,
        maxLen: 10,
        modelValue: [''],
        textarea: true,
      },
    });
    await wrapper.setProps({
      modelValue: ['test'],
    });
    expect(wrapper.vm.modelValue[0].length).toBe(4);
    await wrapper.setProps({
      modelValue: ['test和'],
    });
    expect(wrapper.vm.modelValue[0].length).toBe(5);
    await wrapper.setProps({
      modelValue: ['test和你a'],
    });
    expect(wrapper.vm.modelValue[0].length).toBe(7);
    await wrapper.findAll('.input-textarea textarea')[0].setValue('test和你ab');
    expect(wrapper.vm.modelValue[0].length).toBe(8);
    await wrapper.findAll('.input-textarea textarea')[0].setValue('test和你ab你');
    expect(wrapper.vm.modelValue[0]).toBe('test和你ab');
    expect(wrapper.vm.modelValue[0].length).toBe(8);
    expect(wrapper.findAll('.foot-action')).toHaveLength(0);
  });
  it('test length by textinput', async () => {
    // 进行断言和测试
    const wrapper = mount(ForInput, {
      props: {
        minNumber: 1,
        maxNumber: 1,
        maxLen: 10,
        modelValue: [''],
        textarea: false,
      },
    });
    await wrapper.setProps({
      modelValue: ['test'],
    });
    expect(wrapper.vm.modelValue[0].length).toBe(4);
    await wrapper.setProps({
      modelValue: ['test和'],
    });
    expect(wrapper.vm.modelValue[0].length).toBe(5);
    await wrapper.setProps({
      modelValue: ['test和你a'],
    });
    expect(wrapper.vm.modelValue[0].length).toBe(7);
    await wrapper.findAll('.input-textinput input')[0].setValue('test和你ab');
    expect(wrapper.vm.modelValue[0].length).toBe(8);
    await wrapper.findAll('.input-textinput input')[0].setValue('test和你ab你');
    expect(wrapper.vm.modelValue[0]).toBe('test和你ab');
    expect(wrapper.vm.modelValue[0].length).toBe(8);

    await wrapper.setProps({
      isUTF8: true,
    });
    await wrapper.findAll('.input-textinput input')[0].setValue('test1我和你');
    expect(wrapper.vm.modelValue[0]).toBe('test1我');
  });
});
