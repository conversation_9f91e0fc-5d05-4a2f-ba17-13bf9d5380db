/*
 * @Date: 2023-06-07 14:17:19
 * @LastEditors: maclerylin
 * @LastEditTime: 2023-06-07 14:29:35
 */
import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import ForSelect from '../ForSelect.vue';


describe('ForSelectComponentTest()', () => {
  it('test maxNumber by select', async () => {
    const wrapper = mount(ForSelect, {
      props: {
        maxNumber: 3,
        modelValue: [''],
        options: [
          { label: 'label', value: 'value' },
          { label: 'label1', value: 'value1' },
          { label: 'label2', value: 'value2' },
        ],
      },
    });
    expect(wrapper.findAll('.for-select-dom')).toHaveLength(1);
    await wrapper.find('.footer-action button').trigger('click');
    expect(wrapper.findAll('.for-select-dom')).toHaveLength(2);
    await wrapper.find('.footer-action button').trigger('click');
    expect(wrapper.findAll('.for-select-dom')).toHaveLength(3);
    expect(wrapper.findAll('.footer-action button')).toHaveLength(0);

    await wrapper.find('.delete-icon').trigger('click');
    expect(wrapper.findAll('.for-select-dom')).toHaveLength(2);
    await wrapper.find('.delete-icon').trigger('click');
    expect(wrapper.findAll('.for-select-dom')).toHaveLength(1);

    expect(wrapper.findAll('.delete-icon')).toHaveLength(0);
  });
});
