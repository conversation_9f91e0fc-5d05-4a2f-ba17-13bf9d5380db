import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import CampaignName from '../CampaignName.vue';

describe('campaignNameComponentTest()', () => {
  it('should hide edit input form if publishing is true', () => {
    // 进行断言和测试
    const wrapper = mount(CampaignName, {
      props: {
        publishing: true,
      },
    });
    expect(wrapper.findAll('.campaign-name-form')).toHaveLength(0);
    // 检测出漏测用例
    expect(wrapper.findAll('.unstander-value-form')).toHaveLength(0);
  });
  it('should display edit input form if both publishing and published is false', () => {
    // 进行断言和测试
    // expect(wrapper.text()).toContain('Hello, world!');
    const wrapper = mount(CampaignName, {
      props: {
        publishing: false,
        published: false,
        modelValue: 'pre_media-REGION-123-FOO-BAR-CUS-567-PRO',
        objective: 2,
      },
    });
    expect(wrapper.findAll('.campaign-name-form')).toHaveLength(1);
  });
  it('should display objective options if objective value changed', async () => {
    // 进行断言和测试
    // expect(wrapper.text()).toContain('Hello, world!');
    const wrapper = mount(CampaignName, {
      props: {
        publishing: false,
        published: false,
        modelValue: 'pre_media-REGION-123-FOO-BAR-CUS-567-PRO',
        objective: 2,
      },
    });
    expect(wrapper.findAll('.campaign-name-form')).toHaveLength(1);
    expect((wrapper.vm as any).platformOptions.findIndex((item: { value: string }) => item.value === 'pc') !== -1).equals(true);
    await wrapper.setProps({ objective: 1 });
    expect((wrapper.vm as any).platformOptions.findIndex((item: { value: string }) => item.value === 'pc') === -1).equals(true);
  });
  it('should display edit input form if publishing is false and published is true', async () => {
    // 进行断言和测试
    const wrapper = mount(CampaignName, {
      props: {
        publishing: false,
        published: true,
        modelValue: 'pre_mediaPRO',
        objective: 2,
      },
    });
    expect(wrapper.findAll('.campaign-name-form')).toHaveLength(0);
    expect(wrapper.findAll('.unstander-value-form')).toHaveLength(0);
    await wrapper.find('.edit-btn').trigger('click');
    expect(wrapper.findAll('.unstander-value-form')).toHaveLength(1);
  });
});
