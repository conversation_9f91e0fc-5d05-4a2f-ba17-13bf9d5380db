import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import Inputcount from '../Input-count.vue';


describe('ForInputomponentTest()', () => {
  it('test length by textarea', async () => {
    // 进行断言和测试
    const wrapper = mount(Inputcount, {
      maxLen: 10,
      modelValue: '',
    });
    await wrapper.setProps({
      modelValue: 'test',
    });
    expect(wrapper.vm.modelValue.length).toBe(4);
    await wrapper.setProps({
      modelValue: 'test和',
    });
    expect(wrapper.vm.modelValue.length).toBe(5);
    await wrapper.setProps({
      modelValue: 'test和你a',
    });
    expect(wrapper.vm.modelValue.length).toBe(7);
    await wrapper.findAll('.input-textarea')[0].setValue('test和你ab');
    expect(wrapper.vm.modelValue.length).toBe(8);
    await wrapper.findAll('.input-textarea')[0].setValue('test和你ab你');
    expect(wrapper.vm.modelValue).toBe('test和你ab');
    expect(wrapper.vm.modelValue.length).toBe(8);
    expect(wrapper.findAll('.foot-action')).toHaveLength(0);
  });
  it('test length by textinput', async () => {
    // 进行断言和测试
    const wrapper = mount(Inputcount, {
      props: {
        minNumber: 1,
        maxNumber: 1,
        maxLen: 10,
        modelValue: '',
        textarea: false,
      },
    });
    await wrapper.setProps({
      modelValue: 'test',
    });
    expect(wrapper.vm.modelValue.length).toBe(4);
    await wrapper.setProps({
      modelValue: 'test和',
    });
    expect(wrapper.vm.modelValue.length).toBe(5);
    await wrapper.setProps({
      modelValue: 'test和你a',
    });
    expect(wrapper.vm.modelValue.length).toBe(7);
    await wrapper.findAll('.input-textinput')[0].setValue('test和你ab');
    expect(wrapper.vm.modelValue.length).toBe(8);
    await wrapper.findAll('.input-textinput')[0].setValue('test和你ab你');
    expect(wrapper.vm.modelValue).toBe('test和你ab');
    expect(wrapper.vm.modelValue.length).toBe(8);

    await wrapper.setProps({
      isUTF8: true,
    });
    await wrapper.findAll('.input-textinput')[0].setValue('test1我和你');
    expect(wrapper.vm.modelValue).toBe('test1我');
  });
});
