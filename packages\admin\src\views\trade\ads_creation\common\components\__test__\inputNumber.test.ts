/*
 * @Date: 2023-06-07 14:48:22
 * @LastEditors: maclerylin
 * @LastEditTime: 2023-06-07 15:13:49
 */
import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import InputNumber from '../Input-number.vue';


describe('Input number componentTest()', () => {
  it('test length by textarea', async () => {
    // 进行断言和测试
    const wrapper = await mount(InputNumber, {
      decimalPlaces: 2,
      modelValue: '1000.12',
    });
    await wrapper.setProps({
      modelValue: '1000.12',
    });
    const innerInputNumber = wrapper.findComponent('.input-number');
    expect((innerInputNumber as any).vm.modelValue).toBe('1000.12');
    await wrapper.find('.input-number input').setValue('1000.12212');
    expect(wrapper.vm.modelValue).toBe('1000.12');

    await innerInputNumber.setValue('1000.1223');
    expect((innerInputNumber as any).vm.modelValue).toBe('1000.12');
  });
});
