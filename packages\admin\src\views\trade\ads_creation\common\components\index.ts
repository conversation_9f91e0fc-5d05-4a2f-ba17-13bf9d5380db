import { defineAsyncComponent } from 'vue';

interface COMPONENT {
  [key: string]: any,
}

const components: COMPONENT = {
  CampaignName: defineAsyncComponent(() => import('./CampaignName.vue')),
  FormModule: defineAsyncComponent(() => import('./FormModule.vue')),
  TabsTool: defineAsyncComponent(() => import('./TabsTool.vue')),
  't-form': defineAsyncComponent(() => import('tdesign-vue-next/es/form').then(From => From)),
  't-form-item': defineAsyncComponent(() => import('tdesign-vue-next/es/form').then(({ FormItem }) => FormItem)),
  't-input': defineAsyncComponent(() => import('tdesign-vue-next/es/input').then(Input => Input)),
  't-textarea': defineAsyncComponent(() => import('tdesign-vue-next/es/textarea').then(Textarea => Textarea)),
  't-input-number': defineAsyncComponent(() => import('tdesign-vue-next/es/input-number').then(InputNumber => InputNumber)),
  't-input-adornment': defineAsyncComponent(() => import('./InputAdornment.vue')),
  't-select': defineAsyncComponent(() => import('tdesign-vue-next/es/select').then(({ Select }) => Select)),
  't-switch': defineAsyncComponent(() => import('tdesign-vue-next/es/switch').then(Switch => Switch)),
  't-radio-group': defineAsyncComponent(() => import('tdesign-vue-next/es/radio').then(({ RadioGroup }) => RadioGroup)),
  't-radio-button': defineAsyncComponent(() => import('tdesign-vue-next/es/radio').then(({ RadioButton }) => RadioButton)),
  't-link': defineAsyncComponent(() => import('tdesign-vue-next/es/link').then(link => link)),
  icon: defineAsyncComponent(() => import('tdesign-vue-next/es/icon').then(Icon => Icon)),
  't-range-input': defineAsyncComponent(() => import('tdesign-vue-next/es/range-input').then(Range => Range)),
  collapse: defineAsyncComponent(() => import('./Collapse/index')),
  't-checkbox-group': defineAsyncComponent(() => import('tdesign-vue-next/es/checkbox').then(({ CheckboxGroup }) => CheckboxGroup)),
  't-checkbox': defineAsyncComponent(() => import('tdesign-vue-next/es/checkbox').then(({ Checkbox }) => Checkbox)),
  't-button': defineAsyncComponent(() => import('tdesign-vue-next/es/button').then(Button => Button)),
  't-date-picker': defineAsyncComponent(() => import('tdesign-vue-next/es/date-picker').then(DatePicker => DatePicker)),
  mediaList: defineAsyncComponent(() => import('./Media/MediaList.vue')),
  'for-input': defineAsyncComponent(() => import('./ForInput.vue')),
  'for-select': defineAsyncComponent(() => import('./ForSelect.vue')),
  'account-select': defineAsyncComponent(() => import('./AccountSelect.vue')),
  'audience-tt': defineAsyncComponent(() => import('./Audience/AudienceTT.vue')),
  'audience-fb': defineAsyncComponent(() => import('./Audience/AudienceFB.vue')),
  'audience-tw': defineAsyncComponent(() => import('./Audience/AudienceTW.vue')),
  'identity-select': defineAsyncComponent(() => import('../../tiktok/components/IdentitySelect.vue')),
  'app-select': defineAsyncComponent(() => import('./AppSelect.vue')),
  locations: defineAsyncComponent(() => import('../components/Locations.vue')),
  'multi-filter-select': defineAsyncComponent(() => import('./MultiFilterSelect.vue')),
  conversionGoal: defineAsyncComponent(() => import('./ConversionGoal.vue')),
  urlParam: defineAsyncComponent(() => import('./UrlParams.vue')),
  rangeSelect: defineAsyncComponent(() => import('./RangeSelect.vue')),
  'audience-gg': defineAsyncComponent(() => import('./Audience/AudienceGG.vue')),
  'input-number': defineAsyncComponent(() => import('./Input-number.vue')),
  'input-count': defineAsyncComponent(() => import('./Input-count.vue')),
  'name-edit': defineAsyncComponent(() => import('./NameEdit.vue')),
  newSiteLink: defineAsyncComponent(() => import('./NewSiteLink.vue')),
  'radio-group': defineAsyncComponent(() => import('./RadioGroup.vue')),
  switch: defineAsyncComponent(() => import('./Switch.vue')),
  'custom-select': defineAsyncComponent(() => import('./CustomSelect.vue')),
  FBTracking: defineAsyncComponent(() => import('../../facebook/components/Tracking.vue')),
  FBAppEvent: defineAsyncComponent(() => import('../../facebook/components/AppEvent.vue')),
  FBCustomEvent: defineAsyncComponent(() => import('../../facebook/components/ConversionEvent.vue')),
  FBManual: defineAsyncComponent(() => import('../../facebook/components/Manual.vue')),
  FBCustomAudiences: defineAsyncComponent(() => import('../../facebook/components/CustomAudiences.vue')),
  'collapse-select': defineAsyncComponent(() => import('./CollapseSelect.vue')),
};

export default components;
