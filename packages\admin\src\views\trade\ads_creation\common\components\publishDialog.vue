<template>
  <div class="flex flex-col	justify-center">
    <t-dialog
      :visible="isShow"
      :close-on-overlay-click="false"
      :close-on-esc-keydown="false"
      :close-btn="finishAdsNumber === publishAdsNumber ? true : false"
      :confirm-btn="null"
      :cancel-btn="null"
      :on-close="onClose"
    >
      <template #body>
        <div>
          <div class="publish-status-tip">
            {{ publishStatusTipText }}
          </div>
          <div class="publish-progress-tip">
            <Progress
              theme="line"
              :status="publishStatus"
              :percentage="percentage"
              :color="colorVal"
            />
          </div>
          <div class="publish-number-tip mb-[10px]">
            {{ getPublishFooterTip() }}
            <br v-if="getPublishFooterTip()">
            <span v-if="publishingNumber > 0">{{ getPublishingTipText() }}</span>
          </div>
          <div class="publish-footer-btn text-center">
            <t-button
              theme="primary" size="medium" variant="base"
              @click="goToManageAds"
            >
              Back to manage ads
            </t-button>
          </div>
        </div>
      </template>
    </t-dialog>
  </div>
</template>
<script lang="ts" setup>
import { toRefs, defineComponent, computed, PropType } from 'vue';
import { Progress, StatusEnum } from 'tdesign-vue-next';

defineComponent({
  Progress,
});

const props = defineProps({
  publishStatus: {
    type: String as PropType<StatusEnum>,
    default: 'active',
  },
  isShow: {
    type: Boolean,
    default: false,
  },
  // 总的发布数量
  publishAdsNumber: {
    type: Number,
    default: 1,
  },
  // 已经完成的数量，不管发布是否成功
  finishAdsNumber: {
    type: Number,
    default: 0,
  },
  // 发布中的数量
  publishingNumber: {
    type: Number,
    default: 0,
  },
  // 每个层级发布错误的数量
  publishError: {
    type: Array as PropType<number[]>,
    default: () => [],
  },
  percentage: {
    type: Number,
    default: 0,
  },
});
const {
  isShow, publishStatus, percentage,
  publishAdsNumber, finishAdsNumber, publishingNumber, publishError,
} = toRefs(props);
const emit = defineEmits(['onClose', 'onGotoManagement']);

const publishStatusTipSet: { [key: string]: string } = {
  success: 'Succeeded!',
  active: 'Publishing',
  error: 'Some items not publish!',
};
const colors: { [key: string]: string } = {
  success: 'var(--aix-text-color-success-primary)',
  active: 'var(--aix-text-color-link)',
  error: 'var(--aix-text-color-error-secondary)',
};
const publishStatusTipText = computed(() => {
  const defaultTip = publishStatusTipSet[publishStatus.value];
  if (publishStatus.value === 'error' && publishAdsNumber.value === 1) {
    return 'Failed';
  }
  return defaultTip;
});
const colorVal = computed(() => colors[publishStatus.value]);

// 获取进度条的底部提示
const getPublishFooterTip = () => {
  let tipText = '';
  if (publishAdsNumber.value > 1) {
    if (finishAdsNumber.value === publishAdsNumber.value && publishStatus.value === 'error') {
      const [campainErr, adGroupErr, adErr] = publishError.value;
      tipText += `${campainErr > 0 ? `${campainErr} campaign${campainErr > 1 ? 's' : ''},` : ''}`;
      tipText += `${adGroupErr > 0 ? `${adGroupErr} ad group${adGroupErr > 1 ? 's' : ''} and ` : ''}`;
      tipText += `${adErr > 0 ? `${adErr} ad${adErr > 1 ? 's' : ''}` : ''} `;
      tipText += 'failed because of errors.';
    }
    if (finishAdsNumber.value === publishAdsNumber.value && publishStatus.value === 'success') {
      tipText += `${finishAdsNumber.value} / ${finishAdsNumber.value}`;
    }
    if (finishAdsNumber.value !== publishAdsNumber.value) {
      tipText += `${finishAdsNumber.value}/${publishAdsNumber.value}(Please don't close the page)` ;
    }
  }
  return tipText;
};
const getPublishingTipText = () => `${publishingNumber.value} ad${publishingNumber.value > 1 ? 's are' : ' is'} still publishing.`;
const onClose = () => {
  emit('onClose', {});
};
const goToManageAds = () => {
  console.log('go to pivot');
  emit('onGotoManagement', {});
};
</script>
