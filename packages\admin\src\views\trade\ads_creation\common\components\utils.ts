import type { MEDIA } from '@@/index';
import { EBusEmit } from '@/views/trade/ads_creation/common/template/event';
import { useTreeListDataStore } from '@/store/trade/template.store';
import { compareNode, useCurrentData } from '../template/compose/currentCompose';
import { Level } from '../template/config';
import { isNodeTemp } from '../template/utils-common';
const { initTreeFromLevel } = useTreeListDataStore();

export const importantChange = (level: Level, emits: Function, val: string | number) => {
  const { campaign, adgroup, channelConfig, channelType } = useCurrentData();
  if (level === Level.CampaignLevel) {
    // campaign重要字段变更处理
    const campaignTmp = isNodeTemp(Level.CampaignLevel, campaign, channelConfig);
    if (campaignTmp) {
      // 临时节点，需要判断其他层级是否有变更，有的话需要提示，没有的话直接修改，且不触发保存
      const { same: adgroupSame } = compareNode(Level.AdgroupLevel, channelType as MEDIA);
      const { same: adSame } = compareNode(Level.AdLevel, channelType as MEDIA);
      if (adgroupSame && adSame) {
        emits('update:modelValue', val);
        initTreeFromLevel(Level.AdgroupLevel);
      } else {
        EBusEmit('importantChange', {
          isTemp: true,
          level: Level.AdgroupLevel, // 初始化adgroup数据
          cb: () => emits('update:modelValue', val),
        });
      }
    } else {
      // 草稿节点，弹框并且触发保存
      EBusEmit('importantChange', {
        isTemp: false,
        level: Level.AdgroupLevel,
        cb: () => {
          emits('update:modelValue', val);
        },
        final: () => {
          EBusEmit('clearValidate', { levels: ['adgroup', 'ad'] });
        },
      });
    }
  } else {
    // adgroup重要字段变更处理
    const adgroupTmp = isNodeTemp(Level.AdgroupLevel, adgroup, channelConfig);
    if (adgroupTmp) {
      const { same: adSame } = compareNode(Level.AdLevel, channelType as MEDIA);
      if (adSame) {
        emits('update:modelValue', val);
        initTreeFromLevel(Level.AdLevel);
      } else {
        EBusEmit('importantChange', {
          isTemp: true,
          level: Level.AdLevel, // 初始化ad数据
          cb: () => emits('update:modelValue', val),
        });
      }
    } else {
      // 草稿节点，弹框并且触发保存
      EBusEmit('importantChange', {
        isTemp: false,
        level: Level.AdLevel, // 初始化ad数据
        cb: () => {
          emits('update:modelValue', val);
        },
        final: () => {
          EBusEmit('clearValidate', { levels: ['ad'] });
        },
      });
    }
  }
};
